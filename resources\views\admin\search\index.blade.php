@extends('layouts.dashboard')

@section('title', 'Advanced Search')

@section('content')
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-start">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Advanced Search</h1>
            <p class="text-gray-600">Search across all database entities with advanced filters</p>
        </div>
    </div>

    <!-- Search Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Search Criteria</h2>
        </div>
        <div class="p-6">
            <form id="advanced-search-form" class="space-y-6">
                <!-- Main Search -->
                <div>
                    <label for="search-query" class="block text-sm font-medium text-gray-700 mb-2">Search Query</label>
                    <div class="relative">
                        <input type="text" 
                               id="search-query" 
                               name="q"
                               placeholder="Enter your search terms..."
                               class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Entity Type Filter -->
                    <div>
                        <label for="entity-type" class="block text-sm font-medium text-gray-700 mb-1">Entity Type</label>
                        <select id="entity-type" name="type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                            <option value="">All Types</option>
                            <option value="user">Users</option>
                            <option value="order">Orders</option>
                            <option value="product">Products</option>
                            <option value="project">Projects</option>
                            <option value="job">Jobs</option>
                            <option value="job_application">Job Applications</option>
                            <option value="coupon">Coupons</option>
                            <option value="payment">Payments</option>
                            <option value="activity_log">Activity Logs</option>
                            <option value="visitor_analytic">Visitor Analytics</option>
                        </select>
                    </div>

                    <!-- Status Filter -->
                    <div>
                        <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <select id="status-filter" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                            <option value="">All Statuses</option>
                        </select>
                    </div>

                    <!-- Date Range -->
                    <div>
                        <label for="date-range" class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
                        <select id="date-range" name="date_range" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                            <option value="">All Time</option>
                            <option value="today">Today</option>
                            <option value="week">This Week</option>
                            <option value="month">This Month</option>
                            <option value="quarter">This Quarter</option>
                            <option value="year">This Year</option>
                        </select>
                    </div>
                </div>

                <!-- Search Button -->
                <div class="flex justify-between items-center">
                    <button type="button" id="clear-filters" class="text-sm text-gray-500 hover:text-gray-700">
                        Clear All Filters
                    </button>
                    <button type="submit" class="bg-primary-600 text-white px-6 py-2 rounded-md hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                        Search
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Search Results -->
    <div id="advanced-search-results-container" class="hidden">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900">Search Results</h2>
                    <div id="advanced-results-count" class="text-sm text-gray-500"></div>
                </div>
            </div>
            <div id="advanced-search-results" class="divide-y divide-gray-200">
                <!-- Results will be populated here -->
            </div>
            <div id="advanced-search-loading" class="hidden p-8 text-center">
                <div class="inline-flex items-center">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Searching...
                </div>
            </div>
            <div id="advanced-no-results" class="hidden p-8 text-center">
                <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                    </svg>
                </div>
                <p class="text-gray-500">No results found for your search criteria.</p>
                <p class="text-sm text-gray-400 mt-1">Try adjusting your search terms or filters.</p>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Advanced Search Scope - prevent conflicts with header search
    (function() {
        const searchForm = document.getElementById('advanced-search-form');
    const searchQuery = document.getElementById('search-query');
    const entityType = document.getElementById('entity-type');
    const statusFilter = document.getElementById('status-filter');
    const clearFilters = document.getElementById('clear-filters');
    const resultsContainer = document.getElementById('advanced-search-results-container');
    const searchResults = document.getElementById('advanced-search-results');
    const searchLoading = document.getElementById('advanced-search-loading');
    const noResults = document.getElementById('advanced-no-results');
    const resultsCount = document.getElementById('advanced-results-count');

    // Load filters on page load
    loadFilters();

    // Handle entity type change to update status options
    entityType.addEventListener('change', function() {
        updateStatusOptions(this.value);
    });

    // Handle form submission
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        performSearch();
    });

    // Add real-time search as user types
    let advancedSearchTimeout;
    searchQuery.addEventListener('input', function() {
        clearTimeout(advancedSearchTimeout);
        const query = this.value.trim();

        if (query.length >= 2) {
            advancedSearchTimeout = setTimeout(() => {
                performSearch();
            }, 500); // 500ms delay for advanced search
        } else if (query.length === 0) {
            resultsContainer.classList.add('hidden');
        }
    });

    // Handle clear filters
    clearFilters.addEventListener('click', function() {
        searchForm.reset();
        updateStatusOptions('');
        resultsContainer.classList.add('hidden');
    });

    function loadFilters() {
        fetch('{{ route("admin.search.filters") }}', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin'
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Store filters for later use
                    window.searchFilters = data.filters;
                }
            })
            .catch(error => {
                console.error('Error loading filters:', error);
            });
    }

    function updateStatusOptions(entityType) {
        statusFilter.innerHTML = '<option value="">All Statuses</option>';
        
        if (!window.searchFilters || !entityType) return;

        let options = [];
        switch(entityType) {
            case 'order':
                options = window.searchFilters.order_statuses;
                break;
            case 'payment':
                options = window.searchFilters.payment_statuses;
                break;
            case 'user':
                options = window.searchFilters.user_roles;
                break;
            case 'project':
                options = window.searchFilters.project_statuses;
                break;
        }

        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.label;
            statusFilter.appendChild(optionElement);
        });
    }

    function performSearch() {
        const formData = new FormData(searchForm);
        const params = new URLSearchParams();

        // Only add non-empty parameters
        for (const [key, value] of formData.entries()) {
            if (value && value.trim() !== '') {
                params.append(key, value.trim());
            }
        }

        if (!params.get('q') || params.get('q').length < 2) {
            resultsContainer.classList.add('hidden');
            return;
        }

        // Show loading state
        resultsContainer.classList.remove('hidden');
        searchLoading.classList.remove('hidden');
        searchResults.classList.add('hidden');
        noResults.classList.add('hidden');

        const searchUrl = `{{ route('admin.search') }}?${params.toString()}`;

        fetch(searchUrl, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin'
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                searchLoading.classList.add('hidden');

                if (data.success && data.results && data.results.length > 0) {
                    displayResults(data.results);
                    resultsCount.textContent = `${data.total} result${data.total !== 1 ? 's' : ''} found`;
                } else if (data.success) {
                    noResults.classList.remove('hidden');
                    resultsCount.textContent = '0 results found';
                } else {
                    noResults.classList.remove('hidden');
                    resultsCount.textContent = data.message || 'Search failed';
                }
            })
            .catch(error => {
                console.error('Search error:', error);
                searchLoading.classList.add('hidden');
                noResults.classList.remove('hidden');
                resultsCount.textContent = 'Search failed - please try again';
            });
    }

    function displayResults(results) {
        let html = '';
        
        results.forEach(result => {
            const iconSvg = getIconSvg(result.icon || result.type);
            const typeColor = getTypeColor(result.type);
            
            html += `
                <div class="p-6 hover:bg-gray-50 transition-colors duration-200">
                    <div class="flex items-start space-x-4">
                        <div class="w-10 h-10 bg-${typeColor}-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-5 h-5 text-${typeColor}-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                ${iconSvg}
                            </svg>
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-medium text-gray-900">
                                    <a href="${result.url}" class="hover:text-primary-600 transition-colors duration-200">
                                        ${result.title}
                                    </a>
                                </h3>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${typeColor}-100 text-${typeColor}-800 capitalize">
                                    ${result.type.replace('_', ' ')}
                                </span>
                            </div>
                            <p class="text-gray-600 mt-1">${result.description}</p>
                            ${result.meta ? `<div class="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                                ${formatMetaData(result.meta)}
                            </div>` : ''}
                        </div>
                    </div>
                </div>
            `;
        });
        
        searchResults.innerHTML = html;
        searchResults.classList.remove('hidden');
    }

    function getIconSvg(icon) {
        const icons = {
            'user': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>',
            'order': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>',
            'shopping-bag': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>',
            'product': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>',
            'cube': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>',
            'project': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>',
            'briefcase': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6"/>',
            'job': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6"/>',
            'document-text': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>',
            'job_application': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>',
            'ticket': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>',
            'coupon': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>',
            'credit-card': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>',
            'payment': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>',
            'activity_log': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>',
            'chart-bar': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>',
            'visitor_analytic': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>'
        };
        return icons[icon] || icons['document-text'];
    }

    function getTypeColor(type) {
        const colors = {
            'user': 'blue',
            'order': 'green',
            'product': 'purple',
            'project': 'indigo',
            'job': 'yellow',
            'job_application': 'pink',
            'coupon': 'red',
            'payment': 'gray',
            'activity_log': 'orange',
            'visitor_analytic': 'teal'
        };
        return colors[type] || 'gray';
    }

    function formatMetaData(meta) {
        const parts = [];
        if (meta.status) parts.push(`<span>Status: ${meta.status}</span>`);
        if (meta.created_at) parts.push(`<span>Created: ${meta.created_at}</span>`);
        if (meta.role) parts.push(`<span>Role: ${meta.role}</span>`);
        if (meta.total) parts.push(`<span>Total: ${meta.total}</span>`);
        if (meta.sku) parts.push(`<span>SKU: ${meta.sku}</span>`);
        if (meta.price) parts.push(`<span>Price: ${meta.price}</span>`);
        return parts.join('');
    }

    })(); // Close advanced search scope
});
</script>
@endsection
