<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\ChatRoom;
use App\Http\Middleware\ChatApiAuthentication;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Laravel\Sanctum\PersonalAccessToken;
use PHPUnit\Framework\Attributes\Test;

class ChatAuthenticationMiddlewareTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected ChatApiAuthentication $middleware;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->middleware = new ChatApiAuthentication();
        
        // Clear cache before each test
        Cache::flush();
    }

    #[Test]
    public function authenticated_user_with_bearer_token_can_access_protected_routes()
    {
        $token = $this->user->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/v1/chat/rooms');

        $response->assertStatus(200);
    }

    #[Test]
    public function authenticated_user_with_session_can_access_protected_routes()
    {
        $this->actingAs($this->user, 'sanctum');

        $response = $this->getJson('/api/v1/chat/rooms');

        $response->assertStatus(200);
    }

    #[Test]
    public function unauthenticated_user_cannot_access_protected_routes()
    {
        $response = $this->getJson('/api/v1/chat/rooms');

        $response->assertStatus(401)
                 ->assertJson([
                     'success' => false,
                     'message' => 'Authentication required',
                 ]);
    }

    #[Test]
    public function anonymous_user_can_access_allowed_public_routes()
    {
        $room = ChatRoom::factory()->create();

        // Test allowed anonymous routes
        $publicRoutes = [
            ['POST', '/api/v1/chat/rooms', ['type' => 'visitor', 'customer_name' => 'Test', 'customer_email' => '<EMAIL>']],
            ['GET', "/api/v1/chat/rooms/{$room->uuid}"],
            ['POST', "/api/v1/chat/rooms/{$room->uuid}/messages", ['content' => 'Test message', 'message_type' => 'text']],
            ['GET', "/api/v1/chat/rooms/{$room->uuid}/messages"],
        ];

        foreach ($publicRoutes as $route) {
            $method = $route[0];
            $url = $route[1];
            $data = $route[2] ?? [];

            $response = $this->json($method, $url, $data);
            
            // Should not return 401 (authentication required)
            $this->assertNotEquals(401, $response->status(), 
                "Route {$method} {$url} should be accessible anonymously");
        }
    }

    #[Test]
    public function invalid_bearer_token_returns_unauthorized()
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer invalid-token',
        ])->getJson('/api/v1/chat/rooms');

        $response->assertStatus(401);
    }

    #[Test]
    public function api_key_authentication_works()
    {
        // Mock API key configuration
        config(['chat.api_keys' => [
            'test-api-key' => ['user_id' => $this->user->id]
        ]]);

        $response = $this->withHeaders([
            'X-API-Key' => 'test-api-key',
        ])->getJson('/api/v1/chat/rooms');

        $response->assertStatus(200);
    }

    #[Test]
    public function invalid_api_key_returns_unauthorized()
    {
        $response = $this->withHeaders([
            'X-API-Key' => 'invalid-api-key',
        ])->getJson('/api/v1/chat/rooms');

        $response->assertStatus(401);
    }

    #[Test]
    public function middleware_tracks_api_usage_for_authenticated_users()
    {
        config(['chat.api.usage_tracking' => true]);
        
        $token = $this->user->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'X-Application-Name' => 'test-app',
        ])->getJson('/api/v1/chat/rooms');

        $response->assertStatus(200);

        // Check that usage was tracked in cache
        $cacheKey = 'api_usage_' . date('Y-m-d-H');
        $usage = Cache::get($cacheKey, []);
        
        $this->assertNotEmpty($usage);
        $this->assertEquals($this->user->id, $usage[0]['user_id']);
        $this->assertEquals('test-app', $usage[0]['application']);
        $this->assertEquals('api/v1/chat/rooms', $usage[0]['endpoint']);
        $this->assertEquals('GET', $usage[0]['method']);
    }

    #[Test]
    public function middleware_does_not_track_usage_when_disabled()
    {
        config(['chat.api.usage_tracking' => false]);
        
        $token = $this->user->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/v1/chat/rooms');

        $response->assertStatus(200);

        // Check that usage was not tracked
        $cacheKey = 'api_usage_' . date('Y-m-d-H');
        $usage = Cache::get($cacheKey, []);
        
        $this->assertEmpty($usage);
    }

    #[Test]
    public function middleware_caches_api_key_lookups()
    {
        config(['chat.api_keys' => [
            'cached-api-key' => ['user_id' => $this->user->id]
        ]]);

        // First request should cache the result
        $response1 = $this->withHeaders([
            'X-API-Key' => 'cached-api-key',
        ])->getJson('/api/v1/chat/rooms');

        $response1->assertStatus(200);

        // Verify the result is cached
        $cachedUser = Cache::get('api_key_user_cached-api-key');
        $this->assertNotNull($cachedUser);
        $this->assertEquals($this->user->id, $cachedUser->id);

        // Second request should use cached result
        $response2 = $this->withHeaders([
            'X-API-Key' => 'cached-api-key',
        ])->getJson('/api/v1/chat/rooms');

        $response2->assertStatus(200);
    }

    #[Test]
    public function route_pattern_matching_works_correctly()
    {
        $middleware = new ChatApiAuthentication();
        $reflection = new \ReflectionClass($middleware);
        $method = $reflection->getMethod('matchesRoute');
        $method->setAccessible(true);

        // Test exact matches
        $this->assertTrue($method->invoke($middleware, 'GET:/api/v1/chat/rooms', 'GET:/api/v1/chat/rooms'));
        
        // Test wildcard matches
        $this->assertTrue($method->invoke($middleware, 'GET:/api/v1/chat/rooms/123', 'GET:/api/v1/chat/rooms/*'));
        $this->assertTrue($method->invoke($middleware, 'POST:/api/v1/chat/rooms/abc-def/messages', 'POST:/api/v1/chat/rooms/*/messages'));
        
        // Test non-matches
        $this->assertFalse($method->invoke($middleware, 'GET:/api/v1/chat/rooms', 'POST:/api/v1/chat/rooms'));
        $this->assertFalse($method->invoke($middleware, 'GET:/api/v1/chat/users', 'GET:/api/v1/chat/rooms'));
    }

    #[Test]
    public function anonymous_access_is_properly_validated()
    {
        $middleware = new ChatApiAuthentication();
        $reflection = new \ReflectionClass($middleware);
        $method = $reflection->getMethod('isAnonymousAllowed');
        $method->setAccessible(true);

        // Test allowed anonymous routes
        $allowedRequests = [
            Request::create('/api/v1/chat/rooms', 'POST'),
            Request::create('/api/v1/chat/rooms/123', 'GET'),
            Request::create('/api/v1/chat/rooms/123/messages', 'POST'),
            Request::create('/api/v1/chat/rooms/123/messages', 'GET'),
            Request::create('/api/v1/chat/files/token123/download', 'GET'),
        ];

        foreach ($allowedRequests as $request) {
            $this->assertTrue($method->invoke($middleware, $request), 
                "Request {$request->method()} {$request->path()} should be allowed anonymously");
        }

        // Test disallowed anonymous routes
        $disallowedRequests = [
            Request::create('/api/v1/chat/rooms', 'GET'),
            Request::create('/api/v1/chat/rooms/123', 'PUT'),
            Request::create('/api/v1/chat/rooms/123', 'DELETE'),
            Request::create('/api/v1/chat/statistics', 'GET'),
        ];

        foreach ($disallowedRequests as $request) {
            $this->assertFalse($method->invoke($middleware, $request), 
                "Request {$request->method()} {$request->path()} should NOT be allowed anonymously");
        }
    }

    #[Test]
    public function bearer_token_validation_handles_invalid_tokens_gracefully()
    {
        $middleware = new ChatApiAuthentication();
        $reflection = new \ReflectionClass($middleware);
        $method = $reflection->getMethod('validateBearerToken');
        $method->setAccessible(true);

        // Test with invalid token
        $result = $method->invoke($middleware, 'invalid-token');
        $this->assertNull($result);

        // Test with malformed token
        $result = $method->invoke($middleware, '');
        $this->assertNull($result);
    }

    #[Test]
    public function api_key_validation_handles_missing_keys_gracefully()
    {
        config(['chat.api_keys' => []]);

        $middleware = new ChatApiAuthentication();
        $reflection = new \ReflectionClass($middleware);
        $method = $reflection->getMethod('validateApiKey');
        $method->setAccessible(true);

        // Test with non-existent API key
        $result = $method->invoke($middleware, 'non-existent-key');
        $this->assertNull($result);
    }

    #[Test]
    public function multiple_authentication_methods_are_tried_in_order()
    {
        // Set up API key
        config(['chat.api_keys' => [
            'test-api-key' => ['user_id' => $this->user->id]
        ]]);

        // Create bearer token
        $token = $this->user->createToken('test-token')->plainTextToken;

        // Request with both bearer token and API key (bearer should take precedence)
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'X-API-Key' => 'test-api-key',
        ])->getJson('/api/v1/chat/rooms');

        $response->assertStatus(200);
        
        // Verify the user was authenticated (both methods would work, but bearer is tried first)
        $this->assertEquals($this->user->id, auth()->id());
    }
}
