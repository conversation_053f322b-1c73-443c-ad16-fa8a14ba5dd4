<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\ChatRoom;
use App\Models\ChatSystemSetting;
use App\Services\ChatAIService;
use App\Services\ActivityLogger;
use App\Services\DashboardCacheService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use OpenAI\Laravel\Facades\OpenAI;
use PHPUnit\Framework\Attributes\Test;

class ChatAIMultiLanguageTest extends TestCase
{
    use RefreshDatabase;

    protected ChatAIService $aiService;
    protected User $user;
    protected ChatRoom $room;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles
        $userRole = Role::factory()->create(['name' => 'user']);

        // Create user
        $this->user = User::factory()->create(['role_id' => $userRole->id]);

        // Create chat room
        $this->room = ChatRoom::factory()->create([
            'status' => 'active',
            'language' => 'en',
        ]);

        // Enable AI
        ChatSystemSetting::updateOrCreate(
            ['setting_key' => 'ai_enabled'],
            [
                'setting_value' => 'true',
                'setting_type' => 'boolean',
                'description' => 'Enable or disable AI chatbot',
                'is_public' => true,
            ]
        );

        // Mock dependencies
        $this->mock(ActivityLogger::class, function ($mock) {
            $mock->shouldReceive('log')->andReturn(new \App\Models\ActivityLog());
        });

        $this->mock(DashboardCacheService::class, function ($mock) {
            $mock->shouldReceive('invalidatePattern')->andReturn(true);
            $mock->shouldReceive('remember')->andReturnUsing(function ($key, $callback, $ttl = null) {
                return $callback();
            });
        });

        // Create AI service
        $this->aiService = app(ChatAIService::class);

        // Clear cache
        Cache::flush();
    }

    #[Test]
    public function ai_service_detects_english_language()
    {
        $language = $this->aiService->detectLanguage('Hello, can you help me with my order?');
        
        $this->assertEquals('en', $language);
    }

    #[Test]
    public function ai_service_detects_afrikaans_language()
    {
        $language = $this->aiService->detectLanguage('Hallo, kan jy my help met my bestelling?');
        
        $this->assertEquals('af', $language);
    }

    #[Test]
    public function ai_service_detects_zulu_language()
    {
        $language = $this->aiService->detectLanguage('Sawubona, ungangisiza nge-oda yami?');
        
        $this->assertEquals('zu', $language);
    }

    #[Test]
    public function ai_service_detects_xhosa_language()
    {
        $language = $this->aiService->detectLanguage('Molo, ungandinceda nge-oda yam?');

        $this->assertEquals('xh', $language);
    }

    #[Test]
    public function ai_service_detects_spanish_language()
    {
        $language = $this->aiService->detectLanguage('Hola, ¿puedes ayudarme con mi pedido?');

        $this->assertEquals('es', $language);
    }

    #[Test]
    public function ai_service_detects_french_language()
    {
        $language = $this->aiService->detectLanguage('Bonjour, pouvez-vous m\'aider avec ma commande?');

        $this->assertEquals('fr', $language);
    }

    #[Test]
    public function ai_service_defaults_to_english_for_unknown_language()
    {
        $language = $this->aiService->detectLanguage('123 456 789');
        
        $this->assertEquals('en', $language);
    }

    #[Test]
    public function ai_service_provides_localized_templates()
    {
        // Test English templates
        $englishTemplates = $this->aiService->getLocalizedTemplates('en');
        $this->assertArrayHasKey('greeting', $englishTemplates);
        $this->assertStringContainsString('Hello', $englishTemplates['greeting']);

        // Test Afrikaans templates
        $afrikaansTemplates = $this->aiService->getLocalizedTemplates('af');
        $this->assertArrayHasKey('greeting', $afrikaansTemplates);
        $this->assertStringContainsString('Hallo', $afrikaansTemplates['greeting']);

        // Test Zulu templates
        $zuluTemplates = $this->aiService->getLocalizedTemplates('zu');
        $this->assertArrayHasKey('greeting', $zuluTemplates);
        $this->assertStringContainsString('Sawubona', $zuluTemplates['greeting']);

        // Test Xhosa templates
        $xhosaTemplates = $this->aiService->getLocalizedTemplates('xh');
        $this->assertArrayHasKey('greeting', $xhosaTemplates);
        $this->assertStringContainsString('Molo', $xhosaTemplates['greeting']);

        // Test Spanish templates
        $spanishTemplates = $this->aiService->getLocalizedTemplates('es');
        $this->assertArrayHasKey('greeting', $spanishTemplates);
        $this->assertStringContainsString('Hola', $spanishTemplates['greeting']);

        // Test French templates
        $frenchTemplates = $this->aiService->getLocalizedTemplates('fr');
        $this->assertArrayHasKey('greeting', $frenchTemplates);
        $this->assertStringContainsString('Bonjour', $frenchTemplates['greeting']);
    }

    #[Test]
    public function ai_service_includes_detected_language_in_response()
    {
        // Mock OpenAI response
        OpenAI::fake([
            \OpenAI\Responses\Chat\CreateResponse::fake([
                'choices' => [
                    [
                        'message' => [
                            'content' => 'Hallo! Hoe kan ek jou vandag help?'
                        ]
                    ]
                ],
                'usage' => [
                    'total_tokens' => 25
                ]
            ])
        ]);

        $response = $this->aiService->generateResponse(
            'Hallo, ek het hulp nodig.',
            $this->room,
            []
        );

        $this->assertArrayHasKey('detected_language', $response);
        $this->assertEquals('af', $response['detected_language']);
    }

    #[Test]
    public function ai_service_uses_localized_fallback_responses()
    {
        // Disable AI to trigger fallback
        ChatSystemSetting::updateOrCreate(
            ['setting_key' => 'ai_enabled'],
            [
                'setting_value' => 'false',
                'setting_type' => 'boolean',
                'description' => 'Enable or disable AI chatbot',
                'is_public' => true,
            ]
        );

        // Clear cache to ensure setting change takes effect
        Cache::flush();

        $response = $this->aiService->generateResponse(
            'Hallo, ek het hulp nodig.',
            $this->room,
            []
        );

        $this->assertArrayHasKey('detected_language', $response);
        $this->assertEquals('af', $response['detected_language']);
        $this->assertArrayHasKey('language', $response);
        $this->assertEquals('af', $response['language']);
        $this->assertStringContainsString('Jammer', $response['response']); // Afrikaans fallback
    }

    #[Test]
    public function language_detection_is_cached()
    {
        $message = 'Hello, this is a test message for caching.';
        
        // First call
        $result1 = $this->aiService->detectLanguage($message);
        
        // Second call should use cache
        $result2 = $this->aiService->detectLanguage($message);
        
        $this->assertEquals($result1, $result2);
        
        // Verify cache key exists
        $cacheKey = 'language_detection_' . md5($message);
        $this->assertTrue(Cache::has($cacheKey));
    }

    #[Test]
    public function localized_templates_are_cached()
    {
        // First call
        $templates1 = $this->aiService->getLocalizedTemplates('af');
        
        // Second call should use cache
        $templates2 = $this->aiService->getLocalizedTemplates('af');
        
        $this->assertEquals($templates1, $templates2);
        
        // Verify cache key exists
        $cacheKey = 'chat_templates_af';
        $this->assertTrue(Cache::has($cacheKey));
    }

    #[Test]
    public function ai_service_handles_room_language_preference()
    {
        // Set room language to Afrikaans
        $this->room->update(['language' => 'af']);

        // Mock OpenAI response
        OpenAI::fake([
            \OpenAI\Responses\Chat\CreateResponse::fake([
                'choices' => [
                    [
                        'message' => [
                            'content' => 'Ek kan jou help!'
                        ]
                    ]
                ],
                'usage' => [
                    'total_tokens' => 15
                ]
            ])
        ]);

        $response = $this->aiService->generateResponse(
            'Hello, I need help.', // English message
            $this->room,
            []
        );

        // Should detect English but use room's Afrikaans preference
        $this->assertEquals('en', $response['detected_language']);
        $this->assertArrayHasKey('language', $response);
    }
}
