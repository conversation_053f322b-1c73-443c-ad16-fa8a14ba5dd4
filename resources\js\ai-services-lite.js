/**
 * Lightweight AI Services Animations
 * CSS-based animations for better performance
 */

class AIServicesLite {
    constructor() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }

    init() {
        // Only initialize on AI services page
        if (!document.querySelector('#ai-services-hero')) {
            return;
        }

        this.initScrollAnimations();
        this.initHoverEffects();
    }

    initScrollAnimations() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, { threshold: 0.1 });

        // Observe elements for scroll animations
        document.querySelectorAll('.ai-service-card, .ai-3d-icon').forEach(el => {
            observer.observe(el);
        });
    }

    initHoverEffects() {
        // Add interactive hover effects
        document.querySelectorAll('.ai-3d-icon').forEach(icon => {
            icon.addEventListener('mouseenter', () => {
                icon.style.transform = 'scale(1.05) rotateY(10deg)';
            });
            
            icon.addEventListener('mouseleave', () => {
                icon.style.transform = 'scale(1) rotateY(0deg)';
            });
        });
    }
}

// Initialize lightweight version
new AIServicesLite();