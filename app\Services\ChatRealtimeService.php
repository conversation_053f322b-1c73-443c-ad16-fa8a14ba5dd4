<?php

namespace App\Services;

use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\ChatParticipant;
use App\Events\Chat\MessageSent;
use App\Events\Chat\UserTyping;
use App\Events\Chat\UserOnlineStatus;
use App\Events\Chat\MessageRead;
use App\Events\Chat\RoomJoined;
use App\Events\Chat\RoomLeft;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ChatRealtimeService
{
    private const TYPING_TIMEOUT = 300; // 300ms debouncing
    private const ONLINE_STATUS_TIMEOUT = 30; // 30s throttling
    private const CONNECTION_POOL_KEY = 'chat:connections';
    private const TYPING_CACHE_KEY = 'chat:typing';
    private const ONLINE_CACHE_KEY = 'chat:online';

    /**
     * Broadcast a new message to room participants.
     */
    public function broadcastMessage(ChatMessage $message, ChatRoom $room): void
    {
        try {
            // Ensure user relationship is loaded for proper sender_type calculation
            $message->load('user');

            event(new MessageSent($message, $room));

            // Log the broadcast
            Log::info('Message broadcasted', [
                'message_id' => $message->id,
                'room_uuid' => $room->uuid,
                'sender' => $message->sender_name,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to broadcast message', [
                'message_id' => $message->id,
                'room_uuid' => $room->uuid,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Handle typing indicator with debouncing.
     */
    public function handleTyping(ChatRoom $room, string $userType, int $userId, string $userName, bool $isTyping = true): void
    {
        $cacheKey = $this->getTypingCacheKey($room->uuid, $userType, $userId);
        
        if ($isTyping) {
            // Set typing status with timeout
            Cache::put($cacheKey, [
                'user_type' => $userType,
                'user_id' => $userId,
                'user_name' => $userName,
                'started_at' => now(),
            ], now()->addSeconds(self::TYPING_TIMEOUT / 1000 * 2)); // Double timeout for safety
            
            // Broadcast typing event
            event(new UserTyping($room, $userType, $userId, $userName, true));
        } else {
            // Remove typing status
            Cache::forget($cacheKey);
            
            // Broadcast stop typing event
            event(new UserTyping($room, $userType, $userId, $userName, false));
        }
    }

    /**
     * Update user online status with throttling.
     */
    public function updateOnlineStatus(ChatRoom $room, string $userType, int $userId, string $userName, bool $isOnline): void
    {
        $cacheKey = $this->getOnlineCacheKey($room->uuid, $userType, $userId);
        $lastUpdate = Cache::get($cacheKey . ':last_update');
        
        // Throttle updates to prevent spam
        if ($lastUpdate && $lastUpdate->diffInSeconds(now()) < self::ONLINE_STATUS_TIMEOUT) {
            return;
        }
        
        // Update cache
        if ($isOnline) {
            Cache::put($cacheKey, [
                'user_type' => $userType,
                'user_id' => $userId,
                'user_name' => $userName,
                'last_seen' => now(),
            ], now()->addMinutes(5));
        } else {
            Cache::forget($cacheKey);
        }
        
        Cache::put($cacheKey . ':last_update', now(), now()->addSeconds(self::ONLINE_STATUS_TIMEOUT));
        
        // Broadcast online status event
        event(new UserOnlineStatus($room, $userType, $userId, $userName, $isOnline));
    }

    /**
     * Mark message as read and broadcast read receipt.
     */
    public function markMessageAsRead(ChatMessage $message, ChatRoom $room, string $readerType, int $readerId, string $readerName): void
    {
        try {
            // Update message read status (if you have a read_receipts table)
            // For now, we'll just broadcast the event
            
            event(new MessageRead($message, $room, $readerType, $readerId, $readerName));
            
            Log::info('Message marked as read', [
                'message_id' => $message->id,
                'room_uuid' => $room->uuid,
                'reader' => $readerName,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to mark message as read', [
                'message_id' => $message->id,
                'room_uuid' => $room->uuid,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Handle user joining a room.
     */
    public function handleRoomJoin(ChatRoom $room, ChatParticipant $participant): void
    {
        try {
            // Update online status
            $this->updateOnlineStatus(
                $room,
                $participant->participant_type,
                $participant->user_id,
                $participant->display_name,
                true
            );
            
            // Broadcast room joined event
            event(new RoomJoined($room, $participant));
            
            Log::info('User joined room', [
                'room_uuid' => $room->uuid,
                'user' => $participant->user_name,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to handle room join', [
                'room_uuid' => $room->uuid,
                'participant_id' => $participant->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Handle user leaving a room.
     */
    public function handleRoomLeave(ChatRoom $room, string $userType, int $userId, string $userName): void
    {
        try {
            // Update online status
            $this->updateOnlineStatus($room, $userType, $userId, $userName, false);
            
            // Clear typing status
            $this->handleTyping($room, $userType, $userId, $userName, false);
            
            // Broadcast room left event
            event(new RoomLeft($room, $userType, $userId, $userName));
            
            Log::info('User left room', [
                'room_uuid' => $room->uuid,
                'user' => $userName,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to handle room leave', [
                'room_uuid' => $room->uuid,
                'user' => $userName,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get currently typing users in a room.
     */
    public function getTypingUsers(string $roomUuid): array
    {
        // For testing with array cache, return empty array
        if (config('cache.default') === 'array') {
            return [];
        }

        $pattern = $this->getTypingCacheKey($roomUuid, '*', '*');
        $keys = Cache::getRedis()->keys($pattern);
        
        $typingUsers = [];
        foreach ($keys as $key) {
            $data = Cache::get($key);
            if ($data && isset($data['started_at'])) {
                // Check if typing timeout has expired
                if (Carbon::parse($data['started_at'])->diffInMilliseconds(now()) < self::TYPING_TIMEOUT * 2) {
                    $typingUsers[] = $data;
                } else {
                    // Clean up expired typing status
                    Cache::forget($key);
                }
            }
        }
        
        return $typingUsers;
    }

    /**
     * Get currently online users in a room.
     */
    public function getOnlineUsers(string $roomUuid): array
    {
        // For testing with array cache, return empty array
        if (config('cache.default') === 'array') {
            return [];
        }

        $pattern = $this->getOnlineCacheKey($roomUuid, '*', '*');
        $keys = Cache::getRedis()->keys($pattern);

        $onlineUsers = [];
        foreach ($keys as $key) {
            $data = Cache::get($key);
            if ($data) {
                $onlineUsers[] = $data;
            }
        }
        
        return $onlineUsers;
    }

    /**
     * Clean up expired connections and statuses.
     */
    public function cleanupExpiredStatuses(): void
    {
        // This method can be called by a scheduled job
        // to clean up expired typing and online statuses
        
        // Implementation would depend on your caching strategy
        // For Redis, you could use SCAN to find and clean expired keys
    }

    /**
     * Get typing cache key.
     */
    private function getTypingCacheKey(string $roomUuid, string $userType, int|string $userId): string
    {
        return self::TYPING_CACHE_KEY . ":{$roomUuid}:{$userType}:{$userId}";
    }

    /**
     * Get online cache key.
     */
    private function getOnlineCacheKey(string $roomUuid, string $userType, int|string $userId): string
    {
        return self::ONLINE_CACHE_KEY . ":{$roomUuid}:{$userType}:{$userId}";
    }
}
