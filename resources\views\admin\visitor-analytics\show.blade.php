@extends('layouts.dashboard')

@section('title', 'Visitor Details - Admin')

@section('content')
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-start">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Visitor Details</h1>
            <p class="text-gray-600 mt-1">Visited {{ $visitorAnalytic->visited_at->format('M j, Y \a\t g:i A') }}</p>
        </div>
        <div class="flex items-center space-x-2">
            @if($visitorAnalytic->is_suspicious)
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    Suspicious Activity
                </span>
            @endif
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {{ $ipStats['total_visits'] }} Total Visits
            </span>
        </div>
    </div>

    <!-- Visitor Information -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
        <h2 class="text-xl font-bold mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
            Visitor Information
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
                <div>
                    <dt class="text-sm font-medium text-gray-500">IP Address</dt>
                    <dd class="mt-1 text-sm text-gray-900 font-mono">{{ $visitorAnalytic->ip_address }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Location</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                        {{ $visitorAnalytic->city ?: 'Unknown City' }}, 
                        {{ $visitorAnalytic->region ?: 'Unknown Region' }}, 
                        {{ $visitorAnalytic->country ?: 'Unknown Country' }}
                    </dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">ISP</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ $visitorAnalytic->isp ?: 'Unknown ISP' }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Organization</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ $visitorAnalytic->organization ?: 'Unknown Organization' }}</dd>
                </div>
            </div>
            <div class="space-y-4">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Device Type</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ $visitorAnalytic->device_type ?: 'Unknown' }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Browser</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ $visitorAnalytic->browser ?: 'Unknown Browser' }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Platform</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ $visitorAnalytic->platform ?: 'Unknown Platform' }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Session Duration</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                        @if($visitorAnalytic->session_duration)
                            {{ gmdate('H:i:s', $visitorAnalytic->session_duration) }}
                        @else
                            Unknown
                        @endif
                    </dd>
                </div>
            </div>
        </div>
    </div>

    <!-- Visit Details -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
        <h2 class="text-xl font-bold mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            Visit Details
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Page URL</dt>
                    <dd class="mt-1 text-sm text-gray-900 break-all">
                        <a href="{{ $visitorAnalytic->page_url }}" target="_blank" class="text-blue-600 hover:text-blue-800">
                            {{ $visitorAnalytic->page_url ?: 'Unknown Page' }}
                        </a>
                    </dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Page Title</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ $visitorAnalytic->page_title ?: 'Unknown Title' }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Referrer</dt>
                    <dd class="mt-1 text-sm text-gray-900 break-all">
                        @if($visitorAnalytic->referrer)
                            <a href="{{ $visitorAnalytic->referrer }}" target="_blank" class="text-blue-600 hover:text-blue-800">
                                {{ $visitorAnalytic->referrer }}
                            </a>
                        @else
                            Direct Visit
                        @endif
                    </dd>
                </div>
            </div>
            <div class="space-y-4">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Visit Time</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ $visitorAnalytic->visited_at->format('M j, Y g:i A') }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Time Ago</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ $visitorAnalytic->visited_at->diffForHumans() }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                    <dd class="mt-1">
                        @if($visitorAnalytic->is_suspicious)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Suspicious Activity Detected
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Normal Activity
                            </span>
                        @endif
                    </dd>
                </div>
            </div>
        </div>
    </div>

    <!-- IP Statistics -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
        <h2 class="text-xl font-bold mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            IP Address Statistics
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">{{ $ipStats['total_visits'] }}</div>
                <div class="text-sm text-gray-500">Total Visits</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-green-600">{{ $ipStats['unique_pages'] }}</div>
                <div class="text-sm text-gray-500">Unique Pages</div>
            </div>
            <div class="text-center">
                <div class="text-sm font-medium text-gray-900">{{ \Carbon\Carbon::parse($ipStats['first_visit'])->format('M j, Y') }}</div>
                <div class="text-sm text-gray-500">First Visit</div>
            </div>
            <div class="text-center">
                <div class="text-sm font-medium text-gray-900">{{ \Carbon\Carbon::parse($ipStats['last_visit'])->format('M j, Y') }}</div>
                <div class="text-sm text-gray-500">Last Visit</div>
            </div>
        </div>
    </div>

    <!-- Technical Information -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
        <h2 class="text-xl font-bold mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Technical Information
        </h2>
        <div class="space-y-4">
            <div>
                <dt class="text-sm font-medium text-gray-500">User Agent</dt>
                <dd class="mt-1 text-sm text-gray-900 break-all">{{ $visitorAnalytic->user_agent ?: 'Unknown User Agent' }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Screen Resolution</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ $visitorAnalytic->screen_resolution ?: 'Unknown' }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Language</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ $visitorAnalytic->language ?: 'Unknown' }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Timezone</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ $visitorAnalytic->timezone ?: 'Unknown' }}</dd>
            </div>
        </div>
    </div>

    <!-- Related Visits -->
    @if($relatedVisits->count() > 0)
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
        <h2 class="text-xl font-bold mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Recent Visits from Same IP ({{ $relatedVisits->count() }})
        </h2>
        <div class="space-y-3 max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
            @foreach($relatedVisits as $visit)
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100">
                <div class="flex-1">
                    <div class="text-sm font-medium text-gray-900">{{ $visit->page_title ?: 'Unknown Page' }}</div>
                    <div class="text-xs text-gray-500 truncate">{{ $visit->page_url }}</div>
                </div>
                <div class="text-right">
                    <div class="text-sm text-gray-900">{{ $visit->visited_at->format('M j, Y') }}</div>
                    <div class="text-xs text-gray-500">{{ $visit->visited_at->format('g:i A') }}</div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
    @endif

    <!-- Actions -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
        <h2 class="text-xl font-bold mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
            Actions
        </h2>
        <div class="flex flex-wrap gap-3">
            <a href="{{ route('admin.visitor-analytics.index', ['search' => $visitorAnalytic->ip_address]) }}" 
               class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                View All Visits from IP
            </a>

            <a href="{{ route('admin.visitor-analytics.index') }}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Analytics
            </a>

            <button onclick="copyToClipboard('{{ $visitorAnalytic->ip_address }}')" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
                Copy IP Address
            </button>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showToast('IP address copied to clipboard!', 'success');
    }, function(err) {
        console.error('Could not copy text: ', err);
        showToast('Failed to copy IP address', 'error');
    });
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    const bgColor = {
        'success': 'bg-green-500',
        'error': 'bg-red-500',
        'warning': 'bg-yellow-500',
        'info': 'bg-blue-500'
    }[type] || 'bg-blue-500';

    toast.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-transform duration-300 translate-x-full`;
    toast.textContent = message;

    document.body.appendChild(toast);

    // Slide in
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 100);

    // Slide out and remove
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 3000);
}
</script>
@endpush
