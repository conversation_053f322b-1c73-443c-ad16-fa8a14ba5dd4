<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Str;
use Laravel\Sanctum\HasApiTokens;
use App\Notifications\ResetPasswordNotification;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasFactory, Notifiable, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'uuid',
        'first_name',
        'last_name',
        'email',
        'password',
        'phone',
        'avatar',
        'role_id',
        'is_active',
        'is_deleted',
        'last_login_at',
        'last_seen_at',
        'chat_blocked',
        'chat_blocked_until',
        'chat_block_reason',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'last_login_at' => 'datetime',
            'last_seen_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
            'is_deleted' => 'boolean',
            'chat_blocked' => 'boolean',
            'chat_blocked_until' => 'datetime',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($user) {
            if (empty($user->uuid)) {
                $user->uuid = Str::uuid();
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Scope a query to only include active users.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('is_deleted', false);
    }

    /**
     * Get the user's full name.
     */
    public function getFullNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }

    /**
     * Get the user's name (alias for full_name for compatibility).
     */
    public function getNameAttribute(): string
    {
        return $this->getFullNameAttribute();
    }

    /**
     * Get the role that the user belongs to.
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

    /**
     * Get the user's login histories.
     */
    public function loginHistories(): HasMany
    {
        return $this->hasMany(LoginHistory::class);
    }

    /**
     * Get the user's login history permission.
     */
    public function loginHistoryPermission(): HasOne
    {
        return $this->hasOne(LoginHistoryPermission::class);
    }

    /**
     * Get the addresses for the user.
     */
    public function addresses(): HasMany
    {
        return $this->hasMany(UserAddress::class);
    }

    /**
     * Get the orders for the user.
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get the projects for the user (as client).
     */
    public function projects(): HasMany
    {
        return $this->hasMany(Project::class, 'client_id');
    }

    /**
     * Get the project applications for the user.
     */
    public function projectApplications(): HasMany
    {
        return $this->hasMany(ProjectApplication::class);
    }

    /**
     * Get the job applications for the user.
     */
    public function jobApplications(): HasMany
    {
        return $this->hasMany(JobApplication::class);
    }

    /**
     * Get the shopping cart for the user.
     */
    public function cart(): HasMany
    {
        return $this->hasMany(ShoppingCart::class);
    }

    /**
     * Get the chat assignments for the user.
     */
    public function chatAssignments(): HasMany
    {
        return $this->hasMany(\App\Models\ChatAssignment::class, 'assigned_to');
    }

    /**
     * Get the user's preferences.
     */
    public function preferences(): HasOne
    {
        return $this->hasOne(UserPreference::class);
    }

    /**
     * Get or create user preferences.
     */
    public function getPreferences(): UserPreference
    {
        return UserPreference::getForUser($this);
    }

    /**
     * Check if user has a specific permission.
     */
    public function hasPermission(string $resource, string $action): bool
    {
        if (!$this->role) {
            return false;
        }

        $permissions = $this->role->permissions ?? [];

        return isset($permissions[$resource]) &&
               in_array($action, $permissions[$resource]);
    }

    /**
     * Send the password reset notification.
     */
    public function sendPasswordResetNotification($token): void
    {
        $this->notify(new ResetPasswordNotification($token));
    }

    /**
     * Check if user is an admin.
     */
    public function isAdmin(): bool
    {
        return $this->role && $this->role->name === 'admin';
    }

    /**
     * Check if user is staff.
     */
    public function isStaff(): bool
    {
        return $this->role && $this->role->name === 'staff';
    }

    /**
     * Check if user is a client.
     */
    public function isClient(): bool
    {
        return $this->role && $this->role->name === 'client';
    }

    /**
     * Check if user is a customer.
     */
    public function isCustomer(): bool
    {
        return $this->role && $this->role->name === 'customer';
    }

    /**
     * Check if user has any of the given roles.
     */
    public function hasRole($roles): bool
    {
        if (!$this->role) {
            return false;
        }

        if (is_string($roles)) {
            return $this->role->name === $roles;
        }

        if (is_array($roles)) {
            return in_array($this->role->name, $roles);
        }

        return false;
    }

    /**
     * Check if user has admin or staff role.
     */
    public function isAdminOrStaff(): bool
    {
        return $this->isAdmin() || $this->isStaff();
    }

    /**
     * Check if user has customer or client role.
     */
    public function isCustomerOrClient(): bool
    {
        return $this->isCustomer() || $this->isClient();
    }

    /**
     * Assign a role to the user by role name.
     */
    public function assignRole(string $roleName): bool
    {
        $role = Role::where('name', $roleName)->first();

        if (!$role) {
            return false;
        }

        $this->role_id = $role->id;
        return $this->save();
    }

    /**
     * Remove role from user.
     */
    public function removeRole(): bool
    {
        $this->role_id = null;
        return $this->save();
    }

    /**
     * Check if user is online (last seen within 5 minutes).
     */
    public function isOnline(): bool
    {
        return $this->last_seen_at && $this->last_seen_at >= now()->subMinutes(5);
    }

    /**
     * Update last seen timestamp.
     */
    public function updateLastSeen(): bool
    {
        $this->last_seen_at = now();
        return $this->save();
    }

    /**
     * Scope to get online users.
     */
    public function scopeOnline($query, int $minutesThreshold = 5)
    {
        return $query->where('last_seen_at', '>=', now()->subMinutes($minutesThreshold));
    }

    /**
     * Scope to get staff members (admin and staff roles).
     */
    public function scopeStaff($query)
    {
        return $query->whereHas('role', function($q) {
            $q->whereIn('name', ['staff', 'admin']);
        });
    }

}
