<?php

namespace App\Http\Controllers;

use App\Models\BlogPost;
use App\Models\BlogCategory;
use App\Models\Service;
use App\Services\VisitorAnalytics;
use Illuminate\Http\Request;
use Illuminate\View\View;

class BlogController extends Controller
{
    protected VisitorAnalytics $visitorAnalytics;

    public function __construct(VisitorAnalytics $visitorAnalytics)
    {
        $this->visitorAnalytics = $visitorAnalytics;
    }

    /**
     * Show blog posts by category.
     */
    public function category(BlogCategory $category): View
    {
        // Track category page visit
        $this->visitorAnalytics->trackPageVisit("Blog Category: {$category->name}", [
            'page_type' => 'blog_category',
            'category_id' => $category->id,
            'category_slug' => $category->slug
        ]);

        $posts = BlogPost::where('is_published', true)
            ->where('is_deleted', false)
            ->where('category_id', $category->id)
            ->with(['author'])
            ->orderBy('published_at', 'desc')
            ->paginate(12);

        // Track category engagement
        $this->visitorAnalytics->trackJourneyStep(
            'Blog Category Browse',
            'content_discovery',
            [
                'category' => $category->name,
                'posts_count' => $posts->total(),
                'page' => $posts->currentPage()
            ]
        );

        return view('pages.blog.category', compact('category', 'posts'));
    }

    /**
     * Show blog posts by service.
     */
    public function service(Service $service): View
    {
        // Track service blog page visit
        $this->visitorAnalytics->trackPageVisit("Blog Posts: {$service->name}", [
            'page_type' => 'blog_service',
            'service_id' => $service->id,
            'service_slug' => $service->slug
        ]);

        $posts = BlogPost::where('is_published', true)
            ->where('is_deleted', false)
            ->withServices([$service->id])
            ->with(['category', 'author'])
            ->orderBy('published_at', 'desc')
            ->paginate(12);

        // Track service content engagement
        $this->visitorAnalytics->trackJourneyStep(
            'Service Blog Browse',
            'content_discovery',
            [
                'service' => $service->name,
                'posts_count' => $posts->total(),
                'page' => $posts->currentPage()
            ]
        );

        return view('pages.blog.service', compact('service', 'posts'));
    }

    /**
     * Search blog posts.
     */
    public function search(Request $request): View
    {
        $query = $request->get('q', '');
        
        // Track blog search
        $this->visitorAnalytics->trackPageVisit("Blog Search: {$query}", [
            'page_type' => 'blog_search',
            'search_query' => $query
        ]);

        $posts = collect();
        
        if (strlen($query) >= 2) {
            $posts = BlogPost::where('is_published', true)
                ->where('is_deleted', false)
                ->where(function($queryBuilder) use ($query) {
                    $queryBuilder->where('title', 'LIKE', "%{$query}%")
                        ->orWhere('excerpt', 'LIKE', "%{$query}%")
                        ->orWhere('content', 'LIKE', "%{$query}%")
                        ->orWhere('meta_keywords', 'LIKE', "%{$query}%");
                })
                ->with(['category', 'author'])
                ->orderBy('is_featured', 'desc')
                ->orderBy('published_at', 'desc')
                ->paginate(12);

            // Track search engagement
            $this->visitorAnalytics->trackJourneyStep(
                'Blog Search',
                'search',
                [
                    'query' => $query,
                    'results_count' => $posts->total(),
                    'page' => $posts->currentPage()
                ]
            );
        }

        return view('pages.blog.search', compact('posts', 'query'));
    }

    /**
     * Show featured blog posts.
     */
    public function featured(): View
    {
        // Track featured posts page visit
        $this->visitorAnalytics->trackPageVisit('Featured Blog Posts', [
            'page_type' => 'blog_featured'
        ]);

        $posts = BlogPost::where('is_published', true)
            ->where('is_deleted', false)
            ->where('is_featured', true)
            ->with(['category', 'author'])
            ->orderBy('published_at', 'desc')
            ->paginate(12);

        return view('pages.blog.featured', compact('posts'));
    }

    /**
     * Get blog archive by year/month.
     */
    public function archive(Request $request): View
    {
        $year = $request->get('year');
        $month = $request->get('month');

        // Track archive page visit
        $this->visitorAnalytics->trackPageVisit("Blog Archive: {$year}" . ($month ? "/{$month}" : ''), [
            'page_type' => 'blog_archive',
            'year' => $year,
            'month' => $month
        ]);

        $query = BlogPost::where('is_published', true)
            ->where('is_deleted', false);

        if ($year) {
            $query->whereYear('published_at', $year);
        }

        if ($month) {
            $query->whereMonth('published_at', $month);
        }

        $posts = $query->with(['category', 'author'])
            ->orderBy('published_at', 'desc')
            ->paginate(12);

        return view('pages.blog.archive', compact('posts', 'year', 'month'));
    }

    /**
     * Get blog posts as RSS feed.
     */
    public function rss(): \Illuminate\Http\Response
    {
        $posts = BlogPost::where('is_published', true)
            ->where('is_deleted', false)
            ->with(['category', 'author'])
            ->orderBy('published_at', 'desc')
            ->limit(20)
            ->get();

        $rss = view('pages.blog.rss', compact('posts'))->render();

        return response($rss, 200, [
            'Content-Type' => 'application/rss+xml; charset=UTF-8'
        ]);
    }

    /**
     * Get popular blog posts.
     */
    public function popular(): View
    {
        // Track popular posts page visit
        $this->visitorAnalytics->trackPageVisit('Popular Blog Posts', [
            'page_type' => 'blog_popular'
        ]);

        $posts = BlogPost::where('is_published', true)
            ->where('is_deleted', false)
            ->where('view_count', '>', 0)
            ->with(['category', 'author'])
            ->orderBy('view_count', 'desc')
            ->orderBy('published_at', 'desc')
            ->paginate(12);

        return view('pages.blog.popular', compact('posts'));
    }
}
