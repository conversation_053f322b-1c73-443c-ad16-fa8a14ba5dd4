<?php

namespace Tests\Unit;

use Tests\TestCase;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Http\Request;
use PHPUnit\Framework\Attributes\Test;

class RateLimiterDebugTest extends TestCase
{
    #[Test]
    public function rate_limiters_are_defined()
    {
        $expectedLimiters = [
            'chat-room-creation',
            'chat-message-sending',
            'chat-realtime',
            'chat-file-upload',
            'chat-usage',
            'api',
        ];

        foreach ($expectedLimiters as $limiterName) {
            $limiter = RateLimiter::limiter($limiterName);
            $this->assertNotNull($limiter, "Rate limiter '{$limiterName}' should be defined");
        }
    }

    #[Test]
    public function old_rate_limiters_are_not_defined()
    {
        $oldLimiters = [
            '10:60',
            '60:1',
        ];

        foreach ($oldLimiters as $limiterName) {
            $limiter = RateLimiter::limiter($limiterName);
            $this->assertNull($limiter, "Old rate limiter '{$limiterName}' should not be defined");
        }
    }

    #[Test]
    public function app_service_provider_is_loaded()
    {
        // Check if the AppServiceProvider is properly loaded
        $providers = app()->getLoadedProviders();
        $this->assertArrayHasKey('App\\Providers\\AppServiceProvider', $providers);
    }
}
