<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_files', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('chat_room_id')->constrained('chat_rooms')->onDelete('cascade');
            $table->foreignId('chat_message_id')->constrained('chat_messages')->onDelete('cascade');
            $table->foreignId('uploaded_by')->nullable()->constrained('users')->onDelete('set null');
            $table->string('original_filename');
            $table->string('stored_filename');
            $table->string('file_path', 500);
            $table->bigInteger('file_size')->unsigned();
            $table->string('mime_type', 100);
            $table->string('file_hash', 64)->comment('SHA-256 hash for deduplication');
            $table->boolean('is_image')->default(false);
            $table->boolean('is_scanned')->default(false);
            $table->enum('scan_result', ['clean', 'infected', 'suspicious', 'error'])->nullable();
            $table->integer('download_count')->unsigned()->default(0);
            $table->timestamp('expires_at')->nullable()->comment('File retention policy');
            $table->timestamps();
            
            // Performance indexes
            $table->index(['chat_room_id', 'created_at'], 'idx_files_room_created');
            $table->index('chat_message_id', 'idx_files_message_id');
            $table->index('file_hash', 'idx_files_hash');
            $table->index('expires_at', 'idx_files_expires_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_files');
    }
};
