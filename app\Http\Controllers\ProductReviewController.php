<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductReview;
use App\Models\Order;
use App\Services\ActivityLogger;
use App\Services\VisitorAnalytics;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProductReviewController extends Controller
{
    protected ActivityLogger $activityLogger;
    protected VisitorAnalytics $visitorAnalytics;

    public function __construct(ActivityLogger $activityLogger, VisitorAnalytics $visitorAnalytics)
    {
        $this->activityLogger = $activityLogger;
        $this->visitorAnalytics = $visitorAnalytics;

        // Require authentication for all review actions
        $this->middleware('auth');

        // Rate limiting for review submission
        $this->middleware('throttle:10,1')->only(['store', 'update']);
    }

    /**
     * Store a new product review.
     */
    public function store(Request $request, Product $product): JsonResponse
    {
        try {
            // Validate purchase eligibility first
            $eligibility = ProductReview::validatePurchaseEligibility($product->id, Auth::id());

            if (!$eligibility['eligible']) {
                return response()->json([
                    'success' => false,
                    'message' => $eligibility['reason']
                ], 403);
            }

            // Check if user already has a review for this product
            if (!$eligibility['can_review_new']) {
                return response()->json([
                    'success' => false,
                    'message' => 'You have already reviewed this product. You can update your existing review instead.'
                ], 409);
            }

            // Validate request data
            $validator = Validator::make($request->all(), [
                'rating' => 'required|integer|min:1|max:5',
                'review_content' => 'required|string|min:10|max:1000',
                'order_id' => 'required|exists:orders,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please check your input.',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Verify the order belongs to the user and contains the product
            $order = Order::where('id', $request->order_id)
                          ->where('user_id', Auth::id())
                          ->where('status', 'delivered')
                          ->whereHas('orderItems', function($query) use ($product) {
                              $query->where('product_id', $product->id);
                          })
                          ->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid order or you have not received this product yet.'
                ], 403);
            }

            // Create the review
            $review = ProductReview::create([
                'product_id' => $product->id,
                'user_id' => Auth::id(),
                'order_id' => $order->id,
                'rating' => $request->rating,
                'review_content' => $request->review_content,
                'is_verified_purchase' => true,
                'purchase_date' => $order->created_at,
                'delivery_date' => $order->updated_at, // Assuming updated_at is when delivered
                'is_approved' => true, // Auto-approve verified purchases
            ]);

            // Log the activity
            $this->activityLogger->log(
                'product_review_submitted',
                'ProductReview',
                $review->id,
                [
                    'product_name' => $product->name,
                    'rating' => $request->rating,
                    'review_length' => strlen($request->review_content),
                    'order_id' => $order->id,
                ]
            );

            // Track with visitor analytics
            $this->visitorAnalytics->trackEvent('product_review_submitted', [
                'product_id' => $product->id,
                'product_name' => $product->name,
                'rating' => $request->rating,
                'user_id' => Auth::id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Thank you for your review! It has been published.',
                'review' => [
                    'id' => $review->uuid,
                    'rating' => $review->rating,
                    'star_rating' => $review->star_rating,
                    'content' => $review->review_content,
                    'created_at' => $review->formatted_created_date,
                    'user_name' => Auth::user()->first_name . ' ' . Auth::user()->last_name,
                    'is_verified_purchase' => true,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Product review submission failed', [
                'product_id' => $product->id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while submitting your review. Please try again.'
            ], 500);
        }
    }

    /**
     * Update an existing product review.
     */
    public function update(Request $request, Product $product, ProductReview $review): JsonResponse
    {
        try {
            // Verify the review belongs to the authenticated user
            if ($review->user_id !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You can only edit your own reviews.'
                ], 403);
            }

            // Check if review can be edited (within 30 days)
            if (!$review->canBeEditedBy(Auth::id())) {
                return response()->json([
                    'success' => false,
                    'message' => 'This review can no longer be edited. Reviews can only be edited within 30 days of submission.'
                ], 403);
            }

            // Validate request data
            $validator = Validator::make($request->all(), [
                'rating' => 'required|integer|min:1|max:5',
                'review_content' => 'required|string|min:10|max:1000'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please check your input.',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Update the review
            $review->update([
                'rating' => $request->rating,
                'review_content' => $request->review_content,
            ]);

            // Log the activity
            $this->activityLogger->log(
                'product_review_updated',
                'ProductReview',
                $review->id,
                [
                    'product_name' => $product->name,
                    'old_rating' => $review->getOriginal('rating'),
                    'new_rating' => $request->rating,
                    'review_length' => strlen($request->review_content),
                ]
            );

            // Track with visitor analytics
            $this->visitorAnalytics->trackEvent('product_review_updated', [
                'product_id' => $product->id,
                'product_name' => $product->name,
                'rating' => $request->rating,
                'user_id' => Auth::id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Your review has been updated successfully.',
                'review' => [
                    'id' => $review->uuid,
                    'rating' => $review->rating,
                    'star_rating' => $review->star_rating,
                    'content' => $review->review_content,
                    'updated_at' => $review->updated_at->format('M j, Y g:i A'),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Product review update failed', [
                'review_id' => $review->id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating your review. Please try again.'
            ], 500);
        }
    }

    /**
     * Mark a review as helpful.
     */
    public function markHelpful(Request $request, ProductReview $review): JsonResponse
    {
        try {
            if ($review->markAsHelpful(Auth::id())) {
                // Log the activity
                $this->activityLogger->log(
                    'product_review_marked_helpful',
                    'ProductReview',
                    $review->id,
                    [
                        'product_name' => $review->product->name,
                        'review_author' => $review->user->first_name . ' ' . $review->user->last_name,
                    ]
                );

                return response()->json([
                    'success' => true,
                    'message' => 'Thank you for your feedback!',
                    'helpful_count' => $review->helpful_count
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'You have already marked this review as helpful.'
            ], 409);

        } catch (\Exception $e) {
            Log::error('Mark review helpful failed', [
                'review_id' => $review->id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred. Please try again.'
            ], 500);
        }
    }

    /**
     * Remove helpful mark from a review.
     */
    public function removeHelpful(Request $request, ProductReview $review): JsonResponse
    {
        try {
            if ($review->removeHelpful(Auth::id())) {
                return response()->json([
                    'success' => true,
                    'message' => 'Helpful mark removed.',
                    'helpful_count' => $review->helpful_count
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'You have not marked this review as helpful.'
            ], 409);

        } catch (\Exception $e) {
            Log::error('Remove helpful mark failed', [
                'review_id' => $review->id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred. Please try again.'
            ], 500);
        }
    }

    /**
     * Flag a review as inappropriate.
     */
    public function flag(Request $request, ProductReview $review): JsonResponse
    {
        try {
            if ($review->flagAsInappropriate(Auth::id())) {
                // Log the activity
                $this->activityLogger->log(
                    'product_review_flagged',
                    'ProductReview',
                    $review->id,
                    [
                        'product_name' => $review->product->name,
                        'review_author' => $review->user->first_name . ' ' . $review->user->last_name,
                        'flag_count' => $review->flag_count,
                    ]
                );

                return response()->json([
                    'success' => true,
                    'message' => 'Thank you for reporting this review. We will investigate it.'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'You have already flagged this review.'
            ], 409);

        } catch (\Exception $e) {
            Log::error('Flag review failed', [
                'review_id' => $review->id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred. Please try again.'
            ], 500);
        }
    }

    /**
     * Get user's purchase eligibility for reviewing a product.
     */
    public function checkEligibility(Product $product): JsonResponse
    {
        try {
            $eligibility = ProductReview::validatePurchaseEligibility($product->id, Auth::id());

            return response()->json([
                'success' => true,
                'eligibility' => $eligibility
            ]);

        } catch (\Exception $e) {
            Log::error('Check review eligibility failed', [
                'product_id' => $product->id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Unable to check eligibility. Please try again.'
            ], 500);
        }
    }
}
