<?php

namespace App\Observers;

use App\Models\ProductCategory;
use App\Services\ShopCacheService;

class ProductCategoryObserver
{
    protected $shopCache;

    public function __construct(ShopCacheService $shopCache)
    {
        $this->shopCache = $shopCache;
    }

    /**
     * Handle the ProductCategory "created" event.
     */
    public function created(ProductCategory $category): void
    {
        $this->invalidateCategoryCaches();
    }

    /**
     * Handle the ProductCategory "updated" event.
     */
    public function updated(ProductCategory $category): void
    {
        $this->invalidateCategoryCaches();
        
        // If active status changed, invalidate all shop caches
        if ($category->wasChanged('is_active')) {
            $this->shopCache->invalidateAll();
        }
        
        // If parent changed, invalidate all category caches
        if ($category->wasChanged('parent_id')) {
            $this->shopCache->invalidateCategoryCaches();
        }
    }

    /**
     * Handle the ProductCategory "deleted" event.
     */
    public function deleted(ProductCategory $category): void
    {
        $this->invalidateCategoryCaches();
    }

    /**
     * Handle the ProductCategory "restored" event.
     */
    public function restored(ProductCategory $category): void
    {
        $this->invalidateCategoryCaches();
    }

    /**
     * Handle the ProductCategory "force deleted" event.
     */
    public function forceDeleted(ProductCategory $category): void
    {
        $this->invalidateCategoryCaches();
    }

    /**
     * Invalidate category-related caches.
     */
    private function invalidateCategoryCaches(): void
    {
        $this->shopCache->invalidateCategoryCaches();
    }
}
