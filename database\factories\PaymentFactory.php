<?php

namespace Database\Factories;

use App\Models\Currency;
use App\Models\Order;
use App\Models\Payment;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Payment>
 */
class PaymentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Payment::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'uuid' => Str::uuid(),
            'order_id' => Order::factory(),
            'payment_method' => $this->faker->randomElement(['credit_card', 'debit_card', 'stripe', 'paypal']),
            'payment_gateway' => $this->faker->randomElement(['stripe', 'paypal', 'manual']),
            'transaction_id' => 'PAY-' . strtoupper($this->faker->bothify('??##??##??##')),
            'gateway_transaction_id' => $this->faker->optional()->bothify('txn_#?#?#?#?#?#?'),
            'amount' => $this->faker->randomFloat(2, 10, 1000),
            'currency_id' => function () {
                return Currency::where('is_default', true)->first()?->id
                    ?? Currency::first()?->id
                    ?? Currency::create([
                        'code' => 'ZAR',
                        'name' => 'South African Rand',
                        'symbol' => 'R',
                        'exchange_rate' => 1.0000,
                        'is_default' => true,
                        'is_active' => true,
                    ])->id;
            },
            'status' => $this->faker->randomElement(['pending', 'completed', 'failed']),
            'gateway_response' => null,
            'processed_at' => null,
            'failed_at' => null,
            'failure_reason' => null,
            'refunded_at' => null,
            'refund_amount' => null,
            'refund_reason' => null,
            'metadata' => null,
            'is_deleted' => false,
        ];
    }

    /**
     * Indicate that the payment is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'processed_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'gateway_response' => [
                'transaction_id' => $this->faker->bothify('txn_#?#?#?#?#?#?'),
                'status' => 'succeeded',
                'message' => 'Payment completed successfully',
            ],
        ]);
    }

    /**
     * Indicate that the payment is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'processed_at' => null,
            'failed_at' => null,
        ]);
    }

    /**
     * Indicate that the payment is failed.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'failed_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'failure_reason' => $this->faker->randomElement([
                'Insufficient funds',
                'Card declined',
                'Invalid card number',
                'Expired card',
                'Gateway timeout',
            ]),
            'gateway_response' => [
                'error_code' => $this->faker->randomElement(['card_declined', 'insufficient_funds', 'expired_card']),
                'error_message' => 'Payment failed',
            ],
        ]);
    }

    /**
     * Indicate that the payment is refunded.
     */
    public function refunded(): static
    {
        return $this->state(function (array $attributes) {
            $refundAmount = $this->faker->randomFloat(2, 1, $attributes['amount']);
            $isPartialRefund = $refundAmount < $attributes['amount'];

            return [
                'status' => $isPartialRefund ? 'partially_refunded' : 'refunded',
                'refunded_at' => $this->faker->dateTimeBetween('-2 weeks', 'now'),
                'refund_amount' => $refundAmount,
                'refund_reason' => $this->faker->randomElement([
                    'Customer request',
                    'Product defect',
                    'Order cancellation',
                    'Duplicate payment',
                ]),
            ];
        });
    }

    /**
     * Create a payment for a specific order.
     */
    public function forOrder(Order $order): static
    {
        return $this->state(fn (array $attributes) => [
            'order_id' => $order->id,
            'amount' => $order->total_amount,
            'currency_id' => $order->currency_id,
        ]);
    }

    /**
     * Create a payment with a specific amount.
     */
    public function withAmount(float $amount): static
    {
        return $this->state(fn (array $attributes) => [
            'amount' => $amount,
        ]);
    }

    /**
     * Create a payment with a specific method.
     */
    public function withMethod(string $method): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_method' => $method,
        ]);
    }
}
