<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_preferences', function (Blueprint $table) {
            $table->id();
            $table->string('uuid')->unique();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');

            // General Preferences
            $table->string('language', 10)->default('en');
            $table->string('timezone', 50)->default('UTC');
            $table->string('currency', 10)->default('USD');
            $table->string('date_format', 20)->default('Y-m-d');
            $table->string('time_format', 20)->default('H:i');
            $table->enum('theme', ['light', 'dark', 'auto'])->default('light');

            // Dashboard Preferences
            $table->json('dashboard_widgets')->nullable(); // Which widgets to show/hide
            $table->enum('dashboard_layout', ['grid', 'list', 'compact'])->default('grid');
            $table->boolean('show_welcome_message')->default(true);
            $table->boolean('auto_refresh_dashboard')->default(true);
            $table->integer('auto_refresh_interval')->default(30); // seconds

            // Notification Preferences
            $table->boolean('email_notifications')->default(true);
            $table->boolean('browser_notifications')->default(true);
            $table->boolean('sms_notifications')->default(false);
            $table->json('notification_types')->nullable(); // Which types of notifications to receive
            $table->time('quiet_hours_start')->nullable();
            $table->time('quiet_hours_end')->nullable();

            // Chat Preferences (for staff/admin)
            $table->boolean('chat_sound_enabled')->default(true);
            $table->boolean('chat_desktop_notifications')->default(true);
            $table->enum('chat_status', ['available', 'busy', 'away', 'offline'])->default('available');
            $table->integer('max_concurrent_chats')->default(5);
            $table->boolean('auto_assign_chats')->default(true);

            // Display Preferences
            $table->integer('items_per_page')->default(25);
            $table->boolean('show_tooltips')->default(true);
            $table->boolean('compact_mode')->default(false);
            $table->json('table_columns')->nullable(); // Which columns to show in tables

            // Privacy Preferences
            $table->boolean('profile_visible')->default(true);
            $table->boolean('activity_tracking')->default(true);
            $table->boolean('analytics_tracking')->default(true);

            // Advanced Preferences
            $table->json('custom_settings')->nullable(); // For future extensibility

            $table->timestamps();

            // Indexes
            $table->index(['user_id']);
            $table->index(['language']);
            $table->index(['timezone']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_preferences');
    }
};
