<?php

namespace Tests\Feature\Helpers;

use App\Services\FileService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

use PHPUnit\Framework\Attributes\Test;
class FileHelpersTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up test configuration
        Config::set('file.max_file_size', 50 * 1024 * 1024);
        Config::set('file.allowed_mimes', [
            'application/pdf',
            'text/plain',
            'text/csv',
            'application/zip',
        ]);
        Config::set('file.allowed_extensions', ['pdf', 'txt', 'csv', 'zip']);
        Config::set('file.virus_scan.enabled', false);
        Config::set('file.storage.disk', 'testing');
        Config::set('file.storage.path', 'files');
        Config::set('file.logging.enabled', false);
        Config::set('file.content_analysis.extract_text', true);
        
        Config::set('filesystems.disks.testing', [
            'driver' => 'local',
            'root' => storage_path('app/testing'),
        ]);
        
        Storage::fake('testing');
    }
    
    protected function tearDown(): void
    {
        Storage::disk('testing')->deleteDirectory('');
        parent::tearDown();
    }
    #[Test]
    public function file_service_helper_returns_service_instance()
    {
        $service = file_service();
        
        $this->assertInstanceOf(FileService::class, $service);
    }
    #[Test]
    public function process_file_helper_works()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt');
        file_put_contents($tempPath, 'Test content for helper');
        
        $file = new UploadedFile($tempPath, 'helper.txt', 'text/plain', null, true);
        
        $result = process_file($file);
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('file_path', $result);
        $this->assertEquals('helper.txt', $result['file_info']['original_name']);
        
        unlink($tempPath);
    }
    #[Test]
    public function quick_file_upload_helper_works()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt');
        file_put_contents($tempPath, 'Quick upload test');
        
        $file = new UploadedFile($tempPath, 'quick.txt', 'text/plain', null, true);
        
        $result = quick_file_upload($file, 'quick-uploads');
        
        $this->assertTrue($result['success']);
        $this->assertStringContainsString('quick-uploads', $result['file_path']);
        
        unlink($tempPath);
    }
    #[Test]
    public function secure_file_upload_helper_works()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt');
        file_put_contents($tempPath, 'Secure upload test');
        
        $file = new UploadedFile($tempPath, 'secure.txt', 'text/plain', null, true);
        
        $result = secure_file_upload($file, 'secure-uploads');
        
        $this->assertTrue($result['success']);
        $this->assertStringContainsString('secure-uploads', $result['file_path']);
        
        unlink($tempPath);
    }
    #[Test]
    public function validate_file_helper_works()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt');
        file_put_contents($tempPath, 'Validation test content');
        
        $file = new UploadedFile($tempPath, 'validate.txt', 'text/plain', null, true);
        
        $result = validate_file($file);
        
        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);
        $this->assertEquals('validate.txt', $result['file_info']['original_name']);
        
        unlink($tempPath);
    }
    #[Test]
    public function scan_file_for_viruses_helper_works()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_file');
        file_put_contents($tempPath, 'Clean file content');
        
        $result = scan_file_for_viruses($tempPath);
        
        $this->assertTrue($result['clean']);
        $this->assertArrayHasKey('message', $result);
        $this->assertArrayHasKey('scanner', $result);
        
        unlink($tempPath);
    }
    #[Test]
    public function sanitize_filename_helper_works()
    {
        $dangerous = '../../../etc/passwd.txt';
        $sanitized = sanitize_filename($dangerous);
        
        $this->assertStringNotContainsString('..', $sanitized);
        $this->assertStringNotContainsString('/', $sanitized);
        $this->assertStringContainsString('.txt', $sanitized);
    }
    #[Test]
    public function extract_file_text_helper_works()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt') . '.txt';
        file_put_contents($tempPath, 'Text extraction test content');

        $result = extract_file_text($tempPath);

        $this->assertTrue($result['success']);
        $this->assertStringContainsString('extraction test', $result['text']);

        unlink($tempPath);
    }
    #[Test]
    public function process_archive_file_helper_works()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_zip');
        file_put_contents($tempPath, 'PK' . str_repeat('a', 100)); // ZIP signature
        
        $result = process_archive_file($tempPath);
        
        $this->assertArrayHasKey('success', $result);
        $this->assertArrayHasKey('message', $result);
        $this->assertArrayHasKey('extracted_files', $result);
        
        unlink($tempPath);
    }
    #[Test]
    public function get_file_url_helper_works()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt');
        file_put_contents($tempPath, 'URL test content');
        
        $file = new UploadedFile($tempPath, 'url.txt', 'text/plain', null, true);
        $result = process_file($file);
        
        $this->assertTrue($result['success']);
        
        $url = get_file_url($result['file_path']);
        
        $this->assertIsString($url);
        $this->assertStringContainsString($result['file_path'], $url);
        
        unlink($tempPath);
    }
    #[Test]
    public function delete_file_helper_works()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt');
        file_put_contents($tempPath, 'Delete test content');
        
        $file = new UploadedFile($tempPath, 'delete.txt', 'text/plain', null, true);
        $result = process_file($file);
        
        $this->assertTrue($result['success']);
        $this->assertTrue(Storage::disk('testing')->exists($result['file_path']));
        
        $deleted = delete_file($result['file_path']);
        
        $this->assertTrue($deleted);
        $this->assertFalse(Storage::disk('testing')->exists($result['file_path']));
        
        unlink($tempPath);
    }
    #[Test]
    public function get_file_info_helper_works()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt');
        file_put_contents($tempPath, 'Info test content');
        
        $file = new UploadedFile($tempPath, 'info.txt', 'text/plain', null, true);
        $result = process_file($file);
        
        $this->assertTrue($result['success']);
        
        $info = get_file_info($result['file_path']);
        
        $this->assertTrue($info['exists']);
        $this->assertArrayHasKey('size', $info);
        $this->assertArrayHasKey('last_modified', $info);
        $this->assertArrayHasKey('mime_type', $info);
        $this->assertArrayHasKey('url', $info);
        
        unlink($tempPath);
    }
    #[Test]
    public function remove_file_metadata_helper_works()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_pdf');
        file_put_contents($tempPath, '%PDF-1.4' . str_repeat('a', 100));
        
        $result = remove_file_metadata($tempPath);
        
        $this->assertTrue($result); // Should return true even if no actual metadata removal
        
        unlink($tempPath);
    }
    #[Test]
    public function helpers_handle_invalid_files_gracefully()
    {
        $file = UploadedFile::fake()->create('malware.exe', 1024, 'application/octet-stream');
        
        $result = validate_file($file);
        
        $this->assertFalse($result['valid']);
        $this->assertNotEmpty($result['errors']);
    }
    #[Test]
    public function helpers_handle_nonexistent_files_gracefully()
    {
        $info = get_file_info('non-existent-file.txt');
        
        $this->assertFalse($info['exists']);
        $this->assertEquals('File not found', $info['message']);
        
        $deleted = delete_file('non-existent-file.txt');
        $this->assertTrue($deleted); // Laravel Storage::delete returns true even for non-existent files
    }
    #[Test]
    public function sanitize_filename_helper_handles_edge_cases()
    {
        // Empty filename
        $sanitized1 = sanitize_filename('');
        $this->assertNotEmpty($sanitized1);
        $this->assertStringContainsString('file_', $sanitized1);
        
        // Only extension
        $sanitized2 = sanitize_filename('.txt');
        $this->assertNotEmpty($sanitized2);
        $this->assertStringContainsString('.txt', $sanitized2);
        
        // Special characters
        $sanitized3 = sanitize_filename('file with spaces & symbols!@#$.pdf');
        $this->assertStringNotContainsString(' ', $sanitized3);
        $this->assertStringNotContainsString('&', $sanitized3);
        $this->assertStringContainsString('.pdf', $sanitized3);
    }
    #[Test]
    public function file_processing_helpers_work_with_different_file_types()
    {
        // Test with PDF
        $pdfPath = tempnam(sys_get_temp_dir(), 'test_pdf');
        file_put_contents($pdfPath, '%PDF-1.4' . str_repeat('a', 1000));
        $pdfFile = new UploadedFile($pdfPath, 'test.pdf', 'application/pdf', null, true);
        
        $pdfResult = process_file($pdfFile);
        $this->assertTrue($pdfResult['success']);
        $this->assertEquals('document', $pdfResult['file_info']['type_category']);
        
        // Test with CSV
        $csvPath = tempnam(sys_get_temp_dir(), 'test_csv');
        file_put_contents($csvPath, "Name,Email\nJohn,<EMAIL>");
        $csvFile = new UploadedFile($csvPath, 'test.csv', 'text/csv', null, true);
        
        $csvResult = process_file($csvFile);
        $this->assertTrue($csvResult['success']);
        $this->assertEquals('spreadsheet', $csvResult['file_info']['type_category']);
        
        unlink($pdfPath);
        unlink($csvPath);
    }
}
