<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CircuitBreakerService
{
    private const STATE_CLOSED = 'closed';
    private const STATE_OPEN = 'open';
    private const STATE_HALF_OPEN = 'half_open';

    private int $failureThreshold;
    private int $recoveryTimeout;
    private int $expectedExceptionThreshold;
    private string $serviceName;

    public function __construct(
        string $serviceName,
        int $failureThreshold = 5,
        int $recoveryTimeout = 60,
        int $expectedExceptionThreshold = 10
    ) {
        $this->serviceName = $serviceName;
        $this->failureThreshold = $failureThreshold;
        $this->recoveryTimeout = $recoveryTimeout;
        $this->expectedExceptionThreshold = $expectedExceptionThreshold;
    }

    /**
     * Execute a callable with circuit breaker protection
     */
    public function call(callable $service, callable $fallback = null)
    {
        $state = $this->getState();

        if ($state === self::STATE_OPEN) {
            if ($this->shouldAttemptReset()) {
                $this->setState(self::STATE_HALF_OPEN);
            } else {
                Log::warning("Circuit breaker is OPEN for service: {$this->serviceName}");
                return $this->executeFallback($fallback);
            }
        }

        try {
            $result = $service();
            $this->onSuccess();
            return $result;
        } catch (\Exception $e) {
            $this->onFailure($e);

            if ($this->getState() === self::STATE_OPEN) {
                Log::error("Circuit breaker opened for service: {$this->serviceName}", [
                    'exception' => $e->getMessage(),
                    'failure_count' => $this->getFailureCount()
                ]);
            }

            return $this->executeFallback($fallback, $e);
        }
    }

    /**
     * Handle successful service call
     */
    private function onSuccess(): void
    {
        $this->resetFailureCount();
        if ($this->getState() === self::STATE_HALF_OPEN) {
            $this->setState(self::STATE_CLOSED);
            Log::info("Circuit breaker closed for service: {$this->serviceName}");
        }
    }

    /**
     * Handle failed service call
     */
    private function onFailure(\Exception $e): void
    {
        $failureCount = $this->incrementFailureCount();

        if ($failureCount >= $this->failureThreshold) {
            $this->setState(self::STATE_OPEN);
            $this->setLastFailureTime(now()->timestamp);
        }
    }

    /**
     * Execute fallback function or throw exception
     */
    private function executeFallback(callable $fallback = null, \Exception $originalException = null)
    {
        if ($fallback) {
            try {
                return $fallback($originalException);
            } catch (\Exception $e) {
                Log::error("Fallback failed for service: {$this->serviceName}", [
                    'fallback_exception' => $e->getMessage(),
                    'original_exception' => $originalException?->getMessage()
                ]);
                throw $e;
            }
        }

        if ($originalException) {
            throw $originalException;
        }

        throw new \RuntimeException("Service {$this->serviceName} is currently unavailable (circuit breaker open)");
    }

    /**
     * Check if we should attempt to reset the circuit breaker
     */
    private function shouldAttemptReset(): bool
    {
        $lastFailureTime = $this->getLastFailureTime();
        return $lastFailureTime && (now()->timestamp - $lastFailureTime) >= $this->recoveryTimeout;
    }

    /**
     * Get current circuit breaker state
     */
    private function getState(): string
    {
        return Cache::get($this->getStateKey(), self::STATE_CLOSED);
    }

    /**
     * Set circuit breaker state
     */
    private function setState(string $state): void
    {
        Cache::put($this->getStateKey(), $state, now()->addHours(24));
    }

    /**
     * Get failure count
     */
    private function getFailureCount(): int
    {
        return Cache::get($this->getFailureCountKey(), 0);
    }

    /**
     * Increment failure count
     */
    private function incrementFailureCount(): int
    {
        $key = $this->getFailureCountKey();
        $count = Cache::get($key, 0) + 1;
        Cache::put($key, $count, now()->addHours(1));
        return $count;
    }

    /**
     * Reset failure count
     */
    private function resetFailureCount(): void
    {
        Cache::forget($this->getFailureCountKey());
    }

    /**
     * Get last failure time
     */
    private function getLastFailureTime(): ?int
    {
        return Cache::get($this->getLastFailureTimeKey());
    }

    /**
     * Set last failure time
     */
    private function setLastFailureTime(int $timestamp): void
    {
        Cache::put($this->getLastFailureTimeKey(), $timestamp, now()->addHours(24));
    }

    /**
     * Get cache keys
     */
    private function getStateKey(): string
    {
        return "circuit_breaker:{$this->serviceName}:state";
    }

    private function getFailureCountKey(): string
    {
        return "circuit_breaker:{$this->serviceName}:failure_count";
    }

    private function getLastFailureTimeKey(): string
    {
        return "circuit_breaker:{$this->serviceName}:last_failure_time";
    }

    /**
     * Get circuit breaker status for monitoring
     */
    public function getStatus(): array
    {
        return [
            'service' => $this->serviceName,
            'state' => $this->getState(),
            'failure_count' => $this->getFailureCount(),
            'last_failure_time' => $this->getLastFailureTime(),
            'failure_threshold' => $this->failureThreshold,
            'recovery_timeout' => $this->recoveryTimeout,
        ];
    }

    /**
     * Manually reset circuit breaker (for admin use)
     */
    public function reset(): void
    {
        $this->resetFailureCount();
        $this->setState(self::STATE_CLOSED);
        Cache::forget($this->getLastFailureTimeKey());

        Log::info("Circuit breaker manually reset for service: {$this->serviceName}");
    }

    /**
     * Check if the service is available (circuit breaker is not open).
     */
    public function isAvailable(): bool
    {
        $state = $this->getState();

        if ($state === self::STATE_OPEN) {
            // Check if we should attempt reset
            return $this->shouldAttemptReset();
        }

        // Closed or half-open states allow requests
        return true;
    }
}