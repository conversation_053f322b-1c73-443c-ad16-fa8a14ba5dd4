<?php

namespace Database\Factories;

use App\Models\ChatMessage;
use App\Models\ChatRoom;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ChatMessage>
 */
class ChatMessageFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ChatMessage::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'uuid' => $this->faker->uuid(),
            'chat_room_id' => ChatRoom::factory(),
            'user_id' => $this->faker->optional()->randomElement([null, User::factory()]),
            'message_type' => $this->faker->randomElement(['text', 'image', 'file', 'system']),
            'content' => $this->faker->sentence(),
            'metadata' => [
                'ip_address' => $this->faker->optional()->ipv4(),
                'user_agent' => $this->faker->optional()->userAgent(),
                'timestamp' => $this->faker->dateTimeThisMonth()->format('Y-m-d H:i:s'),
            ],
            'is_ai_generated' => $this->faker->boolean(20), // 20% chance of being AI generated
            'ai_confidence' => $this->faker->optional(0.2)->randomFloat(2, 0.5, 1.0), // Only if AI generated
            'ai_model' => $this->faker->optional(0.2)->randomElement(['gpt-4', 'gpt-3.5-turbo', 'claude-3']),
            'reply_to_message_id' => null, // Can be set explicitly when needed
            'is_edited' => false,
            'edit_count' => 0,
        ];
    }

    /**
     * Indicate that the message is AI generated.
     */
    public function aiGenerated(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_ai_generated' => true,
            'ai_confidence' => $this->faker->randomFloat(2, 0.7, 1.0),
            'ai_model' => $this->faker->randomElement(['gpt-4', 'gpt-3.5-turbo', 'claude-3']),
            'user_id' => null, // AI messages don't have a user
        ]);
    }

    /**
     * Indicate that the message is from a human user.
     */
    public function human(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_ai_generated' => false,
            'ai_confidence' => null,
            'ai_model' => null,
            'user_id' => User::factory(),
        ]);
    }

    /**
     * Indicate that the message is a text message.
     */
    public function text(): static
    {
        return $this->state(fn (array $attributes) => [
            'message_type' => 'text',
            'content' => $this->faker->sentence(),
        ]);
    }

    /**
     * Indicate that the message is a system message.
     */
    public function system(): static
    {
        return $this->state(fn (array $attributes) => [
            'message_type' => 'system',
            'content' => $this->faker->randomElement([
                'User joined the chat',
                'User left the chat',
                'Chat session started',
                'Chat session ended',
                'File uploaded',
            ]),
            'user_id' => null,
            'is_ai_generated' => false,
        ]);
    }

    /**
     * Indicate that the message has been edited.
     */
    public function edited(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_edited' => true,
            'edit_count' => $this->faker->numberBetween(1, 5),
        ]);
    }

    /**
     * Set the chat room for this message.
     */
    public function forRoom(ChatRoom $room): static
    {
        return $this->state(fn (array $attributes) => [
            'chat_room_id' => $room->id,
        ]);
    }

    /**
     * Set the user for this message.
     */
    public function fromUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
            'is_ai_generated' => false,
        ]);
    }
}
