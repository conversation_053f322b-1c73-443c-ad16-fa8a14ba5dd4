<?php

namespace App\Services;

use App\Models\ContactSubmission;
use App\Models\NewsletterSubscription;
use App\Models\User;
use App\Models\Order;
use App\Models\ProjectApplication;
use App\Models\JobApplication;
use App\Models\Product;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class RecentActivityService
{
    const CACHE_TTL = 300; // 5 minutes

    /**
     * Get recent activities for admin dashboard.
     */
    public function getRecentActivities(int $limit = 10): array
    {
        return Cache::remember('admin.dashboard.recent_activities', self::CACHE_TTL, function () use ($limit) {
            $activities = collect();

            // Contact form submissions
            $this->addContactSubmissions($activities);

            // Newsletter subscriptions
            $this->addNewsletterSubscriptions($activities);

            // New user registrations
            $this->addUserRegistrations($activities);

            // New orders
            $this->addNewOrders($activities);

            // Project applications
            $this->addProjectApplications($activities);

            // Job applications
            $this->addJobApplications($activities);

            // Product activities (added, updated)
            $this->addProductActivities($activities);

            // Sort by created_at descending and limit
            return $activities->sortByDesc('created_at')
                             ->take($limit)
                             ->values()
                             ->toArray();
        });
    }

    /**
     * Add contact form submissions to activities.
     */
    private function addContactSubmissions(Collection $activities): void
    {
        $submissions = ContactSubmission::select(['id', 'name', 'email', 'subject', 'created_at'])
                                       ->orderBy('created_at', 'desc')
                                       ->limit(5)
                                       ->get();

        foreach ($submissions as $submission) {
            $activities->push([
                'type' => 'contact_submission',
                'title' => 'New contact form submission',
                'description' => "From {$submission->name} - {$submission->subject}",
                'created_at' => $submission->created_at,
                'time_ago' => $submission->created_at->diffForHumans(),
                'icon' => 'mail',
                'color' => 'orange',
                'url' => route('admin.contact-submissions.show', $submission->id), // Uses ID
            ]);
        }
    }

    /**
     * Add newsletter subscriptions to activities.
     */
    private function addNewsletterSubscriptions(Collection $activities): void
    {
        $subscriptions = NewsletterSubscription::select(['id', 'email', 'name', 'created_at'])
                                              ->where('is_active', true)
                                              ->orderBy('created_at', 'desc')
                                              ->limit(5)
                                              ->get();

        foreach ($subscriptions as $subscription) {
            $activities->push([
                'type' => 'newsletter_subscription',
                'title' => 'New newsletter subscription',
                'description' => "From " . ($subscription->name ?: $subscription->email),
                'created_at' => $subscription->created_at,
                'time_ago' => $subscription->created_at->diffForHumans(),
                'icon' => 'mail-open',
                'color' => 'indigo',
                'url' => route('admin.newsletter-subscriptions.index'),
            ]);
        }
    }

    /**
     * Add user registrations to activities.
     */
    private function addUserRegistrations(Collection $activities): void
    {
        $users = User::select(['id', 'uuid', 'first_name', 'last_name', 'email', 'created_at'])
                    ->where('is_deleted', false)
                    ->orderBy('created_at', 'desc')
                    ->limit(5)
                    ->get();

        foreach ($users as $user) {
            $activities->push([
                'type' => 'user_registration',
                'title' => 'New user registered',
                'description' => "{$user->first_name} {$user->last_name} ({$user->email})",
                'created_at' => $user->created_at,
                'time_ago' => $user->created_at->diffForHumans(),
                'icon' => 'user-plus',
                'color' => 'blue',
                'url' => route('admin.users.show', $user->uuid), // Uses UUID
            ]);
        }
    }

    /**
     * Add new orders to activities.
     */
    private function addNewOrders(Collection $activities): void
    {
        $orders = Order::select(['id', 'uuid', 'order_number', 'email', 'total_amount', 'status', 'created_at'])
                      ->where('is_deleted', false)
                      ->orderBy('created_at', 'desc')
                      ->limit(5)
                      ->get();

        foreach ($orders as $order) {
            $activities->push([
                'type' => 'new_order',
                'title' => 'New order received',
                'description' => "Order #{$order->order_number} - $" . number_format($order->total_amount, 2),
                'created_at' => $order->created_at,
                'time_ago' => $order->created_at->diffForHumans(),
                'icon' => 'shopping-bag',
                'color' => 'green',
                'url' => route('admin.orders.show', $order->uuid), // Uses UUID
            ]);
        }
    }

    /**
     * Add project applications to activities.
     */
    private function addProjectApplications(Collection $activities): void
    {
        $applications = ProjectApplication::select(['id', 'title', 'full_name', 'email', 'status', 'created_at'])
                                         ->orderBy('created_at', 'desc')
                                         ->limit(5)
                                         ->get();

        foreach ($applications as $application) {
            $activities->push([
                'type' => 'project_application',
                'title' => 'New project application',
                'description' => "From {$application->full_name} - {$application->title}",
                'created_at' => $application->created_at,
                'time_ago' => $application->created_at->diffForHumans(),
                'icon' => 'briefcase',
                'color' => 'teal',
                'url' => route('admin.project-applications.show', $application->id), // Uses ID
            ]);
        }
    }

    /**
     * Add job applications to activities.
     */
    private function addJobApplications(Collection $activities): void
    {
        $applications = JobApplication::select(['id', 'first_name', 'last_name', 'email', 'status', 'created_at'])
                                     ->with('job:id,title')
                                     ->orderBy('created_at', 'desc')
                                     ->limit(5)
                                     ->get();

        foreach ($applications as $application) {
            $jobTitle = $application->job ? $application->job->title : 'Unknown Position';
            $activities->push([
                'type' => 'job_application',
                'title' => 'New job application',
                'description' => "From {$application->first_name} {$application->last_name} for {$jobTitle}",
                'created_at' => $application->created_at,
                'time_ago' => $application->created_at->diffForHumans(),
                'icon' => 'user-tie',
                'color' => 'pink',
                'url' => route('admin.job-applications.show', $application->id), // Uses ID
            ]);
        }
    }

    /**
     * Add product activities to activities.
     */
    private function addProductActivities(Collection $activities): void
    {
        // Recently added products
        $newProducts = Product::select(['id', 'slug', 'name', 'created_at', 'updated_at'])
                             ->where('is_deleted', false)
                             ->where('created_at', '>=', now()->subDays(7))
                             ->orderBy('created_at', 'desc')
                             ->limit(3)
                             ->get();

        foreach ($newProducts as $product) {
            $activities->push([
                'type' => 'product_added',
                'title' => 'Product added',
                'description' => $product->name,
                'created_at' => $product->created_at,
                'time_ago' => $product->created_at->diffForHumans(),
                'icon' => 'plus-circle',
                'color' => 'purple',
                'url' => route('admin.products.show', $product->slug), // Uses slug
            ]);
        }

        // Recently updated products (excluding newly created ones)
        $updatedProducts = Product::select(['id', 'slug', 'name', 'created_at', 'updated_at'])
                                 ->where('is_deleted', false)
                                 ->whereColumn('updated_at', '>', 'created_at')
                                 ->where('updated_at', '>=', now()->subDays(7))
                                 ->orderBy('updated_at', 'desc')
                                 ->limit(3)
                                 ->get();

        foreach ($updatedProducts as $product) {
            $activities->push([
                'type' => 'product_updated',
                'title' => 'Product updated',
                'description' => $product->name,
                'created_at' => $product->updated_at,
                'time_ago' => $product->updated_at->diffForHumans(),
                'icon' => 'edit',
                'color' => 'yellow',
                'url' => route('admin.products.show', $product->slug), // Uses slug
            ]);
        }
    }

    /**
     * Clear the recent activities cache.
     */
    public function clearCache(): void
    {
        Cache::forget('admin.dashboard.recent_activities');
    }

    /**
     * Get activity icon SVG based on type.
     */
    public function getActivityIcon(string $icon): string
    {
        $icons = [
            'mail' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>',
            'mail-open' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 19v-8.93a2 2 0 01.89-1.664l7-4.666a2 2 0 012.22 0l7 4.666A2 2 0 0121 10.07V19M3 19a2 2 0 002 2h14a2 2 0 002-2M3 19l6.75-4.5M21 19l-6.75-4.5"></path>',
            'user-plus' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>',
            'shopping-bag' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>',
            'briefcase' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6M8 6V4a2 2 0 012-2h4a2 2 0 012 2v2m-8 0h8m-8 0a2 2 0 00-2 2v6a2 2 0 002 2h8a2 2 0 002-2V8a2 2 0 00-2-2z"></path>',
            'user-tie' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>',
            'plus-circle' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>',
            'edit' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>',
        ];

        return $icons[$icon] ?? $icons['mail'];
    }

    /**
     * Get activity color classes based on color.
     */
    public function getActivityColorClasses(string $color): array
    {
        $colors = [
            'orange' => ['bg' => 'bg-orange-100', 'text' => 'text-orange-600'],
            'indigo' => ['bg' => 'bg-indigo-100', 'text' => 'text-indigo-600'],
            'blue' => ['bg' => 'bg-blue-100', 'text' => 'text-blue-600'],
            'green' => ['bg' => 'bg-green-100', 'text' => 'text-green-600'],
            'teal' => ['bg' => 'bg-teal-100', 'text' => 'text-teal-600'],
            'pink' => ['bg' => 'bg-pink-100', 'text' => 'text-pink-600'],
            'purple' => ['bg' => 'bg-purple-100', 'text' => 'text-purple-600'],
            'yellow' => ['bg' => 'bg-yellow-100', 'text' => 'text-yellow-600'],
        ];

        return $colors[$color] ?? $colors['blue'];
    }
}
