<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ChatParticipant extends Model
{
    use HasFactory;

    protected $fillable = [
        'chat_room_id',
        'user_id',
        'participant_type',
        'role',
        'display_name',
        'permissions',
        'joined_at',
        'left_at',
        'is_active',
        'last_seen_at',
    ];

    protected $casts = [
        'permissions' => 'array',
        'joined_at' => 'datetime',
        'left_at' => 'datetime',
        'is_active' => 'boolean',
        'last_seen_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $attributes = [
        'role' => 'participant',
        'is_active' => true,
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->joined_at)) {
                $model->joined_at = now();
            }
        });
    }

    /**
     * Scope for active participants.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for staff participants.
     */
    public function scopeStaff($query)
    {
        return $query->whereIn('participant_type', ['staff', 'admin']);
    }

    /**
     * Scope for customer participants.
     */
    public function scopeCustomers($query)
    {
        return $query->whereIn('participant_type', ['customer', 'visitor']);
    }

    /**
     * Scope for moderators.
     */
    public function scopeModerators($query)
    {
        return $query->where('role', 'moderator');
    }

    /**
     * Scope for online participants (seen in last 5 minutes).
     */
    public function scopeOnline($query)
    {
        return $query->where('last_seen_at', '>=', now()->subMinutes(5));
    }

    /**
     * Get the chat room this participant belongs to.
     */
    public function chatRoom(): BelongsTo
    {
        return $this->belongsTo(ChatRoom::class);
    }

    /**
     * Get the user for this participant.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if participant is staff.
     */
    public function isStaff(): bool
    {
        return in_array($this->participant_type, ['staff', 'admin']);
    }

    /**
     * Check if participant is customer.
     */
    public function isCustomer(): bool
    {
        return in_array($this->participant_type, ['customer', 'visitor']);
    }

    /**
     * Check if participant is moderator.
     */
    public function isModerator(): bool
    {
        return $this->role === 'moderator';
    }

    /**
     * Check if participant is owner.
     */
    public function isOwner(): bool
    {
        return $this->role === 'owner';
    }

    /**
     * Check if participant is online.
     */
    public function isOnline(): bool
    {
        return $this->last_seen_at && $this->last_seen_at >= now()->subMinutes(5);
    }

    /**
     * Check if participant has permission.
     */
    public function hasPermission(string $permission): bool
    {
        $permissions = $this->permissions ?? [];
        return in_array($permission, $permissions);
    }

    /**
     * Get display name for participant.
     */
    public function getDisplayNameAttribute($value): string
    {
        if ($value) {
            return $value;
        }

        if ($this->user) {
            return $this->user->name;
        }

        // For anonymous visitors
        $visitorInfo = $this->chatRoom->visitor_info ?? [];
        return $visitorInfo['name'] ?? 'Anonymous Visitor';
    }

    /**
     * Get participant status.
     */
    public function getStatusAttribute(): string
    {
        if (!$this->is_active) {
            return 'left';
        }

        if ($this->isOnline()) {
            return 'online';
        }

        return 'offline';
    }

    /**
     * Get time since last seen.
     */
    public function getLastSeenHumanAttribute(): string
    {
        if (!$this->last_seen_at) {
            return 'Never';
        }

        return $this->last_seen_at->diffForHumans();
    }

    /**
     * Get session duration.
     */
    public function getSessionDurationAttribute(): ?int
    {
        if (!$this->joined_at) {
            return null;
        }

        $endTime = $this->left_at ?? now();
        return $this->joined_at->diffInSeconds($endTime);
    }

    /**
     * Update last seen timestamp.
     */
    public function updateLastSeen(): bool
    {
        $this->last_seen_at = now();
        return $this->save();
    }

    /**
     * Leave the chat room.
     */
    public function leave(): bool
    {
        $this->is_active = false;
        $this->left_at = now();
        return $this->save();
    }

    /**
     * Rejoin the chat room.
     */
    public function rejoin(): bool
    {
        $this->is_active = true;
        $this->left_at = null;
        $this->last_seen_at = now();
        return $this->save();
    }

    /**
     * Grant permission to participant.
     */
    public function grantPermission(string $permission): bool
    {
        $permissions = $this->permissions ?? [];
        
        if (!in_array($permission, $permissions)) {
            $permissions[] = $permission;
            $this->permissions = $permissions;
            return $this->save();
        }

        return true;
    }

    /**
     * Revoke permission from participant.
     */
    public function revokePermission(string $permission): bool
    {
        $permissions = $this->permissions ?? [];
        
        if (($key = array_search($permission, $permissions)) !== false) {
            unset($permissions[$key]);
            $this->permissions = array_values($permissions);
            return $this->save();
        }

        return true;
    }

    /**
     * Promote participant to moderator.
     */
    public function promoteToModerator(): bool
    {
        $this->role = 'moderator';
        return $this->save();
    }

    /**
     * Demote participant to regular participant.
     */
    public function demoteToParticipant(): bool
    {
        $this->role = 'participant';
        return $this->save();
    }
}
