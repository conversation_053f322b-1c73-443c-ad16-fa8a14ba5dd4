<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Coupon;
use App\Models\Product;
use App\Models\ProductCategory;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Validation\Rule;

class CouponController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:admin,staff');
    }

    /**
     * Display a listing of coupons.
     */
    public function index(Request $request): View
    {
        $query = Coupon::where('is_deleted', false);

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Type filter
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Status filter
        if ($request->filled('status')) {
            switch ($request->status) {
                case 'active':
                    $query->active();
                    break;
                case 'inactive':
                    $query->where('is_active', false);
                    break;
                case 'expired':
                    $query->where('expires_at', '<', now());
                    break;
                case 'scheduled':
                    $query->where('starts_at', '>', now());
                    break;
            }
        }

        $coupons = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('admin.coupons.index', compact('coupons'));
    }

    /**
     * Show the form for creating a new coupon.
     */
    public function create(): View
    {
        $products = Product::where('is_deleted', false)
                          ->where('is_active', true)
                          ->orderBy('name')
                          ->get(['id', 'name']);

        $categories = ProductCategory::where('is_deleted', false)
                                   ->where('is_active', true)
                                   ->orderBy('name')
                                   ->get(['id', 'name']);

        return view('admin.coupons.create', compact('products', 'categories'));
    }

    /**
     * Store a newly created coupon.
     */
    public function store(Request $request): RedirectResponse|JsonResponse
    {
        $validated = $this->validateCouponData($request);

        // Convert date strings to Carbon instances
        if ($validated['starts_at']) {
            $validated['starts_at'] = \Carbon\Carbon::parse($validated['starts_at']);
        }
        if ($validated['expires_at']) {
            $validated['expires_at'] = \Carbon\Carbon::parse($validated['expires_at']);
        }

        $coupon = Coupon::create($validated);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Coupon created successfully.',
                'redirect' => route('admin.coupons.index'),
                'coupon' => [
                    'id' => $coupon->id,
                    'code' => $coupon->code,
                    'type' => $coupon->type,
                ]
            ]);
        }

        return redirect()->route('admin.coupons.index')
                        ->with('success', 'Coupon created successfully.');
    }

    /**
     * Display the specified coupon.
     */
    public function show(Coupon $coupon): View
    {
        $coupon->load(['orders']);
        
        return view('admin.coupons.show', compact('coupon'));
    }

    /**
     * Show the form for editing the specified coupon.
     */
    public function edit(Coupon $coupon): View
    {
        $products = Product::where('is_deleted', false)
                          ->where('is_active', true)
                          ->orderBy('name')
                          ->get(['id', 'name']);

        $categories = ProductCategory::where('is_deleted', false)
                                   ->where('is_active', true)
                                   ->orderBy('name')
                                   ->get(['id', 'name']);

        return view('admin.coupons.edit', compact('coupon', 'products', 'categories'));
    }

    /**
     * Update the specified coupon.
     */
    public function update(Request $request, Coupon $coupon): RedirectResponse|JsonResponse
    {
        $validated = $this->validateCouponData($request, $coupon);

        // Convert date strings to Carbon instances
        if ($validated['starts_at']) {
            $validated['starts_at'] = \Carbon\Carbon::parse($validated['starts_at']);
        }
        if ($validated['expires_at']) {
            $validated['expires_at'] = \Carbon\Carbon::parse($validated['expires_at']);
        }

        $coupon->update($validated);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Coupon updated successfully.',
                'redirect' => route('admin.coupons.index'),
                'coupon' => [
                    'id' => $coupon->id,
                    'code' => $coupon->code,
                    'type' => $coupon->type,
                ]
            ]);
        }

        return redirect()->route('admin.coupons.index')
                        ->with('success', 'Coupon updated successfully.');
    }

    /**
     * Remove the specified coupon.
     */
    public function destroy(Coupon $coupon): RedirectResponse
    {
        // Check if coupon has been used
        if ($coupon->used_count > 0) {
            return redirect()->route('admin.coupons.index')
                           ->with('error', 'Cannot delete coupon that has been used.');
        }

        // Soft delete
        $coupon->update(['is_deleted' => true]);

        return redirect()->route('admin.coupons.index')
                        ->with('success', 'Coupon deleted successfully.');
    }

    /**
     * Toggle coupon active status.
     */
    public function toggle(Coupon $coupon): RedirectResponse
    {
        $coupon->update(['is_active' => !$coupon->is_active]);

        $status = $coupon->is_active ? 'activated' : 'deactivated';
        
        return redirect()->route('admin.coupons.index')
                        ->with('success', "Coupon {$status} successfully.");
    }

    /**
     * Validate coupon data.
     */
    private function validateCouponData(Request $request, Coupon $coupon = null): array
    {
        $rules = [
            'name' => 'required|string|max:255',
            'code' => [
                'nullable',
                'string',
                'max:50',
                Rule::unique('coupons', 'code')->ignore($coupon?->id)->where(function ($query) {
                    return $query->where('is_deleted', false);
                })
            ],
            'description' => 'nullable|string',
            'type' => 'required|in:percentage,fixed_amount,free_shipping',
            'value' => 'required|numeric|min:0',
            'minimum_amount' => 'nullable|numeric|min:0',
            'maximum_discount' => 'nullable|numeric|min:0',
            'usage_limit' => 'nullable|integer|min:1',
            'usage_limit_per_customer' => 'nullable|integer|min:1',
            'starts_at' => 'nullable|date|after_or_equal:today',
            'expires_at' => 'nullable|date|after:starts_at',
            'applicable_products' => 'nullable|array',
            'applicable_products.*' => 'exists:products,id',
            'applicable_categories' => 'nullable|array',
            'applicable_categories.*' => 'exists:product_categories,id',
            'exclude_products' => 'nullable|array',
            'exclude_products.*' => 'exists:products,id',
            'exclude_categories' => 'nullable|array',
            'exclude_categories.*' => 'exists:product_categories,id',
            'is_active' => 'boolean',
        ];

        // Custom validation for percentage type
        $request->validate($rules);

        if ($request->type === 'percentage' && $request->value > 100) {
            $request->validate([
                'value' => 'max:100'
            ], [
                'value.max' => 'Percentage discount cannot exceed 100%.'
            ]);
        }

        $validated = $request->only([
            'name', 'code', 'description', 'type', 'value', 'minimum_amount',
            'maximum_discount', 'usage_limit', 'usage_limit_per_customer',
            'starts_at', 'expires_at', 'applicable_products', 'applicable_categories',
            'exclude_products', 'exclude_categories', 'is_active'
        ]);

        // Set default values
        $validated['is_active'] = $request->has('is_active');

        // Ensure we have proper defaults for new coupons
        if (!$coupon) {
            // For new coupons, set sensible defaults
            $validated['used_count'] = 0;
            $validated['is_deleted'] = false;

            // If no start date is set, make it effective immediately
            if (empty($validated['starts_at'])) {
                $validated['starts_at'] = null;
            }
        }

        return $validated;
    }
}
