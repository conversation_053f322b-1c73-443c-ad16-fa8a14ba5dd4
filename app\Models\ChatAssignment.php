<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ChatAssignment extends Model
{
    use HasFactory;

    protected $fillable = [
        'chat_room_id',
        'assigned_to',
        'assigned_by',
        'assignment_type',
        'status',
        'workload_score',
        'notes',
        'assigned_at',
        'completed_at',
    ];

    protected $casts = [
        'workload_score' => 'integer',
        'assigned_at' => 'datetime',
        'completed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $attributes = [
        'status' => 'active',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->assigned_at)) {
                $model->assigned_at = now();
            }
        });
    }

    /**
     * Scope for active assignments.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for completed assignments.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for transferred assignments.
     */
    public function scopeTransferred($query)
    {
        return $query->where('status', 'transferred');
    }

    /**
     * Scope for automatic assignments.
     */
    public function scopeAutomatic($query)
    {
        return $query->where('assignment_type', 'automatic');
    }

    /**
     * Scope for manual assignments.
     */
    public function scopeManual($query)
    {
        return $query->where('assignment_type', 'manual');
    }

    /**
     * Scope for escalated assignments.
     */
    public function scopeEscalated($query)
    {
        return $query->where('assignment_type', 'escalation');
    }

    /**
     * Get the chat room this assignment belongs to.
     */
    public function chatRoom(): BelongsTo
    {
        return $this->belongsTo(ChatRoom::class);
    }

    /**
     * Get the staff member assigned to this chat.
     */
    public function assignedStaff(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get the user who made this assignment.
     */
    public function assignedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }

    /**
     * Check if assignment is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if assignment is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if assignment was transferred.
     */
    public function wasTransferred(): bool
    {
        return $this->status === 'transferred';
    }

    /**
     * Check if assignment was automatic.
     */
    public function isAutomatic(): bool
    {
        return $this->assignment_type === 'automatic';
    }

    /**
     * Check if assignment was manual.
     */
    public function isManual(): bool
    {
        return $this->assignment_type === 'manual';
    }

    /**
     * Check if assignment was escalated.
     */
    public function isEscalated(): bool
    {
        return $this->assignment_type === 'escalation';
    }

    /**
     * Get assignment duration in seconds.
     */
    public function getDurationAttribute(): ?int
    {
        if (!$this->assigned_at) {
            return null;
        }

        $endTime = $this->completed_at ?? now();
        return $this->assigned_at->diffInSeconds($endTime);
    }

    /**
     * Get assignment duration in human readable format.
     */
    public function getDurationHumanAttribute(): string
    {
        $duration = $this->duration;
        
        if (!$duration) {
            return 'N/A';
        }

        $hours = floor($duration / 3600);
        $minutes = floor(($duration % 3600) / 60);
        $seconds = $duration % 60;

        if ($hours > 0) {
            return sprintf('%dh %dm %ds', $hours, $minutes, $seconds);
        } elseif ($minutes > 0) {
            return sprintf('%dm %ds', $minutes, $seconds);
        } else {
            return sprintf('%ds', $seconds);
        }
    }

    /**
     * Get assignment type label.
     */
    public function getAssignmentTypeLabelAttribute(): string
    {
        return match($this->assignment_type) {
            'automatic' => 'Automatic',
            'manual' => 'Manual',
            'transfer' => 'Transfer',
            'escalation' => 'Escalation',
            default => 'Unknown'
        };
    }

    /**
     * Get status label.
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'active' => 'Active',
            'transferred' => 'Transferred',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled',
            default => 'Unknown'
        };
    }

    /**
     * Complete the assignment.
     */
    public function complete(): bool
    {
        $this->status = 'completed';
        $this->completed_at = now();
        return $this->save();
    }

    /**
     * Transfer the assignment to another staff member.
     */
    public function transferTo(User $newStaff, ?string $reason = null): ChatAssignment
    {
        // Mark current assignment as transferred
        $this->status = 'transferred';
        $this->completed_at = now();
        $this->save();

        // Create new assignment
        return static::create([
            'chat_room_id' => $this->chat_room_id,
            'assigned_to' => $newStaff->id,
            'assigned_by' => auth()->id(),
            'assignment_type' => 'transfer',
            'status' => 'active',
            'notes' => $reason,
            'assigned_at' => now(),
        ]);
    }

    /**
     * Cancel the assignment.
     */
    public function cancel(?string $reason = null): bool
    {
        $this->status = 'cancelled';
        $this->completed_at = now();
        
        if ($reason) {
            $this->notes = $this->notes ? $this->notes . "\n\nCancellation reason: " . $reason : "Cancelled: " . $reason;
        }
        
        return $this->save();
    }

    /**
     * Calculate workload score for staff member.
     */
    public static function calculateWorkloadScore(User $staff): int
    {
        $activeAssignments = static::where('assigned_to', $staff->id)
                                  ->where('status', 'active')
                                  ->count();

        // Base score on number of active assignments
        // Could be enhanced with priority weights, response times, etc.
        return $activeAssignments * 10;
    }

    /**
     * Find the best available staff member for assignment.
     */
    public static function findBestAvailableStaff(): ?User
    {
        // Get all staff members with their current workload
        $staffMembers = User::whereHas('role', function($query) {
                               $query->whereIn('name', ['staff', 'admin']);
                           })
                           ->where('is_active', true)
                           ->get()
                           ->map(function ($staff) {
                               $staff->workload_score = static::calculateWorkloadScore($staff);
                               return $staff;
                           })
                           ->sortBy('workload_score');

        return $staffMembers->first();
    }
}
