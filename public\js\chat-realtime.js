/**
 * Real-time Chat Client
 * 
 * This file provides a JavaScript client for real-time chat functionality
 * using Laravel Echo and WebSockets.
 */

class ChatRealtimeClient {
    constructor(options = {}) {
        this.roomUuid = options.roomUuid;
        this.authToken = options.authToken;
        this.apiBaseUrl = options.apiBaseUrl || '/api/v1/chat';
        this.echo = null;
        this.channel = null;
        this.typingTimeout = null;
        this.typingDebounceMs = 300;
        this.onlineStatusThrottleMs = 30000;
        this.lastOnlineStatusUpdate = 0;
        
        // Event callbacks
        this.onMessageReceived = options.onMessageReceived || (() => {});
        this.onUserTyping = options.onUserTyping || (() => {});
        this.onUserOnlineStatus = options.onUserOnlineStatus || (() => {});
        this.onMessageRead = options.onMessageRead || (() => {});
        this.onRoomJoined = options.onRoomJoined || (() => {});
        this.onRoomLeft = options.onRoomLeft || (() => {});
        this.onError = options.onError || ((error) => console.error('Chat error:', error));
        
        this.init();
    }

    /**
     * Initialize the real-time client
     */
    init() {
        if (!this.roomUuid) {
            throw new Error('Room UUID is required');
        }

        // Initialize Laravel Echo (assuming Pusher or similar is configured)
        this.echo = new Echo({
            broadcaster: 'pusher', // or 'socket.io', 'reverb', etc.
            key: process.env.MIX_PUSHER_APP_KEY,
            cluster: process.env.MIX_PUSHER_APP_CLUSTER,
            forceTLS: true,
            auth: {
                headers: {
                    Authorization: `Bearer ${this.authToken}`,
                },
            },
        });

        this.connectToRoom();
    }

    /**
     * Connect to the chat room channel
     */
    connectToRoom() {
        const channelName = `private-chat.room.${this.roomUuid}`;
        this.channel = this.echo.private(channelName);

        // Listen for message events
        this.channel.listen('.message.sent', (data) => {
            this.onMessageReceived(data);
        });

        // Listen for typing events
        this.channel.listen('.user.typing', (data) => {
            this.onUserTyping(data);
        });

        // Listen for online status events
        this.channel.listen('.user.online.status', (data) => {
            this.onUserOnlineStatus(data);
        });

        // Listen for read receipt events
        this.channel.listen('.message.read', (data) => {
            this.onMessageRead(data);
        });

        // Listen for room join/leave events
        this.channel.listen('.room.joined', (data) => {
            this.onRoomJoined(data);
        });

        this.channel.listen('.room.left', (data) => {
            this.onRoomLeft(data);
        });

        // Handle connection events
        this.channel.subscribed(() => {
            console.log('Connected to chat room:', this.roomUuid);
            this.joinRoom();
        });

        this.channel.error((error) => {
            console.error('Channel error:', error);
            this.onError(error);
        });
    }

    /**
     * Send a message
     */
    async sendMessage(content, messageType = 'text') {
        try {
            const response = await fetch(`${this.apiBaseUrl}/rooms/${this.roomUuid}/messages`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.authToken}`,
                    'X-Requested-With': 'XMLHttpRequest',
                },
                body: JSON.stringify({
                    content: content,
                    message_type: messageType,
                }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data;
        } catch (error) {
            this.onError(error);
            throw error;
        }
    }

    /**
     * Send typing indicator
     */
    async sendTyping(isTyping = true) {
        // Debounce typing events
        if (this.typingTimeout) {
            clearTimeout(this.typingTimeout);
        }

        if (isTyping) {
            this.typingTimeout = setTimeout(() => {
                this.sendTyping(false);
            }, this.typingDebounceMs);
        }

        try {
            await fetch(`${this.apiBaseUrl}/rooms/${this.roomUuid}/typing`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.authToken}`,
                    'X-Requested-With': 'XMLHttpRequest',
                },
                body: JSON.stringify({
                    is_typing: isTyping,
                }),
            });
        } catch (error) {
            this.onError(error);
        }
    }

    /**
     * Mark message as read
     */
    async markMessageAsRead(messageUuid) {
        try {
            await fetch(`${this.apiBaseUrl}/rooms/${this.roomUuid}/messages/${messageUuid}/read`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'X-Requested-With': 'XMLHttpRequest',
                },
            });
        } catch (error) {
            this.onError(error);
        }
    }

    /**
     * Join the room
     */
    async joinRoom() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/rooms/${this.roomUuid}/join`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'X-Requested-With': 'XMLHttpRequest',
                },
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data;
        } catch (error) {
            this.onError(error);
            throw error;
        }
    }

    /**
     * Leave the room
     */
    async leaveRoom() {
        try {
            await fetch(`${this.apiBaseUrl}/rooms/${this.roomUuid}/leave`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'X-Requested-With': 'XMLHttpRequest',
                },
            });
        } catch (error) {
            this.onError(error);
        }
    }

    /**
     * Get room status
     */
    async getRoomStatus() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/rooms/${this.roomUuid}/status`, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'X-Requested-With': 'XMLHttpRequest',
                },
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data;
        } catch (error) {
            this.onError(error);
            throw error;
        }
    }

    /**
     * Update online status (throttled)
     */
    updateOnlineStatus() {
        const now = Date.now();
        if (now - this.lastOnlineStatusUpdate < this.onlineStatusThrottleMs) {
            return;
        }

        this.lastOnlineStatusUpdate = now;
        // Online status is automatically handled by the WebSocket connection
        // This method is here for future enhancements
    }

    /**
     * Disconnect from the chat
     */
    disconnect() {
        if (this.typingTimeout) {
            clearTimeout(this.typingTimeout);
        }

        if (this.channel) {
            this.echo.leave(`private-chat.room.${this.roomUuid}`);
        }

        this.leaveRoom();
    }
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChatRealtimeClient;
} else if (typeof window !== 'undefined') {
    window.ChatRealtimeClient = ChatRealtimeClient;
}
