<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('project_applications', function (Blueprint $table) {
            // Make user_id nullable to support public applications
            $table->foreignId('user_id')->nullable()->change();
            
            // Add contact information fields for public applications
            $table->string('full_name')->nullable()->after('user_id');
            $table->string('email')->nullable()->after('full_name');
            $table->string('phone')->nullable()->after('email');
            $table->string('company')->nullable()->after('phone');
            $table->string('position')->nullable()->after('company');
            $table->string('country')->default('South Africa')->after('position');
            $table->enum('preferred_contact_method', ['email', 'phone', 'whatsapp'])->default('email')->after('country');
            
            // Add project details that were in PublicApplication
            $table->string('project_type')->nullable()->after('service_id');
            $table->string('project_category')->nullable()->after('project_type');
            $table->text('target_audience')->nullable()->after('project_category');
            $table->string('project_timeline')->nullable()->after('target_audience');
            $table->string('estimated_budget')->nullable()->after('project_timeline');
            
            // Add feature checkboxes
            $table->boolean('user_authentication')->default(false)->after('requirements');
            $table->boolean('admin_panel')->default(false)->after('user_authentication');
            $table->boolean('payment_processing')->default(false)->after('admin_panel');
            $table->boolean('multi_language')->default(false)->after('payment_processing');
            $table->boolean('mobile_responsive')->default(true)->after('multi_language');
            $table->boolean('api_development')->default(false)->after('mobile_responsive');
            $table->boolean('third_party_integrations')->default(false)->after('api_development');
            $table->boolean('data_analytics')->default(false)->after('third_party_integrations');
            
            // Add technical preferences
            $table->text('preferred_technologies')->nullable()->after('data_analytics');
            $table->string('hosting_preference')->nullable()->after('preferred_technologies');
            $table->boolean('existing_system_integration')->default(false)->after('hosting_preference');
            $table->text('existing_systems_details')->nullable()->after('existing_system_integration');
            $table->text('performance_requirements')->nullable()->after('existing_systems_details');
            $table->enum('security_level', ['basic', 'standard', 'high', 'enterprise'])->default('standard')->after('performance_requirements');
            $table->boolean('maintenance_support')->default(true)->after('security_level');
            $table->boolean('training_required')->default(false)->after('maintenance_support');
            
            // Add additional fields
            $table->text('additional_notes')->nullable()->after('training_required');
            $table->boolean('newsletter_signup')->default(false)->after('additional_notes');
            $table->boolean('terms_accepted')->default(true)->after('newsletter_signup');
            
            // Add reference number for public applications
            $table->string('reference_number')->nullable()->unique()->after('uuid');
            
            // Add submission tracking
            $table->boolean('is_completed')->default(false)->after('terms_accepted');
            $table->timestamp('submitted_at')->nullable()->after('is_completed');
            $table->timestamp('last_saved_at')->nullable()->after('submitted_at');
            
            // Add tracking information
            $table->string('ip_address')->nullable()->after('last_saved_at');
            $table->text('user_agent')->nullable()->after('ip_address');
            $table->string('referrer')->nullable()->after('user_agent');
            $table->string('utm_source')->nullable()->after('referrer');
            $table->string('utm_medium')->nullable()->after('utm_source');
            $table->string('utm_campaign')->nullable()->after('utm_medium');
            
            // Add admin assignment fields
            $table->timestamp('assigned_at')->nullable()->after('reviewed_at');
            $table->foreignId('assigned_by')->nullable()->constrained('users')->onDelete('set null')->after('assigned_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('project_applications', function (Blueprint $table) {
            // First drop the foreign key constraint for assigned_by
            $table->dropForeign(['assigned_by']);

            // Remove added columns
            $table->dropColumn([
                'full_name', 'email', 'phone', 'company', 'position', 'country', 'preferred_contact_method',
                'project_type', 'project_category', 'target_audience', 'project_timeline', 'estimated_budget',
                'user_authentication', 'admin_panel', 'payment_processing', 'multi_language', 'mobile_responsive',
                'api_development', 'third_party_integrations', 'data_analytics',
                'preferred_technologies', 'hosting_preference', 'existing_system_integration', 'existing_systems_details',
                'performance_requirements', 'security_level', 'maintenance_support', 'training_required',
                'additional_notes', 'newsletter_signup', 'terms_accepted', 'reference_number',
                'is_completed', 'submitted_at', 'last_saved_at',
                'ip_address', 'user_agent', 'referrer', 'utm_source', 'utm_medium', 'utm_campaign',
                'assigned_at', 'assigned_by'
            ]);

            // Make user_id required again
            $table->foreignId('user_id')->nullable(false)->change();
        });
    }
};
