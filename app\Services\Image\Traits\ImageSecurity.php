<?php

namespace App\Services\Image\Traits;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

trait ImageSecurity
{
    /**
     * Validate uploaded image file
     */
    public function validateImageFile(UploadedFile $file): array
    {
        $errors = [];
        
        // Check file size
        if ($file->getSize() > config('image.max_file_size', 10 * 1024 * 1024)) {
            $errors[] = 'File size exceeds maximum allowed size';
        }
        
        // Check MIME type
        $allowedMimes = config('image.allowed_mimes', []);
        if (!in_array($file->getMimeType(), $allowedMimes)) {
            $errors[] = 'Invalid file type: ' . $file->getMimeType();
        }
        
        // Check file extension
        $allowedExtensions = config('image.allowed_extensions', []);
        $extension = strtolower($file->getClientOriginalExtension());
        if (!in_array($extension, $allowedExtensions)) {
            $errors[] = 'Invalid file extension: ' . $extension;
        }
        
        // Validate image content
        if (config('image.security.validate_image_content', true)) {
            if (!$this->validateImageContent($file)) {
                $errors[] = 'File is not a valid image';
            }
        }
        
        // Check file signature
        if (config('image.security.check_file_signature', true)) {
            if (!$this->validateFileSignature($file)) {
                $errors[] = 'File signature validation failed';
            }
        }
        
        // Check image dimensions
        $imageInfo = @getimagesize($file->getPathname());
        if ($imageInfo) {
            $maxWidth = config('image.max_dimensions.width', 4000);
            $maxHeight = config('image.max_dimensions.height', 4000);

            if ($imageInfo[0] > $maxWidth || $imageInfo[1] > $maxHeight) {
                $errors[] = "Image dimensions exceed maximum allowed ({$maxWidth}x{$maxHeight})";
            }
        } else {
            // If getimagesize fails, it's likely not a valid image
            $errors[] = 'Unable to read image dimensions - file may be corrupted or not a valid image';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'file_info' => [
                'original_name' => $file->getClientOriginalName(),
                'mime_type' => $file->getMimeType(),
                'size' => $file->getSize(),
                'extension' => $extension,
                'dimensions' => $imageInfo ? ['width' => $imageInfo[0], 'height' => $imageInfo[1]] : null,
            ]
        ];
    }
    
    /**
     * Scan file for viruses and malware
     */
    public function scanForViruses(string $filePath): array
    {
        if (!config('image.virus_scan.enabled', false)) {
            return ['clean' => true, 'message' => 'Virus scanning disabled'];
        }
        
        $method = config('image.virus_scan.method', 'clamav');
        
        try {
            switch ($method) {
                case 'clamav':
                    return $this->scanWithClamAV($filePath);
                    
                case 'windows_defender':
                    return $this->scanWithWindowsDefender($filePath);
                    
                case 'custom':
                    return $this->scanWithCustomCommand($filePath);
                    
                default:
                    return $this->performBasicSecurityCheck($filePath);
            }
            
        } catch (\Exception $e) {
            $this->logError("Virus scan failed: " . $e->getMessage());
            return ['clean' => false, 'message' => 'Virus scan failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Sanitize filename
     */
    public function sanitizeFilename(string $filename): string
    {
        if (!config('image.security.sanitize_filename', true)) {
            return $filename;
        }
        
        // Remove path traversal attempts
        $filename = basename($filename);
        
        // Remove dangerous characters
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $filename);
        
        // Remove multiple dots and underscores
        $filename = preg_replace('/[._-]+/', '_', $filename);
        
        // Ensure filename is not empty
        if (empty($filename) || $filename === '.') {
            $filename = 'image_' . time();
        }
        
        // Add timestamp to ensure uniqueness
        $pathInfo = pathinfo($filename);
        $name = $pathInfo['filename'] ?? 'image';
        $extension = $pathInfo['extension'] ?? 'jpg';
        
        return $name . '_' . time() . '_' . Str::random(8) . '.' . $extension;
    }
    
    /**
     * Validate image content
     */
    protected function validateImageContent(UploadedFile $file): bool
    {
        try {
            // Try to get image info
            $imageInfo = getimagesize($file->getPathname());
            
            if ($imageInfo === false) {
                return false;
            }
            
            // Check if it's a supported image type
            $supportedTypes = [IMAGETYPE_JPEG, IMAGETYPE_PNG, IMAGETYPE_GIF, IMAGETYPE_WEBP, IMAGETYPE_BMP];
            
            return in_array($imageInfo[2], $supportedTypes);
            
        } catch (\Exception $e) {
            $this->logError("Image content validation failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Validate file signature (magic bytes)
     */
    protected function validateFileSignature(UploadedFile $file): bool
    {
        try {
            $handle = fopen($file->getPathname(), 'rb');
            if (!$handle) {
                return false;
            }
            
            $header = fread($handle, 12);
            fclose($handle);
            
            // Check common image file signatures
            $signatures = [
                'jpeg' => ["\xFF\xD8\xFF"],
                'png' => ["\x89\x50\x4E\x47\x0D\x0A\x1A\x0A"],
                'gif' => ["GIF87a", "GIF89a"],
                'webp' => ["RIFF", "WEBP"],
                'bmp' => ["BM"],
            ];
            
            foreach ($signatures as $type => $sigs) {
                foreach ($sigs as $sig) {
                    if (strpos($header, $sig) === 0) {
                        return true;
                    }
                }
            }
            
            return false;
            
        } catch (\Exception $e) {
            $this->logError("File signature validation failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Perform basic security check
     */
    protected function performBasicSecurityCheck(string $filePath): array
    {
        // Basic checks for suspicious content
        $suspiciousPatterns = [
            '/<script/i',
            '/javascript:/i',
            '/vbscript:/i',
            '/onload=/i',
            '/onerror=/i',
            '/<iframe/i',
            '/<object/i',
            '/<embed/i',
        ];
        
        try {
            $content = file_get_contents($filePath, false, null, 0, 8192); // Read first 8KB
            
            foreach ($suspiciousPatterns as $pattern) {
                if (preg_match($pattern, $content)) {
                    return ['clean' => false, 'message' => 'Suspicious content detected'];
                }
            }
            
            return ['clean' => true, 'message' => 'Basic security check passed'];
            
        } catch (\Exception $e) {
            return ['clean' => false, 'message' => 'Security check failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Scan with ClamAV (requires ClamAV to be installed)
     */
    protected function scanWithClamAV(string $filePath): array
    {
        // This would require ClamAV to be installed and configured
        // For now, return a basic check
        return $this->performBasicSecurityCheck($filePath);
    }
    
    /**
     * Scan with Windows Defender
     */
    protected function scanWithWindowsDefender(string $filePath): array
    {
        try {
            // Use Windows Defender command line scanner
            $command = 'powershell.exe -Command "& {Set-MpPreference -DisableRealtimeMonitoring $false; Start-MpScan -ScanType CustomScan -ScanPath \'' . $filePath . '\'}"';
            
            $output = [];
            $returnCode = 0;
            exec($command, $output, $returnCode);
            
            if ($returnCode === 0) {
                return ['clean' => true, 'message' => 'Windows Defender scan completed - no threats found'];
            } else {
                return ['clean' => false, 'message' => 'Windows Defender detected potential threat'];
            }
            
        } catch (\Exception $e) {
            return $this->performBasicSecurityCheck($filePath);
        }
    }
    
    /**
     * Scan with custom command
     */
    protected function scanWithCustomCommand(string $filePath): array
    {
        $command = config('image.virus_scan.custom_command');
        
        if (!$command) {
            return $this->performBasicSecurityCheck($filePath);
        }
        
        try {
            $fullCommand = str_replace('{file}', escapeshellarg($filePath), $command);
            
            $output = [];
            $returnCode = 0;
            exec($fullCommand, $output, $returnCode);
            
            return [
                'clean' => $returnCode === 0,
                'message' => $returnCode === 0 ? 'Custom scan passed' : 'Custom scan failed',
                'output' => implode("\n", $output)
            ];
            
        } catch (\Exception $e) {
            return $this->performBasicSecurityCheck($filePath);
        }
    }
    
    /**
     * Quarantine infected file
     */
    protected function quarantineFile(string $filePath): bool
    {
        try {
            $quarantinePath = config('image.virus_scan.quarantine_path', storage_path('quarantine'));
            
            if (!is_dir($quarantinePath)) {
                mkdir($quarantinePath, 0755, true);
            }
            
            $quarantineFile = $quarantinePath . '/' . basename($filePath) . '_' . time();
            
            return rename($filePath, $quarantineFile);
            
        } catch (\Exception $e) {
            $this->logError("File quarantine failed: " . $e->getMessage());
            return false;
        }
    }
}
