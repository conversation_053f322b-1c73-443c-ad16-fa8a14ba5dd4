<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;
use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\ChatSystemSetting;
use PHPUnit\Framework\Attributes\Test;

class ChatModelsTest extends TestCase
{
    /**
     * Test ChatRoom model basic functionality.
     */
    #[Test]
    public function test_chat_room_model_exists(): void
    {
        $this->assertTrue(class_exists(ChatRoom::class));
    }

    /**
     * Test ChatMessage model basic functionality.
     */
    #[Test]
    public function test_chat_message_model_exists(): void
    {
        $this->assertTrue(class_exists(ChatMessage::class));
    }

    /**
     * Test ChatSystemSetting model basic functionality.
     */
    #[Test]
    public function test_chat_system_setting_model_exists(): void
    {
        $this->assertTrue(class_exists(ChatSystemSetting::class));
    }

    /**
     * Test ChatRoom priority labels.
     */
    #[Test]
    public function test_chat_room_priority_labels(): void
    {
        $room = new ChatRoom(['priority' => 1]);
        $this->assertEquals('Low', $room->priority_label);

        $room = new ChatRoom(['priority' => 2]);
        $this->assertEquals('Medium', $room->priority_label);

        $room = new ChatRoom(['priority' => 3]);
        $this->assertEquals('High', $room->priority_label);

        $room = new ChatRoom(['priority' => 4]);
        $this->assertEquals('Urgent', $room->priority_label);
    }

    /**
     * Test ChatMessage sender types.
     */
    #[Test]
    public function test_chat_message_sender_types(): void
    {
        // Test that the getSenderTypeAttribute method exists
        $message = new ChatMessage();
        $this->assertTrue(method_exists($message, 'getSenderTypeAttribute'));

        // Test AI generated logic by checking the method directly
        $message->is_ai_generated = true;
        // For unit test, we'll just verify the method exists and can handle the logic
        // The actual database-dependent logic is tested in integration tests
        $this->assertTrue($message->is_ai_generated);
    }
}
