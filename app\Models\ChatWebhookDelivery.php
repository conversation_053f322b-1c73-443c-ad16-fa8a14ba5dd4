<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class ChatWebhookDelivery extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'chat_webhook_id',
        'event_type',
        'payload',
        'status',
        'attempts',
        'response_status',
        'response_body',
        'error_message',
        'delivered_at',
        'next_retry_at',
    ];

    protected $casts = [
        'payload' => 'array',
        'attempts' => 'integer',
        'response_status' => 'integer',
        'delivered_at' => 'datetime',
        'next_retry_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the webhook that owns this delivery.
     */
    public function webhook(): BelongsTo
    {
        return $this->belongsTo(ChatWebhook::class, 'chat_webhook_id');
    }

    /**
     * Mark delivery as successful.
     */
    public function markAsDelivered(int $responseStatus, ?string $responseBody = null): void
    {
        $this->update([
            'status' => 'delivered',
            'response_status' => $responseStatus,
            'response_body' => $responseBody,
            'delivered_at' => now(),
            'error_message' => null,
        ]);
    }

    /**
     * Mark delivery as failed.
     */
    public function markAsFailed(string $errorMessage, ?int $responseStatus = null, ?string $responseBody = null): void
    {
        $this->increment('attempts');
        
        $webhook = $this->webhook;
        $shouldRetry = $this->attempts < $webhook->max_retries;

        $this->update([
            'status' => $shouldRetry ? 'retrying' : 'failed',
            'error_message' => $errorMessage,
            'response_status' => $responseStatus,
            'response_body' => $responseBody,
            'next_retry_at' => $shouldRetry ? $this->calculateNextRetryTime() : null,
        ]);
    }

    /**
     * Calculate next retry time using exponential backoff.
     */
    protected function calculateNextRetryTime(): \Carbon\Carbon
    {
        // Exponential backoff: 2^attempts minutes (1, 2, 4, 8, 16...)
        $delayMinutes = pow(2, $this->attempts);
        
        // Cap at 60 minutes
        $delayMinutes = min($delayMinutes, 60);
        
        return now()->addMinutes($delayMinutes);
    }

    /**
     * Check if this delivery is ready for retry.
     */
    public function isReadyForRetry(): bool
    {
        return $this->status === 'retrying' 
            && $this->next_retry_at 
            && $this->next_retry_at->isPast();
    }

    /**
     * Get status badge color for UI.
     */
    public function getStatusBadgeColor(): string
    {
        return match ($this->status) {
            'delivered' => 'success',
            'failed' => 'danger',
            'retrying' => 'warning',
            'pending' => 'info',
            default => 'secondary',
        };
    }

    /**
     * Get human-readable status.
     */
    public function getStatusLabel(): string
    {
        return match ($this->status) {
            'delivered' => 'Delivered',
            'failed' => 'Failed',
            'retrying' => 'Retrying',
            'pending' => 'Pending',
            default => 'Unknown',
        };
    }

    /**
     * Scope for deliveries ready for retry.
     */
    public function scopeReadyForRetry($query)
    {
        return $query->where('status', 'retrying')
            ->where('next_retry_at', '<=', now());
    }

    /**
     * Scope for failed deliveries.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope for successful deliveries.
     */
    public function scopeDelivered($query)
    {
        return $query->where('status', 'delivered');
    }
}
