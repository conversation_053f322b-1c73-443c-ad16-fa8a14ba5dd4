<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;

class SafeOutputMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Set custom error handler for htmlspecialchars errors
        set_error_handler([$this, 'handleHtmlSpecialCharsError'], E_WARNING);
        
        try {
            $response = $next($request);
            
            // Process response content to fix potential issues
            if ($response instanceof \Illuminate\Http\Response) {
                $content = $response->getContent();
                $fixedContent = $this->fixHtmlSpecialCharsIssues($content);
                $response->setContent($fixedContent);
            }
            
            return $response;
        } catch (\Throwable $e) {
            // Log the error
            Log::error('SafeOutputMiddleware caught error', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // Return a safe error response
            return response()->json([
                'error' => 'An error occurred while processing your request.',
                'message' => 'Please try again later.'
            ], 500);
        } finally {
            // Restore original error handler
            restore_error_handler();
        }
    }

    /**
     * Handle htmlspecialchars specific errors
     */
    public function handleHtmlSpecialCharsError($errno, $errstr, $errfile, $errline)
    {
        if (strpos($errstr, 'htmlspecialchars') !== false) {
            Log::warning('htmlspecialchars error caught and handled', [
                'error' => $errstr,
                'file' => $errfile,
                'line' => $errline
            ]);
            
            // Don't let the error propagate
            return true;
        }
        
        // Let other errors be handled normally
        return false;
    }

    /**
     * Fix common htmlspecialchars issues in content
     */
    private function fixHtmlSpecialCharsIssues(string $content): string
    {
        // Pattern to find potential problematic htmlspecialchars calls
        $patterns = [
            // Fix array to string conversion in blade templates
            '/htmlspecialchars\(\s*\$([a-zA-Z_][a-zA-Z0-9_]*)\s*\)/' => function($matches) {
                return "safe_htmlspecialchars(\${$matches[1]})";
            },
            
            // Fix object to string conversion
            '/htmlspecialchars\(\s*\$([a-zA-Z_][a-zA-Z0-9_]*)->([a-zA-Z_][a-zA-Z0-9_]*)\s*\)/' => function($matches) {
                return "safe_htmlspecialchars(\${$matches[1]}->{$matches[2]})";
            }
        ];

        foreach ($patterns as $pattern => $replacement) {
            if (is_callable($replacement)) {
                $content = preg_replace_callback($pattern, $replacement, $content);
            } else {
                $content = preg_replace($pattern, $replacement, $content);
            }
        }

        return $content;
    }

    /**
     * Safely escape any value for HTML output
     */
    public static function safeEscape($value): string
    {
        if (is_null($value)) {
            return '';
        }
        
        if (is_bool($value)) {
            return $value ? '1' : '0';
        }
        
        if (is_array($value) || is_object($value)) {
            return htmlspecialchars(json_encode($value), ENT_QUOTES, 'UTF-8');
        }
        
        return htmlspecialchars((string) $value, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Check if a value is safe for htmlspecialchars
     */
    public static function isSafeForHtmlSpecialChars($value): bool
    {
        return is_string($value) || is_numeric($value) || is_null($value) || is_bool($value);
    }

    /**
     * Convert any value to a safe string
     */
    public static function toSafeString($value): string
    {
        if (is_null($value)) {
            return '';
        }
        
        if (is_bool($value)) {
            return $value ? 'true' : 'false';
        }
        
        if (is_array($value)) {
            return implode(', ', array_map([self::class, 'toSafeString'], $value));
        }
        
        if (is_object($value)) {
            if (method_exists($value, '__toString')) {
                return (string) $value;
            }
            
            if (method_exists($value, 'toArray')) {
                return self::toSafeString($value->toArray());
            }
            
            return get_class($value);
        }
        
        return (string) $value;
    }
}
