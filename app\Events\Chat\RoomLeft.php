<?php

namespace App\Events\Chat;

use App\Models\ChatRoom;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class RoomLeft implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ChatRoom $room;
    public string $userType;
    public int $userId;
    public string $userName;

    /**
     * Create a new event instance.
     */
    public function __construct(ChatRoom $room, string $userType, int $userId, string $userName)
    {
        $this->room = $room;
        $this->userType = $userType;
        $this->userId = $userId;
        $this->userName = $userName;
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('chat.room.' . $this->room->uuid),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'user' => [
                'type' => $this->userType,
                'id' => $this->userId,
                'name' => $this->userName,
            ],
            'room' => [
                'uuid' => $this->room->uuid,
                'status' => $this->room->status,
                'participant_count' => $this->room->participants()->count(),
            ],
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'room.left';
    }
}
