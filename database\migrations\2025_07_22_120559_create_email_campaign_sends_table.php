<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_campaign_sends', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->unsignedBigInteger('email_campaign_id');
            $table->unsignedBigInteger('newsletter_subscription_id');
            $table->string('email'); // Denormalized for performance
            $table->string('status')->default('pending'); // pending, sent, delivered, bounced, failed

            // Tracking data
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('opened_at')->nullable();
            $table->timestamp('first_clicked_at')->nullable();
            $table->timestamp('unsubscribed_at')->nullable();
            $table->timestamp('bounced_at')->nullable();

            // Analytics
            $table->unsignedInteger('open_count')->default(0);
            $table->unsignedInteger('click_count')->default(0);
            $table->json('clicked_links')->nullable(); // Track which links were clicked
            $table->string('bounce_reason')->nullable();
            $table->text('error_message')->nullable();

            // Tracking tokens
            $table->string('tracking_token')->unique()->nullable();
            $table->string('unsubscribe_token')->unique()->nullable();

            // User agent and IP for tracking
            $table->string('user_agent')->nullable();
            $table->string('ip_address')->nullable();

            $table->timestamps();

            $table->foreign('email_campaign_id')->references('id')->on('email_campaigns')->onDelete('cascade');
            $table->foreign('newsletter_subscription_id')->references('id')->on('newsletter_subscriptions')->onDelete('cascade');

            $table->unique(['email_campaign_id', 'newsletter_subscription_id'], 'campaign_subscriber_unique');
            $table->index(['email_campaign_id', 'status']);
            $table->index(['newsletter_subscription_id', 'sent_at']);
            $table->index(['tracking_token']);
            $table->index(['unsubscribe_token']);
            $table->index('sent_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_campaign_sends');
    }
};
