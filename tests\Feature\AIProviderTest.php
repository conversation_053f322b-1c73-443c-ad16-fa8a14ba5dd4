<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Services\AI\AIProviderManager;
use App\Services\AI\Providers\OpenAIProvider;
use App\Services\ChatAIService;
use App\Models\User;
use App\Models\ChatRoom;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;

class AIProviderTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected ChatRoom $room;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->room = ChatRoom::factory()->create([
            'language' => 'en',
        ]);

        // Mock AI provider configurations
        Config::set('ai-providers.default', 'openai');
        Config::set('ai-providers.providers.openai.api_key', 'test-key');
        Config::set('ai-providers.providers.openai.models.gpt-4.1-mini', [
            'name' => 'GPT-4.1 Mini',
            'type' => 'chat',
            'tier' => 'standard',
            'supports_vision' => true,
            'supports_function_calling' => true,
        ]);
    }

    /** @test */
    public function it_can_get_available_providers()
    {
        $aiManager = app(AIProviderManager::class);
        $providers = $aiManager->getAvailableProviders();

        $this->assertContains('openai', $providers);
        $this->assertContains('anthropic', $providers);
        $this->assertContains('google', $providers);
        $this->assertContains('xai', $providers);
    }

    /** @test */
    public function it_can_get_available_models_for_provider()
    {
        $aiManager = app(AIProviderManager::class);
        $models = $aiManager->getAvailableModels('openai');

        $this->assertArrayHasKey('gpt-4.1-mini', $models);
    }

    /** @test */
    public function it_can_get_models_by_tier()
    {
        $aiManager = app(AIProviderManager::class);
        $models = $aiManager->getModelsByTier('standard');

        $this->assertNotEmpty($models);
        $this->assertEquals('standard', $models[0]['config']['tier']);
    }

    /** @test */
    public function it_can_generate_ai_response_with_openai()
    {
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'content' => 'Hello! How can I help you today?'
                        ],
                        'finish_reason' => 'stop'
                    ]
                ],
                'usage' => [
                    'total_tokens' => 25,
                    'prompt_tokens' => 15,
                    'completion_tokens' => 10
                ]
            ], 200)
        ]);

        $aiManager = app(AIProviderManager::class);
        $response = $aiManager->generateResponse(
            'Hello',
            ['system_message' => 'You are a helpful assistant'],
            'gpt-4.1-mini',
            'openai'
        );

        $this->assertEquals('Hello! How can I help you today?', $response['content']);
        $this->assertEquals('openai', $response['provider']);
        $this->assertEquals('gpt-4.1-mini', $response['model']);
        $this->assertArrayHasKey('usage', $response);
    }

    /** @test */
    public function it_can_analyze_sentiment()
    {
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'content' => '{"score": 0.8, "emotion": "happy", "confidence": 0.9, "explanation": "Positive sentiment detected"}'
                        ]
                    ]
                ],
                'usage' => ['total_tokens' => 30]
            ], 200)
        ]);

        $chatAI = app(ChatAIService::class);
        $result = $chatAI->analyzeSentimentWithAI('I love this product!', 'openai');

        $this->assertEquals(0.8, $result['sentiment_score']);
        $this->assertEquals('happy', $result['emotion']);
        $this->assertEquals(0.9, $result['confidence']);
    }

    /** @test */
    public function it_can_translate_text()
    {
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'content' => 'Hola, ¿cómo estás?'
                        ]
                    ]
                ],
                'usage' => ['total_tokens' => 20]
            ], 200)
        ]);

        $chatAI = app(ChatAIService::class);
        $result = $chatAI->translateWithAI('Hello, how are you?', 'Spanish', 'English', 'openai');

        $this->assertEquals('Hola, ¿cómo estás?', $result['translated_text']);
        $this->assertEquals('Spanish', $result['target_language']);
        $this->assertEquals('English', $result['source_language']);
    }

    /** @test */
    public function it_can_summarize_conversation()
    {
        // Create some messages in the room
        $this->room->messages()->create([
            'user_id' => $this->user->id,
            'content' => 'Hello, I need help with my order',
            'type' => 'text',
        ]);

        $this->room->messages()->create([
            'user_id' => null,
            'content' => 'I can help you with that. What is your order number?',
            'type' => 'text',
            'is_ai_response' => true,
        ]);

        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'content' => 'Customer requested help with order, agent asked for order number.'
                        ]
                    ]
                ],
                'usage' => ['total_tokens' => 35]
            ], 200)
        ]);

        $chatAI = app(ChatAIService::class);
        $result = $chatAI->summarizeConversationWithAI($this->room, 50, 'openai');

        $this->assertStringContainsString('Customer', $result['summary']);
        $this->assertStringContainsString('order', $result['summary']);
    }

    /** @test */
    public function it_can_extract_conversation_topics()
    {
        // Create some messages in the room
        $this->room->messages()->create([
            'user_id' => $this->user->id,
            'content' => 'I want to return my laptop because it has a defective screen',
            'type' => 'text',
        ]);

        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'content' => '[{"title": "Product Return", "description": "Customer wants to return laptop"}, {"title": "Defective Hardware", "description": "Screen issue with laptop"}]'
                        ]
                    ]
                ],
                'usage' => ['total_tokens' => 40]
            ], 200)
        ]);

        $chatAI = app(ChatAIService::class);
        $result = $chatAI->extractConversationTopics($this->room, 5, 'openai');

        $this->assertCount(2, $result['topics']);
        $this->assertEquals('Product Return', $result['topics'][0]['title']);
        $this->assertEquals('Defective Hardware', $result['topics'][1]['title']);
    }

    /** @test */
    public function it_handles_provider_fallback()
    {
        Config::set('ai-providers.fallback.enabled', true);
        Config::set('ai-providers.fallback.providers.openai', ['anthropic']);

        // Mock OpenAI failure
        Http::fake([
            'api.openai.com/*' => Http::response([], 500),
            'api.anthropic.com/*' => Http::response([
                'content' => [
                    [
                        'type' => 'text',
                        'text' => 'Fallback response from Claude'
                    ]
                ],
                'usage' => ['input_tokens' => 10, 'output_tokens' => 5]
            ], 200)
        ]);

        $aiManager = app(AIProviderManager::class);
        $response = $aiManager->generateResponse(
            'Hello',
            ['system_message' => 'You are a helpful assistant'],
            null,
            'openai'
        );

        $this->assertEquals('Fallback response from Claude', $response['content']);
        $this->assertEquals('anthropic', $response['provider']);
    }

    /** @test */
    public function it_can_get_usage_statistics()
    {
        $aiManager = app(AIProviderManager::class);
        $stats = $aiManager->getUsageStats();

        $this->assertArrayHasKey('openai', $stats);
        $this->assertArrayHasKey('anthropic', $stats);
        $this->assertArrayHasKey('google', $stats);
        $this->assertArrayHasKey('xai', $stats);

        foreach ($stats as $providerStats) {
            $this->assertArrayHasKey('requests_today', $providerStats);
            $this->assertArrayHasKey('tokens_used_today', $providerStats);
            $this->assertArrayHasKey('cost_today', $providerStats);
        }
    }

    /** @test */
    public function it_validates_provider_configuration()
    {
        $provider = new OpenAIProvider([
            'api_key' => 'test-key',
            'base_url' => 'https://api.openai.com/v1',
            'models' => []
        ]);

        $this->assertTrue($provider->validateConfiguration());

        $invalidProvider = new OpenAIProvider([
            'api_key' => '',
            'base_url' => '',
            'models' => []
        ]);

        $this->assertFalse($invalidProvider->validateConfiguration());
    }

    /** @test */
    public function it_can_estimate_costs()
    {
        $provider = new OpenAIProvider([
            'api_key' => 'test-key',
            'base_url' => 'https://api.openai.com/v1',
            'models' => [
                'gpt-4.1-mini' => [
                    'input_cost_per_1m_tokens' => 0.40,
                    'output_cost_per_1m_tokens' => 1.60,
                ]
            ]
        ]);

        $estimate = $provider->estimateCost('Hello world', [], 'gpt-4.1-mini');

        $this->assertArrayHasKey('estimated_total_cost', $estimate);
        $this->assertArrayHasKey('estimated_input_tokens', $estimate);
        $this->assertArrayHasKey('estimated_output_tokens', $estimate);
        $this->assertEquals('USD', $estimate['currency']);
    }
}
