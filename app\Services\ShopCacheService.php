<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ProductCategory;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Collection;
use Illuminate\Http\Request;

class ShopCacheService
{
    // Cache TTL constants (in seconds)
    private const SEARCH_RESULTS_TTL = 300;     // 5 minutes for search results
    private const CATEGORIES_TTL = 1800;        // 30 minutes for categories
    private const PRICE_RANGE_TTL = 900;        // 15 minutes for price ranges
    private const FEATURED_PRODUCTS_TTL = 3600; // 1 hour for featured products
    
    // Cache tags for organized invalidation
    private const CACHE_TAGS = [
        'shop',
        'products', 
        'categories',
        'search'
    ];

    /**
     * Get cached search results.
     */
    public function getSearchResults(Request $request, int $perPage = 12)
    {
        $cacheKey = $this->generateSearchCacheKey($request, $perPage);

        if ($this->supportsCacheTags()) {
            return Cache::tags(self::CACHE_TAGS)
                ->remember($cacheKey, self::SEARCH_RESULTS_TTL, function () use ($request, $perPage) {
                    return $this->buildProductQuery($request)->paginate($perPage)->withQueryString();
                });
        } else {
            return Cache::remember($cacheKey, self::SEARCH_RESULTS_TTL, function () use ($request, $perPage) {
                return $this->buildProductQuery($request)->paginate($perPage)->withQueryString();
            });
        }
    }

    /**
     * Get cached categories with product counts.
     */
    public function getCategoriesWithCounts(): Collection
    {
        if ($this->supportsCacheTags()) {
            return Cache::tags(['shop', 'categories'])
                ->remember('shop.categories.with_counts', self::CATEGORIES_TTL, function () {
                    return $this->loadCategoriesWithCounts();
                });
        } else {
            return Cache::remember('shop.categories.with_counts', self::CATEGORIES_TTL, function () {
                return $this->loadCategoriesWithCounts();
            });
        }
    }

    /**
     * Load categories with product counts from database.
     */
    private function loadCategoriesWithCounts(): Collection
    {
        return ProductCategory::active()
            ->root()
            ->withCount(['activeProducts as products_count'])
            ->with(['children' => function ($query) {
                $query->active()->ordered()->withCount(['activeProducts as products_count']);
            }])
            ->ordered()
            ->get();
    }

    /**
     * Get cached price range for all active products.
     */
    public function getPriceRange()
    {
        if ($this->supportsCacheTags()) {
            return Cache::tags(['shop', 'products'])
                ->remember('shop.price_range', self::PRICE_RANGE_TTL, function () {
                    return Product::active()
                        ->selectRaw('MIN(price) as min_price, MAX(price) as max_price')
                        ->first();
                });
        } else {
            return Cache::remember('shop.price_range', self::PRICE_RANGE_TTL, function () {
                return Product::active()
                    ->selectRaw('MIN(price) as min_price, MAX(price) as max_price')
                    ->first();
            });
        }
    }

    /**
     * Get cached featured products.
     */
    public function getFeaturedProducts(int $limit = 8): Collection
    {
        if ($this->supportsCacheTags()) {
            return Cache::tags(['shop', 'products'])
                ->remember("shop.featured_products.{$limit}", self::FEATURED_PRODUCTS_TTL, function () use ($limit) {
                    return Product::active()
                        ->where('is_featured', true)
                        ->with(['categories'])
                        ->limit($limit)
                        ->get();
                });
        } else {
            return Cache::remember("shop.featured_products.{$limit}", self::FEATURED_PRODUCTS_TTL, function () use ($limit) {
                return Product::active()
                    ->where('is_featured', true)
                    ->with(['categories'])
                    ->limit($limit)
                    ->get();
            });
        }
    }

    /**
     * Get cached category-specific price range.
     */
    public function getCategoryPriceRange(ProductCategory $category)
    {
        $cacheKey = "shop.category.{$category->id}.price_range";

        if ($this->supportsCacheTags()) {
            return Cache::tags(['shop', 'products', 'categories'])
                ->remember($cacheKey, self::PRICE_RANGE_TTL, function () use ($category) {
                    return Product::active()
                        ->whereHas('categories', function ($q) use ($category) {
                            $q->where('product_categories.id', $category->id);
                        })
                        ->selectRaw('MIN(price) as min_price, MAX(price) as max_price')
                        ->first();
                });
        } else {
            return Cache::remember($cacheKey, self::PRICE_RANGE_TTL, function () use ($category) {
                return Product::active()
                    ->whereHas('categories', function ($q) use ($category) {
                        $q->where('product_categories.id', $category->id);
                    })
                    ->selectRaw('MIN(price) as min_price, MAX(price) as max_price')
                    ->first();
            });
        }
    }

    /**
     * Generate cache key for search results.
     */
    private function generateSearchCacheKey(Request $request, int $perPage): string
    {
        $params = [
            'search' => $request->get('search', ''),
            'category' => $request->get('category', ''),
            'min_price' => $request->get('min_price', ''),
            'max_price' => $request->get('max_price', ''),
            'sort' => $request->get('sort', 'name'),
            'page' => $request->get('page', 1),
            'per_page' => $perPage,
        ];

        // Remove empty values
        $params = array_filter($params, function ($value) {
            return $value !== '' && $value !== null;
        });

        return 'shop.search.' . md5(serialize($params));
    }

    /**
     * Build product query (same logic as ShopController).
     */
    private function buildProductQuery(Request $request)
    {
        $query = Product::active()->with(['categories']);
        
        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }
        
        // Category filter
        if ($request->filled('category')) {
            $category = ProductCategory::active()->where('slug', $request->category)->first();
            if ($category) {
                $query->whereHas('categories', function ($q) use ($category) {
                    $q->where('product_categories.id', $category->id);
                });
            }
        }
        
        // Price filter
        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }
        
        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }
        
        // Sorting
        $sort = $request->get('sort', 'name');
        
        switch ($sort) {
            case 'name':
                $query->orderBy('name', 'asc');
                break;
            case 'name-desc':
                $query->orderBy('name', 'desc');
                break;
            case 'price':
                $query->orderBy('price', 'asc');
                break;
            case 'price-desc':
                $query->orderBy('price', 'desc');
                break;
            case 'created_at':
                $query->orderBy('created_at', 'desc');
                break;
            case 'created_at-asc':
                $query->orderBy('created_at', 'asc');
                break;
            case 'featured':
                $query->orderBy('is_featured', 'desc')->orderBy('name', 'asc');
                break;
            default:
                $query->orderBy('name', 'asc');
                break;
        }

        return $query;
    }

    /**
     * Invalidate all shop-related caches.
     */
    public function invalidateAll(): void
    {
        if ($this->supportsCacheTags()) {
            Cache::tags(self::CACHE_TAGS)->flush();
        } else {
            // Fallback for cache stores that don't support tags
            $this->invalidateWithoutTags();
        }
    }

    /**
     * Invalidate product-related caches.
     */
    public function invalidateProductCaches(): void
    {
        if ($this->supportsCacheTags()) {
            Cache::tags(['shop', 'products'])->flush();
        } else {
            Cache::forget('shop.price_range');
            Cache::forget('shop.featured_products.8');
            // Clear search caches (this is expensive without tags)
            $this->clearSearchCaches();
        }
    }

    /**
     * Invalidate category-related caches.
     */
    public function invalidateCategoryCaches(): void
    {
        if ($this->supportsCacheTags()) {
            Cache::tags(['shop', 'categories'])->flush();
        } else {
            Cache::forget('shop.categories.with_counts');
            // Clear category-specific price ranges
            $categories = ProductCategory::pluck('id');
            foreach ($categories as $categoryId) {
                Cache::forget("shop.category.{$categoryId}.price_range");
            }
        }
    }

    /**
     * Check if the current cache store supports tags.
     */
    private function supportsCacheTags(): bool
    {
        $store = Cache::getStore();
        return $store instanceof \Illuminate\Cache\RedisStore || 
               $store instanceof \Illuminate\Cache\MemcachedStore;
    }

    /**
     * Invalidate caches without using tags (fallback).
     */
    private function invalidateWithoutTags(): void
    {
        // Clear specific known cache keys
        Cache::forget('shop.categories.with_counts');
        Cache::forget('shop.price_range');
        Cache::forget('shop.featured_products.8');
        
        // Clear search caches (expensive operation)
        $this->clearSearchCaches();
    }

    /**
     * Clear search result caches (expensive without tags).
     */
    private function clearSearchCaches(): void
    {
        // This is expensive - in production, consider using cache tags
        // or implementing a more efficient cache key tracking system
        
        // For now, we'll just clear some common search patterns
        $commonSearches = ['', 'laptop', 'computer', 'dell', 'hp', 'lenovo'];
        $commonSorts = ['name', 'price', 'created_at', 'featured'];
        
        foreach ($commonSearches as $search) {
            foreach ($commonSorts as $sort) {
                for ($page = 1; $page <= 5; $page++) {
                    $params = compact('search', 'sort', 'page');
                    $params['per_page'] = 12;
                    $cacheKey = 'shop.search.' . md5(serialize($params));
                    Cache::forget($cacheKey);
                }
            }
        }
    }
}
