<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AiTrainingData;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class TrainingDataController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin']);
    }

    /**
     * Display training data management interface.
     */
    public function index(): View
    {
        $statistics = AiTrainingData::getStatistics();
        $recentEntries = AiTrainingData::latest()->limit(10)->get();
        
        return view('admin.chat.training-data.index', compact('statistics', 'recentEntries'));
    }

    /**
     * Get training data with pagination and filters.
     */
    public function getData(Request $request): JsonResponse
    {
        $query = AiTrainingData::query();

        // Apply filters
        if ($request->filled('intent')) {
            $query->where('intent', $request->input('intent'));
        }

        if ($request->filled('language')) {
            $query->where('language', $request->input('language'));
        }

        if ($request->filled('category')) {
            $query->where('category', $request->input('category'));
        }

        if ($request->filled('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('input_text', 'like', "%{$search}%")
                  ->orWhere('expected_response', 'like', "%{$search}%")
                  ->orWhere('intent', 'like', "%{$search}%");
            });
        }

        // Sorting
        $sortBy = $request->input('sort_by', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->input('per_page', 15);
        $data = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $data,
        ]);
    }

    /**
     * Store new training data.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'intent' => 'required|string|max:100',
            'input_text' => 'required|string|max:1000',
            'expected_response' => 'required|string|max:2000',
            'language' => 'required|string|size:2',
            'confidence_threshold' => 'nullable|numeric|min:0|max:1',
            'category' => 'nullable|string|max:50',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:30',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $trainingData = AiTrainingData::create($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Training data created successfully',
                'data' => $trainingData,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create training data: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Show training data.
     */
    public function show(AiTrainingData $trainingData): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $trainingData,
        ]);
    }

    /**
     * Update training data.
     */
    public function update(Request $request, AiTrainingData $trainingData): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'intent' => 'required|string|max:100',
            'input_text' => 'required|string|max:1000',
            'expected_response' => 'required|string|max:2000',
            'language' => 'required|string|size:2',
            'confidence_threshold' => 'nullable|numeric|min:0|max:1',
            'category' => 'nullable|string|max:50',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:30',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $trainingData->update($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Training data updated successfully',
                'data' => $trainingData,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update training data: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete training data.
     */
    public function destroy(AiTrainingData $trainingData): JsonResponse
    {
        try {
            $trainingData->delete();

            return response()->json([
                'success' => true,
                'message' => 'Training data deleted successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete training data: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Bulk operations on training data.
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:activate,deactivate,delete',
            'ids' => 'required|array|min:1',
            'ids.*' => 'integer|exists:ai_training_data,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $action = $request->input('action');
            $ids = $request->input('ids');
            $affected = 0;

            switch ($action) {
                case 'activate':
                    $affected = AiTrainingData::whereIn('id', $ids)->update(['is_active' => true]);
                    break;
                case 'deactivate':
                    $affected = AiTrainingData::whereIn('id', $ids)->update(['is_active' => false]);
                    break;
                case 'delete':
                    $affected = AiTrainingData::whereIn('id', $ids)->delete();
                    break;
            }

            return response()->json([
                'success' => true,
                'message' => "Successfully {$action}d {$affected} training data entries",
                'affected' => $affected,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Bulk action failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Import training data from file.
     */
    public function import(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:json,csv|max:10240', // 10MB max
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $file = $request->file('file');
            $extension = $file->getClientOriginalExtension();
            $content = file_get_contents($file->getRealPath());

            $data = [];
            if ($extension === 'json') {
                $data = json_decode($content, true);
            } elseif ($extension === 'csv') {
                $data = $this->parseCsvContent($content);
            }

            if (!is_array($data)) {
                throw new \Exception('Invalid file format');
            }

            $imported = AiTrainingData::importData($data);

            return response()->json([
                'success' => true,
                'message' => "Successfully imported {$imported} training data entries",
                'imported' => $imported,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Import failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export training data.
     */
    public function export(Request $request): JsonResponse
    {
        try {
            $filters = $request->only(['language', 'intent', 'category', 'active']);
            $data = AiTrainingData::exportData($filters);

            $filename = 'training_data_' . date('Y-m-d_H-i-s') . '.json';
            $path = 'exports/' . $filename;

            Storage::disk('local')->put($path, json_encode($data, JSON_PRETTY_PRINT));

            return response()->json([
                'success' => true,
                'message' => 'Training data exported successfully',
                'download_url' => route('admin.training-data.download', ['filename' => $filename]),
                'count' => count($data),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Export failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Download exported file.
     */
    public function download(string $filename)
    {
        $path = 'exports/' . $filename;
        
        if (!Storage::disk('local')->exists($path)) {
            abort(404, 'File not found');
        }

        return Storage::disk('local')->download($path);
    }

    /**
     * Parse CSV content to array.
     */
    protected function parseCsvContent(string $content): array
    {
        $lines = explode("\n", $content);
        $header = str_getcsv(array_shift($lines));
        $data = [];

        foreach ($lines as $line) {
            if (trim($line)) {
                $row = str_getcsv($line);
                if (count($row) === count($header)) {
                    $data[] = array_combine($header, $row);
                }
            }
        }

        return $data;
    }
}
