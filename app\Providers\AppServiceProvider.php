<?php

namespace App\Providers;

use App\Services\ActivityLogger;
use App\Services\VisitorAnalytics;
use App\Events\Chat\WebhookEvent;
use App\Events\PermissionChanged;
use App\Events\RolePermissionsChanged;
use App\Listeners\TriggerChatWebhooks;
use App\Listeners\LogPermissionChange;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Event;
use Illuminate\Http\Request;
use Illuminate\Cache\RateLimiting\Limit;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register ActivityLogger as singleton
        $this->app->singleton(ActivityLogger::class, function ($app) {
            return new ActivityLogger();
        });

        // Register VisitorAnalytics with proper Request injection
        $this->app->bind(VisitorAnalytics::class, function ($app) {
            // Use the current request from the container
            $request = $app->make('request');
            return new VisitorAnalytics($request);
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Explicit route model binding for BlogPost
        Route::bind('post', function ($value) {
            return \App\Models\BlogPost::where('slug', $value)
                ->where('is_published', true)
                ->where('is_deleted', false)
                ->firstOrFail();
        });

        // Register model observers for cache invalidation
        \App\Models\Product::observe(\App\Observers\ProductObserver::class);
        \App\Models\ProductCategory::observe(\App\Observers\ProductCategoryObserver::class);

        // Register event listeners
        Event::listen(WebhookEvent::class, TriggerChatWebhooks::class);

        // Register permission management event listeners
        Event::listen(PermissionChanged::class, [LogPermissionChange::class, 'handlePermissionChanged']);
        Event::listen(RolePermissionsChanged::class, [LogPermissionChange::class, 'handleRolePermissionsChanged']);

        // Configure rate limiters for chat API
        $this->configureRateLimiting();
    }

    /**
     * Configure the rate limiters for the application.
     */
    protected function configureRateLimiting(): void
    {
        // Chat room creation rate limiter: 10 requests per hour (60 minutes)
        RateLimiter::for('chat-room-creation', function (Request $request) {
            return Limit::perHour(10)->by($request->user()?->id ?: $request->ip());
        });

        // Chat message sending rate limiter: 60 requests per minute
        RateLimiter::for('chat-message-sending', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });

        // General API rate limiter
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });

        // Chat API usage tracking rate limiter
        RateLimiter::for('chat-usage', function (Request $request) {
            return Limit::perMinute(100)->by($request->ip());
        });

        // Chat real-time actions (typing, status updates)
        RateLimiter::for('chat-realtime', function (Request $request) {
            return Limit::perMinute(30)->by($request->user()?->id ?: $request->ip());
        });

        // Chat file uploads
        RateLimiter::for('chat-file-upload', function (Request $request) {
            return Limit::perMinute(10)->by($request->user()?->id ?: $request->ip());
        });
    }
}
