<?php

namespace App\Events\Chat;

use App\Models\ChatMessage;
use App\Models\ChatRoom;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MessageSent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ChatMessage $message;
    public ChatRoom $room;

    /**
     * Create a new event instance.
     */
    public function __construct(ChatMessage $message, ChatRoom $room)
    {
        $this->message = $message;
        $this->room = $room;
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        $channels = [];

        // For visitor rooms, use public channel so anonymous users can listen
        if ($this->room->type === 'visitor') {
            $channels[] = new Channel('public.chat.room.' . $this->room->uuid);
        }

        // Always include private channel for authenticated users (staff, etc.)
        $channels[] = new PrivateChannel('chat.room.' . $this->room->uuid);

        return $channels;
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'message' => [
                'id' => $this->message->id,
                'uuid' => $this->message->uuid,
                'content' => $this->message->content,
                'message_type' => $this->message->message_type,
                'sender_type' => $this->message->sender_type,
                'sender_name' => $this->message->sender_name,
                'user_id' => $this->message->user_id,
                'is_ai_generated' => $this->message->is_ai_generated,
                'ai_confidence' => $this->message->ai_confidence,
                'created_at' => $this->message->created_at->toISOString(),
                'metadata' => $this->message->metadata,
                'is_own' => false, // This will be determined by the frontend
            ],
            'room' => [
                'uuid' => $this->room->uuid,
                'status' => $this->room->status,
            ],
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'message.sent';
    }
}
