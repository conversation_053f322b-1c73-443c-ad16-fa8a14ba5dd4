<?php

return [
    // Page Meta
    'page_title' => 'Desarrollo de Aplicaciones Móviles - ChiSolution',
    'meta_description' => 'Empresa líder en desarrollo de aplicaciones móviles en Sudáfrica especializada en desarrollo de aplicaciones multiplataforma, React Native, Flutter, aplicaciones iOS y Android. Servicios expertos de desarrollo de aplicaciones móviles con precios competitivos.',
    'meta_keywords' => 'desarrollo aplicaciones móviles Sudáfrica, desarrollo aplicaciones multiplataforma, desarrollo React Native, desarrollo aplicaciones Flutter, desarrollo aplicaciones iOS, desarrollo aplicaciones Android, costo desarrollo aplicaciones móviles, empresa desarrollo aplicaciones, aplicaciones móviles nativas, desarrollo aplicaciones híbridas, diseño UI/UX aplicaciones móviles, servicios desarrollo aplicaciones',

    // Hero Section
    'hero_title' => 'Servicios de Desarrollo de Aplicaciones Móviles Multiplataforma',
    'hero_description' => 'Empresa líder en desarrollo de aplicaciones móviles especializada en React Native, Flutter y aplicaciones nativas iOS/Android. Transforme sus ideas en aplicaciones móviles poderosas con precios competitivos y servicios de desarrollo expertos.',
    'get_quote' => 'Obtener Cotización',
    'view_apps' => 'Ver Aplicaciones',

    // Cross-Platform Section
    'cross_platform_title' => 'Desarrollo de Aplicaciones Móviles <span class="text-blue-600">Multiplataforma</span>',
    'cross_platform_description' => 'Desarrolle una vez, implemente en todas partes. Nuestros servicios de desarrollo de aplicaciones móviles multiplataforma le ayudan a llegar a usuarios de iOS y Android con una sola base de código, reduciendo el tiempo de desarrollo y los costos.',

    // Features
    'react_native_title' => 'Desarrollo React Native',
    'react_native_description' => 'Rendimiento nativo con JavaScript. Construya aplicaciones móviles de alta calidad usando React Native para plataformas iOS y Android.',
    'flutter_title' => 'Desarrollo de Aplicaciones Flutter',
    'flutter_description' => 'Kit de herramientas UI de Google para hermosas aplicaciones compiladas nativamente desde una sola base de código.',
    'native_ios_title' => 'Desarrollo iOS Nativo',
    'native_ios_description' => 'Desarrollo Swift y Objective-C para rendimiento óptimo y características específicas de la plataforma.',
    'native_android_title' => 'Desarrollo Android Nativo',
    'native_android_description' => 'Desarrollo Kotlin y Java aprovechando todo el poder de la plataforma Android.',
    'ui_ux_design_title' => 'Diseño UI/UX Móvil',
    'ui_ux_design_description' => 'Enfoque de diseño centrado en el usuario creando experiencias móviles intuitivas y atractivas.',
    'app_testing_title' => 'Pruebas de Aplicaciones y QA',
    'app_testing_description' => 'Pruebas exhaustivas en dispositivos, plataformas y escenarios para asegurar la calidad.',
    'educational_apps_title' => 'Aplicaciones Educativas',
    'educational_apps_description' => 'Plataformas de aprendizaje con contenido interactivo, seguimiento de progreso y características de gamificación.',
    'healthcare_apps_title' => 'Aplicaciones de Salud',
    'healthcare_apps_description' => 'Aplicaciones de salud y bienestar con reserva de citas, seguimiento de salud y características de telemedicina.',
    'entertainment_apps_title' => 'Aplicaciones de Entretenimiento',
    'entertainment_apps_description' => 'Aplicaciones de juegos, streaming de medios y entretenimiento con experiencias de usuario atractivas.',

    // Technologies Section
    'technologies_title' => 'Tecnologías de Aplicaciones Móviles <span class="text-blue-600">2025</span>',
    'technologies_description' => 'Tecnologías de desarrollo de aplicaciones móviles de vanguardia para aplicaciones rentables y de alto rendimiento con precios competitivos.',

    // Technology Items
    'react_native' => 'React Native',
    'flutter' => 'Flutter',
    'swift' => 'Swift',
    'kotlin' => 'Kotlin',
    'xamarin' => 'Xamarin',
    'ionic' => 'Ionic',
    'firebase' => 'Firebase',
    'aws_amplify' => 'AWS Amplify',
    'graphql' => 'GraphQL',
    'rest_api' => 'API REST',
    'push_notifications' => 'Notificaciones Push',
    'offline_storage' => 'Almacenamiento Offline',

    // Process Steps
    'discovery_title' => 'Descubrimiento y Planificación',
    'discovery_description' => 'Analizamos sus requisitos, audiencia objetivo y objetivos comerciales para crear una estrategia de desarrollo integral.',
    'design_title' => 'Diseño UI/UX',
    'design_description' => 'Nuestros diseñadores crean interfaces de usuario intuitivas y atractivas optimizadas para dispositivos móviles.',
    'development_title' => 'Desarrollo',
    'development_description' => 'Nuestros desarrolladores expertos construyen su aplicación usando las últimas tecnologías y mejores prácticas.',
    'testing_title' => 'Pruebas y Lanzamiento',
    'testing_description' => 'Pruebas rigurosas en dispositivos y plataformas antes del lanzamiento en las tiendas de aplicaciones.',

    // Service Descriptions
    'react_native_description' => 'Aplicaciones móviles multiplataforma con React Native. Base de código única para iOS y Android con 60% de ahorro en costos y tiempo de comercialización más rápido.',
    'cross_platform_description' => 'Base de código única para iOS y Android usando React Native o Flutter. Desarrollo más rápido y solución rentable.',

    // Benefits
    'cost_effective_title' => 'Rentable',
    'cost_effective_description' => 'Base de código única para múltiples plataformas',
    'faster_development_title' => 'Desarrollo Más Rápido',
    'faster_development_description' => 'Tiempo reducido al mercado',
    'single_codebase_title' => 'Base de Código Única',
    'native_performance_title' => 'Rendimiento Nativo',
    'native_performance_description' => 'Optimizado para cada plataforma',
    'easy_maintenance_title' => 'Mantenimiento Fácil',
    'easy_maintenance_description' => 'Actualizaciones y correcciones de errores simplificadas',

    // CTA Section
    'cta_title' => '¿Listo para Construir Su Aplicación Móvil?',
    'cta_description' => 'Hablemos de su idea de aplicación móvil y creemos una solución que involucre a sus usuarios y haga crecer su negocio.',
    'start_project' => 'Iniciar Su Proyecto',
    'get_consultation' => 'Consulta Gratuita',
];
