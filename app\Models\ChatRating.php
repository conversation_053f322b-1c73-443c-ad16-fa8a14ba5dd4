<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ChatRating extends Model
{
    use HasFactory;

    protected $fillable = [
        'chat_room_id',
        'user_id',
        'rating',
        'feedback',
        'rating_categories',
        'staff_user_id',
        'is_anonymous',
    ];

    protected $casts = [
        'rating' => 'integer',
        'rating_categories' => 'array',
        'is_anonymous' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $attributes = [
        'is_anonymous' => false,
    ];

    /**
     * Scope for high ratings (4-5 stars).
     */
    public function scopeHighRating($query)
    {
        return $query->where('rating', '>=', 4);
    }

    /**
     * Scope for low ratings (1-2 stars).
     */
    public function scopeLowRating($query)
    {
        return $query->where('rating', '<=', 2);
    }

    /**
     * Scope for specific rating.
     */
    public function scopeRating($query, int $rating)
    {
        return $query->where('rating', $rating);
    }

    /**
     * Scope for ratings with feedback.
     */
    public function scopeWithFeedback($query)
    {
        return $query->whereNotNull('feedback')->where('feedback', '!=', '');
    }

    /**
     * Get the chat room this rating belongs to.
     */
    public function chatRoom(): BelongsTo
    {
        return $this->belongsTo(ChatRoom::class);
    }

    /**
     * Get the user who gave this rating.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the staff member being rated.
     */
    public function staffUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'staff_user_id');
    }

    /**
     * Check if rating is positive (4-5 stars).
     */
    public function isPositive(): bool
    {
        return $this->rating >= 4;
    }

    /**
     * Check if rating is negative (1-2 stars).
     */
    public function isNegative(): bool
    {
        return $this->rating <= 2;
    }

    /**
     * Check if rating is neutral (3 stars).
     */
    public function isNeutral(): bool
    {
        return $this->rating === 3;
    }

    /**
     * Get rating as stars.
     */
    public function getStarsAttribute(): string
    {
        return str_repeat('⭐', $this->rating) . str_repeat('☆', 5 - $this->rating);
    }

    /**
     * Get rating label.
     */
    public function getRatingLabelAttribute(): string
    {
        return match($this->rating) {
            1 => 'Very Poor',
            2 => 'Poor',
            3 => 'Average',
            4 => 'Good',
            5 => 'Excellent',
            default => 'Unknown'
        };
    }

    /**
     * Get category rating.
     */
    public function getCategoryRating(string $category): ?int
    {
        $categories = $this->rating_categories ?? [];
        return $categories[$category] ?? null;
    }

    /**
     * Get all category ratings with labels.
     */
    public function getCategoryRatingsWithLabelsAttribute(): array
    {
        $categories = $this->rating_categories ?? [];
        $result = [];

        foreach ($categories as $category => $rating) {
            $result[$category] = [
                'rating' => $rating,
                'label' => $this->getCategoryLabel($category),
                'stars' => str_repeat('⭐', $rating) . str_repeat('☆', 5 - $rating),
            ];
        }

        return $result;
    }

    /**
     * Get category label.
     */
    private function getCategoryLabel(string $category): string
    {
        return match($category) {
            'response_time' => 'Response Time',
            'helpfulness' => 'Helpfulness',
            'professionalism' => 'Professionalism',
            'knowledge' => 'Knowledge',
            'communication' => 'Communication',
            'problem_solving' => 'Problem Solving',
            default => ucfirst(str_replace('_', ' ', $category))
        };
    }

    /**
     * Get average category rating.
     */
    public function getAverageCategoryRatingAttribute(): float
    {
        $categories = $this->rating_categories ?? [];
        
        if (empty($categories)) {
            return 0;
        }

        return round(array_sum($categories) / count($categories), 2);
    }

    /**
     * Check if rating has detailed categories.
     */
    public function hasDetailedRating(): bool
    {
        return !empty($this->rating_categories);
    }

    /**
     * Get sentiment based on rating and feedback.
     */
    public function getSentimentAttribute(): string
    {
        if ($this->rating >= 4) {
            return 'positive';
        } elseif ($this->rating <= 2) {
            return 'negative';
        } else {
            return 'neutral';
        }
    }

    /**
     * Get feedback summary (first 100 characters).
     */
    public function getFeedbackSummaryAttribute(): ?string
    {
        if (!$this->feedback) {
            return null;
        }

        return strlen($this->feedback) > 100 
            ? substr($this->feedback, 0, 97) . '...'
            : $this->feedback;
    }

    /**
     * Calculate overall satisfaction score (0-100).
     */
    public function getSatisfactionScoreAttribute(): int
    {
        return ($this->rating / 5) * 100;
    }

    /**
     * Get rating color for UI.
     */
    public function getRatingColorAttribute(): string
    {
        return match($this->rating) {
            1, 2 => 'red',
            3 => 'yellow',
            4, 5 => 'green',
            default => 'gray'
        };
    }

    /**
     * Static method to calculate average rating for a staff member.
     */
    public static function getStaffAverageRating(User $staff): float
    {
        return static::where('staff_user_id', $staff->id)
                    ->avg('rating') ?? 0;
    }

    /**
     * Static method to get rating distribution for a staff member.
     */
    public static function getStaffRatingDistribution(User $staff): array
    {
        $ratings = static::where('staff_user_id', $staff->id)
                        ->selectRaw('rating, COUNT(*) as count')
                        ->groupBy('rating')
                        ->pluck('count', 'rating')
                        ->toArray();

        // Fill in missing ratings with 0
        for ($i = 1; $i <= 5; $i++) {
            if (!isset($ratings[$i])) {
                $ratings[$i] = 0;
            }
        }

        ksort($ratings);
        return $ratings;
    }

    /**
     * Static method to get overall system rating statistics.
     */
    public static function getSystemRatingStats(): array
    {
        $totalRatings = static::count();
        $averageRating = static::avg('rating') ?? 0;
        
        $distribution = static::selectRaw('rating, COUNT(*) as count')
                             ->groupBy('rating')
                             ->pluck('count', 'rating')
                             ->toArray();

        // Fill in missing ratings with 0
        for ($i = 1; $i <= 5; $i++) {
            if (!isset($distribution[$i])) {
                $distribution[$i] = 0;
            }
        }

        ksort($distribution);

        return [
            'total_ratings' => $totalRatings,
            'average_rating' => round($averageRating, 2),
            'distribution' => $distribution,
            'satisfaction_rate' => $totalRatings > 0 ? round((($distribution[4] + $distribution[5]) / $totalRatings) * 100, 2) : 0,
        ];
    }
}
