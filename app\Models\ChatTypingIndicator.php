<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ChatTypingIndicator extends Model
{
    use HasFactory;

    protected $fillable = [
        'chat_room_id',
        'user_id',
        'display_name',
        'is_typing',
        'started_at',
        'expires_at',
    ];

    protected $casts = [
        'is_typing' => 'boolean',
        'started_at' => 'datetime',
        'expires_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $attributes = [
        'is_typing' => true,
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->started_at)) {
                $model->started_at = now();
            }
            
            if (empty($model->expires_at)) {
                $model->expires_at = now()->addSeconds(config('chat.performance.typing_debounce_ms', 300) / 1000 + 5);
            }
        });
    }

    /**
     * Scope for active typing indicators.
     */
    public function scopeActive($query)
    {
        return $query->where('is_typing', true)
                    ->where('expires_at', '>', now());
    }

    /**
     * Scope for expired typing indicators.
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<=', now());
    }

    /**
     * Scope for specific chat room.
     */
    public function scopeForRoom($query, int $roomId)
    {
        return $query->where('chat_room_id', $roomId);
    }

    /**
     * Get the chat room this typing indicator belongs to.
     */
    public function chatRoom(): BelongsTo
    {
        return $this->belongsTo(ChatRoom::class);
    }

    /**
     * Get the user who is typing.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if typing indicator is active.
     */
    public function isActive(): bool
    {
        return $this->is_typing && $this->expires_at > now();
    }

    /**
     * Check if typing indicator is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at <= now();
    }

    /**
     * Get display name for the typing user.
     */
    public function getDisplayNameAttribute($value): string
    {
        if ($value) {
            return $value;
        }

        if ($this->user) {
            return $this->user->name;
        }

        // For anonymous visitors
        $visitorInfo = $this->chatRoom->visitor_info ?? [];
        return $visitorInfo['name'] ?? 'Anonymous Visitor';
    }

    /**
     * Get typing duration in seconds.
     */
    public function getTypingDurationAttribute(): int
    {
        if (!$this->started_at) {
            return 0;
        }

        $endTime = $this->is_typing ? now() : $this->updated_at;
        return $this->started_at->diffInSeconds($endTime);
    }

    /**
     * Get time remaining before expiry.
     */
    public function getTimeRemainingAttribute(): int
    {
        if ($this->isExpired()) {
            return 0;
        }

        return now()->diffInSeconds($this->expires_at);
    }

    /**
     * Stop typing.
     */
    public function stopTyping(): bool
    {
        $this->is_typing = false;
        return $this->save();
    }

    /**
     * Extend typing indicator.
     */
    public function extend(int $seconds = 5): bool
    {
        $this->expires_at = now()->addSeconds($seconds);
        return $this->save();
    }

    /**
     * Refresh typing indicator.
     */
    public function refresh(): bool
    {
        $this->started_at = now();
        $this->expires_at = now()->addSeconds(config('chat.performance.typing_debounce_ms', 300) / 1000 + 5);
        $this->is_typing = true;
        return $this->save();
    }

    /**
     * Get active typing users for a room.
     */
    public static function getActiveTypingUsers(int $roomId): array
    {
        return static::active()
                    ->forRoom($roomId)
                    ->with('user')
                    ->get()
                    ->map(function ($indicator) {
                        return [
                            'user_id' => $indicator->user_id,
                            'display_name' => $indicator->display_name,
                            'started_at' => $indicator->started_at,
                            'expires_at' => $indicator->expires_at,
                        ];
                    })
                    ->toArray();
    }

    /**
     * Set typing indicator for user in room.
     */
    public static function setTyping(int $roomId, ?int $userId = null, ?string $displayName = null): self
    {
        $indicator = static::where('chat_room_id', $roomId)
                          ->where('user_id', $userId)
                          ->first();

        if ($indicator) {
            $indicator->refresh();
            return $indicator;
        }

        return static::create([
            'chat_room_id' => $roomId,
            'user_id' => $userId,
            'display_name' => $displayName,
            'is_typing' => true,
            'started_at' => now(),
            'expires_at' => now()->addSeconds(config('chat.performance.typing_debounce_ms', 300) / 1000 + 5),
        ]);
    }

    /**
     * Stop typing for user in room.
     */
    public static function stopTypingForUser(int $roomId, ?int $userId = null): bool
    {
        $indicator = static::where('chat_room_id', $roomId)
                          ->where('user_id', $userId)
                          ->first();

        if ($indicator) {
            return $indicator->stopTyping();
        }

        return true;
    }

    /**
     * Clean up expired typing indicators.
     */
    public static function cleanupExpired(): int
    {
        return static::expired()->delete();
    }

    /**
     * Get typing statistics for a room.
     */
    public static function getRoomTypingStats(int $roomId): array
    {
        $total = static::forRoom($roomId)->count();
        $active = static::active()->forRoom($roomId)->count();
        $avgDuration = static::forRoom($roomId)->avg('typing_duration') ?? 0;

        return [
            'total_typing_events' => $total,
            'currently_typing' => $active,
            'average_typing_duration' => round($avgDuration, 2),
        ];
    }

    /**
     * Get system-wide typing statistics.
     */
    public static function getSystemTypingStats(): array
    {
        $total = static::count();
        $active = static::active()->count();
        $avgDuration = static::avg('typing_duration') ?? 0;
        $roomsWithTyping = static::active()->distinct('chat_room_id')->count();

        return [
            'total_typing_events' => $total,
            'currently_typing_users' => $active,
            'rooms_with_typing' => $roomsWithTyping,
            'average_typing_duration' => round($avgDuration, 2),
        ];
    }

    /**
     * Get typing activity for the last N hours.
     */
    public static function getTypingActivity(int $hours = 24): array
    {
        $activity = static::where('created_at', '>=', now()->subHours($hours))
                         ->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d %H:00:00") as hour, COUNT(*) as count')
                         ->groupBy('hour')
                         ->orderBy('hour')
                         ->pluck('count', 'hour')
                         ->toArray();

        // Fill in missing hours with 0
        $result = [];
        for ($i = $hours - 1; $i >= 0; $i--) {
            $hour = now()->subHours($i)->format('Y-m-d H:00:00');
            $result[$hour] = $activity[$hour] ?? 0;
        }

        return $result;
    }
}
