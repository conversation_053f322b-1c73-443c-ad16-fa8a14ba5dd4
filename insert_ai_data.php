<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

try {
    $data = [
        [
            'intent' => 'greeting',
            'input_text' => 'hello',
            'expected_response' => 'Hello! Welcome to ChiSolution. How can I help you today?',
            'language' => 'en',
            'confidence_threshold' => 0.95,
            'category' => 'greeting',
            'tags' => json_encode(['greeting', 'welcome']),
            'is_active' => 1,
            'created_at' => now(),
            'updated_at' => now(),
        ],
        [
            'intent' => 'greeting',
            'input_text' => 'hi',
            'expected_response' => 'Hi there! I\'m here to assist you. What can I help you with?',
            'language' => 'en',
            'confidence_threshold' => 0.95,
            'category' => 'greeting',
            'tags' => json_encode(['greeting', 'welcome']),
            'is_active' => 1,
            'created_at' => now(),
            'updated_at' => now(),
        ],
        [
            'intent' => 'greeting',
            'input_text' => 'hey',
            'expected_response' => 'Hey! Thanks for reaching out to ChiSolution. How can I assist you today?',
            'language' => 'en',
            'confidence_threshold' => 0.95,
            'category' => 'greeting',
            'tags' => json_encode(['greeting', 'welcome']),
            'is_active' => 1,
            'created_at' => now(),
            'updated_at' => now(),
        ],
        [
            'intent' => 'services',
            'input_text' => 'what services do you offer',
            'expected_response' => 'ChiSolution offers web development, mobile app development, digital marketing, and custom software solutions. Would you like to know more about any specific service?',
            'language' => 'en',
            'confidence_threshold' => 0.90,
            'category' => 'services',
            'tags' => json_encode(['services', 'offerings']),
            'is_active' => 1,
            'created_at' => now(),
            'updated_at' => now(),
        ],
        [
            'intent' => 'about',
            'input_text' => 'who are you',
            'expected_response' => 'I\'m an AI assistant for ChiSolution, a digital agency that creates amazing websites, mobile apps, and digital solutions. We help businesses succeed online. What would you like to know about us?',
            'language' => 'en',
            'confidence_threshold' => 0.90,
            'category' => 'about',
            'tags' => json_encode(['about', 'company', 'ai']),
            'is_active' => 1,
            'created_at' => now(),
            'updated_at' => now(),
        ],
    ];

    foreach ($data as $item) {
        DB::table('ai_training_data')->updateOrInsert(
            [
                'intent' => $item['intent'],
                'input_text' => $item['input_text'],
                'language' => $item['language'],
            ],
            $item
        );
        echo "Inserted: {$item['input_text']}\n";
    }

    echo "\nAI training data inserted successfully!\n";
    echo "Total records in ai_training_data: " . DB::table('ai_training_data')->count() . "\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
