<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat API Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">Chat API Test</h1>
        
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">API Endpoint Tests</h2>
            
            <div class="space-y-4">
                <button onclick="testCreateRoom()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    Test Create Room
                </button>
                
                <button onclick="testHealthCheck()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    Test Health Check
                </button>
                
                <button onclick="testCsrfToken()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    Test CSRF Token
                </button>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">Test Results</h2>
            <div id="results" class="space-y-2 font-mono text-sm"></div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            
            let className = 'p-2 rounded ';
            switch(type) {
                case 'error':
                    className += 'bg-red-100 text-red-800';
                    break;
                case 'success':
                    className += 'bg-green-100 text-green-800';
                    break;
                case 'warning':
                    className += 'bg-yellow-100 text-yellow-800';
                    break;
                default:
                    className += 'bg-blue-100 text-blue-800';
            }
            
            div.className = className;
            div.innerHTML = `[${timestamp}] ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        async function testHealthCheck() {
            log('Testing health check endpoint...');
            
            try {
                const response = await fetch('/up', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                log(`Health check response: ${response.status} ${response.statusText}`, 
                    response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const data = await response.text();
                    log(`Health check data: ${data.substring(0, 100)}...`);
                }
            } catch (error) {
                log(`Health check error: ${error.message}`, 'error');
            }
        }

        async function testCsrfToken() {
            log('Testing CSRF token availability...');
            
            // Check for CSRF token in meta tag
            const metaToken = document.querySelector('meta[name="csrf-token"]');
            if (metaToken) {
                log(`CSRF token found in meta tag: ${metaToken.getAttribute('content').substring(0, 20)}...`, 'success');
            } else {
                log('No CSRF token found in meta tag', 'warning');
            }
            
            // Try to get CSRF token from Laravel
            try {
                const response = await fetch('/sanctum/csrf-cookie', {
                    method: 'GET',
                    credentials: 'same-origin'
                });
                
                log(`CSRF cookie response: ${response.status} ${response.statusText}`, 
                    response.ok ? 'success' : 'error');
                    
            } catch (error) {
                log(`CSRF cookie error: ${error.message}`, 'error');
            }
        }

        async function testCreateRoom() {
            log('Testing chat room creation...');
            
            // Get CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
            log(`Using CSRF token: ${csrfToken ? 'present' : 'missing'}`);
            
            const payload = {
                type: 'visitor',
                priority: 1,
                language: 'en',
                visitor_info: {
                    name: 'Test User',
                    email: '<EMAIL>', // Required field
                    page: window.location.href,
                    user_agent: navigator.userAgent,
                    referrer: document.referrer || '',
                    timestamp: new Date().toISOString()
                },
                metadata: {
                    source: 'api_test',
                    version: '1.0.0'
                }
            };
            
            log(`Payload: ${JSON.stringify(payload, null, 2)}`);
            
            try {
                const headers = {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                };
                
                if (csrfToken) {
                    headers['X-CSRF-TOKEN'] = csrfToken;
                }
                
                log(`Headers: ${JSON.stringify(headers, null, 2)}`);
                
                const response = await fetch('/api/v1/chat/rooms', {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(payload)
                });
                
                log(`Response status: ${response.status} ${response.statusText}`, 
                    response.ok ? 'success' : 'error');
                
                log(`Response headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}`);
                
                const responseText = await response.text();
                log(`Response body: ${responseText}`);
                
                if (response.ok) {
                    try {
                        const data = JSON.parse(responseText);
                        log(`Parsed response: ${JSON.stringify(data, null, 2)}`, 'success');
                        
                        if (data.success && data.data?.room) {
                            log(`Room created successfully! UUID: ${data.data.room.uuid}`, 'success');
                            
                            // Test sending a message
                            await testSendMessage(data.data.room.uuid);
                        }
                    } catch (parseError) {
                        log(`Failed to parse JSON response: ${parseError.message}`, 'error');
                    }
                } else {
                    log(`Room creation failed: ${response.status} - ${responseText}`, 'error');
                }
                
            } catch (error) {
                log(`Room creation error: ${error.message}`, 'error');
            }
        }

        async function testSendMessage(roomUuid) {
            log(`Testing message sending to room: ${roomUuid}...`);
            
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
            
            const payload = {
                content: 'Hello from API test!',
                message_type: 'text',
                metadata: {
                    source: 'api_test',
                    timestamp: new Date().toISOString()
                }
            };
            
            try {
                const headers = {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                };
                
                if (csrfToken) {
                    headers['X-CSRF-TOKEN'] = csrfToken;
                }
                
                const response = await fetch(`/api/v1/chat/rooms/${roomUuid}/messages`, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(payload)
                });
                
                log(`Message response: ${response.status} ${response.statusText}`, 
                    response.ok ? 'success' : 'error');
                
                const responseText = await response.text();
                log(`Message response body: ${responseText}`);
                
                if (response.ok) {
                    try {
                        const data = JSON.parse(responseText);
                        log(`Message sent successfully!`, 'success');
                    } catch (parseError) {
                        log(`Failed to parse message response: ${parseError.message}`, 'error');
                    }
                }
                
            } catch (error) {
                log(`Message sending error: ${error.message}`, 'error');
            }
        }

        // Add CSRF token meta tag for testing
        if (!document.querySelector('meta[name="csrf-token"]')) {
            const meta = document.createElement('meta');
            meta.name = 'csrf-token';
            meta.content = 'test-token-' + Math.random().toString(36).substr(2, 9);
            document.head.appendChild(meta);
            log('Added test CSRF token', 'warning');
        }
        
        // Auto-run health check on load
        document.addEventListener('DOMContentLoaded', function() {
            testHealthCheck();
            testCsrfToken();
        });
    </script>
</body>
</html>
