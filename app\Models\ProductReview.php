<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;
use Carbon\Carbon;

class ProductReview extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'product_id',
        'user_id',
        'order_id',
        'rating',
        'review_content',
        'is_verified_purchase',
        'purchase_date',
        'delivery_date',
        'is_approved',
        'is_featured',
        'is_deleted',
        'helpful_count',
        'helpful_users',
        'is_flagged',
        'flag_count',
        'flagged_by',
        'admin_notes',
    ];

    protected $casts = [
        'helpful_users' => 'array',
        'flagged_by' => 'array',
        'is_verified_purchase' => 'boolean',
        'is_approved' => 'boolean',
        'is_featured' => 'boolean',
        'is_deleted' => 'boolean',
        'is_flagged' => 'boolean',
        'purchase_date' => 'datetime',
        'delivery_date' => 'datetime',
    ];

    protected $appends = [
        'formatted_created_date',
        'time_ago',
        'star_rating',
        'is_recent',
        'days_since_purchase',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($review) {
            if (empty($review->uuid)) {
                $review->uuid = Str::uuid();
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    // ==================== RELATIONSHIPS ====================

    /**
     * Get the product this review belongs to.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the user who wrote this review.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the order this review is associated with.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    // ==================== SCOPES ====================

    /**
     * Scope a query to only include approved reviews.
     */
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    /**
     * Scope a query to only include non-deleted reviews.
     */
    public function scopeActive($query)
    {
        return $query->where('is_deleted', false);
    }

    /**
     * Scope a query to only include verified purchase reviews.
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified_purchase', true);
    }

    /**
     * Scope a query to only include featured reviews.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to order by most helpful.
     */
    public function scopeByHelpfulness($query)
    {
        return $query->orderBy('helpful_count', 'desc');
    }

    /**
     * Scope a query to order by rating (highest first).
     */
    public function scopeByRating($query, $direction = 'desc')
    {
        return $query->orderBy('rating', $direction);
    }

    /**
     * Scope a query to filter by rating.
     */
    public function scopeWithRating($query, $rating)
    {
        return $query->where('rating', $rating);
    }

    // ==================== ACCESSORS ====================

    /**
     * Get the formatted creation date.
     */
    public function getFormattedCreatedDateAttribute(): string
    {
        return $this->created_at->format('M j, Y');
    }

    /**
     * Get the time ago string.
     */
    public function getTimeAgoAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Get the star rating display.
     */
    public function getStarRatingAttribute(): string
    {
        $stars = '';
        for ($i = 1; $i <= 5; $i++) {
            $stars .= $i <= $this->rating ? '★' : '☆';
        }
        return $stars;
    }

    /**
     * Check if this is a recent review (within 30 days).
     */
    public function getIsRecentAttribute(): bool
    {
        return $this->created_at->isAfter(now()->subDays(30));
    }

    /**
     * Get days since purchase.
     */
    public function getDaysSincePurchaseAttribute(): ?int
    {
        return $this->purchase_date ? $this->purchase_date->diffInDays($this->created_at) : null;
    }

    // ==================== METHODS ====================

    /**
     * Mark review as helpful by a user.
     */
    public function markAsHelpful(int $userId): bool
    {
        $helpfulUsers = $this->helpful_users ?? [];

        if (!in_array($userId, $helpfulUsers)) {
            $helpfulUsers[] = $userId;
            $this->helpful_users = $helpfulUsers;
            $this->helpful_count = count($helpfulUsers);
            return $this->save();
        }

        return false;
    }

    /**
     * Remove helpful mark by a user.
     */
    public function removeHelpful(int $userId): bool
    {
        $helpfulUsers = $this->helpful_users ?? [];
        $key = array_search($userId, $helpfulUsers);

        if ($key !== false) {
            unset($helpfulUsers[$key]);
            $this->helpful_users = array_values($helpfulUsers);
            $this->helpful_count = count($helpfulUsers);
            return $this->save();
        }

        return false;
    }

    /**
     * Check if user marked this review as helpful.
     */
    public function isMarkedHelpfulBy(int $userId): bool
    {
        return in_array($userId, $this->helpful_users ?? []);
    }

    /**
     * Flag review as inappropriate.
     */
    public function flagAsInappropriate(int $userId): bool
    {
        $flaggedBy = $this->flagged_by ?? [];

        if (!in_array($userId, $flaggedBy)) {
            $flaggedBy[] = $userId;
            $this->flagged_by = $flaggedBy;
            $this->flag_count = count($flaggedBy);

            // Auto-flag if multiple users flag it
            if ($this->flag_count >= 3) {
                $this->is_flagged = true;
            }

            return $this->save();
        }

        return false;
    }

    /**
     * Check if user can edit this review.
     */
    public function canBeEditedBy(int $userId): bool
    {
        return $this->user_id === $userId &&
               $this->created_at->isAfter(now()->subDays(30)) && // Allow edits within 30 days
               !$this->is_deleted;
    }

    /**
     * Validate that user has purchased and received the product.
     */
    public static function validatePurchaseEligibility(int $productId, int $userId): array
    {
        // Find delivered orders containing this product for this user
        $eligibleOrders = Order::where('user_id', $userId)
            ->where('status', 'delivered')
            ->whereHas('orderItems', function($query) use ($productId) {
                $query->where('product_id', $productId);
            })
            ->with(['orderItems' => function($query) use ($productId) {
                $query->where('product_id', $productId);
            }])
            ->get();

        if ($eligibleOrders->isEmpty()) {
            return [
                'eligible' => false,
                'reason' => 'You must purchase and receive this product before you can leave a review.',
                'orders' => []
            ];
        }

        // Check if user has already reviewed this product for any of these orders
        $existingReviews = self::where('product_id', $productId)
            ->where('user_id', $userId)
            ->whereIn('order_id', $eligibleOrders->pluck('id'))
            ->where('is_deleted', false)
            ->get();

        return [
            'eligible' => true,
            'orders' => $eligibleOrders,
            'existing_reviews' => $existingReviews,
            'can_review_new' => $existingReviews->isEmpty(),
            'can_update_existing' => $existingReviews->isNotEmpty()
        ];
    }
}
