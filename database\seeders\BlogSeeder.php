<?php

namespace Database\Seeders;

use App\Models\BlogCategory;
use App\Models\BlogPost;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class BlogSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create blog categories
        $this->createCategories();
        
        // Create blog posts
        $this->createPosts();
    }

    /**
     * Create blog categories.
     */
    private function createCategories(): void
    {
        $categories = [
            [
                'name' => 'Web Development',
                'slug' => 'web-development',
                'description' => 'Articles about web development, frameworks, and best practices.',
                'sort_order' => 1,
            ],
            [
                'name' => 'Mobile Development',
                'slug' => 'mobile-development',
                'description' => 'Mobile app development tutorials and insights.',
                'sort_order' => 2,
            ],
            [
                'name' => 'Digital Marketing',
                'slug' => 'digital-marketing',
                'description' => 'Digital marketing strategies, SEO, and online advertising.',
                'sort_order' => 3,
            ],
            [
                'name' => 'Design',
                'slug' => 'design',
                'description' => 'UI/UX design principles, trends, and case studies.',
                'sort_order' => 4,
            ],
            [
                'name' => 'Technology Trends',
                'slug' => 'technology-trends',
                'description' => 'Latest technology trends and industry insights.',
                'sort_order' => 5,
            ],
        ];

        foreach ($categories as $categoryData) {
            BlogCategory::firstOrCreate(
                ['slug' => $categoryData['slug']],
                array_merge($categoryData, [
                    'is_active' => true,
                    'is_deleted' => false,
                    'meta_title' => $categoryData['name'] . ' - Blog Category',
                    'meta_description' => $categoryData['description'],
                ])
            );
        }
    }

    /**
     * Create blog posts.
     */
    private function createPosts(): void
    {
        $categories = BlogCategory::active()->get();
        $authors = User::whereHas('role', function($query) {
            $query->whereIn('name', ['admin', 'staff']);
        })->get();

        if ($authors->isEmpty()) {
            $authors = User::take(3)->get();
        }

        // Get services for associations
        $services = \App\Models\Service::active()->get();

        $posts = [
            [
                'title' => 'The Future of Web Development: Trends to Watch in 2024',
                'content' => $this->getWebDevContent(),
                'category' => 'web-development',
                'is_featured' => true,
                'services' => ['web-development'],
                'gallery_images' => [
                    'blog/samples/web-dev-trends-1.jpg',
                    'blog/samples/web-dev-trends-2.jpg'
                ]
            ],
            [
                'title' => 'Building Responsive Mobile Apps with React Native',
                'content' => $this->getMobileDevContent(),
                'category' => 'mobile-development',
                'is_featured' => true,
                'services' => ['mobile-app-development'],
                'gallery_images' => [
                    'blog/samples/react-native-1.jpg',
                    'blog/samples/react-native-2.jpg',
                    'blog/samples/react-native-3.jpg'
                ]
            ],
            [
                'title' => 'SEO Best Practices for Modern Websites',
                'content' => $this->getSeoContent(),
                'category' => 'digital-marketing',
                'is_featured' => false,
                'services' => ['digital-marketing', 'web-development'],
                'gallery_images' => []
            ],
            [
                'title' => 'UI/UX Design Principles That Drive Conversions',
                'content' => $this->getDesignContent(),
                'category' => 'design',
                'is_featured' => true,
                'services' => ['ui-ux-design'],
                'gallery_images' => [
                    'blog/samples/ux-design-1.jpg',
                    'blog/samples/ux-design-2.jpg'
                ]
            ],
            [
                'title' => 'Artificial Intelligence in Web Development',
                'content' => $this->getAiContent(),
                'category' => 'technology-trends',
                'is_featured' => false,
                'services' => ['web-development'],
                'gallery_images' => [
                    'blog/samples/ai-web-dev-1.jpg'
                ]
            ],
            [
                'title' => 'E-commerce Platform Development: Complete Guide',
                'content' => $this->getEcommerceContent(),
                'category' => 'web-development',
                'is_featured' => true,
                'services' => ['ecommerce-solutions', 'web-development'],
                'gallery_images' => [
                    'blog/samples/ecommerce-1.jpg',
                    'blog/samples/ecommerce-2.jpg',
                    'blog/samples/ecommerce-3.jpg'
                ]
            ],
        ];

        foreach ($posts as $postData) {
            $category = $categories->where('slug', $postData['category'])->first();
            $author = $authors->random();

            // Get service IDs for associations
            $serviceIds = [];
            if (!empty($postData['services'])) {
                $serviceIds = $services->whereIn('slug', $postData['services'])->pluck('id')->toArray();
            }

            BlogPost::firstOrCreate(
                ['slug' => Str::slug($postData['title'])],
                [
                    'uuid' => Str::uuid(),
                    'title' => $postData['title'],
                    'slug' => Str::slug($postData['title']),
                    'content' => $postData['content'],
                    'excerpt' => Str::limit(strip_tags($postData['content']), 160),
                    'gallery_images' => $postData['gallery_images'] ?? [],
                    'category_id' => $category?->id,
                    'author_id' => $author->id,
                    'service_ids' => $serviceIds,
                    'view_count' => rand(50, 500),
                    'is_published' => true,
                    'is_featured' => $postData['is_featured'],
                    'is_deleted' => false,
                    'published_at' => now()->subDays(rand(1, 30)),
                    'meta_title' => $postData['title'],
                    'meta_description' => Str::limit(strip_tags($postData['content']), 160),
                ]
            );
        }
    }

    private function getWebDevContent(): string
    {
        return '<p>Web development continues to evolve at a rapid pace, with new frameworks, tools, and methodologies emerging regularly. In this comprehensive guide, we explore the key trends that will shape web development in 2024 and beyond.</p>

<h2>1. Progressive Web Applications (PWAs)</h2>
<p>PWAs are becoming increasingly popular as they offer native app-like experiences through web browsers. They provide offline functionality, push notifications, and improved performance, making them an excellent choice for businesses looking to enhance user engagement.</p>

<h2>2. Serverless Architecture</h2>
<p>Serverless computing is revolutionizing how we build and deploy web applications. By eliminating the need to manage servers, developers can focus on writing code while cloud providers handle the infrastructure.</p>

<h2>3. AI-Powered Development Tools</h2>
<p>Artificial intelligence is transforming the development process with tools that can generate code, detect bugs, and optimize performance automatically. These tools are making developers more productive and helping create better applications.</p>';
    }

    private function getMobileDevContent(): string
    {
        return '<p>React Native has become one of the most popular frameworks for building cross-platform mobile applications. This guide covers best practices for creating responsive and performant mobile apps.</p>

<h2>Getting Started with React Native</h2>
<p>React Native allows developers to build mobile apps using React and JavaScript. The framework provides native components that compile to native code, ensuring excellent performance across iOS and Android platforms.</p>

<h2>Key Benefits</h2>
<ul>
<li>Code reusability across platforms</li>
<li>Faster development cycles</li>
<li>Native performance</li>
<li>Large community support</li>
</ul>

<h2>Best Practices</h2>
<p>To build successful React Native apps, follow these essential practices: optimize images, use proper state management, implement proper navigation, and test on real devices.</p>';
    }

    private function getSeoContent(): string
    {
        return '<p>Search Engine Optimization (SEO) remains crucial for online visibility. This article covers the latest SEO best practices that can help your website rank higher in search results.</p>

<h2>Technical SEO Fundamentals</h2>
<p>Technical SEO forms the foundation of your website\'s search performance. Ensure your site has proper URL structure, fast loading times, mobile responsiveness, and clean code.</p>

<h2>Content Optimization</h2>
<p>Create high-quality, relevant content that addresses user intent. Use proper heading structure, optimize meta tags, and include relevant keywords naturally throughout your content.</p>

<h2>Local SEO</h2>
<p>For businesses with physical locations, local SEO is essential. Optimize your Google My Business profile, gather positive reviews, and ensure consistent NAP (Name, Address, Phone) information across all platforms.</p>';
    }

    private function getDesignContent(): string
    {
        return '<p>Great UI/UX design is more than just aesthetics—it\'s about creating experiences that convert visitors into customers. Learn the key principles that drive successful conversions.</p>

<h2>User-Centered Design</h2>
<p>Always start with your users. Understand their needs, pain points, and goals. Conduct user research, create personas, and design with empathy to create solutions that truly serve your audience.</p>

<h2>Clear Visual Hierarchy</h2>
<p>Guide users through your interface with clear visual hierarchy. Use size, color, contrast, and spacing to direct attention to the most important elements and actions.</p>

<h2>Simplified User Flows</h2>
<p>Reduce friction in user journeys by simplifying processes. Minimize the number of steps required to complete actions and provide clear feedback at each stage.</p>';
    }

    private function getAiContent(): string
    {
        return '<p>Artificial Intelligence is transforming web development, offering new possibilities for creating smarter, more efficient applications. Explore how AI is being integrated into modern web development workflows.</p>

<h2>AI-Powered Code Generation</h2>
<p>Tools like GitHub Copilot and ChatGPT are helping developers write code faster and more efficiently. These AI assistants can generate boilerplate code, suggest optimizations, and help debug issues.</p>

<h2>Intelligent User Interfaces</h2>
<p>AI is enabling more personalized and adaptive user interfaces. From chatbots to recommendation systems, AI helps create more engaging user experiences.</p>

<h2>Automated Testing and Quality Assurance</h2>
<p>AI-powered testing tools can automatically generate test cases, identify potential bugs, and optimize application performance, leading to higher quality software.</p>';
    }

    private function getEcommerceContent(): string
    {
        return '<p>Building a successful e-commerce platform requires careful planning, robust architecture, and attention to user experience. This comprehensive guide covers everything you need to know about e-commerce development.</p>

<h2>Platform Architecture</h2>
<p>Choose the right architecture for your e-commerce platform. Consider microservices for scalability, implement proper caching strategies, and ensure your database can handle high transaction volumes.</p>

<h2>Payment Integration</h2>
<p>Secure payment processing is crucial for e-commerce success. Integrate multiple payment gateways, implement PCI compliance, and provide various payment options including digital wallets and buy-now-pay-later services.</p>

<h2>Inventory Management</h2>
<p>Implement real-time inventory tracking, automated stock alerts, and multi-warehouse support. Consider integration with third-party logistics providers for seamless order fulfillment.</p>

<h2>User Experience Optimization</h2>
<p>Focus on mobile-first design, fast loading times, intuitive navigation, and streamlined checkout processes. Implement features like wishlist, product comparison, and personalized recommendations.</p>

<h2>Security and Performance</h2>
<p>Implement robust security measures including SSL certificates, data encryption, and regular security audits. Optimize for performance with CDN integration, image optimization, and efficient caching strategies.</p>';
    }
}
