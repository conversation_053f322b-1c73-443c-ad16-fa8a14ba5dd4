<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\AiConversationLog;
use App\Services\ChatService;
use App\Services\ChatAIService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use PHPUnit\Framework\Attributes\Test;

class ChatIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected ChatService $chatService;
    protected ChatAIService $aiService;
    protected User $user;
    protected ChatRoom $room;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->chatService = app(ChatService::class);
        $this->aiService = app(ChatAIService::class);
        
        // Create a test user
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_active' => true,
        ]);
        
        // Create a test chat room
        $this->room = ChatRoom::factory()->create([
            'type' => 'visitor',
            'status' => 'active',
            'language' => 'en',
        ]);
    }

    #[Test]
    public function test_complete_chat_flow_with_ai_response()
    {
        // Disable queues to run synchronously
        Queue::fake();
        
        // Send a user message
        $userMessage = $this->chatService->sendMessage($this->room, [
            'content' => 'What are your pricing options?',
            'message_type' => 'text',
            'user_id' => $this->user->id,
        ]);

        $this->assertInstanceOf(ChatMessage::class, $userMessage);
        $this->assertEquals('What are your pricing options?', $userMessage->content);
        $this->assertEquals($this->room->id, $userMessage->chat_room_id);
        $this->assertEquals($this->user->id, $userMessage->user_id);

        // Generate AI response with proper context
        $context = [
            'user_message_id' => $userMessage->id,
            'conversation_history' => [],
            'room_info' => [
                'type' => $this->room->type,
                'priority' => $this->room->priority,
                'language' => $this->room->language,
            ],
        ];

        $aiResponse = $this->aiService->generateResponse(
            $userMessage->content,
            $this->room,
            $context
        );

        // Verify AI response structure
        $this->assertIsArray($aiResponse);
        $this->assertArrayHasKey('response', $aiResponse);
        $this->assertArrayHasKey('confidence', $aiResponse);
        $this->assertArrayHasKey('intent', $aiResponse);
        $this->assertArrayHasKey('should_escalate', $aiResponse);
        $this->assertArrayHasKey('response_type', $aiResponse);

        // Verify response content is appropriate for pricing inquiry
        $this->assertNotEmpty($aiResponse['response']);
        $this->assertEquals('pricing', $aiResponse['intent']);
        $this->assertGreaterThan(0, $aiResponse['confidence']);

        // Create AI message
        $aiMessage = $this->chatService->sendMessage($this->room, [
            'content' => $aiResponse['response'],
            'message_type' => 'text',
            'is_ai_generated' => true,
            'ai_confidence' => $aiResponse['confidence'],
            'ai_model' => $aiResponse['model_used'] ?? 'simple_nlp',
            'metadata' => [
                'intent' => $aiResponse['intent'],
                'response_type' => $aiResponse['response_type'],
                'user_message_id' => $userMessage->id,
            ],
        ]);

        $this->assertInstanceOf(ChatMessage::class, $aiMessage);
        $this->assertTrue($aiMessage->is_ai_generated);
        $this->assertEquals($aiResponse['response'], $aiMessage->content);

        // Verify conversation log was created
        $conversationLog = AiConversationLog::where('chat_room_id', $this->room->id)
            ->where('chat_message_id', $userMessage->id)
            ->first();

        $this->assertNotNull($conversationLog, 'AI conversation log should be created');
        $this->assertEquals($userMessage->content, $conversationLog->user_message);
        $this->assertEquals($aiResponse['response'], $conversationLog->ai_response);
        $this->assertEquals($aiResponse['intent'], $conversationLog->intent_detected);
        $this->assertEquals($aiResponse['confidence'], $conversationLog->confidence_score);
    }

    #[Test]
    public function test_ai_fallback_when_external_providers_fail()
    {
        // Mock external AI provider failure
        $this->mock(\App\Services\AIProviderManager::class, function ($mock) {
            $mock->shouldReceive('generateResponse')
                ->andThrow(new \Exception('All AI providers failed'));
        });

        // Use a message that won't match simple NLP patterns to force external AI call
        $userMessage = $this->chatService->sendMessage($this->room, [
            'content' => 'Complex technical query about database optimization strategies',
            'message_type' => 'text',
            'user_id' => $this->user->id,
        ]);

        $context = [
            'user_message_id' => $userMessage->id,
            'conversation_history' => [],
        ];

        $aiResponse = $this->aiService->generateResponse(
            $userMessage->content,
            $this->room,
            $context
        );

        // Should get a fallback response
        $this->assertIsArray($aiResponse);
        $this->assertArrayHasKey('response', $aiResponse);
        $this->assertEquals('fallback', $aiResponse['intent']);
        $this->assertTrue($aiResponse['should_escalate']);
        $this->assertStringContainsString('human agent', $aiResponse['response']);

        // Verify conversation log was still created
        $conversationLog = AiConversationLog::where('chat_room_id', $this->room->id)
            ->where('chat_message_id', $userMessage->id)
            ->first();

        $this->assertNotNull($conversationLog);
        $this->assertTrue($conversationLog->escalated_to_human);
        $this->assertNotNull($conversationLog->escalation_reason);
    }

    #[Test]
    public function test_simple_nlp_responses_work_without_external_ai()
    {
        $testCases = [
            ['message' => 'hello', 'expected_intent' => 'greeting'],
            ['message' => 'what are your business hours?', 'expected_intent' => 'business_hours'],
            ['message' => 'how can I contact you?', 'expected_intent' => 'contact'],
            ['message' => 'what services do you offer?', 'expected_intent' => 'services'],
            ['message' => 'thank you', 'expected_intent' => 'thanks'],
        ];

        foreach ($testCases as $testCase) {
            $userMessage = $this->chatService->sendMessage($this->room, [
                'content' => $testCase['message'],
                'message_type' => 'text',
                'user_id' => $this->user->id,
            ]);

            $context = [
                'user_message_id' => $userMessage->id,
            ];

            $aiResponse = $this->aiService->generateResponse(
                $userMessage->content,
                $this->room,
                $context
            );

            $this->assertEquals($testCase['expected_intent'], $aiResponse['intent'], 
                "Failed for message: {$testCase['message']}");
            $this->assertFalse($aiResponse['should_escalate'], 
                "Should not escalate for message: {$testCase['message']}");
            $this->assertNotEmpty($aiResponse['response'], 
                "Response should not be empty for message: {$testCase['message']}");

            // Clean up for next iteration
            $userMessage->delete();
            AiConversationLog::where('chat_room_id', $this->room->id)->delete();
        }
    }

    #[Test]
    public function test_enhanced_fallback_for_questions()
    {
        $userMessage = $this->chatService->sendMessage($this->room, [
            'content' => 'What can you help me with?',
            'message_type' => 'text',
            'user_id' => $this->user->id,
        ]);

        $context = [
            'user_message_id' => $userMessage->id,
        ];

        $aiResponse = $this->aiService->generateResponse(
            $userMessage->content,
            $this->room,
            $context
        );

        // Should get an enhanced fallback response for questions
        $this->assertIsArray($aiResponse);
        $this->assertNotEmpty($aiResponse['response']);
        $this->assertContains($aiResponse['intent'], ['services', 'question']);
        $this->assertFalse($aiResponse['should_escalate']);
    }

    #[Test]
    public function test_frustration_detection_and_escalation()
    {
        $userMessage = $this->chatService->sendMessage($this->room, [
            'content' => 'I am very frustrated with this terrible service',
            'message_type' => 'text',
            'user_id' => $this->user->id,
        ]);

        $context = [
            'user_message_id' => $userMessage->id,
        ];

        $aiResponse = $this->aiService->generateResponse(
            $userMessage->content,
            $this->room,
            $context
        );

        // Should detect frustration and escalate
        $this->assertEquals('frustration', $aiResponse['intent']);
        $this->assertTrue($aiResponse['should_escalate']);
        $this->assertStringContainsString('understand your frustration', $aiResponse['response']);
    }

    #[Test]
    public function test_conversation_logging_handles_missing_message_id()
    {
        // Test without providing user_message_id in context
        $context = [];

        $aiResponse = $this->aiService->generateResponse(
            'Hello',
            $this->room,
            $context
        );

        // Should still work and not throw database errors
        $this->assertIsArray($aiResponse);
        $this->assertNotEmpty($aiResponse['response']);
        
        // Log should either not be created or use the latest message ID
        $logs = AiConversationLog::where('chat_room_id', $this->room->id)->get();
        // Should not cause database errors
        $this->assertTrue(true, 'No database errors occurred');
    }

    #[Test]
    public function test_multilingual_responses()
    {
        // Test Afrikaans
        $this->room->update(['language' => 'af']);
        
        $userMessage = $this->chatService->sendMessage($this->room, [
            'content' => 'hallo',
            'message_type' => 'text',
            'user_id' => $this->user->id,
        ]);

        $context = [
            'user_message_id' => $userMessage->id,
        ];

        $aiResponse = $this->aiService->generateResponse(
            $userMessage->content,
            $this->room,
            $context
        );

        $this->assertEquals('greeting', $aiResponse['intent']);
        $this->assertStringContainsString('help', strtolower($aiResponse['response']));
    }
}
