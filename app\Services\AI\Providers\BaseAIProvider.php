<?php

namespace App\Services\AI\Providers;

use App\Services\AI\Contracts\AIProviderInterface;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Exception;

abstract class BaseAIProvider implements AIProviderInterface
{
    protected array $config;
    protected array $lastUsage = [];
    protected string $name;

    public function __construct(array $config)
    {
        $this->config = $config;
        $this->name = $config['name'] ?? static::class;
    }

    /**
     * Get the default model for this provider.
     */
    protected function getDefaultModel(): string
    {
        $models = $this->config['models'] ?? [];
        
        // Find first standard tier model
        foreach ($models as $modelName => $modelConfig) {
            if (($modelConfig['tier'] ?? 'standard') === 'standard') {
                return $modelName;
            }
        }

        // Fallback to first available model
        return array_key_first($models) ?: '';
    }

    /**
     * Get model configuration.
     */
    protected function getModelConfig(string $model): array
    {
        return $this->config['models'][$model] ?? [];
    }

    /**
     * Validate that a model exists and is configured.
     */
    protected function validateModel(string $model): void
    {
        if (!isset($this->config['models'][$model])) {
            throw new Exception("Model '{$model}' is not configured for provider '{$this->name}'");
        }
    }

    /**
     * Make HTTP request to the provider API.
     */
    protected function makeRequest(string $endpoint, array $data, array $headers = []): array
    {
        $baseUrl = $this->config['base_url'];
        $url = rtrim($baseUrl, '/') . '/' . ltrim($endpoint, '/');

        $defaultHeaders = $this->getDefaultHeaders();
        $headers = array_merge($defaultHeaders, $headers);

        Log::info("Making AI API request", [
            'provider' => $this->name,
            'url' => $url,
            'data_size' => strlen(json_encode($data)),
        ]);

        $response = Http::withHeaders($headers)
            ->timeout(120)
            ->retry(3, 1000)
            ->post($url, $data);

        if (!$response->successful()) {
            $error = "AI API request failed: " . $response->status() . " - " . $response->body();
            Log::error($error, [
                'provider' => $this->name,
                'status' => $response->status(),
                'response' => $response->body(),
            ]);
            throw new Exception($error);
        }

        $responseData = $response->json();
        
        // Track usage if available
        if (isset($responseData['usage'])) {
            $this->trackUsage($responseData['usage']);
        }

        return $responseData;
    }

    /**
     * Get default headers for API requests.
     */
    protected function getDefaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'User-Agent' => 'ChiSolution-Chat/1.0',
        ];
    }

    /**
     * Track usage statistics.
     */
    protected function trackUsage(array $usage): void
    {
        $this->lastUsage = $usage;

        // Update daily usage counters
        $today = now()->format('Y-m-d');
        $requestKey = "ai_requests_today_{$this->name}_{$today}";
        $tokenKey = "ai_tokens_today_{$this->name}_{$today}";
        $costKey = "ai_cost_today_{$this->name}_{$today}";

        Cache::increment($requestKey, 1, 86400); // 24 hours
        
        if (isset($usage['total_tokens'])) {
            Cache::increment($tokenKey, $usage['total_tokens'], 86400);
        }

        // Calculate cost if possible
        $cost = $this->calculateCost($usage);
        if ($cost > 0) {
            $currentCost = Cache::get($costKey, 0.0);
            Cache::put($costKey, $currentCost + $cost, 86400);
        }

        // Update last used timestamp
        Cache::put("ai_last_used_{$this->name}", now(), 86400);
    }

    /**
     * Calculate cost based on usage.
     */
    protected function calculateCost(array $usage, string $model = null): float
    {
        if (!$model) {
            return 0.0;
        }

        $modelConfig = $this->getModelConfig($model);
        $cost = 0.0;

        if (isset($usage['prompt_tokens']) && isset($modelConfig['input_cost_per_1m_tokens'])) {
            $cost += ($usage['prompt_tokens'] / 1000000) * $modelConfig['input_cost_per_1m_tokens'];
        }

        if (isset($usage['completion_tokens']) && isset($modelConfig['output_cost_per_1m_tokens'])) {
            $cost += ($usage['completion_tokens'] / 1000000) * $modelConfig['output_cost_per_1m_tokens'];
        }

        return $cost;
    }

    /**
     * Build conversation context for API request.
     */
    protected function buildMessages(string $message, array $context = []): array
    {
        $messages = [];

        // Add system message if provided
        if (isset($context['system_message'])) {
            $messages[] = [
                'role' => 'system',
                'content' => $context['system_message'],
            ];
        }

        // Add conversation history
        if (isset($context['history']) && is_array($context['history'])) {
            foreach ($context['history'] as $historyMessage) {
                $messages[] = [
                    'role' => $historyMessage['role'] ?? 'user',
                    'content' => $historyMessage['content'] ?? '',
                ];
            }
        }

        // Add current message
        $messages[] = [
            'role' => 'user',
            'content' => $message,
        ];

        return $messages;
    }

    /**
     * Extract response content from API response.
     */
    protected function extractContent(array $response): string
    {
        // This method should be overridden by each provider
        return $response['content'] ?? '';
    }

    /**
     * Get available models for this provider.
     */
    public function getAvailableModels(): array
    {
        return array_keys($this->config['models'] ?? []);
    }

    /**
     * Get provider name.
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * Check if the provider supports a specific feature.
     */
    public function supportsFeature(string $feature, string $model = null): bool
    {
        if ($model) {
            $modelConfig = $this->getModelConfig($model);
            return $modelConfig["supports_{$feature}"] ?? false;
        }

        // Check if any model supports the feature
        foreach ($this->config['models'] ?? [] as $modelConfig) {
            if ($modelConfig["supports_{$feature}"] ?? false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get usage information for the last request.
     */
    public function getLastUsage(): array
    {
        return $this->lastUsage;
    }

    /**
     * Validate API configuration.
     */
    public function validateConfiguration(): bool
    {
        return !empty($this->config['api_key']) && !empty($this->config['base_url']);
    }

    /**
     * Get cost estimate for a request.
     */
    public function estimateCost(string $message, array $context = [], string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        $modelConfig = $this->getModelConfig($model);

        // Rough token estimation (4 characters per token average)
        $inputTokens = strlen($message . json_encode($context)) / 4;
        $estimatedOutputTokens = min($inputTokens * 0.5, 1000); // Estimate 50% of input, max 1000

        $inputCost = ($inputTokens / 1000000) * ($modelConfig['input_cost_per_1m_tokens'] ?? 0);
        $outputCost = ($estimatedOutputTokens / 1000000) * ($modelConfig['output_cost_per_1m_tokens'] ?? 0);

        return [
            'estimated_input_tokens' => (int) $inputTokens,
            'estimated_output_tokens' => (int) $estimatedOutputTokens,
            'estimated_total_tokens' => (int) ($inputTokens + $estimatedOutputTokens),
            'estimated_input_cost' => round($inputCost, 6),
            'estimated_output_cost' => round($outputCost, 6),
            'estimated_total_cost' => round($inputCost + $outputCost, 6),
            'currency' => 'USD',
            'model' => $model,
            'provider' => $this->name,
        ];
    }

    /**
     * Default implementation for features that may not be supported by all providers.
     */
    public function generateStreamingResponse(string $message, array $context = [], string $model = null, callable $callback = null): array
    {
        // Default implementation falls back to regular response
        return $this->generateResponse($message, $context, $model);
    }

    public function analyzeImage(string $imageUrl, string $prompt = null, string $model = null): array
    {
        throw new Exception("Image analysis not supported by this provider");
    }

    public function generateFunctionCalls(string $message, array $availableFunctions = [], array $context = [], string $model = null): array
    {
        throw new Exception("Function calling not supported by this provider");
    }
}
