<?php
// Test multiple API endpoints
$endpoints = [
    'Cart Count' => 'http://localhost:8000/api/cart/count',
    'Chat AI Providers' => 'http://localhost:8000/api/v1/chat/ai/providers',
    'Health Check' => 'http://localhost:8000/admin/health',
];

foreach ($endpoints as $name => $url) {
    echo "\n=== Testing $name ===\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_NOBODY, false);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "cURL Error: " . $error . "\n";
} else {
    echo "HTTP Status Code: " . $httpCode . "\n";
    if ($httpCode == 200) {
        echo "✅ Cart API is working correctly!\n";
        echo "Response:\n" . $response . "\n";
    } else {
        echo "❌ $name returned error code: " . $httpCode . "\n";
        echo "Response:\n" . $response . "\n";
    }
}
}
?>
