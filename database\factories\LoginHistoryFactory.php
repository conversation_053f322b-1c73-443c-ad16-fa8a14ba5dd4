<?php

namespace Database\Factories;

use App\Models\LoginHistory;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LoginHistory>
 */
class LoginHistoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = LoginHistory::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $loginStatus = $this->faker->randomElement(['success', 'failed']);
        $deviceTypes = ['mobile', 'desktop', 'tablet'];
        $browsers = ['Chrome', 'Firefox', 'Safari', 'Edge'];
        $operatingSystems = ['Windows', 'macOS', 'Linux', 'iOS', 'Android'];
        
        return [
            'uuid' => Str::uuid(),
            'user_id' => User::factory(),
            'login_status' => $loginStatus,
            'ip_address' => $this->faker->ipv4(),
            'location' => $this->faker->city() . ', ' . $this->faker->country(),
            'latitude' => $this->faker->latitude(),
            'longitude' => $this->faker->longitude(),
            'device_info' => [
                'browser' => $this->faker->randomElement($browsers),
                'browser_version' => $this->faker->numerify('#.#.#'),
                'platform' => $this->faker->randomElement($operatingSystems),
                'device' => $this->faker->randomElement(['iPhone', 'Samsung Galaxy', 'MacBook Pro', 'Dell Laptop']),
                'is_mobile' => $this->faker->boolean(30),
                'is_tablet' => $this->faker->boolean(10),
                'is_desktop' => $this->faker->boolean(60),
                'is_robot' => false,
                'languages' => ['en-US', 'en'],
                'user_agent' => $this->faker->userAgent(),
            ],
            'device_fingerprint' => hash('sha256', $this->faker->uuid()),
            'user_agent' => $this->faker->userAgent(),
            'os_info' => $this->faker->randomElement($operatingSystems) . ' ' . $this->faker->numerify('#.#'),
            'browser_info' => $this->faker->randomElement($browsers) . ' ' . $this->faker->numerify('#.#.#'),
            'device_type' => $this->faker->randomElement($deviceTypes),
            'login_method' => $this->faker->randomElement(['standard', 'oauth', '2fa']),
            'failed_attempts' => $loginStatus === 'failed' ? $this->faker->numberBetween(1, 5) : 0,
            'is_vpn' => $this->faker->boolean(5),
            'is_tor' => $this->faker->boolean(2),
            'security_alert' => $this->faker->boolean(10),
            'login_timezone' => $this->faker->timezone(),
            'login_ip_class' => $this->faker->randomElement(['private', 'public']),
            'risk_score' => $this->faker->numberBetween(0, 100),
            'is_device_known' => $this->faker->boolean(70),
            'is_location_known' => $this->faker->boolean(80),
            'is_ip_blacklisted' => $this->faker->boolean(1),
            'is_ip_whitelisted' => $this->faker->boolean(5),
            'twofa_used' => $this->faker->boolean(20),
            'twofa_code_verified' => function (array $attributes) {
                return $attributes['twofa_used'] ? $this->faker->boolean(95) : false;
            },
            'session_id' => Str::random(40),
            'referrer' => $this->faker->optional()->url(),
            'session_started_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'session_ended_at' => function (array $attributes) {
                if ($attributes['login_status'] === 'success' && $this->faker->boolean(70)) {
                    return $this->faker->dateTimeBetween($attributes['session_started_at'], 'now');
                }
                return null;
            },
            'session_duration' => function (array $attributes) {
                if ($attributes['session_ended_at']) {
                    $start = is_string($attributes['session_started_at']) 
                        ? strtotime($attributes['session_started_at'])
                        : $attributes['session_started_at']->getTimestamp();
                    $end = is_string($attributes['session_ended_at'])
                        ? strtotime($attributes['session_ended_at'])
                        : $attributes['session_ended_at']->getTimestamp();
                    return $end - $start;
                }
                return null;
            },
            'failure_reason' => $loginStatus === 'failed' 
                ? $this->faker->randomElement([
                    'Invalid credentials',
                    'Account locked',
                    'Too many attempts',
                    'Account disabled',
                    'Email not verified'
                ])
                : null,
            'security_flags' => [
                'is_vpn' => $this->faker->boolean(5),
                'is_tor' => $this->faker->boolean(2),
                'is_proxy' => $this->faker->boolean(3),
                'suspicious_headers' => [],
                'rate_limited' => $this->faker->boolean(5),
                'previous_failures' => $this->faker->numberBetween(0, 3),
            ],
            'geolocation_data' => [
                'country' => $this->faker->country(),
                'country_code' => $this->faker->countryCode(),
                'region' => $this->faker->state(),
                'region_code' => $this->faker->stateAbbr(),
                'city' => $this->faker->city(),
                'zip' => $this->faker->postcode(),
                'timezone' => $this->faker->timezone(),
                'isp' => $this->faker->company() . ' ISP',
                'org' => $this->faker->company(),
                'as' => 'AS' . $this->faker->numberBetween(1000, 99999),
            ],
            'isp' => $this->faker->company() . ' ISP',
            'organization' => $this->faker->company(),
            'is_suspicious' => function (array $attributes) {
                return $attributes['risk_score'] >= 60;
            },
            'admin_notes' => $this->faker->optional(10)->sentence(),
            'created_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'updated_at' => function (array $attributes) {
                return $attributes['created_at'];
            },
        ];
    }

    /**
     * Indicate that the login was successful.
     */
    public function successful(): static
    {
        return $this->state(fn (array $attributes) => [
            'login_status' => 'success',
            'failure_reason' => null,
            'failed_attempts' => 0,
            'risk_score' => $this->faker->numberBetween(0, 40),
        ]);
    }

    /**
     * Indicate that the login failed.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'login_status' => 'failed',
            'failure_reason' => $this->faker->randomElement([
                'Invalid credentials',
                'Account locked',
                'Too many attempts',
            ]),
            'failed_attempts' => $this->faker->numberBetween(1, 5),
            'risk_score' => $this->faker->numberBetween(30, 100),
            'session_ended_at' => null,
            'session_duration' => null,
        ]);
    }

    /**
     * Indicate that the login is suspicious.
     */
    public function suspicious(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_suspicious' => true,
            'security_alert' => true,
            'risk_score' => $this->faker->numberBetween(60, 100),
            'is_device_known' => false,
            'is_location_known' => false,
        ]);
    }

    /**
     * Indicate that the login is from a new device.
     */
    public function newDevice(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_device_known' => false,
            'risk_score' => $attributes['risk_score'] + 20,
        ]);
    }

    /**
     * Indicate that the login is from a new location.
     */
    public function newLocation(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_location_known' => false,
            'risk_score' => $attributes['risk_score'] + 15,
        ]);
    }

    /**
     * Indicate that the login used VPN.
     */
    public function withVpn(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_vpn' => true,
            'security_flags' => array_merge($attributes['security_flags'] ?? [], [
                'is_vpn' => true,
            ]),
            'risk_score' => $attributes['risk_score'] + 25,
        ]);
    }

    /**
     * Indicate that the login used Tor.
     */
    public function withTor(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_tor' => true,
            'security_flags' => array_merge($attributes['security_flags'] ?? [], [
                'is_tor' => true,
            ]),
            'risk_score' => $attributes['risk_score'] + 40,
            'is_suspicious' => true,
            'security_alert' => true,
        ]);
    }

    /**
     * Indicate that 2FA was used.
     */
    public function withTwoFa(): static
    {
        return $this->state(fn (array $attributes) => [
            'twofa_used' => true,
            'twofa_code_verified' => true,
            'login_method' => '2fa',
            'risk_score' => max(0, $attributes['risk_score'] - 10), // Lower risk with 2FA
        ]);
    }

    /**
     * Indicate that the login is from a mobile device.
     */
    public function mobile(): static
    {
        return $this->state(fn (array $attributes) => [
            'device_type' => 'mobile',
            'device_info' => array_merge($attributes['device_info'] ?? [], [
                'is_mobile' => true,
                'is_tablet' => false,
                'is_desktop' => false,
                'device' => $this->faker->randomElement(['iPhone 13', 'Samsung Galaxy S21', 'Google Pixel 6']),
                'platform' => $this->faker->randomElement(['iOS', 'Android']),
            ]),
        ]);
    }

    /**
     * Indicate that the login is recent.
     */
    public function recent(): static
    {
        return $this->state(fn (array $attributes) => [
            'created_at' => $this->faker->dateTimeBetween('-24 hours', 'now'),
            'session_started_at' => $this->faker->dateTimeBetween('-24 hours', 'now'),
        ]);
    }
}
