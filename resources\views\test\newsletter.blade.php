@extends('layouts.app')

@section('title', 'Newsletter Test')

@section('content')
<div class="min-h-screen bg-gray-50 py-12">
    <div class="container mx-auto px-4">
        <div class="max-w-2xl mx-auto">
            <h1 class="text-3xl font-bold text-center mb-8">Newsletter Functionality Test</h1>
            
            <!-- Newsletter Subscription Test -->
            <div class="bg-white rounded-lg shadow-md p-8 mb-8">
                <h2 class="text-xl font-semibold mb-4">Newsletter Subscription (AJAX)</h2>
                
                <!-- Success/Error Messages -->
                <div id="test-newsletter-message" class="mb-4 hidden">
                    <div id="test-newsletter-message-content" class="p-4 rounded-lg"></div>
                </div>

                <form id="test-newsletter-form" action="{{ route('newsletter.subscribe') }}" method="POST" class="space-y-4">
                    @csrf
                    <div>
                        <label for="test_email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                        <input type="email" id="test_email" name="email" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="Enter your email address">
                    </div>
                    <div>
                        <label for="test_name" class="block text-sm font-medium text-gray-700 mb-2">Name (Optional)</label>
                        <input type="text" id="test_name" name="name"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="Your name">
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="test_consent" name="consent" required
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="test_consent" class="ml-2 block text-sm text-gray-700">
                            I agree to receive marketing emails and can unsubscribe at any time.
                        </label>
                    </div>
                    <button type="submit" id="test-newsletter-submit" class="w-full bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                        Subscribe to Newsletter
                    </button>
                </form>
            </div>

            <!-- Newsletter Unsubscription Test -->
            <div class="bg-white rounded-lg shadow-md p-8 mb-8">
                <h2 class="text-xl font-semibold mb-4">Newsletter Unsubscription (AJAX)</h2>
                
                <!-- Success/Error Messages -->
                <div id="test-unsubscribe-message" class="mb-4 hidden">
                    <div id="test-unsubscribe-message-content" class="p-4 rounded-lg"></div>
                </div>

                <form id="test-unsubscribe-form" action="{{ route('newsletter.unsubscribe') }}" method="POST" class="space-y-4">
                    @csrf
                    <div>
                        <label for="unsub_email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                        <input type="email" id="unsub_email" name="email" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                               placeholder="Enter email to unsubscribe">
                    </div>
                    <button type="submit" id="test-unsubscribe-submit" class="w-full bg-red-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-red-700 transition-colors">
                        Unsubscribe from Newsletter
                    </button>
                </form>
            </div>

            <!-- Links -->
            <div class="text-center space-y-4">
                <div>
                    <a href="{{ route('newsletter.unsubscribe.form') }}" class="text-blue-600 hover:text-blue-800 underline">
                        Visit Unsubscribe Page
                    </a>
                </div>
                <div>
                    <a href="{{ route('home') }}" class="text-gray-600 hover:text-gray-800 underline">
                        Back to Home
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Newsletter subscription form
    const testNewsletterForm = document.getElementById('test-newsletter-form');
    const testNewsletterMessage = document.getElementById('test-newsletter-message');
    const testNewsletterMessageContent = document.getElementById('test-newsletter-message-content');
    
    if (testNewsletterForm) {
        testNewsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = this.querySelector('input[name="email"]').value;
            const consent = this.querySelector('input[name="consent"]').checked;
            const submitBtn = document.getElementById('test-newsletter-submit');
            const originalText = submitBtn.textContent;

            // Basic validation
            if (!email || !consent) {
                showTestNewsletterMessage('Please fill in your email and agree to receive marketing emails.', 'error');
                return;
            }

            // Show loading state
            submitBtn.innerHTML = `
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Subscribing...
            `;
            submitBtn.disabled = true;
            
            // Hide previous messages
            testNewsletterMessage.classList.add('hidden');
            
            // Submit form via AJAX
            fetch(this.action, {
                method: 'POST',
                body: new FormData(this),
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showTestNewsletterMessage(data.message, 'success');
                    // Clear form on success
                    this.reset();
                } else {
                    let errorMessage = data.message || 'An error occurred. Please try again.';
                    
                    // Show field errors if any
                    if (data.errors) {
                        const errorList = Object.values(data.errors).flat();
                        if (errorList.length > 0) {
                            errorMessage += ' ' + errorList.join(' ');
                        }
                    }
                    
                    showTestNewsletterMessage(errorMessage, data.type === 'info' ? 'info' : 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showTestNewsletterMessage('An unexpected error occurred. Please try again later.', 'error');
            })
            .finally(() => {
                // Restore button
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        });
    }
    
    function showTestNewsletterMessage(message, type) {
        testNewsletterMessage.classList.remove('hidden');
        
        // Remove existing classes
        testNewsletterMessageContent.className = 'p-4 rounded-lg';
        
        // Add appropriate classes based on type
        if (type === 'success') {
            testNewsletterMessageContent.classList.add('bg-green-100', 'border', 'border-green-400', 'text-green-700');
        } else if (type === 'info') {
            testNewsletterMessageContent.classList.add('bg-blue-100', 'border', 'border-blue-400', 'text-blue-700');
        } else {
            testNewsletterMessageContent.classList.add('bg-red-100', 'border', 'border-red-400', 'text-red-700');
        }
        
        testNewsletterMessageContent.textContent = message;
        
        // Auto-hide success messages after 5 seconds
        if (type === 'success') {
            setTimeout(() => {
                testNewsletterMessage.classList.add('hidden');
            }, 5000);
        }
    }

    // Newsletter unsubscription form
    const testUnsubscribeForm = document.getElementById('test-unsubscribe-form');
    const testUnsubscribeMessage = document.getElementById('test-unsubscribe-message');
    const testUnsubscribeMessageContent = document.getElementById('test-unsubscribe-message-content');
    
    if (testUnsubscribeForm) {
        testUnsubscribeForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = this.querySelector('input[name="email"]').value;
            const submitBtn = document.getElementById('test-unsubscribe-submit');
            const originalText = submitBtn.textContent;

            // Basic validation
            if (!email) {
                showTestUnsubscribeMessage('Please enter an email address.', 'error');
                return;
            }

            // Show loading state
            submitBtn.innerHTML = `
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Unsubscribing...
            `;
            submitBtn.disabled = true;
            
            // Hide previous messages
            testUnsubscribeMessage.classList.add('hidden');
            
            // Submit form via AJAX
            fetch(this.action, {
                method: 'POST',
                body: new FormData(this),
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showTestUnsubscribeMessage(data.message, 'success');
                    // Clear form on success
                    this.reset();
                } else {
                    let errorMessage = data.message || 'An error occurred. Please try again.';
                    
                    // Show field errors if any
                    if (data.errors) {
                        const errorList = Object.values(data.errors).flat();
                        if (errorList.length > 0) {
                            errorMessage += ' ' + errorList.join(' ');
                        }
                    }
                    
                    showTestUnsubscribeMessage(errorMessage, data.type === 'info' ? 'info' : 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showTestUnsubscribeMessage('An unexpected error occurred. Please try again later.', 'error');
            })
            .finally(() => {
                // Restore button
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        });
    }
    
    function showTestUnsubscribeMessage(message, type) {
        testUnsubscribeMessage.classList.remove('hidden');
        
        // Remove existing classes
        testUnsubscribeMessageContent.className = 'p-4 rounded-lg';
        
        // Add appropriate classes based on type
        if (type === 'success') {
            testUnsubscribeMessageContent.classList.add('bg-green-100', 'border', 'border-green-400', 'text-green-700');
        } else if (type === 'info') {
            testUnsubscribeMessageContent.classList.add('bg-blue-100', 'border', 'border-blue-400', 'text-blue-700');
        } else {
            testUnsubscribeMessageContent.classList.add('bg-red-100', 'border', 'border-red-400', 'text-red-700');
        }
        
        testUnsubscribeMessageContent.textContent = message;
        
        // Auto-hide success messages after 5 seconds
        if (type === 'success') {
            setTimeout(() => {
                testUnsubscribeMessage.classList.add('hidden');
            }, 5000);
        }
    }
});
</script>
@endsection
