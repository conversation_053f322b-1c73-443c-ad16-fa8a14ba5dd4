@extends('layouts.dashboard')

@section('title', 'Chat Analytics Dashboard')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Chat Analytics Dashboard</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="#">Chat</a></li>
                        <li class="breadcrumb-item active">Analytics</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Real-time Metrics Row -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate" title="Active Conversations">Active Conversations</h5>
                            <h3 class="my-2 py-1">{{ $metrics['real_time']['active_conversations'] ?? 0 }}</h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-chat-processing text-success" style="font-size: 24px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate" title="Waiting Customers">Waiting Customers</h5>
                            <h3 class="my-2 py-1">{{ $metrics['real_time']['waiting_customers'] ?? 0 }}</h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-account-clock text-warning" style="font-size: 24px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate" title="Online Staff">Online Staff</h5>
                            <h3 class="my-2 py-1">{{ $metrics['real_time']['online_staff'] ?? 0 }}</h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-account-check text-info" style="font-size: 24px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate" title="Avg Response Time">Avg Response Time</h5>
                            <h3 class="my-2 py-1">{{ number_format($metrics['real_time']['avg_response_time'] ?? 0, 1) }}s</h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-timer text-primary" style="font-size: 24px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Metrics Row -->
    <div class="row">
        <div class="col-xl-8">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Conversation Trends</h4>
                    <div id="conversation-trends-chart" style="height: 300px;"></div>
                </div>
            </div>
        </div>

        <div class="col-xl-4">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Today's Summary</h4>
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="text-primary">{{ $metrics['today']['total_conversations'] ?? 0 }}</h3>
                                <p class="text-muted mb-2">Conversations</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="text-success">{{ $metrics['today']['total_messages'] ?? 0 }}</h3>
                                <p class="text-muted mb-2">Messages</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="text-info">{{ $metrics['today']['unique_visitors'] ?? 0 }}</h3>
                                <p class="text-muted mb-2">Visitors</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="text-warning">{{ number_format($metrics['today']['escalation_rate'] ?? 0, 1) }}%</h3>
                                <p class="text-muted mb-2">Escalation Rate</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Staff Performance Row -->
    <div class="row">
        <div class="col-xl-8">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Staff Performance</h4>
                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Staff Member</th>
                                    <th>Conversations</th>
                                    <th>Messages</th>
                                    <th>Avg Response Time</th>
                                    <th>Online Hours</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($staffPerformance as $staff)
                                <tr>
                                    <td>{{ $staff['name'] }}</td>
                                    <td>{{ $staff['conversations_handled'] }}</td>
                                    <td>{{ $staff['messages_sent'] }}</td>
                                    <td>{{ number_format($staff['avg_response_time'], 1) }}s</td>
                                    <td>{{ number_format($staff['online_hours'], 1) }}h</td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="text-center text-muted">No staff performance data available</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Customer Satisfaction</h4>
                    <div class="text-center">
                        <h2 class="text-primary mb-2">{{ number_format($customerSatisfaction['average_rating'] ?? 0, 1) }}/5</h2>
                        <p class="text-muted">Average Rating</p>
                        <p class="mb-0">Based on {{ $customerSatisfaction['total_ratings'] ?? 0 }} ratings</p>
                    </div>
                    
                    <div class="mt-4">
                        <h6 class="mb-3">Rating Distribution</h6>
                        @if(isset($customerSatisfaction['rating_distribution']))
                            @foreach($customerSatisfaction['rating_distribution'] as $rating => $count)
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>{{ $rating }} Stars</span>
                                <span class="badge bg-primary">{{ $count }}</span>
                            </div>
                            @endforeach
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export and Actions Row -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Export & Actions</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-primary me-2" onclick="exportReport('json')">
                                <i class="mdi mdi-download"></i> Export JSON
                            </button>
                            <button type="button" class="btn btn-success me-2" onclick="exportReport('csv')">
                                <i class="mdi mdi-file-excel"></i> Export CSV
                            </button>
                            <button type="button" class="btn btn-danger" onclick="exportReport('pdf')">
                                <i class="mdi mdi-file-pdf"></i> Export PDF
                            </button>
                        </div>
                        <div class="col-md-6 text-end">
                            <button type="button" class="btn btn-info" onclick="refreshMetrics()">
                                <i class="mdi mdi-refresh"></i> Refresh Data
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh real-time metrics every 30 seconds
setInterval(function() {
    refreshMetrics();
}, 30000);

function refreshMetrics() {
    fetch('{{ route("admin.chat.analytics.metrics.realtime") }}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update real-time metrics
                console.log('Metrics refreshed:', data.data);
                // You can update the DOM elements here
            }
        })
        .catch(error => console.error('Error refreshing metrics:', error));
}

function exportReport(format) {
    fetch('{{ route("admin.chat.analytics.export") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            format: format,
            period: 'month'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.download_url) {
                window.open(data.download_url, '_blank');
            } else {
                // Handle JSON export
                const blob = new Blob([JSON.stringify(data.data, null, 2)], {type: 'application/json'});
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `chat-analytics-${format}-${new Date().toISOString().split('T')[0]}.${format}`;
                a.click();
                window.URL.revokeObjectURL(url);
            }
        }
    })
    .catch(error => console.error('Error exporting report:', error));
}
</script>
@endsection
