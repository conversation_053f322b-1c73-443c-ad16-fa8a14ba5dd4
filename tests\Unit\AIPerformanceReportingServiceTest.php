<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\AiConversationLog;
use App\Services\AIPerformanceReportingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use PHPUnit\Framework\Attributes\Test;

class AIPerformanceReportingServiceTest extends TestCase
{
    use RefreshDatabase;

    protected AIPerformanceReportingService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new AIPerformanceReportingService();

        // Create required related models for foreign key constraints
        $this->createRequiredModels();
    }

    private function createRequiredModels(): void
    {
        // Create a chat room and message that can be referenced by AI logs
        $chatRoom = \App\Models\ChatRoom::factory()->create();
        $chatMessage = \App\Models\ChatMessage::factory()->create(['chat_room_id' => $chatRoom->id]);
    }

    #[Test]
    public function it_calculates_performance_summary_correctly()
    {
        // Clear any existing data first
        AiConversationLog::truncate();

        // Create test data
        $startDate = Carbon::now()->subDays(7);
        $endDate = Carbon::now();

        // Create AI conversation logs with known data
        AiConversationLog::factory()->count(80)->create([
            'escalated_to_human' => false,
            'confidence_score' => 0.85,
            'processing_time_ms' => 1500,
            'created_at' => $startDate->copy()->addDays(1),
        ]);

        AiConversationLog::factory()->count(20)->create([
            'escalated_to_human' => true,
            'confidence_score' => 0.45,
            'processing_time_ms' => 2000,
            'created_at' => $startDate->copy()->addDays(2),
        ]);

        $summary = $this->service->getPerformanceReport(['start_date' => $startDate, 'end_date' => $endDate]);

        $this->assertArrayHasKey('summary', $summary);
        $this->assertEquals(100, $summary['summary']['total_interactions']);
        $this->assertEquals(80.0, $summary['summary']['success_rate']);
        $this->assertEquals(20.0, $summary['summary']['escalation_rate']);
        $this->assertGreaterThan(0, $summary['summary']['avg_confidence_score']);
        $this->assertGreaterThan(0, $summary['summary']['avg_response_time_ms']);
    }

    #[Test]
    public function it_handles_empty_data_gracefully()
    {
        // No data created - test with empty database
        $summary = $this->service->getPerformanceReport();

        $this->assertArrayHasKey('summary', $summary);
        $this->assertEquals(0, $summary['summary']['total_interactions']);
        $this->assertEquals(0, $summary['summary']['success_rate']);
        $this->assertEquals(0, $summary['summary']['escalation_rate']);
        $this->assertEquals(0, $summary['summary']['avg_confidence_score']);
        $this->assertEquals(0, $summary['summary']['avg_response_time_ms']);
    }

    #[Test]
    public function it_applies_model_filter_correctly()
    {
        // Create data for different models
        AiConversationLog::factory()->count(30)->create(['model_used' => 'gpt-4']);
        AiConversationLog::factory()->count(20)->create(['model_used' => 'gpt-3.5-turbo']);

        $filters = ['model' => 'gpt-4'];
        $report = $this->service->getPerformanceReport($filters);

        $this->assertArrayHasKey('summary', $report);
        $this->assertEquals(30, $report['summary']['total_interactions']);

        // Test that only gpt-4 data is included
        $allModelsReport = $this->service->getPerformanceReport();
        $this->assertEquals(50, $allModelsReport['summary']['total_interactions']);
    }

    #[Test]
    public function it_calculates_response_time_percentiles_correctly()
    {
        // Create test data with known response times
        $responseTimes = [100, 200, 300, 400, 500, 600, 700, 800, 900, 1000];

        foreach ($responseTimes as $time) {
            AiConversationLog::factory()->create(['processing_time_ms' => $time]);
        }

        $report = $this->service->getPerformanceReport();

        $this->assertArrayHasKey('response_time_analysis', $report);
        $responseTimeData = $report['response_time_analysis'];

        $this->assertEquals(550, $responseTimeData['avg_response_time']); // Average of 100-1000
        $this->assertEquals(550, $responseTimeData['median_response_time']); // Median of 10 numbers
        $this->assertEquals(100, $responseTimeData['min_response_time']);
        $this->assertEquals(1000, $responseTimeData['max_response_time']);
    }

    #[Test]
    public function it_categorizes_confidence_scores_correctly()
    {
        // Clear any existing data first
        AiConversationLog::truncate();

        $confidenceScores = [0.1, 0.3, 0.6, 0.8, 0.95];

        foreach ($confidenceScores as $score) {
            AiConversationLog::factory()->create(['confidence_score' => $score]);
        }

        $report = $this->service->getPerformanceReport();

        $this->assertArrayHasKey('confidence_analysis', $report);
        $confidenceData = $report['confidence_analysis'];

        // Calculate expected average: (0.1 + 0.3 + 0.6 + 0.8 + 0.95) / 5 = 2.75 / 5 = 0.55
        $this->assertEquals(0.55, $confidenceData['avg_confidence']); // Corrected expected value
        $this->assertEquals(40.0, $confidenceData['low_confidence_rate']); // 2 out of 5 scores < 0.5

        $distribution = $confidenceData['confidence_distribution'];
        $this->assertEquals(1, $distribution['very_high']); // 0.95
        $this->assertEquals(1, $distribution['high']); // 0.8
        $this->assertEquals(1, $distribution['medium']); // 0.6
        $this->assertEquals(2, $distribution['low']); // 0.1, 0.3
    }

    #[Test]
    public function it_handles_export_functionality()
    {
        // Create some test data
        AiConversationLog::factory()->count(10)->create();

        $filters = ['start_date' => Carbon::now()->subDays(7), 'end_date' => Carbon::now()];
        $exportData = $this->service->exportReport($filters, 'json');

        $this->assertArrayHasKey('report_generated_at', $exportData);
        $this->assertArrayHasKey('filters_applied', $exportData);
        $this->assertArrayHasKey('format', $exportData);
        $this->assertArrayHasKey('data', $exportData);
        $this->assertEquals('json', $exportData['format']);
        $this->assertEquals($filters, $exportData['filters_applied']);
    }

    #[Test]
    public function it_validates_date_range_filters()
    {
        $startDate = Carbon::now()->subDays(30);
        $endDate = Carbon::now();

        // Create data outside the date range
        AiConversationLog::factory()->create(['created_at' => $startDate->copy()->subDays(5)]);

        // Create data inside the date range
        AiConversationLog::factory()->count(5)->create(['created_at' => $startDate->copy()->addDays(1)]);

        $filters = ['start_date' => $startDate, 'end_date' => $endDate];
        $report = $this->service->getPerformanceReport($filters);

        $this->assertEquals($startDate->toDateString(), $report['summary']['period_start']);
        $this->assertEquals($endDate->toDateString(), $report['summary']['period_end']);
        $this->assertEquals(5, $report['summary']['total_interactions']); // Only data within range
    }

    #[Test]
    public function it_handles_language_filter_when_column_missing()
    {
        // Create some test data
        AiConversationLog::factory()->count(5)->create();

        // Test with language filter (detected_language column doesn't exist in our schema)
        $filters = ['language' => 'es'];
        $report = $this->service->getPerformanceReport($filters);

        // Should handle gracefully even if column doesn't exist
        $this->assertArrayHasKey('summary', $report);
        $this->assertIsInt($report['summary']['total_interactions']);
    }

    #[Test]
    public function it_calculates_success_rate_with_division_by_zero_protection()
    {
        // Test with no data to ensure division by zero protection
        $report = $this->service->getPerformanceReport();

        $this->assertEquals(0, $report['summary']['success_rate']);
        $this->assertEquals(0, $report['summary']['escalation_rate']);
        $this->assertEquals(0, $report['summary']['total_interactions']);
    }

    #[Test]
    public function it_provides_comprehensive_report_structure()
    {
        // Create some test data to ensure all sections are populated
        AiConversationLog::factory()->count(10)->create();

        $report = $this->service->getPerformanceReport();

        // Verify all required sections are present
        $expectedSections = [
            'summary',
            'success_metrics',
            'escalation_analysis',
            'response_time_analysis',
            'confidence_analysis',
            'language_performance',
            'model_comparison',
            'trending_topics',
            'failure_analysis'
        ];

        foreach ($expectedSections as $section) {
            $this->assertArrayHasKey($section, $report, "Missing section: {$section}");
        }
    }
}
