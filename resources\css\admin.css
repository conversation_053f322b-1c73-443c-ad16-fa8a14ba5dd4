/* Professional Admin UI Components */

/* Custom Properties */
:root {
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;
    
    --secondary-50: #f8fafc;
    --secondary-100: #f1f5f9;
    --secondary-200: #e2e8f0;
    --secondary-300: #cbd5e1;
    --secondary-400: #94a3b8;
    --secondary-500: #64748b;
    --secondary-600: #475569;
    --secondary-700: #334155;
    --secondary-800: #1e293b;
    --secondary-900: #0f172a;
    
    --success-500: #10b981;
    --success-600: #059669;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --danger-500: #ef4444;
    --danger-600: #dc2626;
    
    --shadow-soft: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Dashboard Components */
.dashboard-card {
    @apply bg-white rounded-xl p-6 border border-neutral-100;
    box-shadow: var(--shadow-soft);
}

.dashboard-stat {
    @apply flex items-center justify-between;
}

.dashboard-stat-content h3 {
    @apply text-2xl font-bold text-gray-900;
}

.dashboard-stat-content p {
    @apply text-sm text-gray-600;
}

.dashboard-stat-icon {
    @apply w-12 h-12 rounded-lg flex items-center justify-center;
}

/* Activity Items */
.activity-item {
    @apply flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200;
}

.activity-icon {
    @apply w-8 h-8 rounded-full flex items-center justify-center;
}

.activity-content h4 {
    @apply text-sm font-medium text-gray-900;
}

.activity-content p {
    @apply text-xs text-gray-500;
}

/* Quick Actions */
.quick-action {
    @apply flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 text-center;
}

.quick-action-icon {
    @apply w-8 h-8 rounded-lg flex items-center justify-center mb-2;
}

.quick-action h3 {
    @apply text-sm font-medium text-gray-900 mb-1;
}

.quick-action p {
    @apply text-xs text-gray-500;
}

/* Navigation Items */
.nav-item {
    @apply flex items-center space-x-3 px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200;
    @apply text-neutral-700 hover:bg-neutral-50 hover:text-blue-700;
}

.nav-item.active {
    @apply bg-blue-100 text-blue-800;
}

.nav-item svg {
    @apply w-5 h-5;
}

/* Form Components */
.form-group {
    @apply mb-6;
}

.form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
}

.form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200;
}

.form-input:focus {
    @apply outline-none;
}

.form-select {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-white;
}

.form-textarea {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 resize-y;
}

.form-error {
    @apply text-sm text-red-600 mt-1;
}

.form-help {
    @apply text-sm text-gray-500 mt-1;
}

/* Button Components */
.btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
    @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
}

.btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
}

.btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn-warning {
    @apply bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500;
}

.btn-outline {
    @apply border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-gray-500;
}

.btn-outline-primary {
    @apply border border-blue-600 text-blue-600 bg-white hover:bg-blue-50 focus:ring-blue-500;
}

.btn-sm {
    @apply px-3 py-1.5 text-xs;
}

.btn-lg {
    @apply px-6 py-3 text-base;
}

.btn:disabled {
    @apply opacity-50 cursor-not-allowed;
}

/* Badge Components */
.badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-primary {
    @apply bg-blue-100 text-blue-800;
}

.badge-secondary {
    @apply bg-gray-100 text-gray-800;
}

.badge-success {
    @apply bg-green-100 text-green-800;
}

.badge-danger {
    @apply bg-red-100 text-red-800;
}

.badge-warning {
    @apply bg-yellow-100 text-yellow-800;
}

.badge-info {
    @apply bg-blue-100 text-blue-800;
}

/* Alert Components */
.alert {
    @apply p-4 rounded-lg border;
}

.alert-success {
    @apply bg-green-50 border-green-200 text-green-800;
}

.alert-danger {
    @apply bg-red-50 border-red-200 text-red-800;
}

.alert-warning {
    @apply bg-yellow-50 border-yellow-200 text-yellow-800;
}

.alert-info {
    @apply bg-blue-50 border-blue-200 text-blue-800;
}

/* Table Components */
.table {
    @apply w-full divide-y divide-gray-200;
}

.table thead {
    @apply bg-gray-50;
}

.table th {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.table tbody tr:nth-child(even) {
    @apply bg-gray-50;
}

.table tbody tr:hover {
    @apply bg-gray-100;
}

/* Card Components */
.card {
    @apply bg-white rounded-lg border border-gray-200;
    box-shadow: var(--shadow-soft);
}

.card-header {
    @apply px-6 py-4 border-b border-gray-200;
}

.card-body {
    @apply p-6;
}

.card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
}

/* Modal Components */
.modal-overlay {
    @apply fixed inset-0 bg-black/50 z-50;
}

.modal-container {
    @apply fixed inset-0 z-50 overflow-y-auto;
}

.modal-content {
    @apply bg-white rounded-lg max-w-lg mx-auto mt-20 relative;
    box-shadow: var(--shadow-large);
}

.modal-header {
    @apply px-6 py-4 border-b border-gray-200;
}

.modal-body {
    @apply p-6;
}

.modal-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50 flex justify-end space-x-3;
}

/* Dropdown Components */
.dropdown {
    @apply relative inline-block text-left;
}

.dropdown-menu {
    @apply absolute right-0 mt-2 w-56 rounded-lg bg-white border border-gray-200 z-10;
    box-shadow: var(--shadow-medium);
}

.dropdown-item {
    @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900;
}

.dropdown-divider {
    @apply border-t border-gray-200 my-1;
}

/* Pagination Components */
.pagination {
    @apply flex items-center justify-between;
}

.pagination-info {
    @apply text-sm text-gray-700;
}

.pagination-nav {
    @apply flex space-x-2;
}

.pagination-link {
    @apply px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700;
}

.pagination-link.active {
    @apply bg-blue-600 text-white border-blue-600;
}

.pagination-link:disabled {
    @apply opacity-50 cursor-not-allowed;
}
