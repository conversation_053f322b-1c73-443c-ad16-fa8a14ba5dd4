<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CartItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'cart_id',
        'product_id',
        'product_variant_id',
        'quantity',
        'price',
        'total',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'total' => 'decimal:2',
    ];

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($cartItem) {
            $cartItem->total = $cartItem->price * $cartItem->quantity;
        });
    }

    /**
     * Get the shopping cart that owns the cart item.
     */
    public function shoppingCart(): BelongsTo
    {
        return $this->belongsTo(ShoppingCart::class, 'cart_id');
    }

    /**
     * Get the product for the cart item.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the product variant for the cart item.
     */
    public function productVariant(): BelongsTo
    {
        return $this->belongsTo(ProductVariant::class);
    }

    /**
     * Get the item name (product or variant name).
     */
    public function getNameAttribute(): string
    {
        if ($this->productVariant) {
            return $this->productVariant->full_name;
        }

        return $this->product->name;
    }

    /**
     * Get the item image.
     */
    public function getImageAttribute(): string
    {
        if ($this->productVariant && $this->productVariant->image) {
            return $this->productVariant->image_url;
        }

        return $this->product->primary_image;
    }

    /**
     * Get the item SKU.
     */
    public function getSkuAttribute(): string
    {
        if ($this->productVariant) {
            return $this->productVariant->sku;
        }

        return $this->product->sku;
    }

    /**
     * Get the formatted price.
     */
    public function getFormattedPriceAttribute(): string
    {
        return 'R' . number_format($this->price, 2);
    }

    /**
     * Get the formatted total.
     */
    public function getFormattedTotalAttribute(): string
    {
        return 'R' . number_format($this->total, 2);
    }

    /**
     * Check if the item is available (in stock).
     */
    public function isAvailable(): bool
    {
        if ($this->productVariant) {
            return $this->productVariant->isInStock() && $this->productVariant->inventory_quantity >= $this->quantity;
        }

        return $this->product->isInStock() && 
               (!$this->product->track_inventory || $this->product->inventory_quantity >= $this->quantity);
    }

    /**
     * Get available quantity for this item.
     */
    public function getAvailableQuantity(): int
    {
        if ($this->productVariant) {
            return $this->productVariant->inventory_quantity;
        }

        if (!$this->product->track_inventory) {
            return 999; // Assume unlimited if not tracking
        }

        return $this->product->inventory_quantity;
    }

    /**
     * Update the quantity and recalculate total.
     * Uses database transactions to prevent race conditions.
     */
    public function updateQuantity(int $quantity): bool
    {
        if ($quantity <= 0) {
            return $this->delete();
        }

        return \DB::transaction(function () use ($quantity) {
            // Lock the product/variant record to prevent concurrent modifications
            if ($this->productVariant) {
                $variant = $this->productVariant()->lockForUpdate()->first();
                if (!$variant || ($variant->track_inventory && $variant->inventory_quantity < $quantity)) {
                    return false;
                }
            } else {
                $product = $this->product()->lockForUpdate()->first();
                if (!$product || ($product->track_inventory && $product->inventory_quantity < $quantity)) {
                    return false;
                }
            }

            $this->quantity = $quantity;
            $this->total = $this->price * $quantity;

            return $this->save();
        });
    }

    /**
     * Get the current price from product/variant.
     */
    public function getCurrentPrice(): float
    {
        if ($this->productVariant) {
            return $this->productVariant->price;
        }

        return $this->product->price;
    }

    /**
     * Update price to current product/variant price.
     */
    public function updatePrice(): void
    {
        $this->price = $this->getCurrentPrice();
        $this->total = $this->price * $this->quantity;
        $this->save();
    }
}
