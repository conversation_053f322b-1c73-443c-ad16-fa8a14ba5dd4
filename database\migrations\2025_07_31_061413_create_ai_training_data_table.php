<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ai_training_data', function (Blueprint $table) {
            $table->id();
            $table->string('intent', 100);
            $table->text('input_text');
            $table->text('expected_response');
            $table->char('language', 2)->default('en');
            $table->decimal('confidence_threshold', 3, 2)->default(0.80);
            $table->string('category', 50)->nullable();
            $table->json('tags')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            // Performance indexes
            $table->index(['intent', 'language'], 'idx_intent_language');
            $table->index('category', 'idx_category');
            $table->index('is_active', 'idx_active');
            
            // Full-text search index (MySQL only)
            if (DB::getDriverName() === 'mysql') {
                $table->fullText('input_text', 'idx_input_text');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ai_training_data');
    }
};
