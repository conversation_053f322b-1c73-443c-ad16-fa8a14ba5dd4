<?php

namespace App\Services;

use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class ChatSearchService
{
    /**
     * Search chat messages with full-text search capabilities.
     */
    public function searchMessages(array $filters = [], int $perPage = 20): LengthAwarePaginator
    {
        $query = ChatMessage::with(['user', 'chatRoom', 'files'])
            ->select('chat_messages.*')
            ->join('chat_rooms', 'chat_messages.chat_room_id', '=', 'chat_rooms.id');

        // Apply search filters
        $this->applyMessageFilters($query, $filters);

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortOrder = $filters['sort_order'] ?? 'desc';
        $query->orderBy("chat_messages.{$sortBy}", $sortOrder);

        return $query->paginate($perPage);
    }

    /**
     * Search chat rooms with advanced filtering.
     */
    public function searchRooms(array $filters = [], int $perPage = 20): LengthAwarePaginator
    {
        $query = ChatRoom::with(['participants.user', 'currentAssignment.assignedStaff', 'lastMessage']);

        // Apply search filters
        $this->applyRoomFilters($query, $filters);

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortOrder = $filters['sort_order'] ?? 'desc';
        $query->orderBy($sortBy, $sortOrder);

        return $query->paginate($perPage);
    }

    /**
     * Global search across messages and rooms.
     */
    public function globalSearch(string $query, array $filters = [], int $perPage = 20): array
    {
        $messageFilters = array_merge($filters, ['content' => $query]);
        $roomFilters = array_merge($filters, ['title' => $query]);

        $messages = $this->searchMessages($messageFilters, $perPage);
        $rooms = $this->searchRooms($roomFilters, $perPage);

        return [
            'messages' => $messages,
            'rooms' => $rooms,
            'total_results' => $messages->total() + $rooms->total(),
        ];
    }

    /**
     * Search for conversations by participant.
     */
    public function searchByParticipant(int $userId, array $filters = [], int $perPage = 20): LengthAwarePaginator
    {
        $query = ChatRoom::with(['participants.user', 'currentAssignment.assignedStaff', 'lastMessage'])
            ->whereHas('participants', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            });

        $this->applyRoomFilters($query, $filters);

        $sortBy = $filters['sort_by'] ?? 'updated_at';
        $sortOrder = $filters['sort_order'] ?? 'desc';
        $query->orderBy($sortBy, $sortOrder);

        return $query->paginate($perPage);
    }

    /**
     * Search for messages by AI confidence score.
     */
    public function searchAIMessages(array $filters = [], int $perPage = 20): LengthAwarePaginator
    {
        $query = ChatMessage::with(['user', 'chatRoom'])
            ->where('is_ai_generated', true);

        if (isset($filters['min_confidence'])) {
            $query->where('ai_confidence', '>=', $filters['min_confidence']);
        }

        if (isset($filters['max_confidence'])) {
            $query->where('ai_confidence', '<=', $filters['max_confidence']);
        }

        if (isset($filters['ai_model'])) {
            $query->where('ai_model', $filters['ai_model']);
        }

        $this->applyMessageFilters($query, $filters);

        $query->orderBy('ai_confidence', 'desc');

        return $query->paginate($perPage);
    }

    /**
     * Get search suggestions based on recent queries.
     */
    public function getSearchSuggestions(string $partial, int $limit = 10): array
    {
        // Simplified approach for suggestions - get words from recent messages
        $recentMessages = ChatMessage::where('content', 'LIKE', "%{$partial}%")
            ->where('created_at', '>=', now()->subDays(30))
            ->limit(100)
            ->pluck('content');

        $suggestions = [];
        foreach ($recentMessages as $content) {
            $words = explode(' ', strtolower($content));
            foreach ($words as $word) {
                $word = trim($word, '.,!?;:"()[]{}');
                if (strlen($word) > 2 && stripos($word, $partial) === 0) {
                    $suggestions[$word] = ($suggestions[$word] ?? 0) + 1;
                }
            }
        }

        arsort($suggestions);
        $suggestions = array_slice(array_keys($suggestions), 0, $limit);

        return $suggestions;
    }

    /**
     * Get search analytics and statistics.
     */
    public function getSearchAnalytics(array $filters = []): array
    {
        $startDate = $filters['start_date'] ?? now()->subDays(30);
        $endDate = $filters['end_date'] ?? now();

        $totalMessages = ChatMessage::whereBetween('created_at', [$startDate, $endDate])->count();
        $totalRooms = ChatRoom::whereBetween('created_at', [$startDate, $endDate])->count();
        $aiMessages = ChatMessage::where('is_ai_generated', true)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        // Most active users
        $activeUsers = ChatMessage::select('user_id', DB::raw('COUNT(*) as message_count'))
            ->with('user:id,name')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->whereNotNull('user_id')
            ->groupBy('user_id')
            ->orderByDesc('message_count')
            ->limit(10)
            ->get();

        // Most common words
        $commonWords = $this->getCommonWords($startDate, $endDate);

        // Room types distribution
        $roomTypes = ChatRoom::select('type', DB::raw('COUNT(*) as count'))
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('type')
            ->get();

        return [
            'total_messages' => $totalMessages,
            'total_rooms' => $totalRooms,
            'ai_messages' => $aiMessages,
            'ai_percentage' => $totalMessages > 0 ? round(($aiMessages / $totalMessages) * 100, 2) : 0,
            'active_users' => $activeUsers,
            'common_words' => $commonWords,
            'room_types' => $roomTypes,
            'period' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
            ],
        ];
    }

    /**
     * Apply message search filters.
     */
    protected function applyMessageFilters(Builder $query, array $filters): void
    {
        // Content search
        if (!empty($filters['content'])) {
            $query->where('chat_messages.content', 'LIKE', "%{$filters['content']}%");
        }

        // User filter
        if (!empty($filters['user_id'])) {
            $query->where('chat_messages.user_id', $filters['user_id']);
        }

        // Room filter
        if (!empty($filters['room_id'])) {
            $query->where('chat_messages.chat_room_id', $filters['room_id']);
        }

        // Message type filter
        if (!empty($filters['message_type'])) {
            $query->where('chat_messages.message_type', $filters['message_type']);
        }

        // AI generated filter
        if (isset($filters['is_ai_generated'])) {
            $query->where('chat_messages.is_ai_generated', $filters['is_ai_generated']);
        }

        // Date range filter
        if (!empty($filters['start_date'])) {
            $query->where('chat_messages.created_at', '>=', $filters['start_date']);
        }

        if (!empty($filters['end_date'])) {
            $query->where('chat_messages.created_at', '<=', $filters['end_date']);
        }

        // Room type filter
        if (!empty($filters['room_type'])) {
            $query->where('chat_rooms.type', $filters['room_type']);
        }

        // Room status filter
        if (!empty($filters['room_status'])) {
            $query->where('chat_rooms.status', $filters['room_status']);
        }
    }

    /**
     * Apply room search filters.
     */
    protected function applyRoomFilters(Builder $query, array $filters): void
    {
        // Title search
        if (!empty($filters['title'])) {
            $query->where('title', 'LIKE', "%{$filters['title']}%");
        }

        // Type filter
        if (!empty($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        // Status filter
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // Priority filter
        if (!empty($filters['priority'])) {
            $query->where('priority', $filters['priority']);
        }

        // Language filter
        if (!empty($filters['language'])) {
            $query->where('language', $filters['language']);
        }

        // Assigned staff filter
        if (!empty($filters['assigned_to'])) {
            $query->whereHas('currentAssignment', function ($q) use ($filters) {
                $q->where('assigned_to', $filters['assigned_to']);
            });
        }

        // Date range filter
        if (!empty($filters['start_date'])) {
            $query->where('created_at', '>=', $filters['start_date']);
        }

        if (!empty($filters['end_date'])) {
            $query->where('created_at', '<=', $filters['end_date']);
        }

        // Visitor info search
        if (!empty($filters['visitor_name'])) {
            $query->whereRaw("JSON_EXTRACT(visitor_info, '$.name') LIKE ?", ["%{$filters['visitor_name']}%"]);
        }

        if (!empty($filters['visitor_email'])) {
            $query->whereRaw("JSON_EXTRACT(visitor_info, '$.email') LIKE ?", ["%{$filters['visitor_email']}%"]);
        }
    }

    /**
     * Get most common words from messages.
     */
    protected function getCommonWords($startDate, $endDate, int $limit = 20): array
    {
        // This is a simplified version - in production, you might want to use
        // a more sophisticated text analysis library or search engine
        $messages = ChatMessage::select('content')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->where('is_ai_generated', false) // Exclude AI messages for more authentic data
            ->get();

        $wordCounts = [];
        $stopWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'this', 'that', 'these', 'those'];

        foreach ($messages as $message) {
            $words = str_word_count(strtolower($message->content), 1);
            foreach ($words as $word) {
                if (strlen($word) > 2 && !in_array($word, $stopWords)) {
                    $wordCounts[$word] = ($wordCounts[$word] ?? 0) + 1;
                }
            }
        }

        arsort($wordCounts);
        return array_slice($wordCounts, 0, $limit, true);
    }
}
