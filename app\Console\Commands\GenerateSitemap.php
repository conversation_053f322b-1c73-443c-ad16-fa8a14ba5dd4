<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;
use App\Models\Product;
use App\Models\Service;
use App\Models\BlogPost;
use App\Models\Project;
use Carbon\Carbon;

class GenerateSitemap extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'seo:generate-sitemap {--force : Force regeneration even if file exists}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate comprehensive sitemap.xml with all dynamic content';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🗺️ Generating comprehensive sitemap...');

        $sitemap = Sitemap::create();

        // Get supported locales
        $locales = ['en', 'es', 'fr']; // English, Spanish, French

        // Add static pages for all locales
        $this->addStaticPages($sitemap, $locales);

        // Add dynamic content for all locales
        $this->addProducts($sitemap, $locales);
        $this->addServices($sitemap, $locales);
        $this->addBlogPosts($sitemap, $locales);
        $this->addProjects($sitemap, $locales);

        // Write sitemap to file
        $sitemapPath = public_path('sitemap.xml');
        $sitemap->writeToFile($sitemapPath);

        $this->info("✅ Sitemap generated successfully at: {$sitemapPath}");
        $this->info("📊 Total URLs: " . count($sitemap->getTags()));

        return Command::SUCCESS;
    }

    /**
     * Add static pages to sitemap
     */
    protected function addStaticPages(Sitemap $sitemap, array $locales): void
    {
        $staticPages = [
            ['path' => '', 'priority' => 1.0, 'changefreq' => 'weekly'], // Home page
            ['path' => 'about', 'priority' => 0.8, 'changefreq' => 'monthly'],
            ['path' => 'services', 'priority' => 0.9, 'changefreq' => 'weekly'],
            ['path' => 'projects', 'priority' => 0.8, 'changefreq' => 'weekly'], // Portfolio
            ['path' => 'blog', 'priority' => 0.7, 'changefreq' => 'daily'],
            ['path' => 'shop', 'priority' => 0.8, 'changefreq' => 'daily'],
            ['path' => 'contact', 'priority' => 0.6, 'changefreq' => 'monthly'],
            ['path' => 'careers', 'priority' => 0.5, 'changefreq' => 'monthly'],
            ['path' => 'apply', 'priority' => 0.4, 'changefreq' => 'monthly'],
            ['path' => 'cart', 'priority' => 0.3, 'changefreq' => 'hourly'],
            ['path' => 'checkout', 'priority' => 0.3, 'changefreq' => 'hourly'],
            // Service pages
            ['path' => 'services/ai-services', 'priority' => 0.8, 'changefreq' => 'monthly'],
            ['path' => 'services/web-development', 'priority' => 0.8, 'changefreq' => 'monthly'],
            ['path' => 'services/ecommerce-development', 'priority' => 0.8, 'changefreq' => 'monthly'],
            ['path' => 'services/mobile-app-development', 'priority' => 0.8, 'changefreq' => 'monthly'],
            ['path' => 'services/digital-marketing', 'priority' => 0.8, 'changefreq' => 'monthly'],
            ['path' => 'services/seo-services', 'priority' => 0.8, 'changefreq' => 'monthly'],
            ['path' => 'services/data-analytics', 'priority' => 0.8, 'changefreq' => 'monthly'],
            ['path' => 'services/accounting-services', 'priority' => 0.8, 'changefreq' => 'monthly'],
            ['path' => 'services/maintenance-support', 'priority' => 0.7, 'changefreq' => 'monthly'],
        ];

        $totalAdded = 0;

        foreach ($locales as $locale) {
            foreach ($staticPages as $page) {
                $url = $page['path'] ? "/{$locale}/{$page['path']}" : "/{$locale}";

                $sitemap->add(
                    Url::create($url)
                        ->setPriority($page['priority'])
                        ->setChangeFrequency($page['changefreq'])
                        ->setLastModificationDate(Carbon::now())
                );
                $totalAdded++;
            }
        }

        $this->info("✓ Added {$totalAdded} static pages across " . count($locales) . " locales");
    }

    /**
     * Add products to sitemap
     */
    protected function addProducts(Sitemap $sitemap, array $locales): void
    {
        if (!class_exists(Product::class)) {
            return;
        }

        $products = Product::where('is_active', true)
            ->where('is_deleted', false)
            ->select(['slug', 'updated_at'])
            ->get();

        $totalAdded = 0;

        foreach ($locales as $locale) {
            foreach ($products as $product) {
                $sitemap->add(
                    Url::create("/{$locale}/shop/product/{$product->slug}")
                        ->setPriority(0.7)
                        ->setChangeFrequency('weekly')
                        ->setLastModificationDate($product->updated_at)
                );
                $totalAdded++;
            }
        }

        $this->info("✓ Added {$totalAdded} product URLs across " . count($locales) . " locales");
    }

    /**
     * Add services to sitemap
     */
    protected function addServices(Sitemap $sitemap, array $locales): void
    {
        if (!class_exists(Service::class)) {
            return;
        }

        $services = Service::where('is_active', true)
            ->select(['slug', 'updated_at'])
            ->get();

        $totalAdded = 0;

        foreach ($locales as $locale) {
            foreach ($services as $service) {
                $sitemap->add(
                    Url::create("/{$locale}/services/{$service->slug}")
                        ->setPriority(0.8)
                        ->setChangeFrequency('monthly')
                        ->setLastModificationDate($service->updated_at)
                );
                $totalAdded++;
            }
        }

        $this->info("✓ Added {$totalAdded} service URLs across " . count($locales) . " locales");
    }

    /**
     * Add blog posts to sitemap
     */
    protected function addBlogPosts(Sitemap $sitemap, array $locales): void
    {
        if (!class_exists(BlogPost::class)) {
            return;
        }

        $posts = BlogPost::where('is_published', true)
            ->where('published_at', '<=', now())
            ->select(['slug', 'updated_at'])
            ->get();

        $totalAdded = 0;

        foreach ($locales as $locale) {
            foreach ($posts as $post) {
                $sitemap->add(
                    Url::create("/{$locale}/blog/{$post->slug}")
                        ->setPriority(0.6)
                        ->setChangeFrequency('monthly')
                        ->setLastModificationDate($post->updated_at)
                );
                $totalAdded++;
            }
        }

        $this->info("✓ Added {$totalAdded} blog post URLs across " . count($locales) . " locales");
    }

    /**
     * Add projects to sitemap
     */
    protected function addProjects(Sitemap $sitemap, array $locales): void
    {
        if (!class_exists(Project::class)) {
            return;
        }

        $projects = Project::where('is_published', true)
            ->select(['slug', 'updated_at'])
            ->get();

        $totalAdded = 0;

        foreach ($locales as $locale) {
            foreach ($projects as $project) {
                $sitemap->add(
                    Url::create("/{$locale}/projects/{$project->slug}")
                        ->setPriority(0.7)
                        ->setChangeFrequency('monthly')
                        ->setLastModificationDate($project->updated_at)
                );
                $totalAdded++;
            }
        }

        $this->info("✓ Added {$totalAdded} project URLs across " . count($locales) . " locales");
    }
}
