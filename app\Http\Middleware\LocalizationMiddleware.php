<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Language;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use App\Services\MiddlewarePerformanceMonitor;

class LocalizationMiddleware
{
    /**
     * Cache key for active languages
     */
    private const LANGUAGES_CACHE_KEY = 'active_languages';

    /**
     * Cache duration in seconds (1 hour)
     */
    private const CACHE_DURATION = 3600;

    /**
     * Static cache for languages within request lifecycle
     */
    private static $languagesCache = null;

    /**
     * Circuit breaker for database failures
     */
    private static $databaseFailureCount = 0;
    private static $lastFailureTime = null;
    private const MAX_FAILURES = 3;
    private const FAILURE_TIMEOUT = 300; // 5 minutes

    /**
     * Supported language codes (fallback if database is unavailable)
     */
    private const SUPPORTED_LANGUAGES = ['en', 'fr', 'es'];

    /**
     * Emergency circuit breaker for performance issues
     */
    private static $performanceFailures = 0;
    private static $lastPerformanceFailure = null;
    private const MAX_PERFORMANCE_FAILURES = 5;
    private const PERFORMANCE_TIMEOUT = 300; // 5 minutes

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip localization for non-essential requests
        if ($this->shouldSkipLocalization($request)) {
            return $next($request);
        }

        // Check emergency circuit breaker
        if ($this->isPerformanceCircuitBreakerOpen()) {
            App::setLocale('en'); // Emergency fallback
            \Illuminate\Support\Facades\URL::defaults(['locale' => 'en']);
            return $next($request);
        }

        // Ultra-fast locale detection with performance monitoring
        $startTime = microtime(true);

        try {
            $locale = $this->getFastLocale($request);

            // Emergency timeout check (relaxed threshold to 60ms)
            if ((microtime(true) - $startTime) > 0.06) { // 60ms emergency limit
                $this->recordPerformanceFailure();
                App::setLocale('en');
                \Illuminate\Support\Facades\URL::defaults(['locale' => 'en']);
                return $next($request);
            }

            // Set locale
            App::setLocale($locale);

            // Set default locale parameter for URL generation
            \Illuminate\Support\Facades\URL::defaults(['locale' => $locale]);

            // Lazy session update (only if different)
            if (Session::get('locale') !== $locale) {
                Session::put('locale', $locale);
            }

            // Reset performance failures on success
            $this->resetPerformanceCircuitBreaker();

        } catch (\Exception $e) {
            // Record failure and fail fast with default locale
            $this->recordPerformanceFailure();
            \Log::error('LocalizationMiddleware error', [
                'error' => $e->getMessage()
            ]);
            App::setLocale('en');
        }

        return $next($request);
    }

    /**
     * Check if localization should be skipped for this request.
     */
    private function shouldSkipLocalization(Request $request): bool
    {
        // Skip for console commands, unbooted app, or health checks
        if (app()->runningInConsole() || !app()->isBooted()) {
            return true;
        }

        $path = $request->path();

        // Ultra-fast static asset detection
        if ($this->isStaticAsset($path)) {
            return true;
        }

        // Skip API routes
        if (str_starts_with($path, 'api/')) {
            return true;
        }

        // Skip debug routes but handle admin routes specially
        $debugRoutes = ['_debugbar', 'telescope', 'horizon', 'livewire', 'debug-test', 'up'];
        foreach ($debugRoutes as $route) {
            if (str_starts_with($path, $route)) {
                return true;
            }
        }

        // Handle admin and dashboard routes specially - don't skip localization entirely
        if (str_starts_with($path, 'admin') || $path === 'dashboard') {
            // Set default locale for admin/dashboard routes
            $locale = Session::get('locale', 'en');
            App::setLocale($locale);
            \Illuminate\Support\Facades\URL::defaults(['locale' => $locale]);
            return true; // Skip the rest of localization logic but locale is set
        }

        return false;
    }

    /**
     * Ultra-fast static asset detection.
     */
    private function isStaticAsset(string $path): bool
    {
        // Check file extension directly (fastest method)
        $extension = pathinfo($path, PATHINFO_EXTENSION);

        $staticExtensions = [
            'css', 'js', 'png', 'jpg', 'jpeg', 'gif', 'svg', 'ico',
            'woff', 'woff2', 'ttf', 'eot', 'otf', 'webp', 'avif',
            'mp4', 'webm', 'pdf', 'zip', 'txt', 'xml', 'json'
        ];

        return in_array(strtolower($extension), $staticExtensions);
    }

    /**
     * Ultra-fast locale detection with aggressive caching.
     */
    private function getFastLocale(Request $request): string
    {
        // Static cache within request lifecycle (fastest possible)
        static $requestLocaleCache = [];

        $requestKey = $request->getPathInfo() . '|' . $request->get('lang', '') . '|' . Session::getId();

        if (isset($requestLocaleCache[$requestKey])) {
            return $requestLocaleCache[$requestKey];
        }

        // Create a simple cache key
        $cacheKey = 'locale_' . md5($requestKey);

        // Wrap in try/catch so cache failures don’t trigger performance breaker
        try {
            $locale = Cache::remember($cacheKey, 3600, function() use ($request) { // 1 hour cache
                return $this->detectLocale($request);
            });
        } catch (\Exception $e) {
            // Fallback without cache if cache backend is slow/unavailable
            \Log::warning('LocalizationMiddleware cache fallback', [
                'error' => $e->getMessage()
            ]);
            $locale = $this->detectLocale($request);
        }

        // Store in static cache for this request
        $requestLocaleCache[$requestKey] = $locale;

        return $locale;
    }

    /**
     * Detect locale from various sources (optimized version).
     */
    private function detectLocale(Request $request): string
    {
        // 1. URL prefix (fastest check)
        $segments = $request->segments();
        if (!empty($segments) && in_array($segments[0], self::SUPPORTED_LANGUAGES)) {
            return $segments[0];
        }

        // 2. Query parameter
        $langParam = $request->get('lang');
        if ($langParam && in_array($langParam, self::SUPPORTED_LANGUAGES)) {
            return $langParam;
        }

        // 3. Session (already loaded)
        $sessionLocale = Session::get('locale');
        if ($sessionLocale && in_array($sessionLocale, self::SUPPORTED_LANGUAGES)) {
            return $sessionLocale;
        }

        // 4. User preference (if authenticated)
        $user = $request->user();
        if ($user && isset($user->preferred_language) &&
            in_array($user->preferred_language, self::SUPPORTED_LANGUAGES)) {
            return $user->preferred_language;
        }

        // 5. Accept-Language header (simplified)
        $acceptLang = $request->header('Accept-Language');
        if ($acceptLang) {
            foreach (self::SUPPORTED_LANGUAGES as $lang) {
                if (strpos($acceptLang, $lang) !== false) {
                    return $lang;
                }
            }
        }

        // 6. Default
        return 'en';
    }

    /**
     * Get active languages with caching.
     */
    private function getActiveLanguages(): array
    {
        // Return static cache if available (within request lifecycle)
        if (self::$languagesCache !== null) {
            return self::$languagesCache;
        }

        // Try to get from cache
        $languages = Cache::get(self::LANGUAGES_CACHE_KEY);

        if ($languages === null) {
            // Check circuit breaker
            if ($this->isCircuitBreakerOpen()) {
                $languages = self::SUPPORTED_LANGUAGES;
            } else {
                try {
                    // Check if database is available and ready
                    if (!$this->isDatabaseReady()) {
                        $this->recordDatabaseFailure();
                        $languages = self::SUPPORTED_LANGUAGES;
                    } else {
                        // Fetch from database
                        $languages = Language::where('is_active', true)
                            ->pluck('code')
                            ->toArray();

                        // Cache the result only if we got data from database
                        if (!empty($languages)) {
                            Cache::put(self::LANGUAGES_CACHE_KEY, $languages, self::CACHE_DURATION);
                            $this->resetCircuitBreaker(); // Reset on success
                        } else {
                            $languages = self::SUPPORTED_LANGUAGES;
                        }
                    }
                } catch (\Exception $e) {
                    // Record failure and fallback to supported languages
                    $this->recordDatabaseFailure();
                    \Log::warning('Failed to fetch languages from database, using fallback', [
                        'error' => $e->getMessage(),
                        'failure_count' => self::$databaseFailureCount
                    ]);
                    $languages = self::SUPPORTED_LANGUAGES;
                }
            }
        }

        // Store in static cache for this request
        self::$languagesCache = $languages;

        return $languages;
    }

    /**
     * Check if language code is valid and active.
     */
    private function isValidLanguage(string $langCode, array $activeLanguages): bool
    {
        return in_array($langCode, $activeLanguages);
    }

    /**
     * Parse Accept-Language header.
     */
    private function parseAcceptLanguage(string $acceptLanguage): array
    {
        $languages = [];
        $parts = explode(',', $acceptLanguage);

        foreach ($parts as $part) {
            $part = trim($part);
            if (strpos($part, ';') !== false) {
                $part = explode(';', $part)[0];
            }
            if (strpos($part, '-') !== false) {
                $part = explode('-', $part)[0];
            }
            $languages[] = strtolower($part);
        }

        return array_unique($languages);
    }

    /**
     * Check if database is ready and accessible.
     */
    private function isDatabaseReady(): bool
    {
        try {
            // Quick database connectivity check with timeout
            \DB::connection()->getPdo();
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Check if circuit breaker is open (too many failures).
     */
    private function isCircuitBreakerOpen(): bool
    {
        if (self::$databaseFailureCount >= self::MAX_FAILURES) {
            if (self::$lastFailureTime && (time() - self::$lastFailureTime) < self::FAILURE_TIMEOUT) {
                return true;
            } else {
                // Reset after timeout
                $this->resetCircuitBreaker();
            }
        }
        return false;
    }

    /**
     * Record a database failure.
     */
    private function recordDatabaseFailure(): void
    {
        self::$databaseFailureCount++;
        self::$lastFailureTime = time();
    }

    /**
     * Reset the circuit breaker.
     */
    private function resetCircuitBreaker(): void
    {
        self::$databaseFailureCount = 0;
        self::$lastFailureTime = null;
    }

    /**
     * Clear the languages cache (useful for testing or when languages are updated).
     */
    public static function clearLanguagesCache(): void
    {
        Cache::forget(self::LANGUAGES_CACHE_KEY);
        self::$languagesCache = null;
    }

    /**
     * Generate a localized URL for the given route and locale.
     */
    public static function localizedUrl(string $routeName, array $parameters = [], string $locale = null): string
    {
        $locale = $locale ?: app()->getLocale();

        // Add locale to parameters
        $parameters['locale'] = $locale;

        return route($routeName, $parameters);
    }

    /**
     * Get the current URL with a different locale.
     */
    public static function switchLocaleUrl(string $locale): string
    {
        $request = request();
        $currentPath = $request->path();
        $supportedLocales = self::SUPPORTED_LANGUAGES;

        // Remove current locale from path if present
        $pathSegments = explode('/', $currentPath);
        if (!empty($pathSegments) && in_array($pathSegments[0], $supportedLocales)) {
            array_shift($pathSegments);
        }

        // Build new path with new locale
        $newPath = $locale . '/' . implode('/', $pathSegments);

        // Remove trailing slash if path is just locale
        $newPath = rtrim($newPath, '/');
        if (empty($newPath)) {
            $newPath = $locale;
        }

        return url($newPath) . ($request->getQueryString() ? '?' . $request->getQueryString() : '');
    }

    /**
     * Check if performance circuit breaker is open.
     */
    private function isPerformanceCircuitBreakerOpen(): bool
    {
        if (self::$performanceFailures >= self::MAX_PERFORMANCE_FAILURES) {
            if (self::$lastPerformanceFailure && (time() - self::$lastPerformanceFailure) < self::PERFORMANCE_TIMEOUT) {
                return true;
            } else {
                // Reset after timeout
                $this->resetPerformanceCircuitBreaker();
            }
        }
        return false;
    }

/**
     * Record a performance failure.
     */
    private function recordPerformanceFailure(): void
    {
        self::$performanceFailures++;
        self::$lastPerformanceFailure = time();

        // Only log every Nth failure to avoid log flooding
        if (self::$performanceFailures % 3 === 0 || self::$performanceFailures >= self::MAX_PERFORMANCE_FAILURES) {
            \Log::warning('LocalizationMiddleware performance failure recorded', [
                'failure_count' => self::$performanceFailures,
                'circuit_breaker_open' => self::$performanceFailures >= self::MAX_PERFORMANCE_FAILURES
            ]);
        }
    }

    /**
     * Reset the performance circuit breaker.
     */
    private function resetPerformanceCircuitBreaker(): void
    {
        if (self::$performanceFailures > 0) {
            \Log::info('LocalizationMiddleware performance circuit breaker reset', [
                'previous_failures' => self::$performanceFailures
            ]);
            self::$performanceFailures = 0;
            self::$lastPerformanceFailure = null;
        }
    }
}
