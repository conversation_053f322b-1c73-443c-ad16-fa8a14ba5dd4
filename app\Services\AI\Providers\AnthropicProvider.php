<?php

namespace App\Services\AI\Providers;

use Exception;

class Anthropic<PERSON>rovider extends BaseAIProvider
{
    /**
     * Validate the provider configuration.
     */
    public function validateConfiguration(): bool
    {
        if (empty($this->config['api_key'])) {
            throw new \Exception('Anthropic API key is not configured. Please set ANTHROPIC_API_KEY in your environment variables.');
        }

        if (empty($this->config['base_url'])) {
            throw new \Exception('Anthropic base URL is not configured. Please set ANTHROPIC_BASE_URL in your environment variables.');
        }

        if (!filter_var($this->config['base_url'], FILTER_VALIDATE_URL)) {
            throw new \Exception('Anthropic base URL is not a valid URL.');
        }

        return true;
    }

    /**
     * Generate a response from Anthropic Claude.
     */
    public function generateResponse(string $message, array $context = [], string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        $this->validateModel($model);

        $messages = $this->buildAnthropicMessages($message, $context);
        
        $data = [
            'model' => $model,
            'max_tokens' => $context['max_tokens'] ?? 1000,
            'temperature' => $context['temperature'] ?? 0.7,
            'top_p' => $context['top_p'] ?? 1.0,
            'messages' => $messages,
        ];

        // Add system message if provided
        if (isset($context['system_message'])) {
            $data['system'] = $context['system_message'];
        }

        // Add tools if supported
        if (isset($context['tools']) && $this->supportsFeature('function_calling', $model)) {
            $data['tools'] = $context['tools'];
            if (isset($context['tool_choice'])) {
                $data['tool_choice'] = $context['tool_choice'];
            }
        }

        $response = $this->makeRequest('messages', $data);

        return [
            'content' => $this->extractContent($response),
            'model' => $model,
            'provider' => $this->name,
            'usage' => $response['usage'] ?? [],
            'stop_reason' => $response['stop_reason'] ?? null,
            'tool_calls' => $this->extractToolCalls($response),
            'raw_response' => $response,
        ];
    }

    /**
     * Analyze sentiment using Claude.
     */
    public function analyzeSentiment(string $text, string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        
        $prompt = "Analyze the sentiment of the following text. Provide a sentiment score from -1 (very negative) to 1 (very positive), the dominant emotion, and your confidence level. Format your response as JSON with keys: score, emotion, confidence, explanation.\n\nText: {$text}";

        $response = $this->generateResponse($prompt, [
            'temperature' => 0.3,
            'max_tokens' => 200,
        ], $model);

        try {
            $analysis = json_decode($response['content'], true);
            return [
                'sentiment_score' => $analysis['score'] ?? 0,
                'emotion' => $analysis['emotion'] ?? 'neutral',
                'confidence' => $analysis['confidence'] ?? 0.5,
                'explanation' => $analysis['explanation'] ?? '',
                'provider' => $this->name,
                'model' => $model,
            ];
        } catch (Exception $e) {
            return [
                'sentiment_score' => 0,
                'emotion' => 'neutral',
                'confidence' => 0.5,
                'explanation' => 'Unable to parse sentiment analysis',
                'provider' => $this->name,
                'model' => $model,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Translate text using Claude.
     */
    public function translateText(string $text, string $targetLanguage, string $sourceLanguage = null, string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        
        $sourceText = $sourceLanguage ? "from {$sourceLanguage} " : '';
        $prompt = "Translate the following text {$sourceText}to {$targetLanguage}. Provide only the translation:\n\n{$text}";

        $response = $this->generateResponse($prompt, [
            'temperature' => 0.3,
            'max_tokens' => strlen($text) * 2,
        ], $model);

        return [
            'translated_text' => trim($response['content']),
            'source_language' => $sourceLanguage,
            'target_language' => $targetLanguage,
            'provider' => $this->name,
            'model' => $model,
            'confidence' => 0.9,
        ];
    }

    /**
     * Summarize text using Claude.
     */
    public function summarizeText(string $text, int $maxLength = 150, string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        
        $prompt = "Summarize the following text in approximately {$maxLength} words. Focus on the key points:\n\n{$text}";

        $response = $this->generateResponse($prompt, [
            'temperature' => 0.5,
            'max_tokens' => $maxLength * 2,
        ], $model);

        return [
            'summary' => trim($response['content']),
            'original_length' => strlen($text),
            'summary_length' => strlen($response['content']),
            'compression_ratio' => strlen($response['content']) / strlen($text),
            'provider' => $this->name,
            'model' => $model,
        ];
    }

    /**
     * Extract topics from text using Claude.
     */
    public function extractTopics(string $text, int $maxTopics = 5, string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        
        $prompt = "Extract the top {$maxTopics} topics from the following text. For each topic, provide a title and description. Format as JSON array with objects containing 'title' and 'description':\n\n{$text}";

        $response = $this->generateResponse($prompt, [
            'temperature' => 0.4,
            'max_tokens' => 500,
        ], $model);

        try {
            $topics = json_decode($response['content'], true);
            return [
                'topics' => $topics ?: [],
                'provider' => $this->name,
                'model' => $model,
            ];
        } catch (Exception $e) {
            return [
                'topics' => [],
                'provider' => $this->name,
                'model' => $model,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Generate function calls using Claude.
     */
    public function generateFunctionCalls(string $message, array $availableFunctions = [], array $context = [], string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        
        if (!$this->supportsFeature('function_calling', $model)) {
            throw new Exception("Function calling not supported by model {$model}");
        }

        $messages = $this->buildAnthropicMessages($message, $context);
        
        $data = [
            'model' => $model,
            'messages' => $messages,
            'tools' => $availableFunctions,
            'tool_choice' => ['type' => 'auto'],
            'max_tokens' => 1000,
        ];

        $response = $this->makeRequest('messages', $data);

        return [
            'content' => $this->extractContent($response),
            'tool_calls' => $this->extractToolCalls($response),
            'model' => $model,
            'provider' => $this->name,
            'usage' => $response['usage'] ?? [],
            'raw_response' => $response,
        ];
    }

    /**
     * Analyze image using Claude Vision.
     */
    public function analyzeImage(string $imageUrl, string $prompt = null, string $model = null): array
    {
        $model = $model ?: 'claude-sonnet-4-20250514';
        
        if (!$this->supportsFeature('vision', $model)) {
            throw new Exception("Vision not supported by model {$model}");
        }

        $prompt = $prompt ?: "Describe what you see in this image in detail.";

        // For Anthropic, we need to handle image data differently
        $imageData = $this->processImageForAnthropic($imageUrl);

        $messages = [
            [
                'role' => 'user',
                'content' => [
                    [
                        'type' => 'text',
                        'text' => $prompt,
                    ],
                    [
                        'type' => 'image',
                        'source' => $imageData,
                    ],
                ],
            ],
        ];

        $data = [
            'model' => $model,
            'messages' => $messages,
            'max_tokens' => 1000,
        ];

        $response = $this->makeRequest('messages', $data);

        return [
            'description' => $this->extractContent($response),
            'model' => $model,
            'provider' => $this->name,
            'usage' => $response['usage'] ?? [],
            'raw_response' => $response,
        ];
    }

    /**
     * Build messages in Anthropic format.
     */
    protected function buildAnthropicMessages(string $message, array $context = []): array
    {
        $messages = [];

        // Add conversation history (excluding system messages)
        if (isset($context['history']) && is_array($context['history'])) {
            foreach ($context['history'] as $historyMessage) {
                if (($historyMessage['role'] ?? 'user') !== 'system') {
                    $messages[] = [
                        'role' => $historyMessage['role'] ?? 'user',
                        'content' => $historyMessage['content'] ?? '',
                    ];
                }
            }
        }

        // Add current message
        $messages[] = [
            'role' => 'user',
            'content' => $message,
        ];

        return $messages;
    }

    /**
     * Extract tool calls from Anthropic response.
     */
    protected function extractToolCalls(array $response): array
    {
        $toolCalls = [];
        
        if (isset($response['content']) && is_array($response['content'])) {
            foreach ($response['content'] as $content) {
                if (isset($content['type']) && $content['type'] === 'tool_use') {
                    $toolCalls[] = [
                        'id' => $content['id'] ?? '',
                        'type' => 'function',
                        'function' => [
                            'name' => $content['name'] ?? '',
                            'arguments' => json_encode($content['input'] ?? []),
                        ],
                    ];
                }
            }
        }

        return $toolCalls;
    }

    /**
     * Process image for Anthropic API format.
     */
    protected function processImageForAnthropic(string $imageUrl): array
    {
        // This is a simplified implementation
        // In production, you'd need to download the image and convert to base64
        if (str_starts_with($imageUrl, 'data:')) {
            // Already base64 encoded
            $parts = explode(',', $imageUrl, 2);
            $mediaType = str_replace('data:', '', explode(';', $parts[0])[0]);
            
            return [
                'type' => 'base64',
                'media_type' => $mediaType,
                'data' => $parts[1],
            ];
        }

        // For URL, you'd need to download and encode
        throw new Exception("URL-based images not yet implemented for Anthropic");
    }

    /**
     * Get default headers for Anthropic API.
     */
    protected function getDefaultHeaders(): array
    {
        $headers = parent::getDefaultHeaders();
        $headers['x-api-key'] = $this->config['api_key'];
        $headers['anthropic-version'] = '2023-06-01';
        
        return $headers;
    }

    /**
     * Extract content from Anthropic response.
     */
    protected function extractContent(array $response): string
    {
        if (isset($response['content']) && is_array($response['content'])) {
            foreach ($response['content'] as $content) {
                if (isset($content['type']) && $content['type'] === 'text') {
                    return $content['text'] ?? '';
                }
            }
        }

        return $response['content'] ?? '';
    }
}
