<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\BlogPost;
use App\Models\BlogCategory;
use App\Services\ActivityLogger;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class BlogApiController extends Controller
{
    public function __construct(
        private ActivityLogger $activityLogger
    ) {}

    /**
     * Get list of blog posts with pagination.
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $query = BlogPost::with(['category', 'author'])
            ->where('is_published', true)
            ->where('is_deleted', false)
            ->orderBy('published_at', 'desc');

        // Filter by category
        if ($request->filled('category')) {
            $query->whereHas('category', function($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        // Filter by featured
        if ($request->filled('featured')) {
            $query->where('is_featured', filter_var($request->featured, FILTER_VALIDATE_BOOLEAN));
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('excerpt', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        // Pagination
        $perPage = min($request->get('per_page', 10), 50); // Max 50 per page
        $posts = $query->paginate($perPage);

        // Transform data
        $data = $posts->map(function($post) {
            return $this->transformPost($post, false);
        });

        // Log API access
        $this->activityLogger->logActivity(
            'blog_api_index',
            'Blog API: List posts accessed',
            'success',
            null,
            [
                'filters' => $request->only(['category', 'featured', 'search', 'per_page']),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ],
            ['total_posts' => $posts->total()]
        );

        return response()->json([
            'success' => true,
            'data' => $data,
            'meta' => [
                'current_page' => $posts->currentPage(),
                'last_page' => $posts->lastPage(),
                'per_page' => $posts->perPage(),
                'total' => $posts->total(),
                'from' => $posts->firstItem(),
                'to' => $posts->lastItem(),
            ],
            'links' => [
                'first' => $posts->url(1),
                'last' => $posts->url($posts->lastPage()),
                'prev' => $posts->previousPageUrl(),
                'next' => $posts->nextPageUrl(),
            ],
            'attribution' => [
                'powered_by' => 'ChiSolution Digital Agency',
                'website' => url('/'),
                'api_docs' => url('/api/blog/docs'),
                'backlink_required' => true,
                'backlink_text' => 'Powered by ChiSolution',
                'backlink_url' => url('/'),
            ]
        ]);
    }

    /**
     * Get a single blog post by slug.
     * 
     * @param string $slug
     * @param Request $request
     * @return JsonResponse
     */
    public function show(string $slug, Request $request): JsonResponse
    {
        $post = BlogPost::with(['category', 'author', 'services'])
            ->where('slug', $slug)
            ->where('is_published', true)
            ->where('is_deleted', false)
            ->first();

        if (!$post) {
            return response()->json([
                'success' => false,
                'message' => 'Blog post not found',
                'error' => [
                    'code' => 'POST_NOT_FOUND',
                    'slug' => $slug
                ]
            ], 404);
        }

        // Increment view count
        $post->incrementViewCount();

        // Log API access
        $this->activityLogger->logActivity(
            'blog_api_show',
            "Blog API: Post '{$post->title}' accessed",
            'success',
            null,
            [
                'post_id' => $post->id,
                'slug' => $slug,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'referer' => $request->header('referer')
            ],
            []
        );

        return response()->json([
            'success' => true,
            'data' => $this->transformPost($post, true),
            'attribution' => [
                'powered_by' => 'ChiSolution Digital Agency',
                'website' => url('/'),
                'original_url' => route('blog.show', ['locale' => 'en', 'post' => $post->slug]),
                'backlink_required' => true,
                'backlink_text' => 'Originally published on ChiSolution',
                'backlink_url' => route('blog.show', ['locale' => 'en', 'post' => $post->slug]),
                'canonical_url' => $post->canonical_url ?? route('blog.show', ['locale' => 'en', 'post' => $post->slug]),
            ]
        ]);
    }

    /**
     * Get list of categories.
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function categories(Request $request): JsonResponse
    {
        $categories = BlogCategory::where('is_active', true)
            ->where('is_deleted', false)
            ->withCount(['blogPosts' => function($q) {
                $q->where('is_published', true)->where('is_deleted', false);
            }])
            ->orderBy('name')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $categories->map(function($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'description' => $category->description,
                    'posts_count' => $category->blog_posts_count,
                    'url' => route('blog.category', ['locale' => 'en', 'category' => $category->slug]),
                ];
            }),
            'attribution' => [
                'powered_by' => 'ChiSolution Digital Agency',
                'website' => url('/'),
            ]
        ]);
    }

    /**
     * Get featured posts.
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function featured(Request $request): JsonResponse
    {
        $limit = min($request->get('limit', 5), 20); // Max 20

        $posts = BlogPost::with(['category', 'author'])
            ->where('is_published', true)
            ->where('is_deleted', false)
            ->where('is_featured', true)
            ->orderBy('published_at', 'desc')
            ->limit($limit)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $posts->map(function($post) {
                return $this->transformPost($post, false);
            }),
            'attribution' => [
                'powered_by' => 'ChiSolution Digital Agency',
                'website' => url('/'),
            ]
        ]);
    }

    /**
     * Get latest posts.
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function latest(Request $request): JsonResponse
    {
        $limit = min($request->get('limit', 10), 50); // Max 50

        $posts = BlogPost::with(['category', 'author'])
            ->where('is_published', true)
            ->where('is_deleted', false)
            ->orderBy('published_at', 'desc')
            ->limit($limit)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $posts->map(function($post) {
                return $this->transformPost($post, false);
            }),
            'attribution' => [
                'powered_by' => 'ChiSolution Digital Agency',
                'website' => url('/'),
            ]
        ]);
    }

    /**
     * Get API documentation.
     * 
     * @return JsonResponse
     */
    public function docs(): JsonResponse
    {
        return response()->json([
            'name' => 'ChiSolution Blog API',
            'version' => '1.0',
            'description' => 'REST API for accessing ChiSolution blog posts for guest blogging and content syndication',
            'base_url' => url('/api/blog'),
            'endpoints' => [
                [
                    'method' => 'GET',
                    'path' => '/posts',
                    'description' => 'Get list of blog posts with pagination',
                    'parameters' => [
                        'page' => 'Page number (default: 1)',
                        'per_page' => 'Items per page (default: 10, max: 50)',
                        'category' => 'Filter by category slug',
                        'featured' => 'Filter by featured status (true/false)',
                        'search' => 'Search in title, excerpt, and content',
                    ]
                ],
                [
                    'method' => 'GET',
                    'path' => '/posts/{slug}',
                    'description' => 'Get a single blog post by slug',
                    'parameters' => [
                        'slug' => 'Post slug (required)',
                    ]
                ],
                [
                    'method' => 'GET',
                    'path' => '/categories',
                    'description' => 'Get list of blog categories',
                    'parameters' => []
                ],
                [
                    'method' => 'GET',
                    'path' => '/featured',
                    'description' => 'Get featured blog posts',
                    'parameters' => [
                        'limit' => 'Number of posts (default: 5, max: 20)',
                    ]
                ],
                [
                    'method' => 'GET',
                    'path' => '/latest',
                    'description' => 'Get latest blog posts',
                    'parameters' => [
                        'limit' => 'Number of posts (default: 10, max: 50)',
                    ]
                ],
            ],
            'attribution_requirements' => [
                'backlink_required' => true,
                'backlink_text' => 'Powered by ChiSolution or Originally published on ChiSolution',
                'backlink_url' => url('/'),
                'canonical_url' => 'Must include canonical URL in meta tags',
                'nofollow' => false,
            ],
            'rate_limiting' => [
                'requests_per_minute' => 60,
                'requests_per_hour' => 1000,
            ],
            'contact' => [
                'email' => '<EMAIL>',
                'website' => url('/contact'),
            ]
        ]);
    }

    /**
     * Transform blog post to API response format.
     * 
     * @param BlogPost $post
     * @param bool $full
     * @return array
     */
    private function transformPost(BlogPost $post, bool $full = false): array
    {
        $data = [
            'id' => $post->id,
            'uuid' => $post->uuid,
            'title' => $post->title,
            'slug' => $post->slug,
            'excerpt' => $post->excerpt,
            'featured_image' => $post->featured_image ? asset('storage/' . $post->featured_image) : null,
            'category' => $post->category ? [
                'id' => $post->category->id,
                'name' => $post->category->name,
                'slug' => $post->category->slug,
            ] : null,
            'author' => [
                'name' => $post->author->first_name . ' ' . $post->author->last_name,
                'avatar' => $post->author->avatar ? asset('storage/' . $post->author->avatar) : null,
            ],
            'published_at' => $post->published_at?->toIso8601String(),
            'reading_time' => $post->reading_time,
            'view_count' => $post->view_count,
            'is_featured' => $post->is_featured,
            'url' => route('blog.show', ['locale' => 'en', 'post' => $post->slug]),
        ];

        if ($full) {
            $data['content'] = $post->content;
            $data['gallery_images'] = $post->gallery_images ? array_map(function($image) {
                return asset('storage/' . $image);
            }, $post->gallery_images) : [];
            $data['services'] = $post->services->map(function($service) {
                return [
                    'id' => $service->id,
                    'name' => $service->name,
                    'slug' => $service->slug,
                ];
            });
            $data['seo'] = [
                'meta_title' => $post->meta_title,
                'meta_description' => $post->meta_description,
                'meta_keywords' => $post->meta_keywords,
                'canonical_url' => $post->canonical_url ?? route('blog.show', ['locale' => 'en', 'post' => $post->slug]),
                'og_title' => $post->og_title,
                'og_description' => $post->og_description,
                'og_image' => $post->og_image,
            ];
        }

        return $data;
    }
}

