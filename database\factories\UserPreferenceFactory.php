<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\UserPreference;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserPreference>
 */
class UserPreferenceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $timezones = array_keys(UserPreference::getAvailableTimezones());
        $currencies = array_keys(UserPreference::getAvailableCurrencies());
        $languages = array_keys(UserPreference::getAvailableLanguages());

        return [
            'uuid' => $this->faker->uuid(),
            'user_id' => User::factory(),
            'language' => $this->faker->randomElement($languages),
            'timezone' => $this->faker->randomElement($timezones),
            'currency' => $this->faker->randomElement($currencies),
            'date_format' => $this->faker->randomElement(['Y-m-d', 'd/m/Y', 'm/d/Y', 'd-m-Y']),
            'time_format' => $this->faker->randomElement(['H:i', 'h:i A']),
            'theme' => $this->faker->randomElement(['light', 'dark', 'auto']),
            'dashboard_layout' => $this->faker->randomElement(['grid', 'list', 'compact']),
            'show_welcome_message' => $this->faker->boolean(),
            'auto_refresh_dashboard' => $this->faker->boolean(),
            'auto_refresh_interval' => $this->faker->randomElement([10, 30, 60, 300]),
            'email_notifications' => $this->faker->boolean(),
            'browser_notifications' => $this->faker->boolean(),
            'sms_notifications' => $this->faker->boolean(20), // 20% chance
            'chat_sound_enabled' => $this->faker->boolean(),
            'chat_desktop_notifications' => $this->faker->boolean(),
            'chat_status' => $this->faker->randomElement(['available', 'busy', 'away', 'offline']),
            'max_concurrent_chats' => $this->faker->numberBetween(1, 20),
            'auto_assign_chats' => $this->faker->boolean(),
            'items_per_page' => $this->faker->randomElement([10, 25, 50, 100]),
            'show_tooltips' => $this->faker->boolean(),
            'compact_mode' => $this->faker->boolean(),
            'profile_visible' => $this->faker->boolean(),
            'activity_tracking' => $this->faker->boolean(),
            'analytics_tracking' => $this->faker->boolean(),
        ];
    }

    /**
     * Configure the factory for staff users.
     */
    public function forStaff(): static
    {
        return $this->state(fn (array $attributes) => [
            'chat_status' => 'available',
            'max_concurrent_chats' => $this->faker->numberBetween(3, 10),
            'auto_assign_chats' => true,
            'chat_sound_enabled' => true,
            'chat_desktop_notifications' => true,
        ]);
    }

    /**
     * Configure the factory for admin users.
     */
    public function forAdmin(): static
    {
        return $this->state(fn (array $attributes) => [
            'chat_status' => 'available',
            'max_concurrent_chats' => $this->faker->numberBetween(5, 20),
            'auto_assign_chats' => false, // Admins might prefer manual assignment
            'items_per_page' => $this->faker->randomElement([50, 100]),
            'show_tooltips' => false, // Admins are more experienced
        ]);
    }

    /**
     * Configure the factory with quiet hours.
     */
    public function withQuietHours(): static
    {
        return $this->state(fn (array $attributes) => [
            'quiet_hours_start' => $this->faker->time('H:i', '23:59'),
            'quiet_hours_end' => $this->faker->time('H:i', '08:00'),
        ]);
    }
}
