<?php

namespace App\Services;

use App\Models\User;
use App\Models\Order;
use App\Models\Product;
use App\Models\Project;
use App\Models\Job;
use App\Models\JobApplication;
use App\Models\Coupon;
use App\Models\Payment;
use App\Models\ActivityLog;
use App\Models\VisitorAnalytic;
use App\Models\ContactSubmission;
use App\Models\NewsletterSubscription;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class GlobalSearchService
{
    private static array $cache = [];

    /**
     * Search across all database entities.
     */
    public function search(string $query, int $limit = 50, array $filters = []): Collection
    {
        $results = collect();
        $query = trim($query);

        if (strlen($query) < 2) {
            return $results;
        }

        // Simple cache key
        $cacheKey = md5($query . serialize($filters) . $limit);

        // Check cache (valid for 30 seconds)
        if (isset(self::$cache[$cacheKey]) &&
            self::$cache[$cacheKey]['expires'] > time()) {
            return self::$cache[$cacheKey]['data'];
        }

        $type = $filters['type'] ?? null;
        $status = $filters['status'] ?? null;
        $dateRange = $filters['date_range'] ?? null;

        // If type filter is specified, only search that type
        if ($type) {
            switch ($type) {
                case 'user':
                    $results = $this->searchUsers($query, $limit, $status, $dateRange);
                    break;
                case 'order':
                    $results = $this->searchOrders($query, $limit, $status, $dateRange);
                    break;
                case 'product':
                    $results = $this->searchProducts($query, $limit, $status, $dateRange);
                    break;
                case 'project':
                    $results = $this->searchProjects($query, $limit, $status, $dateRange);
                    break;
                case 'job':
                    $results = $this->searchJobs($query, $limit, $status, $dateRange);
                    break;
                case 'job_application':
                    $results = $this->searchJobApplications($query, $limit, $status, $dateRange);
                    break;
                case 'coupon':
                    $results = $this->searchCoupons($query, $limit, $status, $dateRange);
                    break;
                case 'payment':
                    $results = $this->searchPayments($query, $limit, $status, $dateRange);
                    break;
                case 'activity_log':
                    $results = $this->searchActivityLogs($query, $limit, $status, $dateRange);
                    break;
                case 'visitor_analytic':
                    $results = $this->searchVisitorAnalytics($query, $limit, $status, $dateRange);
                    break;
                case 'contact_submission':
                    $results = $this->searchContactSubmissions($query, $limit, $status, $dateRange);
                    break;
                case 'newsletter_subscription':
                    $results = $this->searchNewsletterSubscriptions($query, $limit, $status, $dateRange);
                    break;
            }
        } else {
            // Search all types with reduced limits for performance
            $users = $this->searchUsers($query, 5, $status, $dateRange);
            $results = $results->merge($users);

            $orders = $this->searchOrders($query, 5, $status, $dateRange);
            $results = $results->merge($orders);

            $products = $this->searchProducts($query, 5, $status, $dateRange);
            $results = $results->merge($products);

            $projects = $this->searchProjects($query, 3, $status, $dateRange);
            $results = $results->merge($projects);

            $jobs = $this->searchJobs($query, 3, $status, $dateRange);
            $results = $results->merge($jobs);

            $jobApplications = $this->searchJobApplications($query, 3, $status, $dateRange);
            $results = $results->merge($jobApplications);

            $coupons = $this->searchCoupons($query, 3, $status, $dateRange);
            $results = $results->merge($coupons);

            $payments = $this->searchPayments($query, 3, $status, $dateRange);
            $results = $results->merge($payments);

            // Add contact submissions and newsletter subscriptions
            $contactSubmissions = $this->searchContactSubmissions($query, 3, $status, $dateRange);
            $results = $results->merge($contactSubmissions);

            $newsletterSubscriptions = $this->searchNewsletterSubscriptions($query, 3, $status, $dateRange);
            $results = $results->merge($newsletterSubscriptions);

            // Skip activity logs and visitor analytics for general search to improve performance
            // They can still be searched specifically by type filter
            if (strlen($query) >= 4) { // Only search these for longer queries
                $activityLogs = $this->searchActivityLogs($query, 2, $status, $dateRange);
                $results = $results->merge($activityLogs);

                $visitorAnalytics = $this->searchVisitorAnalytics($query, 2, $status, $dateRange);
                $results = $results->merge($visitorAnalytics);
            }
        }

        $finalResults = $results->take($limit)->sortByDesc('relevance');

        // Cache results for 30 seconds
        self::$cache[$cacheKey] = [
            'data' => $finalResults,
            'expires' => time() + 30
        ];

        // Clean old cache entries (keep only last 10)
        if (count(self::$cache) > 10) {
            $oldestKey = array_key_first(self::$cache);
            unset(self::$cache[$oldestKey]);
        }

        return $finalResults;
    }

    /**
     * Search users.
     */
    private function searchUsers(string $query, int $limit, ?string $status = null, ?string $dateRange = null): Collection
    {
        $usersQuery = User::where('is_deleted', false);

        // Optimize search by checking for email pattern first
        if (filter_var($query, FILTER_VALIDATE_EMAIL)) {
            $usersQuery->where('email', 'like', "%{$query}%");
        } else {
            $usersQuery->where(function ($q) use ($query) {
                $q->where('first_name', 'like', "%{$query}%")
                  ->orWhere('last_name', 'like', "%{$query}%")
                  ->orWhere('email', 'like', "%{$query}%");

                // Only search phone if query looks like a phone number
                if (preg_match('/[\d\+\-\(\)\s]/', $query)) {
                    $q->orWhere('phone', 'like', "%{$query}%");
                }
            });
        }

        // Apply status filter (role filter for users)
        if ($status) {
            $usersQuery->whereHas('role', function ($q) use ($status) {
                $q->where('name', $status);
            });
        }

        // Apply date range filter
        if ($dateRange) {
            $usersQuery = $this->applyDateFilter($usersQuery, $dateRange);
        }

        $users = $usersQuery->with('role')->limit($limit)->get();

        return $users->map(function ($user) use ($query) {
            return [
                'id' => $user->id,
                'type' => 'user',
                'title' => $user->first_name . ' ' . $user->last_name,
                'description' => $user->email . ' • ' . ucfirst($user->role->name ?? 'User'),
                'url' => route('admin.users.show', $user),
                'icon' => 'user',
                'relevance' => $this->calculateRelevance($query, [
                    $user->first_name,
                    $user->last_name,
                    $user->email
                ]),
                'meta' => [
                    'role' => $user->role->name ?? 'user',
                    'created_at' => $user->created_at->format('M j, Y'),
                ]
            ];
        });
    }

    /**
     * Search orders.
     */
    private function searchOrders(string $query, int $limit, ?string $status = null, ?string $dateRange = null): Collection
    {
        $ordersQuery = Order::where('is_deleted', false);

        // Optimize by checking if query looks like an order number first
        if (preg_match('/^[A-Z]{2,4}-\d+/', $query)) {
            $ordersQuery->where('order_number', 'like', "%{$query}%");
        } elseif (filter_var($query, FILTER_VALIDATE_EMAIL)) {
            $ordersQuery->where('email', 'like', "%{$query}%");
        } else {
            $ordersQuery->where(function ($q) use ($query) {
                $q->where('order_number', 'like', "%{$query}%")
                  ->orWhere('email', 'like', "%{$query}%");

                // Only search user names for longer queries to improve performance
                if (strlen($query) >= 3) {
                    $q->orWhereHas('user', function ($userQuery) use ($query) {
                        $userQuery->where('first_name', 'like', "%{$query}%")
                                 ->orWhere('last_name', 'like', "%{$query}%");
                    });
                }
            });
        }

        // Apply status filter
        if ($status) {
            $ordersQuery->where('status', $status);
        }

        // Apply date range filter
        if ($dateRange) {
            $ordersQuery = $this->applyDateFilter($ordersQuery, $dateRange);
        }

        $orders = $ordersQuery->with(['user', 'currency'])->limit($limit)->get();

        return $orders->map(function ($order) use ($query) {
            $customerName = $order->user 
                ? $order->user->first_name . ' ' . $order->user->last_name
                : 'Guest Customer';

            return [
                'id' => $order->id,
                'type' => 'order',
                'title' => 'Order #' . $order->order_number,
                'description' => $customerName . ' • ' . ($order->currency->symbol ?? 'R') . number_format($order->total_amount, 2),
                'url' => route('admin.orders.show', $order),
                'icon' => 'shopping-bag',
                'relevance' => $this->calculateRelevance($query, [
                    $order->order_number,
                    $order->email,
                    $customerName
                ]),
                'meta' => [
                    'status' => $order->status,
                    'payment_status' => $order->payment_status,
                    'total' => ($order->currency->symbol ?? 'R') . number_format($order->total_amount, 2),
                    'created_at' => $order->created_at->format('M j, Y'),
                ]
            ];
        });
    }

    /**
     * Search products.
     */
    private function searchProducts(string $query, int $limit, ?string $status = null, ?string $dateRange = null): Collection
    {
        $productsQuery = Product::where('is_deleted', false)
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('sku', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%");
            });

        // Apply status filter (active/inactive for products)
        if ($status) {
            $productsQuery->where('is_active', $status === 'active');
        }

        // Apply date range filter
        if ($dateRange) {
            $productsQuery = $this->applyDateFilter($productsQuery, $dateRange);
        }

        $products = $productsQuery->limit($limit)->get();

        return $products->map(function ($product) use ($query) {
            return [
                'id' => $product->id,
                'type' => 'product',
                'title' => $product->name,
                'description' => 'SKU: ' . ($product->sku ?? 'N/A') . ' • R' . number_format($product->price, 2),
                'url' => route('admin.products.show', $product),
                'icon' => 'cube',
                'relevance' => $this->calculateRelevance($query, [
                    $product->name,
                    $product->sku,
                    $product->description
                ]),
                'meta' => [
                    'sku' => $product->sku,
                    'price' => 'R' . number_format($product->price, 2),
                    'status' => $product->is_active ? 'Active' : 'Inactive',
                    'stock' => $product->stock_quantity ?? 0,
                ]
            ];
        });
    }

    /**
     * Search projects.
     */
    private function searchProjects(string $query, int $limit, ?string $status = null, ?string $dateRange = null): Collection
    {
        $projectsQuery = Project::where(function ($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%")
                  ->orWhere('client_name', 'like', "%{$query}%");
            });

        if ($status) {
            $projectsQuery->where('status', $status);
        }

        if ($dateRange) {
            $projectsQuery = $this->applyDateFilter($projectsQuery, $dateRange);
        }

        $projects = $projectsQuery->with('client')->limit($limit)->get();

        return $projects->map(function ($project) use ($query) {
            return [
                'id' => $project->id,
                'type' => 'project',
                'title' => $project->title,
                'description' => ($project->client_name ?? 'No Client') . ' • ' . ucfirst(str_replace('_', ' ', $project->status)),
                'url' => route('admin.projects.show', $project),
                'icon' => 'briefcase',
                'relevance' => $this->calculateRelevance($query, [
                    $project->title,
                    $project->description,
                    $project->client_name
                ]),
                'meta' => [
                    'status' => ucfirst(str_replace('_', ' ', $project->status)),
                    'client' => $project->client_name ?? 'No Client',
                    'budget' => $project->budget ? 'R' . number_format($project->budget, 2) : 'N/A',
                    'created_at' => $project->created_at->format('M j, Y'),
                ]
            ];
        });
    }

    /**
     * Search jobs.
     */
    private function searchJobs(string $query, int $limit, ?string $status = null, ?string $dateRange = null): Collection
    {
        $jobsQuery = Job::where(function ($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%")
                  ->orWhere('department', 'like', "%{$query}%")
                  ->orWhere('location', 'like', "%{$query}%");
            });

        if ($status) {
            $jobsQuery->where('is_active', $status === 'active');
        }

        if ($dateRange) {
            $jobsQuery = $this->applyDateFilter($jobsQuery, $dateRange);
        }

        $jobs = $jobsQuery->limit($limit)->get();

        return $jobs->map(function ($job) use ($query) {
            return [
                'id' => $job->id,
                'type' => 'job',
                'title' => $job->title,
                'description' => ($job->department ?? 'General') . ' • ' . ($job->location ?? 'Remote'),
                'url' => route('admin.jobs.show', $job),
                'icon' => 'briefcase',
                'relevance' => $this->calculateRelevance($query, [
                    $job->title,
                    $job->description,
                    $job->department,
                    $job->location
                ]),
                'meta' => [
                    'department' => $job->department ?? 'General',
                    'location' => $job->location ?? 'Remote',
                    'type' => ucfirst($job->type ?? 'full_time'),
                    'status' => $job->is_active ? 'Active' : 'Inactive',
                ]
            ];
        });
    }

    /**
     * Search job applications.
     */
    private function searchJobApplications(string $query, int $limit, ?string $status = null, ?string $dateRange = null): Collection
    {
        $applicationsQuery = JobApplication::where(function ($q) use ($query) {
                $q->where('first_name', 'like', "%{$query}%")
                  ->orWhere('last_name', 'like', "%{$query}%")
                  ->orWhere('email', 'like', "%{$query}%")
                  ->orWhere('reference_number', 'like', "%{$query}%")
                  ->orWhereHas('job', function ($jobQuery) use ($query) {
                      $jobQuery->where('title', 'like', "%{$query}%");
                  });
            });

        if ($status) {
            $applicationsQuery->where('status', $status);
        }

        if ($dateRange) {
            $applicationsQuery = $this->applyDateFilter($applicationsQuery, $dateRange);
        }

        $applications = $applicationsQuery->with('job')->limit($limit)->get();

        return $applications->map(function ($application) use ($query) {
            return [
                'id' => $application->id,
                'type' => 'job_application',
                'title' => $application->first_name . ' ' . $application->last_name,
                'description' => ($application->job->title ?? 'Unknown Job') . ' • ' . ucfirst($application->status),
                'url' => route('admin.job-applications.show', $application),
                'icon' => 'document-text',
                'relevance' => $this->calculateRelevance($query, [
                    $application->first_name,
                    $application->last_name,
                    $application->email,
                    $application->reference_number
                ]),
                'meta' => [
                    'reference' => $application->reference_number,
                    'status' => ucfirst($application->status),
                    'job' => $application->job->title ?? 'Unknown Job',
                    'applied_at' => $application->created_at->format('M j, Y'),
                ]
            ];
        });
    }

    /**
     * Search coupons.
     */
    private function searchCoupons(string $query, int $limit, ?string $status = null, ?string $dateRange = null): Collection
    {
        $couponsQuery = Coupon::where('is_deleted', false)
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('code', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%");
            });

        if ($status) {
            $couponsQuery->where('is_active', $status === 'active');
        }

        if ($dateRange) {
            $couponsQuery = $this->applyDateFilter($couponsQuery, $dateRange);
        }

        $coupons = $couponsQuery->limit($limit)->get();

        return $coupons->map(function ($coupon) use ($query) {
            return [
                'id' => $coupon->id,
                'type' => 'coupon',
                'title' => $coupon->name,
                'description' => 'Code: ' . $coupon->code . ' • ' . ucfirst($coupon->type),
                'url' => route('admin.coupons.show', $coupon),
                'icon' => 'ticket',
                'relevance' => $this->calculateRelevance($query, [
                    $coupon->name,
                    $coupon->code,
                    $coupon->description
                ]),
                'meta' => [
                    'code' => $coupon->code,
                    'type' => ucfirst($coupon->type),
                    'value' => $coupon->type === 'percentage' ? $coupon->value . '%' : 'R' . number_format($coupon->value, 2),
                    'status' => $coupon->is_active ? 'Active' : 'Inactive',
                ]
            ];
        });
    }

    /**
     * Search payments.
     */
    private function searchPayments(string $query, int $limit, ?string $status = null, ?string $dateRange = null): Collection
    {
        $paymentsQuery = Payment::where('is_deleted', false)
            ->where(function ($q) use ($query) {
                $q->where('transaction_id', 'like', "%{$query}%")
                  ->orWhere('gateway_transaction_id', 'like', "%{$query}%")
                  ->orWhereHas('order', function ($orderQuery) use ($query) {
                      $orderQuery->where('order_number', 'like', "%{$query}%");
                  });
            });

        if ($status) {
            $paymentsQuery->where('status', $status);
        }

        if ($dateRange) {
            $paymentsQuery = $this->applyDateFilter($paymentsQuery, $dateRange);
        }

        $payments = $paymentsQuery->with(['order', 'currency'])->limit($limit)->get();

        return $payments->map(function ($payment) use ($query) {
            return [
                'id' => $payment->id,
                'type' => 'payment',
                'title' => 'Payment ' . $payment->transaction_id,
                'description' => 'Order #' . ($payment->order->order_number ?? 'N/A') . ' • ' . ($payment->currency->symbol ?? 'R') . number_format($payment->amount, 2),
                'url' => route('admin.orders.show', $payment->order),
                'icon' => 'credit-card',
                'relevance' => $this->calculateRelevance($query, [
                    $payment->transaction_id,
                    $payment->gateway_transaction_id,
                    $payment->order->order_number ?? ''
                ]),
                'meta' => [
                    'status' => ucfirst($payment->status),
                    'method' => ucfirst(str_replace('_', ' ', $payment->payment_method)),
                    'amount' => ($payment->currency->symbol ?? 'R') . number_format($payment->amount, 2),
                    'processed_at' => $payment->processed_at ? $payment->processed_at->format('M j, Y') : 'Pending',
                ]
            ];
        });
    }

    /**
     * Calculate relevance score based on query match.
     */
    private function calculateRelevance(string $query, array $fields): int
    {
        $score = 0;
        $query = strtolower($query);

        foreach ($fields as $field) {
            if (!$field) continue;
            
            $field = strtolower($field);
            
            // Exact match gets highest score
            if ($field === $query) {
                $score += 100;
            }
            // Starts with query gets high score
            elseif (Str::startsWith($field, $query)) {
                $score += 80;
            }
            // Contains query gets medium score
            elseif (Str::contains($field, $query)) {
                $score += 50;
            }
            // Similar gets low score
            elseif (similar_text($field, $query) > strlen($query) * 0.6) {
                $score += 20;
            }
        }

        return $score;
    }

    /**
     * Search activity logs.
     */
    private function searchActivityLogs(string $query, int $limit, ?string $status = null, ?string $dateRange = null): Collection
    {
        $activityLogsQuery = ActivityLog::where(function ($q) use ($query) {
                $q->where('user_email', 'like', "%{$query}%")
                  ->orWhere('user_name', 'like', "%{$query}%")
                  ->orWhere('activity_type', 'like', "%{$query}%")
                  ->orWhere('activity_description', 'like', "%{$query}%")
                  ->orWhere('ip_address', 'like', "%{$query}%")
                  ->orWhere('url', 'like', "%{$query}%")
                  ->orWhere('browser', 'like', "%{$query}%")
                  ->orWhere('platform', 'like', "%{$query}%")
                  ->orWhere('country', 'like', "%{$query}%")
                  ->orWhere('city', 'like', "%{$query}%");
            });

        if ($status) {
            $activityLogsQuery->where('status', $status);
        }

        if ($dateRange) {
            $activityLogsQuery = $this->applyDateFilter($activityLogsQuery, $dateRange, 'occurred_at');
        }

        $activityLogs = $activityLogsQuery->with('user')->limit($limit)->get();

        return $activityLogs->map(function ($log) use ($query) {
            return [
                'id' => $log->id,
                'type' => 'activity_log',
                'title' => ucfirst(str_replace('_', ' ', $log->activity_type)),
                'description' => ($log->user_name ?? $log->user_email ?? 'Unknown User') . ' • ' . $log->activity_description,
                'url' => route('admin.activity-logs.show', $log),
                'icon' => 'document-text',
                'relevance' => $this->calculateRelevance($query, [
                    $log->activity_type,
                    $log->activity_description,
                    $log->user_email,
                    $log->user_name,
                    $log->ip_address
                ]),
                'meta' => [
                    'status' => ucfirst($log->status),
                    'ip_address' => $log->ip_address,
                    'location' => $log->location_info,
                    'occurred_at' => $log->occurred_at->format('M j, Y g:i A'),
                ]
            ];
        });
    }

    /**
     * Search visitor analytics.
     */
    private function searchVisitorAnalytics(string $query, int $limit, ?string $status = null, ?string $dateRange = null): Collection
    {
        $visitorAnalyticsQuery = VisitorAnalytic::where(function ($q) use ($query) {
                $q->where('page_url', 'like', "%{$query}%")
                  ->orWhere('page_title', 'like', "%{$query}%")
                  ->orWhere('referrer_url', 'like', "%{$query}%")
                  ->orWhere('ip_address', 'like', "%{$query}%")
                  ->orWhere('browser', 'like', "%{$query}%")
                  ->orWhere('platform', 'like', "%{$query}%")
                  ->orWhere('country', 'like', "%{$query}%")
                  ->orWhere('city', 'like', "%{$query}%")
                  ->orWhere('utm_source', 'like', "%{$query}%")
                  ->orWhere('utm_campaign', 'like', "%{$query}%");
            });

        // For visitor analytics, status could be conversion status
        if ($status) {
            if ($status === 'converted') {
                $visitorAnalyticsQuery->where('converted', true);
            } elseif ($status === 'bounce') {
                $visitorAnalyticsQuery->where('is_bounce', true);
            } elseif ($status === 'suspicious') {
                $visitorAnalyticsQuery->where('is_suspicious', true);
            }
        }

        if ($dateRange) {
            $visitorAnalyticsQuery = $this->applyDateFilter($visitorAnalyticsQuery, $dateRange, 'visited_at');
        }

        $visitorAnalytics = $visitorAnalyticsQuery->limit($limit)->get();

        return $visitorAnalytics->map(function ($analytic) use ($query) {
            return [
                'id' => $analytic->id,
                'type' => 'visitor_analytic',
                'title' => $analytic->page_title ?: 'Page Visit',
                'description' => $analytic->page_url . ' • ' . ($analytic->country ?? 'Unknown Location'),
                'url' => route('admin.dashboard') . '#visitor-analytics', // Link to analytics section
                'icon' => 'chart-bar',
                'relevance' => $this->calculateRelevance($query, [
                    $analytic->page_title,
                    $analytic->page_url,
                    $analytic->referrer_url,
                    $analytic->country,
                    $analytic->city
                ]),
                'meta' => [
                    'device' => $analytic->device_type ?? 'Unknown',
                    'browser' => $analytic->browser ?? 'Unknown',
                    'location' => trim(($analytic->city ?? '') . ', ' . ($analytic->country ?? ''), ', '),
                    'visited_at' => $analytic->visited_at ? $analytic->visited_at->format('M j, Y g:i A') : 'Unknown',
                ]
            ];
        });
    }

    /**
     * Apply date range filter to query.
     */
    private function applyDateFilter($query, string $dateRange, string $dateColumn = 'created_at')
    {
        $now = now();

        switch ($dateRange) {
            case 'today':
                return $query->whereDate($dateColumn, $now->toDateString());
            case 'week':
                return $query->whereBetween($dateColumn, [$now->startOfWeek(), $now->endOfWeek()]);
            case 'month':
                return $query->whereBetween($dateColumn, [$now->startOfMonth(), $now->endOfMonth()]);
            case 'quarter':
                return $query->whereBetween($dateColumn, [$now->startOfQuarter(), $now->endOfQuarter()]);
            case 'year':
                return $query->whereBetween($dateColumn, [$now->startOfYear(), $now->endOfYear()]);
            default:
                return $query;
        }
    }

    /**
     * Search contact submissions.
     */
    private function searchContactSubmissions(string $query, int $limit, ?string $status = null, ?string $dateRange = null): Collection
    {
        $submissionsQuery = ContactSubmission::query();

        // Search in name, email, company, subject, and message
        $submissionsQuery->where(function ($q) use ($query) {
            $q->where('name', 'like', "%{$query}%")
              ->orWhere('email', 'like', "%{$query}%")
              ->orWhere('company', 'like', "%{$query}%")
              ->orWhere('subject', 'like', "%{$query}%")
              ->orWhere('message', 'like', "%{$query}%");
        });

        // Apply status filter
        if ($status) {
            switch ($status) {
                case 'read':
                    $submissionsQuery->where('is_read', true);
                    break;
                case 'unread':
                    $submissionsQuery->where('is_read', false);
                    break;
                case 'spam':
                    $submissionsQuery->where('is_spam', true);
                    break;
                case 'not_spam':
                    $submissionsQuery->where('is_spam', false);
                    break;
            }
        }

        // Apply date range filter
        if ($dateRange) {
            $submissionsQuery = $this->applyDateFilter($submissionsQuery, $dateRange);
        }

        $submissions = $submissionsQuery->orderBy('created_at', 'desc')->limit($limit)->get();

        return $submissions->map(function ($submission) use ($query) {
            return [
                'id' => $submission->id,
                'type' => 'contact_submission',
                'title' => $submission->name . ' - ' . ($submission->subject ?: 'Contact Form'),
                'description' => $submission->email . ' • ' . Str::limit($submission->message, 100),
                'url' => route('admin.contact-submissions.show', $submission),
                'icon' => 'mail',
                'relevance' => $this->calculateRelevance($query, [
                    $submission->name,
                    $submission->email,
                    $submission->subject,
                    $submission->message
                ]),
                'meta' => [
                    'status' => $submission->is_read ? 'read' : 'unread',
                    'spam' => $submission->is_spam,
                    'created_at' => $submission->created_at->format('M j, Y'),
                ]
            ];
        });
    }

    /**
     * Search newsletter subscriptions.
     */
    private function searchNewsletterSubscriptions(string $query, int $limit, ?string $status = null, ?string $dateRange = null): Collection
    {
        $subscriptionsQuery = NewsletterSubscription::query();

        // Search in email and name
        $subscriptionsQuery->where(function ($q) use ($query) {
            $q->where('email', 'like', "%{$query}%")
              ->orWhere('name', 'like', "%{$query}%");
        });

        // Apply status filter
        if ($status) {
            switch ($status) {
                case 'active':
                    $subscriptionsQuery->where('is_active', true);
                    break;
                case 'inactive':
                    $subscriptionsQuery->where('is_active', false);
                    break;
                case 'verified':
                    $subscriptionsQuery->whereNotNull('email_verified_at');
                    break;
                case 'unverified':
                    $subscriptionsQuery->whereNull('email_verified_at');
                    break;
            }
        }

        // Apply date range filter
        if ($dateRange) {
            $subscriptionsQuery = $this->applyDateFilter($subscriptionsQuery, $dateRange);
        }

        $subscriptions = $subscriptionsQuery->orderBy('created_at', 'desc')->limit($limit)->get();

        return $subscriptions->map(function ($subscription) use ($query) {
            return [
                'id' => $subscription->id,
                'type' => 'newsletter_subscription',
                'title' => $subscription->name ?: $subscription->email,
                'description' => $subscription->email . ' • ' . ($subscription->is_active ? 'Active' : 'Inactive') . ' • ' . ($subscription->email_verified_at ? 'Verified' : 'Unverified'),
                'url' => route('admin.newsletter-subscriptions.show', $subscription),
                'icon' => 'mail',
                'relevance' => $this->calculateRelevance($query, [
                    $subscription->email,
                    $subscription->name
                ]),
                'meta' => [
                    'status' => $subscription->is_active ? 'active' : 'inactive',
                    'verified' => $subscription->email_verified_at ? true : false,
                    'created_at' => $subscription->created_at->format('M j, Y'),
                ]
            ];
        });
    }
}
