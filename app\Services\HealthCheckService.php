<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Storage;
use App\Models\ChatRoom;
use App\Models\ChatMessage;

class HealthCheckService
{
    /**
     * Perform comprehensive health check.
     */
    public function performHealthCheck(): array
    {
        $checks = [
            'database' => $this->checkDatabase(),
            'redis' => $this->checkRedis(),
            'cache' => $this->checkCache(),
            'queue' => $this->checkQueue(),
            'storage' => $this->checkStorage(),
            'chat_system' => $this->checkChatSystem(),
            'ai_service' => $this->checkAIService(),
            'websocket' => $this->checkWebSocket(),
        ];

        $overallStatus = collect($checks)->every(fn($check) => $check['status'] === 'healthy');

        return [
            'status' => $overallStatus ? 'healthy' : 'unhealthy',
            'timestamp' => now()->toISOString(),
            'checks' => $checks,
            'version' => config('app.version', '1.0.0'),
            'environment' => config('app.env'),
        ];
    }

    /**
     * Check database connectivity and performance.
     */
    protected function checkDatabase(): array
    {
        try {
            $start = microtime(true);
            
            // Test basic connectivity
            DB::connection()->getPdo();
            
            // Test query performance
            $count = DB::table('chat_rooms')->count();
            
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            return [
                'status' => 'healthy',
                'response_time_ms' => $responseTime,
                'details' => [
                    'connection' => 'active',
                    'total_rooms' => $count,
                ],
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'details' => [
                    'connection' => 'failed',
                ],
            ];
        }
    }

    /**
     * Check Redis connectivity and performance.
     */
    protected function checkRedis(): array
    {
        try {
            $start = microtime(true);
            
            // Test basic connectivity
            Redis::ping();
            
            // Test read/write operations
            $testKey = 'health_check_' . time();
            Redis::set($testKey, 'test_value', 'EX', 60);
            $value = Redis::get($testKey);
            Redis::del($testKey);
            
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            return [
                'status' => 'healthy',
                'response_time_ms' => $responseTime,
                'details' => [
                    'connection' => 'active',
                    'read_write' => $value === 'test_value' ? 'working' : 'failed',
                ],
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'details' => [
                    'connection' => 'failed',
                ],
            ];
        }
    }

    /**
     * Check cache system.
     */
    protected function checkCache(): array
    {
        try {
            $start = microtime(true);
            
            $testKey = 'health_check_cache_' . time();
            $testValue = 'cache_test_value';
            
            Cache::put($testKey, $testValue, 60);
            $retrievedValue = Cache::get($testKey);
            Cache::forget($testKey);
            
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            return [
                'status' => 'healthy',
                'response_time_ms' => $responseTime,
                'details' => [
                    'driver' => config('cache.default'),
                    'read_write' => $retrievedValue === $testValue ? 'working' : 'failed',
                ],
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'details' => [
                    'driver' => config('cache.default'),
                ],
            ];
        }
    }

    /**
     * Check queue system.
     */
    protected function checkQueue(): array
    {
        try {
            $start = microtime(true);
            
            // Check queue connection
            $connection = Queue::connection();
            $size = $connection->size();
            
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            return [
                'status' => 'healthy',
                'response_time_ms' => $responseTime,
                'details' => [
                    'driver' => config('queue.default'),
                    'pending_jobs' => $size,
                    'connection' => 'active',
                ],
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'details' => [
                    'driver' => config('queue.default'),
                ],
            ];
        }
    }

    /**
     * Check storage system.
     */
    protected function checkStorage(): array
    {
        try {
            $start = microtime(true);
            
            $testFile = 'health_check_' . time() . '.txt';
            $testContent = 'Health check test content';
            
            // Test write
            Storage::disk('local')->put($testFile, $testContent);

            // Test read
            $retrievedContent = Storage::disk('local')->get($testFile);

            // Test delete
            Storage::disk('local')->delete($testFile);
            
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            return [
                'status' => 'healthy',
                'response_time_ms' => $responseTime,
                'details' => [
                    'disk' => 'local',
                    'read_write_delete' => $retrievedContent === $testContent ? 'working' : 'failed',
                    'free_space' => $this->getStorageInfo(),
                ],
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'details' => [
                    'disk' => 'local',
                ],
            ];
        }
    }

    /**
     * Check chat system functionality.
     */
    protected function checkChatSystem(): array
    {
        try {
            $start = microtime(true);
            
            // Check recent activity
            $recentRooms = ChatRoom::where('created_at', '>=', now()->subHour())->count();
            $recentMessages = ChatMessage::where('created_at', '>=', now()->subHour())->count();
            $activeRooms = ChatRoom::where('status', 'active')->count();
            
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            return [
                'status' => 'healthy',
                'response_time_ms' => $responseTime,
                'details' => [
                    'recent_rooms_1h' => $recentRooms,
                    'recent_messages_1h' => $recentMessages,
                    'active_rooms' => $activeRooms,
                    'system_status' => 'operational',
                ],
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'details' => [
                    'system_status' => 'failed',
                ],
            ];
        }
    }

    /**
     * Check AI service connectivity.
     */
    protected function checkAIService(): array
    {
        try {
            $start = microtime(true);
            
            // Check if OpenAI API key is configured
            $apiKey = config('openai.api_key');
            if (!$apiKey) {
                return [
                    'status' => 'unhealthy',
                    'error' => 'OpenAI API key not configured',
                    'details' => [
                        'configuration' => 'missing',
                    ],
                ];
            }

            // Simple connectivity test (you might want to implement actual API call)
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            return [
                'status' => 'healthy',
                'response_time_ms' => $responseTime,
                'details' => [
                    'configuration' => 'present',
                    'api_key' => 'configured',
                    'service' => 'available',
                ],
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'details' => [
                    'service' => 'failed',
                ],
            ];
        }
    }

    /**
     * Check WebSocket service.
     */
    protected function checkWebSocket(): array
    {
        try {
            $start = microtime(true);
            
            // Check if WebSocket server is configured
            $host = config('broadcasting.connections.pusher.options.host', 'localhost');
            $port = config('broadcasting.connections.pusher.options.port', 6001);
            
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            return [
                'status' => 'healthy',
                'response_time_ms' => $responseTime,
                'details' => [
                    'host' => $host,
                    'port' => $port,
                    'configuration' => 'present',
                ],
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'details' => [
                    'service' => 'failed',
                ],
            ];
        }
    }

    /**
     * Get storage information.
     */
    protected function getStorageInfo(): array
    {
        $path = storage_path();
        $bytes = disk_free_space($path);
        $totalBytes = disk_total_space($path);
        
        return [
            'free_bytes' => $bytes,
            'total_bytes' => $totalBytes,
            'free_percentage' => round(($bytes / $totalBytes) * 100, 2),
        ];
    }

    /**
     * Get system metrics for monitoring.
     */
    public function getSystemMetrics(): array
    {
        return [
            'memory_usage' => [
                'current' => memory_get_usage(true),
                'peak' => memory_get_peak_usage(true),
                'limit' => $this->parseBytes(ini_get('memory_limit')),
            ],
            'cpu_load' => sys_getloadavg(),
            'uptime' => $this->getUptime(),
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
        ];
    }

    /**
     * Parse bytes from PHP ini values.
     */
    protected function parseBytes(string $val): int
    {
        $val = trim($val);
        $last = strtolower($val[strlen($val) - 1]);
        $val = (int) $val;
        
        switch ($last) {
            case 'g':
                $val *= 1024;
            case 'm':
                $val *= 1024;
            case 'k':
                $val *= 1024;
        }
        
        return $val;
    }

    /**
     * Get system uptime.
     */
    protected function getUptime(): ?float
    {
        if (PHP_OS_FAMILY === 'Linux') {
            $uptime = file_get_contents('/proc/uptime');
            return $uptime ? (float) explode(' ', $uptime)[0] : null;
        }
        
        return null;
    }
}
