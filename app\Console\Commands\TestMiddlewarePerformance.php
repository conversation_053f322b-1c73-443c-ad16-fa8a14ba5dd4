<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use App\Services\MiddlewarePerformanceMonitor;

class TestMiddlewarePerformance extends Command
{
    protected $signature = 'test:middleware-performance {--requests=10 : Number of requests to make}';
    protected $description = 'Test middleware performance by making multiple requests';

    public function handle()
    {
        $requestCount = (int) $this->option('requests');
        $baseUrl = 'http://127.0.0.1:8000';
        
        $this->info("Testing middleware performance with {$requestCount} requests...");
        
        // Clear previous metrics
        MiddlewarePerformanceMonitor::clearMetrics();
        
        $routes = [
            '/en/',
            '/en/shop',
            '/en/about',
            '/en/contact',
            '/en/services'
        ];
        
        $startTime = microtime(true);
        $successCount = 0;
        $errorCount = 0;
        
        for ($i = 0; $i < $requestCount; $i++) {
            $route = $routes[array_rand($routes)];
            
            try {
                $response = Http::timeout(5)->get($baseUrl . $route);
                
                if ($response->successful()) {
                    $successCount++;
                    $this->line("✓ Request " . ($i + 1) . " to {$route} - " . $response->status());
                } else {
                    $errorCount++;
                    $this->error("✗ Request " . ($i + 1) . " to {$route} - " . $response->status());
                }
            } catch (\Exception $e) {
                $errorCount++;
                $this->error("✗ Request " . ($i + 1) . " to {$route} - Error: " . $e->getMessage());
            }
            
            // Small delay to avoid overwhelming the server
            usleep(100000); // 100ms
        }
        
        $totalTime = microtime(true) - $startTime;
        
        $this->newLine();
        $this->info("Performance Test Results:");
        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Requests', $requestCount],
                ['Successful Requests', $successCount],
                ['Failed Requests', $errorCount],
                ['Total Time', round($totalTime, 2) . 's'],
                ['Average Time per Request', round($totalTime / $requestCount, 3) . 's'],
                ['Requests per Second', round($requestCount / $totalTime, 2)],
            ]
        );
        
        // Show middleware performance metrics
        $this->newLine();
        $this->info("Middleware Performance Metrics:");
        
        $summary = MiddlewarePerformanceMonitor::getPerformanceSummary();
        
        if (empty($summary)) {
            $this->warn("No middleware performance data available.");
        } else {
            $this->table(
                ['Middleware', 'Operation', 'Requests', 'Avg Time (ms)', 'Max Time (ms)', 'Status'],
                array_map(function($item) {
                    return [
                        $item['middleware'],
                        $item['operation'],
                        $item['requests'],
                        $item['avg_time_ms'],
                        $item['max_time_ms'],
                        $item['is_slow'] ? '⚠️ SLOW' : '✅ OK'
                    ];
                }, $summary)
            );
        }
        
        // Recommendations
        $this->newLine();
        $slowMiddleware = array_filter($summary, fn($item) => $item['is_slow']);
        
        if (!empty($slowMiddleware)) {
            $this->warn("⚠️ Performance Issues Detected:");
            foreach ($slowMiddleware as $middleware) {
                $this->line("  - {$middleware['middleware']}: {$middleware['avg_time_ms']}ms average");
            }
            $this->newLine();
            $this->info("💡 Recommendations:");
            $this->line("  - Consider adding more caching");
            $this->line("  - Move heavy operations to background jobs");
            $this->line("  - Optimize database queries");
        } else {
            $this->info("✅ All middleware performing within acceptable limits!");
        }
        
        return 0;
    }
}
