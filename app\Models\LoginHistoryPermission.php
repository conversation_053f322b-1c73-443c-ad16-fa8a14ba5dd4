<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class LoginHistoryPermission extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'user_id',
        'granted_by_user_id',
        'access_level',
        'specific_permissions',
        'reason',
        'granted_at',
        'expires_at',
        'is_active',
        'admin_notes',
    ];

    protected $casts = [
        'specific_permissions' => 'array',
        'granted_at' => 'datetime',
        'expires_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = Str::uuid();
            }
            if (empty($model->granted_at)) {
                $model->granted_at = now();
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Get the user who has the permission.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who granted the permission.
     */
    public function grantedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'granted_by_user_id');
    }

    /**
     * Scope to get active permissions.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where(function ($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                    });
    }

    /**
     * Scope to get permissions by access level.
     */
    public function scopeByAccessLevel($query, string $level)
    {
        return $query->where('access_level', $level);
    }

    /**
     * Scope to get expired permissions.
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<=', now());
    }

    /**
     * Check if the permission is currently valid.
     */
    public function isValid(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        if ($this->expires_at && $this->expires_at->isPast()) {
            return false;
        }

        return true;
    }

    /**
     * Check if the permission has expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Get the permission level display name.
     */
    public function getAccessLevelDisplayAttribute(): string
    {
        return match ($this->access_level) {
            'none' => 'No Access',
            'partial' => 'Partial Access',
            'full' => 'Full Access',
            default => 'Unknown',
        };
    }

    /**
     * Get the permission level color for UI.
     */
    public function getAccessLevelColorAttribute(): string
    {
        return match ($this->access_level) {
            'none' => 'red',
            'partial' => 'yellow',
            'full' => 'green',
            default => 'gray',
        };
    }

    /**
     * Check if user has specific permission.
     */
    public function hasSpecificPermission(string $permission): bool
    {
        if (!$this->isValid()) {
            return false;
        }

        if ($this->access_level === 'full') {
            return true;
        }

        if ($this->access_level === 'none') {
            return false;
        }

        // For partial access, check specific permissions
        $permissions = $this->specific_permissions ?? [];
        return in_array($permission, $permissions);
    }

    /**
     * Get available specific permissions.
     */
    public static function getAvailableSpecificPermissions(): array
    {
        return [
            'view_basic_info' => 'View basic login information',
            'view_ip_addresses' => 'View IP addresses',
            'view_risk_scores' => 'View risk scores and security metadata',
            'view_device_details' => 'View detailed device information',
            'view_location_data' => 'View location and geolocation data',
            'view_session_data' => 'View session duration and details',
            'view_security_alerts' => 'View security alerts and flags',
            'export_data' => 'Export login history data',
            'view_statistics' => 'View login statistics and analytics',
            'manage_user_permissions' => 'Manage other users\' login history permissions',
        ];
    }

    /**
     * Get default permissions for partial access.
     */
    public static function getDefaultPartialPermissions(): array
    {
        return [
            'view_basic_info',
            'view_device_details',
            'view_location_data',
            'view_statistics',
        ];
    }

    /**
     * Revoke the permission.
     */
    public function revoke(): bool
    {
        return $this->update([
            'is_active' => false,
            'admin_notes' => ($this->admin_notes ?? '') . "\nRevoked at " . now()->toDateTimeString(),
        ]);
    }

    /**
     * Extend the permission expiration.
     */
    public function extend(int $days): bool
    {
        $newExpiration = $this->expires_at 
            ? $this->expires_at->addDays($days)
            : now()->addDays($days);

        return $this->update([
            'expires_at' => $newExpiration,
            'admin_notes' => ($this->admin_notes ?? '') . "\nExtended by {$days} days at " . now()->toDateTimeString(),
        ]);
    }

    /**
     * Update access level and permissions.
     */
    public function updateAccess(string $accessLevel, array $specificPermissions = null): bool
    {
        $data = [
            'access_level' => $accessLevel,
            'admin_notes' => ($this->admin_notes ?? '') . "\nAccess level changed to {$accessLevel} at " . now()->toDateTimeString(),
        ];

        if ($specificPermissions !== null) {
            $data['specific_permissions'] = $specificPermissions;
        } elseif ($accessLevel === 'partial') {
            $data['specific_permissions'] = self::getDefaultPartialPermissions();
        } elseif ($accessLevel === 'full') {
            $data['specific_permissions'] = array_keys(self::getAvailableSpecificPermissions());
        } else {
            $data['specific_permissions'] = [];
        }

        return $this->update($data);
    }

    /**
     * Get formatted granted date.
     */
    public function getFormattedGrantedAtAttribute(): string
    {
        return $this->granted_at->format('M j, Y g:i A');
    }

    /**
     * Get formatted expiration date.
     */
    public function getFormattedExpiresAtAttribute(): ?string
    {
        return $this->expires_at?->format('M j, Y g:i A');
    }

    /**
     * Get days until expiration.
     */
    public function getDaysUntilExpirationAttribute(): ?int
    {
        if (!$this->expires_at) {
            return null;
        }

        return now()->diffInDays($this->expires_at, false);
    }

    /**
     * Check if permission is expiring soon (within 7 days).
     */
    public function isExpiringSoon(): bool
    {
        if (!$this->expires_at) {
            return false;
        }

        return $this->expires_at->diffInDays(now()) <= 7;
    }
}
