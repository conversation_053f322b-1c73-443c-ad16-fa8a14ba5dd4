@extends('layouts.app')

@section('title', 'Unsubscribe from Newsletter')
@section('meta_description', 'Unsubscribe from our newsletter if you no longer wish to receive updates.')

@section('content')
<div class="min-h-screen bg-gray-50 py-12">
    <div class="container mx-auto px-4">
        <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-8">
            <div class="text-center mb-8">
                <h1 class="text-2xl font-bold text-gray-900 mb-2">Unsubscribe from Newsletter</h1>
                <p class="text-gray-600">We're sorry to see you go. You can unsubscribe from our newsletter below.</p>
            </div>

            <!-- Success/Error Messages -->
            <div id="message-container" class="mb-6 hidden">
                <div id="message" class="p-4 rounded-lg"></div>
            </div>

            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                    {{ session('error') }}
                </div>
            @endif

            @if(session('info'))
                <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-6">
                    {{ session('info') }}
                </div>
            @endif

            <form id="unsubscribe-form" action="{{ route('newsletter.unsubscribe') }}" method="POST" class="space-y-6">
                @csrf
                
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                        Email Address *
                    </label>
                    <input 
                        type="email" 
                        id="email" 
                        name="email" 
                        value="{{ old('email', $email) }}"
                        required
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('email') border-red-500 @enderror"
                        placeholder="Enter your email address"
                    >
                    @error('email')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                @if($token)
                    <input type="hidden" name="token" value="{{ $token }}">
                @endif

                <button 
                    type="submit" 
                    id="unsubscribe-btn"
                    class="w-full bg-red-600 text-white py-3 px-6 rounded-lg hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition duration-200 font-medium"
                >
                    Unsubscribe from Newsletter
                </button>
            </form>

            <div class="mt-8 text-center">
                <p class="text-sm text-gray-600 mb-4">
                    Changed your mind? You can always 
                    <a href="{{ route('home') }}#newsletter" class="text-blue-600 hover:text-blue-800 underline">
                        subscribe again
                    </a> 
                    later.
                </p>
                
                <div class="border-t pt-4">
                    <p class="text-xs text-gray-500">
                        If you're having trouble unsubscribing, please 
                        <a href="{{ route('contact') }}" class="text-blue-600 hover:text-blue-800 underline">
                            contact us
                        </a> 
                        for assistance.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('unsubscribe-form');
    const submitBtn = document.getElementById('unsubscribe-btn');
    const messageContainer = document.getElementById('message-container');
    const messageDiv = document.getElementById('message');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Show loading state
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = `
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Processing...
        `;
        submitBtn.disabled = true;
        
        // Hide previous messages
        messageContainer.classList.add('hidden');
        
        // Submit form via AJAX
        fetch(form.action, {
            method: 'POST',
            body: new FormData(form),
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            // Show message
            messageContainer.classList.remove('hidden');
            
            if (data.success) {
                messageDiv.className = 'p-4 rounded-lg bg-green-100 border border-green-400 text-green-700';
                messageDiv.textContent = data.message;
                
                // Clear form on success
                form.reset();
            } else {
                messageDiv.className = 'p-4 rounded-lg bg-red-100 border border-red-400 text-red-700';
                messageDiv.textContent = data.message || 'An error occurred. Please try again.';
                
                // Show field errors if any
                if (data.errors) {
                    let errorText = data.message + '\n\n';
                    Object.values(data.errors).forEach(errors => {
                        errors.forEach(error => {
                            errorText += '• ' + error + '\n';
                        });
                    });
                    messageDiv.textContent = errorText;
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            messageContainer.classList.remove('hidden');
            messageDiv.className = 'p-4 rounded-lg bg-red-100 border border-red-400 text-red-700';
            messageDiv.textContent = 'An unexpected error occurred. Please try again later.';
        })
        .finally(() => {
            // Restore button
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    });
});
</script>
@endsection
