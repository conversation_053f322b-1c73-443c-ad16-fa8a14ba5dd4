<?php

namespace App\Http\Controllers;

use App\Models\NewsletterSubscription;
use App\Services\VisitorAnalytics;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Validator;

class NewsletterController extends Controller
{
    protected VisitorAnalytics $visitorAnalytics;

    public function __construct(VisitorAnalytics $visitorAnalytics)
    {
        $this->visitorAnalytics = $visitorAnalytics;
    }

    /**
     * Handle newsletter subscription.
     */
    public function subscribe(Request $request): JsonResponse|RedirectResponse
    {
        try {
            // Log incoming request for debugging
            \Log::info('Newsletter subscription attempt', [
                'email' => $request->get('email'),
                'name' => $request->get('name'),
                'consent' => $request->get('consent'),
                'has_consent' => $request->has('consent'),
                'all_data' => $request->all(),
                'is_ajax' => $request->ajax(),
                'expects_json' => $request->expectsJson(),
            ]);

            $validator = Validator::make($request->all(), [
                'email' => 'required|email|max:255',
                'name' => 'nullable|string|max:255',
                'consent' => 'required|accepted',
            ]);

        if ($validator->fails()) {
            // Track failed newsletter subscription
            try {
                $this->visitorAnalytics->trackFormInteraction(
                    'newsletter_subscription',
                    'submit',
                    false,
                    [
                        'email' => $request->get('email'),
                        'has_name' => !empty($request->get('name')),
                        'consent_given' => $request->has('consent'),
                        'errors' => $validator->errors()->toArray(),
                    ]
                );
            } catch (\Exception $e) {
                \Log::warning('Failed to track newsletter validation failure: ' . $e->getMessage());
            }

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please check your input and try again.',
                    'errors' => $validator->errors()
                ], 422);
            }

            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $validated = $validator->validated();

        // Check if email already exists
        $existingSubscription = NewsletterSubscription::where('email', $validated['email'])->first();

        if ($existingSubscription) {
            if ($existingSubscription->is_active) {
                // Track duplicate subscription attempt
                $this->visitorAnalytics->trackFormInteraction(
                    'newsletter_subscription',
                    'submit',
                    false,
                    [
                        'email' => $validated['email'],
                        'error_type' => 'already_subscribed',
                        'existing_subscription_date' => $existingSubscription->created_at,
                    ]
                );

                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'You are already subscribed to our newsletter!',
                        'type' => 'info'
                    ]);
                }

                return redirect()->back()
                    ->with('info', 'You are already subscribed to our newsletter!');
            } else {
                // Reactivate existing subscription
                $existingSubscription->update([
                    'is_active' => true,
                    'name' => $validated['name'] ?? $existingSubscription->name ?? null,
                    'resubscribed_at' => now(),
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ]);

                // Track resubscription
                try {
                    $this->visitorAnalytics->trackFormInteraction(
                        'newsletter_subscription',
                        'resubscribe',
                        true,
                        [
                            'subscription_id' => $existingSubscription->id,
                            'email' => $validated['email'],
                            'original_subscription_date' => $existingSubscription->created_at,
                        ]
                    );
                } catch (\Exception $e) {
                    \Log::warning('Failed to track newsletter resubscription analytics: ' . $e->getMessage());
                }

                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Welcome back! Your newsletter subscription has been reactivated.',
                        'type' => 'reactivated'
                    ]);
                }

                return redirect()->back()
                    ->with('success', 'Welcome back! Your newsletter subscription has been reactivated.');
            }
        }

        // Create new subscription
        $subscription = NewsletterSubscription::create([
            'email' => $validated['email'],
            'name' => $validated['name'] ?? null,
            'is_active' => true,
            'subscribed_at' => now(),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'referrer' => $request->header('referer'),
            'utm_source' => $request->get('utm_source'),
            'utm_medium' => $request->get('utm_medium'),
            'utm_campaign' => $request->get('utm_campaign'),
        ]);

        // Track subscription history
        $subscription->trackHistory(
            action: 'subscribed',
            statusFrom: null,
            statusTo: 'active',
            description: 'User subscribed to newsletter',
            metadata: [
                'source' => 'website',
                'utm_source' => $request->get('utm_source'),
                'utm_medium' => $request->get('utm_medium'),
                'utm_campaign' => $request->get('utm_campaign'),
                'referrer' => $request->header('referer'),
            ],
            triggeredBy: 'user',
            ipAddress: $request->ip(),
            userAgent: $request->userAgent()
        );

        // Track successful newsletter subscription
        try {
            $this->visitorAnalytics->trackFormInteraction(
                'newsletter_subscription',
                'submit',
                true,
                [
                    'subscription_id' => $subscription->id,
                    'email' => $validated['email'],
                    'has_name' => !empty($validated['name'] ?? null),
                    'referrer' => $request->header('referer'),
                    'utm_source' => $request->get('utm_source'),
                    'utm_medium' => $request->get('utm_medium'),
                    'utm_campaign' => $request->get('utm_campaign'),
                ]
            );

            // Track conversion
            $this->visitorAnalytics->trackConversion(
                'newsletter_signup',
                [
                    'subscription_id' => $subscription->id,
                    'email' => $validated['email'],
                    'has_name' => !empty($validated['name'] ?? null),
                    'source_page' => $request->header('referer'),
                ]
            );

            // Update lead score
            $this->visitorAnalytics->updateLeadScore(
                'newsletter_signup',
                ['has_name' => !empty($validated['name'] ?? null)]
            );
        } catch (\Exception $e) {
            \Log::warning('Failed to track newsletter subscription analytics: ' . $e->getMessage());
        }

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Thank you for subscribing to our newsletter! You will receive updates about our latest insights and offers.',
                'type' => 'subscribed',
                'subscription_id' => $subscription->uuid
            ]);
        }

        return redirect()->back()
            ->with('success', 'Thank you for subscribing to our newsletter! You will receive updates about our latest insights and offers.');

        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('Newsletter subscription error: ' . $e->getMessage(), [
                'email' => $request->get('email'),
                'name' => $request->get('name'),
                'has_consent' => $request->has('consent'),
                'request_data' => $request->all(),
                'exception' => $e->getTraceAsString()
            ]);

            // Track failed subscription
            $this->visitorAnalytics->trackFormInteraction(
                'newsletter_subscription',
                'submit',
                false,
                [
                    'email' => $request->get('email'),
                    'has_name' => !empty($request->get('name')),
                    'consent_given' => $request->has('consent'),
                    'error_type' => 'exception',
                    'error_message' => $e->getMessage(),
                ]
            );

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'An error occurred while processing your subscription. Please try again.',
                    'error_code' => 'SUBSCRIPTION_ERROR'
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'An error occurred while processing your subscription. Please try again.');
        }
    }

    /**
     * Show the newsletter unsubscribe form.
     */
    public function showUnsubscribeForm(Request $request): View
    {
        // Track unsubscribe page visit
        $this->visitorAnalytics->trackPageVisit(
            'Newsletter Unsubscribe',
            [
                'email' => $request->get('email'),
                'token' => $request->get('token'),
                'referrer' => $request->header('referer'),
            ]
        );

        return view('newsletter.unsubscribe', [
            'email' => $request->get('email'),
            'token' => $request->get('token'),
        ]);
    }

    /**
     * Handle newsletter unsubscription.
     */
    public function unsubscribe(Request $request): JsonResponse|RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|max:255',
            'token' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please check your input and try again.',
                    'errors' => $validator->errors()
                ], 422);
            }

            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $validated = $validator->validated();

        $subscription = NewsletterSubscription::where('email', $validated['email'])
            ->where('is_active', true)
            ->first();

        if (!$subscription) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Email address not found in our newsletter list.',
                    'type' => 'not_found'
                ]);
            }

            return redirect()->back()
                ->with('info', 'Email address not found in our newsletter list.');
        }

        // Deactivate subscription
        $subscription->update([
            'is_active' => false,
            'unsubscribed_at' => now(),
            'unsubscribe_ip' => $request->ip(),
            'unsubscribe_user_agent' => $request->userAgent(),
        ]);

        // Track unsubscription history
        $subscription->trackHistory(
            action: 'unsubscribed',
            statusFrom: 'active',
            statusTo: 'inactive',
            description: 'User unsubscribed from newsletter',
            metadata: [
                'subscription_duration_days' => $subscription->subscribed_at->diffInDays(now()),
                'unsubscribe_method' => $request->get('token') ? 'email_link' : 'manual',
            ],
            triggeredBy: 'user',
            ipAddress: $request->ip(),
            userAgent: $request->userAgent()
        );

        // Track unsubscription
        $this->visitorAnalytics->trackFormInteraction(
            'newsletter_unsubscription',
            'submit',
            true,
            [
                'subscription_id' => $subscription->id,
                'email' => $validated['email'],
                'subscription_duration_days' => $subscription->subscribed_at->diffInDays(now()),
            ]
        );

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'You have been successfully unsubscribed from our newsletter.',
                'type' => 'unsubscribed'
            ]);
        }

        return redirect()->back()
            ->with('success', 'You have been successfully unsubscribed from our newsletter.');
    }
}
