<?php

namespace App\Jobs;

use App\Models\ChatWebhookDelivery;
use App\Services\ChatWebhookService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class DeliverChatWebhook implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 1; // We handle retries manually
    public int $timeout = 60;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public ChatWebhookDelivery $delivery
    ) {
        // Set queue based on priority
        $this->onQueue('webhooks');
    }

    /**
     * Execute the job.
     */
    public function handle(ChatWebhookService $webhookService): void
    {
        try {
            $success = $webhookService->deliverWebhook($this->delivery);

            if ($success) {
                Log::info('Webhook delivered successfully', [
                    'delivery_id' => $this->delivery->id,
                    'webhook_id' => $this->delivery->chat_webhook_id,
                    'event_type' => $this->delivery->event_type,
                ]);
            } else {
                Log::warning('Webhook delivery failed', [
                    'delivery_id' => $this->delivery->id,
                    'webhook_id' => $this->delivery->chat_webhook_id,
                    'event_type' => $this->delivery->event_type,
                    'attempts' => $this->delivery->attempts,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Webhook delivery job failed', [
                'delivery_id' => $this->delivery->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Mark as failed if not already handled by service
            if ($this->delivery->status === 'pending') {
                $this->delivery->markAsFailed($e->getMessage());
            }
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Webhook delivery job completely failed', [
            'delivery_id' => $this->delivery->id,
            'error' => $exception->getMessage(),
        ]);

        $this->delivery->markAsFailed($exception->getMessage());
    }
}
