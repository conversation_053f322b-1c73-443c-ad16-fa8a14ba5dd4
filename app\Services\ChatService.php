<?php

namespace App\Services;

use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\ChatParticipant;
use App\Models\ChatSystemSetting;
use App\Models\User;
use App\Events\Chat\WebhookEvent;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class ChatService
{
    protected ActivityLogger $activityLogger;
    protected PerformanceOptimizer $performanceOptimizer;
    protected DashboardCacheService $cacheService;

    public function __construct(
        ActivityLogger $activityLogger,
        PerformanceOptimizer $performanceOptimizer,
        DashboardCacheService $cacheService
    ) {
        $this->activityLogger = $activityLogger;
        $this->performanceOptimizer = $performanceOptimizer;
        $this->cacheService = $cacheService;
    }

    /**
     * Create a new chat room.
     */
    public function createRoom(array $data): ChatRoom
    {
        // Don't cache room creation as each request should create a unique room
        // return $this->performanceOptimizer->optimizeQuery(
        //     'chat_room_creation',
        //     function () use ($data) {

        // Direct execution without caching
        return DB::transaction(function () use ($data) {
                    $room = ChatRoom::create([
                        'type' => $data['type'] ?? 'visitor',
                        'title' => $data['title'] ?? null,
                        'visitor_info' => $data['visitor_info'] ?? null,
                        'metadata' => $data['metadata'] ?? null,
                        'priority' => $data['priority'] ?? 1,
                        'language' => $data['language'] ?? 'en',
                    ]);

                    // Log activity
                    $this->activityLogger->logActivity(
                        'chat_room_created',
                        "Chat room created: {$room->uuid}",
                        'success',
                        null,
                        [
                            'room_type' => $room->type,
                            'priority' => $room->priority,
                            'language' => $room->language,
                        ]
                    );

                    // Clear relevant caches
                    $this->clearRoomCaches();

                    // Trigger webhook event
                    event(new WebhookEvent('room.created', [
                        'room_id' => $room->id,
                        'room_uuid' => $room->uuid,
                        'type' => $room->type,
                        'priority' => $room->priority,
                        'language' => $room->language,
                        'visitor_info' => $room->visitor_info,
                        'created_at' => $room->created_at->toISOString(),
                    ]));

                    return $room;
                });
    }

    /**
     * Send a message to a chat room.
     */
    public function sendMessage(ChatRoom $room, array $data): ChatMessage
    {
        if (!ChatSystemSetting::isChatEnabled()) {
            throw new \Exception('Chat system is currently disabled');
        }

        return DB::transaction(function () use ($room, $data) {
            $message = ChatMessage::create([
                'chat_room_id' => $room->id,
                'user_id' => $data['user_id'] ?? auth()->id(),
                'message_type' => $data['message_type'] ?? 'text',
                'content' => $data['content'],
                'metadata' => $data['metadata'] ?? null,
                'is_ai_generated' => $data['is_ai_generated'] ?? false,
                'ai_confidence' => $data['ai_confidence'] ?? null,
                'ai_model' => $data['ai_model'] ?? null,
                'reply_to_message_id' => $data['reply_to_message_id'] ?? null,
            ]);

            // Update room's last activity
            $room->touch();

            // Log activity
            $this->activityLogger->logActivity(
                'chat_message_sent',
                "Message sent in room: {$room->uuid}",
                'success',
                null,
                [
                    'room_id' => $room->id,
                    'message_type' => $message->message_type,
                    'is_ai_generated' => $message->is_ai_generated,
                    'content_length' => strlen($message->content),
                ]
            );

            // Clear message caches for this room
            $this->clearMessageCaches($room->id);

            // Trigger webhook event
            event(new WebhookEvent('message.sent', [
                'message_id' => $message->id,
                'message_uuid' => $message->uuid,
                'room_id' => $room->id,
                'room_uuid' => $room->uuid,
                'user_id' => $message->user_id,
                'content' => $message->content,
                'message_type' => $message->message_type,
                'is_ai_generated' => $message->is_ai_generated,
                'ai_confidence' => $message->ai_confidence,
                'created_at' => $message->created_at->toISOString(),
            ]));

            return $message;
        });
    }

    /**
     * Add participant to chat room.
     */
    public function addParticipant(ChatRoom $room, array $data): ChatParticipant
    {
        return DB::transaction(function () use ($room, $data) {
            $participant = ChatParticipant::create([
                'chat_room_id' => $room->id,
                'user_id' => $data['user_id'] ?? null,
                'participant_type' => $data['participant_type'] ?? 'visitor',
                'role' => $data['role'] ?? 'participant',
                'display_name' => $data['display_name'] ?? null,
                'permissions' => $data['permissions'] ?? null,
            ]);

            // Log activity
            $this->activityLogger->logActivity(
                'chat_participant_added',
                "Participant added to room: {$room->uuid}",
                'success',
                null,
                [
                    'room_id' => $room->id,
                    'participant_type' => $participant->participant_type,
                    'role' => $participant->role,
                ]
            );

            // Clear participant caches
            $this->clearParticipantCaches($room->id);

            return $participant;
        });
    }

    /**
     * Get chat room with optimized loading.
     */
    public function getRoom(string $uuid): ?ChatRoom
    {
        return $this->cacheService->remember(
            "chat_room_{$uuid}",
            function () use ($uuid) {
                return ChatRoom::with([
                    'participants.user',
                    'currentAssignment.assignedStaff',
                    'lastMessage',
                    'rating'
                ])->where('uuid', $uuid)->first();
            },
            300 // 5 minutes
        );
    }

    /**
     * Get recent messages for a room with caching.
     */
    public function getRecentMessages(ChatRoom $room, int $limit = 50): Collection
    {
        return $this->cacheService->remember(
            "chat_messages_recent_{$room->id}_{$limit}",
            function () use ($room, $limit) {
                return $room->messages()
                          ->with(['user', 'files'])
                          ->orderBy('created_at', 'desc')
                          ->limit($limit)
                          ->get()
                          ->reverse()
                          ->values();
            },
            30 // 30 seconds for real-time data
        );
    }

    /**
     * Get active rooms with pagination and caching.
     */
    public function getActiveRooms(array $filters = [], int $perPage = 20): array
    {
        $cacheKey = 'chat_active_rooms_' . md5(serialize($filters)) . "_{$perPage}";
        
        return $this->cacheService->remember(
            $cacheKey,
            function () use ($filters, $perPage) {
                $query = ChatRoom::with([
                    'participants.user',
                    'currentAssignment.assignedStaff',
                    'lastMessage'
                ])->active();

                // Apply filters
                if (isset($filters['type'])) {
                    $query->where('type', $filters['type']);
                }

                if (isset($filters['priority'])) {
                    $query->where('priority', $filters['priority']);
                }

                if (isset($filters['language'])) {
                    $query->where('language', $filters['language']);
                }

                if (isset($filters['assigned_to'])) {
                    $query->whereHas('currentAssignment', function ($q) use ($filters) {
                        $q->where('assigned_to', $filters['assigned_to']);
                    });
                }

                return $query->orderBy('priority', 'desc')
                           ->orderBy('created_at', 'desc')
                           ->paginate($perPage)
                           ->toArray();
            },
            60 // 1 minute
        );
    }

    /**
     * Close a chat room.
     */
    public function closeRoom(ChatRoom $room, ?string $reason = null): bool
    {
        return DB::transaction(function () use ($room, $reason) {
            $success = $room->close();

            if ($success) {
                // Complete any active assignments
                $room->currentAssignment?->complete();

                // Log activity
                $this->activityLogger->logActivity(
                    'chat_room_closed',
                    "Chat room closed: {$room->uuid}",
                    'success',
                    null,
                    [
                        'reason' => $reason,
                        'duration_seconds' => $room->created_at->diffInSeconds(now()),
                        'message_count' => $room->messages()->count(),
                    ]
                );

                // Clear caches
                $this->clearRoomCaches();
                $this->clearMessageCaches($room->id);
            }

            return $success;
        });
    }

    /**
     * Get chat statistics with caching.
     */
    public function getStatistics(array $filters = []): array
    {
        $cacheKey = 'chat_statistics_' . md5(serialize($filters));
        
        return $this->cacheService->remember(
            $cacheKey,
            function () use ($filters) {
                $query = ChatRoom::query();

                // Apply date filters
                if (isset($filters['date_from'])) {
                    $query->where('created_at', '>=', $filters['date_from']);
                }

                if (isset($filters['date_to'])) {
                    $query->where('created_at', '<=', $filters['date_to']);
                }

                $totalRooms = $query->count();
                $activeRooms = $query->clone()->active()->count();
                $closedRooms = $query->clone()->closed()->count();

                $messageQuery = ChatMessage::query();
                if (isset($filters['date_from'])) {
                    $messageQuery->where('created_at', '>=', $filters['date_from']);
                }
                if (isset($filters['date_to'])) {
                    $messageQuery->where('created_at', '<=', $filters['date_to']);
                }

                $totalMessages = $messageQuery->count();
                $aiMessages = $messageQuery->clone()->where('is_ai_generated', true)->count();

                return [
                    'total_rooms' => $totalRooms,
                    'active_rooms' => $activeRooms,
                    'closed_rooms' => $closedRooms,
                    'total_messages' => $totalMessages,
                    'ai_messages' => $aiMessages,
                    'human_messages' => $totalMessages - $aiMessages,
                    'ai_usage_percentage' => $totalMessages > 0 ? round(($aiMessages / $totalMessages) * 100, 2) : 0,
                ];
            },
            300 // 5 minutes
        );
    }

    /**
     * Batch process messages for performance.
     */
    public function batchProcessMessages(array $messages): array
    {
        return $this->performanceOptimizer->optimizeQuery(
            'chat_messages_batch_' . time(),
            function () use ($messages) {
                return DB::transaction(function () use ($messages) {
                    $results = [];
                    
                    foreach (array_chunk($messages, 100) as $batch) {
                        $batchResults = [];
                        
                        foreach ($batch as $messageData) {
                            $room = ChatRoom::find($messageData['chat_room_id']);
                            if ($room) {
                                $batchResults[] = $this->sendMessage($room, $messageData);
                            }
                        }
                        
                        $results = array_merge($results, $batchResults);
                    }
                    
                    return $results;
                });
            },
            300
        );
    }

    /**
     * Clear room-related caches.
     */
    protected function clearRoomCaches(): void
    {
        // Check if cache store supports tags
        if ($this->supportsCacheTags()) {
            Cache::tags(['chat_rooms'])->flush();
        } else {
            // Fallback: Clear specific cache keys (less efficient but works)
            $this->clearSpecificRoomCaches();
        }
    }

    /**
     * Clear message-related caches for a room.
     */
    protected function clearMessageCaches(int $roomId): void
    {
        // Always clear specific message cache
        Cache::forget("chat_messages_recent_{$roomId}_50");

        if ($this->supportsCacheTags()) {
            Cache::tags(['chat_messages', "room_{$roomId}"])->flush();
        } else {
            // Clear other message-related caches for this room
            for ($limit = 10; $limit <= 100; $limit += 10) {
                Cache::forget("chat_messages_recent_{$roomId}_{$limit}");
            }
        }
    }

    /**
     * Clear participant-related caches for a room.
     */
    protected function clearParticipantCaches(int $roomId): void
    {
        if ($this->supportsCacheTags()) {
            Cache::tags(['chat_participants', "room_{$roomId}"])->flush();
        } else {
            // Clear specific participant caches
            Cache::forget("chat_participants_{$roomId}");
            Cache::forget("chat_room_participants_{$roomId}");
        }
    }

    /**
     * Check if the current cache store supports tags.
     */
    private function supportsCacheTags(): bool
    {
        $store = Cache::getStore();
        return $store instanceof \Illuminate\Cache\RedisStore ||
               $store instanceof \Illuminate\Cache\MemcachedStore;
    }

    /**
     * Clear specific room caches when tags are not supported.
     */
    private function clearSpecificRoomCaches(): void
    {
        // Clear active rooms cache with different filter combinations
        $filterCombinations = [
            [],
            ['type' => 'visitor'],
            ['type' => 'support'],
            ['priority' => 1],
            ['priority' => 2],
            ['priority' => 3],
        ];

        foreach ($filterCombinations as $filters) {
            for ($perPage = 10; $perPage <= 50; $perPage += 10) {
                $cacheKey = 'chat_active_rooms_' . md5(serialize($filters)) . "_{$perPage}";
                Cache::forget($cacheKey);
            }
        }
    }
}
