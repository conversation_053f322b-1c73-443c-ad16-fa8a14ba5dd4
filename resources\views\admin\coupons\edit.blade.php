@extends('layouts.dashboard')

@section('title', 'Edit Coupon - Admin Dashboard')
@section('page_title', 'Edit Coupon')

@section('content')
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Edit Coupon</h1>
            <p class="text-gray-600">Update coupon settings and configuration</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('admin.coupons.show', $coupon) }}" 
               class="inline-flex items-center px-4 py-2 bg-secondary-600 text-white rounded-lg hover:bg-secondary-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                </svg>
                View Coupon
            </a>
            <a href="{{ route('admin.coupons.index') }}" 
               class="inline-flex items-center px-4 py-2 bg-neutral-600 text-white rounded-lg hover:bg-neutral-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                Back to Coupons
            </a>
        </div>
    </div>

    <!-- Status Alert -->
    @if(!$coupon->is_active)
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">Coupon is currently inactive</h3>
                    <p class="mt-1 text-sm text-yellow-700">This coupon is not available for customers to use.</p>
                </div>
            </div>
        </div>
    @endif

    @if($coupon->expires_at && $coupon->expires_at->isPast())
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Coupon has expired</h3>
                    <p class="mt-1 text-sm text-red-700">This coupon expired on {{ $coupon->expires_at->format('M j, Y \a\t g:i A') }}.</p>
                </div>
            </div>
        </div>
    @endif

    <!-- Form Messages -->
    <div id="form-messages" class="hidden"></div>

    <!-- Form -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100">
        <form id="coupon-form" method="POST" action="{{ route('admin.coupons.update', $coupon) }}" class="p-6 space-y-8">
            @csrf
            @method('PUT')
            
            <!-- Basic Information -->
            <div class="space-y-6">
                <h3 class="text-lg font-semibold text-gray-900 border-b border-neutral-200 pb-2">
                    Basic Information
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Coupon Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            Coupon Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               id="name" 
                               name="name" 
                               value="{{ old('name', $coupon->name) }}"
                               class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('name') border-red-500 @enderror"
                               placeholder="Enter coupon name"
                               required>
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Coupon Code -->
                    <div>
                        <label for="code" class="block text-sm font-medium text-gray-700 mb-2">
                            Coupon Code <span class="text-red-500">*</span>
                        </label>
                        <div class="flex space-x-2">
                            <input type="text" 
                                   id="code" 
                                   name="code" 
                                   value="{{ old('code', $coupon->code) }}"
                                   class="flex-1 px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('code') border-red-500 @enderror"
                                   placeholder="Enter coupon code"
                                   required>
                            <button type="button" 
                                    id="generate-code" 
                                    class="px-4 py-2 bg-secondary-600 text-white rounded-lg hover:bg-secondary-700 transition-colors duration-200">
                                Generate
                            </button>
                        </div>
                        <p class="mt-1 text-xs text-gray-500">Customers will use this code at checkout</p>
                        @error('code')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                        Description
                    </label>
                    <textarea id="description" 
                              name="description" 
                              rows="3"
                              class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('description') border-red-500 @enderror"
                              placeholder="Describe this coupon">{{ old('description', $coupon->description) }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Discount Settings -->
            <div class="space-y-6">
                <h3 class="text-lg font-semibold text-gray-900 border-b border-neutral-200 pb-2">
                    Discount Settings
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Discount Type -->
                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 mb-2">
                            Discount Type <span class="text-red-500">*</span>
                        </label>
                        <select id="type" 
                                name="type" 
                                class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('type') border-red-500 @enderror"
                                required>
                            <option value="">Select discount type</option>
                            <option value="percentage" {{ old('type', $coupon->type) == 'percentage' ? 'selected' : '' }}>Percentage (%)</option>
                            <option value="fixed" {{ old('type', $coupon->type) == 'fixed' ? 'selected' : '' }}>Fixed Amount</option>
                        </select>
                        @error('type')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Discount Value -->
                    <div>
                        <label for="value" class="block text-sm font-medium text-gray-700 mb-2">
                            Discount Value <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input type="number" 
                                   id="value" 
                                   name="value" 
                                   value="{{ old('value', $coupon->value) }}"
                                   step="0.01"
                                   min="0"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('value') border-red-500 @enderror"
                                   placeholder="0.00"
                                   required>
                            <div id="value-suffix" class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 text-sm">
                                {{ old('type', $coupon->type) == 'percentage' ? '%' : 'R' }}
                            </div>
                        </div>
                        @error('value')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Maximum Discount -->
                    <div id="max-discount-field" style="{{ old('type', $coupon->type) == 'percentage' ? '' : 'display: none;' }}">
                        <label for="maximum_discount" class="block text-sm font-medium text-gray-700 mb-2">
                            Maximum Discount (R)
                        </label>
                        <input type="number" 
                               id="maximum_discount" 
                               name="maximum_discount" 
                               value="{{ old('maximum_discount', $coupon->maximum_discount) }}"
                               step="0.01"
                               min="0"
                               class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('maximum_discount') border-red-500 @enderror"
                               placeholder="0.00">
                        <p class="mt-1 text-xs text-gray-500">Leave empty for no limit</p>
                        @error('maximum_discount')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Minimum Amount -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="minimum_amount" class="block text-sm font-medium text-gray-700 mb-2">
                            Minimum Order Amount (R)
                        </label>
                        <input type="number" 
                               id="minimum_amount" 
                               name="minimum_amount" 
                               value="{{ old('minimum_amount', $coupon->minimum_amount) }}"
                               step="0.01"
                               min="0"
                               class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('minimum_amount') border-red-500 @enderror"
                               placeholder="0.00">
                        <p class="mt-1 text-xs text-gray-500">Leave empty for no minimum</p>
                        @error('minimum_amount')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Usage Limits -->
            <div class="space-y-6">
                <h3 class="text-lg font-semibold text-gray-900 border-b border-neutral-200 pb-2">
                    Usage Limits
                </h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Total Usage Limit -->
                    <div>
                        <label for="usage_limit" class="block text-sm font-medium text-gray-700 mb-2">
                            Total Usage Limit
                        </label>
                        <input type="number"
                               id="usage_limit"
                               name="usage_limit"
                               value="{{ old('usage_limit', $coupon->usage_limit) }}"
                               min="1"
                               class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('usage_limit') border-red-500 @enderror"
                               placeholder="Unlimited">
                        <p class="mt-1 text-xs text-gray-500">Leave empty for unlimited uses</p>
                        @error('usage_limit')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Usage Limit Per Customer -->
                    <div>
                        <label for="usage_limit_per_customer" class="block text-sm font-medium text-gray-700 mb-2">
                            Usage Limit Per Customer
                        </label>
                        <input type="number"
                               id="usage_limit_per_customer"
                               name="usage_limit_per_customer"
                               value="{{ old('usage_limit_per_customer', $coupon->usage_limit_per_customer) }}"
                               min="1"
                               class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('usage_limit_per_customer') border-red-500 @enderror"
                               placeholder="Unlimited">
                        <p class="mt-1 text-xs text-gray-500">Leave empty for unlimited uses per customer</p>
                        @error('usage_limit_per_customer')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Usage Statistics -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Current Usage Statistics</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                            <span class="text-gray-600">Times Used:</span>
                            <span class="font-medium text-gray-900">{{ $coupon->used_count }}</span>
                        </div>
                        @if($coupon->usage_limit)
                        <div>
                            <span class="text-gray-600">Remaining Uses:</span>
                            <span class="font-medium text-gray-900">{{ max(0, $coupon->usage_limit - $coupon->used_count) }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">Usage Rate:</span>
                            <span class="font-medium text-gray-900">{{ $coupon->usage_limit > 0 ? round(($coupon->used_count / $coupon->usage_limit) * 100, 1) : 0 }}%</span>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Validity Period -->
            <div class="space-y-6">
                <h3 class="text-lg font-semibold text-gray-900 border-b border-neutral-200 pb-2">
                    Validity Period
                </h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Start Date -->
                    <div>
                        <label for="starts_at" class="block text-sm font-medium text-gray-700 mb-2">
                            Start Date & Time
                        </label>
                        <input type="datetime-local"
                               id="starts_at"
                               name="starts_at"
                               value="{{ old('starts_at', $coupon->starts_at ? $coupon->starts_at->format('Y-m-d\TH:i') : '') }}"
                               class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('starts_at') border-red-500 @enderror">
                        <p class="mt-1 text-xs text-gray-500">Leave empty to start immediately</p>
                        @error('starts_at')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- End Date -->
                    <div>
                        <label for="expires_at" class="block text-sm font-medium text-gray-700 mb-2">
                            Expiry Date & Time
                        </label>
                        <input type="datetime-local"
                               id="expires_at"
                               name="expires_at"
                               value="{{ old('expires_at', $coupon->expires_at ? $coupon->expires_at->format('Y-m-d\TH:i') : '') }}"
                               class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('expires_at') border-red-500 @enderror">
                        <p class="mt-1 text-xs text-gray-500">Leave empty for no expiry</p>
                        @error('expires_at')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Product Restrictions -->
            <div class="space-y-6">
                <h3 class="text-lg font-semibold text-gray-900 border-b border-neutral-200 pb-2">
                    Product Restrictions
                </h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Applicable Products -->
                    <div>
                        <label for="applicable_products" class="block text-sm font-medium text-gray-700 mb-2">
                            Applicable Products
                        </label>
                        <select id="applicable_products"
                                name="applicable_products[]"
                                multiple
                                class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('applicable_products') border-red-500 @enderror"
                                size="6">
                            @foreach($products as $product)
                                <option value="{{ $product->id }}"
                                        {{ in_array($product->id, old('applicable_products', $coupon->applicable_products ?? [])) ? 'selected' : '' }}>
                                    {{ $product->name }}
                                </option>
                            @endforeach
                        </select>
                        <p class="mt-1 text-xs text-gray-500">Leave empty to apply to all products</p>
                        @error('applicable_products')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Exclude Products -->
                    <div>
                        <label for="exclude_products" class="block text-sm font-medium text-gray-700 mb-2">
                            Exclude Products
                        </label>
                        <select id="exclude_products"
                                name="exclude_products[]"
                                multiple
                                class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('exclude_products') border-red-500 @enderror"
                                size="6">
                            @foreach($products as $product)
                                <option value="{{ $product->id }}"
                                        {{ in_array($product->id, old('exclude_products', $coupon->exclude_products ?? [])) ? 'selected' : '' }}>
                                    {{ $product->name }}
                                </option>
                            @endforeach
                        </select>
                        <p class="mt-1 text-xs text-gray-500">Products that cannot use this coupon</p>
                        @error('exclude_products')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Applicable Categories -->
                    <div>
                        <label for="applicable_categories" class="block text-sm font-medium text-gray-700 mb-2">
                            Applicable Categories
                        </label>
                        <select id="applicable_categories"
                                name="applicable_categories[]"
                                multiple
                                class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('applicable_categories') border-red-500 @enderror"
                                size="4">
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}"
                                        {{ in_array($category->id, old('applicable_categories', $coupon->applicable_categories ?? [])) ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                        <p class="mt-1 text-xs text-gray-500">Leave empty to apply to all categories</p>
                        @error('applicable_categories')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Exclude Categories -->
                    <div>
                        <label for="exclude_categories" class="block text-sm font-medium text-gray-700 mb-2">
                            Exclude Categories
                        </label>
                        <select id="exclude_categories"
                                name="exclude_categories[]"
                                multiple
                                class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('exclude_categories') border-red-500 @enderror"
                                size="4">
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}"
                                        {{ in_array($category->id, old('exclude_categories', $coupon->exclude_categories ?? [])) ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                        <p class="mt-1 text-xs text-gray-500">Categories that cannot use this coupon</p>
                        @error('exclude_categories')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Status Settings -->
            <div class="space-y-6">
                <h3 class="text-lg font-semibold text-gray-900 border-b border-neutral-200 pb-2">
                    Status Settings
                </h3>

                <div class="flex items-center">
                    <input type="hidden" name="is_active" value="0">
                    <input type="checkbox"
                           id="is_active"
                           name="is_active"
                           value="1"
                           {{ old('is_active', $coupon->is_active) ? 'checked' : '' }}
                           class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded">
                    <label for="is_active" class="ml-2 block text-sm text-gray-900">
                        Active (customers can use this coupon)
                    </label>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-between pt-6 border-t border-neutral-200">
                <div class="flex space-x-3">
                    <a href="{{ route('admin.coupons.index') }}"
                       class="inline-flex items-center px-4 py-2 bg-neutral-600 text-white rounded-lg hover:bg-neutral-700 transition-colors duration-200">
                        Cancel
                    </a>
                </div>

                <div class="flex space-x-3">
                    <button type="submit"
                            id="submit-btn"
                            name="submit_action"
                            value="save"
                            class="inline-flex items-center px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        <span class="submit-text">Update Coupon</span>
                        <span class="loading-text hidden">Updating...</span>
                    </button>

                    <button type="submit"
                            id="submit-continue-btn"
                            name="submit_action"
                            value="save_and_continue"
                            class="inline-flex items-center px-6 py-2 bg-secondary-600 text-white rounded-lg hover:bg-secondary-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                        </svg>
                        <span class="submit-continue-text">Save & Continue Editing</span>
                        <span class="loading-continue-text hidden">Saving...</span>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const typeSelect = document.getElementById('type');
    const valueSuffix = document.getElementById('value-suffix');
    const maxDiscountField = document.getElementById('max-discount-field');
    const generateCodeBtn = document.getElementById('generate-code');
    const codeInput = document.getElementById('code');

    // Update value suffix and max discount field visibility based on discount type
    function updateDiscountFields() {
        const selectedType = typeSelect.value;

        if (selectedType === 'percentage') {
            valueSuffix.textContent = '%';
            maxDiscountField.style.display = 'block';
        } else if (selectedType === 'fixed') {
            valueSuffix.textContent = 'R';
            maxDiscountField.style.display = 'none';
        } else {
            valueSuffix.textContent = '';
            maxDiscountField.style.display = 'none';
        }
    }

    // Generate random coupon code
    function generateCouponCode() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 8; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    // Event listeners
    typeSelect.addEventListener('change', updateDiscountFields);

    generateCodeBtn.addEventListener('click', function() {
        codeInput.value = generateCouponCode();
    });

    // Initialize on page load
    updateDiscountFields();

    // AJAX Form Submission
    const form = document.getElementById('coupon-form');
    const submitBtn = document.getElementById('submit-btn');
    const submitContinueBtn = document.getElementById('submit-continue-btn');
    const submitText = submitBtn.querySelector('.submit-text');
    const loadingText = submitBtn.querySelector('.loading-text');
    const submitContinueText = submitContinueBtn.querySelector('.submit-continue-text');
    const loadingContinueText = submitContinueBtn.querySelector('.loading-continue-text');
    const messagesContainer = document.getElementById('form-messages');

    function handleFormSubmit(button, isSubmitBtn) {
        return async function(e) {
            e.preventDefault();

            // Show loading state
            button.disabled = true;
            if (isSubmitBtn) {
                submitText.classList.add('hidden');
                loadingText.classList.remove('hidden');
            } else {
                submitContinueText.classList.add('hidden');
                loadingContinueText.classList.remove('hidden');
            }

            // Clear previous messages
            messagesContainer.innerHTML = '';
            messagesContainer.classList.add('hidden');

            // Clear previous error states
            document.querySelectorAll('.border-red-500').forEach(el => {
                el.classList.remove('border-red-500');
            });
            document.querySelectorAll('.text-red-600').forEach(el => {
                if (el.classList.contains('error-message')) {
                    el.remove();
                }
            });

            try {
                const formData = new FormData(form);
                // Remove any existing submit_action field to avoid conflicts
                formData.delete('submit_action');
                // Add the specific action value from the clicked button
                formData.append('submit_action', button.getAttribute('value'));

                const response = await fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    // Show success message
                    messagesContainer.innerHTML = `
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-green-800">${data.message}</p>
                                </div>
                            </div>
                        </div>
                    `;
                    messagesContainer.classList.remove('hidden');

                    // Redirect after a short delay (only for save, not save & continue)
                    if (isSubmitBtn) {
                        setTimeout(() => {
                            window.location.href = data.redirect;
                        }, 1500);
                    }
                } else {
                    // Show error message
                    messagesContainer.innerHTML = `
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-red-800">${data.message}</p>
                                </div>
                            </div>
                        </div>
                    `;
                    messagesContainer.classList.remove('hidden');

                    // Show field errors
                    if (data.errors) {
                        Object.keys(data.errors).forEach(field => {
                            const input = document.querySelector(`[name="${field}"]`);
                            if (input) {
                                input.classList.add('border-red-500');

                                // Add error message
                                const errorDiv = document.createElement('p');
                                errorDiv.className = 'mt-1 text-sm text-red-600 error-message';
                                errorDiv.textContent = data.errors[field][0];
                                input.parentNode.appendChild(errorDiv);
                            }
                        });
                    }
                }
            } catch (error) {
                console.error('Error:', error);
                messagesContainer.innerHTML = `
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-red-800">An error occurred. Please try again.</p>
                            </div>
                        </div>
                    </div>
                `;
                messagesContainer.classList.remove('hidden');
            } finally {
                // Reset button state
                button.disabled = false;
                if (isSubmitBtn) {
                    submitText.classList.remove('hidden');
                    loadingText.classList.add('hidden');
                } else {
                    submitContinueText.classList.remove('hidden');
                    loadingContinueText.classList.add('hidden');
                }
            }
        };
    }

    // Re-enable AJAX form submission
    submitBtn.addEventListener('click', handleFormSubmit(submitBtn, true));
    submitContinueBtn.addEventListener('click', handleFormSubmit(submitContinueBtn, false));
});
</script>
@endpush
@endsection
