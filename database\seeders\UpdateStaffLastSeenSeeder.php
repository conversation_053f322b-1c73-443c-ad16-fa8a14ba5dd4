<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\User;

class UpdateStaffLastSeenSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Update all staff and admin users to have a recent last_seen_at timestamp
        $updated = User::staff()->update(['last_seen_at' => now()]);
        
        $this->command->info("Updated {$updated} staff members with last_seen_at timestamp.");
    }
}
