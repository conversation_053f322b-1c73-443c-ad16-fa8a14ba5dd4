@import "tailwindcss";
@import "./professional-carousel.css";
@import "./chat-widget.css";
@import "./admin.css";

/* Custom CSS Variables for Colors */
:root {
  /* Primary Colors */
  --color-primary-50: #f8fafc;
  --color-primary-100: #f1f5f9;
  --color-primary-200: #e2e8f0;
  --color-primary-300: #cbd5e1;
  --color-primary-400: #94a3b8;
  --color-primary-500: #64748b;
  --color-primary-600: #1e3a8a;
  --color-primary-700: #1e40af;
  --color-primary-800: #1e293b;
  --color-primary-900: #0f172a;

  /* Secondary Colors */
  --color-secondary-50: #f0fdf4;
  --color-secondary-100: #dcfce7;
  --color-secondary-200: #bbf7d0;
  --color-secondary-300: #86efac;
  --color-secondary-400: #4ade80;
  --color-secondary-500: #22c55e;
  --color-secondary-600: #166534;
  --color-secondary-700: #14532d;
  --color-secondary-800: #052e16;
  --color-secondary-900: #022c22;

  /* Accent Colors */
  --color-accent-50: #eff6ff;
  --color-accent-100: #dbeafe;
  --color-accent-200: #bfdbfe;
  --color-accent-300: #93c5fd;
  --color-accent-400: #60a5fa;
  --color-accent-500: #3b82f6;
  --color-accent-600: #1d4ed8;
  --color-accent-700: #1e40af;
  --color-accent-800: #1e3a8a;
  --color-accent-900: #172554;
}

/* Custom CSS */
html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', sans-serif;
}

/* Flexible container system - allows both contained and full-width layouts */
.container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 box-border;
}

/* Button Components */
.btn-primary {
  background-color: #2563eb;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: #1d4ed8;
}

.btn-secondary {
  background-color: #4b5563;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.btn-secondary:hover {
  background-color: #374151;
}

.btn-outline {
  border: 2px solid #2563eb;
  color: #2563eb;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.btn-outline:hover {
  background-color: #2563eb;
  color: white;
}

/* Card Components */
.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.card-hover {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  transition: box-shadow 0.2s;
}

.card-hover:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Global Floating Label Form Styles */
.floating-input-group {
  position: relative;
  margin-bottom: 2rem;
}

.floating-input {
  width: 100%;
  padding: 1.25rem 0 0.75rem 0;
  border: none;
  border-bottom: 2px solid #e5e7eb;
  background: transparent;
  outline: none;
  font-size: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #374151;
  font-weight: 400;
}

.floating-input:focus {
  border-bottom-color: #3b82f6;
  box-shadow: 0 1px 0 0 #3b82f6;
}

.floating-input:focus ~ .floating-label,
.floating-input:not(:placeholder-shown) ~ .floating-label,
.floating-input.has-value ~ .floating-label {
  transform: translateY(-1.75rem) scale(0.85);
  color: #3b82f6;
  font-weight: 500;
}

.floating-label {
  position: absolute;
  top: 1.25rem;
  left: 0;
  font-size: 1rem;
  color: #9ca3af;
  pointer-events: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: left top;
  background: transparent;
  z-index: 1;
}

/* Select specific styles */
.floating-input-group select.floating-input {
  padding: 1.25rem 2.5rem 0.75rem 0;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.25em 1.25em;
  cursor: pointer;
}

.floating-input-group select.floating-input:focus ~ .floating-label,
.floating-input-group select.floating-input:valid ~ .floating-label,
.floating-input-group select.floating-input.has-value ~ .floating-label {
  transform: translateY(-1.75rem) scale(0.85);
  color: #3b82f6;
  font-weight: 500;
}

/* Textarea specific styles */
.floating-input-group textarea.floating-input {
  min-height: 140px;
  resize: vertical;
  padding-top: 1.75rem;
  line-height: 1.5;
}

/* Hover effects */
.floating-input-group:hover .floating-input:not(:focus) {
  border-bottom-color: #d1d5db;
}

.floating-input-group:hover .floating-label {
  color: #6b7280;
}

/* Error states */
.floating-input.error {
  border-bottom-color: #ef4444;
}

.floating-input.error ~ .floating-label {
  color: #ef4444;
}

/* Success states */
.floating-input.success {
  border-bottom-color: #10b981;
}

.floating-input.success ~ .floating-label {
  color: #10b981;
}

/* Disabled states */
.floating-input:disabled {
  border-bottom-color: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
}

.floating-input:disabled ~ .floating-label {
  color: #d1d5db;
}

/* Legacy form styles for backward compatibility */
.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Navigation Components */
.nav-link {
  color: #374151;
  font-weight: 500;
  transition: color 0.2s;
}

.nav-link:hover {
  color: #2563eb;
}

.nav-link-active {
  color: #2563eb;
  font-weight: 500;
}

/* Text Components */
.heading-1 {
  font-size: 2.25rem;
  font-weight: 700;
  color: #111827;
  line-height: 1.2;
}

@media (min-width: 1024px) {
  .heading-1 {
    font-size: 3rem;
  }
}

.heading-2 {
  font-size: 1.875rem;
  font-weight: 700;
  color: #111827;
  line-height: 1.2;
}

@media (min-width: 1024px) {
  .heading-2 {
    font-size: 2.25rem;
  }
}

.text-lead {
  font-size: 1.125rem;
  color: #4b5563;
  line-height: 1.6;
}

@media (min-width: 1024px) {
  .text-lead {
    font-size: 1.25rem;
  }
}

/* Custom utilities */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-blue {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

/* Animation utilities */
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% { transform: translateY(20px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
  0% { transform: translateY(-20px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes scaleIn {
  0% { transform: scale(0.95); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

.animate-slide-down {
  animation: slideDown 0.5s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
}

/* Filter and Category Buttons */
.filter-btn, .category-btn {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
  background-color: #f3f4f6;
  color: #4b5563;
}

.filter-btn:hover, .category-btn:hover {
  background-color: #e5e7eb;
}

.filter-btn.active, .category-btn.active {
  background-color: #2563eb;
  color: white;
}

/* Project and Blog Card Animations */
.project-card, .blog-card {
  transition: transform 0.3s ease;
}

.project-card:hover, .blog-card:hover {
  transform: translateY(-4px);
}

/* Navigation Active States */
.nav-link {
  position: relative;
  transition: all 0.3s ease;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #2563eb, #3b82f6);
  transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

.nav-link:hover {
  color: #2563eb;
  transform: translateY(-1px);
}

.nav-link.active {
  color: #2563eb;
  font-weight: 600;
}

/* Mobile Navigation */
.mobile-nav-link {
  position: relative;
  transition: all 0.3s ease;
}

.mobile-nav-link::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  height: 100%;
  background: linear-gradient(90deg, #2563eb, #3b82f6);
  opacity: 0.1;
  transition: width 0.3s ease;
}

.mobile-nav-link:hover::before,
.mobile-nav-link.active::before {
  width: 4px;
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
  color: #2563eb;
  background-color: #eff6ff;
}

/* Dashboard Styles */
.nav-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #6b7280;
    text-decoration: none;
    border-radius: 0.5rem;
    transition: all 0.2s;
    font-weight: 500;
    gap: 0.75rem;
}

.nav-item:hover {
    background-color: #f3f4f6;
    color: #374151;
}

.nav-item.active {
    background-color: #eff6ff;
    color: #2563eb;
    font-weight: 600;
}

.nav-item-active {
    background-color: #eff6ff;
    color: #2563eb;
    font-weight: 600;
    position: relative;
}

.nav-item-active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 1rem;
    right: 1rem;
    height: 2px;
    background-color: #2563eb;
    border-radius: 1px;
}

.nav-item svg {
    flex-shrink: 0;
}

/* Dashboard Cards */
.dashboard-card {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    transition: box-shadow 0.2s;
}

.dashboard-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.dashboard-stat {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.dashboard-stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dashboard-stat-content h3 {
    font-size: 1.875rem;
    font-weight: 700;
    color: #111827;
    margin: 0;
}

.dashboard-stat-content p {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0;
}

/* Recent Activity */
.activity-item {
    display: flex;
    align-items: start;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.activity-content h4 {
    font-weight: 600;
    color: #111827;
    margin: 0 0 0.25rem 0;
    font-size: 0.875rem;
}

.activity-content p {
    color: #6b7280;
    font-size: 0.75rem;
    margin: 0;
}

/* Quick Actions */
.quick-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem;
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    text-decoration: none;
    transition: all 0.2s;
    text-align: center;
}

.quick-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.quick-action-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.quick-action h3 {
    font-weight: 600;
    color: #111827;
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
}

.quick-action p {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0;
}

/* Hide scrollbar but keep functionality */
.scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
}

/* Ensure sidebar scrolling works properly */
#dashboard-sidebar {
    height: 100vh;
    overflow: hidden;
}

#dashboard-sidebar nav {
    overflow-y: auto;
    overflow-x: hidden;
    max-height: calc(100vh - 200px); /* Adjust based on header and profile section height */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
}

#dashboard-sidebar nav::-webkit-scrollbar {
    display: none; /* Safari and Chrome */
}

/* Featured Projects Carousel Styles */
.carousel-track {
    display: flex;
    transition: transform 0.5s ease-in-out;
}

.carousel-slide {
    flex: 0 0 auto;
    width: 100%;
}

@media (min-width: 768px) {
    .carousel-slide {
        width: 50%;
    }
}

@media (min-width: 1024px) {
    .carousel-slide {
        width: 33.333333%;
    }
}

.carousel-prev,
.carousel-next {
    transition: all 0.2s ease-in-out;
    z-index: 10;
}

.carousel-prev:hover,
.carousel-next:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.carousel-prev:disabled,
.carousel-next:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.carousel-indicator {
    transition: all 0.2s ease-in-out;
    cursor: pointer;
}

.carousel-indicator:hover {
    transform: scale(1.2);
}

.carousel-indicator.active {
    transform: scale(1.1);
}

/* Responsive carousel navigation positioning */
@media (max-width: 767px) {
    .carousel-prev,
    .carousel-next {
        display: none;
    }
}

/* Smooth hover effects for project cards in carousel */
#featured-projects-carousel .carousel-slide .group {
    transition: all 0.3s ease-in-out;
}

#featured-projects-carousel .carousel-slide .group:hover {
    transform: translateY(-2px);
}

/* Ensure proper aspect ratio for project images */
#featured-projects-carousel .aspect-w-16 {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

#featured-projects-carousel .aspect-w-16 > * {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

/* Fix for invisible admin buttons - Direct CSS approach using CSS variables */
.bg-primary-50 {
    background-color: var(--color-primary-50, #f8fafc) !important;
}

.bg-primary-100 {
    background-color: var(--color-primary-100, #f1f5f9) !important;
}

.bg-primary-600 {
    background-color: var(--color-primary-600, #1e3a8a) !important;
}

.bg-primary-700 {
    background-color: var(--color-primary-700, #1e40af) !important;
}

.hover\:bg-primary-700:hover {
    background-color: var(--color-primary-700, #1e40af) !important;
}

.bg-secondary-50 {
    background-color: var(--color-secondary-50, #f0fdf4) !important;
}

.bg-secondary-100 {
    background-color: var(--color-secondary-100, #dcfce7) !important;
}

.bg-secondary-600 {
    background-color: var(--color-secondary-600, #166534) !important;
}

.bg-secondary-700 {
    background-color: var(--color-secondary-700, #14532d) !important;
}

.hover\:bg-secondary-700:hover {
    background-color: var(--color-secondary-700, #14532d) !important;
}

.bg-accent-600 {
    background-color: var(--color-accent-600, #1d4ed8) !important;
}

.bg-accent-700 {
    background-color: var(--color-accent-700, #1e40af) !important;
}

.hover\:bg-accent-700:hover {
    background-color: var(--color-accent-700, #1e40af) !important;
}

.bg-success-600 {
    background-color: #16a34a !important;
}

.bg-success-700 {
    background-color: #15803d !important;
}

.hover\:bg-success-700:hover {
    background-color: #15803d !important;
}

/* Text colors using CSS variables */
.text-primary-600 {
    color: var(--color-primary-600, #1e3a8a) !important;
}

.text-primary-700 {
    color: var(--color-primary-700, #1e40af) !important;
}

.text-primary-800 {
    color: var(--color-primary-800, #1e293b) !important;
}

.text-primary-900 {
    color: var(--color-primary-900, #0f172a) !important;
}

.text-secondary-600 {
    color: var(--color-secondary-600, #166534) !important;
}

.text-secondary-700 {
    color: var(--color-secondary-700, #14532d) !important;
}

.text-secondary-800 {
    color: var(--color-secondary-800, #052e16) !important;
}

/* Ensure text is visible */
.text-white {
    color: #ffffff !important;
}

/* Comprehensive fixes for white-on-white visibility issues */

/* Fix hover states that might cause white text on white background */
.hover\:bg-white:hover {
  background-color: #f8fafc !important;
  color: #1f2937 !important;
}

.hover\:text-white:hover {
  color: #ffffff !important;
}

/* Ensure button hover states are visible */
button:hover,
.btn:hover,
a.btn:hover {
  color: inherit !important;
}

/* Fix for buttons with white backgrounds on hover */
.bg-white:hover,
.hover\:bg-white:hover {
  background-color: #f9fafb !important;
  color: #111827 !important;
}

/* Fix navigation hover states */
.nav-link:hover {
  color: #2563eb !important;
}

/* Fix mobile navigation hover states */
.mobile-nav-link:hover {
  color: #2563eb !important;
  background-color: #eff6ff !important;
}

/* Additional Edge browser fixes for hover states */
@supports (-ms-ime-align: auto) {
  /* Edge-specific hover fixes */
  .hover\:bg-white:hover {
    background-color: #f8fafc !important;
    color: #1f2937 !important;
  }

  .hover\:text-white:hover {
    color: #ffffff !important;
  }

  /* Ensure button hover states are visible */
  button:hover,
  .btn:hover,
  a.btn:hover {
    color: inherit !important;
  }

  /* Fix for buttons with white backgrounds on hover */
  .bg-white:hover,
  .hover\:bg-white:hover {
    background-color: #f9fafb !important;
    color: #111827 !important;
  }

  /* Fix navigation hover states */
  .nav-link:hover {
    color: #2563eb !important;
  }

  /* Fix mobile navigation hover states */
  .mobile-nav-link:hover {
    color: #2563eb !important;
    background-color: #eff6ff !important;
  }
}

/* Additional color utilities for all custom colors */
.bg-accent-600 {
    background-color: #1d4ed8 !important;
}

.bg-accent-700 {
    background-color: #1e40af !important;
}

.hover\:bg-accent-700:hover {
    background-color: #1e40af !important;
}

.bg-danger-600 {
    background-color: #dc2626 !important;
}

.bg-danger-700 {
    background-color: #b91c1c !important;
}

.hover\:bg-danger-700:hover {
    background-color: #b91c1c !important;
}

.bg-warning-600 {
    background-color: #d97706 !important;
}

.bg-warning-700 {
    background-color: #b45309 !important;
}

.hover\:bg-warning-700:hover {
    background-color: #b45309 !important;
}

.bg-info-600 {
    background-color: #0284c7 !important;
}

.bg-info-700 {
    background-color: #0369a1 !important;
}

.hover\:bg-info-700:hover {
    background-color: #0369a1 !important;
}

.bg-neutral-600 {
    background-color: #525252 !important;
}

.bg-neutral-700 {
    background-color: #404040 !important;
}

.hover\:bg-neutral-700:hover {
    background-color: #404040 !important;
}

/* Text colors */
.text-primary-600 {
    color: #1e3a8a !important;
}

.text-primary-700 {
    color: #1e40af !important;
}

.text-primary-800 {
    color: #1e293b !important;
}

.text-primary-900 {
    color: #0f172a !important;
}

.text-secondary-600 {
    color: #166534 !important;
}

.text-secondary-700 {
    color: #14532d !important;
}

.text-secondary-800 {
    color: #052e16 !important;
}

.text-success-600 {
    color: #16a34a !important;
}

.text-success-700 {
    color: #15803d !important;
}

.text-success-800 {
    color: #166534 !important;
}

.text-danger-500 {
    color: #ef4444 !important;
}

.text-danger-600 {
    color: #dc2626 !important;
}

.text-danger-800 {
    color: #991b1b !important;
}

.text-warning-600 {
    color: #d97706 !important;
}

.text-warning-800 {
    color: #92400e !important;
}

.text-neutral-600 {
    color: #525252 !important;
}

.text-neutral-700 {
    color: #404040 !important;
}

/* Border colors */
.border-primary-500 {
    border-color: #64748b !important;
}

.border-primary-600 {
    border-color: #1e3a8a !important;
}

.border-secondary-500 {
    border-color: #22c55e !important;
}

.border-secondary-600 {
    border-color: #166534 !important;
}

.border-neutral-200 {
    border-color: #e5e5e5 !important;
}

.border-neutral-300 {
    border-color: #d4d4d4 !important;
}

.border-red-500 {
    border-color: #ef4444 !important;
}

/* Background colors for badges and alerts */
.bg-primary-50 {
    background-color: #f8fafc !important;
}

.bg-primary-100 {
    background-color: #f1f5f9 !important;
}

.bg-secondary-50 {
    background-color: #f0fdf4 !important;
}

.bg-secondary-100 {
    background-color: #dcfce7 !important;
}

.bg-success-50 {
    background-color: #f0fdf4 !important;
}

.bg-success-100 {
    background-color: #dcfce7 !important;
}

.bg-danger-50 {
    background-color: #fef2f2 !important;
}

.bg-danger-100 {
    background-color: #fee2e2 !important;
}

.bg-warning-50 {
    background-color: #fffbeb !important;
}

.bg-warning-100 {
    background-color: #fef3c7 !important;
}

.bg-neutral-50 {
    background-color: #fafafa !important;
}

.bg-neutral-100 {
    background-color: #f5f5f5 !important;
}

/* Focus ring colors */
.focus\:ring-primary-500:focus {
    --tw-ring-color: #64748b !important;
    box-shadow: 0 0 0 2px var(--tw-ring-color) !important;
}

.focus\:ring-secondary-500:focus {
    --tw-ring-color: #22c55e !important;
    box-shadow: 0 0 0 2px var(--tw-ring-color) !important;
}

.focus\:ring-danger-500:focus {
    --tw-ring-color: #ef4444 !important;
    box-shadow: 0 0 0 2px var(--tw-ring-color) !important;
}

.focus\:ring-success-500:focus {
    --tw-ring-color: #22c55e !important;
    box-shadow: 0 0 0 2px var(--tw-ring-color) !important;
}

/* Hover background colors */
.hover\:bg-primary-50:hover {
    background-color: #f8fafc !important;
}

.hover\:bg-neutral-50:hover {
    background-color: #fafafa !important;
}

/* Disabled states */
.disabled\:bg-primary-300:disabled {
    background-color: #cbd5e1 !important;
}

.disabled\:bg-secondary-300:disabled {
    background-color: #86efac !important;
}

.disabled\:bg-neutral-300:disabled {
    background-color: #d4d4d4 !important;
}

/* General hover state fixes for better visibility */
.hover\:bg-blue-50:hover {
    background-color: #eff6ff !important;
    color: #1e40af !important;
}

.hover\:text-blue-600:hover {
    color: #2563eb !important;
}

.hover\:text-blue-700:hover {
    color: #1d4ed8 !important;
}

/* Fix for any potential white-on-white issues */
.bg-white .text-white,
.hover\:bg-white:hover .text-white {
    color: #111827 !important;
}

/* Ensure proper contrast for all button states */
.btn-primary:hover,
.bg-blue-600:hover {
    background-color: #1d4ed8 !important;
    color: #ffffff !important;
}

.btn-secondary:hover,
.bg-gray-600:hover {
    background-color: #4b5563 !important;
    color: #ffffff !important;
}

/* Fix for testimonial and other card hover states */
.group:hover .group-hover\:text-white {
    color: #ffffff !important;
}

.group:hover .group-hover\:bg-blue-600 {
    background-color: #2563eb !important;
}

/* Fix newsletter input text visibility */
#footer-newsletter-form input[type="email"] {
    color: #ffffff !important;
    background-color: #1f2937 !important;
    border-color: #374151 !important;
}

#footer-newsletter-form input[type="email"]:focus {
    color: #ffffff !important;
    background-color: #1f2937 !important;
    border-color: #3b82f6 !important;
    outline: none !important;
}

#footer-newsletter-form input[type="email"]::placeholder {
    color: #9ca3af !important;
}

@theme {
    --font-sans: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
}
