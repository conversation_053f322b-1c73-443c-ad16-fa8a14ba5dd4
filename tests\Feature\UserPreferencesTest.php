<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\UserPreference;
use App\Models\Role;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class UserPreferencesTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin role
        $adminRole = Role::firstOrCreate([
            'name' => 'admin',
            'slug' => 'admin',
            'description' => 'Administrator role',
            'is_active' => true,
        ]);

        // Create a test user with admin role
        $this->user = User::factory()->create([
            'role_id' => $adminRole->id,
        ]);

        $this->actingAs($this->user);
    }

    public function test_user_can_access_preferences_page(): void
    {
        $response = $this->get(route('admin.preferences.index'));

        $response->assertStatus(200);

        // Just check that we get a successful response with preferences content
        $content = $response->getContent();
        $this->assertStringContainsString('User Preferences', $content);
        $this->assertStringContainsString('General Preferences', $content);
        $this->assertStringContainsString('language', $content);
        $this->assertStringContainsString('timezone', $content);
    }

    public function test_user_preferences_are_created_automatically(): void
    {
        $preferences = $this->user->getPreferences();

        $this->assertInstanceOf(UserPreference::class, $preferences);
        $this->assertEquals($this->user->id, $preferences->user_id);
        $this->assertNotNull($preferences->uuid);
        $this->assertEquals('en', $preferences->language);
        $this->assertEquals('UTC', $preferences->timezone);
    }

    public function test_user_can_update_preferences(): void
    {
        $updateData = [
            'language' => 'af',
            'timezone' => 'Africa/Johannesburg',
            'currency' => 'ZAR',
            'theme' => 'dark',
            'items_per_page' => 50,
            'email_notifications' => false,
            'chat_sound_enabled' => true,
        ];

        $response = $this->postJson(route('admin.preferences.update'), $updateData);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $preferences = $this->user->fresh()->getPreferences();
        $this->assertEquals('af', $preferences->language);
        $this->assertEquals('Africa/Johannesburg', $preferences->timezone);
        $this->assertEquals('ZAR', $preferences->currency);
        $this->assertEquals('dark', $preferences->theme);
        $this->assertEquals(50, $preferences->items_per_page);
        $this->assertFalse($preferences->email_notifications);
        $this->assertTrue($preferences->chat_sound_enabled);
    }

    public function test_user_can_update_single_preference(): void
    {
        $response = $this->postJson(route('admin.preferences.update-single'), [
            'key' => 'max_concurrent_chats',
            'value' => 15,
        ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $preferences = $this->user->fresh()->getPreferences();
        $this->assertEquals(15, $preferences->max_concurrent_chats);
    }

    public function test_user_cannot_update_invalid_preference(): void
    {
        $response = $this->postJson(route('admin.preferences.update-single'), [
            'key' => 'invalid_key',
            'value' => 'some_value',
        ]);

        $response->assertStatus(400);
        $response->assertJson(['success' => false]);
    }

    public function test_user_can_reset_preferences(): void
    {
        // First, update some preferences
        $preferences = $this->user->getPreferences();
        $preferences->update([
            'language' => 'af',
            'theme' => 'dark',
            'items_per_page' => 100,
        ]);

        // Then reset them
        $response = $this->postJson(route('admin.preferences.reset'));

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $preferences = $this->user->fresh()->getPreferences();
        $this->assertEquals('en', $preferences->language);
        $this->assertEquals('light', $preferences->theme);
        $this->assertEquals(25, $preferences->items_per_page);
    }

    public function test_user_can_export_preferences(): void
    {
        $response = $this->getJson(route('admin.preferences.export'));

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        $response->assertJsonStructure([
            'success',
            'preferences',
            'exported_at',
            'user' => ['id', 'name', 'email'],
        ]);
    }

    public function test_preferences_validation_works(): void
    {
        $response = $this->postJson(route('admin.preferences.update'), [
            'language' => 'invalid_language',
            'timezone' => 'invalid_timezone',
            'items_per_page' => 999, // Invalid value
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['language', 'timezone', 'items_per_page']);
    }

    public function test_quiet_hours_functionality(): void
    {
        $preferences = $this->user->getPreferences();
        $preferences->update([
            'quiet_hours_start' => '22:00',
            'quiet_hours_end' => '08:00',
        ]);

        // Test the isInQuietHours method
        $this->assertIsBool($preferences->isInQuietHours());
    }

    public function test_available_options_methods(): void
    {
        $timezones = UserPreference::getAvailableTimezones();
        $currencies = UserPreference::getAvailableCurrencies();
        $languages = UserPreference::getAvailableLanguages();

        $this->assertIsArray($timezones);
        $this->assertIsArray($currencies);
        $this->assertIsArray($languages);

        $this->assertArrayHasKey('UTC', $timezones);
        $this->assertArrayHasKey('USD', $currencies);
        $this->assertArrayHasKey('en', $languages);
    }

    public function test_user_preferences_relationship(): void
    {
        $preferences = $this->user->getPreferences();

        $this->assertEquals($this->user->id, $preferences->user->id);
        $this->assertEquals($this->user->name, $preferences->user->name);
    }

    public function test_preferences_use_uuid_as_route_key(): void
    {
        $preferences = $this->user->getPreferences();

        $this->assertEquals('uuid', $preferences->getRouteKeyName());
        $this->assertNotNull($preferences->getRouteKey());
        $this->assertIsString($preferences->getRouteKey());
    }
}
