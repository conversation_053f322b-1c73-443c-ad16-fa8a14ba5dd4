<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\DashboardCacheService;
use App\Services\ChatService;
use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Mockery;
use PHPUnit\Framework\Attributes\Test;

class DashboardCacheServiceIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected DashboardCacheService $cacheService;
    protected ChatService $chatService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->cacheService = app(DashboardCacheService::class);
        $this->chatService = app(ChatService::class);
        
        // Clear cache before each test
        Cache::flush();
    }

    #[Test]
    public function dashboard_cache_service_has_required_methods()
    {
        $this->assertTrue(method_exists($this->cacheService, 'remember'));
        $this->assertTrue(method_exists($this->cacheService, 'forget'));
        $this->assertTrue(method_exists($this->cacheService, 'put'));
    }

    #[Test]
    public function remember_method_works_correctly()
    {
        $key = 'test_key';
        $value = 'test_value';
        $ttl = 300;

        $result = $this->cacheService->remember($key, function () use ($value) {
            return $value;
        }, $ttl);

        $this->assertEquals($value, $result);
        $this->assertEquals($value, Cache::get($key));
    }

    #[Test]
    public function remember_method_returns_cached_value_on_subsequent_calls()
    {
        $key = 'test_key';
        $callCount = 0;

        // First call
        $result1 = $this->cacheService->remember($key, function () use (&$callCount) {
            $callCount++;
            return 'computed_value';
        }, 300);

        // Second call should return cached value without calling the callback
        $result2 = $this->cacheService->remember($key, function () use (&$callCount) {
            $callCount++;
            return 'new_computed_value';
        }, 300);

        $this->assertEquals('computed_value', $result1);
        $this->assertEquals('computed_value', $result2);
        $this->assertEquals(1, $callCount); // Callback should only be called once
    }

    #[Test]
    public function forget_method_removes_cached_value()
    {
        $key = 'test_key';
        $value = 'test_value';

        // Cache a value
        $this->cacheService->put($key, $value, 300);
        $this->assertEquals($value, Cache::get($key));

        // Forget the value
        $result = $this->cacheService->forget($key);

        $this->assertTrue($result);
        $this->assertNull(Cache::get($key));
    }

    #[Test]
    public function put_method_stores_value_in_cache()
    {
        $key = 'test_key';
        $value = 'test_value';
        $ttl = 300;

        $result = $this->cacheService->put($key, $value, $ttl);

        $this->assertTrue($result);
        $this->assertEquals($value, Cache::get($key));
    }

    #[Test]
    public function chat_service_can_use_dashboard_cache_service()
    {
        $user = User::factory()->create();
        $room = ChatRoom::factory()->create();
        
        // Create some messages
        ChatMessage::factory()->count(3)->create([
            'chat_room_id' => $room->id,
            'user_id' => $user->id,
        ]);

        // First call should hit the database and cache the result
        $messages1 = $this->chatService->getRecentMessages($room, 10);
        
        // Second call should return cached result
        $messages2 = $this->chatService->getRecentMessages($room, 10);

        $this->assertEquals($messages1->count(), $messages2->count());
        $this->assertEquals($messages1->pluck('id')->toArray(), $messages2->pluck('id')->toArray());
    }

    #[Test]
    public function chat_service_caching_respects_different_parameters()
    {
        $user = User::factory()->create();
        $room = ChatRoom::factory()->create();
        
        // Create 5 messages
        ChatMessage::factory()->count(5)->create([
            'chat_room_id' => $room->id,
            'user_id' => $user->id,
        ]);

        // Get recent messages with different limits
        $messages3 = $this->chatService->getRecentMessages($room, 3);
        $messages5 = $this->chatService->getRecentMessages($room, 5);

        $this->assertEquals(3, $messages3->count());
        $this->assertEquals(5, $messages5->count());
        $this->assertNotEquals($messages3->count(), $messages5->count());
    }

    #[Test]
    public function chat_service_caching_respects_different_rooms()
    {
        $user = User::factory()->create();
        $room1 = ChatRoom::factory()->create();
        $room2 = ChatRoom::factory()->create();
        
        // Create messages for room1
        ChatMessage::factory()->count(2)->create([
            'chat_room_id' => $room1->id,
            'user_id' => $user->id,
        ]);

        // Create messages for room2
        ChatMessage::factory()->count(3)->create([
            'chat_room_id' => $room2->id,
            'user_id' => $user->id,
        ]);

        $room1Messages = $this->chatService->getRecentMessages($room1, 10);
        $room2Messages = $this->chatService->getRecentMessages($room2, 10);

        $this->assertEquals(2, $room1Messages->count());
        $this->assertEquals(3, $room2Messages->count());
    }

    #[Test]
    public function cache_keys_are_properly_namespaced()
    {
        $room = ChatRoom::factory()->create();
        
        // Call the method to cache the result
        $this->chatService->getRecentMessages($room, 10);

        // Check that the cache key exists with proper format
        $expectedKey = "chat_messages_recent_{$room->id}_10";
        $this->assertTrue(Cache::has($expectedKey));
    }

    #[Test]
    public function cache_service_handles_null_values()
    {
        $key = 'null_test_key';
        
        $result = $this->cacheService->remember($key, function () {
            return null;
        }, 300);

        $this->assertNull($result);
        // Laravel cache doesn't store null values by default, so the key won't exist
        $this->assertFalse(Cache::has($key));
    }

    #[Test]
    public function cache_service_handles_complex_data_structures()
    {
        $key = 'complex_data_key';
        $complexData = [
            'array' => [1, 2, 3],
            'object' => (object) ['prop' => 'value'],
            'nested' => [
                'level1' => [
                    'level2' => 'deep_value'
                ]
            ]
        ];

        $result = $this->cacheService->remember($key, function () use ($complexData) {
            return $complexData;
        }, 300);

        $this->assertEquals($complexData, $result);
        $this->assertEquals($complexData, Cache::get($key));
    }

    #[Test]
    public function cache_service_respects_ttl()
    {
        $key = 'ttl_test_key';
        $value = 'ttl_test_value';
        $shortTtl = 1; // 1 second

        $this->cacheService->put($key, $value, $shortTtl);
        $this->assertEquals($value, Cache::get($key));

        // Wait for cache to expire (in a real test, you might mock time)
        sleep(2);
        
        $this->assertNull(Cache::get($key));
    }

    protected function tearDown(): void
    {
        Cache::flush();
        parent::tearDown();
    }
}
