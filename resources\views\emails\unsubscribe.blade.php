<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unsubscribe - {{ config('app.name') }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div class="text-center">
            <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
                Unsubscribe from our emails
            </h2>
            <p class="mt-2 text-sm text-gray-600">
                We're sorry to see you go! You can unsubscribe from our email list below.
            </p>
        </div>

        <div class="bg-white rounded-lg shadow-md p-8">
            <div class="mb-6">
                <div class="flex items-center justify-center w-12 h-12 mx-auto bg-yellow-100 rounded-full">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-9-7a9 9 0 1118 0 9 9 0 01-18 0z"/>
                    </svg>
                </div>
                <h3 class="mt-4 text-lg font-medium text-gray-900 text-center">
                    Confirm Unsubscribe
                </h3>
                <p class="mt-2 text-sm text-gray-600 text-center">
                    Email: <strong>{{ $subscription->email }}</strong>
                </p>
            </div>

            <form method="POST" action="{{ route('email.unsubscribe', $token) }}" class="space-y-6">
                @csrf
                
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-9-7a9 9 0 1118 0 9 9 0 01-18 0z"/>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">
                                Before you go...
                            </h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p>You'll no longer receive:</p>
                                <ul class="list-disc list-inside mt-1 space-y-1">
                                    <li>Newsletter updates</li>
                                    <li>Product announcements</li>
                                    <li>Special offers and promotions</li>
                                    <li>Important account notifications</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Why are you unsubscribing? (Optional)
                        </label>
                        <select name="unsubscribe_reason" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <option value="">Select a reason...</option>
                            <option value="too_frequent">Emails are too frequent</option>
                            <option value="not_relevant">Content is not relevant</option>
                            <option value="never_signed_up">I never signed up for this</option>
                            <option value="spam">This is spam</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <div>
                        <label for="feedback" class="block text-sm font-medium text-gray-700 mb-2">
                            Additional feedback (Optional)
                        </label>
                        <textarea id="feedback" 
                                  name="feedback" 
                                  rows="3" 
                                  placeholder="Help us improve by sharing your feedback..."
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"></textarea>
                    </div>
                </div>

                <div class="flex flex-col space-y-3">
                    <button type="submit" 
                            class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                        Yes, unsubscribe me
                    </button>
                    
                    <a href="{{ url('/') }}" 
                       class="w-full flex justify-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        Keep me subscribed
                    </a>
                </div>
            </form>

            <div class="mt-6 pt-6 border-t border-gray-200">
                <p class="text-xs text-gray-500 text-center">
                    If you change your mind, you can always 
                    <a href="{{ route('newsletter.subscribe') }}" class="text-primary-600 hover:text-primary-700">
                        resubscribe here
                    </a>.
                </p>
            </div>
        </div>

        <div class="text-center">
            <p class="text-xs text-gray-500">
                © {{ date('Y') }} {{ config('app.name') }}. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
