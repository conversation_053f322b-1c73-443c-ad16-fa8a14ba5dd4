@extends('layouts.app')

@section('title', 'Search Results' . ($query ? ' for "' . $query . '"' : '') . ' - Blog')
@section('meta_description', 'Search our blog for articles, tutorials, and insights' . ($query ? ' related to "' . $query . '"' : '') . '.')

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-blue text-white overflow-hidden">
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
    </div>
    
    <div class="container mx-auto px-4 py-20 lg:py-32 relative z-10">
        <div class="text-center max-w-4xl mx-auto">
            <!-- Breadcrumb -->
            <nav class="mb-8">
                <ol class="flex items-center justify-center space-x-2 text-sm text-blue-200">
                    <li><a href="{{ route('home') }}" class="hover:text-white transition-colors">Home</a></li>
                    <li><span class="mx-2">/</span></li>
                    <li><a href="{{ route('blog.index') }}" class="hover:text-white transition-colors">Blog</a></li>
                    <li><span class="mx-2">/</span></li>
                    <li class="text-white">Search</li>
                </ol>
            </nav>

            <h1 class="heading-1 text-white mb-6">
                @if($query)
                    Search Results for <span class="text-blue-300">"{{ $query }}"</span>
                @else
                    <span class="text-blue-300">Search</span> Blog
                @endif
            </h1>
            
            @if($query && $posts->count() > 0)
                <p class="text-lead text-blue-100 mb-8">
                    Found {{ $posts->total() }} {{ Str::plural('result', $posts->total()) }} for "{{ $query }}"
                </p>
            @elseif($query)
                <p class="text-lead text-blue-100 mb-8">
                    No results found for "{{ $query }}". Try different keywords or browse our categories.
                </p>
            @else
                <p class="text-lead text-blue-100 mb-8">
                    Search our blog for articles, tutorials, and insights on web development, mobile apps, and digital marketing.
                </p>
            @endif

            <!-- Search Form -->
            <form method="GET" action="{{ route('blog.search') }}" class="max-w-2xl mx-auto">
                <div class="flex gap-4">
                    <input type="text" 
                           name="q" 
                           value="{{ $query }}" 
                           placeholder="Search articles, tutorials, insights..." 
                           class="form-input flex-1 text-gray-900 bg-white border-white">
                    <button type="submit" class="btn-primary bg-white text-blue-600 hover:bg-blue-50 whitespace-nowrap">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Search
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <div class="absolute bottom-0 left-0 w-full">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block w-full h-16 fill-white">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
        </svg>
    </div>
</section>

<!-- Search Results -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        @if($posts->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($posts as $post)
                    <article class="blog-card card-hover bg-white rounded-lg shadow-lg overflow-hidden">
                        <div class="relative overflow-hidden">
                            @if($post->featured_image)
                                <picture>
                                    @if($post->getWebPImageUrl($post->featured_image))
                                        <source srcset="{{ $post->getWebPImageUrl($post->featured_image) }}" type="image/webp">
                                    @endif
                                    <img src="{{ $post->getOptimizedImageUrl($post->featured_image, 'medium') }}" 
                                         alt="{{ $post->title }}" 
                                         class="w-full h-48 object-cover hover-lift">
                                </picture>
                            @elseif($post->first_gallery_image_url)
                                <picture>
                                    @if($post->getWebPImageUrl($post->gallery_images[0]))
                                        <source srcset="{{ $post->getWebPImageUrl($post->gallery_images[0]) }}" type="image/webp">
                                    @endif
                                    <img src="{{ $post->getOptimizedImageUrl($post->gallery_images[0], 'medium') }}" 
                                         alt="{{ $post->title }}" 
                                         class="w-full h-48 object-cover hover-lift">
                                </picture>
                            @else
                                <div class="w-full h-48 bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                                    <svg class="w-12 h-12 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                    </svg>
                                </div>
                            @endif
                            
                            @if($post->total_image_count > 1)
                                <div class="absolute top-2 right-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded-full flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                                    </svg>
                                    {{ $post->total_image_count }}
                                </div>
                            @endif
                        </div>
                        
                        <div class="p-6 space-y-3">
                            <div class="flex items-center space-x-2">
                                @if($post->category)
                                    <span class="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded">{{ $post->category->name }}</span>
                                @endif
                                @if($post->is_featured)
                                    <span class="px-2 py-1 bg-yellow-100 text-yellow-600 text-xs rounded">Featured</span>
                                @endif
                                <span class="text-gray-500 text-xs">{{ $post->formatted_published_date }}</span>
                            </div>
                            
                            <h3 class="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors">
                                <a href="{{ route('blog.show', $post->slug) }}">{{ $post->title }}</a>
                            </h3>
                            
                            <p class="text-gray-600 text-sm leading-relaxed">
                                {{ $post->excerpt }}
                            </p>
                            
                            @if($post->services()->count() > 0)
                                <div class="flex flex-wrap gap-1">
                                    @foreach($post->services()->take(2) as $service)
                                        <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">{{ $service->name }}</span>
                                    @endforeach
                                    @if($post->services()->count() > 2)
                                        <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">+{{ $post->services()->count() - 2 }} more</span>
                                    @endif
                                </div>
                            @endif
                            
                            <div class="flex items-center justify-between pt-3">
                                <div class="flex items-center space-x-2">
                                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                                        <span class="text-white font-semibold text-xs">{{ substr($post->author->first_name, 0, 1) }}{{ substr($post->author->last_name, 0, 1) }}</span>
                                    </div>
                                    <span class="text-sm text-gray-600">{{ $post->author->first_name }} {{ $post->author->last_name }}</span>
                                </div>
                                <div class="flex items-center space-x-2 text-xs text-gray-500">
                                    <span>{{ $post->reading_time }} min read</span>
                                    @if($post->view_count > 0)
                                        <span>•</span>
                                        <span>{{ number_format($post->view_count) }} views</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </article>
                @endforeach
            </div>
            
            <!-- Pagination -->
            @if($posts->hasPages())
                <div class="flex justify-center mt-12">
                    {{ $posts->appends(['q' => $query])->links('pagination::tailwind') }}
                </div>
            @endif
        @elseif($query)
            <div class="text-center py-12">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">No Results Found</h3>
                <p class="text-gray-600 mb-6">We couldn't find any articles matching "{{ $query }}". Try different keywords or browse our categories.</p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('blog.index') }}" class="btn-secondary">Browse All Articles</a>
                    <a href="{{ route('blog.featured') }}" class="btn-primary">View Featured Posts</a>
                </div>
            </div>
        @endif
    </div>
</section>

@if(!$query || $posts->count() == 0)
    <!-- Popular Categories -->
    @php
        $popularCategories = \App\Models\BlogCategory::active()
            ->withCount(['posts' => function($query) {
                $query->where('is_published', true)->where('is_deleted', false);
            }])
            ->having('posts_count', '>', 0)
            ->orderBy('posts_count', 'desc')
            ->limit(6)
            ->get();
    @endphp

    @if($popularCategories->count() > 0)
        <section class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <div class="text-center mb-12">
                    <h2 class="heading-2 mb-4">
                        Browse by <span class="text-blue-600">Category</span>
                    </h2>
                </div>
                
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                    @foreach($popularCategories as $category)
                        <a href="{{ route('blog.category', $category->slug) }}" 
                           class="group p-4 bg-gray-50 rounded-lg hover:bg-blue-50 hover:border-blue-200 border border-transparent transition-all duration-200 text-center">
                            <h3 class="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors mb-1">
                                {{ $category->name }}
                            </h3>
                            <p class="text-sm text-gray-500">
                                {{ $category->posts_count }} {{ Str::plural('article', $category->posts_count) }}
                            </p>
                        </a>
                    @endforeach
                </div>
            </div>
        </section>
    @endif
@endif

@push('styles')
<style>
.blog-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.blog-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
</style>
@endpush
@endsection
