<?php

namespace App\Console\Commands;

use App\Models\Role;
use Illuminate\Console\Command;

class AddNewsletterPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permissions:add-newsletter {--staff-only : Only add to staff role} {--force : Add permissions without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add newsletter management permissions to roles';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $staffOnly = $this->option('staff-only');
        $force = $this->option('force');

        // Add newsletter permissions to admin role (if not staff-only)
        if (!$staffOnly) {
            $adminRole = Role::where('slug', 'admin')->first();
            if ($adminRole) {
                $permissions = $adminRole->permissions ?? [];
                $permissions['newsletter'] = ['create', 'read', 'update', 'delete', 'manage'];
                $permissions['contact_submissions'] = ['create', 'read', 'update', 'delete', 'manage'];

                $adminRole->update(['permissions' => $permissions]);
                $this->info('Added newsletter and contact submission permissions to admin role.');
            }
        }

        // Add newsletter permissions to staff role
        $staffRole = Role::where('slug', 'staff')->first();
        if ($staffRole) {
            $permissions = $staffRole->permissions ?? [];

            // Ask if we should add newsletter permissions to staff
            if ($force || $this->confirm('Add newsletter management permissions to staff role?')) {
                $permissions['newsletter'] = ['read', 'manage'];
                $this->info('Added newsletter permissions to staff role.');
            }

            // Ask if we should add contact submission permissions to staff
            if ($force || $this->confirm('Add contact submission management permissions to staff role?')) {
                $permissions['contact_submissions'] = ['read', 'manage'];
                $this->info('Added contact submission permissions to staff role.');
            }

            $staffRole->update(['permissions' => $permissions]);
        }

        // Add permissions to customer role for viewing their own data
        $customerRole = Role::where('slug', 'customer')->first();
        if ($customerRole && !$staffOnly) {
            $permissions = $customerRole->permissions ?? [];
            $permissions['newsletter'] = ['create']; // Can subscribe
            $permissions['contact_submissions'] = ['create']; // Can submit contact forms
            
            $customerRole->update(['permissions' => $permissions]);
            $this->info('Added basic newsletter and contact permissions to customer role.');
        }

        $this->info('Newsletter permissions have been updated successfully!');
        
        // Show current permissions
        $this->info("\nCurrent role permissions:");
        $roles = Role::all();
        foreach ($roles as $role) {
            $this->line("- {$role->name}: " . json_encode($role->permissions));
        }
    }
}
