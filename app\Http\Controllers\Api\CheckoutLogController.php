<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\ActivityLogger;
use App\Services\VisitorAnalytics;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class CheckoutLogController extends Controller
{
    protected ActivityLogger $activityLogger;
    protected VisitorAnalytics $visitorAnalytics;

    public function __construct(ActivityLogger $activityLogger, VisitorAnalytics $visitorAnalytics)
    {
        $this->activityLogger = $activityLogger;
        $this->visitorAnalytics = $visitorAnalytics;
    }

    /**
     * Log checkout activity from client-side
     */
    public function logActivity(Request $request): JsonResponse
    {
        try {
            // Log the incoming request for debugging
            Log::debug('Checkout log request received:', [
                'request_data' => $request->all(),
                'headers' => $request->headers->all()
            ]);

            $request->validate([
                'log_type' => 'required|string|in:error,interaction,form_validation_error,batch',
                'log_data' => 'required|array'
            ]);

            $logType = $request->input('log_type');
            $logData = $request->input('log_data');

            switch ($logType) {
                case 'error':
                    $this->handleErrorLog($logData);
                    break;
                
                case 'interaction':
                    $this->handleInteractionLog($logData);
                    break;
                
                case 'form_validation_error':
                    $this->handleFormValidationErrorLog($logData);
                    break;
                
                case 'batch':
                    $this->handleBatchLog($logData);
                    break;
            }

            return response()->json(['success' => true]);

        } catch (\Exception $e) {
            Log::error('Failed to process checkout log', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json(['success' => false, 'error' => 'Failed to process log'], 500);
        }
    }

    /**
     * Handle error logs from client-side
     */
    protected function handleErrorLog(array $logData): void
    {
        $errorType = $logData['type'] ?? 'unknown_error';
        $errorData = $logData['data'] ?? [];
        
        $description = $this->getErrorDescription($errorType, $errorData);
        
        $this->activityLogger->logCheckoutActivity(
            'client_error',
            [
                'error_type' => $errorType,
                'client_session_id' => $logData['session_id'] ?? null,
                'page_url' => $logData['page_url'] ?? null,
                'error_details' => $errorData,
                'client_timestamp' => $logData['timestamp'] ?? null,
                'source' => 'client_side'
            ],
            false,
            $description,
            null,
            []
        );

        // Track error in visitor analytics
        // Note: Temporarily disabled for API requests due to session issues
        // TODO: Implement session-less visitor analytics for API requests
        /*
        $this->visitorAnalytics->trackError(
            $errorType,
            $description, // Use the description as the error message
            [
                'error_details' => $errorData,
                'page_url' => $logData['page_url'] ?? null
            ]
        );
        */
    }

    /**
     * Handle interaction logs from client-side
     */
    protected function handleInteractionLog(array $logData): void
    {
        $interactionType = $logData['type'] ?? 'unknown_interaction';
        $interactionData = $logData['data'] ?? [];
        
        // Only log important interactions to avoid spam
        $importantInteractions = [
            'form_submit_attempt',
            'page_unload',
            'payment_error',
            'page_load'
        ];

        if (in_array($interactionType, $importantInteractions)) {
            $this->activityLogger->logCheckoutActivity(
                'client_interaction',
                [
                    'interaction_type' => $interactionType,
                    'client_session_id' => $logData['session_id'] ?? null,
                    'interaction_details' => $interactionData,
                    'client_timestamp' => $logData['timestamp'] ?? null,
                    'source' => 'client_side'
                ],
                true,
                null,
                null,
                []
            );

            // Track specific interactions in visitor analytics
            // Note: Temporarily disabled for API requests due to session issues
            /*
            if ($interactionType === 'form_submit_attempt') {
                $this->visitorAnalytics->trackFormInteraction(
                    'checkout_form',
                    'submit_attempt',
                    true,
                    $interactionData
                );
            } elseif ($interactionType === 'page_load') {
                $this->visitorAnalytics->trackPageVisit('Checkout Page Loaded', array_merge($interactionData, [
                    'page_type' => 'checkout_client',
                    'source' => 'client_side'
                ]));
            }
            */
        }
    }

    /**
     * Handle form validation error logs from client-side
     */
    protected function handleFormValidationErrorLog(array $logData): void
    {
        $fieldName = $logData['field_name'] ?? 'unknown_field';
        $validationData = $logData['data'] ?? [];
        
        $this->activityLogger->logCheckoutActivity(
            'client_form_validation',
            [
                'field_name' => $fieldName,
                'client_session_id' => $logData['session_id'] ?? null,
                'validation_details' => $validationData,
                'client_timestamp' => $logData['timestamp'] ?? null,
                'source' => 'client_side'
            ],
            false,
            "Client-side validation failed for field: {$fieldName}",
            null,
            [$fieldName => [$validationData['validation_message'] ?? 'Validation failed']]
        );

        // Track form validation errors
        // Note: Temporarily disabled for API requests due to session issues
        /*
        $this->visitorAnalytics->trackFormInteraction(
            'checkout_form',
            'validation_error',
            false,
            [
                'field_name' => $fieldName,
                'validation_message' => $validationData['validation_message'] ?? 'Unknown error'
            ]
        );
        */
    }

    /**
     * Handle batch logs from client-side
     */
    protected function handleBatchLog(array $logData): void
    {
        $interactions = $logData['interactions'] ?? [];
        $errors = $logData['errors'] ?? [];
        $formValidationErrors = $logData['form_validation_errors'] ?? [];
        
        // Process batch of interactions
        if (!empty($interactions)) {
            $this->activityLogger->logCheckoutActivity(
                'client_interaction_batch',
                [
                    'client_session_id' => $logData['session_id'] ?? null,
                    'interactions_count' => count($interactions),
                    'interactions_summary' => $this->summarizeInteractions($interactions),
                    'client_timestamp' => $logData['timestamp'] ?? null,
                    'source' => 'client_side'
                ],
                true,
                null,
                null,
                []
            );
        }

        // Process batch of errors
        if (!empty($errors)) {
            $this->activityLogger->logCheckoutActivity(
                'client_error_batch',
                [
                    'client_session_id' => $logData['session_id'] ?? null,
                    'errors_count' => count($errors),
                    'errors_summary' => $this->summarizeErrors($errors),
                    'client_timestamp' => $logData['timestamp'] ?? null,
                    'source' => 'client_side'
                ],
                false,
                'Multiple client-side errors occurred during checkout session',
                null,
                []
            );
        }

        // Process batch of form validation errors
        if (!empty($formValidationErrors)) {
            $this->activityLogger->logCheckoutActivity(
                'client_form_validation_batch',
                [
                    'client_session_id' => $logData['session_id'] ?? null,
                    'validation_errors_count' => count($formValidationErrors),
                    'validation_errors_summary' => $this->summarizeFormValidationErrors($formValidationErrors),
                    'client_timestamp' => $logData['timestamp'] ?? null,
                    'source' => 'client_side'
                ],
                false,
                'Multiple client-side form validation errors occurred',
                null,
                []
            );
        }
    }

    /**
     * Get error description based on error type
     */
    protected function getErrorDescription(string $errorType, array $errorData): string
    {
        $descriptions = [
            'javascript_error' => 'JavaScript runtime error: ' . ($errorData['message'] ?? 'Unknown error'),
            'promise_rejection' => 'Unhandled promise rejection: ' . ($errorData['reason'] ?? 'Unknown reason'),
            'ajax_error' => 'AJAX request failed: ' . ($errorData['status'] ?? 'Unknown status'),
            'ajax_network_error' => 'Network error during AJAX request: ' . ($errorData['error_message'] ?? 'Unknown error'),
            'xhr_error' => 'XMLHttpRequest error: ' . ($errorData['url'] ?? 'Unknown URL'),
            'xhr_http_error' => 'HTTP error in XMLHttpRequest: ' . ($errorData['status'] ?? 'Unknown status'),
            'stripe_error' => 'Stripe payment error: ' . ($errorData['error_message'] ?? 'Unknown error'),
        ];

        return $descriptions[$errorType] ?? "Unknown client-side error: {$errorType}";
    }

    /**
     * Summarize interactions for batch logging
     */
    protected function summarizeInteractions(array $interactions): array
    {
        $summary = [];
        $types = array_column($interactions, 'type');
        $typeCounts = array_count_values($types);
        
        $summary['interaction_types'] = $typeCounts;
        $summary['total_interactions'] = count($interactions);
        $summary['time_span'] = $this->calculateTimeSpan($interactions);
        
        return $summary;
    }

    /**
     * Summarize errors for batch logging
     */
    protected function summarizeErrors(array $errors): array
    {
        $summary = [];
        $types = array_column($errors, 'type');
        $typeCounts = array_count_values($types);
        
        $summary['error_types'] = $typeCounts;
        $summary['total_errors'] = count($errors);
        $summary['most_common_error'] = array_keys($typeCounts, max($typeCounts))[0] ?? null;
        
        return $summary;
    }

    /**
     * Summarize form validation errors for batch logging
     */
    protected function summarizeFormValidationErrors(array $validationErrors): array
    {
        $summary = [];
        $fields = array_column($validationErrors, 'field_name');
        $fieldCounts = array_count_values($fields);
        
        $summary['failed_fields'] = $fieldCounts;
        $summary['total_validation_errors'] = count($validationErrors);
        $summary['most_problematic_field'] = array_keys($fieldCounts, max($fieldCounts))[0] ?? null;
        
        return $summary;
    }

    /**
     * Calculate time span for interactions
     */
    protected function calculateTimeSpan(array $interactions): ?int
    {
        if (empty($interactions)) {
            return null;
        }

        $timestamps = array_column($interactions, 'timestamp');
        $timestamps = array_filter($timestamps);
        
        if (count($timestamps) < 2) {
            return null;
        }

        return max($timestamps) - min($timestamps);
    }
}
