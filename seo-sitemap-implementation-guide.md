# SEO, Sitemap, and Structured Data Implementation Guide

## Overview
This guide documents the implementation and configuration of SEO, sitemap, and structured data features for the Professional Business Consultancy Laravel application. It covers:
- Sitemap generation and configuration
- robots.txt setup
- Structured data (JSON-LD) for rich results
- SEO meta tags and best practices
- Environment and deployment notes

---

## 1. Sitemap Generation

### Purpose
The sitemap provides search engines with a complete, up-to-date list of all public pages, including localized and service detail URLs, to improve indexing and discoverability.

### Implementation
- **Controller:** `app/Http/Controllers/SitemapController.php`
- **Route:** `/sitemap.xml` (see `routes/web.php`)
- **Caching:** Sitemap is cached for 60 minutes using <PERSON><PERSON>'s cache system.
- **Locale Support:** All supported locales (e.g., `en`, `fr`) are included for each route.
- **Service Pages:** All valid service slugs are included for each locale.
- **Hreflang:** `<xhtml:link rel="alternate" hreflang="...">` tags are generated for each locale.

#### Example: Adding a New Service Page
1. Add the new slug to the `$serviceSlugs` array in `SitemapController.php`.
2. Run `php artisan sitemap:clear` to clear the cached sitemap.
3. The new page will appear in `/sitemap.xml` on next request.

#### Example Sitemap XML Entry
```xml
<url>
  <loc>https://your-domain.com/en/services/business-consultancy</loc>
  <xhtml:link rel="alternate" hreflang="fr" href="https://your-domain.com/fr/services/business-consultancy" />
  <lastmod>2025-09-22T07:18:07.919157Z</lastmod>
  <changefreq>monthly</changefreq>
  <priority>0.8</priority>
</url>
```

---

## 2. robots.txt Configuration

### Purpose
Controls which URLs search engines can crawl and index, protecting admin and sensitive endpoints while allowing all public content.

### Location
- `public/robots.txt`

### Key Rules
- **Allow:** All public pages, including localized service pages and legal pages.
- **Disallow:** Admin, dashboard, profile, form endpoints, build assets, and system files.
- **Sitemap:** Points to the live sitemap URL.

#### Example robots.txt
```
User-agent: *
Allow: /
Sitemap: https://your-domain.com/sitemap.xml
Disallow: /admin/
Disallow: /dashboard/
Disallow: /profile
Disallow: /contact/submit
Disallow: /services/inquiry
Disallow: /build/
Disallow: /.env
Disallow: /storage/
Allow: /en/services/business-consultancy
Allow: /fr/services/business-consultancy
... (all valid service pages)
Crawl-delay: 1
```

---

## 3. Structured Data (JSON-LD)

### Purpose
Provides search engines with machine-readable metadata for rich results (organization, breadcrumbs, services, webpage).

### Implementation
- **Component:** `resources/views/components/seo/structured-data.blade.php`
- **Approach:**
  - All JSON-LD blocks are built as PHP arrays and rendered using `json_encode` for valid output.
  - No `@verbatim` blocks; avoids Blade parse errors and trailing comma issues.
  - Supports types: `organization`, `breadcrumb`, `service`, `webpage`.

#### Example Usage
```blade
<x-seo.structured-data type="organization" />
<x-seo.structured-data type="breadcrumb" :data="$breadcrumbData" />
<x-seo.structured-data type="service" :data="$serviceData" />
<x-seo.structured-data type="webpage" :data="$webpageData" />
```
## Content
```{{-- SEO Structured Data Component --}}
@props(['type' => 'organization', 'data' => []])

@php
    // Helper to encode JSON-LD with readable formatting and unescaped slashes
    $ldJson = function ($arr) {
        return json_encode($arr, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    };
@endphp

@if($type === 'organization')
    @php
        $org = [
            '@context' => 'https://schema.org',
            '@type' => 'ProfessionalService',
            'name' => __('common.company.name'),
            'alternateName' => 'TC Rwanda',
            'description' => __('common.company.description'),
            'url' => url('/'),
            'logo' => asset('images/logo.png'),
            'image' => asset('images/og-image.png'),
            'telephone' => '+250-XXX-XXXXXX',
            'email' => '<EMAIL>',
            'address' => [
                '@type' => 'PostalAddress',
                'streetAddress' => 'Kigali Business District',
                'addressLocality' => 'Kigali',
                'addressRegion' => 'Kigali Province',
                'postalCode' => '00000',
                'addressCountry' => 'RW',
            ],
            'geo' => [
                '@type' => 'GeoCoordinates',
                'latitude' => -1.9441,
                'longitude' => 30.0619,
            ],
            'areaServed' => [
                ['@type' => 'Country', 'name' => 'Rwanda'],
                ['@type' => 'Country', 'name' => 'Canada'],
                ['@type' => 'Country', 'name' => 'United States'],
                ['@type' => 'Country', 'name' => 'Cameroon'],
            ],
            'serviceType' => [
                'Business Consultancy', 'Accounting Services', 'Tax Advisory', 'Financial Planning',
                'Business Registration', 'Audit & Compliance', 'Corporate Training', 'Career Development'
            ],
            'priceRange' => '$$',
            'openingHours' => 'Mo-Fr 08:00-17:00',
            'sameAs' => [
                'https://linkedin.com/company/triumphant-consult',
                'https://twitter.com/pbc_rwanda',
            ],
            'founder' => ['@type' => 'Person', 'name' => __('common.company.team_name')],
            'foundingDate' => '2020',
            'numberOfEmployees' => [
                '@type' => 'QuantitativeValue',
                'minValue' => 5,
                'maxValue' => 10,
            ],
            'slogan' => 'Transform your business with strategic insights and expert guidance',
        ];
    @endphp
    <script type="application/ld+json">{!! $ldJson($org) !!}</script>
@endif

@if($type === 'breadcrumb')
    @php
        $items = [];
        foreach ($data as $index => $item) {
            $items[] = [
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $item['name'] ?? '',
                'item' => $item['url'] ?? '',
            ];
        }
        $breadcrumb = [
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => $items,
        ];
    @endphp
    <script type="application/ld+json">{!! $ldJson($breadcrumb) !!}</script>
@endif

@if($type === 'service')
    @php
        $service = [
            '@context' => 'https://schema.org',
            '@type' => 'Service',
            'name' => $data['name'] ?? '',
            'description' => $data['description'] ?? '',
            'provider' => [
                '@type' => 'ProfessionalService',
                'name' => __('common.company.name'),
                'url' => url('/'),
            ],
            'areaServed' => [
                ['@type' => 'Country', 'name' => 'Rwanda'],
                ['@type' => 'Country', 'name' => 'Canada'],
                ['@type' => 'Country', 'name' => 'United States'],
                ['@type' => 'Country', 'name' => 'Cameroon'],
            ],
            'serviceType' => $data['serviceType'] ?? 'Business Consultancy',
            'category' => $data['category'] ?? 'Professional Services',
        ];

        if (!empty($data['offers'])) {
            $service['offers'] = [
                '@type' => 'Offer',
                'description' => $data['offers']['description'] ?? '',
                'priceRange' => $data['offers']['priceRange'] ?? '$$',
            ];
        }

        $service['url'] = $data['url'] ?? url()->current();
    @endphp
    <script type="application/ld+json">{!! $ldJson($service) !!}</script>
@endif

@if($type === 'webpage')
    @php
        $webpage = [
            '@context' => 'https://schema.org',
            '@type' => 'WebPage',
            'name' => $data['title'] ?? '',
            'description' => $data['description'] ?? '',
            'url' => url()->current(),
            'inLanguage' => app()->getLocale(),
            'isPartOf' => [
                '@type' => 'WebSite',
                'name' => __('common.company.name'),
                'url' => url('/'),
            ],
        ];
    @endphp
    <script type="application/ld+json">{!! $ldJson($webpage) !!}</script>
@endif
```

#### Example Output
```json
{
  "@context": "https://schema.org",
  "@type": "ProfessionalService",
  "name": "Professional Business Consultancy",
  "url": "https://your-domain.com",
  ...
}
```

#### Troubleshooting
- If you add new fields, update the PHP array in the component.
- Always clear compiled views after changes: `php artisan view:clear`

---

## 4. SEO Meta Tags & Best Practices

### Recommendations
- Use descriptive `<title>` and `<meta name="description">` tags in all layouts and pages.
- Use canonical URLs (`<link rel="canonical" href="...">`) for each page.
- Ensure Open Graph and Twitter Card meta tags are present for social sharing.
- Use localized meta tags for multilingual support.

#### Example Meta Tags
```html
<title>Professional Business Consultancy - Business Consultancy Services</title>
<meta name="description" content="Leading business consultancy, accounting, tax advisory, and more in Rwanda and beyond." />
<link rel="canonical" href="https://your-domain.com/en/services/business-consultancy" />
<meta property="og:title" content="Professional Business Consultancy" />
<meta property="og:description" content="Leading business consultancy..." />
<meta property="og:image" content="https://your-domain.com/images/og-image.jpg" />
<meta name="twitter:card" content="summary_large_image" />
```

---

## 5. Environment & Deployment Notes

- **.env.example:**
  - Set `APP_URL` to your production domain.
  - Set `CACHE_DRIVER` to a production-ready cache (redis, database, etc.).
  - Set `SITEMAP_CACHE_TTL` as needed.
- **Kernel Registration:**
  - Ensure `app/Console/Kernel.php` registers the `ClearSitemapCache` command.
- **Cache Management:**
  - Use `php artisan sitemap:clear` to refresh the sitemap cache after changes.
- **View Compilation:**
  - Run `php artisan view:clear` after changing Blade components.

---

## 6. Example Workflow for Adding a New Service

1. Add the new service slug to `$serviceSlugs` in `SitemapController.php`.
2. Add the corresponding Blade view in `resources/views/pages/services/`.
3. Update robots.txt to allow the new service page.
4. Clear sitemap cache: `php artisan sitemap:clear`
5. Clear compiled views: `php artisan view:clear`
6. Verify `/sitemap.xml` and the new page in Search Console.

---

## 7. References & Further Reading
- [Google Search Central: Sitemaps](https://developers.google.com/search/docs/crawling-indexing/sitemaps/overview)
- [Google Search Central: Structured Data](https://developers.google.com/search/docs/appearance/structured-data/intro-structured-data)
- [Laravel Documentation: Blade Templates](https://laravel.com/docs/blade)
- [Laravel Documentation: Caching](https://laravel.com/docs/cache)

---

## Contact
For further questions or improvements, contact the development <NAME_EMAIL>.
