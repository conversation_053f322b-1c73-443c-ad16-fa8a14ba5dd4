<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_messages', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('chat_room_id')->constrained('chat_rooms')->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null')
                  ->comment('NULL for anonymous visitors');
            $table->enum('message_type', ['text', 'file', 'image', 'system', 'ai', 'emoji'])->default('text');
            $table->text('content');
            $table->json('metadata')->nullable()->comment('File info, AI context, etc.');
            $table->boolean('is_ai_generated')->default(false);
            $table->decimal('ai_confidence', 3, 2)->nullable()->comment('AI confidence score 0.00-1.00');
            $table->string('ai_model', 50)->nullable()->comment('AI model used for generation');
            $table->foreignId('reply_to_message_id')->nullable()->constrained('chat_messages')->onDelete('set null')
                  ->comment('For threaded conversations');
            $table->boolean('is_edited')->default(false);
            $table->tinyInteger('edit_count')->unsigned()->default(0);
            $table->timestamps();
            $table->softDeletes();
            
            // Performance indexes for real-time queries
            $table->index(['chat_room_id', 'created_at'], 'idx_room_created');
            $table->index(['user_id', 'created_at'], 'idx_user_created');
            $table->index(['is_ai_generated', 'created_at'], 'idx_ai_generated');
            $table->index('message_type', 'idx_message_type');
            $table->index('reply_to_message_id', 'idx_reply_to');
        });

        // Add partitioning comment for future optimization (MySQL only)
        if (DB::getDriverName() === 'mysql') {
            DB::statement("ALTER TABLE chat_messages COMMENT = 'Partitioned by month for performance'");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_messages');
    }
};
