<?php

namespace App\Services;

use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\ChatRating;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class ConversationInsightsService
{
    protected DashboardCacheService $cacheService;
    protected ActivityLogger $activityLogger;

    public function __construct(DashboardCacheService $cacheService, ActivityLogger $activityLogger)
    {
        $this->cacheService = $cacheService;
        $this->activityLogger = $activityLogger;
    }

    /**
     * Get comprehensive conversation insights.
     */
    public function getConversationInsights(array $filters = []): array
    {
        $startDate = isset($filters['start_date']) ? Carbon::parse($filters['start_date']) : Carbon::now()->subDays(30);
        $endDate = isset($filters['end_date']) ? Carbon::parse($filters['end_date']) : Carbon::now();
        $staffId = $filters['staff_id'] ?? null;
        $department = $filters['department'] ?? null;

        return $this->cacheService->remember(
            "conversation_insights:" . md5(serialize($filters)),
            function () use ($startDate, $endDate, $staffId, $department) {
                return [
                    'overview' => $this->getInsightsOverview($startDate, $endDate, $staffId, $department),
                    'conversation_patterns' => $this->getConversationPatterns($startDate, $endDate, $staffId, $department),
                    'trending_topics' => $this->getTrendingTopics($startDate, $endDate, $staffId, $department),
                    'usage_patterns' => $this->getUsagePatterns($startDate, $endDate, $staffId, $department),
                    'conversation_flow' => $this->getConversationFlow($startDate, $endDate, $staffId, $department),
                    'peak_hours' => $this->getPeakHours($startDate, $endDate, $staffId, $department),
                    'conversation_length' => $this->getConversationLength($startDate, $endDate, $staffId, $department),
                    'resolution_insights' => $this->getResolutionInsights($startDate, $endDate, $staffId, $department),
                    'user_behavior' => $this->getUserBehavior($startDate, $endDate, $staffId, $department),
                    'staff_insights' => $this->getStaffInsights($startDate, $endDate, $department),
                ];
            },
            3600 // Cache for 1 hour
        );
    }

    /**
     * Get insights overview metrics.
     */
    protected function getInsightsOverview(Carbon $startDate, Carbon $endDate, ?int $staffId, ?string $department): array
    {
        $query = ChatRoom::whereBetween('created_at', [$startDate, $endDate]);

        if ($staffId) {
            $query->whereHas('assignments', function ($q) use ($staffId) {
                $q->where('assigned_to', $staffId);
            });
        }

        $totalConversations = $query->count();
        $completedConversations = $query->where('status', 'closed')->count();
        $averageMessages = $this->getAverageMessagesPerConversation($startDate, $endDate, $staffId, $department);
        $averageDuration = $this->getAverageDuration($startDate, $endDate, $staffId, $department);

        return [
            'total_conversations' => $totalConversations,
            'completed_conversations' => $completedConversations,
            'completion_rate' => $totalConversations > 0 ? round(($completedConversations / $totalConversations) * 100, 2) : 0,
            'average_messages_per_conversation' => round($averageMessages, 1),
            'average_duration_minutes' => round($averageDuration, 1),
            'active_conversations' => ChatRoom::where('status', 'active')->count(),
            'pending_conversations' => ChatRoom::where('status', 'pending')->count(),
        ];
    }

    /**
     * Get conversation patterns analysis.
     */
    protected function getConversationPatterns(Carbon $startDate, Carbon $endDate, ?int $staffId, ?string $department): array
    {
        // Analyze conversation initiation patterns
        $initiationPatterns = DB::table('chat_rooms')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select(
                $this->getHourExpression('created_at', 'hour'),
                $this->getDayOfWeekExpression('created_at', 'day_of_week'),
                DB::raw('COUNT(*) as count')
            )
            ->groupBy('hour', 'day_of_week')
            ->get()
            ->groupBy('day_of_week')
            ->map(function ($dayData) {
                return $dayData->pluck('count', 'hour')->toArray();
            });

        // Analyze conversation duration patterns - simplified for SQLite compatibility
        $durationPatterns = collect([
            '0-5 min' => 0,
            '5-15 min' => 0,
            '15-30 min' => 0,
            '30-60 min' => 0,
            '60+ min' => 0,
        ]);

        return [
            'initiation_patterns' => $initiationPatterns,
            'duration_patterns' => $durationPatterns,
            'common_start_phrases' => $this->getCommonStartPhrases($startDate, $endDate),
            'conversation_outcomes' => $this->getConversationOutcomes($startDate, $endDate, $staffId, $department),
        ];
    }

    /**
     * Get trending topics from conversations.
     */
    protected function getTrendingTopics(Carbon $startDate, Carbon $endDate, ?int $staffId, ?string $department): array
    {
        $query = ChatMessage::whereBetween('created_at', [$startDate, $endDate])
            ->where('is_ai_generated', false)
            ->whereNotNull('user_id'); // Only analyze user messages

        if ($staffId) {
            $query->whereHas('chatRoom.assignments', function ($q) use ($staffId) {
                $q->where('assigned_to', $staffId);
            });
        }

        $messages = $query->pluck('content')->toArray();
        
        // Simple keyword extraction (in production, you might use NLP libraries)
        $keywords = $this->extractKeywords($messages);
        $topKeywords = array_slice($keywords, 0, 20);

        // Analyze topic trends over time
        $topicTrends = $this->getTopicTrends($startDate, $endDate, array_keys($topKeywords));

        return [
            'top_keywords' => $topKeywords,
            'topic_trends' => $topicTrends,
            'emerging_topics' => $this->getEmergingTopics($startDate, $endDate),
            'topic_sentiment' => $this->getTopicSentiment($topKeywords),
        ];
    }

    /**
     * Get usage patterns analysis.
     */
    protected function getUsagePatterns(Carbon $startDate, Carbon $endDate, ?int $staffId, ?string $department): array
    {
        // Daily usage patterns
        $dailyUsage = DB::table('chat_rooms')
            ->leftJoin('chat_participants', 'chat_rooms.id', '=', 'chat_participants.chat_room_id')
            ->whereBetween('chat_rooms.created_at', [$startDate, $endDate])
            ->select(
                DB::raw('DATE(chat_rooms.created_at) as date'),
                DB::raw('COUNT(DISTINCT chat_rooms.id) as conversations'),
                DB::raw('COUNT(DISTINCT chat_participants.user_id) as unique_users')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Hourly patterns
        $hourlyPatterns = DB::table('chat_rooms')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select(
                $this->getHourExpression('created_at', 'hour'),
                DB::raw('COUNT(*) as count'),
                $this->getTimeDiffExpression('created_at', 'closed_at', 'avg_duration')
            )
            ->groupBy('hour')
            ->orderBy('hour')
            ->get();

        // Device/Platform patterns (if available)
        $platformPatterns = $this->getPlatformPatterns($startDate, $endDate);

        return [
            'daily_usage' => $dailyUsage,
            'hourly_patterns' => $hourlyPatterns,
            'platform_patterns' => $platformPatterns,
            'repeat_users' => $this->getRepeatUserPatterns($startDate, $endDate),
            'seasonal_trends' => $this->getSeasonalTrends($startDate, $endDate),
        ];
    }

    /**
     * Get conversation flow analysis.
     */
    protected function getConversationFlow(Carbon $startDate, Carbon $endDate, ?int $staffId, ?string $department): array
    {
        // Analyze typical conversation flow
        $flowSteps = [
            'greeting' => 0,
            'problem_identification' => 0,
            'solution_discussion' => 0,
            'resolution' => 0,
            'satisfaction_check' => 0,
        ];

        // Analyze handoff patterns
        $handoffPatterns = DB::table('chat_assignments')
            ->join('chat_rooms', 'chat_assignments.chat_room_id', '=', 'chat_rooms.id')
            ->whereBetween('chat_rooms.created_at', [$startDate, $endDate])
            ->select(
                'chat_room_id',
                DB::raw('COUNT(*) as handoff_count')
            )
            ->groupBy('chat_room_id')
            ->having('handoff_count', '>', 1)
            ->get();

        return [
            'typical_flow_steps' => $flowSteps,
            'handoff_patterns' => $handoffPatterns,
            'escalation_points' => $this->getEscalationPoints($startDate, $endDate),
            'conversation_stages' => $this->getConversationStages($startDate, $endDate),
        ];
    }

    /**
     * Get peak hours analysis.
     */
    protected function getPeakHours(Carbon $startDate, Carbon $endDate, ?int $staffId, ?string $department): array
    {
        $hourlyData = DB::table('chat_rooms')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select(
                $this->getHourExpression('created_at', 'hour'),
                $this->getDayOfWeekExpression('created_at', 'day_of_week'),
                DB::raw('COUNT(*) as conversation_count'),
                $this->getTimeDiffExpression('created_at', 'closed_at', 'avg_duration')
            )
            ->groupBy('hour', 'day_of_week')
            ->get();

        $peakHours = $hourlyData->sortByDesc('conversation_count')->take(5);
        $lowHours = $hourlyData->sortBy('conversation_count')->take(5);

        return [
            'peak_hours' => $peakHours,
            'low_activity_hours' => $lowHours,
            'hourly_distribution' => $hourlyData->groupBy('hour'),
            'weekly_distribution' => $hourlyData->groupBy('day_of_week'),
            'recommendations' => $this->getStaffingRecommendations($hourlyData),
        ];
    }

    /**
     * Get conversation length analysis.
     */
    protected function getConversationLength(Carbon $startDate, Carbon $endDate, ?int $staffId, ?string $department): array
    {
        $lengthData = DB::table('chat_rooms')
            ->leftJoin('chat_messages', 'chat_rooms.id', '=', 'chat_messages.chat_room_id')
            ->whereBetween('chat_rooms.created_at', [$startDate, $endDate])
            ->select(
                'chat_rooms.id',
                DB::raw('COUNT(chat_messages.id) as message_count')
            )
            ->groupBy('chat_rooms.id')
            ->get();

        $avgMessages = $lengthData->avg('message_count');

        return [
            'average_messages' => round($avgMessages, 1),
            'average_duration' => 0, // Simplified for SQLite compatibility
            'message_distribution' => $this->getMessageDistribution($lengthData),
            'duration_distribution' => [],
            'correlation_analysis' => [],
        ];
    }

    /**
     * Get resolution insights.
     */
    protected function getResolutionInsights(Carbon $startDate, Carbon $endDate, ?int $staffId, ?string $department): array
    {
        $resolutionData = DB::table('chat_rooms')
            ->leftJoin('chat_ratings', 'chat_rooms.id', '=', 'chat_ratings.chat_room_id')
            ->whereBetween('chat_rooms.created_at', [$startDate, $endDate])
            ->select(
                'chat_rooms.status',
                DB::raw('COUNT(chat_rooms.id) as count'),
                DB::raw('AVG(chat_ratings.rating) as avg_rating')
            )
            ->groupBy('chat_rooms.status')
            ->get();

        return [
            'resolution_by_status' => $resolutionData,
            'first_contact_resolution' => $this->getFirstContactResolution($startDate, $endDate),
            'resolution_time_trends' => $this->getResolutionTimeTrends($startDate, $endDate),
            'satisfaction_by_resolution_time' => $this->getSatisfactionByResolutionTime($startDate, $endDate),
        ];
    }

    /**
     * Get user behavior analysis.
     */
    protected function getUserBehavior(Carbon $startDate, Carbon $endDate, ?int $staffId, ?string $department): array
    {
        $userStats = DB::table('chat_rooms')
            ->join('chat_participants', 'chat_rooms.id', '=', 'chat_participants.chat_room_id')
            ->join('users', 'chat_participants.user_id', '=', 'users.id')
            ->where('chat_participants.participant_type', 'customer')
            ->whereBetween('chat_rooms.created_at', [$startDate, $endDate])
            ->select(
                'users.id',
                DB::raw('(users.first_name || " " || users.last_name) as user_name'),
                DB::raw('COUNT(DISTINCT chat_rooms.id) as conversation_count')
            )
            ->groupBy('users.id', 'users.first_name', 'users.last_name')
            ->orderByDesc('conversation_count')
            ->limit(50)
            ->get();

        return [
            'top_users' => $userStats,
            'new_vs_returning' => $this->getNewVsReturningUsers($startDate, $endDate),
            'user_engagement' => $this->getUserEngagement($startDate, $endDate),
            'user_satisfaction_patterns' => $this->getUserSatisfactionPatterns($startDate, $endDate),
        ];
    }

    /**
     * Get staff insights.
     */
    protected function getStaffInsights(Carbon $startDate, Carbon $endDate, ?string $department): array
    {
        $staffStats = DB::table('chat_assignments')
            ->join('chat_rooms', 'chat_assignments.chat_room_id', '=', 'chat_rooms.id')
            ->join('users', 'chat_assignments.assigned_to', '=', 'users.id')
            ->leftJoin('chat_participants as customer_participants', function($join) {
                $join->on('chat_rooms.id', '=', 'customer_participants.chat_room_id')
                     ->where('customer_participants.participant_type', '=', 'customer');
            })
            ->whereBetween('chat_rooms.created_at', [$startDate, $endDate])
            ->select(
                'users.id',
                DB::raw('(users.first_name || " " || users.last_name) as staff_name'),
                DB::raw('COUNT(DISTINCT chat_rooms.id) as conversations_handled'),
                DB::raw('COUNT(DISTINCT customer_participants.user_id) as unique_customers_served')
            )
            ->groupBy('users.id', 'users.first_name', 'users.last_name')
            ->orderByDesc('conversations_handled')
            ->get();

        return [
            'staff_performance' => $staffStats,
            'workload_distribution' => $this->getWorkloadDistribution($startDate, $endDate),
            'staff_efficiency' => $this->getStaffEfficiency($startDate, $endDate),
            'collaboration_patterns' => $this->getCollaborationPatterns($startDate, $endDate),
        ];
    }

    // Helper methods for data processing
    protected function getAverageMessagesPerConversation(Carbon $startDate, Carbon $endDate, ?int $staffId, ?string $department): float
    {
        $query = DB::table('chat_rooms')
            ->leftJoin('chat_messages', 'chat_rooms.id', '=', 'chat_messages.chat_room_id')
            ->whereBetween('chat_rooms.created_at', [$startDate, $endDate]);

        if ($staffId) {
            $query->whereExists(function ($q) use ($staffId) {
                $q->select(DB::raw(1))
                  ->from('chat_assignments')
                  ->whereColumn('chat_assignments.chat_room_id', 'chat_rooms.id')
                  ->where('chat_assignments.assigned_to', $staffId);
            });
        }

        $result = $query->select(
            'chat_rooms.id',
            DB::raw('COUNT(chat_messages.id) as message_count')
        )
        ->groupBy('chat_rooms.id')
        ->get();

        return $result->avg('message_count') ?? 0;
    }

    protected function getAverageDuration(Carbon $startDate, Carbon $endDate, ?int $staffId, ?string $department): float
    {
        $query = ChatRoom::whereBetween('created_at', [$startDate, $endDate])
            ->whereNotNull('closed_at');

        if ($staffId) {
            $query->whereHas('assignments', function ($q) use ($staffId) {
                $q->where('assigned_to', $staffId);
            });
        }

        $conversations = $query->get();
        
        if ($conversations->isEmpty()) {
            return 0;
        }

        $totalMinutes = $conversations->sum(function ($conversation) {
            return $conversation->created_at->diffInMinutes($conversation->closed_at);
        });

        return $totalMinutes / $conversations->count();
    }

    protected function extractKeywords(array $messages): array
    {
        // Simple keyword extraction - in production, use proper NLP
        $allText = implode(' ', $messages);
        $words = str_word_count(strtolower($allText), 1);
        
        // Filter out common words
        $stopWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'cannot', 'cant', 'wont', 'dont', 'doesnt', 'didnt', 'isnt', 'arent', 'wasnt', 'werent', 'havent', 'hasnt', 'hadnt', 'a', 'an', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'];
        
        $filteredWords = array_filter($words, function ($word) use ($stopWords) {
            return strlen($word) > 2 && !in_array($word, $stopWords);
        });

        $wordCounts = array_count_values($filteredWords);
        arsort($wordCounts);

        return $wordCounts;
    }

    // Additional helper methods would be implemented here...
    protected function getCommonStartPhrases(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for common conversation start phrases
        return [];
    }

    protected function getConversationOutcomes(Carbon $startDate, Carbon $endDate, ?int $staffId, ?string $department): array
    {
        // Implementation for conversation outcomes analysis
        return [];
    }

    protected function getTopicTrends(Carbon $startDate, Carbon $endDate, array $keywords): array
    {
        // Implementation for topic trends over time
        return [];
    }

    protected function getEmergingTopics(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for emerging topics detection
        return [];
    }

    protected function getTopicSentiment(array $keywords): array
    {
        // Implementation for topic sentiment analysis
        return [];
    }

    protected function getPlatformPatterns(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for platform/device patterns
        return [];
    }

    protected function getRepeatUserPatterns(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for repeat user analysis
        return [];
    }

    protected function getSeasonalTrends(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for seasonal trends
        return [];
    }

    protected function getEscalationPoints(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for escalation point analysis
        return [];
    }

    protected function getConversationStages(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for conversation stage analysis
        return [];
    }

    protected function getStaffingRecommendations($hourlyData): array
    {
        // Implementation for staffing recommendations
        return [];
    }

    protected function getMessageDistribution($lengthData): array
    {
        // Implementation for message count distribution
        return [];
    }

    protected function getDurationDistribution($lengthData): array
    {
        // Implementation for duration distribution
        return [];
    }

    protected function getMessageDurationCorrelation($lengthData): array
    {
        // Implementation for message-duration correlation
        return [];
    }

    protected function getFirstContactResolution(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for first contact resolution analysis
        return [];
    }

    protected function getResolutionTimeTrends(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for resolution time trends
        return [];
    }

    protected function getSatisfactionByResolutionTime(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for satisfaction vs resolution time analysis
        return [];
    }

    protected function getNewVsReturningUsers(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for new vs returning user analysis
        return [];
    }

    protected function getUserEngagement(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for user engagement analysis
        return [];
    }

    protected function getUserSatisfactionPatterns(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for user satisfaction patterns
        return [];
    }

    protected function getWorkloadDistribution(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for workload distribution analysis
        return [];
    }

    protected function getStaffEfficiency(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for staff efficiency analysis
        return [];
    }

    protected function getCollaborationPatterns(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for collaboration patterns analysis
        return [];
    }

    /**
     * Get database-agnostic hour expression.
     */
    protected function getHourExpression(string $column, string $alias): \Illuminate\Database\Query\Expression
    {
        $driver = DB::connection()->getDriverName();

        switch ($driver) {
            case 'sqlite':
                return DB::raw("CAST(strftime('%H', {$column}) AS INTEGER) as {$alias}");
            case 'mysql':
                return DB::raw("HOUR({$column}) as {$alias}");
            case 'pgsql':
                return DB::raw("EXTRACT(HOUR FROM {$column}) as {$alias}");
            default:
                return DB::raw("HOUR({$column}) as {$alias}");
        }
    }

    /**
     * Get database-agnostic day of week expression.
     */
    protected function getDayOfWeekExpression(string $column, string $alias): \Illuminate\Database\Query\Expression
    {
        $driver = DB::connection()->getDriverName();

        switch ($driver) {
            case 'sqlite':
                return DB::raw("CAST(strftime('%w', {$column}) AS INTEGER) + 1 as {$alias}");
            case 'mysql':
                return DB::raw("DAYOFWEEK({$column}) as {$alias}");
            case 'pgsql':
                return DB::raw("EXTRACT(DOW FROM {$column}) + 1 as {$alias}");
            default:
                return DB::raw("DAYOFWEEK({$column}) as {$alias}");
        }
    }

    /**
     * Get database-agnostic time difference expression in minutes.
     */
    protected function getTimeDiffExpression(string $startColumn, string $endColumn, string $alias): \Illuminate\Database\Query\Expression
    {
        $driver = DB::connection()->getDriverName();

        switch ($driver) {
            case 'sqlite':
                return DB::raw("AVG((julianday(COALESCE({$endColumn}, datetime('now'))) - julianday({$startColumn})) * 24 * 60) as {$alias}");
            case 'mysql':
                return DB::raw("AVG(TIMESTAMPDIFF(MINUTE, {$startColumn}, COALESCE({$endColumn}, NOW()))) as {$alias}");
            case 'pgsql':
                return DB::raw("AVG(EXTRACT(EPOCH FROM (COALESCE({$endColumn}, NOW()) - {$startColumn})) / 60) as {$alias}");
            default:
                return DB::raw("AVG(TIMESTAMPDIFF(MINUTE, {$startColumn}, COALESCE({$endColumn}, NOW()))) as {$alias}");
        }
    }
}
