@extends('layouts.dashboard')

@section('title', 'Conversation Insights & Trends')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Conversation Insights & Trends</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.chat.analytics.index') }}">Chat Analytics</a></li>
                        <li class="breadcrumb-item active">Conversation Insights</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Row -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form id="insights-filters" class="row g-3">
                        <div class="col-md-3">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                   value="{{ $defaultFilters['start_date']->format('Y-m-d') }}">
                        </div>
                        <div class="col-md-3">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" 
                                   value="{{ $defaultFilters['end_date']->format('Y-m-d') }}">
                        </div>
                        <div class="col-md-2">
                            <label for="staff_id" class="form-label">Staff Member</label>
                            <select class="form-select" id="staff_id" name="staff_id">
                                <option value="all">All Staff</option>
                                <!-- Staff options would be populated dynamically -->
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="department" class="form-label">Department</label>
                            <select class="form-select" id="department" name="department">
                                <option value="all">All Departments</option>
                                <option value="support">Support</option>
                                <option value="sales">Sales</option>
                                <option value="technical">Technical</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block">Apply Filters</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Overview Metrics Row -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate">Total Conversations</h5>
                            <h3 class="my-2 py-1" id="total-conversations">{{ $insights['overview']['total_conversations'] ?? 0 }}</h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-chat text-primary" style="font-size: 24px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate">Completion Rate</h5>
                            <h3 class="my-2 py-1" id="completion-rate">{{ number_format($insights['overview']['completion_rate'] ?? 0, 1) }}%</h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-check-circle text-success" style="font-size: 24px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate">Avg Messages</h5>
                            <h3 class="my-2 py-1" id="avg-messages">{{ number_format($insights['overview']['average_messages_per_conversation'] ?? 0, 1) }}</h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-message-text text-info" style="font-size: 24px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate">Avg Duration</h5>
                            <h3 class="my-2 py-1" id="avg-duration">{{ number_format($insights['overview']['average_duration_minutes'] ?? 0, 1) }}m</h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-clock text-warning" style="font-size: 24px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 1 -->
    <div class="row">
        <div class="col-xl-6">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Trending Topics</h4>
                    <div id="trending-topics-chart" style="height: 300px;"></div>
                    
                    <div class="mt-3">
                        <h6>Top Keywords:</h6>
                        <div id="top-keywords" class="d-flex flex-wrap gap-2">
                            @if(isset($insights['trending_topics']['top_keywords']))
                                @foreach(array_slice($insights['trending_topics']['top_keywords'], 0, 10, true) as $keyword => $count)
                                <span class="badge bg-primary">{{ $keyword }} ({{ $count }})</span>
                                @endforeach
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-6">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Usage Patterns</h4>
                    <div id="usage-patterns-chart" style="height: 300px;"></div>
                    
                    <div class="mt-3">
                        <div class="row text-center">
                            <div class="col-4">
                                <h5 class="fw-normal text-muted">Active</h5>
                                <h4 id="active-conversations">{{ $insights['overview']['active_conversations'] ?? 0 }}</h4>
                            </div>
                            <div class="col-4">
                                <h5 class="fw-normal text-muted">Pending</h5>
                                <h4 id="pending-conversations">{{ $insights['overview']['pending_conversations'] ?? 0 }}</h4>
                            </div>
                            <div class="col-4">
                                <h5 class="fw-normal text-muted">Completed</h5>
                                <h4 id="completed-conversations">{{ $insights['overview']['completed_conversations'] ?? 0 }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 2 -->
    <div class="row">
        <div class="col-xl-8">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Peak Hours Analysis</h4>
                    <div id="peak-hours-chart" style="height: 350px;"></div>
                    
                    <div class="mt-3">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Peak Hours:</h6>
                                <ul class="list-unstyled" id="peak-hours-list">
                                    @if(isset($insights['peak_hours']['peak_hours']))
                                        @foreach($insights['peak_hours']['peak_hours']->take(3) as $hour)
                                        <li><i class="mdi mdi-clock text-success"></i> {{ $hour->hour }}:00 - {{ $hour->conversation_count }} conversations</li>
                                        @endforeach
                                    @endif
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Low Activity Hours:</h6>
                                <ul class="list-unstyled" id="low-hours-list">
                                    @if(isset($insights['peak_hours']['low_activity_hours']))
                                        @foreach($insights['peak_hours']['low_activity_hours']->take(3) as $hour)
                                        <li><i class="mdi mdi-clock text-muted"></i> {{ $hour->hour }}:00 - {{ $hour->conversation_count }} conversations</li>
                                        @endforeach
                                    @endif
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Conversation Flow</h4>
                    <div id="conversation-flow-chart" style="height: 350px;"></div>
                    
                    <div class="mt-3">
                        <h6>Flow Insights:</h6>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>Handoffs per conversation:</span>
                            <span class="badge bg-info">1.2 avg</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>Escalation rate:</span>
                            <span class="badge bg-warning">8.5%</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Resolution efficiency:</span>
                            <span class="badge bg-success">92.3%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Behavior and Staff Insights Row -->
    <div class="row">
        <div class="col-xl-6">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">User Behavior Analysis</h4>
                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap mb-0" id="user-behavior-table">
                            <thead class="table-light">
                                <tr>
                                    <th>User</th>
                                    <th>Conversations</th>
                                    <th>Avg Duration</th>
                                    <th>Engagement</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($insights['user_behavior']['top_users'] ?? [] as $user)
                                <tr>
                                    <td><strong>{{ $user->user_name }}</strong></td>
                                    <td>{{ $user->conversation_count }}</td>
                                    <td>{{ number_format($user->avg_duration, 1) }}m</td>
                                    <td>
                                        @php
                                            $engagement = $user->conversation_count > 5 ? 'high' : ($user->conversation_count > 2 ? 'medium' : 'low');
                                        @endphp
                                        <span class="badge bg-{{ $engagement === 'high' ? 'success' : ($engagement === 'medium' ? 'warning' : 'secondary') }}">
                                            {{ ucfirst($engagement) }}
                                        </span>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="4" class="text-center text-muted">No user behavior data available</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-6">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Staff Performance Insights</h4>
                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap mb-0" id="staff-insights-table">
                            <thead class="table-light">
                                <tr>
                                    <th>Staff Member</th>
                                    <th>Conversations</th>
                                    <th>Avg Handling Time</th>
                                    <th>Efficiency</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($insights['staff_insights']['staff_performance'] ?? [] as $staff)
                                <tr>
                                    <td><strong>{{ $staff->staff_name }}</strong></td>
                                    <td>{{ $staff->conversations_handled }}</td>
                                    <td>{{ number_format($staff->avg_handling_time, 1) }}m</td>
                                    <td>
                                        @php
                                            $efficiency = $staff->avg_handling_time < 15 ? 'high' : ($staff->avg_handling_time < 30 ? 'medium' : 'low');
                                        @endphp
                                        <span class="badge bg-{{ $efficiency === 'high' ? 'success' : ($efficiency === 'medium' ? 'warning' : 'danger') }}">
                                            {{ ucfirst($efficiency) }}
                                        </span>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="4" class="text-center text-muted">No staff insights data available</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Row -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Export & Actions</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-primary me-2" onclick="exportReport('json')">
                                <i class="mdi mdi-download"></i> Export JSON
                            </button>
                            <button type="button" class="btn btn-success me-2" onclick="exportReport('csv')">
                                <i class="mdi mdi-file-excel"></i> Export CSV
                            </button>
                            <button type="button" class="btn btn-danger" onclick="exportReport('pdf')">
                                <i class="mdi mdi-file-pdf"></i> Export PDF
                            </button>
                        </div>
                        <div class="col-md-6 text-end">
                            <button type="button" class="btn btn-info" onclick="refreshInsights()">
                                <i class="mdi mdi-refresh"></i> Refresh Insights
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh insights every 10 minutes
setInterval(function() {
    refreshInsights();
}, 600000);

// Handle filter form submission
document.getElementById('insights-filters').addEventListener('submit', function(e) {
    e.preventDefault();
    refreshInsights();
});

function refreshInsights() {
    const formData = new FormData(document.getElementById('insights-filters'));
    const params = new URLSearchParams(formData);
    
    fetch(`{{ route('admin.chat.insights.api.v1.insights.index') }}?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateDashboard(data.data);
            }
        })
        .catch(error => console.error('Error refreshing insights:', error));
}

function updateDashboard(insights) {
    // Update overview metrics
    document.getElementById('total-conversations').textContent = insights.overview.total_conversations;
    document.getElementById('completion-rate').textContent = insights.overview.completion_rate.toFixed(1) + '%';
    document.getElementById('avg-messages').textContent = insights.overview.average_messages_per_conversation.toFixed(1);
    document.getElementById('avg-duration').textContent = insights.overview.average_duration_minutes.toFixed(1) + 'm';
    
    // Update charts and tables would go here
    console.log('Dashboard updated with new insights:', insights);
}

function exportReport(format) {
    const formData = new FormData(document.getElementById('insights-filters'));
    formData.append('format', format);
    
    fetch('{{ route("admin.chat.insights.api.v1.export") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.download_url) {
                window.open(data.download_url, '_blank');
            } else {
                // Handle JSON export
                const blob = new Blob([JSON.stringify(data.data, null, 2)], {type: 'application/json'});
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `conversation-insights-${format}-${new Date().toISOString().split('T')[0]}.${format}`;
                a.click();
                window.URL.revokeObjectURL(url);
            }
        }
    })
    .catch(error => console.error('Error exporting report:', error));
}
</script>
@endsection
