<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\LoginHistory;
use App\Models\LoginHistoryPermission;
use App\Models\Role;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Carbon\Carbon;
use PHPUnit\Framework\Attributes\Test;

class AdminLoginHistoryTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Set locale for testing
        app()->setLocale('en');

        // Create roles
        Role::create(['name' => 'admin', 'slug' => 'admin', 'display_name' => 'Administrator']);
        Role::create(['name' => 'staff', 'slug' => 'staff', 'display_name' => 'Staff']);
        Role::create(['name' => 'customer', 'slug' => 'customer', 'display_name' => 'Customer']);
        Role::create(['name' => 'client', 'slug' => 'client', 'display_name' => 'Client']);
    }

    #[Test]
    public function admin_can_access_login_history_management_page()
    {
        // Mock the ActivityLogger to prevent memory issues in tests
        $this->mock(\App\Services\ActivityLogger::class, function ($mock) {
            $mock->shouldReceive('log')->andReturn(\App\Models\ActivityLog::factory()->make());
        });

        $admin = User::factory()->create(['role_id' => Role::where('name', 'admin')->first()->id]);

        // Test the route exists first
        $this->assertTrue(route('admin.login-histories.index') !== null);

        $response = $this->actingAs($admin)->get(route('admin.login-histories.index'));

        $response->assertStatus(200);
        $response->assertSee('Login History Management');
        $response->assertSee('Advanced Filters');
        $response->assertSee('Login History Data');
    }

    #[Test]
    public function staff_with_permission_can_access_login_history_management()
    {
        // Mock the ActivityLogger to prevent memory issues in tests
        $this->mock(\App\Services\ActivityLogger::class, function ($mock) {
            $mock->shouldReceive('log')->andReturn(\App\Models\ActivityLog::factory()->make());
        });

        $staffRole = Role::where('name', 'staff')->first();
        $adminRole = Role::where('name', 'admin')->first();

        $staff = User::factory()->create(['role_id' => $staffRole->id]);
        $admin = User::factory()->create(['role_id' => $adminRole->id]);

        // Ensure roles are loaded
        $staff->load('role');
        $admin->load('role');

        // Grant permission to staff
        LoginHistoryPermission::factory()->create([
            'user_id' => $staff->id,
            'granted_by_user_id' => $admin->id,
            'access_level' => 'partial',
            'specific_permissions' => ['view_basic_info'],
            'expires_at' => now()->addDays(30),
            'is_active' => true,
            'granted_at' => now()
        ]);

        $response = $this->actingAs($staff)->get(route('admin.login-histories.index'));

        $response->assertStatus(200);
        $response->assertSee('Login History Management');
    }

    #[Test]
    public function staff_without_permission_cannot_access_login_history_management()
    {
        $staff = User::factory()->create(['role_id' => Role::where('name', 'staff')->first()->id]);
        
        $response = $this->actingAs($staff)->get(route('admin.login-histories.index'));
        
        $response->assertStatus(403);
    }

    #[Test]
    public function customer_cannot_access_admin_login_history_management()
    {
        $customer = User::factory()->create(['role_id' => Role::where('name', 'customer')->first()->id]);
        
        $response = $this->actingAs($customer)->get(route('admin.login-histories.index'));
        
        $response->assertStatus(403);
    }

    #[Test]
    public function admin_can_view_all_users_login_history_data()
    {
        $admin = User::factory()->create(['role_id' => Role::where('name', 'admin')->first()->id]);
        $user1 = User::factory()->create(['role_id' => Role::where('name', 'customer')->first()->id]);
        $user2 = User::factory()->create(['role_id' => Role::where('name', 'customer')->first()->id]);
        
        // Create login history for different users
        LoginHistory::factory()->count(3)->create(['user_id' => $user1->id]);
        LoginHistory::factory()->count(2)->create(['user_id' => $user2->id]);
        
        $response = $this->actingAs($admin)->get(route('admin.login-histories.data'));
        
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertTrue($data['success']);
        $this->assertCount(5, $data['data']); // All login histories
        $this->assertEquals('full', $data['access_level']);
    }

    #[Test]
    public function staff_with_partial_access_gets_masked_sensitive_data()
    {
        $staff = User::factory()->create(['role_id' => Role::where('name', 'staff')->first()->id]);
        $user = User::factory()->create(['role_id' => Role::where('name', 'customer')->first()->id]);
        
        // Grant partial permission to staff
        LoginHistoryPermission::factory()->create([
            'user_id' => $staff->id,
            'access_level' => 'partial',
            'specific_permissions' => ['view_basic_info'],
            'expires_at' => now()->addDays(30)
        ]);
        
        // Create login history with sensitive data
        LoginHistory::factory()->create([
            'user_id' => $user->id,
            'ip_address' => '*************',
            'risk_score' => 75
        ]);
        
        $response = $this->actingAs($staff)->get(route('admin.login-histories.data'));
        
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertEquals('partial', $data['access_level']);
        
        $loginHistory = $data['data'][0];
        $this->assertEquals('192.168.1.xxx', $loginHistory['ip_address']); // IP should be masked
        $this->assertArrayNotHasKey('risk_score', $loginHistory); // Risk score should not be present
    }

    #[Test]
    public function admin_can_filter_login_history_by_user()
    {
        $admin = User::factory()->create(['role_id' => Role::where('name', 'admin')->first()->id]);
        $user1 = User::factory()->create(['role_id' => Role::where('name', 'customer')->first()->id]);
        $user2 = User::factory()->create(['role_id' => Role::where('name', 'customer')->first()->id]);
        
        // Create login history for different users
        LoginHistory::factory()->count(3)->create(['user_id' => $user1->id]);
        LoginHistory::factory()->count(2)->create(['user_id' => $user2->id]);
        
        $response = $this->actingAs($admin)->get(route('admin.login-histories.data', ['user_id' => $user1->id]));
        
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertCount(3, $data['data']); // Only user1's login histories
        
        foreach ($data['data'] as $history) {
            $this->assertEquals($user1->id, $history['user']['id']);
        }
    }

    #[Test]
    public function admin_can_filter_login_history_by_risk_level()
    {
        // Mock the ActivityLogger to prevent memory issues in tests
        $this->mock(\App\Services\ActivityLogger::class, function ($mock) {
            $mock->shouldReceive('log')->andReturn(\App\Models\ActivityLog::factory()->make());
        });

        $admin = User::factory()->create(['role_id' => Role::where('name', 'admin')->first()->id]);
        $user = User::factory()->create(['role_id' => Role::where('name', 'customer')->first()->id]);

        // Create login history with different risk scores (high risk = 80+, low risk = 0-30)
        LoginHistory::factory()->count(2)->create([
            'user_id' => $user->id,
            'risk_score' => 85 // High risk
        ]);

        LoginHistory::factory()->count(3)->create([
            'user_id' => $user->id,
            'risk_score' => 15 // Low risk
        ]);
        
        $response = $this->actingAs($admin)->get(route('admin.login-histories.data', ['risk_level' => 'critical']));

        $response->assertStatus(200);
        $data = $response->json();
        $this->assertTrue($data['success']);
        $this->assertGreaterThanOrEqual(2, count($data['data'])); // Should have high risk logins
    }

    #[Test]
    public function admin_can_view_detailed_login_history()
    {
        // Mock the ActivityLogger to prevent memory issues in tests
        $this->mock(\App\Services\ActivityLogger::class, function ($mock) {
            $mock->shouldReceive('log')->andReturn(\App\Models\ActivityLog::factory()->make());
        });

        $admin = User::factory()->create(['role_id' => Role::where('name', 'admin')->first()->id]);
        $user = User::factory()->create(['role_id' => Role::where('name', 'customer')->first()->id]);

        $loginHistory = LoginHistory::factory()->create([
            'user_id' => $user->id,
            'ip_address' => '*************',
            'risk_score' => 75,
            'security_flags' => ['test' => 'data'] // Use security_flags instead of security_metadata
        ]);
        
        $response = $this->actingAs($admin)->get(route('admin.login-histories.show', $loginHistory->uuid));
        
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertTrue($data['success']);
        $this->assertEquals('full', $data['access_level']);
        $this->assertEquals('*************', $data['data']['ip_address']); // Full IP visible
        $this->assertEquals(75, $data['data']['risk_score']); // Risk score visible
        $this->assertArrayHasKey('security_metadata', $data['data']);
    }

    #[Test]
    public function staff_with_partial_access_gets_masked_detailed_view()
    {
        // Mock the ActivityLogger to prevent memory issues in tests
        $this->mock(\App\Services\ActivityLogger::class, function ($mock) {
            $mock->shouldReceive('log')->andReturn(\App\Models\ActivityLog::factory()->make());
        });

        $staffRole = Role::where('name', 'staff')->first();
        $customerRole = Role::where('name', 'customer')->first();
        $adminRole = Role::where('name', 'admin')->first();

        $staff = User::factory()->create(['role_id' => $staffRole->id]);
        $user = User::factory()->create(['role_id' => $customerRole->id]);
        $admin = User::factory()->create(['role_id' => $adminRole->id]);

        // Ensure roles are loaded
        $staff->load('role');
        $user->load('role');
        $admin->load('role');

        // Grant partial permission to staff
        LoginHistoryPermission::factory()->create([
            'user_id' => $staff->id,
            'granted_by_user_id' => $admin->id,
            'access_level' => 'partial',
            'specific_permissions' => ['view_basic_info'],
            'expires_at' => now()->addDays(30),
            'is_active' => true,
            'granted_at' => now()
        ]);
        
        $loginHistory = LoginHistory::factory()->create([
            'user_id' => $user->id,
            'ip_address' => '*************',
            'risk_score' => 75
        ]);
        
        $response = $this->actingAs($staff)->get(route('admin.login-histories.show', $loginHistory->uuid));
        
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertEquals('partial', $data['access_level']);
        $this->assertEquals('192.168.1.xxx', $data['data']['ip_address']); // IP should be masked
        $this->assertArrayNotHasKey('risk_score', $data['data']); // Risk score should not be present
    }

    #[Test]
    public function admin_can_export_login_history_data()
    {
        $admin = User::factory()->create(['role_id' => Role::where('name', 'admin')->first()->id]);
        $user = User::factory()->create(['role_id' => Role::where('name', 'customer')->first()->id]);
        
        LoginHistory::factory()->count(5)->create(['user_id' => $user->id]);
        
        // Test CSV export
        $response = $this->actingAs($admin)->get(route('admin.login-histories.export', ['format' => 'csv']));
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'text/csv; charset=UTF-8');
        
        // Test JSON export
        $response = $this->actingAs($admin)->get(route('admin.login-histories.export', ['format' => 'json']));
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/json');
    }

    #[Test]
    public function staff_with_partial_access_cannot_export_data()
    {
        $staff = User::factory()->create(['role_id' => Role::where('name', 'staff')->first()->id]);
        
        // Grant partial permission to staff
        LoginHistoryPermission::factory()->create([
            'user_id' => $staff->id,
            'access_level' => 'partial',
            'expires_at' => now()->addDays(30)
        ]);
        
        $response = $this->actingAs($staff)->get(route('admin.login-histories.export'));
        
        $response->assertStatus(403);
    }

    #[Test]
    public function admin_can_get_login_history_statistics()
    {
        $admin = User::factory()->create(['role_id' => Role::where('name', 'admin')->first()->id]);
        $user = User::factory()->create(['role_id' => Role::where('name', 'customer')->first()->id]);
        
        // Create various login histories
        LoginHistory::factory()->count(3)->create([
            'user_id' => $user->id,
            'login_status' => 'success',
            'created_at' => now()->subDays(1)
        ]);

        LoginHistory::factory()->count(2)->create([
            'user_id' => $user->id,
            'login_status' => 'failed',
            'created_at' => now()->subDays(1)
        ]);
        
        $response = $this->actingAs($admin)->get(route('admin.login-histories.statistics'));
        
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertTrue($data['success']);
        $this->assertArrayHasKey('total_logins', $data['data']);
        $this->assertArrayHasKey('successful_logins', $data['data']);
        $this->assertArrayHasKey('failed_logins', $data['data']);
        $this->assertArrayHasKey('success_rate', $data['data']);
    }

    #[Test]
    public function login_history_management_page_shows_correct_access_level()
    {
        // Test admin access
        $admin = User::factory()->create(['role_id' => Role::where('name', 'admin')->first()->id]);
        $response = $this->actingAs($admin)->get(route('admin.login-histories.index'));
        $response->assertStatus(200);
        $response->assertSee('Login History Management');
        $response->assertSee('full'); // Check for access level in content
        
        // Test staff with partial access
        $staff = User::factory()->create(['role_id' => Role::where('name', 'staff')->first()->id]);
        LoginHistoryPermission::factory()->create([
            'user_id' => $staff->id,
            'access_level' => 'partial',
            'specific_permissions' => ['view_basic_info'],
            'expires_at' => now()->addDays(30)
        ]);
        
        $response = $this->actingAs($staff)->get(route('admin.login-histories.index'));
        $response->assertStatus(200);
        $response->assertSee('Login History Management');
        $response->assertSee('partial'); // Check for access level in content
    }

    #[Test]
    public function sidebar_shows_login_histories_link_for_authorized_users()
    {
        // Test admin can see the link
        $admin = User::factory()->create(['role_id' => Role::where('name', 'admin')->first()->id]);
        $response = $this->actingAs($admin)->get(route('admin.dashboard'));
        $response->assertStatus(200);
        $response->assertSee('Login Histories');
        
        // Test staff with permission can see the link
        $staff = User::factory()->create(['role_id' => Role::where('name', 'staff')->first()->id]);
        LoginHistoryPermission::factory()->create([
            'user_id' => $staff->id,
            'access_level' => 'partial',
            'expires_at' => now()->addDays(30)
        ]);
        
        $response = $this->actingAs($staff)->get(route('admin.dashboard'));
        $response->assertStatus(200);
        $response->assertSee('Login Histories');
    }
}
