<?php

namespace App\Services;

use App\Models\ActivityLog;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Jenssegers\Agent\Agent;

class ActivityLogger
{
    protected Agent $agent;
    protected Request $request;

    public function __construct()
    {
        $this->agent = new Agent();
        $this->request = request();
    }

    /**
     * Log password reset request activity.
     */
    public function logPasswordResetRequest(
        string $email,
        bool $emailExists,
        bool $wasRateLimited = false,
        ?string $failureReason = null
    ): ActivityLog {
        $user = User::where('email', $email)->first();

        // Always show success to user for security, but log the real situation for admin
        $publicStatus = 'success';
        $adminDescription = $this->getAdminDescription($email, $emailExists, $wasRateLimited);
        $riskScore = $this->calculateRiskScore($email, $emailExists);

        // Increase risk score for non-existent emails (potential enumeration attempt)
        if (!$emailExists) {
            $riskScore += 25; // Higher risk for email enumeration attempts
        }

        if ($wasRateLimited) {
            $failureReason = 'Rate limited - too many requests';
            $riskScore += 20; // Additional risk for rate limiting
        }

        // Flag as suspicious if high risk score or multiple non-existent email attempts
        $isSuspicious = $riskScore >= 50 || $this->isRepeatedEnumerationAttempt($email);

        return $this->createActivityLog([
            'user_id' => $user?->id,
            'user_email' => $email,
            'user_name' => $user ? "{$user->first_name} {$user->last_name}" : null,
            'activity_type' => 'password_reset_request',
            'activity_description' => $adminDescription,
            'status' => $publicStatus, // Always success for user
            'failure_reason' => $failureReason,
            'risk_score' => $riskScore,
            'is_suspicious' => $isSuspicious,
            'security_notes' => $this->getSecurityNotes($email, $emailExists, $wasRateLimited),
            'request_data' => [
                'email_submitted' => $email,
                'email_exists_in_system' => $emailExists,
                'user_account_found' => $user ? true : false,
                'user_account_active' => $user?->is_active ?? false,
                'was_rate_limited' => $wasRateLimited,
                'enumeration_attempt' => !$emailExists,
                'form_data' => $this->sanitizeRequestData($this->request->all()),
                'admin_notes' => $emailExists
                    ? 'Valid email - reset link sent to existing user'
                    : 'ENUMERATION ATTEMPT - Email does not exist in system',
            ],
            'response_data' => [
                'public_response' => 'success', // What user sees
                'public_message' => 'If an account with that email address exists, you will receive a password reset link shortly.',
                'actual_email_exists' => $emailExists,
                'actual_email_sent' => $emailExists && !$wasRateLimited,
                'admin_action_taken' => $emailExists && !$wasRateLimited ? 'Email sent' : 'No email sent',
                'security_status' => $emailExists ? 'legitimate_request' : 'potential_enumeration',
            ],
        ]);
    }

    /**
     * Log successful password reset.
     */
    public function logPasswordResetSuccess(User $user, string $token): ActivityLog
    {
        return $this->createActivityLog([
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_name' => "{$user->first_name} {$user->last_name}",
            'activity_type' => 'password_reset_success',
            'activity_description' => 'Password successfully reset using valid token',
            'status' => 'success',
            'risk_score' => 10, // Low risk for successful reset
            'request_data' => [
                'token_used' => substr($token, 0, 8) . '...', // Partial token for reference
                'form_data' => $this->sanitizeRequestData($this->request->all()),
            ],
            'response_data' => [
                'password_changed' => true,
                'sessions_cleared' => true,
                'remember_tokens_cleared' => true,
            ],
        ]);
    }

    /**
     * Log failed password reset attempt.
     */
    public function logPasswordResetFailed(
        string $email,
        string $reason,
        ?string $token = null
    ): ActivityLog {
        $user = User::where('email', $email)->first();
        $riskScore = $this->calculateFailureRiskScore($reason);

        return $this->createActivityLog([
            'user_id' => $user?->id,
            'user_email' => $email,
            'user_name' => $user ? "{$user->first_name} {$user->last_name}" : null,
            'activity_type' => 'password_reset_failed',
            'activity_description' => "Password reset failed: {$reason}",
            'status' => 'failed',
            'failure_reason' => $reason,
            'risk_score' => $riskScore,
            'is_suspicious' => $riskScore >= 60,
            'request_data' => [
                'email' => $email,
                'token_provided' => $token ? (substr($token, 0, 8) . '...') : null,
                'failure_type' => $reason,
                'form_data' => $this->sanitizeRequestData($this->request->all()),
            ],
        ]);
    }

    /**
     * Get session ID safely, handling cases where session is not available.
     */
    protected function getSessionId(): ?string
    {
        try {
            if ($this->request->hasSession() && session()->isStarted()) {
                return session()->getId();
            }
        } catch (\Exception $e) {
            // Session not available, return null
        }

        return null;
    }

    /**
     * Get session duration safely.
     */
    protected function getSessionDuration(): ?int
    {
        try {
            if ($this->request->hasSession() && session()->isStarted()) {
                $loginTime = session()->get('login_time');
                return $loginTime ? now()->diffInMinutes($loginTime) : null;
            }
        } catch (\Exception $e) {
            // Session not available, return null
        }

        return null;
    }

    /**
     * Create activity log with common data.
     */
    protected function createActivityLog(array $data): ActivityLog
    {
        $deviceInfo = $this->getDeviceInfo();
        $locationInfo = $this->getLocationInfo();

        $commonData = [
            'uuid' => Str::uuid(),
            'ip_address' => $this->request->ip(),
            'user_agent' => $this->request->userAgent() ?? "Missing or Unknown User Agent",
            'device_type' => $deviceInfo['type'],
            'browser' => $deviceInfo['browser'],
            'platform' => $deviceInfo['platform'],
            'country' => $locationInfo['country'],
            'region' => $locationInfo['region'],
            'city' => $locationInfo['city'],
            'latitude' => $locationInfo['latitude'],
            'longitude' => $locationInfo['longitude'],
            'url' => $this->request->fullUrl(),
            'method' => $this->request->method(),
            'session_id' => $this->getSessionId(),
            'request_id' => $this->request->header('X-Request-ID') ?? Str::uuid(),
            'occurred_at' => now(),
        ];

        return ActivityLog::create(array_merge($commonData, $data));
    }

    /**
     * Get device information from user agent.
     */
    protected function getDeviceInfo(): array
    {
        return [
            'type' => $this->agent->deviceType(),
            'browser' => $this->agent->browser() . ' ' . $this->agent->version($this->agent->browser()),
            'platform' => $this->agent->platform() . ' ' . $this->agent->version($this->agent->platform()),
        ];
    }

    /**
     * Get location information from IP address.
     */
    protected function getLocationInfo(): array
    {
        $ip = $this->request->ip();
        
        // Skip location lookup for local IPs
        if ($this->isLocalIp($ip)) {
            return [
                'country' => 'Local',
                'region' => 'Local',
                'city' => 'Local',
                'latitude' => null,
                'longitude' => null,
            ];
        }

        // Use circuit breaker for external API calls
        $circuitBreaker = new \App\Services\CircuitBreakerService('ip_geolocation', 3, 300);

        try {
            return $circuitBreaker->call(
                function () use ($ip) {
                    $response = Http::timeout(5)->get("http://ip-api.com/json/{$ip}");

                    if ($response->successful()) {
                        $data = $response->json();

                        return [
                            'country' => $data['country'] ?? null,
                            'region' => $data['regionName'] ?? null,
                            'city' => $data['city'] ?? null,
                            'latitude' => $data['lat'] ?? null,
                            'longitude' => $data['lon'] ?? null,
                        ];
                    }

                    throw new \Exception('Geolocation API returned non-successful response');
                },
                function (\Exception $e = null) {
                    // Fallback when circuit breaker is open or service fails
                    \Log::warning('Geolocation service unavailable, using fallback', [
                        'ip' => request()->ip(),
                        'error' => $e?->getMessage(),
                        'circuit_breaker_status' => app(\App\Services\CircuitBreakerService::class, ['ip_geolocation'])->getStatus()
                    ]);

                    return [
                        'country' => 'Unknown',
                        'region' => 'Unknown',
                        'city' => 'Unknown',
                        'latitude' => null,
                        'longitude' => null,
                    ];
                }
            );
        } catch (\Exception $e) {
            // Final fallback if everything fails
            \Log::error('Complete geolocation failure', [
                'ip' => $ip,
                'error' => $e->getMessage()
            ]);
        }

        return [
            'country' => null,
            'region' => null,
            'city' => null,
            'latitude' => null,
            'longitude' => null,
        ];
    }

    /**
     * Check if IP address is local.
     */
    protected function isLocalIp(string $ip): bool
    {
        return in_array($ip, ['127.0.0.1', '::1', 'localhost']) || 
               filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false;
    }

    /**
     * Calculate risk score for password reset request.
     */
    protected function calculateRiskScore(string $email, bool $emailExists): int
    {
        $score = 0;
        
        // Base score for non-existent email
        if (!$emailExists) {
            $score += 30;
        }
        
        // Check for recent failed attempts from same IP
        $recentFailures = ActivityLog::byIpAddress($this->request->ip())
            ->where('activity_type', 'password_reset_request')
            ->where('status', 'failed')
            ->recent(1)
            ->count();
        
        $score += min($recentFailures * 10, 40);
        
        // Check for suspicious user agent patterns
        $userAgent = $this->request->userAgent();
        if (empty($userAgent) || str_contains(strtolower($userAgent), 'bot')) {
            $score += 25;
        }
        
        return min($score, 100);
    }

    /**
     * Calculate risk score for failed password reset.
     */
    protected function calculateFailureRiskScore(string $reason): int
    {
        return match ($reason) {
            'Invalid token' => 40,
            'Expired token' => 20,
            'Token already used' => 30,
            'Invalid email' => 50,
            'Validation failed' => 25,
            default => 35,
        };
    }

    /**
     * Get admin-friendly description of the password reset attempt.
     */
    protected function getAdminDescription(string $email, bool $emailExists, bool $wasRateLimited): string
    {
        if ($wasRateLimited) {
            return "Password reset request RATE LIMITED for: {$email}";
        }

        if ($emailExists) {
            return "Password reset requested for EXISTING user: {$email}";
        }

        return "Password reset requested for NON-EXISTENT email: {$email} (ENUMERATION ATTEMPT)";
    }

    /**
     * Get security notes for admin review.
     */
    protected function getSecurityNotes(string $email, bool $emailExists, bool $wasRateLimited): ?string
    {
        $notes = [];

        if (!$emailExists) {
            $notes[] = "⚠️ EMAIL ENUMERATION: User attempted to reset password for non-existent email";
        }

        if ($wasRateLimited) {
            $notes[] = "🚫 RATE LIMITED: Too many attempts from this IP/session";
        }

        if ($this->isRepeatedEnumerationAttempt($email)) {
            $notes[] = "🔍 REPEATED ENUMERATION: Multiple non-existent email attempts detected";
        }

        if ($this->isSuspiciousEmailPattern($email)) {
            $notes[] = "🤖 SUSPICIOUS PATTERN: Email follows common enumeration patterns";
        }

        return empty($notes) ? null : implode(' | ', $notes);
    }

    /**
     * Check if this is a repeated enumeration attempt from the same source.
     */
    protected function isRepeatedEnumerationAttempt(string $email): bool
    {
        $recentEnumerationAttempts = ActivityLog::byIpAddress($this->request->ip())
            ->where('activity_type', 'password_reset_request')
            ->where('request_data->enumeration_attempt', true)
            ->recent(1) // Last hour
            ->count();

        return $recentEnumerationAttempts >= 2;
    }

    /**
     * Check if email follows suspicious enumeration patterns.
     */
    protected function isSuspiciousEmailPattern(string $email): bool
    {
        $suspiciousPatterns = [
            '/^admin@/',
            '/^test@/',
            '/^user\d+@/',
            '/^[a-z]{1,3}@/',
            '/^\d+@/',
            '/@test\./',
            '/@example\./',
            '/@gmail\.com$/',
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, strtolower($email))) {
                return true;
            }
        }

        return false;
    }

    /**
     * Generic log method for backward compatibility.
     */
    public function log(
        string $activityType,
        ?User $user = null,
        array $data = []
    ): ActivityLog {
        return $this->logActivity(
            $activityType,
            $data['description'] ?? "Activity: {$activityType}",
            $data['status'] ?? 'success',
            $data['failure_reason'] ?? null,
            $data,
            [],
            null, // riskScore
            $user
        );
    }

    /**
     * Log generic activity (supports both authenticated and unauthenticated users).
     */
    public function logActivity(
        string $activityType,
        string $description,
        string $status = 'success',
        ?string $failureReason = null,
        array $requestData = [],
        array $responseData = [],
        ?int $riskScore = null,
        ?User $user = null
    ): ActivityLog {
        $user = $user ?? auth()->user();

        // Calculate risk score if not provided
        if ($riskScore === null) {
            $riskScore = $this->calculateGenericRiskScore($activityType, $status);
        }

        return $this->createActivityLog([
            'user_id' => $user?->id,
            'user_email' => $user?->email,
            'user_name' => $user ? "{$user->first_name} {$user->last_name}" : null,
            'activity_type' => $activityType,
            'activity_description' => $description,
            'status' => $status,
            'failure_reason' => $failureReason,
            'risk_score' => $riskScore,
            'is_suspicious' => $riskScore >= 60,
            'request_data' => array_merge([
                'form_data' => $this->sanitizeRequestData($this->request->all()),
                'route_name' => $this->request->route()?->getName(),
                'route_parameters' => $this->request->route()?->parameters() ?? [],
            ], $requestData),
            'response_data' => $responseData,
        ]);
    }

    /**
     * Log generic customer dashboard activity.
     */
    public function logCustomerActivity(
        string $activityType,
        string $description,
        string $status = 'success',
        ?string $failureReason = null,
        array $requestData = [],
        array $responseData = [],
        ?int $riskScore = null
    ): ActivityLog {
        return $this->logActivity(
            $activityType,
            $description,
            $status,
            $failureReason,
            $requestData,
            $responseData,
            $riskScore,
            auth()->user()
        );
    }

    /**
     * Log career-related activity.
     */
    public function logCareerActivity(
        string $activityType,
        string $description,
        string $status = 'success',
        ?string $failureReason = null,
        array $requestData = [],
        array $responseData = [],
        ?int $riskScore = null
    ): ActivityLog {
        $user = auth()->user();

        // Calculate risk score if not provided
        if ($riskScore === null) {
            $riskScore = $this->calculateCareerRiskScore($activityType, $status);
        }

        return $this->createActivityLog([
            'user_id' => $user?->id,
            'user_email' => $user?->email ?? $requestData['email'] ?? null,
            'user_name' => $user ? "{$user->first_name} {$user->last_name}" : ($requestData['applicant_name'] ?? null),
            'activity_type' => $activityType,
            'activity_description' => $description,
            'status' => $status,
            'failure_reason' => $failureReason,
            'risk_score' => $riskScore,
            'is_suspicious' => $riskScore >= 60,
            'request_data' => array_merge([
                'form_data' => $this->sanitizeRequestData($this->request->all()),
                'route_name' => $this->request->route()?->getName(),
                'route_parameters' => $this->request->route()?->parameters() ?? [],
            ], $requestData),
            'response_data' => $responseData,
        ]);
    }

    /**
     * Log job application submission.
     */
    public function logJobApplication(
        int $jobId,
        string $jobTitle,
        bool $success,
        ?string $failureReason = null,
        array $applicationData = [],
        array $fileUploadResults = []
    ): ActivityLog {
        $user = auth()->user();
        $applicantName = $user ? "{$user->first_name} {$user->last_name}" :
                        ($applicationData['first_name'] ?? '') . ' ' . ($applicationData['last_name'] ?? '');

        $description = $success
            ? "Job application submitted successfully for position: {$jobTitle}"
            : "Job application submission failed for position: {$jobTitle}";

        if (!empty($fileUploadResults)) {
            $fileCount = count($fileUploadResults);
            $description .= " ({$fileCount} file(s) processed)";
        }

        return $this->logCareerActivity(
            'job_application_submit',
            $description,
            $success ? 'success' : 'failed',
            $failureReason,
            [
                'job_id' => $jobId,
                'job_title' => $jobTitle,
                'applicant_name' => trim($applicantName),
                'email' => $user?->email ?? $applicationData['email'] ?? null,
                'is_authenticated' => $user !== null,
                'application_data' => $this->sanitizeApplicationData($applicationData),
                'file_uploads' => $this->sanitizeFileUploadData($fileUploadResults),
            ],
            [
                'application_submitted' => $success,
                'files_uploaded' => count($fileUploadResults),
                'virus_scans_passed' => collect($fileUploadResults)->where('virus_scan_passed', true)->count(),
            ],
            $this->calculateJobApplicationRiskScore($success, $fileUploadResults)
        );
    }

    /**
     * Log job viewing activity.
     */
    public function logJobView(int $jobId, string $jobTitle): ActivityLog
    {
        $user = auth()->user();

        return $this->logCareerActivity(
            'job_view',
            "Viewed job posting: {$jobTitle}",
            'success',
            null,
            [
                'job_id' => $jobId,
                'job_title' => $jobTitle,
                'is_authenticated' => $user !== null,
            ],
            [
                'job_viewed' => true,
            ],
            5 // Low risk for viewing jobs
        );
    }

    /**
     * Log application status check.
     */
    public function logApplicationStatusCheck(
        string $referenceNumber,
        bool $found,
        ?string $applicationType = 'job'
    ): ActivityLog {
        $description = $found
            ? "Application status checked successfully for reference: {$referenceNumber}"
            : "Application status check failed - reference not found: {$referenceNumber}";

        $riskScore = $found ? 10 : 25; // Higher risk for failed lookups (potential enumeration)

        return $this->logCareerActivity(
            'application_status_check',
            $description,
            $found ? 'success' : 'failed',
            $found ? null : 'Reference number not found',
            [
                'reference_number' => $referenceNumber,
                'application_type' => $applicationType,
            ],
            [
                'application_found' => $found,
            ],
            $riskScore
        );
    }

    /**
     * Log profile update activity.
     */
    public function logProfileUpdate(
        array $changes,
        bool $success = true,
        ?string $failureReason = null
    ): ActivityLog {
        $user = auth()->user();
        $changeTypes = array_keys($changes);

        $description = $success
            ? "Profile updated: " . implode(', ', $changeTypes)
            : "Profile update failed: " . ($failureReason ?? 'Unknown error');

        return $this->logCustomerActivity(
            'profile_update',
            $description,
            $success ? 'success' : 'failed',
            $failureReason,
            [
                'fields_changed' => $changeTypes,
                'changes_made' => $this->sanitizeProfileChanges($changes),
                'user_role' => $user?->role?->name,
            ],
            [
                'profile_updated' => $success,
                'fields_affected' => count($changeTypes),
            ],
            $success ? 5 : 25
        );
    }

    /**
     * Log project application activity.
     */
    public function logProjectApplicationActivity(
        string $action,
        ?int $applicationId = null,
        array $applicationData = [],
        bool $success = true,
        ?string $failureReason = null
    ): ActivityLog {
        $user = auth()->user();

        $descriptions = [
            'create' => $success ? 'Project application submitted' : 'Project application submission failed',
            'update' => $success ? 'Project application updated' : 'Project application update failed',
            'delete' => $success ? 'Project application deleted' : 'Project application deletion failed',
            'view' => 'Project application viewed',
        ];

        $description = $descriptions[$action] ?? "Project application {$action}";
        if ($applicationId) {
            $description .= " (ID: {$applicationId})";
        }

        return $this->logCustomerActivity(
            "project_application_{$action}",
            $description,
            $success ? 'success' : 'failed',
            $failureReason,
            [
                'application_id' => $applicationId,
                'action_performed' => $action,
                'application_data' => $this->sanitizeApplicationData($applicationData),
                'user_role' => $user?->role?->name,
            ],
            [
                'action_completed' => $success,
                'application_affected' => $applicationId,
            ],
            $this->calculateApplicationRiskScore($action, $success)
        );
    }

    /**
     * Log file upload activity.
     */
    public function logFileUpload(
        string $fileName,
        string $fileType,
        int $fileSize,
        string $uploadContext,
        bool $success = true,
        ?string $failureReason = null,
        array $securityScanResults = []
    ): ActivityLog {
        $user = auth()->user();

        $description = $success
            ? "File uploaded: {$fileName} ({$fileType}, " . $this->formatFileSize($fileSize) . ")"
            : "File upload failed: {$fileName} - " . ($failureReason ?? 'Unknown error');

        return $this->logCustomerActivity(
            'file_upload',
            $description,
            $success ? 'success' : 'failed',
            $failureReason,
            [
                'file_name' => $fileName,
                'file_type' => $fileType,
                'file_size' => $fileSize,
                'file_size_formatted' => $this->formatFileSize($fileSize),
                'upload_context' => $uploadContext,
                'security_scan_results' => $securityScanResults,
                'user_role' => $user?->role?->name,
            ],
            [
                'upload_completed' => $success,
                'file_processed' => $success,
                'security_passed' => empty($securityScanResults['threats_found'] ?? []),
            ],
            $this->calculateFileUploadRiskScore($fileType, $fileSize, $securityScanResults, $success)
        );
    }

    /**
     * Log order activity.
     */
    public function logOrderActivity(
        string $action,
        ?int $orderId = null,
        array $orderData = [],
        bool $success = true,
        ?string $failureReason = null
    ): ActivityLog {
        $user = auth()->user();

        $descriptions = [
            'create' => $success ? 'Order placed' : 'Order placement failed',
            'update' => $success ? 'Order updated' : 'Order update failed',
            'cancel' => $success ? 'Order cancelled' : 'Order cancellation failed',
            'view' => 'Order viewed',
        ];

        $description = $descriptions[$action] ?? "Order {$action}";
        if ($orderId) {
            $description .= " (Order #: {$orderId})";
        }

        return $this->logCustomerActivity(
            "order_{$action}",
            $description,
            $success ? 'success' : 'failed',
            $failureReason,
            [
                'order_id' => $orderId,
                'action_performed' => $action,
                'order_data' => $this->sanitizeOrderData($orderData),
                'user_role' => $user?->role?->name,
            ],
            [
                'action_completed' => $success,
                'order_affected' => $orderId,
            ],
            $this->calculateOrderRiskScore($action, $success)
        );
    }

    /**
     * Log dashboard access.
     */
    public function logDashboardAccess(
        string $section = 'main',
        array $additionalData = []
    ): ActivityLog {
        $user = auth()->user();

        return $this->logCustomerActivity(
            'dashboard_access',
            "Dashboard accessed: {$section}",
            'success',
            null,
            array_merge([
                'dashboard_section' => $section,
                'user_role' => $user?->role?->name,
                'session_duration' => $this->getSessionDuration(),
            ], $additionalData),
            [
                'access_granted' => true,
                'section_loaded' => $section,
            ],
            2 // Very low risk for dashboard access
        );
    }

    /**
     * Calculate generic risk score based on activity type and status.
     */
    protected function calculateGenericRiskScore(string $activityType, string $status): int
    {
        $baseScores = [
            'profile_update' => 10,
            'project_application_create' => 5,
            'project_application_update' => 5,
            'project_application_delete' => 15,
            'file_upload' => 20,
            'order_create' => 10,
            'order_update' => 10,
            'order_cancel' => 15,
            'dashboard_access' => 2,
        ];

        $baseScore = $baseScores[$activityType] ?? 10;

        // Increase score for failed activities
        if ($status === 'failed') {
            $baseScore += 25;

            // Check for recent failed attempts from same user/IP to detect enumeration
            $userId = auth()->id();
            $ipAddress = $this->request->ip();

            $recentFailures = ActivityLog::where(function($query) use ($userId, $ipAddress) {
                if ($userId) {
                    $query->where('user_id', $userId);
                } else {
                    $query->where('ip_address', $ipAddress);
                }
            })
            ->where('status', 'failed')
            ->where('occurred_at', '>', now()->subMinutes(10))
            ->count();

            // Add additional risk for multiple recent failures (enumeration attempts)
            $baseScore += min($recentFailures * 15, 45);
        }

        return min($baseScore, 100);
    }

    /**
     * Calculate risk score for project applications.
     */
    protected function calculateApplicationRiskScore(string $action, bool $success): int
    {
        $scores = [
            'create' => $success ? 5 : 25,
            'update' => $success ? 5 : 20,
            'delete' => $success ? 15 : 30,
            'view' => 2,
        ];

        return $scores[$action] ?? 10;
    }

    /**
     * Calculate risk score for career-related activities.
     */
    protected function calculateCareerRiskScore(string $activityType, string $status): int
    {
        $baseScores = [
            'job_view' => 5,
            'job_application_submit' => 10,
            'application_status_check' => 10,
            'file_upload' => 15,
        ];

        $baseScore = $baseScores[$activityType] ?? 10;

        // Increase risk for failed activities
        if ($status === 'failed') {
            $baseScore += 15;
        }

        return $baseScore;
    }

    /**
     * Calculate risk score for job applications.
     */
    protected function calculateJobApplicationRiskScore(bool $success, array $fileUploadResults = []): int
    {
        $baseScore = $success ? 10 : 30;

        // Add risk for file uploads
        $fileCount = count($fileUploadResults);
        if ($fileCount > 0) {
            $baseScore += min($fileCount * 2, 10); // Max 10 additional points for files

            // Check for failed virus scans
            $failedScans = collect($fileUploadResults)->where('virus_scan_passed', false)->count();
            if ($failedScans > 0) {
                $baseScore += $failedScans * 20; // High risk for virus scan failures
            }
        }

        return min($baseScore, 100); // Cap at 100
    }

    /**
     * Calculate risk score for file uploads.
     */
    protected function calculateFileUploadRiskScore(
        string $fileType,
        int $fileSize,
        array $securityScanResults,
        bool $success
    ): int {
        $score = $success ? 10 : 40;

        // Increase score for executable file types
        $dangerousTypes = ['exe', 'bat', 'cmd', 'scr', 'pif', 'com'];
        $extension = strtolower(pathinfo($fileType, PATHINFO_EXTENSION));
        if (in_array($extension, $dangerousTypes)) {
            $score += 50;
        }

        // Increase score for large files
        if ($fileSize > 50 * 1024 * 1024) { // 50MB
            $score += 15;
        }

        // Increase score if security threats found
        if (!empty($securityScanResults['threats_found'] ?? [])) {
            $score += 60;
        }

        return min($score, 100);
    }

    /**
     * Calculate risk score for orders.
     */
    protected function calculateOrderRiskScore(string $action, bool $success): int
    {
        $scores = [
            'create' => $success ? 10 : 30,
            'update' => $success ? 8 : 25,
            'cancel' => $success ? 15 : 25,
            'view' => 2,
        ];

        return $scores[$action] ?? 10;
    }

    /**
     * Format file size for display.
     */
    protected function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Sanitize profile changes for logging.
     */
    protected function sanitizeProfileChanges(array $changes): array
    {
        $sensitive = ['password', 'password_confirmation'];

        foreach ($sensitive as $field) {
            if (isset($changes[$field])) {
                $changes[$field] = '***REDACTED***';
            }
        }

        return $changes;
    }

    /**
     * Sanitize application data for logging.
     */
    protected function sanitizeApplicationData(array $data): array
    {
        // Remove sensitive file data but keep metadata
        if (isset($data['attachments'])) {
            $data['attachments'] = array_map(function ($attachment) {
                // Handle File objects (from tests or uploads)
                if ($attachment instanceof \Illuminate\Http\UploadedFile || $attachment instanceof \Illuminate\Http\Testing\File) {
                    return [
                        'file_name' => $attachment->getClientOriginalName(),
                        'file_size' => $attachment->getSize(),
                        'file_type' => $attachment->getMimeType(),
                    ];
                }

                // Handle array data (processed attachments)
                if (is_array($attachment)) {
                    return [
                        'file_name' => $attachment['file_name'] ?? $attachment['original_name'] ?? 'unknown',
                        'file_size' => $attachment['file_size'] ?? $attachment['size'] ?? 0,
                        'file_type' => $attachment['file_type'] ?? $attachment['mime_type'] ?? 'unknown',
                    ];
                }

                // Fallback for unknown types
                return [
                    'file_name' => 'unknown',
                    'file_size' => 0,
                    'file_type' => 'unknown',
                ];
            }, $data['attachments']);
        }

        return $this->sanitizeRequestData($data);
    }

    /**
     * Sanitize file upload data for logging.
     */
    protected function sanitizeFileUploadData(array $fileResults): array
    {
        return array_map(function ($result) {
            return [
                'original_name' => $result['original_name'] ?? 'unknown',
                'size' => $result['size'] ?? 0,
                'mime_type' => $result['mime_type'] ?? 'unknown',
                'virus_scan_passed' => $result['virus_scan_passed'] ?? false,
                'scan_engine' => $result['scan_engine'] ?? 'unknown',
                'processing_success' => $result['success'] ?? false,
            ];
        }, $fileResults);
    }

    /**
     * Sanitize order data for logging.
     */
    protected function sanitizeOrderData(array $data): array
    {
        $sensitive = ['payment_method', 'card_number', 'cvv'];

        foreach ($sensitive as $field) {
            if (isset($data[$field])) {
                $data[$field] = '***REDACTED***';
            }
        }

        return $data;
    }

    /**
     * Log cart activity with business context.
     */
    public function logCartActivity(
        string $action,
        ?int $cartId = null,
        array $cartData = [],
        bool $success = true,
        ?string $failureReason = null
    ): ActivityLog {
        $user = auth()->user();

        $descriptions = [
            'add_item' => $success ? 'Item added to cart' : 'Failed to add item to cart',
            'update_item' => $success ? 'Cart item updated' : 'Failed to update cart item',
            'remove_item' => $success ? 'Item removed from cart' : 'Failed to remove cart item',
            'clear' => $success ? 'Cart cleared' : 'Failed to clear cart',
            'checkout' => $success ? 'Checkout initiated' : 'Checkout failed',
            'merge' => $success ? 'Guest cart merged with user cart' : 'Failed to merge carts',
        ];

        $description = $descriptions[$action] ?? "Cart {$action}";
        if ($cartId) {
            $description .= " (Cart ID: {$cartId})";
        }

        // Calculate risk score based on action and success
        $riskScore = $this->calculateCartRiskScore($action, $success, $cartData);

        return $this->createActivityLog([
            'user_id' => $user?->id,
            'user_email' => $user?->email,
            'user_name' => $user ? "{$user->first_name} {$user->last_name}" : null,
            'activity_type' => 'cart_activity',
            'activity_description' => $description,
            'status' => $success ? 'success' : 'failed',
            'failure_reason' => $failureReason,
            'risk_score' => $riskScore,
            'is_suspicious' => $riskScore >= 60,
            'request_data' => array_merge([
                'cart_id' => $cartId,
                'action' => $action,
                'cart_data' => $cartData,
                'form_data' => $this->sanitizeRequestData($this->request->all()),
                'route_name' => $this->request->route()?->getName(),
                'route_parameters' => $this->request->route()?->parameters() ?? [],
            ], $cartData),
            'response_data' => [
                'action_completed' => $success,
                'cart_affected' => $cartId,
                'requires_audit' => $action === 'checkout' || !$success,
                'business_impact' => $this->assessCartBusinessImpact($action, $success, $cartData),
            ],
        ]);
    }

    /**
     * Log comprehensive checkout activity.
     */
    public function logCheckoutActivity(
        string $step,
        array $checkoutData = [],
        bool $success = true,
        ?string $failureReason = null,
        ?int $orderId = null,
        array $validationErrors = []
    ): ActivityLog {
        $user = auth()->user();

        $stepDescriptions = [
            'initiated' => $success ? 'Checkout process initiated' : 'Failed to initiate checkout',
            'cart_review' => $success ? 'Cart reviewed in checkout' : 'Cart review failed',
            'form_validation' => $success ? 'Checkout form validated' : 'Checkout form validation failed',
            'shipping_calculated' => $success ? 'Shipping costs calculated' : 'Shipping calculation failed',
            'billing_processed' => $success ? 'Billing information processed' : 'Billing processing failed',
            'order_created' => $success ? 'Order successfully created' : 'Order creation failed',
            'payment_initiated' => $success ? 'Payment process initiated' : 'Payment initiation failed',
            'payment_completed' => $success ? 'Payment completed successfully' : 'Payment failed',
            'confirmation_sent' => $success ? 'Order confirmation sent' : 'Failed to send confirmation',
            'completed' => $success ? 'Checkout process completed' : 'Checkout process failed',
        ];

        $description = $stepDescriptions[$step] ?? "Checkout {$step}";
        if ($orderId) {
            $description .= " (Order ID: {$orderId})";
        }

        // Add validation error details to description
        if (!empty($validationErrors)) {
            $errorFields = array_keys($validationErrors);
            $description .= " - Validation errors: " . implode(', ', $errorFields);
        }

        // Calculate risk score based on step and success
        $riskScore = $this->calculateCheckoutRiskScore($step, $success, $checkoutData, $validationErrors);

        return $this->createActivityLog([
            'user_id' => $user?->id,
            'user_email' => $user?->email ?? $checkoutData['email'] ?? null,
            'user_name' => $user ? "{$user->first_name} {$user->last_name}" :
                          ($checkoutData['first_name'] ?? null && $checkoutData['last_name'] ?? null ?
                           "{$checkoutData['first_name']} {$checkoutData['last_name']}" : null),
            'activity_type' => 'checkout_activity',
            'activity_description' => $description,
            'status' => $success ? 'success' : 'failed',
            'failure_reason' => $failureReason,
            'risk_score' => $riskScore,
            'is_suspicious' => $riskScore >= 60,
            'request_data' => array_merge([
                'checkout_step' => $step,
                'order_id' => $orderId,
                'validation_errors' => $validationErrors,
                'checkout_data' => $this->sanitizeCheckoutData($checkoutData),
                'form_data' => $this->sanitizeRequestData($this->request->all()),
                'route_name' => $this->request->route()?->getName(),
                'route_parameters' => $this->request->route()?->parameters() ?? [],
                'cart_summary' => $this->getCartSummaryForLogging(),
                'user_type' => $user ? 'authenticated' : 'guest',
                'cart_type' => $user ? 'user' : 'session',
                'customer_type' => $user ? 'registered' : 'guest',
                'session_id' => $this->getSessionId(),
            ]),
            'response_data' => [
                'step_completed' => $success,
                'order_affected' => $orderId,
                'requires_audit' => in_array($step, ['payment_initiated', 'payment_completed', 'order_created']) || !$success,
                'business_impact' => $this->assessCheckoutBusinessImpact($step, $success, $checkoutData),
                'validation_summary' => !empty($validationErrors) ? [
                    'failed_fields_count' => count($validationErrors),
                    'critical_fields_failed' => $this->getCriticalFieldFailures($validationErrors),
                ] : null,
            ],
        ]);
    }

    /**
     * Log inventory changes with stock tracking.
     */
    public function logInventoryActivity(
        string $action,
        int $productId,
        array $inventoryData = [],
        bool $success = true,
        ?string $failureReason = null
    ): ActivityLog {
        $user = auth()->user();

        $descriptions = [
            'decrease' => $success ? 'Inventory decreased' : 'Failed to decrease inventory',
            'increase' => $success ? 'Inventory increased' : 'Failed to increase inventory',
            'adjust' => $success ? 'Inventory adjusted' : 'Failed to adjust inventory',
            'reserve' => $success ? 'Inventory reserved' : 'Failed to reserve inventory',
            'release' => $success ? 'Inventory reservation released' : 'Failed to release inventory',
            'low_stock_alert' => 'Low stock alert triggered',
            'out_of_stock' => 'Product out of stock',
        ];

        $description = $descriptions[$action] ?? "Inventory {$action}";
        $description .= " (Product ID: {$productId})";

        $severity = $this->getInventorySeverity($action, $inventoryData);

        return $this->createActivityLog([
            'user_id' => $user?->id,
            'user_email' => $user?->email,
            'user_name' => $user ? "{$user->first_name} {$user->last_name}" : null,
            'activity_type' => "inventory_{$action}",
            'activity_description' => $description,
            'status' => $success ? 'success' : 'failed',
            'failure_reason' => $failureReason,
            'risk_score' => $this->calculateInventoryRiskScore($action, $success, $inventoryData),
            'is_suspicious' => false, // Inventory changes are typically not suspicious
            'request_data' => array_merge([
                'product_id' => $productId,
                'action_performed' => $action,
                'user_role' => $user?->role?->name,
                'automated_action' => !$user, // True if system-generated
            ], $inventoryData),
            'response_data' => [
                'action_completed' => $success,
                'product_affected' => $productId,
                'requires_reorder' => $inventoryData['requires_reorder'] ?? false,
                'business_impact' => $this->assessInventoryBusinessImpact($action, $inventoryData),
            ],
            'severity' => $severity,
        ]);
    }

    /**
     * Log payment processing with financial compliance.
     */
    public function logPaymentActivity(
        string $action,
        ?int $paymentId = null,
        array $paymentData = [],
        bool $success = true,
        ?string $failureReason = null
    ): ActivityLog {
        $user = auth()->user();

        $descriptions = [
            'initiate' => $success ? 'Payment initiated' : 'Payment initiation failed',
            'process' => $success ? 'Payment processed' : 'Payment processing failed',
            'complete' => $success ? 'Payment completed' : 'Payment completion failed',
            'refund' => $success ? 'Payment refunded' : 'Refund failed',
            'void' => $success ? 'Payment voided' : 'Payment void failed',
            'capture' => $success ? 'Payment captured' : 'Payment capture failed',
        ];

        $description = $descriptions[$action] ?? "Payment {$action}";
        if ($paymentId) {
            $description .= " (Payment ID: {$paymentId})";
        }

        $severity = $success ? 'info' : 'warning';
        if (!$success && in_array($action, ['process', 'complete'])) {
            $severity = 'error'; // Failed payments are more serious
        }

        return $this->createActivityLog([
            'user_id' => $user?->id,
            'user_email' => $user?->email,
            'user_name' => $user ? "{$user->first_name} {$user->last_name}" : null,
            'activity_type' => "payment_{$action}",
            'activity_description' => $description,
            'status' => $success ? 'success' : 'failed',
            'failure_reason' => $failureReason,
            'risk_score' => $this->calculatePaymentRiskScore($action, $success, $paymentData),
            'is_suspicious' => $this->isPaymentSuspicious($action, $paymentData),
            'request_data' => array_merge([
                'payment_id' => $paymentId,
                'action_performed' => $action,
                'user_role' => $user?->role?->name,
                'payment_method' => $paymentData['payment_method'] ?? null,
                'gateway' => $paymentData['gateway'] ?? null,
            ], $this->sanitizePaymentData($paymentData)),
            'response_data' => [
                'action_completed' => $success,
                'payment_affected' => $paymentId,
                'requires_audit' => true, // All payment activities require audit
                'compliance_flags' => $this->getPaymentComplianceFlags($action, $paymentData),
                'business_impact' => $this->assessPaymentBusinessImpact($action, $success, $paymentData),
            ],
            'severity' => $severity,
        ]);
    }

    /**
     * Log security events with threat assessment.
     */
    public function logSecurityEvent(
        string $event,
        array $context = [],
        string $severity = 'warning'
    ): ActivityLog {
        $user = auth()->user();
        $threatLevel = $this->assessThreatLevel($event, $context);

        return $this->createActivityLog([
            'user_id' => $user?->id,
            'user_email' => $user?->email,
            'user_name' => $user ? "{$user->first_name} {$user->last_name}" : null,
            'activity_type' => "security_{$event}",
            'activity_description' => "Security event: {$event}",
            'status' => 'alert',
            'risk_score' => min($threatLevel * 10, 100),
            'is_suspicious' => $threatLevel >= 7,
            'security_notes' => $this->getSecurityEventNotes($event, $context),
            'request_data' => array_merge([
                'event_type' => $event,
                'threat_level' => $threatLevel,
                'automated_response' => $this->getAutomatedSecurityResponse($event, $threatLevel),
                'requires_investigation' => $threatLevel >= 8,
            ], $context),
            'response_data' => [
                'alert_triggered' => true,
                'escalation_required' => $threatLevel >= 9,
                'immediate_action_taken' => $this->getImmediateSecurityAction($event, $threatLevel),
            ],
            'severity' => $severity,
        ]);
    }

    /**
     * Calculate risk score for cart operations.
     */
    protected function calculateCartRiskScore(string $action, bool $success, array $cartData): int
    {
        $baseScores = [
            'add_item' => $success ? 5 : 20,
            'update_item' => $success ? 5 : 15,
            'remove_item' => $success ? 3 : 10,
            'clear' => $success ? 8 : 15,
            'checkout' => $success ? 15 : 40,
            'merge' => $success ? 10 : 25,
        ];

        $score = $baseScores[$action] ?? 10;

        // Increase risk for high-value carts
        if (isset($cartData['total_amount']) && $cartData['total_amount'] > 1000) {
            $score += 10;
        }

        // Increase risk for large quantity changes
        if (isset($cartData['quantity']) && $cartData['quantity'] > 10) {
            $score += 5;
        }

        return min($score, 100);
    }

    /**
     * Calculate risk score for inventory operations.
     */
    protected function calculateInventoryRiskScore(string $action, bool $success, array $inventoryData): int
    {
        $baseScores = [
            'decrease' => $success ? 5 : 25,
            'increase' => $success ? 5 : 20,
            'adjust' => $success ? 10 : 30,
            'reserve' => $success ? 8 : 20,
            'release' => $success ? 5 : 15,
            'low_stock_alert' => 15,
            'out_of_stock' => 25,
        ];

        $score = $baseScores[$action] ?? 10;

        // Increase risk for large quantity changes
        if (isset($inventoryData['quantity_changed']) && abs($inventoryData['quantity_changed']) > 100) {
            $score += 15;
        }

        // Increase risk if stock goes negative
        if (isset($inventoryData['new_quantity']) && $inventoryData['new_quantity'] < 0) {
            $score += 30;
        }

        return min($score, 100);
    }

    /**
     * Calculate risk score for payment operations.
     */
    protected function calculatePaymentRiskScore(string $action, bool $success, array $paymentData): int
    {
        $baseScores = [
            'initiate' => $success ? 10 : 25,
            'process' => $success ? 15 : 40,
            'complete' => $success ? 10 : 35,
            'refund' => $success ? 20 : 30,
            'void' => $success ? 15 : 25,
            'capture' => $success ? 12 : 28,
        ];

        $score = $baseScores[$action] ?? 15;

        // Increase risk for high-value transactions
        if (isset($paymentData['amount']) && $paymentData['amount'] > 5000) {
            $score += 20;
        }

        // Increase risk for failed payments
        if (!$success) {
            $score += 15;
        }

        return min($score, 100);
    }

    /**
     * Assess business impact of cart operations.
     */
    protected function assessCartBusinessImpact(string $action, bool $success, array $cartData): string
    {
        if (!$success) {
            return match($action) {
                'checkout' => 'high', // Failed checkout is high impact
                'add_item', 'update_item' => 'medium',
                default => 'low'
            };
        }

        return match($action) {
            'checkout' => 'high', // Successful checkout is high impact
            'add_item', 'update_item', 'clear' => 'medium',
            default => 'low'
        };
    }

    /**
     * Assess business impact of inventory changes.
     */
    protected function assessInventoryBusinessImpact(string $action, array $inventoryData): string
    {
        return match($action) {
            'out_of_stock' => 'high',
            'low_stock_alert' => 'medium',
            'decrease', 'reserve' => isset($inventoryData['new_quantity']) && $inventoryData['new_quantity'] < 10 ? 'medium' : 'low',
            default => 'low'
        };
    }

    /**
     * Assess business impact of payment operations.
     */
    protected function assessPaymentBusinessImpact(string $action, bool $success, array $paymentData): string
    {
        if (!$success && in_array($action, ['process', 'complete'])) {
            return 'high'; // Failed payments are high impact
        }

        return match($action) {
            'complete', 'refund' => 'high',
            'process', 'capture' => 'medium',
            default => 'low'
        };
    }

    /**
     * Get inventory event severity.
     */
    protected function getInventorySeverity(string $action, array $inventoryData): string
    {
        return match($action) {
            'out_of_stock' => 'error',
            'low_stock_alert' => 'warning',
            default => 'info'
        };
    }

    /**
     * Check if payment is suspicious.
     */
    protected function isPaymentSuspicious(string $action, array $paymentData): bool
    {
        // High-value transactions
        if (isset($paymentData['amount']) && $paymentData['amount'] > 10000) {
            return true;
        }

        // Multiple rapid transactions
        if ($action === 'process' && $this->hasRecentPaymentActivity()) {
            return true;
        }

        return false;
    }

    /**
     * Get payment compliance flags.
     */
    protected function getPaymentComplianceFlags(string $action, array $paymentData): array
    {
        $flags = [];

        if (isset($paymentData['amount']) && $paymentData['amount'] > 10000) {
            $flags[] = 'high_value_transaction';
        }

        if (in_array($action, ['refund', 'void'])) {
            $flags[] = 'reversal_transaction';
        }

        return $flags;
    }

    /**
     * Get security event notes.
     */
    protected function getSecurityEventNotes(string $event, array $context): ?string
    {
        $notes = [];

        if (isset($context['repeated_attempts']) && $context['repeated_attempts'] > 3) {
            $notes[] = "🔄 REPEATED ATTEMPTS: {$context['repeated_attempts']} attempts detected";
        }

        if (isset($context['from_tor']) && $context['from_tor']) {
            $notes[] = "🧅 TOR ACCESS: Request originated from Tor network";
        }

        if (isset($context['suspicious_user_agent']) && $context['suspicious_user_agent']) {
            $notes[] = "🤖 SUSPICIOUS UA: Automated/bot-like user agent detected";
        }

        return empty($notes) ? null : implode(' | ', $notes);
    }

    /**
     * Get automated security response.
     */
    protected function getAutomatedSecurityResponse(string $event, int $threatLevel): ?string
    {
        if ($threatLevel >= 9) {
            return 'account_suspended';
        } elseif ($threatLevel >= 7) {
            return 'rate_limited';
        } elseif ($threatLevel >= 5) {
            return 'additional_verification_required';
        }

        return null;
    }

    /**
     * Get immediate security action taken.
     */
    protected function getImmediateSecurityAction(string $event, int $threatLevel): ?string
    {
        if ($threatLevel >= 8) {
            return 'alert_sent_to_security_team';
        } elseif ($threatLevel >= 6) {
            return 'logged_for_review';
        }

        return null;
    }

    /**
     * Check for recent payment activity.
     */
    protected function hasRecentPaymentActivity(): bool
    {
        $user = auth()->user();
        if (!$user) return false;

        $recentPayments = ActivityLog::where('user_id', $user->id)
            ->where('activity_type', 'like', 'payment_%')
            ->where('created_at', '>=', now()->subMinutes(5))
            ->count();

        return $recentPayments > 3;
    }

    /**
     * Sanitize payment data for logging.
     */
    protected function sanitizePaymentData(array $data): array
    {
        $sensitive = ['card_number', 'cvv', 'account_number', 'routing_number', 'token'];

        foreach ($sensitive as $field) {
            if (isset($data[$field])) {
                $data[$field] = '***REDACTED***';
            }
        }

        return $data;
    }

    /**
     * Enhanced sanitize request data for logging.
     */
    protected function sanitizeRequestData(array $data): array
    {
        $sensitive = ['password', 'password_confirmation', 'token', '_token', 'card_number', 'credit_card', 'cvv', 'ssn'];

        foreach ($data as $key => $value) {
            // Redact sensitive fields
            if (in_array($key, $sensitive)) {
                $data[$key] = '***REDACTED***';
                continue;
            }

            // Handle File objects (from uploads or tests)
            if ($value instanceof \Illuminate\Http\UploadedFile || $value instanceof \Illuminate\Http\Testing\File) {
                $data[$key] = [
                    'file_name' => $value->getClientOriginalName(),
                    'file_size' => $value->getSize(),
                    'file_type' => $value->getMimeType(),
                ];
                continue;
            }

            // Handle arrays that might contain File objects
            if (is_array($value) && !empty($value)) {
                $data[$key] = $this->sanitizeRequestData($value);
                continue;
            }
        }

        return $data;
    }

    /**
     * Calculate risk score for checkout activities.
     */
    private function calculateCheckoutRiskScore(string $step, bool $success, array $checkoutData, array $validationErrors = []): int
    {
        $baseScore = 10; // Base risk for checkout activities

        // Increase risk for failed activities
        if (!$success) {
            $baseScore += 25;
        }

        // Higher risk for certain steps
        $stepRiskMultipliers = [
            'payment_initiated' => 3,
            'payment_completed' => 2.5,
            'order_created' => 2,
            'form_validation' => 1.5,
            'initiated' => 1,
        ];

        $multiplier = $stepRiskMultipliers[$step] ?? 1.2;
        $score = (int)($baseScore * $multiplier);

        // Add risk for validation errors
        if (!empty($validationErrors)) {
            $score += count($validationErrors) * 5;

            // Higher risk for critical field failures
            $criticalFields = ['email', 'payment_method', 'billing_address_line_1'];
            $criticalFailures = array_intersect(array_keys($validationErrors), $criticalFields);
            $score += count($criticalFailures) * 10;
        }

        // Add risk based on checkout data patterns
        if (isset($checkoutData['payment_method']) && $checkoutData['payment_method'] === 'cash_on_delivery') {
            $score += 5; // Slightly higher risk for COD
        }

        return min(100, $score);
    }

    /**
     * Sanitize checkout data for logging.
     */
    private function sanitizeCheckoutData(array $checkoutData): array
    {
        $sensitiveFields = ['password', 'password_confirmation', 'stripe_token', 'payment_token'];

        $sanitized = $checkoutData;
        foreach ($sensitiveFields as $field) {
            if (isset($sanitized[$field])) {
                $sanitized[$field] = '[REDACTED]';
            }
        }

        return $sanitized;
    }

    /**
     * Get cart summary for logging purposes.
     */
    private function getCartSummaryForLogging(): ?array
    {
        try {
            $cart = app('App\Services\CartService')->getCart();
            if (!$cart) {
                return null;
            }

            return [
                'cart_id' => $cart->id,
                'items_count' => $cart->items->count(),
                'total_amount' => $cart->total,
                'currency' => $cart->currency?->code,
                'is_guest_cart' => $cart->user_id === null,
            ];
        } catch (\Exception $e) {
            return ['error' => 'Unable to retrieve cart summary'];
        }
    }

    /**
     * Assess business impact of checkout activities.
     */
    private function assessCheckoutBusinessImpact(string $step, bool $success, array $checkoutData): string
    {
        if (!$success) {
            $impactMap = [
                'payment_completed' => 'high', // Lost revenue
                'order_created' => 'high', // Lost order
                'payment_initiated' => 'medium', // Potential lost revenue
                'form_validation' => 'low', // User experience issue
                'initiated' => 'low', // Minor funnel drop
            ];
            return $impactMap[$step] ?? 'medium';
        }

        $successImpactMap = [
            'payment_completed' => 'high', // Revenue generated
            'order_created' => 'medium', // Order in pipeline
            'payment_initiated' => 'low', // Progress made
            'completed' => 'high', // Full conversion
        ];

        return $successImpactMap[$step] ?? 'low';
    }

    /**
     * Get critical field failures from validation errors.
     */
    private function getCriticalFieldFailures(array $validationErrors): array
    {
        $criticalFields = [
            'email',
            'payment_method',
            'billing_address_line_1',
            'billing_city',
            'billing_country',
            'terms_accepted'
        ];

        return array_intersect(array_keys($validationErrors), $criticalFields);
    }
}
