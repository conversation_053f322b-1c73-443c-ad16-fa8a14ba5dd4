<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ChatSystemSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'setting_key',
        'setting_value',
        'setting_type',
        'description',
        'is_public',
        'updated_by',
    ];

    protected $casts = [
        'is_public' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $attributes = [
        'setting_type' => 'string',
        'is_public' => false,
    ];

    /**
     * Scope for public settings.
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope for private settings.
     */
    public function scopePrivate($query)
    {
        return $query->where('is_public', false);
    }

    /**
     * Scope for specific setting type.
     */
    public function scopeType($query, string $type)
    {
        return $query->where('setting_type', $type);
    }

    /**
     * Get the user who last updated this setting.
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the typed value of the setting.
     */
    public function getTypedValueAttribute()
    {
        return match($this->setting_type) {
            'boolean' => in_array(strtolower($this->setting_value), ['1', 'true', 'yes', 'on'], true),
            'integer' => (int) $this->setting_value,
            'json' => json_decode($this->setting_value, true),
            default => $this->setting_value
        };
    }

    /**
     * Set the typed value of the setting.
     */
    public function setTypedValue($value): bool
    {
        $this->setting_value = match($this->setting_type) {
            'boolean' => $value ? '1' : '0',
            'integer' => (string) $value,
            'json' => json_encode($value),
            default => (string) $value
        };

        return $this->save();
    }

    /**
     * Get setting by key.
     */
    public static function get(string $key, $default = null)
    {
        $setting = static::where('setting_key', $key)->first();
        
        if (!$setting) {
            return $default;
        }

        return $setting->typed_value;
    }

    /**
     * Set setting by key.
     */
    public static function set(string $key, $value, ?int $updatedBy = null): bool
    {
        $setting = static::where('setting_key', $key)->first();

        if ($setting) {
            $setting->updated_by = $updatedBy ?? auth()->id();
            return $setting->setTypedValue($value);
        }

        // Create new setting
        $type = match(gettype($value)) {
            'boolean' => 'boolean',
            'integer' => 'integer',
            'array' => 'json',
            default => 'string'
        };

        return static::create([
            'setting_key' => $key,
            'setting_value' => match($type) {
                'boolean' => $value ? '1' : '0',
                'integer' => (string) $value,
                'json' => json_encode($value),
                default => (string) $value
            },
            'setting_type' => $type,
            'updated_by' => $updatedBy ?? auth()->id(),
        ])->exists;
    }

    /**
     * Check if chat system is enabled.
     */
    public static function isChatEnabled(): bool
    {
        return static::get('chat_enabled', true);
    }

    /**
     * Enable chat system.
     */
    public static function enableChat(?int $updatedBy = null): bool
    {
        return static::set('chat_enabled', true, $updatedBy);
    }

    /**
     * Disable chat system.
     */
    public static function disableChat(?int $updatedBy = null): bool
    {
        return static::set('chat_enabled', false, $updatedBy);
    }

    /**
     * Check if AI is enabled.
     */
    public static function isAiEnabled(): bool
    {
        return static::get('ai_enabled', true);
    }

    /**
     * Enable AI.
     */
    public static function enableAi(?int $updatedBy = null): bool
    {
        return static::set('ai_enabled', true, $updatedBy);
    }

    /**
     * Disable AI.
     */
    public static function disableAi(?int $updatedBy = null): bool
    {
        return static::set('ai_enabled', false, $updatedBy);
    }

    /**
     * Get max concurrent users.
     */
    public static function getMaxConcurrentUsers(): int
    {
        return static::get('max_concurrent_users', 1000);
    }

    /**
     * Set max concurrent users.
     */
    public static function setMaxConcurrentUsers(int $max, ?int $updatedBy = null): bool
    {
        return static::set('max_concurrent_users', $max, $updatedBy);
    }

    /**
     * Get all public settings for frontend.
     */
    public static function getPublicSettings(): array
    {
        return static::public()
                    ->get()
                    ->pluck('typed_value', 'setting_key')
                    ->toArray();
    }

    /**
     * Get all settings as array.
     */
    public static function getAllSettings(): array
    {
        return static::all()
                    ->pluck('typed_value', 'setting_key')
                    ->toArray();
    }

    /**
     * Bulk update settings.
     */
    public static function bulkUpdate(array $settings, ?int $updatedBy = null): array
    {
        $results = [];

        foreach ($settings as $key => $value) {
            $results[$key] = static::set($key, $value, $updatedBy);
        }

        return $results;
    }

    /**
     * Reset setting to default.
     */
    public static function resetToDefault(string $key): bool
    {
        $defaults = [
            'chat_enabled' => true,
            'ai_enabled' => true,
            'max_concurrent_users' => 1000,
        ];

        if (isset($defaults[$key])) {
            return static::set($key, $defaults[$key]);
        }

        return false;
    }

    /**
     * Get setting history (if audit logging is enabled).
     */
    public function getHistory(): array
    {
        // This would integrate with the ActivityLog model
        return ActivityLog::where('subject_type', static::class)
                         ->where('subject_id', $this->id)
                         ->orderByDesc('created_at')
                         ->get()
                         ->map(function ($log) {
                             return [
                                 'action' => $log->description,
                                 'old_value' => $log->properties['old'] ?? null,
                                 'new_value' => $log->properties['attributes'] ?? null,
                                 'user' => $log->causer?->name,
                                 'timestamp' => $log->created_at,
                             ];
                         })
                         ->toArray();
    }

    /**
     * Get settings statistics.
     */
    public static function getStatistics(): array
    {
        $total = static::count();
        $public = static::public()->count();
        $byType = static::selectRaw('setting_type, COUNT(*) as count')
                       ->groupBy('setting_type')
                       ->pluck('count', 'setting_type')
                       ->toArray();

        return [
            'total_settings' => $total,
            'public_settings' => $public,
            'private_settings' => $total - $public,
            'by_type' => $byType,
        ];
    }

    /**
     * Export settings to array.
     */
    public static function exportSettings(bool $publicOnly = false): array
    {
        $query = static::query();

        if ($publicOnly) {
            $query->public();
        }

        return $query->get()->map(function ($setting) {
            return [
                'key' => $setting->setting_key,
                'value' => $setting->typed_value,
                'type' => $setting->setting_type,
                'description' => $setting->description,
                'is_public' => $setting->is_public,
            ];
        })->toArray();
    }

    /**
     * Import settings from array.
     */
    public static function importSettings(array $settings, ?int $updatedBy = null): int
    {
        $imported = 0;

        foreach ($settings as $setting) {
            if (isset($setting['key'], $setting['value'])) {
                static::set($setting['key'], $setting['value'], $updatedBy);
                $imported++;
            }
        }

        return $imported;
    }
}
