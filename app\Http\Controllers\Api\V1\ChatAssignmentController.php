<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Services\ChatAssignmentService;
use App\Models\ChatRoom;
use App\Models\ChatAssignment;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class ChatAssignmentController extends Controller
{
    protected ChatAssignmentService $assignmentService;

    public function __construct(ChatAssignmentService $assignmentService)
    {
        $this->assignmentService = $assignmentService;
        
        // Ensure only staff/admin can access these endpoints
        $this->middleware('role:staff|admin');

        // Check if user is active
        $this->middleware(function ($request, $next) {
            if (auth()->check() && !auth()->user()->is_active) {
                return response()->json([
                    'success' => false,
                    'message' => 'Account is not active',
                ], 403);
            }
            return $next($request);
        });
    }

    /**
     * Get all assignments with filtering.
     */
    public function index(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'status' => 'sometimes|in:active,transferred,completed,cancelled',
            'assignment_type' => 'sometimes|in:automatic,manual,transfer,escalation',
            'assigned_to' => 'sometimes|integer|exists:users,id',
            'date_from' => 'sometimes|date',
            'date_to' => 'sometimes|date|after_or_equal:date_from',
            'per_page' => 'sometimes|integer|min:1|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $filters = $request->only(['status', 'assignment_type', 'assigned_to', 'date_from', 'date_to']);
            $perPage = $request->get('per_page', 20);

            $query = ChatAssignment::with(['chatRoom', 'assignedStaff', 'assignedBy']);

            // Apply filters
            foreach ($filters as $key => $value) {
                if ($value !== null) {
                    if (in_array($key, ['date_from', 'date_to'])) {
                        $operator = $key === 'date_from' ? '>=' : '<=';
                        $query->where('created_at', $operator, $value);
                    } else {
                        $query->where($key, $value);
                    }
                }
            }

            $assignments = $query->orderBy('created_at', 'desc')
                                ->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $assignments,
                'meta' => [
                    'filters_applied' => $filters,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve assignments',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Assign chat room to staff member.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'room_uuid' => 'required|string|exists:chat_rooms,uuid',
            'assigned_to' => 'required|integer|exists:users,id',
            'notes' => 'sometimes|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $room = ChatRoom::where('uuid', $request->room_uuid)->firstOrFail();
            $staff = User::findOrFail($request->assigned_to);

            // Check if staff has appropriate role
            if (!$staff->hasRole(['staff', 'admin'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not a staff member',
                ], 400);
            }

            // Check if room already has active assignment
            if ($room->currentAssignment) {
                return response()->json([
                    'success' => false,
                    'message' => 'Chat room already has an active assignment',
                ], 400);
            }

            $assignment = $this->assignmentService->manualAssign($room, $staff, auth()->user());

            if ($request->has('notes')) {
                $assignment->update(['notes' => $request->notes]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Chat room assigned successfully',
                'data' => [
                    'assignment' => $assignment->load(['chatRoom', 'assignedStaff', 'assignedBy']),
                ],
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create assignment',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Auto-assign chat room to best available staff.
     */
    public function autoAssign(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'room_uuid' => 'required|string|exists:chat_rooms,uuid',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $room = ChatRoom::where('uuid', $request->room_uuid)->firstOrFail();

            // Check if room already has active assignment
            if ($room->currentAssignment) {
                return response()->json([
                    'success' => false,
                    'message' => 'Chat room already has an active assignment',
                ], 400);
            }

            $assignment = $this->assignmentService->autoAssign($room);

            if (!$assignment) {
                return response()->json([
                    'success' => false,
                    'message' => 'No available staff members for assignment',
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => 'Chat room auto-assigned successfully',
                'data' => [
                    'assignment' => $assignment->load(['chatRoom', 'assignedStaff']),
                ],
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to auto-assign chat room',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Transfer assignment to another staff member.
     */
    public function transfer(Request $request, int $assignmentId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'new_staff_id' => 'required|integer|exists:users,id',
            'reason' => 'sometimes|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $assignment = ChatAssignment::findOrFail($assignmentId);
            $newStaff = User::findOrFail($request->new_staff_id);

            // Check if assignment is active
            if (!$assignment->isActive()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Assignment is not active',
                ], 400);
            }

            // Check if new staff has appropriate role
            if (!$newStaff->hasRole(['staff', 'admin'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Target user is not a staff member',
                ], 400);
            }

            // Check permissions (only assigned staff, admin, or supervisor can transfer)
            if ($assignment->assigned_to !== auth()->id() && !auth()->user()->hasRole('admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized to transfer this assignment',
                ], 403);
            }

            $newAssignment = $this->assignmentService->transferAssignment(
                $assignment,
                $newStaff,
                $request->get('reason'),
                auth()->user()
            );

            return response()->json([
                'success' => true,
                'message' => 'Assignment transferred successfully',
                'data' => [
                    'old_assignment' => $assignment->fresh(),
                    'new_assignment' => $newAssignment->load(['chatRoom', 'assignedStaff', 'assignedBy']),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to transfer assignment',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Complete assignment.
     */
    public function complete(int $assignmentId): JsonResponse
    {
        try {
            $assignment = ChatAssignment::findOrFail($assignmentId);

            // Check if assignment is active
            if (!$assignment->isActive()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Assignment is not active',
                ], 400);
            }

            // Check permissions (only assigned staff or admin can complete)
            if ($assignment->assigned_to !== auth()->id() && !auth()->user()->hasRole('admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized to complete this assignment',
                ], 403);
            }

            $success = $this->assignmentService->completeAssignment($assignment);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Assignment completed successfully',
                    'data' => [
                        'assignment' => $assignment->fresh(),
                    ],
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to complete assignment',
            ], 500);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to complete assignment',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get assignment statistics.
     */
    public function getStatistics(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'date_from' => 'sometimes|date',
            'date_to' => 'sometimes|date|after_or_equal:date_from',
            'staff_id' => 'sometimes|integer|exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $filters = $request->only(['date_from', 'date_to', 'staff_id']);
            $statistics = $this->assignmentService->getAssignmentStatistics($filters);

            return response()->json([
                'success' => true,
                'data' => $statistics,
                'meta' => [
                    'filters' => $filters,
                    'generated_at' => now(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get available staff for assignment.
     */
    public function getAvailableStaff(): JsonResponse
    {
        try {
            $availableStaff = User::whereHas('role', function($query) {
                                     $query->whereIn('name', ['staff', 'admin']);
                                 })
                                 ->where('is_active', true)
                                 ->with(['role'])
                                 ->get()
                                 ->map(function ($staff) {
                                     $currentAssignments = ChatAssignment::where('assigned_to', $staff->id)
                                                                        ->active()
                                                                        ->count();
                                     
                                     return [
                                         'id' => $staff->id,
                                         'name' => $staff->name,
                                         'email' => $staff->email,
                                         'role' => $staff->role ? $staff->role->name : null,
                                         'current_assignments' => $currentAssignments,
                                         'workload_score' => ChatAssignment::calculateWorkloadScore($staff),
                                         'is_online' => $staff->last_seen_at && $staff->last_seen_at >= now()->subMinutes(5),
                                     ];
                                 });

            return response()->json([
                'success' => true,
                'data' => [
                    'staff' => $availableStaff,
                ],
                'meta' => [
                    'count' => $availableStaff->count(),
                    'generated_at' => now(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve available staff',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
