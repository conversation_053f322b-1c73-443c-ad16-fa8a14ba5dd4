/**
 * Cart State Manager
 * Handles cart state with local storage caching and smart server synchronization
 */

class CartStateManager {
    constructor() {
        this.STORAGE_KEY = 'cart_state';
        this.SYNC_KEY = 'cart_last_sync';
        this.SYNC_INTERVAL = 5 * 60 * 1000; // 5 minutes
        this.MAX_RETRY_ATTEMPTS = 3;
        this.RETRY_DELAY = 1000; // 1 second
        
        this.state = {
            items: [],
            count: 0,
            subtotal: 0,
            total: 0,
            tax_amount: 0,
            shipping_amount: 0,
            discount_amount: 0,
            last_updated: null,
            needs_sync: false
        };
        
        this.syncQueue = [];
        this.isSyncing = false;
        this.retryCount = 0;
        
        this.init();
    }

    /**
     * Initialize the cart state manager
     */
    init() {
        this.loadFromStorage();
        this.setupEventListeners();
        this.scheduleSync();
        
        // Initial sync if needed
        if (this.shouldSync()) {
            this.syncWithServer();
        }
    }

    /**
     * Load cart state from localStorage
     */
    loadFromStorage() {
        try {
            const stored = localStorage.getItem(this.STORAGE_KEY);
            if (stored) {
                const parsedState = JSON.parse(stored);
                this.state = { ...this.state, ...parsedState };

            }
        } catch (error) {
            console.error('❌ Error loading cart state from storage:', error);
            this.clearStorage();
        }
    }

    /**
     * Save cart state to localStorage
     */
    saveToStorage() {
        try {
            localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.state));
            localStorage.setItem(this.SYNC_KEY, Date.now().toString());

        } catch (error) {
            console.error('❌ Error saving cart state to storage:', error);
        }
    }

    /**
     * Clear storage
     */
    clearStorage() {
        localStorage.removeItem(this.STORAGE_KEY);
        localStorage.removeItem(this.SYNC_KEY);
    }

    /**
     * Check if sync is needed
     */
    shouldSync() {
        const lastSync = localStorage.getItem(this.SYNC_KEY);
        if (!lastSync) return true;
        
        const timeSinceSync = Date.now() - parseInt(lastSync);
        return timeSinceSync > this.SYNC_INTERVAL || this.state.needs_sync;
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Sync when page becomes visible
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.shouldSync()) {
                this.syncWithServer();
            }
        });

        // Sync before page unload if needed
        window.addEventListener('beforeunload', () => {
            if (this.state.needs_sync) {
                // Use sendBeacon for reliable sync on page unload
                this.sendBeaconSync();
            }
        });

        // Listen for storage changes from other tabs
        window.addEventListener('storage', (e) => {
            if (e.key === this.STORAGE_KEY) {
                this.loadFromStorage();
                this.notifyStateChange();
            }
        });
    }

    /**
     * Schedule periodic sync
     */
    scheduleSync() {
        setInterval(() => {
            if (this.shouldSync()) {
                this.syncWithServer();
            }
        }, this.SYNC_INTERVAL);
    }

    /**
     * Get current cart state
     */
    getState() {
        return { ...this.state };
    }

    /**
     * Get cart count
     */
    getCount() {
        return this.state.count;
    }

    /**
     * Update cart state locally
     */
    updateState(newState) {
        this.state = { 
            ...this.state, 
            ...newState, 
            last_updated: Date.now(),
            needs_sync: true 
        };
        this.saveToStorage();
        this.notifyStateChange();
        
        // Queue sync for later
        this.queueSync();
    }

    /**
     * Add item to cart locally
     */
    addItem(product, quantity = 1, variant = null) {
        const existingItemIndex = this.state.items.findIndex(item => 
            item.product_id === product.id && 
            item.variant_id === (variant ? variant.id : null)
        );

        if (existingItemIndex >= 0) {
            // Update existing item
            this.state.items[existingItemIndex].quantity += quantity;
            this.state.items[existingItemIndex].total_price = 
                this.state.items[existingItemIndex].quantity * this.state.items[existingItemIndex].unit_price;
        } else {
            // Add new item
            const unitPrice = variant ? (variant.price || product.price) : product.price;
            this.state.items.push({
                id: Date.now(), // Temporary ID
                product_id: product.id,
                variant_id: variant ? variant.id : null,
                quantity: quantity,
                unit_price: unitPrice,
                total_price: quantity * unitPrice,
                product: product,
                variant: variant
            });
        }

        this.recalculateState();
        this.updateState({});
    }

    /**
     * Update item quantity locally
     */
    updateItemQuantity(itemId, quantity) {
        const itemIndex = this.state.items.findIndex(item => item.id === itemId);
        
        if (itemIndex >= 0) {
            if (quantity <= 0) {
                this.state.items.splice(itemIndex, 1);
            } else {
                this.state.items[itemIndex].quantity = quantity;
                this.state.items[itemIndex].total_price = 
                    this.state.items[itemIndex].quantity * this.state.items[itemIndex].unit_price;
            }
            
            this.recalculateState();
            this.updateState({});
        }
    }

    /**
     * Remove item from cart locally
     */
    removeItem(itemId) {
        this.state.items = this.state.items.filter(item => item.id !== itemId);
        this.recalculateState();
        this.updateState({});
    }

    /**
     * Clear cart locally
     */
    clearCart() {
        this.state.items = [];
        this.recalculateState();
        this.updateState({});
    }

    /**
     * Recalculate cart totals
     */
    recalculateState() {
        this.state.count = this.state.items.reduce((sum, item) => sum + item.quantity, 0);
        this.state.subtotal = this.state.items.reduce((sum, item) => sum + item.total_price, 0);
        
        // Simple tax calculation (15%)
        this.state.tax_amount = this.state.subtotal * 0.15;
        
        // Simple shipping calculation
        this.state.shipping_amount = this.state.subtotal > 100 ? 0 : 10;
        
        this.state.total = this.state.subtotal + this.state.tax_amount + 
                          this.state.shipping_amount - this.state.discount_amount;
    }

    /**
     * Queue sync operation
     */
    queueSync() {
        if (!this.isSyncing) {
            setTimeout(() => this.syncWithServer(), 1000); // Debounce sync
        }
    }

    /**
     * Sync with server
     */
    async syncWithServer() {
        if (this.isSyncing) return;
        
        this.isSyncing = true;
        
        try {
            const response = await fetch('/api/cart/sync', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
                },
                body: JSON.stringify({
                    local_state: this.state,
                    last_sync: localStorage.getItem(this.SYNC_KEY)
                })
            });

            if (response.ok) {
                const serverState = await response.json();
                this.mergeServerState(serverState);
                this.retryCount = 0;

            } else {
                throw new Error(`Sync failed with status: ${response.status}`);
            }
        } catch (error) {
            console.error('❌ Cart sync failed:', error);
            this.handleSyncError();
        } finally {
            this.isSyncing = false;
        }
    }

    /**
     * Merge server state with local state
     */
    mergeServerState(serverState) {
        // Server state takes precedence for authoritative data
        this.state = {
            ...this.state,
            ...serverState,
            needs_sync: false,
            last_updated: Date.now()
        };
        
        this.saveToStorage();
        this.notifyStateChange();
    }

    /**
     * Handle sync errors with retry logic
     */
    handleSyncError() {
        this.retryCount++;
        
        if (this.retryCount < this.MAX_RETRY_ATTEMPTS) {
            const delay = this.RETRY_DELAY * Math.pow(2, this.retryCount - 1); // Exponential backoff
            setTimeout(() => this.syncWithServer(), delay);
        } else {
            console.error('❌ Max retry attempts reached. Cart will sync on next page load.');
            this.retryCount = 0;
        }
    }

    /**
     * Send beacon sync for page unload
     */
    sendBeaconSync() {
        if (navigator.sendBeacon) {
            const data = JSON.stringify({
                local_state: this.state,
                last_sync: localStorage.getItem(this.SYNC_KEY)
            });
            
            navigator.sendBeacon('/api/cart/sync', data);
        }
    }

    /**
     * Notify state change to listeners
     */
    notifyStateChange() {
        // Update global cart count
        if (window.updateCartCount) {
            window.updateCartCount(this.state.count);
        }

        // Dispatch custom event
        window.dispatchEvent(new CustomEvent('cartStateChanged', {
            detail: this.getState()
        }));
    }

    /**
     * Force sync with server
     */
    forceSync() {
        this.state.needs_sync = true;
        return this.syncWithServer();
    }
}

// Create global instance
window.cartStateManager = new CartStateManager();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CartStateManager;
}
