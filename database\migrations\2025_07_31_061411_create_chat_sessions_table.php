<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_sessions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('chat_room_id')->constrained('chat_rooms')->onDelete('cascade');
            $table->timestamp('session_start')->useCurrent();
            $table->timestamp('session_end')->nullable();
            $table->integer('duration_seconds')->unsigned()->nullable();
            $table->integer('message_count')->unsigned()->default(0);
            $table->tinyInteger('participant_count')->unsigned()->default(0);
            $table->integer('ai_message_count')->unsigned()->default(0);
            $table->integer('staff_response_time_avg')->unsigned()->nullable()
                  ->comment('Average response time in seconds');
            $table->tinyInteger('customer_satisfaction')->unsigned()->nullable()
                  ->comment('Rating 1-5');
            $table->enum('resolution_status', ['resolved', 'unresolved', 'escalated', 'abandoned'])->nullable();
            $table->json('tags')->nullable()->comment('Session tags for categorization');
            $table->timestamps();
            
            // Performance indexes
            $table->index('session_start', 'idx_session_start');
            $table->index('duration_seconds', 'idx_duration');
            $table->index('resolution_status', 'idx_resolution_status');
            $table->index('customer_satisfaction', 'idx_satisfaction');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_sessions');
    }
};
