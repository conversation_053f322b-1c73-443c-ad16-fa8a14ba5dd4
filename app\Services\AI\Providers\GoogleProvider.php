<?php

namespace App\Services\AI\Providers;

use Exception;
use Illuminate\Support\Facades\Http;

class GoogleProvider extends BaseAIProvider
{
    /**
     * Validate the provider configuration.
     */
    public function validateConfiguration(): bool
    {
        if (empty($this->config['api_key'])) {
            throw new \Exception('Google AI API key is not configured. Please set GOOGLE_AI_API_KEY in your environment variables.');
        }

        if (empty($this->config['base_url'])) {
            throw new \Exception('Google AI base URL is not configured. Please set GOOGLE_AI_BASE_URL in your environment variables.');
        }

        if (!filter_var($this->config['base_url'], FILTER_VALIDATE_URL)) {
            throw new \Exception('Google AI base URL is not a valid URL.');
        }

        return true;
    }

    /**
     * Generate a response from Google Gemini.
     */
    public function generateResponse(string $message, array $context = [], string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        $this->validateModel($model);

        $contents = $this->buildGeminiContents($message, $context);
        
        $data = [
            'contents' => $contents,
            'generationConfig' => [
                'temperature' => $context['temperature'] ?? 0.7,
                'topP' => $context['top_p'] ?? 1.0,
                'topK' => $context['top_k'] ?? 40,
                'maxOutputTokens' => $context['max_tokens'] ?? 1000,
            ],
        ];

        // Add system instruction if provided
        if (isset($context['system_message'])) {
            $data['systemInstruction'] = [
                'parts' => [
                    ['text' => $context['system_message']]
                ]
            ];
        }

        // Add tools if supported
        if (isset($context['tools']) && $this->supportsFeature('function_calling', $model)) {
            $data['tools'] = $this->formatGeminiTools($context['tools']);
        }

        $endpoint = "models/{$model}:generateContent";
        $response = $this->makeRequest($endpoint, $data);

        return [
            'content' => $this->extractContent($response),
            'model' => $model,
            'provider' => $this->name,
            'usage' => $this->extractUsage($response),
            'finish_reason' => $response['candidates'][0]['finishReason'] ?? null,
            'function_calls' => $this->extractFunctionCalls($response),
            'raw_response' => $response,
        ];
    }

    /**
     * Analyze sentiment using Gemini.
     */
    public function analyzeSentiment(string $text, string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        
        $prompt = "Analyze the sentiment of the following text. Provide a sentiment score from -1 (very negative) to 1 (very positive), the dominant emotion, and your confidence level. Respond in JSON format with keys: score, emotion, confidence, explanation.\n\nText: {$text}";

        $response = $this->generateResponse($prompt, [
            'temperature' => 0.3,
            'max_tokens' => 200,
        ], $model);

        try {
            $analysis = json_decode($response['content'], true);
            return [
                'sentiment_score' => $analysis['score'] ?? 0,
                'emotion' => $analysis['emotion'] ?? 'neutral',
                'confidence' => $analysis['confidence'] ?? 0.5,
                'explanation' => $analysis['explanation'] ?? '',
                'provider' => $this->name,
                'model' => $model,
            ];
        } catch (Exception $e) {
            return [
                'sentiment_score' => 0,
                'emotion' => 'neutral',
                'confidence' => 0.5,
                'explanation' => 'Unable to parse sentiment analysis',
                'provider' => $this->name,
                'model' => $model,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Translate text using Gemini.
     */
    public function translateText(string $text, string $targetLanguage, string $sourceLanguage = null, string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        
        $sourceText = $sourceLanguage ? "from {$sourceLanguage} " : '';
        $prompt = "Translate the following text {$sourceText}to {$targetLanguage}. Provide only the translation:\n\n{$text}";

        $response = $this->generateResponse($prompt, [
            'temperature' => 0.3,
            'max_tokens' => strlen($text) * 2,
        ], $model);

        return [
            'translated_text' => trim($response['content']),
            'source_language' => $sourceLanguage,
            'target_language' => $targetLanguage,
            'provider' => $this->name,
            'model' => $model,
            'confidence' => 0.9,
        ];
    }

    /**
     * Summarize text using Gemini.
     */
    public function summarizeText(string $text, int $maxLength = 150, string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        
        $prompt = "Summarize the following text in approximately {$maxLength} words. Focus on the key points:\n\n{$text}";

        $response = $this->generateResponse($prompt, [
            'temperature' => 0.5,
            'max_tokens' => $maxLength * 2,
        ], $model);

        return [
            'summary' => trim($response['content']),
            'original_length' => strlen($text),
            'summary_length' => strlen($response['content']),
            'compression_ratio' => strlen($response['content']) / strlen($text),
            'provider' => $this->name,
            'model' => $model,
        ];
    }

    /**
     * Extract topics from text using Gemini.
     */
    public function extractTopics(string $text, int $maxTopics = 5, string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        
        $prompt = "Extract the top {$maxTopics} topics from the following text. For each topic, provide a title and description. Format as JSON array with objects containing 'title' and 'description':\n\n{$text}";

        $response = $this->generateResponse($prompt, [
            'temperature' => 0.4,
            'max_tokens' => 500,
        ], $model);

        try {
            $topics = json_decode($response['content'], true);
            return [
                'topics' => $topics ?: [],
                'provider' => $this->name,
                'model' => $model,
            ];
        } catch (Exception $e) {
            return [
                'topics' => [],
                'provider' => $this->name,
                'model' => $model,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Generate function calls using Gemini.
     */
    public function generateFunctionCalls(string $message, array $availableFunctions = [], array $context = [], string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        
        if (!$this->supportsFeature('function_calling', $model)) {
            throw new Exception("Function calling not supported by model {$model}");
        }

        $contents = $this->buildGeminiContents($message, $context);
        
        $data = [
            'contents' => $contents,
            'tools' => $this->formatGeminiTools($availableFunctions),
            'generationConfig' => [
                'maxOutputTokens' => 1000,
            ],
        ];

        $endpoint = "models/{$model}:generateContent";
        $response = $this->makeRequest($endpoint, $data);

        return [
            'content' => $this->extractContent($response),
            'function_calls' => $this->extractFunctionCalls($response),
            'model' => $model,
            'provider' => $this->name,
            'usage' => $this->extractUsage($response),
            'raw_response' => $response,
        ];
    }

    /**
     * Analyze image using Gemini Vision.
     */
    public function analyzeImage(string $imageUrl, string $prompt = null, string $model = null): array
    {
        $model = $model ?: 'gemini-2.5-pro';
        
        if (!$this->supportsFeature('vision', $model)) {
            throw new Exception("Vision not supported by model {$model}");
        }

        $prompt = $prompt ?: "Describe what you see in this image in detail.";

        $imageData = $this->processImageForGemini($imageUrl);

        $contents = [
            [
                'parts' => [
                    ['text' => $prompt],
                    $imageData,
                ],
            ],
        ];

        $data = [
            'contents' => $contents,
            'generationConfig' => [
                'maxOutputTokens' => 1000,
            ],
        ];

        $endpoint = "models/{$model}:generateContent";
        $response = $this->makeRequest($endpoint, $data);

        return [
            'description' => $this->extractContent($response),
            'model' => $model,
            'provider' => $this->name,
            'usage' => $this->extractUsage($response),
            'raw_response' => $response,
        ];
    }

    /**
     * Build contents in Gemini format.
     */
    protected function buildGeminiContents(string $message, array $context = []): array
    {
        $contents = [];

        // Add conversation history
        if (isset($context['history']) && is_array($context['history'])) {
            foreach ($context['history'] as $historyMessage) {
                $role = $historyMessage['role'] ?? 'user';
                if ($role === 'system') continue; // System messages handled separately
                
                $geminiRole = $role === 'assistant' ? 'model' : 'user';
                $contents[] = [
                    'role' => $geminiRole,
                    'parts' => [
                        ['text' => $historyMessage['content'] ?? '']
                    ],
                ];
            }
        }

        // Add current message
        $contents[] = [
            'role' => 'user',
            'parts' => [
                ['text' => $message]
            ],
        ];

        return $contents;
    }

    /**
     * Format tools for Gemini API.
     */
    protected function formatGeminiTools(array $tools): array
    {
        $geminiTools = [];
        
        foreach ($tools as $tool) {
            if (isset($tool['function'])) {
                $geminiTools[] = [
                    'functionDeclarations' => [
                        [
                            'name' => $tool['function']['name'],
                            'description' => $tool['function']['description'] ?? '',
                            'parameters' => $tool['function']['parameters'] ?? [],
                        ]
                    ]
                ];
            }
        }

        return $geminiTools;
    }

    /**
     * Extract function calls from Gemini response.
     */
    protected function extractFunctionCalls(array $response): array
    {
        $functionCalls = [];
        
        if (isset($response['candidates'][0]['content']['parts'])) {
            foreach ($response['candidates'][0]['content']['parts'] as $part) {
                if (isset($part['functionCall'])) {
                    $functionCalls[] = [
                        'id' => uniqid(),
                        'type' => 'function',
                        'function' => [
                            'name' => $part['functionCall']['name'],
                            'arguments' => json_encode($part['functionCall']['args'] ?? []),
                        ],
                    ];
                }
            }
        }

        return $functionCalls;
    }

    /**
     * Extract usage information from Gemini response.
     */
    protected function extractUsage(array $response): array
    {
        $usage = $response['usageMetadata'] ?? [];
        
        return [
            'prompt_tokens' => $usage['promptTokenCount'] ?? 0,
            'completion_tokens' => $usage['candidatesTokenCount'] ?? 0,
            'total_tokens' => $usage['totalTokenCount'] ?? 0,
        ];
    }

    /**
     * Process image for Gemini API format.
     */
    protected function processImageForGemini(string $imageUrl): array
    {
        if (str_starts_with($imageUrl, 'data:')) {
            // Extract base64 data and mime type
            $parts = explode(',', $imageUrl, 2);
            $mimeType = str_replace('data:', '', explode(';', $parts[0])[0]);
            
            return [
                'inlineData' => [
                    'mimeType' => $mimeType,
                    'data' => $parts[1],
                ]
            ];
        }

        // For URL, you'd need to download and encode
        throw new Exception("URL-based images not yet implemented for Google Gemini");
    }

    /**
     * Get default headers for Google AI API.
     */
    protected function getDefaultHeaders(): array
    {
        $headers = parent::getDefaultHeaders();
        // Google AI uses API key as query parameter, not header
        return $headers;
    }

    /**
     * Make HTTP request to Google AI API.
     */
    protected function makeRequest(string $endpoint, array $data, array $headers = []): array
    {
        $baseUrl = $this->config['base_url'];
        $apiKey = $this->config['api_key'];
        $url = rtrim($baseUrl, '/') . '/' . ltrim($endpoint, '/') . '?key=' . $apiKey;

        $defaultHeaders = $this->getDefaultHeaders();
        $headers = array_merge($defaultHeaders, $headers);

        $response = Http::withHeaders($headers)
            ->timeout(120)
            ->retry(3, 1000)
            ->post($url, $data);

        if (!$response->successful()) {
            $error = "Google AI API request failed: " . $response->status() . " - " . $response->body();
            throw new Exception($error);
        }

        return $response->json();
    }

    /**
     * Extract content from Gemini response.
     */
    protected function extractContent(array $response): string
    {
        if (isset($response['candidates'][0]['content']['parts'][0]['text'])) {
            return $response['candidates'][0]['content']['parts'][0]['text'];
        }

        return '';
    }
}
