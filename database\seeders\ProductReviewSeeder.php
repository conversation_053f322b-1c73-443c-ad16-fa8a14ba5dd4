<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ProductReview;
use App\Models\Product;
use App\Models\User;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Currency;
use Illuminate\Support\Str;

class ProductReviewSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get existing data
        $products = Product::active()->get();
        $users = User::all();
        $currency = Currency::first();

        if ($products->isEmpty() || $users->isEmpty() || !$currency) {
            $this->command->warn('No products, users, or currency found. Please seed them first.');
            return;
        }

        // Create sample orders with delivered status for review eligibility
        $sampleOrders = [];

        foreach ($users->take(5) as $user) {
            // Create 1-2 delivered orders per user
            $orderCount = rand(1, 2);

            for ($i = 0; $i < $orderCount; $i++) {
                $order = Order::create([
                    'uuid' => Str::uuid(),
                    'order_number' => 'ORD-' . strtoupper(Str::random(8)),
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'phone' => $user->phone ?? '+27123456789',
                    'status' => 'delivered',
                    'payment_status' => 'paid',
                    'currency_id' => $currency->id,
                    'subtotal' => 0, // Will be calculated
                    'tax_amount' => 0,
                    'shipping_amount' => 50.00,
                    'discount_amount' => 0,
                    'total_amount' => 0, // Will be calculated
                    'billing_address' => [
                        'first_name' => $user->first_name,
                        'last_name' => $user->last_name,
                        'email' => $user->email,
                        'phone' => $user->phone ?? '+27123456789',
                        'address_line_1' => '123 Main Street',
                        'address_line_2' => 'Apt 4B',
                        'city' => 'Cape Town',
                        'state' => 'Western Cape',
                        'postal_code' => '8001',
                        'country' => 'South Africa',
                    ],
                    'shipping_address' => [
                        'first_name' => $user->first_name,
                        'last_name' => $user->last_name,
                        'email' => $user->email,
                        'phone' => $user->phone ?? '+27123456789',
                        'address_line_1' => '123 Main Street',
                        'address_line_2' => 'Apt 4B',
                        'city' => 'Cape Town',
                        'state' => 'Western Cape',
                        'postal_code' => '8001',
                        'country' => 'South Africa',
                    ],
                    'shipped_at' => now()->subDays(rand(10, 30)),
                    'delivered_at' => now()->subDays(rand(5, 15)),
                    'created_at' => now()->subDays(rand(20, 45)),
                ]);

                // Add 1-3 products to each order
                $orderProducts = $products->random(rand(1, 3));
                $subtotal = 0;

                foreach ($orderProducts as $product) {
                    $quantity = rand(1, 2);
                    $unitPrice = $product->price;
                    $totalPrice = $unitPrice * $quantity;
                    $subtotal += $totalPrice;

                    OrderItem::create([
                        'order_id' => $order->id,
                        'product_id' => $product->id,
                        'variant_id' => null,
                        'quantity' => $quantity,
                        'unit_price' => $unitPrice,
                        'total_price' => $totalPrice,
                        'product_snapshot' => [
                            'name' => $product->name,
                            'sku' => $product->sku,
                            'price' => $product->price,
                            'image' => $product->featured_image,
                        ],
                    ]);
                }

                // Update order totals
                $order->update([
                    'subtotal' => $subtotal,
                    'total_amount' => $subtotal + $order->shipping_amount,
                ]);

                $sampleOrders[] = $order;
            }
        }

        // Sample review content
        $reviewContents = [
            [
                'rating' => 5,
                'content' => 'Excellent product! Exactly what I was looking for. Fast delivery and great quality. Highly recommend this to anyone looking for a reliable solution.',
            ],
            [
                'rating' => 4,
                'content' => 'Very good product overall. Works as expected and the build quality is solid. Only minor issue was the packaging could be better, but the product itself is great.',
            ],
            [
                'rating' => 5,
                'content' => 'Outstanding quality and performance! This exceeded my expectations. The customer service was also excellent when I had questions. Will definitely buy again.',
            ],
            [
                'rating' => 3,
                'content' => 'Decent product for the price. It does what it\'s supposed to do, but nothing exceptional. Delivery was on time and packaging was adequate.',
            ],
            [
                'rating' => 4,
                'content' => 'Good value for money. The product works well and seems durable. Installation was straightforward. Would recommend for anyone on a budget.',
            ],
            [
                'rating' => 5,
                'content' => 'Perfect! This is exactly what I needed for my project. The quality is top-notch and it arrived quickly. Very satisfied with this purchase.',
            ],
            [
                'rating' => 2,
                'content' => 'Product is okay but had some issues. The functionality is there but the build quality could be better. Customer service was helpful though.',
            ],
            [
                'rating' => 4,
                'content' => 'Solid product that delivers on its promises. Good build quality and reasonable price. Shipping was fast and packaging was secure.',
            ],
            [
                'rating' => 5,
                'content' => 'Absolutely love this product! It has made my work so much easier. The quality is exceptional and it\'s very user-friendly. Highly recommended!',
            ],
            [
                'rating' => 3,
                'content' => 'Average product. It works fine but nothing special. The price is fair for what you get. Delivery was prompt and no issues with the order.',
            ],
        ];

        // Create reviews for delivered orders
        $reviewCount = 0;
        foreach ($sampleOrders as $order) {
            // Review 60% of ordered products
            $orderItems = $order->orderItems;
            $itemsToReview = $orderItems->random(max(1, (int)($orderItems->count() * 0.6)));

            foreach ($itemsToReview as $orderItem) {
                $reviewData = $reviewContents[array_rand($reviewContents)];

                ProductReview::create([
                    'product_id' => $orderItem->product_id,
                    'user_id' => $order->user_id,
                    'order_id' => $order->id,
                    'rating' => $reviewData['rating'],
                    'review_content' => $reviewData['content'],
                    'is_verified_purchase' => true,
                    'purchase_date' => $order->created_at,
                    'delivery_date' => $order->delivered_at,
                    'is_approved' => true,
                    'helpful_count' => rand(0, 8),
                    'helpful_users' => [], // Will be populated by user interactions
                    'created_at' => $order->delivered_at->addDays(rand(1, 10)),
                ]);

                $reviewCount++;
            }
        }

        $this->command->info("Created {$reviewCount} product reviews with verified purchases!");
        $this->command->info("Created " . count($sampleOrders) . " sample delivered orders.");
    }
}
