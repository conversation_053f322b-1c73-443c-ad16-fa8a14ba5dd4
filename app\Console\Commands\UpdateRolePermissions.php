<?php

namespace App\Console\Commands;

use App\Models\Role;
use Illuminate\Console\Command;

class UpdateRolePermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permissions:update-roles 
                            {--force : Force update without confirmation}
                            {--role= : Update specific role only}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update existing roles with new permission structure';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $force = $this->option('force');
        $specificRole = $this->option('role');

        if (!$force && !$this->confirm('This will update role permissions. Continue?')) {
            $this->info('Operation cancelled.');
            return 0;
        }

        $roles = $specificRole ? 
            Role::where('name', $specificRole)->get() : 
            Role::all();

        if ($roles->isEmpty()) {
            $this->error($specificRole ? "Role '{$specificRole}' not found." : 'No roles found.');
            return 1;
        }

        foreach ($roles as $role) {
            $this->updateRolePermissions($role);
        }

        $this->info('Role permissions updated successfully.');
        return 0;
    }

    /**
     * Update permissions for a specific role.
     */
    private function updateRolePermissions(Role $role): void
    {
        $this->info("Updating permissions for role: {$role->name}");

        $newPermissions = match ($role->name) {
            'admin' => [
                'users' => ['create', 'read', 'update', 'delete'],
                'products' => ['create', 'read', 'update', 'delete'],
                'orders' => ['create', 'read', 'update', 'delete'],
                'projects' => ['create', 'read', 'update', 'delete'],
                'content' => ['create', 'read', 'update', 'delete'],
                'analytics' => ['read'],
                'settings' => ['create', 'read', 'update', 'delete'],
                'team' => ['create', 'read', 'update', 'delete'],
                'newsletter' => ['create', 'read', 'update', 'delete', 'manage'],
                'contact_submissions' => ['create', 'read', 'update', 'delete', 'manage'],
                'chat' => ['participate', 'moderate', 'assign', 'view_all', 'export', 'admin'],
                'categories' => ['create', 'read', 'update', 'delete'],
                'coupons' => ['create', 'read', 'update', 'delete'],
                'jobs' => ['create', 'read', 'update', 'delete'],
                'job_applications' => ['create', 'read', 'update', 'delete'],
                'project_applications' => ['create', 'read', 'update', 'delete'],
                'visitor_analytics' => ['read', 'export'],
                'activity_logs' => ['read', 'delete'],
                'permissions' => ['read', 'manage'], // Full permission management access
                'profile' => ['read', 'update']
            ],
            'staff' => [
                'products' => ['create', 'read', 'update'],
                'orders' => ['create', 'read', 'update'],
                'projects' => ['create', 'read', 'update'],
                'content' => ['create', 'read', 'update'],
                'analytics' => ['read'],
                'chat' => ['participate', 'moderate'], // Staff can participate and moderate chat
                'categories' => ['create', 'read', 'update'],
                'newsletter' => ['read', 'manage'],
                'contact_submissions' => ['read', 'manage'],
                'profile' => ['read', 'update']
                // Note: No 'permissions' => ['manage'] - staff cannot manage permissions by default
            ],
            'client' => [
                'projects' => ['read'], // own projects only
                'orders' => ['create', 'read'], // own orders only
                'chat' => ['participate'], // Clients can participate in chat
                'profile' => ['read', 'update']
            ],
            'customer' => [
                'orders' => ['create', 'read'], // own orders only
                'profile' => ['read', 'update'],
                'products' => ['read'],
                'chat' => ['participate'] // Customers can participate in chat
            ],
            default => $role->permissions ?? []
        };

        // Merge with existing permissions to avoid losing custom permissions
        $existingPermissions = $role->permissions ?? [];
        $mergedPermissions = array_merge_recursive($existingPermissions, $newPermissions);

        // Remove duplicates from arrays
        foreach ($mergedPermissions as $resource => $actions) {
            if (is_array($actions)) {
                $mergedPermissions[$resource] = array_unique($actions);
            }
        }

        $role->update(['permissions' => $mergedPermissions]);

        $this->line("  - Updated {$role->name} role with " . count($mergedPermissions) . " permission resources");
    }
}
