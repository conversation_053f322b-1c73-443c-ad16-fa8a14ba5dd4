<tr class="hover:bg-gray-50">
    <td class="px-6 py-4 whitespace-nowrap">
        <div class="flex flex-col">
            <div class="text-sm font-medium text-gray-900">
                <a href="{{ route('admin.orders.show', $order) }}" class="hover:text-primary-600">
                    #{{ $order->order_number }}
                </a>
            </div>
            <div class="text-sm text-gray-500">
                {{ $order->items->count() }} {{ Str::plural('item', $order->items->count()) }}
            </div>
        </div>
    </td>
    
    <td class="px-6 py-4 whitespace-nowrap">
        <div class="flex flex-col">
            @if($order->user)
                <div class="text-sm font-medium text-gray-900">
                    {{ $order->user->first_name }} {{ $order->user->last_name }}
                </div>
                <div class="text-sm text-gray-500">{{ $order->user->email }}</div>
            @else
                <div class="text-sm font-medium text-gray-900">Guest Customer</div>
                <div class="text-sm text-gray-500">{{ $order->email }}</div>
            @endif
        </div>
    </td>
    
    <td class="px-6 py-4 whitespace-nowrap">
        <div class="status-container" data-order-id="{{ $order->uuid }}">
            @if($order->status === 'pending')
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    Pending
                </span>
            @elseif($order->status === 'processing')
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Processing
                </span>
            @elseif($order->status === 'shipped')
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                    Shipped
                </span>
            @elseif($order->status === 'delivered')
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Delivered
                </span>
            @elseif($order->status === 'cancelled')
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    Cancelled
                </span>
            @endif
        </div>
        
        <!-- Quick Status Update Dropdown -->
        <div class="mt-1">
            <select class="status-select text-xs border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                    data-order-id="{{ $order->uuid }}"
                    data-current-status="{{ $order->status }}">
                <option value="pending" {{ $order->status === 'pending' ? 'selected' : '' }}>Pending</option>
                <option value="processing" {{ $order->status === 'processing' ? 'selected' : '' }}>Processing</option>
                <option value="shipped" {{ $order->status === 'shipped' ? 'selected' : '' }}>Shipped</option>
                <option value="delivered" {{ $order->status === 'delivered' ? 'selected' : '' }}>Delivered</option>
                <option value="cancelled" {{ $order->status === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
            </select>
        </div>
    </td>
    
    <td class="px-6 py-4 whitespace-nowrap">
        @if($order->payment_status === 'pending')
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Pending
            </span>
        @elseif($order->payment_status === 'paid')
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Paid
            </span>
        @elseif($order->payment_status === 'failed')
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                Failed
            </span>
        @elseif($order->payment_status === 'refunded')
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                Refunded
            </span>
        @endif
    </td>
    
    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        <div class="font-medium">
            {{ $order->currency->symbol ?? '$' }}{{ number_format($order->total_amount, 2) }}
        </div>
        @if($order->coupon)
            <div class="text-xs text-green-600">
                Coupon: {{ $order->coupon_code }}
            </div>
        @endif
    </td>
    
    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        <div>{{ $order->created_at->format('M j, Y') }}</div>
        <div class="text-xs">{{ $order->created_at->format('g:i A') }}</div>
    </td>
    
    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div class="flex items-center space-x-2">
            <a href="{{ route('admin.orders.show', $order) }}" 
               class="text-primary-600 hover:text-primary-900 font-medium">
                View
            </a>
        </div>
    </td>
</tr>
