<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add AI provider settings to chat system settings
        $aiProviderSettings = [
            [
                'setting_key' => 'ai_default_provider',
                'setting_value' => 'openai',
                'setting_type' => 'string',
                'description' => 'Default AI provider for chat responses',
                'is_public' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'setting_key' => 'ai_fallback_enabled',
                'setting_value' => 'true',
                'setting_type' => 'boolean',
                'description' => 'Enable fallback to other providers when primary fails',
                'is_public' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'setting_key' => 'ai_rate_limiting_enabled',
                'setting_value' => 'true',
                'setting_type' => 'boolean',
                'description' => 'Enable rate limiting for AI API calls',
                'is_public' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'setting_key' => 'ai_caching_enabled',
                'setting_value' => 'true',
                'setting_type' => 'boolean',
                'description' => 'Enable caching for AI responses',
                'is_public' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'setting_key' => 'ai_openai_model',
                'setting_value' => 'gpt-4.1-mini',
                'setting_type' => 'string',
                'description' => 'Default OpenAI model for chat responses',
                'is_public' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'setting_key' => 'ai_anthropic_model',
                'setting_value' => 'claude-sonnet-4-20250514',
                'setting_type' => 'string',
                'description' => 'Default Anthropic model for chat responses',
                'is_public' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'setting_key' => 'ai_google_model',
                'setting_value' => 'gemini-2.5-flash',
                'setting_type' => 'string',
                'description' => 'Default Google model for chat responses',
                'is_public' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'setting_key' => 'ai_xai_model',
                'setting_value' => 'grok-3',
                'setting_type' => 'string',
                'description' => 'Default xAI model for chat responses',
                'is_public' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('chat_system_settings')->insert($aiProviderSettings);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove AI provider settings
        DB::table('chat_system_settings')
            ->whereIn('setting_key', [
                'ai_default_provider',
                'ai_fallback_enabled',
                'ai_rate_limiting_enabled',
                'ai_caching_enabled',
                'ai_openai_model',
                'ai_anthropic_model',
                'ai_google_model',
                'ai_xai_model',
            ])
            ->delete();
    }
};
