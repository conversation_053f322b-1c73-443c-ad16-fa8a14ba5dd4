<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\ChatSystemSetting;
use App\Services\ChatAIService;
use App\Services\ActivityLogger;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use OpenAI\Laravel\Facades\OpenAI;
use PHPUnit\Framework\Attributes\Test;

class ChatAIIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected User $user;
    protected ChatRoom $room;
    protected ChatAIService $aiService;

    protected function setUp(): void
    {
        parent::setUp();

        // Enable exception handling for debugging
        // $this->withoutExceptionHandling(); // Temporarily disabled to debug 500 error

        // Enable debug mode for tests
        config(['app.debug' => true]);

        // Mock ActivityLogger to avoid undefined method errors
        $mockActivityLogger = $this->createMock(ActivityLogger::class);
        $mockActivityLogger->method('log')->willReturn(new \App\Models\ActivityLog());
        $this->app->instance(ActivityLogger::class, $mockActivityLogger);

        // Create roles
        $adminRole = Role::create([
            'name' => 'admin',
            'slug' => 'admin',
            'description' => 'Administrator',
            'is_active' => true,
        ]);

        $userRole = Role::create([
            'name' => 'user',
            'slug' => 'user',
            'description' => 'Regular User',
            'is_active' => true,
        ]);

        // Create users
        $this->admin = User::factory()->create(['role_id' => $adminRole->id]);
        $this->user = User::factory()->create(['role_id' => $userRole->id]);

        // Load roles to ensure they're available in middleware
        $this->admin->load('role');
        $this->user->load('role');

        // Create chat room
        $this->room = ChatRoom::factory()->create([
            'status' => 'active',
            'language' => 'en',
        ]);

        // Enable AI
        ChatSystemSetting::updateOrCreate(
            ['setting_key' => 'ai_enabled'],
            [
                'setting_value' => 'true',
                'setting_type' => 'boolean',
                'description' => 'Enable or disable AI chatbot',
                'is_public' => true,
            ]
        );

        $this->aiService = app(ChatAIService::class);
    }

    protected function tearDown(): void
    {
        // Clear cache between tests
        Cache::flush();

        parent::tearDown();
    }

    #[Test]
    public function admin_can_access_ai_configuration_page()
    {
        $response = $this->actingAs($this->admin)
            ->get('/admin/chat/ai');



        $response->assertStatus(200);

        // Instead of assertViewIs, check for specific content that should be in the view
        $response->assertSee('AI Configuration');
        $response->assertSee('AI Settings');
        $response->assertSee('Response Templates');
        $response->assertSee('Training Data');
        $response->assertSee('Analytics');

        // Check that it's not a JSON response
        $this->assertStringContainsString('<!DOCTYPE html', $response->getContent());
    }

    #[Test]
    public function admin_can_update_ai_settings()
    {
        $settings = [
            'enabled' => true,
            'model' => 'gpt-4',
            'temperature' => 0.8,
            'max_tokens' => 200,
            'confidence_threshold' => 0.7,
            'escalation_threshold' => 0.4,
            'greeting_enabled' => true,
            'fallback_enabled' => true,
        ];

        $response = $this->actingAs($this->admin)
            ->postJson(route('admin.chat.ai.settings.update'), $settings);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify settings are cached
        $cachedSettings = Cache::get('chat_ai_runtime_settings');
        $this->assertEquals('gpt-4', $cachedSettings['model']);
        $this->assertEquals(0.8, $cachedSettings['temperature']);
    }

    #[Test]
    public function admin_can_update_response_templates()
    {
        $templates = [
            'greeting' => 'Welcome to ChiSolution! How can I help you today?',
            'fallback' => 'Let me connect you with a human agent.',
            'error' => 'I\'m experiencing technical difficulties.',
            'offline' => 'AI is currently offline.',
            'goodbye' => 'Thank you for contacting us!',
        ];

        $response = $this->actingAs($this->admin)
            ->postJson(route('admin.chat.ai.templates.update'), ['templates' => $templates]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify templates are cached
        $cachedTemplates = Cache::get('chat_ai_templates');
        $this->assertEquals($templates['greeting'], $cachedTemplates['greeting']);
    }

    #[Test]
    public function ai_service_can_check_availability()
    {
        // Test when AI is enabled
        $this->assertTrue($this->aiService->isAIAvailable());

        // Test when AI is disabled
        ChatSystemSetting::updateOrCreate(
            ['setting_key' => 'ai_enabled'],
            [
                'setting_value' => 'false',
                'setting_type' => 'boolean',
                'description' => 'Enable or disable AI chatbot',
                'is_public' => true,
            ]
        );

        // Clear any cached availability status
        Cache::flush();
        
        $this->assertFalse($this->aiService->isAIAvailable());
    }

    #[Test]
    public function ai_service_generates_greeting_when_enabled()
    {
        $greeting = $this->aiService->generateGreeting($this->user);

        $this->assertIsArray($greeting);
        $this->assertArrayHasKey('content', $greeting);
        $this->assertArrayHasKey('confidence', $greeting);
        $this->assertArrayHasKey('is_greeting', $greeting);
        $this->assertTrue($greeting['is_greeting']);
        $this->assertEquals(1.0, $greeting['confidence']);
        $this->assertStringContainsString($this->user->first_name, $greeting['content']);
    }

    #[Test]
    public function ai_service_returns_empty_greeting_when_disabled()
    {
        // Disable greetings in config
        config(['openai.chat.greeting_enabled' => false]);

        $greeting = $this->aiService->generateGreeting($this->user);

        $this->assertIsArray($greeting);
        $this->assertEmpty($greeting);
    }

    #[Test]
    public function ai_service_can_analyze_intent()
    {
        // Test support request intent
        $supportMessage = "I'm having trouble with my account login";
        $response = $this->aiService->generateResponse($supportMessage, $this->room, []);

        $this->assertIsArray($response);
        $this->assertArrayHasKey('intent', $response);
        $this->assertArrayHasKey('confidence', $response);
    }

    #[Test]
    public function ai_service_handles_api_errors_gracefully()
    {
        // Mock OpenAI failure by throwing an exception
        OpenAI::fake([
            'chat/completions' => new \Exception('OpenAI API Error')
        ]);

        $response = $this->aiService->generateResponse(
            "Hello, can you help me?",
            $this->room,
            []
        );

        $this->assertIsArray($response);
        $this->assertArrayHasKey('is_fallback', $response);
        $this->assertTrue($response['is_fallback']);
        $this->assertEquals(0.0, $response['confidence']);
    }

    #[Test]
    public function ai_service_caches_responses()
    {
        $message = "What is ChiSolution?";
        $context = [];

        // First call should generate response
        $response1 = $this->aiService->generateResponse($message, $this->room, $context);
        
        // Second call should return cached response
        $response2 = $this->aiService->generateResponse($message, $this->room, $context);

        $this->assertEquals($response1, $response2);
    }

    #[Test]
    public function admin_can_test_ai_connection()
    {
        // Mock successful OpenAI response
        OpenAI::fake([
            \OpenAI\Responses\Chat\CreateResponse::fake([
                'choices' => [
                    [
                        'message' => [
                            'content' => 'Hello! I can help you with information about ChiSolution.'
                        ]
                    ]
                ],
                'usage' => [
                    'total_tokens' => 25
                ]
            ])
        ]);

        $response = $this->actingAs($this->admin)
            ->postJson(route('admin.chat.ai.test'), [
                'test_message' => 'Hello, can you help me with information about ChiSolution?'
            ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'ai_response',
                'confidence',
                'intent',
                'response_type',
                'processing_time_ms',
                'should_escalate',
                'ai_available'
            ]
        ]);
    }

    #[Test]
    public function ai_configuration_validates_settings_input()
    {
        $invalidSettings = [
            'enabled' => 'not_boolean',
            'model' => 'invalid_model',
            'temperature' => 3.0, // Too high
            'max_tokens' => 2000, // Too high
            'confidence_threshold' => 1.5, // Too high
        ];

        $response = $this->actingAs($this->admin)
            ->postJson(route('admin.chat.ai.settings.update'), $invalidSettings);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'enabled', 'model', 'temperature', 'max_tokens', 'confidence_threshold'
        ]);
    }

    #[Test]
    public function ai_configuration_validates_templates_input()
    {
        $invalidTemplates = [
            'templates' => [
                'greeting' => str_repeat('a', 600), // Too long
                'fallback' => '', // Required
            ]
        ];

        $response = $this->actingAs($this->admin)
            ->postJson(route('admin.chat.ai.templates.update'), $invalidTemplates);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'templates.greeting', 'templates.fallback'
        ]);
    }

    #[Test]
    public function regular_user_cannot_access_ai_configuration()
    {
        // Temporarily disable exception handling to see the actual error
        $this->app['env'] = 'testing';

        // Ensure the user role is loaded
        $this->user->load('role');

        $response = $this->actingAs($this->user)
            ->get(route('admin.chat.ai.index'));

        if ($response->status() === 500) {
            dump('Response content: ' . $response->getContent());
        }

        $response->assertStatus(403);
    }

    #[Test]
    public function ai_service_respects_escalation_threshold()
    {
        // Set low escalation threshold
        Cache::put('chat_ai_runtime_settings', [
            'escalation_threshold' => 0.8
        ], 3600);

        // Mock low confidence response
        OpenAI::fake([
            \OpenAI\Responses\Chat\CreateResponse::fake([
                'choices' => [
                    [
                        'message' => [
                            'content' => 'I\'m not sure about that.'
                        ]
                    ]
                ],
                'usage' => [
                    'total_tokens' => 15
                ]
            ])
        ]);

        $response = $this->aiService->generateResponse(
            "Complex technical question",
            $this->room,
            []
        );

        $this->assertIsArray($response);
        $this->assertArrayHasKey('should_escalate', $response);
        // Should escalate due to low confidence
        $this->assertTrue($response['should_escalate']);
    }
}
