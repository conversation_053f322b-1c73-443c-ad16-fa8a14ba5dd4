<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('login_histories', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->enum('login_status', ['success', 'failed'])->index();
            $table->string('ip_address', 45)->index(); // IPv4 or IPv6
            $table->string('location', 255)->nullable();
            $table->decimal('latitude', 9, 6)->nullable();
            $table->decimal('longitude', 9, 6)->nullable();
            $table->json('device_info')->nullable(); // Browser, OS, platform, isMobile, etc.
            $table->string('device_fingerprint', 255)->nullable()->index();
            $table->text('user_agent')->nullable();
            $table->string('os_info', 255)->nullable();
            $table->string('browser_info', 255)->nullable();
            $table->enum('device_type', ['mobile', 'desktop', 'tablet'])->nullable();
            $table->enum('login_method', ['standard', 'oauth', '2fa'])->default('standard');
            $table->integer('failed_attempts')->default(0);
            $table->boolean('is_vpn')->default(false);
            $table->boolean('is_tor')->default(false);
            $table->boolean('security_alert')->default(false);
            $table->string('login_timezone', 64)->nullable();
            $table->enum('login_ip_class', ['private', 'public'])->nullable();
            $table->integer('risk_score')->default(0)->index();
            $table->boolean('is_device_known')->default(false);
            $table->boolean('is_location_known')->default(false);
            $table->boolean('is_ip_blacklisted')->default(false);
            $table->boolean('is_ip_whitelisted')->default(false);
            $table->boolean('twofa_used')->default(false);
            $table->boolean('twofa_code_verified')->default(false);
            $table->string('session_id', 255)->nullable()->index();
            $table->string('referrer', 500)->nullable();
            $table->timestamp('session_started_at')->nullable();
            $table->timestamp('session_ended_at')->nullable();
            $table->integer('session_duration')->nullable(); // in seconds
            $table->string('failure_reason', 255)->nullable();
            $table->json('security_flags')->nullable(); // Additional security metadata
            $table->json('geolocation_data')->nullable(); // Full geolocation response
            $table->string('isp', 255)->nullable();
            $table->string('organization', 255)->nullable();
            $table->boolean('is_suspicious')->default(false)->index();
            $table->text('admin_notes')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['user_id', 'login_status']);
            $table->index(['ip_address', 'created_at']);
            $table->index(['created_at', 'login_status']);
            $table->index(['risk_score', 'is_suspicious']);
            $table->index(['device_fingerprint', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('login_histories');
    }
};
