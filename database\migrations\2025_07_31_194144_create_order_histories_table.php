<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_histories', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('order_id')->constrained('orders')->onDelete('cascade');
            $table->string('event_type'); // status_change, payment_change, note_added, shipped, delivered, etc.
            $table->string('field_name')->nullable(); // status, payment_status, notes, etc.
            $table->text('old_value')->nullable();
            $table->text('new_value')->nullable();
            $table->text('description'); // Human readable description
            $table->json('metadata')->nullable(); // Additional data like IP, user agent, etc.
            $table->string('triggered_by')->default('system'); // system, admin, customer, api
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null'); // Who made the change
            $table->string('user_name')->nullable(); // Denormalized for deleted users
            $table->string('user_email')->nullable(); // Denormalized for deleted users
            $table->string('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['order_id', 'created_at']);
            $table->index(['event_type', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index('uuid');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_histories');
    }
};
