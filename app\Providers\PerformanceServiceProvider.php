<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\PerformanceOptimizer;

class PerformanceServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(PerformanceOptimizer::class, function ($app) {
            return new PerformanceOptimizer();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Set up performance monitoring
        if (!app()->runningInConsole()) {
            $this->setupPerformanceMonitoring();
        }
    }

    /**
     * Set up performance monitoring
     */
    protected function setupPerformanceMonitoring(): void
    {
        // Register shutdown function to log performance metrics
        register_shutdown_function(function () {
            if (app()->bound(PerformanceOptimizer::class)) {
                $optimizer = app(PerformanceOptimizer::class);
                $metrics = $optimizer->getMetrics();
                
                // Log performance metrics if execution time is high
                if ($metrics['execution_time'] > 10) {
                    \Log::info('High execution time detected', $metrics);
                }
                
                // Log memory usage if high
                if ($metrics['memory_usage'] > 64 * 1024 * 1024) { // 64MB
                    \Log::warning('High memory usage detected', $metrics);
                }
            }
        });

        // Set up emergency timeout handler
        if (function_exists('pcntl_signal')) {
            pcntl_signal(SIGALRM, function () {
                if (app()->bound(PerformanceOptimizer::class)) {
                    app(PerformanceOptimizer::class)->handleTimeout();
                }
            });
        }
    }
}
