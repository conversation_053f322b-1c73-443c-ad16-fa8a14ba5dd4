<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('visitor_analytics', function (Blueprint $table) {
            // Checkout Funnel Tracking
            $table->json('checkout_funnel')->nullable()->after('conversions');
            $table->json('checkout_analytics')->nullable()->after('checkout_funnel');
            $table->boolean('checkout_started')->default(false)->after('checkout_analytics');
            $table->boolean('checkout_completed')->default(false)->after('checkout_started');
            $table->boolean('checkout_abandoned')->default(false)->after('checkout_completed');
            $table->timestamp('checkout_started_at')->nullable()->after('checkout_abandoned');
            $table->timestamp('checkout_completed_at')->nullable()->after('checkout_started_at');
            $table->timestamp('checkout_abandoned_at')->nullable()->after('checkout_completed_at');
            
            // Checkout Performance Metrics
            $table->json('checkout_performance')->nullable()->after('checkout_abandoned_at');
            $table->integer('checkout_duration')->nullable()->after('checkout_performance'); // Time spent in checkout process (seconds)
            $table->string('checkout_exit_step')->nullable()->after('checkout_duration'); // Step where user exited
            
            // Add indexes for checkout analytics
            $table->index(['checkout_started', 'visited_at']);
            $table->index(['checkout_completed', 'visited_at']);
            $table->index(['checkout_abandoned', 'visited_at']);
            $table->index(['checkout_started_at']);
            $table->index(['checkout_completed_at']);
            $table->index(['checkout_abandoned_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('visitor_analytics', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['checkout_started', 'visited_at']);
            $table->dropIndex(['checkout_completed', 'visited_at']);
            $table->dropIndex(['checkout_abandoned', 'visited_at']);
            $table->dropIndex(['checkout_started_at']);
            $table->dropIndex(['checkout_completed_at']);
            $table->dropIndex(['checkout_abandoned_at']);
            
            // Drop columns
            $table->dropColumn([
                'checkout_funnel',
                'checkout_analytics',
                'checkout_started',
                'checkout_completed',
                'checkout_abandoned',
                'checkout_started_at',
                'checkout_completed_at',
                'checkout_abandoned_at',
                'checkout_performance',
                'checkout_duration',
                'checkout_exit_step',
            ]);
        });
    }
};
