<?php

namespace App\Services\AI\Providers;

use Exception;

class OpenAIProvider extends BaseAIProvider
{
    /**
     * Validate the provider configuration.
     */
    public function validateConfiguration(): bool
    {
        if (empty($this->config['api_key'])) {
            throw new \Exception('OpenAI API key is not configured. Please set OPENAI_API_KEY in your environment variables.');
        }

        if (empty($this->config['base_url'])) {
            throw new \Exception('OpenAI base URL is not configured. Please set OPENAI_BASE_URL in your environment variables.');
        }

        if (!filter_var($this->config['base_url'], FILTER_VALIDATE_URL)) {
            throw new \Exception('OpenAI base URL is not a valid URL.');
        }

        return true;
    }

    /**
     * Generate a response from OpenAI.
     */
    public function generateResponse(string $message, array $context = [], string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        $this->validateModel($model);

        $messages = $this->buildMessages($message, $context);
        
        $data = [
            'model' => $model,
            'messages' => $messages,
            'max_tokens' => $context['max_tokens'] ?? 1000,
            'temperature' => $context['temperature'] ?? 0.7,
            'top_p' => $context['top_p'] ?? 1.0,
            'frequency_penalty' => $context['frequency_penalty'] ?? 0.0,
            'presence_penalty' => $context['presence_penalty'] ?? 0.0,
        ];

        // Add function calling if supported and provided
        if (isset($context['functions']) && $this->supportsFeature('function_calling', $model)) {
            $data['functions'] = $context['functions'];
            if (isset($context['function_call'])) {
                $data['function_call'] = $context['function_call'];
            }
        }

        // Add tools if supported
        if (isset($context['tools']) && $this->supportsFeature('function_calling', $model)) {
            $data['tools'] = $context['tools'];
            if (isset($context['tool_choice'])) {
                $data['tool_choice'] = $context['tool_choice'];
            }
        }

        $response = $this->makeRequest('chat/completions', $data);

        return [
            'content' => $this->extractContent($response),
            'model' => $model,
            'provider' => $this->name,
            'usage' => $response['usage'] ?? [],
            'finish_reason' => $response['choices'][0]['finish_reason'] ?? null,
            'function_call' => $response['choices'][0]['message']['function_call'] ?? null,
            'tool_calls' => $response['choices'][0]['message']['tool_calls'] ?? null,
            'raw_response' => $response,
        ];
    }

    /**
     * Generate streaming response from OpenAI.
     */
    public function generateStreamingResponse(string $message, array $context = [], string $model = null, callable $callback = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        $this->validateModel($model);

        $messages = $this->buildMessages($message, $context);
        
        $data = [
            'model' => $model,
            'messages' => $messages,
            'max_tokens' => $context['max_tokens'] ?? 1000,
            'temperature' => $context['temperature'] ?? 0.7,
            'stream' => true,
        ];

        // For streaming, we'll need to implement Server-Sent Events handling
        // This is a simplified version - full implementation would handle SSE
        $response = $this->makeRequest('chat/completions', $data);

        return [
            'content' => $this->extractContent($response),
            'model' => $model,
            'provider' => $this->name,
            'usage' => $response['usage'] ?? [],
            'finish_reason' => $response['choices'][0]['finish_reason'] ?? null,
            'raw_response' => $response,
        ];
    }

    /**
     * Analyze sentiment using OpenAI.
     */
    public function analyzeSentiment(string $text, string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        
        $prompt = "Analyze the sentiment of the following text and provide a score from -1 (very negative) to 1 (very positive), along with the dominant emotion and confidence level. Respond in JSON format with keys: score, emotion, confidence, explanation.\n\nText: {$text}";

        $response = $this->generateResponse($prompt, [
            'temperature' => 0.3,
            'max_tokens' => 200,
        ], $model);

        try {
            $analysis = json_decode($response['content'], true);
            return [
                'sentiment_score' => $analysis['score'] ?? 0,
                'emotion' => $analysis['emotion'] ?? 'neutral',
                'confidence' => $analysis['confidence'] ?? 0.5,
                'explanation' => $analysis['explanation'] ?? '',
                'provider' => $this->name,
                'model' => $model,
            ];
        } catch (Exception $e) {
            return [
                'sentiment_score' => 0,
                'emotion' => 'neutral',
                'confidence' => 0.5,
                'explanation' => 'Unable to parse sentiment analysis',
                'provider' => $this->name,
                'model' => $model,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Translate text using OpenAI.
     */
    public function translateText(string $text, string $targetLanguage, string $sourceLanguage = null, string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        
        $sourceText = $sourceLanguage ? "from {$sourceLanguage} " : '';
        $prompt = "Translate the following text {$sourceText}to {$targetLanguage}. Provide only the translation without any additional text:\n\n{$text}";

        $response = $this->generateResponse($prompt, [
            'temperature' => 0.3,
            'max_tokens' => strlen($text) * 2, // Estimate max tokens needed
        ], $model);

        return [
            'translated_text' => trim($response['content']),
            'source_language' => $sourceLanguage,
            'target_language' => $targetLanguage,
            'provider' => $this->name,
            'model' => $model,
            'confidence' => 0.9, // OpenAI generally has high translation confidence
        ];
    }

    /**
     * Summarize text using OpenAI.
     */
    public function summarizeText(string $text, int $maxLength = 150, string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        
        $prompt = "Summarize the following text in approximately {$maxLength} words. Focus on the key points and main ideas:\n\n{$text}";

        $response = $this->generateResponse($prompt, [
            'temperature' => 0.5,
            'max_tokens' => $maxLength * 2, // Allow some buffer
        ], $model);

        return [
            'summary' => trim($response['content']),
            'original_length' => strlen($text),
            'summary_length' => strlen($response['content']),
            'compression_ratio' => strlen($response['content']) / strlen($text),
            'provider' => $this->name,
            'model' => $model,
        ];
    }

    /**
     * Extract topics from text using OpenAI.
     */
    public function extractTopics(string $text, int $maxTopics = 5, string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        
        $prompt = "Extract the top {$maxTopics} topics/themes from the following text. For each topic, provide a title and brief description. Respond in JSON format with an array of objects containing 'title' and 'description' keys:\n\n{$text}";

        $response = $this->generateResponse($prompt, [
            'temperature' => 0.4,
            'max_tokens' => 500,
        ], $model);

        try {
            $topics = json_decode($response['content'], true);
            return [
                'topics' => $topics ?: [],
                'provider' => $this->name,
                'model' => $model,
            ];
        } catch (Exception $e) {
            return [
                'topics' => [],
                'provider' => $this->name,
                'model' => $model,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Generate function calls using OpenAI.
     */
    public function generateFunctionCalls(string $message, array $availableFunctions = [], array $context = [], string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        
        if (!$this->supportsFeature('function_calling', $model)) {
            throw new Exception("Function calling not supported by model {$model}");
        }

        $messages = $this->buildMessages($message, $context);
        
        $data = [
            'model' => $model,
            'messages' => $messages,
            'tools' => $availableFunctions,
            'tool_choice' => 'auto',
        ];

        $response = $this->makeRequest('chat/completions', $data);

        return [
            'content' => $this->extractContent($response),
            'tool_calls' => $response['choices'][0]['message']['tool_calls'] ?? [],
            'model' => $model,
            'provider' => $this->name,
            'usage' => $response['usage'] ?? [],
            'raw_response' => $response,
        ];
    }

    /**
     * Analyze image using OpenAI Vision.
     */
    public function analyzeImage(string $imageUrl, string $prompt = null, string $model = null): array
    {
        $model = $model ?: 'gpt-4.1'; // Default to vision-capable model
        
        if (!$this->supportsFeature('vision', $model)) {
            throw new Exception("Vision not supported by model {$model}");
        }

        $prompt = $prompt ?: "Describe what you see in this image in detail.";

        $messages = [
            [
                'role' => 'user',
                'content' => [
                    [
                        'type' => 'text',
                        'text' => $prompt,
                    ],
                    [
                        'type' => 'image_url',
                        'image_url' => [
                            'url' => $imageUrl,
                        ],
                    ],
                ],
            ],
        ];

        $data = [
            'model' => $model,
            'messages' => $messages,
            'max_tokens' => 1000,
        ];

        $response = $this->makeRequest('chat/completions', $data);

        return [
            'description' => $this->extractContent($response),
            'model' => $model,
            'provider' => $this->name,
            'usage' => $response['usage'] ?? [],
            'raw_response' => $response,
        ];
    }

    /**
     * Get default headers for OpenAI API.
     */
    protected function getDefaultHeaders(): array
    {
        $headers = parent::getDefaultHeaders();
        $headers['Authorization'] = 'Bearer ' . $this->config['api_key'];
        
        if (isset($this->config['organization'])) {
            $headers['OpenAI-Organization'] = $this->config['organization'];
        }

        return $headers;
    }

    /**
     * Extract content from OpenAI response.
     */
    protected function extractContent(array $response): string
    {
        return $response['choices'][0]['message']['content'] ?? '';
    }
}
