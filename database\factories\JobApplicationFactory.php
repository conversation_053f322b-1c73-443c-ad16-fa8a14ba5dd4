<?php

namespace Database\Factories;

use App\Models\Job;
use App\Models\JobApplication;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\JobApplication>
 */
class JobApplicationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = JobApplication::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'uuid' => Str::uuid(),
            'job_id' => Job::factory(),
            'user_id' => null, // Default to guest application
            'reference_number' => 'JOB-' . strtoupper($this->faker->bothify('??##??##')),
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'email' => $this->faker->email(),
            'phone' => $this->faker->phoneNumber(),
            'date_of_birth' => $this->faker->optional()->dateTimeBetween('-60 years', '-18 years'),
            'nationality' => $this->faker->optional()->country(),
            'id_number' => $this->faker->optional()->numerify('##########'),
            'address' => $this->faker->optional()->address(),
            'city' => $this->faker->optional()->city(),
            'province' => $this->faker->optional()->state(),
            'postal_code' => $this->faker->optional()->postcode(),
            'country' => 'South Africa',
            'cover_letter' => $this->faker->paragraphs(3, true),
            'experience_summary' => $this->faker->paragraphs(2, true),
            'current_position' => $this->faker->optional()->jobTitle(),
            'current_company' => $this->faker->optional()->company(),
            'current_salary' => $this->faker->optional()->randomFloat(2, 20000, 100000),
            'expected_salary' => $this->faker->optional()->randomFloat(2, 30000, 150000),
            'notice_period' => $this->faker->optional()->randomElement(['Immediate', '1 week', '2 weeks', '1 month', '2 months', '3 months']),
            'available_start_date' => $this->faker->optional()->dateTimeBetween('now', '+3 months'),
            'highest_qualification' => $this->faker->randomElement(['Matric', 'Certificate', 'Diploma', 'Degree', 'Honours', 'Masters', 'PhD']),
            'institution' => $this->faker->optional()->company() . ' University',
            'field_of_study' => $this->faker->optional()->randomElement(['Computer Science', 'Engineering', 'Business', 'Marketing', 'Design']),
            'graduation_year' => $this->faker->optional()->numberBetween(1990, 2024),
            'skills' => $this->faker->optional()->randomElements(['PHP', 'Laravel', 'JavaScript', 'React', 'Vue', 'Python', 'Java'], 3),
            'languages' => $this->faker->optional()->randomElements(['English', 'Afrikaans', 'Zulu', 'Xhosa', 'French'], 2),
            'willing_to_relocate' => $this->faker->boolean(30),
            'willing_to_travel' => $this->faker->boolean(40),
            'has_drivers_license' => $this->faker->boolean(70),
            'has_own_transport' => $this->faker->boolean(60),
            'status' => $this->faker->randomElement(['pending', 'reviewing', 'shortlisted', 'interviewed', 'offered', 'hired', 'rejected']),
            'admin_notes' => $this->faker->optional()->paragraph(),
            'reviewed_by' => null,
            'reviewed_at' => null,
            'attachments' => $this->faker->optional()->randomElements(['cv.pdf', 'portfolio.pdf', 'certificates.pdf'], 2),
            'additional_notes' => $this->faker->optional()->paragraph(),
            'newsletter_signup' => $this->faker->boolean(20),
            'terms_accepted' => true,
            'ip_address' => $this->faker->optional()->ipv4(),
            'user_agent' => $this->faker->optional()->userAgent(),
            'referrer' => $this->faker->optional()->url(),
        ];
    }

    /**
     * Create an application for a specific job.
     */
    public function forJob(Job $job): static
    {
        return $this->state(fn (array $attributes) => [
            'job_id' => $job->id,
        ]);
    }

    /**
     * Create an authenticated user application.
     */
    public function authenticated(User $user = null): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user?->id ?? User::factory()->create()->id,
        ]);
    }

    /**
     * Create a guest application.
     */
    public function guest(): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => null,
        ]);
    }

    /**
     * Create a pending application.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'reviewed_by' => null,
            'reviewed_at' => null,
        ]);
    }

    /**
     * Create a reviewed application.
     */
    public function reviewed(string $status = 'reviewing'): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => $status,
            'reviewed_by' => User::factory()->create()->id,
            'reviewed_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'admin_notes' => $this->faker->paragraph(),
        ]);
    }

    /**
     * Create a shortlisted application.
     */
    public function shortlisted(): static
    {
        return $this->reviewed('shortlisted');
    }

    /**
     * Create a rejected application.
     */
    public function rejected(): static
    {
        return $this->reviewed('rejected');
    }

    /**
     * Create a hired application.
     */
    public function hired(): static
    {
        return $this->reviewed('hired');
    }

    /**
     * Create an application with attachments.
     */
    public function withAttachments(): static
    {
        return $this->state(fn (array $attributes) => [
            'resume_path' => 'resumes/' . $this->faker->uuid() . '.pdf',
            'portfolio_url' => $this->faker->url(),
            'linkedin_url' => 'https://linkedin.com/in/' . $this->faker->userName(),
        ]);
    }

    /**
     * Create an application with salary expectations.
     */
    public function withSalaryExpectation(float $salary): static
    {
        return $this->state(fn (array $attributes) => [
            'expected_salary' => $salary,
        ]);
    }
}
