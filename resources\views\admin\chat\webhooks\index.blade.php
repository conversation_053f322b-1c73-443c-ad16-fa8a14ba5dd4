@extends('layouts.dashboard')

@section('title', 'Chat Webhooks')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Chat Webhooks</h3>
                    <a href="{{ route('admin.chat.webhooks.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create Webhook
                    </a>
                </div>

                <div class="card-body">
                    <!-- Webhook Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-info"><i class="fas fa-webhook"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Total Webhooks</span>
                                    <span class="info-box-number">{{ $webhooks->total() }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-success"><i class="fas fa-check"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Successful Deliveries</span>
                                    <span class="info-box-number">{{ $stats['successful_deliveries'] }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-danger"><i class="fas fa-times"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Failed Deliveries</span>
                                    <span class="info-box-number">{{ $stats['failed_deliveries'] }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-warning"><i class="fas fa-percentage"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Success Rate</span>
                                    <span class="info-box-number">{{ $stats['success_rate'] }}%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Webhooks Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>URL</th>
                                    <th>Events</th>
                                    <th>Status</th>
                                    <th>Success Rate</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($webhooks as $webhook)
                                <tr>
                                    <td>
                                        <strong>{{ $webhook->name }}</strong>
                                        @if($webhook->description)
                                        <br><small class="text-muted">{{ Str::limit($webhook->description, 50) }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <code>{{ Str::limit($webhook->url, 40) }}</code>
                                    </td>
                                    <td>
                                        <span class="badge badge-info">{{ count($webhook->events) }} events</span>
                                    </td>
                                    <td>
                                        @if($webhook->is_active)
                                        <span class="badge badge-success">Active</span>
                                        @else
                                        <span class="badge badge-secondary">Inactive</span>
                                        @endif
                                    </td>
                                    <td>
                                        @php
                                        $stats = $webhook->getDeliveryStats();
                                        @endphp
                                        <span class="badge badge-{{ $stats['success_rate'] >= 90 ? 'success' : ($stats['success_rate'] >= 70 ? 'warning' : 'danger') }}">
                                            {{ $stats['success_rate'] }}%
                                        </span>
                                    </td>
                                    <td>{{ $webhook->created_at->format('M j, Y') }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.chat.webhooks.show', $webhook) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.chat.webhooks.edit', $webhook) }}" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-secondary" onclick="testWebhook({{ $webhook->id }})">
                                                <i class="fas fa-vial"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-{{ $webhook->is_active ? 'secondary' : 'success' }}" onclick="toggleWebhook({{ $webhook->id }})">
                                                <i class="fas fa-{{ $webhook->is_active ? 'pause' : 'play' }}"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" onclick="deleteWebhook({{ $webhook->id }})">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center">
                                        <p class="mb-0">No webhooks configured yet.</p>
                                        <a href="{{ route('admin.chat.webhooks.create') }}" class="btn btn-primary mt-2">Create Your First Webhook</a>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {{ $webhooks->links() }}
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function testWebhook(webhookId) {
    if (confirm('Send a test webhook to verify the endpoint?')) {
        fetch(`/admin/chat/webhooks/${webhookId}/test`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Webhook test successful!');
            } else {
                alert('Webhook test failed: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error testing webhook: ' + error.message);
        });
    }
}

function toggleWebhook(webhookId) {
    fetch(`/admin/chat/webhooks/${webhookId}/toggle`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error toggling webhook: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error toggling webhook: ' + error.message);
    });
}

function deleteWebhook(webhookId) {
    if (confirm('Are you sure you want to delete this webhook? This action cannot be undone.')) {
        fetch(`/admin/chat/webhooks/${webhookId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error deleting webhook: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error deleting webhook: ' + error.message);
        });
    }
}
</script>
@endpush
@endsection
