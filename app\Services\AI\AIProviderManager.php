<?php

namespace App\Services\AI;

use App\Services\AI\Providers\OpenAIProvider;
use App\Services\AI\Providers\AnthropicProvider;
use App\Services\AI\Providers\GoogleProvider;
use App\Services\AI\Providers\XAIProvider;
use App\Services\AI\Contracts\AIProviderInterface;
use App\Services\CircuitBreakerService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use Exception;

class AIProviderManager
{
    protected array $providers = [];
    protected string $defaultProvider;
    protected array $config;
    protected array $circuitBreakers = [];

    public function __construct()
    {
        $this->config = config('ai-providers');
        $this->defaultProvider = $this->config['default'];
        $this->initializeProviders();
    }

    /**
     * Initialize all configured providers.
     */
    protected function initializeProviders(): void
    {
        foreach ($this->config['providers'] as $name => $config) {
            $this->providers[$name] = $this->createProvider($name, $config);
        }
    }

    /**
     * Get or create circuit breaker for a provider.
     */
    protected function getCircuitBreaker(string $providerName): CircuitBreakerService
    {
        if (!isset($this->circuitBreakers[$providerName])) {
            $this->circuitBreakers[$providerName] = new CircuitBreakerService(
                "ai_provider_{$providerName}",
                config('ai-providers.circuit_breaker.failure_threshold', 5),
                config('ai-providers.circuit_breaker.recovery_timeout', 300),
                config('ai-providers.circuit_breaker.expected_exception_threshold', 10)
            );
        }
        return $this->circuitBreakers[$providerName];
    }

    /**
     * Create a provider instance based on the provider name.
     */
    protected function createProvider(string $name, array $config): AIProviderInterface
    {
        return match ($name) {
            'openai' => new OpenAIProvider($config),
            'anthropic' => new AnthropicProvider($config),
            'google' => new GoogleProvider($config),
            'xai' => new XAIProvider($config),
            default => throw new Exception("Unsupported AI provider: {$name}")
        };
    }

    /**
     * Get a provider instance.
     */
    public function provider(string $name = null): AIProviderInterface
    {
        $name = $name ?: $this->defaultProvider;
        
        if (!isset($this->providers[$name])) {
            throw new Exception("AI provider '{$name}' not found");
        }

        return $this->providers[$name];
    }

    /**
     * Generate a chat response with fallback support.
     */
    public function generateResponse(
        string $message,
        array $context = [],
        string $model = null,
        string $provider = null
    ): array {
        $provider = $provider ?: $this->defaultProvider;
        $cacheKey = $this->generateCacheKey($message, $context, $model, $provider);

        // Check cache first
        if ($this->config['caching']['enabled']) {
            $cached = Cache::get($cacheKey);
            if ($cached) {
                Log::info('AI response served from cache', ['provider' => $provider, 'model' => $model]);
                return $cached;
            }
        }

        // Check rate limiting
        if ($this->config['rate_limiting']['enabled']) {
            $this->checkRateLimit($provider);
        }

        try {
            // Try primary provider
            $response = $this->tryProvider($provider, $message, $context, $model);
            
            // Cache successful response
            if ($this->config['caching']['enabled']) {
                Cache::put($cacheKey, $response, $this->config['caching']['ttl']);
            }

            return $response;

        } catch (Exception $e) {
            Log::warning("Primary AI provider failed", [
                'provider' => $provider,
                'error' => $e->getMessage()
            ]);

            // Try fallback providers if enabled
            if ($this->config['fallback']['enabled']) {
                return $this->tryFallbackProviders($provider, $message, $context, $model);
            }

            throw $e;
        }
    }

    /**
     * Try to generate response with a specific provider.
     */
    protected function tryProvider(
        string $providerName,
        string $message,
        array $context,
        string $model = null
    ): array {
        $circuitBreaker = $this->getCircuitBreaker($providerName);

        return $circuitBreaker->call(function () use ($providerName, $message, $context, $model) {
            $provider = $this->provider($providerName);

            // Get default model if not specified
            if (!$model) {
                $model = $this->getDefaultModel($providerName);
            }

            return $provider->generateResponse($message, $context, $model);
        });
    }

    /**
     * Try fallback providers when primary fails.
     */
    protected function tryFallbackProviders(
        string $primaryProvider,
        string $message,
        array $context,
        string $model = null
    ): array {
        $fallbackProviders = $this->config['fallback']['providers'][$primaryProvider] ?? [];

        foreach ($fallbackProviders as $fallbackProvider) {
            try {
                Log::info("Trying fallback AI provider", [
                    'primary' => $primaryProvider,
                    'fallback' => $fallbackProvider
                ]);

                // Map model to equivalent in fallback provider
                $fallbackModel = $this->mapModelToProvider($model, $primaryProvider, $fallbackProvider);
                
                return $this->tryProvider($fallbackProvider, $message, $context, $fallbackModel);

            } catch (Exception $e) {
                Log::warning("Fallback AI provider failed", [
                    'provider' => $fallbackProvider,
                    'error' => $e->getMessage()
                ]);
                continue;
            }
        }

        throw new Exception("All AI providers failed");
    }

    /**
     * Get available models for a provider.
     */
    public function getAvailableModels(string $provider = null): array
    {
        $provider = $provider ?: $this->defaultProvider;
        return $this->config['providers'][$provider]['models'] ?? [];
    }

    /**
     * Get all available providers.
     */
    public function getAvailableProviders(): array
    {
        return array_keys($this->config['providers']);
    }

    /**
     * Get provider information.
     */
    public function getProviderInfo(string $provider): array
    {
        return $this->config['providers'][$provider] ?? [];
    }

    /**
     * Get model information.
     */
    public function getModelInfo(string $provider, string $model): array
    {
        return $this->config['providers'][$provider]['models'][$model] ?? [];
    }

    /**
     * Get models by tier.
     */
    public function getModelsByTier(string $tier, string $provider = null): array
    {
        $models = [];
        $providers = $provider ? [$provider] : $this->getAvailableProviders();

        foreach ($providers as $providerName) {
            $providerModels = $this->getAvailableModels($providerName);
            foreach ($providerModels as $modelName => $modelConfig) {
                if (($modelConfig['tier'] ?? 'standard') === $tier) {
                    $models[] = [
                        'provider' => $providerName,
                        'model' => $modelName,
                        'config' => $modelConfig,
                    ];
                }
            }
        }

        return $models;
    }

    /**
     * Get the default model for a provider.
     */
    protected function getDefaultModel(string $provider): string
    {
        $models = $this->getAvailableModels($provider);
        
        // Return the first standard tier model, or first model if no standard tier
        foreach ($models as $modelName => $config) {
            if (($config['tier'] ?? 'standard') === 'standard') {
                return $modelName;
            }
        }

        return array_key_first($models) ?: '';
    }

    /**
     * Map a model from one provider to an equivalent in another provider.
     */
    protected function mapModelToProvider(string $model, string $fromProvider, string $toProvider): string
    {
        // Get the tier of the original model
        $originalModelInfo = $this->getModelInfo($fromProvider, $model);
        $tier = $originalModelInfo['tier'] ?? 'standard';

        // Find a model with the same tier in the target provider
        $targetModels = $this->getModelsByTier($tier, $toProvider);
        
        if (!empty($targetModels)) {
            return $targetModels[0]['model'];
        }

        // Fallback to default model for the provider
        return $this->getDefaultModel($toProvider);
    }

    /**
     * Check rate limiting.
     */
    protected function checkRateLimit(string $provider): void
    {
        $key = "ai_rate_limit_{$provider}";
        $limit = $this->config['rate_limiting']['requests_per_minute'];

        if (RateLimiter::tooManyAttempts($key, $limit)) {
            $seconds = RateLimiter::availableIn($key);
            throw new Exception("Rate limit exceeded for provider {$provider}. Try again in {$seconds} seconds.");
        }

        RateLimiter::hit($key, 60); // 60 seconds window
    }

    /**
     * Generate cache key for response.
     */
    protected function generateCacheKey(string $message, array $context, string $model = null, string $provider = null): string
    {
        $data = [
            'message' => $message,
            'context' => $context,
            'model' => $model,
            'provider' => $provider,
        ];

        return 'ai_response_' . md5(serialize($data));
    }

    /**
     * Get usage statistics.
     */
    public function getUsageStats(): array
    {
        $stats = [];
        
        foreach ($this->getAvailableProviders() as $provider) {
            $stats[$provider] = [
                'requests_today' => Cache::get("ai_requests_today_{$provider}", 0),
                'tokens_used_today' => Cache::get("ai_tokens_today_{$provider}", 0),
                'cost_today' => Cache::get("ai_cost_today_{$provider}", 0.0),
                'last_used' => Cache::get("ai_last_used_{$provider}"),
                'circuit_breaker_status' => $this->getCircuitBreaker($provider)->getStatus(),
            ];
        }

        return $stats;
    }

    /**
     * Reset usage statistics.
     */
    public function resetUsageStats(string $provider = null): void
    {
        $providers = $provider ? [$provider] : $this->getAvailableProviders();

        foreach ($providers as $providerName) {
            Cache::forget("ai_requests_today_{$providerName}");
            Cache::forget("ai_tokens_today_{$providerName}");
            Cache::forget("ai_cost_today_{$providerName}");
        }
    }
}
