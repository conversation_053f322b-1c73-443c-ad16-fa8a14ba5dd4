# Live Chat AI System - Deployment Guide

## Overview

This guide covers the complete deployment process for the Live Chat AI System, including server setup, configuration, and production optimization.

## System Requirements

### Minimum Requirements

**Server Specifications:**
- CPU: 2 cores, 2.4 GHz
- RAM: 4 GB
- Storage: 20 GB SSD
- Network: 100 Mbps

**Software Requirements:**
- PHP 8.1 or higher
- MySQL 8.0 or PostgreSQL 13+
- Redis 6.0+
- Node.js 16+ (for real-time features)
- Web server (Nginx/Apache)

### Recommended Production Setup

**Server Specifications:**
- CPU: 4+ cores, 3.0 GHz
- RAM: 8+ GB
- Storage: 50+ GB SSD
- Network: 1 Gbps
- Load balancer (for high availability)

**Additional Services:**
- CDN for static assets
- SSL certificate
- Monitoring tools
- Backup solution

## Pre-deployment Checklist

### Environment Preparation

- [ ] Server provisioned and accessible
- [ ] Domain name configured
- [ ] SSL certificate obtained
- [ ] Database server setup
- [ ] Redis server setup
- [ ] Email service configured
- [ ] File storage configured

### Dependencies

- [ ] PHP extensions installed
- [ ] Composer installed
- [ ] Node.js and npm installed
- [ ] Web server configured
- [ ] Firewall rules configured
- [ ] Monitoring tools setup

## Installation Steps

### 1. Server Setup

**Update System:**
```bash
sudo apt update && sudo apt upgrade -y
```

**Install PHP and Extensions:**
```bash
sudo apt install php8.1 php8.1-fpm php8.1-mysql php8.1-redis \
    php8.1-mbstring php8.1-xml php8.1-curl php8.1-zip \
    php8.1-gd php8.1-intl php8.1-bcmath
```

**Install MySQL:**
```bash
sudo apt install mysql-server
sudo mysql_secure_installation
```

**Install Redis:**
```bash
sudo apt install redis-server
sudo systemctl enable redis-server
```

**Install Nginx:**
```bash
sudo apt install nginx
sudo systemctl enable nginx
```

### 2. Application Deployment

**Clone Repository:**
```bash
cd /var/www
sudo git clone https://github.com/your-company/chat-system.git
sudo chown -R www-data:www-data chat-system
cd chat-system
```

**Install Dependencies:**
```bash
composer install --optimize-autoloader --no-dev
npm install --production
npm run production
```

**Environment Configuration:**
```bash
cp .env.example .env
php artisan key:generate
```

### 3. Database Setup

**Create Database:**
```sql
CREATE DATABASE chat_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'chat_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON chat_system.* TO 'chat_user'@'localhost';
FLUSH PRIVILEGES;
```

**Run Migrations:**
```bash
php artisan migrate --force
php artisan db:seed --class=ProductionSeeder
```

### 4. Web Server Configuration

**Nginx Configuration:**
```nginx
server {
    listen 80;
    listen [::]:80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name your-domain.com;
    root /var/www/chat-system/public;

    # SSL Configuration
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    index index.php;

    charset utf-8;

    # Handle Laravel routes
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP-FPM Configuration
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # WebSocket proxy for real-time features
    location /socket.io/ {
        proxy_pass http://127.0.0.1:6001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Static files caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Security
    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

### 5. Environment Configuration

**Production .env Settings:**
```env
APP_NAME="Live Chat AI System"
APP_ENV=production
APP_KEY=base64:your-generated-key
APP_DEBUG=false
APP_URL=https://your-domain.com

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=chat_system
DB_USERNAME=chat_user
DB_PASSWORD=secure_password

BROADCAST_DRIVER=pusher
CACHE_DRIVER=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls

# Pusher Configuration (for real-time features)
PUSHER_APP_ID=your-app-id
PUSHER_APP_KEY=your-app-key
PUSHER_APP_SECRET=your-app-secret
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key
OPENAI_ORGANIZATION=your-organization-id

# Chat Performance Settings
CHAT_CACHE_ENABLED=true
CHAT_RATE_LIMITING_ENABLED=true
CHAT_MONITORING_ENABLED=true
CHAT_AI_CIRCUIT_BREAKER=true
```

### 6. Queue and Scheduler Setup

**Supervisor Configuration:**
```ini
[program:chat-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/chat-system/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=4
redirect_stderr=true
stdout_logfile=/var/www/chat-system/storage/logs/worker.log
stopwaitsecs=3600
```

**Cron Configuration:**
```bash
# Add to crontab (crontab -e)
* * * * * cd /var/www/chat-system && php artisan schedule:run >> /dev/null 2>&1
```

### 7. Real-time Features Setup

**Laravel Echo Server Configuration:**
```json
{
    "authHost": "https://your-domain.com",
    "authEndpoint": "/broadcasting/auth",
    "clients": [
        {
            "appId": "your-app-id",
            "key": "your-app-key"
        }
    ],
    "database": "redis",
    "databaseConfig": {
        "redis": {},
        "publishPresence": true
    },
    "devMode": false,
    "host": null,
    "port": "6001",
    "protocol": "http",
    "socketio": {},
    "secureOptions": 67108864,
    "sslCertPath": "",
    "sslKeyPath": "",
    "sslCertChainPath": "",
    "sslPassphrase": "",
    "subscribers": {
        "http": true,
        "redis": true
    },
    "apiOriginAllow": {
        "allowCors": true,
        "allowOrigin": "https://your-domain.com",
        "allowMethods": "GET, POST",
        "allowHeaders": "Origin, Content-Type, X-Auth-Token, X-Requested-With, Accept, Authorization, X-CSRF-TOKEN, X-Socket-Id"
    }
}
```

**Start Echo Server:**
```bash
sudo npm install -g laravel-echo-server
laravel-echo-server start
```

## Security Configuration

### 1. Firewall Setup

```bash
# UFW Configuration
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### 2. SSL/TLS Configuration

**Let's Encrypt (Certbot):**
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
sudo certbot renew --dry-run
```

### 3. Security Headers

Already included in Nginx configuration above.

### 4. Database Security

```sql
-- Remove test database
DROP DATABASE IF EXISTS test;

-- Remove anonymous users
DELETE FROM mysql.user WHERE User='';

-- Remove remote root access
DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');

-- Reload privileges
FLUSH PRIVILEGES;
```

## Performance Optimization

### 1. PHP Optimization

**PHP-FPM Configuration:**
```ini
; /etc/php/8.1/fpm/pool.d/www.conf
pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 500
```

**OPcache Configuration:**
```ini
; /etc/php/8.1/fpm/conf.d/10-opcache.ini
opcache.enable=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=2
opcache.fast_shutdown=1
```

### 2. Database Optimization

**MySQL Configuration:**
```ini
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
query_cache_type = 1
query_cache_size = 64M
```

### 3. Redis Optimization

```conf
# /etc/redis/redis.conf
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 4. Application Optimization

```bash
# Laravel optimizations
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache
```

## Monitoring and Logging

### 1. Application Monitoring

**Laravel Telescope (Development/Staging):**
```bash
composer require laravel/telescope
php artisan telescope:install
php artisan migrate
```

**Production Monitoring:**
- Use services like New Relic, Datadog, or Sentry
- Configure error tracking and performance monitoring
- Set up alerts for critical issues

### 2. Server Monitoring

**Basic Monitoring Script:**
```bash
#!/bin/bash
# /usr/local/bin/monitor.sh

# Check disk space
df -h | awk '$5 > 80 {print "Disk space warning: " $0}'

# Check memory usage
free -m | awk 'NR==2{printf "Memory Usage: %s/%sMB (%.2f%%)\n", $3,$2,$3*100/$2 }'

# Check CPU load
uptime | awk -F'load average:' '{ print "Load Average: " $2 }'

# Check important services
systemctl is-active --quiet nginx || echo "Nginx is down"
systemctl is-active --quiet mysql || echo "MySQL is down"
systemctl is-active --quiet redis || echo "Redis is down"
systemctl is-active --quiet php8.1-fpm || echo "PHP-FPM is down"
```

### 3. Log Management

**Logrotate Configuration:**
```conf
/var/www/chat-system/storage/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

## Backup Strategy

### 1. Database Backup

**Automated Backup Script:**
```bash
#!/bin/bash
# /usr/local/bin/backup-db.sh

BACKUP_DIR="/var/backups/chat-system"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="chat_system"
DB_USER="chat_user"
DB_PASS="secure_password"

mkdir -p $BACKUP_DIR

# Create backup
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME | gzip > $BACKUP_DIR/db_backup_$DATE.sql.gz

# Keep only last 30 days
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +30 -delete

# Upload to cloud storage (optional)
# aws s3 cp $BACKUP_DIR/db_backup_$DATE.sql.gz s3://your-backup-bucket/
```

### 2. File Backup

```bash
#!/bin/bash
# /usr/local/bin/backup-files.sh

BACKUP_DIR="/var/backups/chat-system"
DATE=$(date +%Y%m%d_%H%M%S)
APP_DIR="/var/www/chat-system"

# Backup uploaded files and configuration
tar -czf $BACKUP_DIR/files_backup_$DATE.tar.gz \
    $APP_DIR/storage/app/public \
    $APP_DIR/.env \
    $APP_DIR/config

# Keep only last 7 days for file backups
find $BACKUP_DIR -name "files_backup_*.tar.gz" -mtime +7 -delete
```

### 3. Backup Automation

```bash
# Add to crontab
0 2 * * * /usr/local/bin/backup-db.sh
0 3 * * 0 /usr/local/bin/backup-files.sh
```

## Troubleshooting

### Common Issues

**1. 500 Internal Server Error:**
- Check Laravel logs: `tail -f storage/logs/laravel.log`
- Check web server error logs
- Verify file permissions
- Check .env configuration

**2. Queue Jobs Not Processing:**
- Check supervisor status: `sudo supervisorctl status`
- Restart workers: `sudo supervisorctl restart chat-worker:*`
- Check Redis connection

**3. Real-time Features Not Working:**
- Check Echo server status
- Verify Pusher configuration
- Check WebSocket proxy in Nginx
- Test broadcasting authentication

**4. Database Connection Issues:**
- Verify database credentials
- Check MySQL service status
- Test connection: `php artisan tinker` then `DB::connection()->getPdo()`

### Performance Issues

**1. Slow Response Times:**
- Enable query logging
- Check database indexes
- Review slow query log
- Optimize heavy queries

**2. High Memory Usage:**
- Check for memory leaks in queue workers
- Optimize image processing
- Review caching strategy

**3. High CPU Usage:**
- Profile application with tools like Blackfire
- Optimize database queries
- Review AI processing load

## Maintenance

### Regular Tasks

**Daily:**
- Monitor system resources
- Check error logs
- Verify backup completion

**Weekly:**
- Review performance metrics
- Update security patches
- Clean up old logs

**Monthly:**
- Review and optimize database
- Update dependencies
- Security audit
- Capacity planning review

### Updates and Patches

**Application Updates:**
```bash
# Backup before update
/usr/local/bin/backup-db.sh
/usr/local/bin/backup-files.sh

# Update application
git pull origin main
composer install --optimize-autoloader --no-dev
npm install --production
npm run production

# Run migrations
php artisan migrate --force

# Clear caches
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Restart services
sudo supervisorctl restart chat-worker:*
sudo systemctl reload php8.1-fpm
sudo systemctl reload nginx
```

**System Updates:**
```bash
sudo apt update
sudo apt upgrade
sudo reboot # if kernel updated
```

## Support and Resources

### Documentation
- [API Documentation](./API_DOCUMENTATION.md)
- [User Guide](./USER_GUIDE.md)
- [Admin Guide](./ADMIN_GUIDE.md)

### Support Channels
- Technical Support: <EMAIL>
- Emergency Hotline: +1-XXX-XXX-XXXX
- Documentation: https://docs.your-company.com

### Useful Commands

```bash
# Check application status
php artisan about

# Clear all caches
php artisan optimize:clear

# Check queue status
php artisan queue:monitor

# Run health checks
php artisan health:check

# Generate performance report
php artisan chat:performance-report
```

---

*Last Updated: January 2024*
*Version: 1.0*
