<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('chat_blocked')->default(false)->after('is_active');
            $table->timestamp('chat_blocked_until')->nullable()->after('chat_blocked');
            $table->text('chat_block_reason')->nullable()->after('chat_blocked_until');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['chat_blocked', 'chat_blocked_until', 'chat_block_reason']);
        });
    }
};
