<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Carbon\Carbon;

class VisitorAnalytic extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'visitor_id',
        'fingerprint',
        'is_returning_visitor',
        'first_visit',
        'last_visit',
        'visit_count',
        'page_url',
        'page_title',
        'route_name',
        'route_parameters',
        'referrer_url',
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'ip_address',
        'user_agent',
        'method',
        'response_status',
        'response_time_ms',
        'device_type',
        'device_model',
        'browser',
        'browser_version',
        'platform',
        'platform_version',
        'screen_width',
        'screen_height',
        'language',
        'timezone',
        'country',
        'country_code',
        'region',
        'city',
        'latitude',
        'longitude',
        'isp',
        'session_id',
        'session_duration',
        'is_bounce',
        'page_views_in_session',
        'has_errors',
        'errors',
        'error_details',
        'dom_load_time',
        'page_load_time',
        'time_to_first_byte',
        'scroll_depth',
        'clicks_count',
        'clicked_elements',
        'form_submitted',
        'form_interactions',

        // Journey Tracking
        'user_journey',
        'journey_length',
        'converted',
        'conversion_type',
        'conversions',
        'exit_intent',
        'exit_data',
        'exit_timestamp',
        'time_on_page',
        'pages_visited_in_session',
        'pages_visited',
        'lead_score',
        'lead_status',

        // Campaign Attribution
        'campaign_source',
        'campaign_medium',
        'campaign_name',
        'campaign_term',
        'campaign_content',
        'is_bot',
        'bot_name',
        'is_suspicious',
        'suspicious_reasons',
        'risk_score',
        'visited_at',
        'left_at',
    ];

    protected function casts(): array
    {
        return [
            'route_parameters' => 'array',
            'errors' => 'array',
            'clicked_elements' => 'array',
            'form_interactions' => 'array',
        'user_journey' => 'array',
        'conversions' => 'array',
        'exit_data' => 'array',
        'pages_visited' => 'array',
        'exit_timestamp' => 'datetime',
            'is_returning_visitor' => 'boolean',
            'is_bounce' => 'boolean',
            'form_submitted' => 'boolean',
            'is_bot' => 'boolean',
            'is_suspicious' => 'boolean',
            'latitude' => 'decimal:8',
            'longitude' => 'decimal:8',
            'first_visit' => 'datetime',
            'last_visit' => 'datetime',
            'visited_at' => 'datetime',
            'left_at' => 'datetime',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($visitorAnalytic) {
            if (empty($visitorAnalytic->uuid)) {
                $visitorAnalytic->uuid = Str::uuid();
            }
            if (empty($visitorAnalytic->visited_at)) {
                $visitorAnalytic->visited_at = now();
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Scope for today's visitors.
     */
    public function scopeToday($query)
    {
        return $query->whereDate('visited_at', today());
    }

    /**
     * Scope for this week's visitors.
     */
    public function scopeThisWeek($query)
    {
        return $query->whereBetween('visited_at', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    /**
     * Scope for this month's visitors.
     */
    public function scopeThisMonth($query)
    {
        return $query->whereMonth('visited_at', now()->month)
                    ->whereYear('visited_at', now()->year);
    }

    /**
     * Scope for returning visitors.
     */
    public function scopeReturning($query)
    {
        return $query->where('is_returning_visitor', true);
    }

    /**
     * Scope for new visitors.
     */
    public function scopeNew($query)
    {
        return $query->where('is_returning_visitor', false);
    }

    /**
     * Scope for non-bot visitors.
     */
    public function scopeHuman($query)
    {
        return $query->where('is_bot', false);
    }

    /**
     * Scope for bot visitors.
     */
    public function scopeBot($query)
    {
        return $query->where('is_bot', true);
    }

    /**
     * Scope for visitors with errors.
     */
    public function scopeWithErrors($query)
    {
        return $query->where('has_errors', true);
    }

    /**
     * Scope for visitors by country.
     */
    public function scopeFromCountry($query, string $country)
    {
        return $query->where('country', $country);
    }

    /**
     * Scope for visitors by device type.
     */
    public function scopeByDevice($query, string $deviceType)
    {
        return $query->where('device_type', $deviceType);
    }

    /**
     * Scope for visitors by page.
     */
    public function scopeByPage($query, string $routeName)
    {
        return $query->where('route_name', $routeName);
    }

    /**
     * Get formatted visit duration.
     */
    public function getFormattedDurationAttribute(): string
    {
        if (!$this->session_duration) {
            return 'Unknown';
        }

        $minutes = floor($this->session_duration / 60);
        $seconds = $this->session_duration % 60;

        if ($minutes > 0) {
            return "{$minutes}m {$seconds}s";
        }

        return "{$seconds}s";
    }

    /**
     * Get visitor location string.
     */
    public function getLocationAttribute(): string
    {
        $parts = array_filter([$this->city, $this->region, $this->country]);
        return implode(', ', $parts) ?: 'Unknown';
    }

    /**
     * Get device info string.
     */
    public function getDeviceInfoAttribute(): string
    {
        $parts = array_filter([
            $this->device_type,
            $this->browser,
            $this->platform
        ]);
        return implode(' / ', $parts) ?: 'Unknown';
    }

    /**
     * Check if visitor is suspicious.
     */
    public function isSuspicious(): bool
    {
        return $this->is_suspicious || $this->risk_score >= 70;
    }

    /**
     * Get time since visit.
     */
    public function getTimeSinceVisitAttribute(): string
    {
        return $this->visited_at->diffForHumans();
    }

    /**
     * Scope for converted visitors.
     */
    public function scopeConverted($query)
    {
        return $query->where('converted', true);
    }

    /**
     * Scope for visitors by conversion type.
     */
    public function scopeByConversionType($query, string $type)
    {
        return $query->where('conversion_type', $type);
    }

    /**
     * Scope for visitors by lead status.
     */
    public function scopeByLeadStatus($query, string $status)
    {
        return $query->where('lead_status', $status);
    }

    /**
     * Scope for high-value leads.
     */
    public function scopeHighValueLeads($query, int $minScore = 70)
    {
        return $query->where('lead_score', '>=', $minScore);
    }

    /**
     * Scope for visitors with exit intent.
     */
    public function scopeWithExitIntent($query)
    {
        return $query->where('exit_intent', true);
    }

    /**
     * Scope for visitors by campaign source.
     */
    public function scopeByCampaignSource($query, string $source)
    {
        return $query->where('campaign_source', $source);
    }

    /**
     * Get user journey summary.
     */
    public function getJourneySummaryAttribute(): string
    {
        if (!$this->user_journey || empty($this->user_journey)) {
            return 'No journey data';
        }

        $steps = collect($this->user_journey)->pluck('step')->take(3);
        $summary = $steps->implode(' → ');

        if (count($this->user_journey) > 3) {
            $summary .= ' → ... (' . count($this->user_journey) . ' steps)';
        }

        return $summary;
    }

    /**
     * Get conversion summary.
     */
    public function getConversionSummaryAttribute(): string
    {
        if (!$this->converted || !$this->conversions) {
            return 'No conversions';
        }

        $types = collect($this->conversions)->pluck('type')->unique();
        return $types->implode(', ');
    }

    /**
     * Get lead score color for UI.
     */
    public function getLeadScoreColorAttribute(): string
    {
        if ($this->lead_score >= 80) return 'text-green-600';
        if ($this->lead_score >= 60) return 'text-yellow-600';
        if ($this->lead_score >= 40) return 'text-orange-600';
        return 'text-red-600';
    }

    /**
     * Get lead status badge color.
     */
    public function getLeadStatusBadgeAttribute(): string
    {
        return match($this->lead_status) {
            'hot' => 'bg-red-100 text-red-800',
            'warm' => 'bg-yellow-100 text-yellow-800',
            'converted' => 'bg-green-100 text-green-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    /**
     * Calculate engagement score.
     */
    public function getEngagementScoreAttribute(): int
    {
        $score = 0;

        // Time on page (max 30 points)
        if ($this->time_on_page) {
            $score += min(30, ($this->time_on_page / 60) * 10); // 10 points per minute, max 30
        }

        // Pages visited (max 20 points)
        $score += min(20, $this->pages_visited_in_session * 5);

        // Journey length (max 20 points)
        $score += min(20, $this->journey_length * 2);

        // Form interactions (max 15 points)
        if ($this->form_interactions) {
            $score += min(15, count($this->form_interactions) * 5);
        }

        // Conversions (max 15 points)
        if ($this->conversions) {
            $score += min(15, count($this->conversions) * 15);
        }

        return min(100, (int) $score);
    }
}
