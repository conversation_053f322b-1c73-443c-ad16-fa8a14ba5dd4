<?php

namespace App\Services;

use App\Models\LoginHistory;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Jenssegers\Agent\Agent;
use Carbon\Carbon;

class LoginHistoryService
{
    protected Agent $agent;
    protected ActivityLogger $activityLogger;

    public function __construct(ActivityLogger $activityLogger)
    {
        $this->agent = new Agent();
        $this->activityLogger = $activityLogger;
    }

    /**
     * Track a login attempt (successful or failed).
     */
    public function trackLoginAttempt(
        Request $request,
        ?User $user = null,
        string $status = 'success',
        ?string $failureReason = null,
        array $additionalData = []
    ): LoginHistory {
        $this->agent->setUserAgent($request->userAgent());

        // Get comprehensive device and location data
        $deviceData = $this->getDeviceInformation($request);
        $locationData = $this->getLocationInformation($request->ip());
        $securityData = $this->getSecurityInformation($request, $user);

        // Calculate risk score
        $riskScore = $this->calculateRiskScore($request, $user, $status, $deviceData, $locationData, $securityData);

        // Determine if this is a known device/location
        $isDeviceKnown = $this->isDeviceKnown($user, $deviceData['fingerprint']);
        $isLocationKnown = $this->isLocationKnown($user, $request->ip(), $locationData);

        $loginHistory = LoginHistory::create([
            'user_id' => $user?->id,
            'login_status' => $status,
            'ip_address' => $request->ip(),
            'location' => $locationData['formatted_location'],
            'latitude' => $locationData['latitude'],
            'longitude' => $locationData['longitude'],
            'device_info' => $deviceData,
            'device_fingerprint' => $deviceData['fingerprint'],
            'user_agent' => $request->userAgent(),
            'os_info' => $this->agent->platform() . ' ' . $this->agent->version($this->agent->platform()),
            'browser_info' => $this->agent->browser() . ' ' . $this->agent->version($this->agent->browser()),
            'device_type' => $this->getDeviceType(),
            'login_method' => $additionalData['login_method'] ?? 'standard',
            'failed_attempts' => $this->getRecentFailedAttempts($request->ip()),
            'is_vpn' => $securityData['is_vpn'],
            'is_tor' => $securityData['is_tor'],
            'security_alert' => $riskScore >= 70,
            'login_timezone' => $this->getTimezone($request),
            'login_ip_class' => $this->getIpClass($request->ip()),
            'risk_score' => $riskScore,
            'is_device_known' => $isDeviceKnown,
            'is_location_known' => $isLocationKnown,
            'is_ip_blacklisted' => $this->isIpBlacklisted($request->ip()),
            'is_ip_whitelisted' => $this->isIpWhitelisted($request->ip()),
            'twofa_used' => $additionalData['twofa_used'] ?? false,
            'twofa_code_verified' => $additionalData['twofa_verified'] ?? false,
            'session_id' => $request->hasSession() ? $request->session()->getId() : null,
            'referrer' => $request->header('referer'),
            'session_started_at' => now(),
            'failure_reason' => $failureReason,
            'security_flags' => $securityData,
            'geolocation_data' => $locationData,
            'isp' => $locationData['isp'] ?? null,
            'organization' => $locationData['org'] ?? null,
            'is_suspicious' => $riskScore >= 60,
        ]);

        // Log to activity logger for additional tracking
        $this->activityLogger->logActivity(
            'login_attempt',
            $status === 'success' ? 'User logged in successfully' : 'Login attempt failed',
            $status,
            $failureReason,
            [
                'login_history_id' => $loginHistory->id,
                'risk_score' => $riskScore,
                'device_known' => $isDeviceKnown,
                'location_known' => $isLocationKnown,
                'ip_address' => $request->ip(),
                'device_type' => $this->getDeviceType(),
            ],
            [],
            $riskScore,
            $user
        );

        return $loginHistory;
    }

    /**
     * Legacy method for backward compatibility.
     */
    public function recordLogin(
        User $user,
        Request $request,
        bool $successful = true,
        string $method = 'standard',
        string $failureReason = null
    ): LoginHistory {
        return $this->trackLoginAttempt(
            $request,
            $user,
            $successful ? 'success' : 'failed',
            $failureReason,
            ['login_method' => $method]
        );
    }

    /**
     * Record logout.
     */
    public function recordLogout(User $user, Request $request): void
    {
        $sessionId = $request->hasSession() ? $request->session()->getId() : null;

        if (!$sessionId) {
            return; // Can't track logout without session
        }

        $loginHistory = LoginHistory::where('user_id', $user->id)
            ->where('session_id', $sessionId)
            ->whereNull('session_ended_at')
            ->latest('session_started_at')
            ->first();

        if ($loginHistory) {
            $sessionDuration = abs(now()->diffInSeconds($loginHistory->session_started_at));

            $loginHistory->update([
                'session_ended_at' => now(),
                'session_duration' => $sessionDuration,
            ]);
        }
    }

    /**
     * Gather comprehensive login data.
     */
    protected function gatherLoginData(Request $request, User $user): array
    {
        $this->agent->setUserAgent($request->userAgent());
        $ipAddress = $this->getClientIpAddress($request);
        $locationData = $this->getLocationData($ipAddress);

        return [
            'ip_address' => $ipAddress,
            'user_agent' => $request->userAgent(),
            'device_fingerprint' => $this->generateDeviceFingerprint($request),
            'device_type' => $this->getDeviceType(),
            'device_name' => $this->getDeviceName(),
            'browser_name' => $this->agent->browser(),
            'browser_version' => $this->agent->version($this->agent->browser()),
            'operating_system' => $this->agent->platform(),
            'platform' => $this->agent->platform(),
            'country' => $locationData['country'] ?? null,
            'country_code' => $locationData['country_code'] ?? null,
            'region' => $locationData['region'] ?? null,
            'city' => $locationData['city'] ?? null,
            'timezone' => $locationData['timezone'] ?? null,
            'latitude' => $locationData['latitude'] ?? null,
            'longitude' => $locationData['longitude'] ?? null,
        ];
    }

    /**
     * Get client IP address.
     */
    protected function getClientIpAddress(Request $request): string
    {
        $ipKeys = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Standard
        ];

        foreach ($ipKeys as $key) {
            if ($request->server($key)) {
                $ips = explode(',', $request->server($key));
                $ip = trim($ips[0]);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $request->ip();
    }

    /**
     * Generate device fingerprint.
     */
    protected function generateDeviceFingerprint(Request $request): string
    {
        $components = [
            $request->userAgent(),
            $request->header('Accept-Language'),
            $request->header('Accept-Encoding'),
            $this->agent->platform(),
            $this->agent->browser(),
        ];

        return hash('sha256', implode('|', array_filter($components)));
    }

    /**
     * Get device type.
     */
    protected function getDeviceType(): string
    {
        if ($this->agent->isMobile()) {
            return 'mobile';
        } elseif ($this->agent->isTablet()) {
            return 'tablet';
        }

        // Default to desktop for unknown devices
        return 'desktop';
    }

    /**
     * Get device name.
     */
    protected function getDeviceName(): ?string
    {
        $device = $this->agent->device();
        return $device !== false ? $device : null;
    }

    /**
     * Get location data from IP address.
     */
    protected function getLocationData(string $ipAddress): array
    {
        // Skip location lookup for local/private IPs
        if (!filter_var($ipAddress, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
            return [];
        }

        try {
            // Using a free IP geolocation service (you can replace with your preferred service)
            $response = Http::timeout(5)->get("http://ip-api.com/json/{$ipAddress}");
            
            if ($response->successful()) {
                $data = $response->json();
                
                if ($data['status'] === 'success') {
                    return [
                        'country' => $data['country'] ?? null,
                        'country_code' => $data['countryCode'] ?? null,
                        'region' => $data['regionName'] ?? null,
                        'city' => $data['city'] ?? null,
                        'timezone' => $data['timezone'] ?? null,
                        'latitude' => $data['lat'] ?? null,
                        'longitude' => $data['lon'] ?? null,
                    ];
                }
            }
        } catch (\Exception $e) {
            // Log error but don't fail the login process
            \Log::warning('Failed to get location data for IP: ' . $ipAddress, ['error' => $e->getMessage()]);
        }

        return [];
    }

    /**
     * Check if this is a new device for the user.
     */
    protected function isNewDevice(User $user, string $deviceFingerprint): bool
    {
        return !LoginHistory::where('user_id', $user->id)
            ->where('device_fingerprint', $deviceFingerprint)
            ->where('login_successful', true)
            ->where('is_deleted', false)
            ->exists();
    }

    /**
     * Check if this is a new location for the user.
     */
    protected function isNewLocation(User $user, string $ipAddress): bool
    {
        return !LoginHistory::where('user_id', $user->id)
            ->where('ip_address', $ipAddress)
            ->where('login_successful', true)
            ->where('is_deleted', false)
            ->exists();
    }

    /**
     * Determine if login is suspicious.
     */
    protected function isSuspiciousLogin(User $user, array $loginData): bool
    {
        $suspiciousFactors = 0;

        // Check for new device
        if ($this->isNewDevice($user, $loginData['device_fingerprint'])) {
            $suspiciousFactors++;
        }

        // Check for new location
        if ($this->isNewLocation($user, $loginData['ip_address'])) {
            $suspiciousFactors++;
        }

        // Check for unusual time (outside normal hours)
        $hour = now()->hour;
        if ($hour < 6 || $hour > 23) {
            $suspiciousFactors++;
        }

        // Check for multiple recent failed attempts
        $recentFailedAttempts = LoginHistory::where('user_id', $user->id)
            ->where('login_successful', false)
            ->where('login_at', '>=', now()->subHours(1))
            ->count();

        if ($recentFailedAttempts >= 3) {
            $suspiciousFactors++;
        }

        // Consider suspicious if 2 or more factors
        return $suspiciousFactors >= 2;
    }

    /**
     * Gather additional data for extensibility.
     */
    protected function gatherAdditionalData(Request $request): array
    {
        return [
            'referer' => $request->header('Referer'),
            'accept_language' => $request->header('Accept-Language'),
            'accept_encoding' => $request->header('Accept-Encoding'),
            'connection' => $request->header('Connection'),
            'screen_resolution' => $request->input('screen_resolution'), // Can be sent via JS
            'timezone_offset' => $request->input('timezone_offset'), // Can be sent via JS
        ];
    }

    /**
     * Get login statistics for a user.
     */
    public function getLoginStats(User $user, int $days = 30): array
    {
        $startDate = now()->subDays($days);

        $totalLogins = $user->loginHistories()
            ->where('created_at', '>=', $startDate)
            ->count();

        $successfulLogins = $user->loginHistories()
            ->where('created_at', '>=', $startDate)
            ->where('login_status', 'success')
            ->count();

        $failedLogins = $user->loginHistories()
            ->where('created_at', '>=', $startDate)
            ->where('login_status', 'failed')
            ->count();

        $uniqueDevices = $user->loginHistories()
            ->where('created_at', '>=', $startDate)
            ->where('login_status', 'success')
            ->distinct('device_fingerprint')
            ->count();

        $uniqueLocations = $user->loginHistories()
            ->where('created_at', '>=', $startDate)
            ->where('login_status', 'success')
            ->distinct('ip_address')
            ->count();

        $recentLogins = $user->loginHistories()
            ->where('created_at', '>=', now()->subDays(7))
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        $suspiciousActivities = $user->loginHistories()
            ->where('created_at', '>=', $startDate)
            ->where('is_suspicious', true)
            ->count();

        return [
            'total_logins' => $totalLogins,
            'successful_logins' => $successfulLogins,
            'failed_logins' => $failedLogins,
            'success_rate' => $totalLogins > 0 ? round(($successfulLogins / $totalLogins) * 100, 2) : 0,
            'unique_devices' => $uniqueDevices,
            'unique_locations' => $uniqueLocations,
            'recent_logins' => $recentLogins,
            'suspicious_activities' => $suspiciousActivities,
            'period_days' => $days,
        ];
    }

    /**
     * Get comprehensive device information.
     */
    protected function getDeviceInformation(Request $request): array
    {
        $userAgent = $request->userAgent();

        return [
            'browser' => $this->agent->browser(),
            'browser_version' => $this->agent->version($this->agent->browser()),
            'platform' => $this->agent->platform(),
            'platform_version' => $this->agent->version($this->agent->platform()),
            'device' => $this->agent->device(),
            'is_mobile' => $this->agent->isMobile(),
            'is_tablet' => $this->agent->isTablet(),
            'is_desktop' => $this->agent->isDesktop(),
            'is_robot' => $this->agent->isRobot(),
            'robot_name' => $this->agent->robot(),
            'languages' => $request->getLanguages(),
            'fingerprint' => $this->generateDeviceFingerprint($request),
            'screen_resolution' => $request->header('sec-ch-viewport-width') . 'x' . $request->header('sec-ch-viewport-height'),
            'timezone_offset' => $request->header('timezone-offset'),
            'user_agent' => $userAgent,
        ];
    }

    /**
     * Get location information from IP address.
     */
    protected function getLocationInformation(string $ipAddress): array
    {
        try {
            // Use a geolocation service (you can replace with your preferred service)
            $response = Http::timeout(5)->get("http://ip-api.com/json/{$ipAddress}");

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'country' => $data['country'] ?? null,
                    'country_code' => $data['countryCode'] ?? null,
                    'region' => $data['regionName'] ?? null,
                    'region_code' => $data['region'] ?? null,
                    'city' => $data['city'] ?? null,
                    'zip' => $data['zip'] ?? null,
                    'latitude' => $data['lat'] ?? null,
                    'longitude' => $data['lon'] ?? null,
                    'timezone' => $data['timezone'] ?? null,
                    'isp' => $data['isp'] ?? null,
                    'org' => $data['org'] ?? null,
                    'as' => $data['as'] ?? null,
                    'formatted_location' => $this->formatLocation($data),
                ];
            }
        } catch (\Exception $e) {
            Log::warning('Failed to get location data for IP: ' . $ipAddress, ['error' => $e->getMessage()]);
        }

        return [
            'country' => null,
            'country_code' => null,
            'region' => null,
            'region_code' => null,
            'city' => null,
            'zip' => null,
            'latitude' => null,
            'longitude' => null,
            'timezone' => null,
            'isp' => null,
            'org' => null,
            'as' => null,
            'formatted_location' => 'Unknown Location',
        ];
    }

    /**
     * Format location data into a readable string.
     */
    protected function formatLocation(array $data): string
    {
        $parts = array_filter([
            $data['city'] ?? null,
            $data['regionName'] ?? null,
            $data['country'] ?? null,
        ]);

        return implode(', ', $parts) ?: 'Unknown Location';
    }

    /**
     * Get security information about the request.
     */
    protected function getSecurityInformation(Request $request, ?User $user): array
    {
        $ipAddress = $request->ip();

        return [
            'is_vpn' => $this->detectVpn($ipAddress),
            'is_tor' => $this->detectTor($ipAddress),
            'is_proxy' => $this->detectProxy($request),
            'suspicious_headers' => $this->detectSuspiciousHeaders($request),
            'rate_limited' => $this->isRateLimited($ipAddress),
            'previous_failures' => $this->getRecentFailedAttempts($ipAddress),
        ];
    }

    /**
     * Calculate risk score based on various factors.
     */
    protected function calculateRiskScore(
        Request $request,
        ?User $user,
        string $status,
        array $deviceData,
        array $locationData,
        array $securityData
    ): int {
        $score = 0;

        // Base score for failed login
        if ($status === 'failed') {
            $score += 30;
        }

        // Unknown device
        if (!$this->isDeviceKnown($user, $deviceData['fingerprint'])) {
            $score += 20;
        }

        // Unknown location
        if (!$this->isLocationKnown($user, $request->ip(), $locationData)) {
            $score += 15;
        }

        // VPN/Proxy usage
        if ($securityData['is_vpn']) {
            $score += 25;
        }

        // Tor usage
        if ($securityData['is_tor']) {
            $score += 40;
        }

        // Recent failed attempts
        $recentFailures = $securityData['previous_failures'];
        if ($recentFailures > 0) {
            $score += min($recentFailures * 10, 30);
        }

        // Suspicious user agent
        if ($this->isSuspiciousUserAgent($request->userAgent())) {
            $score += 15;
        }

        // Time-based factors (unusual login times)
        if ($this->isUnusualLoginTime($user)) {
            $score += 10;
        }

        return min($score, 100); // Cap at 100
    }

    /**
     * Check if device is known for the user.
     */
    protected function isDeviceKnown(?User $user, string $deviceFingerprint): bool
    {
        if (!$user) {
            return false;
        }

        return LoginHistory::where('user_id', $user->id)
            ->where('device_fingerprint', $deviceFingerprint)
            ->where('login_status', 'success')
            ->exists();
    }

    /**
     * Check if location is known for the user.
     */
    protected function isLocationKnown(?User $user, string $ipAddress, array $locationData): bool
    {
        if (!$user) {
            return false;
        }

        // Check by IP address first
        if (LoginHistory::where('user_id', $user->id)
            ->where('ip_address', $ipAddress)
            ->where('login_status', 'success')
            ->exists()) {
            return true;
        }

        // Check by city/region if available
        if (!empty($locationData['city']) && !empty($locationData['region'])) {
            return LoginHistory::where('user_id', $user->id)
                ->whereJsonContains('geolocation_data->city', $locationData['city'])
                ->whereJsonContains('geolocation_data->region', $locationData['region'])
                ->where('login_status', 'success')
                ->exists();
        }

        return false;
    }

    /**
     * Get recent failed attempts for IP.
     */
    protected function getRecentFailedAttempts(string $ipAddress): int
    {
        return LoginHistory::where('ip_address', $ipAddress)
            ->where('login_status', 'failed')
            ->where('created_at', '>=', now()->subHours(1))
            ->count();
    }

    /**
     * Get timezone from request.
     */
    protected function getTimezone(Request $request): string
    {
        return $request->header('timezone') ?? 'UTC';
    }

    /**
     * Get IP class (private/public).
     */
    protected function getIpClass(string $ipAddress): string
    {
        if (filter_var($ipAddress, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
            return 'public';
        }
        return 'private';
    }

    /**
     * Check if IP is blacklisted.
     */
    protected function isIpBlacklisted(string $ipAddress): bool
    {
        // Implement your IP blacklist logic here
        // This could check against a database table or external service
        return false;
    }

    /**
     * Check if IP is whitelisted.
     */
    protected function isIpWhitelisted(string $ipAddress): bool
    {
        // Implement your IP whitelist logic here
        return false;
    }

    /**
     * Detect VPN usage.
     */
    protected function detectVpn(string $ipAddress): bool
    {
        // Implement VPN detection logic
        // This could use a service like IPQualityScore or similar
        return false;
    }

    /**
     * Detect Tor usage.
     */
    protected function detectTor(string $ipAddress): bool
    {
        // Implement Tor detection logic
        return false;
    }

    /**
     * Detect proxy usage.
     */
    protected function detectProxy(Request $request): bool
    {
        $proxyHeaders = [
            'HTTP_VIA',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_FORWARDED',
            'HTTP_CLIENT_IP',
            'HTTP_FORWARDED_FOR_IP',
            'VIA',
            'X_FORWARDED_FOR',
            'FORWARDED_FOR',
            'X_FORWARDED',
            'FORWARDED',
            'CLIENT_IP',
            'FORWARDED_FOR_IP',
            'HTTP_PROXY_CONNECTION'
        ];

        foreach ($proxyHeaders as $header) {
            if ($request->server($header)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Detect suspicious headers.
     */
    protected function detectSuspiciousHeaders(Request $request): array
    {
        $suspicious = [];

        // Check for automation tools
        $userAgent = strtolower($request->userAgent() ?? '');
        $automationTools = ['curl', 'wget', 'python', 'bot', 'crawler', 'spider'];

        foreach ($automationTools as $tool) {
            if (strpos($userAgent, $tool) !== false) {
                $suspicious[] = "automation_tool_{$tool}";
            }
        }

        return $suspicious;
    }

    /**
     * Check if rate limited.
     */
    protected function isRateLimited(string $ipAddress): bool
    {
        // Check if this IP is currently rate limited
        return false; // Implement based on your rate limiting strategy
    }

    /**
     * Check if user agent is suspicious.
     */
    protected function isSuspiciousUserAgent(?string $userAgent): bool
    {
        if (!$userAgent) {
            return true;
        }

        $userAgent = strtolower($userAgent);
        $suspiciousPatterns = [
            'bot', 'crawler', 'spider', 'scraper', 'curl', 'wget', 'python-requests'
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (strpos($userAgent, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if login time is unusual for user.
     */
    protected function isUnusualLoginTime(?User $user): bool
    {
        if (!$user) {
            return false;
        }

        $hour = now()->hour;

        // Check user's typical login hours
        $typicalHours = LoginHistory::where('user_id', $user->id)
            ->where('login_status', 'success')
            ->where('created_at', '>=', now()->subDays(30))
            ->get()
            ->map(function ($login) {
                return $login->created_at->hour;
            })
            ->unique()
            ->values()
            ->toArray();

        // If user has no login history, consider unusual hours
        if (empty($typicalHours)) {
            return $hour < 6 || $hour > 23;
        }

        // If current hour is not in typical hours, it's unusual
        return !in_array($hour, $typicalHours);
    }
}
