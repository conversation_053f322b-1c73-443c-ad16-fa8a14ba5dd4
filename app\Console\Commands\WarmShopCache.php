<?php

namespace App\Console\Commands;

use App\Services\ShopCacheService;
use Illuminate\Console\Command;
use Illuminate\Http\Request;

class WarmShopCache extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'shop:warm-cache {--clear : Clear existing cache before warming}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Warm up shop caches with commonly accessed data';

    protected $shopCache;

    public function __construct(ShopCacheService $shopCache)
    {
        parent::__construct();
        $this->shopCache = $shopCache;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Warming up shop caches...');

        // Clear existing cache if requested
        if ($this->option('clear')) {
            $this->info('Clearing existing shop caches...');
            $this->shopCache->invalidateAll();
        }

        // Warm up categories with counts
        $this->info('Warming categories cache...');
        $this->shopCache->getCategoriesWithCounts();

        // Warm up price range
        $this->info('Warming price range cache...');
        $this->shopCache->getPriceRange();

        // Warm up featured products
        $this->info('Warming featured products cache...');
        $this->shopCache->getFeaturedProducts();

        // Warm up common search results
        $this->info('Warming common search results...');
        $this->warmCommonSearches();

        $this->info('Shop cache warming completed successfully!');
    }

    /**
     * Warm up common search patterns.
     */
    private function warmCommonSearches(): void
    {
        $commonSearches = [
            ['search' => ''],                    // All products
            ['search' => 'laptop'],
            ['search' => 'computer'],
            ['search' => 'dell'],
            ['sort' => 'price'],                 // Price sorted
            ['sort' => 'featured'],              // Featured products
            ['sort' => 'created_at'],            // Newest products
        ];

        foreach ($commonSearches as $params) {
            $request = new Request($params);
            $this->shopCache->getSearchResults($request, 12);
        }
    }
}
