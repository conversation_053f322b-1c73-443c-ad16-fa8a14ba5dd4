<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Services\ChatService;
use App\Services\ChatAIService;
use App\Services\ChatAssignmentService;
use App\Services\ChatRealtimeService;
use App\Http\Requests\CreateChatRoomRequest;
use App\Http\Requests\UpdateChatRoomRequest;
use App\Http\Requests\SendMessageRequest;
use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\ChatParticipant;
use App\Models\ChatSystemSetting;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\RateLimiter;

class ChatController extends Controller
{
    protected ChatService $chatService;
    protected ChatAIService $aiService;
    protected ChatAssignmentService $assignmentService;
    protected ChatRealtimeService $realtimeService;

    public function __construct(
        ChatService $chatService,
        ChatAIService $aiService,
        ChatAssignmentService $assignmentService,
        ChatRealtimeService $realtimeService
    ) {
        $this->chatService = $chatService;
        $this->aiService = $aiService;
        $this->assignmentService = $assignmentService;
        $this->realtimeService = $realtimeService;
        
        // Apply rate limiting using named rate limiters
        $this->middleware('throttle:chat-room-creation')->only('store'); // 10 per hour for room creation
        $this->middleware('throttle:chat-message-sending')->only('sendMessage'); // 60 per minute for messages
        $this->middleware('throttle:chat-realtime')->only(['typing', 'markAsRead', 'getRoomStatus']); // 30 per minute for real-time actions
        $this->middleware('throttle:chat-usage')->only(['joinRoom', 'leaveRoom']); // 100 per minute for room actions
    }

    /**
     * Get all chat rooms with filtering and pagination.
     */
    public function index(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'status' => 'sometimes|in:active,waiting,closed,archived',
            'type' => 'sometimes|in:visitor,customer,internal,support',
            'priority' => 'sometimes|integer|min:1|max:4',
            'language' => 'sometimes|string|size:2',
            'assigned_to' => 'sometimes|integer|exists:users,id',
            'per_page' => 'sometimes|integer|min:1|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $filters = $request->only(['status', 'type', 'priority', 'language', 'assigned_to']);
            $perPage = $request->get('per_page', 20);

            $rooms = $this->chatService->getActiveRooms($filters, $perPage);

            return response()->json([
                'success' => true,
                'data' => $rooms,
                'meta' => [
                    'filters_applied' => $filters,
                    'per_page' => $perPage,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve chat rooms',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create a new chat room.
     */
    public function store(CreateChatRoomRequest $request): JsonResponse
    {
        if (!ChatSystemSetting::isChatEnabled()) {
            return response()->json([
                'success' => false,
                'message' => 'Chat system is currently disabled',
            ], 503);
        }

        // Validation is now handled by CreateChatRoomRequest

        try {
            $validatedData = $request->validated();

            $room = $this->chatService->createRoom($validatedData);

            // Auto-assign if it's a support request
            if ($room->type === 'support' || $room->priority >= 3) {
                $this->assignmentService->autoAssign($room);
            }

            return response()->json([
                'success' => true,
                'message' => 'Chat room created successfully',
                'data' => [
                    'room' => $room->load(['participants', 'currentAssignment']),
                ],
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create chat room',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get a specific chat room.
     */
    public function show(string $uuid): JsonResponse
    {
        try {
            $room = $this->chatService->getRoom($uuid);

            if (!$room) {
                return response()->json([
                    'success' => false,
                    'message' => 'Chat room not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'room' => $room,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve chat room',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update a chat room.
     */
    public function update(UpdateChatRoomRequest $request, string $uuid): JsonResponse
    {
        // Validation is now handled by UpdateChatRoomRequest

        try {
            $room = ChatRoom::where('uuid', $uuid)->firstOrFail();
            $room->update($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Chat room updated successfully',
                'data' => [
                    'room' => $room->fresh(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update chat room',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Close a chat room.
     */
    public function destroy(string $uuid): JsonResponse
    {
        try {
            $room = ChatRoom::where('uuid', $uuid)->firstOrFail();
            
            $success = $this->chatService->closeRoom($room, 'Closed via API');

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Chat room closed successfully',
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to close chat room',
            ], 500);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to close chat room',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Send a message to a chat room.
     */
    public function sendMessage(SendMessageRequest $request, string $uuid): JsonResponse
    {
        // Validation is now handled by SendMessageRequest

        try {
            $room = ChatRoom::where('uuid', $uuid)->firstOrFail();
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Chat room not found',
            ], 404);
        }

        try {

            if ($room->isClosed()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot send message to closed chat room',
                ], 400);
            }

            $message = $this->chatService->sendMessage($room, $request->validated());

            // Broadcast the message in real-time (temporarily disabled for debugging)
            try {
                $this->realtimeService->broadcastMessage($message, $room);
            } catch (\Exception $e) {
                \Log::warning('Broadcasting failed, continuing without real-time updates', [
                    'error' => $e->getMessage(),
                    'message_id' => $message->id
                ]);
            }

            // Generate AI response if enabled and appropriate
            if (ChatSystemSetting::isAiEnabled() && $this->shouldGenerateAIResponse($room, $message) && !app()->environment('testing')) {
                $this->generateAIResponse($room, $message);
            }

            return response()->json([
                'success' => true,
                'message' => 'Message sent successfully',
                'data' => [
                    'message' => $message->load(['user', 'files']),
                ],
            ], 201);

        } catch (\Exception $e) {
            \Log::error('ChatController@sendMessage error', [
                'exception' => $e,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->validated(),
                'room_uuid' => $uuid
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send message',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get messages for a chat room.
     */
    public function getMessages(Request $request, string $uuid): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'limit' => 'sometimes|integer|min:1|max:100',
            'before_id' => 'sometimes|integer|exists:chat_messages,id',
            'after_id' => 'sometimes|integer|exists:chat_messages,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $room = ChatRoom::where('uuid', $uuid)->firstOrFail();
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Chat room not found',
            ], 404);
        }

        try {
            $limit = $request->get('limit', 50);
            $messages = $this->chatService->getRecentMessages($room, $limit);

            return response()->json([
                'success' => true,
                'data' => [
                    'messages' => $messages,
                    'room' => $room,
                ],
                'meta' => [
                    'count' => $messages->count(),
                    'limit' => $limit,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve messages',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get chat statistics.
     */
    public function getStatistics(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'date_from' => 'sometimes|date',
            'date_to' => 'sometimes|date|after_or_equal:date_from',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $filters = $request->only(['date_from', 'date_to']);
            $statistics = $this->chatService->getStatistics($filters);

            return response()->json([
                'success' => true,
                'data' => $statistics,
                'meta' => [
                    'filters' => $filters,
                    'generated_at' => now(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Check if AI response should be generated.
     */
    protected function shouldGenerateAIResponse(ChatRoom $room, ChatMessage $message): bool
    {
        // Don't respond to AI messages
        if ($message->is_ai_generated) {
            return false;
        }

        // Don't respond to staff messages
        if ($message->isFromStaff()) {
            return false;
        }

        // Don't respond if staff is actively assigned
        if ($room->hasStaffAssigned()) {
            return false;
        }

        return true;
    }

    /**
     * Generate AI response asynchronously.
     */
    protected function generateAIResponse(ChatRoom $room, ChatMessage $userMessage): void
    {
        try {
            $context = [
                'conversation_history' => $this->buildConversationHistory($room),
                'room_info' => [
                    'type' => $room->type,
                    'priority' => $room->priority,
                    'language' => $room->language,
                ],
                'user_message_id' => $userMessage->id, // Pass the user message ID for logging
            ];

            $aiResponse = $this->aiService->generateResponse(
                $userMessage->content,
                $room,
                $context
            );

            // Send AI response
            $aiMessage = $this->chatService->sendMessage($room, [
                'content' => $aiResponse['response'],
                'message_type' => 'text',
                'is_ai_generated' => true,
                'ai_confidence' => $aiResponse['confidence'],
                'ai_model' => $aiResponse['model_used'] ?? 'unknown',
                'metadata' => [
                    'intent' => $aiResponse['intent'],
                    'response_type' => $aiResponse['response_type'],
                    'processing_time_ms' => $aiResponse['processing_time_ms'],
                    'user_message_id' => $userMessage->id, // Reference to the user message
                ],
            ]);

            // Broadcast the AI message in real-time (if available)
            if (isset($this->realtimeService)) {
                $this->realtimeService->broadcastMessage($aiMessage, $room);
            }

            // Escalate to human if needed
            if ($aiResponse['should_escalate'] && isset($this->assignmentService)) {
                $this->assignmentService->autoAssign($room);
            }

        } catch (\Exception $e) {
            // Log error but don't fail the original request
            logger()->error('AI response generation failed', [
                'room_id' => $room->id,
                'message_id' => $userMessage->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Build conversation history for AI context.
     */
    protected function buildConversationHistory(ChatRoom $room): array
    {
        return $room->messages()
                   ->with('user')
                   ->orderBy('created_at', 'desc')
                   ->limit(10)
                   ->get()
                   ->reverse()
                   ->map(function ($message) {
                       return [
                           'role' => $message->is_ai_generated ? 'assistant' : 'user',
                           'content' => $message->content,
                       ];
                   })
                   ->toArray();
    }

    /**
     * Handle typing indicator.
     */
    public function typing(Request $request, string $uuid): JsonResponse
    {
        try {
            $request->validate([
                'is_typing' => 'required|boolean',
            ]);

            $room = ChatRoom::where('uuid', $uuid)->firstOrFail();

            // Get current user info (this would depend on your auth setup)
            $user = $request->user();
            $userType = get_class($user);
            $userId = $user->id;
            $userName = $user->name;

            $this->realtimeService->handleTyping(
                $room,
                $userType,
                $userId,
                $userName,
                $request->boolean('is_typing')
            );

            return response()->json([
                'success' => true,
                'message' => 'Typing status updated',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update typing status',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Mark message as read.
     */
    public function markAsRead(Request $request, string $roomUuid, string $messageUuid): JsonResponse
    {
        try {
            $room = ChatRoom::where('uuid', $roomUuid)->firstOrFail();
            $message = ChatMessage::where('uuid', $messageUuid)
                ->where('chat_room_id', $room->id)
                ->firstOrFail();

            $user = $request->user();
            $userType = get_class($user);
            $userId = $user->id;
            $userName = $user->name;

            $this->realtimeService->markMessageAsRead(
                $message,
                $room,
                $userType,
                $userId,
                $userName
            );

            return response()->json([
                'success' => true,
                'message' => 'Message marked as read',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark message as read',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Join a chat room.
     */
    public function joinRoom(Request $request, string $uuid): JsonResponse
    {
        try {
            $room = ChatRoom::where('uuid', $uuid)->firstOrFail();
            $user = $request->user();

            // Check if user is already a participant
            $participant = ChatParticipant::where('chat_room_id', $room->id)
                ->where('user_id', $user->id)
                ->first();

            if (!$participant) {
                // Create new participant
                $participant = ChatParticipant::create([
                    'chat_room_id' => $room->id,
                    'user_id' => $user->id,
                    'participant_type' => 'customer',
                    'role' => 'participant',
                    'display_name' => $user->name,
                    'joined_at' => now(),
                ]);
            }

            $this->realtimeService->handleRoomJoin($room, $participant);

            return response()->json([
                'success' => true,
                'message' => 'Joined room successfully',
                'data' => [
                    'participant' => $participant,
                    'online_users' => $this->realtimeService->getOnlineUsers($room->uuid),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to join room',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Leave a chat room.
     */
    public function leaveRoom(Request $request, string $uuid): JsonResponse
    {
        try {
            $room = ChatRoom::where('uuid', $uuid)->firstOrFail();
            $user = $request->user();

            // Remove participant
            ChatParticipant::where('chat_room_id', $room->id)
                ->where('user_id', $user->id)
                ->delete();

            $this->realtimeService->handleRoomLeave(
                $room,
                'customer',
                $user->id,
                $user->name
            );

            return response()->json([
                'success' => true,
                'message' => 'Left room successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to leave room',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get room status (online users, typing users, etc.).
     */
    public function getRoomStatus(Request $request, string $uuid): JsonResponse
    {
        try {
            $room = ChatRoom::where('uuid', $uuid)->firstOrFail();

            return response()->json([
                'success' => true,
                'data' => [
                    'room_uuid' => $room->uuid,
                    'online_users' => $this->realtimeService->getOnlineUsers($room->uuid),
                    'typing_users' => $this->realtimeService->getTypingUsers($room->uuid),
                    'participant_count' => $room->participants()->count(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get room status',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
