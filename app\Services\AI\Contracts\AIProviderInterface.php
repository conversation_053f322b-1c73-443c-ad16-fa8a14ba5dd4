<?php

namespace App\Services\AI\Contracts;

interface AIProviderInterface
{
    /**
     * Generate a response from the AI provider.
     *
     * @param string $message The user message
     * @param array $context Additional context for the conversation
     * @param string $model The model to use
     * @return array Response data including content, usage, and metadata
     */
    public function generateResponse(string $message, array $context = [], string $model = null): array;

    /**
     * Generate a response with streaming support.
     *
     * @param string $message The user message
     * @param array $context Additional context for the conversation
     * @param string $model The model to use
     * @param callable $callback Callback function for streaming chunks
     * @return array Final response data
     */
    public function generateStreamingResponse(string $message, array $context = [], string $model = null, callable $callback = null): array;

    /**
     * Analyze sentiment of the given text.
     *
     * @param string $text Text to analyze
     * @param string $model The model to use
     * @return array Sentiment analysis results
     */
    public function analyzeSentiment(string $text, string $model = null): array;

    /**
     * Translate text to the specified language.
     *
     * @param string $text Text to translate
     * @param string $targetLanguage Target language code
     * @param string $sourceLanguage Source language code (optional)
     * @param string $model The model to use
     * @return array Translation results
     */
    public function translateText(string $text, string $targetLanguage, string $sourceLanguage = null, string $model = null): array;

    /**
     * Summarize the given text.
     *
     * @param string $text Text to summarize
     * @param int $maxLength Maximum length of summary
     * @param string $model The model to use
     * @return array Summary results
     */
    public function summarizeText(string $text, int $maxLength = 150, string $model = null): array;

    /**
     * Extract key topics from text.
     *
     * @param string $text Text to analyze
     * @param int $maxTopics Maximum number of topics to extract
     * @param string $model The model to use
     * @return array Topics and keywords
     */
    public function extractTopics(string $text, int $maxTopics = 5, string $model = null): array;

    /**
     * Generate function calls based on user input.
     *
     * @param string $message User message
     * @param array $availableFunctions Available functions
     * @param array $context Conversation context
     * @param string $model The model to use
     * @return array Function call suggestions
     */
    public function generateFunctionCalls(string $message, array $availableFunctions = [], array $context = [], string $model = null): array;

    /**
     * Analyze image content (if supported by provider).
     *
     * @param string $imageUrl URL or base64 encoded image
     * @param string $prompt Optional prompt for image analysis
     * @param string $model The model to use
     * @return array Image analysis results
     */
    public function analyzeImage(string $imageUrl, string $prompt = null, string $model = null): array;

    /**
     * Get available models for this provider.
     *
     * @return array List of available models
     */
    public function getAvailableModels(): array;

    /**
     * Get provider name.
     *
     * @return string Provider name
     */
    public function getName(): string;

    /**
     * Check if the provider supports a specific feature.
     *
     * @param string $feature Feature name (vision, function_calling, streaming, etc.)
     * @param string $model Specific model to check (optional)
     * @return bool Whether the feature is supported
     */
    public function supportsFeature(string $feature, string $model = null): bool;

    /**
     * Get usage information for the last request.
     *
     * @return array Usage statistics
     */
    public function getLastUsage(): array;

    /**
     * Validate API configuration.
     *
     * @return bool Whether the configuration is valid
     */
    public function validateConfiguration(): bool;

    /**
     * Get cost estimate for a request.
     *
     * @param string $message Input message
     * @param array $context Context data
     * @param string $model Model to use
     * @return array Cost estimate
     */
    public function estimateCost(string $message, array $context = [], string $model = null): array;
}
