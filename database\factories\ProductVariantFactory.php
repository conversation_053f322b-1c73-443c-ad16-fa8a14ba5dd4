<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\ProductVariant;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductVariant>
 */
class ProductVariantFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ProductVariant::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $colors = ['Red', 'Blue', 'Green', 'Black', 'White', 'Yellow'];
        $sizes = ['Small', 'Medium', 'Large', 'XL', 'XXL'];
        
        $color = $this->faker->randomElement($colors);
        $size = $this->faker->randomElement($sizes);
        $name = "{$color} - {$size}";

        return [
            'product_id' => Product::factory(),
            'name' => $name,
            'sku' => 'VAR-' . strtoupper($this->faker->bothify('??###')),
            'price' => $this->faker->randomFloat(2, 20, 1000),
            'inventory_quantity' => $this->faker->numberBetween(0, 100),
            'image' => null,
            'attributes' => [
                'color' => $color,
                'size' => $size,
            ],
            'is_active' => true,
        ];
    }

    /**
     * Create a variant for a specific product.
     */
    public function forProduct(Product $product): static
    {
        return $this->state(fn (array $attributes) => [
            'product_id' => $product->id,
        ]);
    }

    /**
     * Create an active variant.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
            'is_deleted' => false,
        ]);
    }

    /**
     * Create an inactive variant.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a variant with specific inventory.
     */
    public function withInventory(int $quantity): static
    {
        return $this->state(fn (array $attributes) => [
            'inventory_quantity' => $quantity,
        ]);
    }

    /**
     * Create an out of stock variant.
     */
    public function outOfStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'inventory_quantity' => 0,
        ]);
    }

    /**
     * Create a variant with specific price.
     */
    public function withPrice(float $price): static
    {
        return $this->state(fn (array $attributes) => [
            'price' => $price,
        ]);
    }

    /**
     * Create a variant with specific attributes.
     */
    public function withAttributes(array $attributes): static
    {
        return $this->state(fn (array $factoryAttributes) => [
            'attributes' => $attributes,
            'name' => implode(' - ', array_values($attributes)),
        ]);
    }
}
