<?php

return [
    // Page Meta
    'page_title' => 'Servicios de Diseño de Sistemas - ChiSolution',
    'meta_description' => 'Servicios profesionales de diseño de sistemas incluyendo arquitectura escalable, diseño de microservicios, infraestructura en la nube, diseño de bases de datos, arquitectura API y optimización de rendimiento.',
    'meta_keywords' => 'diseño sistemas, arquitectura software, sistemas escalables, microservicios, arquitectura nube, diseño base datos, diseño API, arquitectura sistema, sistemas distribuidos, optimización rendimiento',

    // Hero Section
    'hero_title' => 'Servicios Profesionales de Diseño de Sistemas',
    'hero_description' => 'Construye sistemas escalables, confiables y de alto rendimiento que crecen con tu negocio. Arquitectura de sistemas experta para aplicaciones modernas y sistemas distribuidos.',
    'get_started' => 'Comenzar',
    'get_quote' => 'Obtener Cotización',
    'view_portfolio' => 'Ver Portafolio',

    // What is System Design Section
    'what_is_title' => '¿Qué es el <span class="text-blue-600">Diseño de Sistemas</span>?',
    'what_is_description' => 'El diseño de sistemas es el proceso de definir la arquitectura, componentes, módulos, interfaces y datos de un sistema para satisfacer requisitos específicos. Involucra crear sistemas escalables, confiables y mantenibles.',
    'high_level_architecture_title' => 'Arquitectura de Alto Nivel',
    'high_level_architecture_description' => 'Definir la estructura general y componentes de tu sistema, incluyendo cómo diferentes servicios interactúan y se comunican entre sí.',
    'scalability_title' => 'Escalabilidad',
    'scalability_description' => 'Diseñar sistemas que puedan manejar carga aumentada escalando horizontal o verticalmente sin comprometer el rendimiento.',
    'reliability_title' => 'Confiabilidad',
    'reliability_description' => 'Construir sistemas tolerantes a fallos con redundancia, mecanismos de failover y estrategias de recuperación ante desastres.',
    'performance_title' => 'Rendimiento',
    'performance_description' => 'Optimizar el rendimiento del sistema a través de algoritmos eficientes, estrategias de caché y gestión de recursos.',
    'security_title' => 'Seguridad',
    'security_description' => 'Implementar medidas de seguridad integrales incluyendo autenticación, autorización, encriptación y protección contra amenazas.',
    'data_flow_title' => 'Flujo de Datos',
    'data_flow_description' => 'Diseñar pipelines de datos eficientes y soluciones de almacenamiento que aseguren integridad y accesibilidad de datos.',

    // System Design Example Section
    'example_title' => 'Ejemplo de Diseño de Sistema: <span class="text-blue-600">Plataforma E-commerce</span>',
    'example_description' => 'Así es como diseñaríamos una plataforma e-commerce escalable que puede manejar millones de usuarios y transacciones.',
    'load_balancer_title' => 'Balanceador de Carga',
    'load_balancer_description' => 'Distribuye solicitudes entrantes a través de múltiples servidores',
    'api_gateway_title' => 'Gateway API',
    'api_gateway_description' => 'Punto de entrada único para todas las solicitudes del cliente',
    'microservices_title' => 'Microservicios',
    'microservices_description' => 'Servicios independientes para usuarios, productos, pedidos, pagos',
    'databases_title' => 'Bases de Datos',
    'databases_description' => 'Almacenamiento de datos optimizado para diferentes necesidades de servicio',
    'cache_layer_title' => 'Capa de Caché',
    'cache_layer_description' => 'Redis/Memcached para recuperación rápida de datos',
    'message_queue_title' => 'Cola de Mensajes',
    'message_queue_description' => 'Comunicación asíncrona entre servicios',
    'cdn_title' => 'CDN',
    'cdn_description' => 'Entrega de contenido global para activos estáticos',
    'monitoring_title' => 'Monitoreo',
    'monitoring_description' => 'Seguimiento en tiempo real de salud y rendimiento del sistema',

    // System Design Tools Section
    'tools_title' => 'Herramientas de <span class="text-blue-600">Diseño de Sistemas</span>',
    'tools_description' => 'Usamos herramientas y plataformas líderes en la industria para diseñar, implementar y monitorear sistemas escalables.',
    'architecture_modeling_title' => 'Modelado de Arquitectura',
    'architecture_modeling_description' => 'Diseño visual de sistemas usando herramientas como Lucidchart, Draw.io y Miro para crear diagramas de arquitectura integrales.',
    'cloud_platforms_title' => 'Plataformas en la Nube',
    'cloud_platforms_description' => 'AWS, Azure y Google Cloud Platform para infraestructura escalable y servicios gestionados.',
    'containerization_title' => 'Contenedorización',
    'containerization_description' => 'Docker y Kubernetes para contenedorización y orquestación de aplicaciones.',
    'database_design_title' => 'Diseño de Base de Datos',
    'database_design_description' => 'Bases de datos SQL y NoSQL incluyendo PostgreSQL, MongoDB, Redis y Elasticsearch.',
    'monitoring_tools_title' => 'Monitoreo y Observabilidad',
    'monitoring_tools_description' => 'Prometheus, Grafana, ELK Stack y New Relic para monitoreo integral del sistema.',
    'api_design_title' => 'Diseño de API',
    'api_design_description' => 'APIs RESTful, GraphQL y gRPC para comunicación eficiente de servicios.',

    // Technology Stack Section
    'tech_stack_title' => 'Stack <span class="text-blue-600">Tecnológico</span>',
    'tech_stack_description' => 'Aprovechamos tecnologías y frameworks modernos para construir sistemas robustos y escalables.',
    'backend_title' => 'Tecnologías Backend',
    'backend_description' => 'Node.js, Python, Java, Go, .NET para construir servicios backend de alto rendimiento.',
    'databases_stack_title' => 'Bases de Datos',
    'databases_stack_description' => 'PostgreSQL, MongoDB, Redis, Elasticsearch, InfluxDB para varias necesidades de almacenamiento de datos.',
    'cloud_devops_title' => 'Nube y DevOps',
    'cloud_devops_description' => 'AWS, Azure, GCP, Docker, Kubernetes, Terraform para infraestructura en la nube y despliegue.',
    'message_queues_title' => 'Colas de Mensajes',
    'message_queues_description' => 'RabbitMQ, Apache Kafka, Amazon SQS para comunicación asíncrona.',

    // System Design Process Section
    'process_title' => 'Nuestro Proceso de <span class="text-blue-600">Diseño de Sistemas</span>',
    'process_description' => 'Seguimos un enfoque sistemático para diseñar sistemas escalables que cumplan con tus requisitos comerciales.',
    'step_requirements_title' => 'Análisis de Requisitos',
    'step_requirements_description' => 'Entender requisitos funcionales y no funcionales, restricciones y objetivos comerciales.',
    'step_high_level_title' => 'Diseño de Alto Nivel',
    'step_high_level_description' => 'Crear vista general de arquitectura del sistema, identificar componentes principales y sus interacciones.',
    'step_detailed_title' => 'Diseño Detallado',
    'step_detailed_description' => 'Diseñar componentes individuales, APIs, modelos de datos y definir detalles de implementación.',
    'step_implementation_title' => 'Implementación y Pruebas',
    'step_implementation_description' => 'Construir el sistema siguiendo el diseño, realizar pruebas y optimizar rendimiento.',

    // Why Choose Us Section
    'why_choose_title' => '¿Por Qué Elegir Nuestros Servicios de Diseño de Sistemas?',
    'why_choose_description' => 'Combinamos experiencia técnica profunda con experiencia práctica para entregar sistemas que escalan y funcionan.',
    'scalable_solutions_title' => 'Soluciones Escalables',
    'scalable_solutions_description' => 'Diseñar sistemas que crecen con tu negocio y manejan carga creciente eficientemente.',
    'proven_expertise_title' => 'Experiencia Comprobada',
    'proven_expertise_description' => 'Años de experiencia diseñando sistemas desde startups hasta aplicaciones de nivel empresarial.',
    'modern_technologies_title' => 'Tecnologías Modernas',
    'modern_technologies_description' => 'Aprovechar tecnologías de vanguardia y mejores prácticas para rendimiento óptimo.',
    'cost_effective_title' => 'Costo-Efectivo',
    'cost_effective_description' => 'Optimizar costos de infraestructura mientras se mantiene alto rendimiento y confiabilidad.',
    'security_first_title' => 'Seguridad Primero',
    'security_first_description' => 'Medidas de seguridad integradas y cumplimiento con estándares de la industria.',
    'ongoing_optimization_title' => 'Optimización Continua',
    'optimization_description' => 'Monitoreo y optimización continuos para asegurar rendimiento máximo.',

    // CTA Section
    'cta_title' => '¿Listo para Construir tu Sistema Escalable?',
    'cta_description' => 'Diseñemos una arquitectura de sistema que escale con tu negocio y entregue rendimiento excepcional.',
    'start_project' => 'Iniciar tu Proyecto de Diseño de Sistema',
    'free_consultation' => 'Consulta de Arquitectura Gratuita',
    'view_work' => 'Ver Nuestro Trabajo',

    // System Components
    'system_components' => [
        'Load Balancers' => 'Balanceadores de Carga',
        'API Gateways' => 'Gateways API',
        'Microservices' => 'Microservicios',
        'Databases' => 'Bases de Datos',
        'Caching' => 'Caché',
        'Message Queues' => 'Colas de Mensajes',
        'CDN' => 'CDN',
        'Monitoring' => 'Monitoreo',
    ],

    // Architecture Patterns
    'architecture_patterns' => [
        'Microservices Architecture' => 'Arquitectura de Microservicios',
        'Event-Driven Architecture' => 'Arquitectura Dirigida por Eventos',
        'Serverless Architecture' => 'Arquitectura Sin Servidor',
        'Layered Architecture' => 'Arquitectura en Capas',
    ],

    // Scalability Features
    'scalability_features' => [
        'Horizontal Scaling' => 'Escalado Horizontal',
        'Vertical Scaling' => 'Escalado Vertical',
        'Auto-scaling' => 'Auto-escalado',
        'Load Distribution' => 'Distribución de Carga',
    ],

    // Performance Features
    'performance_features' => [
        'Caching Strategies' => 'Estrategias de Caché',
        'Database Optimization' => 'Optimización de Base de Datos',
        'CDN Integration' => 'Integración CDN',
        'Performance Monitoring' => 'Monitoreo de Rendimiento',
    ],
];
