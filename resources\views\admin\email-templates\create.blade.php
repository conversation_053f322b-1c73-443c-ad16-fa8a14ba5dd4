@extends('layouts.dashboard')

@section('title', 'Create Email Template')

@section('content')
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Create Email Template</h1>
            <p class="mt-1 text-sm text-gray-600">Create a new email template for your campaigns</p>
        </div>
        <a href="{{ route('admin.email-templates.index') }}" 
           class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
            </svg>
            Back to Templates
        </a>
    </div>

    <!-- Messages Container -->
    <div id="messages-container" class="hidden"></div>

    <!-- Form -->
    <form id="template-form" method="POST" action="{{ route('admin.email-templates.store') }}" class="space-y-6">
        @csrf

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
                    
                    <div class="space-y-4">
                        <!-- Template Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
                                Template Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   id="name" 
                                   name="name" 
                                   value="{{ old('name') }}"
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('name') border-red-500 @enderror">
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                            <textarea id="description" 
                                      name="description" 
                                      rows="3"
                                      placeholder="Brief description of this template..."
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('description') border-red-500 @enderror">{{ old('description') }}</textarea>
                            @error('description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Subject Line -->
                        <div>
                            <label for="subject" class="block text-sm font-medium text-gray-700 mb-1">
                                Subject Line <span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   id="subject" 
                                   name="subject" 
                                   value="{{ old('subject') }}"
                                   required
                                   placeholder="Email subject line..."
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('subject') border-red-500 @enderror">
                            @error('subject')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Email Content -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Email Content</h3>
                        <div class="flex items-center space-x-2">
                            <button type="button" 
                                    id="preview-btn"
                                    class="inline-flex items-center px-3 py-2 bg-secondary-600 text-white rounded-lg hover:bg-secondary-700 transition-colors duration-200">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                </svg>
                                Preview
                            </button>
                        </div>
                    </div>

                    <!-- Content Tabs -->
                    <div class="border-b border-gray-200 mb-4">
                        <nav class="-mb-px flex space-x-8">
                            <button type="button" 
                                    class="content-tab-btn py-2 px-1 border-b-2 font-medium text-sm border-primary-500 text-primary-600"
                                    data-tab="html">
                                HTML Content
                            </button>
                            <button type="button" 
                                    class="content-tab-btn py-2 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                                    data-tab="text">
                                Plain Text
                            </button>
                        </nav>
                    </div>

                    <!-- HTML Content -->
                    <div id="html-content-tab" class="content-tab">
                        <label for="html_content" class="block text-sm font-medium text-gray-700 mb-2">
                            HTML Content <span class="text-red-500">*</span>
                        </label>
                        <textarea id="html_content" 
                                  name="html_content" 
                                  rows="15"
                                  required
                                  placeholder="Enter your HTML email content here..."
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-mono text-sm @error('html_content') border-red-500 @enderror">{{ old('html_content') }}</textarea>
                        @error('html_content')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Text Content -->
                    <div id="text-content-tab" class="content-tab hidden">
                        <label for="text_content" class="block text-sm font-medium text-gray-700 mb-2">Plain Text Content</label>
                        <textarea id="text_content" 
                                  name="text_content" 
                                  rows="15"
                                  placeholder="Enter plain text version of your email (optional)..."
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-mono text-sm @error('text_content') border-red-500 @enderror">{{ old('text_content') }}</textarea>
                        @error('text_content')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Variable Helper -->
                    <div class="mt-4 p-4 bg-blue-50 rounded-lg">
                        <h4 class="text-sm font-medium text-blue-900 mb-2">Available Variables</h4>
                        <p class="text-sm text-blue-700 mb-2">You can use these variables in your content:</p>
                        <div class="flex flex-wrap gap-2">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 cursor-pointer variable-tag" data-variable="@{{name}}">@{{name}}</span>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 cursor-pointer variable-tag" data-variable="@{{email}}">@{{email}}</span>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 cursor-pointer variable-tag" data-variable="@{{unsubscribe_url}}">@{{unsubscribe_url}}</span>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 cursor-pointer variable-tag" data-variable="@{{company_name}}">@{{company_name}}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Template Settings -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Template Settings</h3>
                    
                    <div class="space-y-4">
                        <!-- Category -->
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 mb-1">
                                Category <span class="text-red-500">*</span>
                            </label>
                            <select id="category" 
                                    name="category" 
                                    required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('category') border-red-500 @enderror">
                                <option value="">Select Category</option>
                                @foreach($categories as $key => $label)
                                    <option value="{{ $key }}" {{ old('category') === $key ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                            @error('category')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Type -->
                        <div>
                            <label for="type" class="block text-sm font-medium text-gray-700 mb-1">
                                Type <span class="text-red-500">*</span>
                            </label>
                            <select id="type" 
                                    name="type" 
                                    required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('type') border-red-500 @enderror">
                                <option value="">Select Type</option>
                                @foreach($types as $key => $label)
                                    <option value="{{ $key }}" {{ old('type') === $key ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                            @error('type')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Status -->
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="is_active" 
                                   name="is_active" 
                                   value="1"
                                   {{ old('is_active', true) ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <label for="is_active" class="ml-2 text-sm text-gray-700">Active</label>
                        </div>

                        <!-- Default Template -->
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="is_default" 
                                   name="is_default" 
                                   value="1"
                                   {{ old('is_default') ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <label for="is_default" class="ml-2 text-sm text-gray-700">Set as default template for this category</label>
                        </div>
                    </div>
                </div>

                <!-- Template Variables -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Template Variables</h3>
                    <p class="text-sm text-gray-600 mb-4">Define custom variables for this template</p>
                    
                    <div id="variables-container">
                        <!-- Variables will be added here dynamically -->
                    </div>
                    
                    <button type="button" 
                            id="add-variable-btn"
                            class="mt-2 inline-flex items-center px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        Add Variable
                    </button>
                </div>

                <!-- Actions -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="space-y-3">
                        <button type="submit" 
                                id="submit-btn"
                                name="submit_action"
                                value="save"
                                class="w-full inline-flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                            <span class="submit-text">Create Template</span>
                            <span class="loading-text hidden">Creating...</span>
                        </button>
                        
                        <button type="submit" 
                                id="submit-continue-btn"
                                name="submit_action"
                                value="save_and_continue"
                                class="w-full inline-flex items-center justify-center px-4 py-2 bg-secondary-600 text-white rounded-lg hover:bg-secondary-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                            <span class="submit-continue-text">Create & Continue Editing</span>
                            <span class="loading-continue-text hidden">Creating...</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab functionality
    const tabButtons = document.querySelectorAll('.content-tab-btn');
    const tabContents = document.querySelectorAll('.content-tab');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // Update button states
            tabButtons.forEach(btn => {
                btn.classList.remove('border-primary-500', 'text-primary-600');
                btn.classList.add('border-transparent', 'text-gray-500');
            });
            this.classList.remove('border-transparent', 'text-gray-500');
            this.classList.add('border-primary-500', 'text-primary-600');
            
            // Update tab content
            tabContents.forEach(content => {
                content.classList.add('hidden');
            });
            document.getElementById(targetTab + '-content-tab').classList.remove('hidden');
        });
    });

    // Variable functionality
    let variableIndex = 0;
    
    document.getElementById('add-variable-btn').addEventListener('click', function() {
        const container = document.getElementById('variables-container');
        const variableHtml = `
            <div class="variable-item flex items-center space-x-2 mb-2">
                <input type="text" 
                       name="variables[${variableIndex}][name]" 
                       placeholder="Variable name"
                       class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm">
                <input type="text" 
                       name="variables[${variableIndex}][description]" 
                       placeholder="Description"
                       class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm">
                <button type="button" 
                        class="remove-variable-btn p-2 text-red-600 hover:text-red-700">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                    </svg>
                </button>
            </div>
        `;
        container.insertAdjacentHTML('beforeend', variableHtml);
        variableIndex++;
    });

    // Remove variable functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-variable-btn')) {
            e.target.closest('.variable-item').remove();
        }
    });

    // Variable tag insertion
    document.querySelectorAll('.variable-tag').forEach(tag => {
        tag.addEventListener('click', function() {
            const variable = this.dataset.variable;
            const activeTab = document.querySelector('.content-tab:not(.hidden)');
            const textarea = activeTab.querySelector('textarea');
            
            if (textarea) {
                const start = textarea.selectionStart;
                const end = textarea.selectionEnd;
                const text = textarea.value;
                textarea.value = text.substring(0, start) + variable + text.substring(end);
                textarea.focus();
                textarea.setSelectionRange(start + variable.length, start + variable.length);
            }
        });
    });

    // Form submission
    const form = document.getElementById('template-form');
    const submitBtn = document.getElementById('submit-btn');
    const submitContinueBtn = document.getElementById('submit-continue-btn');
    const messagesContainer = document.getElementById('messages-container');

    function handleFormSubmit(button, isSubmitBtn) {
        return async function(e) {
            e.preventDefault();

            // Show loading state
            button.disabled = true;
            if (isSubmitBtn) {
                document.querySelector('.submit-text').classList.add('hidden');
                document.querySelector('.loading-text').classList.remove('hidden');
            } else {
                document.querySelector('.submit-continue-text').classList.add('hidden');
                document.querySelector('.loading-continue-text').classList.remove('hidden');
            }

            // Clear previous messages
            messagesContainer.innerHTML = '';
            messagesContainer.classList.add('hidden');

            try {
                const formData = new FormData(form);
                formData.delete('submit_action');
                formData.append('submit_action', button.getAttribute('value'));

                const response = await fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    if (data.redirect) {
                        window.location.href = data.redirect;
                    } else {
                        location.reload();
                    }
                } else {
                    // Show error message
                    messagesContainer.innerHTML = `
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <div class="flex">
                                <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">Error</h3>
                                    <p class="mt-1 text-sm text-red-700">${data.message || 'An error occurred while creating the template.'}</p>
                                </div>
                            </div>
                        </div>
                    `;
                    messagesContainer.classList.remove('hidden');
                }
            } catch (error) {
                console.error('Error:', error);
                messagesContainer.innerHTML = `
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex">
                            <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">Error</h3>
                                <p class="mt-1 text-sm text-red-700">An unexpected error occurred. Please try again.</p>
                            </div>
                        </div>
                    </div>
                `;
                messagesContainer.classList.remove('hidden');
            } finally {
                // Reset loading state
                button.disabled = false;
                if (isSubmitBtn) {
                    document.querySelector('.submit-text').classList.remove('hidden');
                    document.querySelector('.loading-text').classList.add('hidden');
                } else {
                    document.querySelector('.submit-continue-text').classList.remove('hidden');
                    document.querySelector('.loading-continue-text').classList.add('hidden');
                }
            }
        };
    }

    submitBtn.addEventListener('click', handleFormSubmit(submitBtn, true));
    submitContinueBtn.addEventListener('click', handleFormSubmit(submitContinueBtn, false));
});
</script>
@endpush
@endsection
