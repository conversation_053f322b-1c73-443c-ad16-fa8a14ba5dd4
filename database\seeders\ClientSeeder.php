<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Client;
use Illuminate\Support\Facades\DB;

class ClientSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing clients
        DB::table('clients')->truncate();

        $clients = [
            [
                'name' => '<PERSON>',
                'company' => 'TechCorp Solutions',
                'description' => 'Leading technology solutions provider specializing in enterprise software development and digital transformation.',
                'logo_path' => null, // Will use default
                'project_images' => json_encode([]),
                'website_url' => 'https://techcorp-solutions.com',
                'industry' => 'Technology',
                'project_start_date' => '2024-01-15',
                'project_end_date' => '2024-06-30',
                'project_status' => 'completed',
                'testimonial' => 'ChiSolution delivered an exceptional e-commerce platform that exceeded our expectations. Their attention to detail and technical expertise is unmatched.',
                'rating' => 5,
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 1,
                'is_deleted' => false,
            ],
            [
                'name' => '<PERSON>',
                'company' => 'Green Energy Co',
                'description' => 'Renewable energy company focused on sustainable solutions for residential and commercial properties.',
                'logo_path' => null,
                'project_images' => json_encode([]),
                'website_url' => 'https://greenenergy.co',
                'industry' => 'Energy',
                'project_start_date' => '2024-02-01',
                'project_end_date' => '2024-05-15',
                'project_status' => 'completed',
                'testimonial' => 'The mobile app they developed for us has revolutionized how our customers interact with our services. Highly recommended!',
                'rating' => 5,
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 2,
                'is_deleted' => false,
            ],
            [
                'name' => 'Michael Chen',
                'company' => 'FinanceFirst Bank',
                'description' => 'Modern digital banking solutions with a focus on customer experience and financial innovation.',
                'logo_path' => null,
                'project_images' => json_encode([]),
                'website_url' => 'https://financefirst.bank',
                'industry' => 'Finance',
                'project_start_date' => '2024-03-10',
                'project_end_date' => '2024-08-20',
                'project_status' => 'ongoing',
                'testimonial' => 'Working with ChiSolution has been a game-changer for our digital transformation. Their team understands our complex requirements perfectly.',
                'rating' => 5,
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 3,
                'is_deleted' => false,
            ],
            [
                'name' => 'Emily Rodriguez',
                'company' => 'HealthCare Plus',
                'description' => 'Comprehensive healthcare services with innovative patient management systems and telemedicine solutions.',
                'logo_path' => null,
                'project_images' => json_encode([]),
                'website_url' => 'https://healthcareplus.com',
                'industry' => 'Healthcare',
                'project_start_date' => '2024-01-20',
                'project_end_date' => '2024-04-30',
                'project_status' => 'completed',
                'testimonial' => 'The patient portal they built has improved our efficiency by 300%. Outstanding work and professional service throughout the project.',
                'rating' => 5,
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 4,
                'is_deleted' => false,
            ],
            [
                'name' => 'David Wilson',
                'company' => 'EduTech Academy',
                'description' => 'Online education platform providing cutting-edge learning management systems and educational technology.',
                'logo_path' => null,
                'project_images' => json_encode([]),
                'website_url' => 'https://edutech-academy.com',
                'industry' => 'Education',
                'project_start_date' => '2024-02-15',
                'project_end_date' => '2024-07-10',
                'project_status' => 'completed',
                'testimonial' => 'ChiSolution created a learning platform that our students love. The user experience is intuitive and the performance is excellent.',
                'rating' => 4,
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 5,
                'is_deleted' => false,
            ],
            [
                'name' => 'Lisa Thompson',
                'company' => 'RetailMax Group',
                'description' => 'Multi-channel retail company with both physical stores and comprehensive e-commerce operations.',
                'logo_path' => null,
                'project_images' => json_encode([]),
                'website_url' => 'https://retailmax.group',
                'industry' => 'Retail',
                'project_start_date' => '2024-04-01',
                'project_end_date' => '2024-09-15',
                'project_status' => 'ongoing',
                'testimonial' => 'Their e-commerce solution has increased our online sales by 250%. The integration with our existing systems was seamless.',
                'rating' => 5,
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 6,
                'is_deleted' => false,
            ],
            [
                'name' => 'Robert Anderson',
                'company' => 'LogiFlow Logistics',
                'description' => 'Advanced logistics and supply chain management company with global operations and real-time tracking.',
                'logo_path' => null,
                'project_images' => json_encode([]),
                'website_url' => 'https://logiflow.com',
                'industry' => 'Logistics',
                'project_start_date' => '2024-01-05',
                'project_end_date' => '2024-03-25',
                'project_status' => 'completed',
                'testimonial' => 'The logistics management system they developed has streamlined our operations significantly. Excellent technical support and delivery.',
                'rating' => 4,
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 7,
                'is_deleted' => false,
            ],
            [
                'name' => 'Jennifer Lee',
                'company' => 'Creative Studios Inc',
                'description' => 'Full-service creative agency specializing in branding, digital marketing, and multimedia content creation.',
                'logo_path' => null,
                'project_images' => json_encode([]),
                'website_url' => 'https://creativestudios.inc',
                'industry' => 'Creative',
                'project_start_date' => '2024-03-01',
                'project_end_date' => '2024-06-15',
                'project_status' => 'completed',
                'testimonial' => 'ChiSolution brought our creative vision to life with a stunning portfolio website. The design perfectly captures our brand essence.',
                'rating' => 5,
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 8,
                'is_deleted' => false,
            ]
        ];

        foreach ($clients as $clientData) {
            Client::create($clientData);
        }

        $this->command->info('Client seeder completed successfully!');
    }
}
