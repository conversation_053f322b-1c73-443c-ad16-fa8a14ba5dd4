<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class NewsletterSubscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'email',
        'name',
        'is_active',
        'subscribed_at',
        'unsubscribed_at',
        'resubscribed_at',
        'ip_address',
        'user_agent',
        'referrer',
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'unsubscribe_ip',
        'unsubscribe_user_agent',
        'email_verified_at',
        'verification_token',
        // Email marketing fields
        'preferences',
        'language',
        'timezone',
        'last_email_sent_at',
        'last_email_opened_at',
        'last_email_clicked_at',
        'total_emails_sent',
        'total_emails_opened',
        'total_emails_clicked',
        'engagement_score',
        'lifecycle_stage',
        'last_activity_at',
        'custom_fields',
        'allow_marketing',
        'allow_promotional',
        'allow_transactional',
        'subscription_source',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'subscribed_at' => 'datetime',
            'unsubscribed_at' => 'datetime',
            'resubscribed_at' => 'datetime',
            'email_verified_at' => 'datetime',
            // Email marketing casts
            'preferences' => 'array',
            'last_email_sent_at' => 'datetime',
            'last_email_opened_at' => 'datetime',
            'last_email_clicked_at' => 'datetime',
            'last_activity_at' => 'datetime',
            'custom_fields' => 'array',
            'allow_marketing' => 'boolean',
            'allow_promotional' => 'boolean',
            'allow_transactional' => 'boolean',
            'engagement_score' => 'decimal:2',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($subscription) {
            if (empty($subscription->uuid)) {
                $subscription->uuid = Str::uuid();
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Scope for active subscriptions.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for inactive subscriptions.
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Scope for verified email subscriptions.
     */
    public function scopeVerified($query)
    {
        return $query->whereNotNull('email_verified_at');
    }

    /**
     * Scope for unverified email subscriptions.
     */
    public function scopeUnverified($query)
    {
        return $query->whereNull('email_verified_at');
    }

    /**
     * Get the subscription history.
     */
    public function history(): HasMany
    {
        return $this->hasMany(NewsletterSubscriptionHistory::class)->orderBy('created_at', 'desc');
    }

    /**
     * Check if subscription is verified.
     */
    public function isVerified(): bool
    {
        return !is_null($this->email_verified_at);
    }

    /**
     * Generate verification token.
     */
    public function generateVerificationToken(): string
    {
        $token = Str::random(64);
        $this->update(['verification_token' => $token]);
        return $token;
    }

    /**
     * Mark email as verified.
     */
    public function markEmailAsVerified(): void
    {
        $this->update([
            'email_verified_at' => now(),
            'verification_token' => null,
        ]);
    }

    /**
     * Get subscription duration in days.
     */
    public function getSubscriptionDurationAttribute(): int
    {
        if (!$this->subscribed_at) {
            return 0;
        }

        $endDate = $this->unsubscribed_at ?? now();
        return $this->subscribed_at->diffInDays($endDate);
    }

    /**
     * Get formatted subscription date.
     */
    public function getFormattedSubscribedDateAttribute(): string
    {
        return $this->subscribed_at?->format('M j, Y') ?? 'Unknown';
    }

    /**
     * Get subscription status.
     */
    public function getStatusAttribute(): string
    {
        if (!$this->is_active) {
            return 'Unsubscribed';
        }

        if (!$this->isVerified()) {
            return 'Pending Verification';
        }

        return 'Active';
    }

    /**
     * Get the tags associated with this subscription.
     */
    public function tags(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {
        return $this->belongsToMany(SubscriberTag::class, 'newsletter_subscription_tags')
                   ->withPivot('tagged_at', 'tagged_by')
                   ->withTimestamps();
    }

    /**
     * Get the email campaign sends for this subscription.
     */
    public function emailCampaignSends(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(EmailCampaignSend::class);
    }

    /**
     * Update engagement score based on email activity.
     */
    public function updateEngagementScore(): void
    {
        $score = 0;

        // Base score for being active
        if ($this->is_active) {
            $score += 20;
        }

        // Score based on email opens
        if ($this->total_emails_sent > 0) {
            $openRate = ($this->total_emails_opened / $this->total_emails_sent) * 100;
            $score += min($openRate * 0.4, 40); // Max 40 points for opens
        }

        // Score based on email clicks
        if ($this->total_emails_sent > 0) {
            $clickRate = ($this->total_emails_clicked / $this->total_emails_sent) * 100;
            $score += min($clickRate * 0.4, 40); // Max 40 points for clicks
        }

        $this->update(['engagement_score' => round($score, 2)]);
    }

    /**
     * Update lifecycle stage based on engagement.
     */
    public function updateLifecycleStage(): void
    {
        $daysSinceLastActivity = $this->last_activity_at
            ? $this->last_activity_at->diffInDays(now())
            : 999;

        $stage = match(true) {
            $daysSinceLastActivity <= 7 && $this->engagement_score >= 70 => 'engaged',
            $daysSinceLastActivity <= 30 && $this->engagement_score >= 40 => 'active',
            $daysSinceLastActivity <= 90 => 'at_risk',
            $daysSinceLastActivity > 90 => 'inactive',
            default => 'new'
        };

        $this->update(['lifecycle_stage' => $stage]);
    }

    /**
     * Track email activity.
     */
    public function trackEmailActivity(string $activity, array $data = []): void
    {
        $updates = ['last_activity_at' => now()];

        switch ($activity) {
            case 'sent':
                $updates['total_emails_sent'] = $this->total_emails_sent + 1;
                $updates['last_email_sent_at'] = now();
                break;
            case 'opened':
                $updates['total_emails_opened'] = $this->total_emails_opened + 1;
                $updates['last_email_opened_at'] = now();
                break;
            case 'clicked':
                $updates['total_emails_clicked'] = $this->total_emails_clicked + 1;
                $updates['last_email_clicked_at'] = now();
                break;
        }

        $this->update($updates);
        $this->updateEngagementScore();
        $this->updateLifecycleStage();
    }

    /**
     * Check if subscriber allows specific email type.
     */
    public function allowsEmailType(string $type): bool
    {
        return match($type) {
            'marketing' => $this->allow_marketing,
            'promotional' => $this->allow_promotional,
            'transactional' => $this->allow_transactional,
            default => false
        };
    }

    /**
     * Get lifecycle stage badge color.
     */
    public function getLifecycleStageBadgeColorAttribute(): string
    {
        return match($this->lifecycle_stage) {
            'new' => 'bg-blue-100 text-blue-800',
            'active' => 'bg-green-100 text-green-800',
            'engaged' => 'bg-emerald-100 text-emerald-800',
            'at_risk' => 'bg-yellow-100 text-yellow-800',
            'inactive' => 'bg-red-100 text-red-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Track subscription history.
     */
    public function trackHistory(
        string $action,
        string $statusFrom = null,
        string $statusTo = null,
        string $description = null,
        array $metadata = [],
        string $triggeredBy = 'system',
        int $adminUserId = null,
        string $ipAddress = null,
        string $userAgent = null
    ): NewsletterSubscriptionHistory {
        return $this->history()->create([
            'action' => $action,
            'status_from' => $statusFrom,
            'status_to' => $statusTo,
            'description' => $description,
            'metadata' => $metadata,
            'triggered_by' => $triggeredBy,
            'admin_user_id' => $adminUserId,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
        ]);
    }

    /**
     * Get engagement level.
     */
    public function getEngagementLevelAttribute(): string
    {
        return match(true) {
            $this->engagement_score >= 80 => 'Very High',
            $this->engagement_score >= 60 => 'High',
            $this->engagement_score >= 40 => 'Medium',
            $this->engagement_score >= 20 => 'Low',
            default => 'Very Low'
        };
    }

    /**
     * Scope subscribers by lifecycle stage.
     */
    public function scopeLifecycleStage($query, string $stage)
    {
        return $query->where('lifecycle_stage', $stage);
    }

    /**
     * Scope subscribers by engagement score.
     */
    public function scopeEngagementScore($query, float $minScore, float $maxScore = 100)
    {
        return $query->whereBetween('engagement_score', [$minScore, $maxScore]);
    }

    /**
     * Scope subscribers who allow specific email type.
     */
    public function scopeAllowsEmailType($query, string $type)
    {
        $column = match($type) {
            'marketing' => 'allow_marketing',
            'promotional' => 'allow_promotional',
            'transactional' => 'allow_transactional',
            default => null
        };

        if ($column) {
            return $query->where($column, true);
        }

        return $query;
    }
}
