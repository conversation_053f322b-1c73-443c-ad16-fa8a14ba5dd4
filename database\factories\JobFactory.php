<?php

namespace Database\Factories;

use App\Models\Job;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Job>
 */
class JobFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Job::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->jobTitle();

        return [
            'uuid' => Str::uuid(),
            'title' => $title,
            'slug' => Str::slug($title) . '-' . $this->faker->randomNumber(4),
            'description' => $this->faker->paragraphs(3, true),
            'requirements' => $this->faker->paragraphs(2, true),
            'responsibilities' => $this->faker->paragraphs(2, true),
            'benefits' => $this->faker->optional()->paragraphs(2, true),
            'location' => $this->faker->city(),
            'employment_type' => $this->faker->randomElement(['full-time', 'part-time', 'contract', 'internship']),
            'experience_level' => $this->faker->randomElement(['entry', 'mid', 'senior', 'executive']),
            'department' => $this->faker->randomElement(['Engineering', 'Design', 'Marketing', 'Sales', 'Operations', 'HR']),
            'salary_min' => $this->faker->optional()->randomFloat(2, 30000, 80000),
            'salary_max' => function (array $attributes) {
                return $attributes['salary_min'] ? $this->faker->randomFloat(2, $attributes['salary_min'], $attributes['salary_min'] + 50000) : null;
            },
            'salary_currency' => 'ZAR',
            'salary_period' => $this->faker->randomElement(['hourly', 'monthly', 'annually']),
            'is_remote' => $this->faker->boolean(30),
            'application_deadline' => $this->faker->optional()->dateTimeBetween('now', '+3 months'),
            'is_featured' => $this->faker->boolean(10),
            'is_active' => true,
            'is_deleted' => false,
            'sort_order' => $this->faker->numberBetween(0, 100),
            'meta_title' => $this->faker->optional()->sentence(),
            'meta_description' => $this->faker->optional()->sentence(),
            'meta_keywords' => $this->faker->optional()->words(5, true),
        ];
    }

    /**
     * Indicate that the job is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
            'is_deleted' => false,
        ]);
    }

    /**
     * Indicate that the job is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the job is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
        ]);
    }

    /**
     * Indicate that the job is remote.
     */
    public function remote(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_remote' => true,
            'location' => 'Remote',
        ]);
    }

    /**
     * Indicate that the job is on-site.
     */
    public function onSite(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_remote' => false,
        ]);
    }

    /**
     * Create a job with a specific employment type.
     */
    public function employmentType(string $type): static
    {
        return $this->state(fn (array $attributes) => [
            'employment_type' => $type,
        ]);
    }

    /**
     * Create a job with a specific experience level.
     */
    public function experienceLevel(string $level): static
    {
        return $this->state(fn (array $attributes) => [
            'experience_level' => $level,
        ]);
    }

    /**
     * Create a job with salary range.
     */
    public function withSalary(float $min, float $max): static
    {
        return $this->state(fn (array $attributes) => [
            'salary_min' => $min,
            'salary_max' => $max,
        ]);
    }

    /**
     * Create a job with application deadline.
     */
    public function withDeadline(\DateTime $deadline): static
    {
        return $this->state(fn (array $attributes) => [
            'application_deadline' => $deadline,
        ]);
    }

    /**
     * Create a job in a specific department.
     */
    public function inDepartment(string $department): static
    {
        return $this->state(fn (array $attributes) => [
            'department' => $department,
        ]);
    }
}
