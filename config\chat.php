<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Chat System Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the Live Chat & AI Chatbots
    | system. The system is designed as a RESTful API-first service that can
    | be consumed by multiple applications.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | System Status
    |--------------------------------------------------------------------------
    |
    | Controls whether the chat system is enabled or disabled. This can be
    | toggled by administrators through the admin panel.
    |
    */
    'enabled' => env('CHAT_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | Performance & Optimization
    |--------------------------------------------------------------------------
    |
    | Configuration for performance optimizations including caching, batching,
    | and circuit breaker settings.
    |
    */
    'performance' => [
        'message_batch_size' => env('CHAT_MESSAGE_BATCH_SIZE', 100),
        'cache_ttl' => [
            'active_rooms' => 30,        // 30 seconds
            'user_preferences' => 3600,  // 1 hour
            'ai_responses' => 86400,     // 24 hours
            'staff_availability' => 60,  // 1 minute
            'file_metadata' => 0,        // Permanent
        ],
        'virtual_scroll_items' => env('CHAT_VIRTUAL_SCROLL_ITEMS', 50),
        'typing_debounce_ms' => 300,
        'presence_throttle_seconds' => 30,
    ],

    /*
    |--------------------------------------------------------------------------
    | Circuit Breaker Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for circuit breaker protection of external services like AI APIs.
    |
    */
    'circuit_breaker' => [
        'ai_service' => [
            'failure_threshold' => env('CHAT_AI_FAILURE_THRESHOLD', 5),
            'recovery_timeout' => env('CHAT_AI_RECOVERY_TIMEOUT', 300),
            'expected_exception_threshold' => env('CHAT_AI_EXCEPTION_THRESHOLD', 10),
        ],
        'translation_service' => [
            'failure_threshold' => 3,
            'recovery_timeout' => 180,
            'expected_exception_threshold' => 5,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | AI Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for AI chatbot functionality including progressive complexity
    | and response caching.
    |
    */
    'ai' => [
        'enabled' => env('CHAT_AI_ENABLED', true),
        'provider' => env('CHAT_AI_PROVIDER', 'openai'),
        'model' => env('CHAT_AI_MODEL', 'gpt-4'),
        'temperature' => env('CHAT_AI_TEMPERATURE', 0.7),
        'max_tokens' => env('CHAT_AI_MAX_TOKENS', 150),
        'confidence_threshold' => env('CHAT_AI_CONFIDENCE_THRESHOLD', 0.6),
        'escalation_threshold' => env('CHAT_AI_ESCALATION_THRESHOLD', 0.3),
        
        'progressive_complexity' => [
            'template_response_time_ms' => 10,
            'simple_nlp_response_time_ms' => 100,
            'full_ai_response_time_ms' => 2000,
        ],
        
        'response_caching' => [
            'enabled' => true,
            'semantic_similarity_threshold' => 0.85,
            'cache_ttl_hours' => 24,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Real-time Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for WebSocket connections and real-time features.
    |
    */
    'realtime' => [
        'broadcaster' => env('BROADCAST_DRIVER', 'pusher'),
        'connection_pool_size' => env('CHAT_CONNECTION_POOL_SIZE', 100),
        'max_concurrent_users' => env('CHAT_MAX_CONCURRENT_USERS', 1000),
        'heartbeat_interval' => 30, // seconds
        'reconnect_attempts' => 3,
    ],

    /*
    |--------------------------------------------------------------------------
    | File Upload Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for file uploads in chat, integrating with existing FileService.
    |
    */
    'files' => [
        'enabled' => env('CHAT_FILES_ENABLED', true),
        'max_size' => env('CHAT_FILE_MAX_SIZE', '10MB'),
        'allowed_types' => [
            'images' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'documents' => ['pdf', 'doc', 'docx', 'txt', 'rtf'],
            'archives' => ['zip', 'rar', '7z'],
        ],
        'virus_scan' => true,
        'auto_optimize_images' => true,
        'token_expiry_hours' => 1,
    ],

    /*
    |--------------------------------------------------------------------------
    | Localization Configuration
    |--------------------------------------------------------------------------
    |
    | Multi-language support settings.
    |
    */
    'localization' => [
        'supported_languages' => ['en', 'af', 'zu', 'xh'],
        'default_language' => env('CHAT_DEFAULT_LANGUAGE', 'en'),
        'auto_detect' => env('CHAT_AUTO_DETECT_LANGUAGE', true),
        'auto_translate' => env('CHAT_AUTO_TRANSLATE', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    |
    | API rate limiting configuration for different endpoints.
    |
    */
    'rate_limits' => [
        'send_message' => '60:1',      // 60 per minute
        'create_room' => '10:60',      // 10 per hour
        'upload_file' => '20:60',      // 20 per hour
        'ai_generate_response' => '100:60', // 100 per hour
        'join_room' => '30:60',        // 30 per hour
    ],

    /*
    |--------------------------------------------------------------------------
    | Database Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for database optimization and archiving.
    |
    */
    'database' => [
        'message_retention_days' => env('CHAT_MESSAGE_RETENTION_DAYS', 365),
        'archive_after_days' => env('CHAT_ARCHIVE_AFTER_DAYS', 90),
        'cleanup_typing_indicators_minutes' => 5,
        'partition_by_month' => env('CHAT_PARTITION_BY_MONTH', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Business Hours
    |--------------------------------------------------------------------------
    |
    | Configuration for business hours and staff availability.
    |
    */
    'business_hours' => [
        'timezone' => env('CHAT_TIMEZONE', 'Africa/Johannesburg'),
        'weekdays' => env('CHAT_WEEKDAY_HOURS', '08:00-18:00'),
        'weekends' => env('CHAT_WEEKEND_HOURS', 'closed'),
        'holidays' => [], // Array of holiday dates
    ],

    /*
    |--------------------------------------------------------------------------
    | API Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for RESTful API functionality and multi-application support.
    |
    */
    'api' => [
        'version' => 'v1',
        'prefix' => 'api',
        'cors_origins' => [
            'https://chisolution.com',
            'https://*.chisolution.com',
        ],
        'authentication_methods' => ['bearer', 'api_key', 'session'],
        'usage_tracking' => env('CHAT_API_USAGE_TRACKING', true),
        'sdk_enabled' => env('CHAT_SDK_ENABLED', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring & Analytics
    |--------------------------------------------------------------------------
    |
    | Configuration for system monitoring and analytics.
    |
    */
    'monitoring' => [
        'enabled' => env('CHAT_MONITORING_ENABLED', true),
        'log_level' => env('CHAT_LOG_LEVEL', 'info'),
        'metrics_retention_days' => 90,
        'alert_thresholds' => [
            'response_time_ms' => 2000,
            'error_rate_percent' => 5,
            'queue_length' => 100,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Service Integration
    |--------------------------------------------------------------------------
    |
    | Configuration for integration with existing ChiSolution services.
    |
    */
    'services' => [
        'activity_logger' => true,
        'file_service' => true,
        'image_service' => true,
        'circuit_breaker' => true,
        'performance_optimizer' => true,
        'dashboard_cache' => true,
    ],
];
