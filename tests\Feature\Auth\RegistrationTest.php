<?php

namespace Tests\Feature\Auth;

use App\Models\Role;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

use PHPUnit\Framework\Attributes\Test;
class RegistrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Only seed roles if they don't exist
        if (!\App\Models\Role::exists()) {
            $this->artisan('db:seed', ['--class' => 'RoleSeeder']);
        }
    }
    #[Test]
    public function user_can_register_with_valid_data()
    {
        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'Password123!',
            'password_confirmation' => 'Password123!',
            'terms' => '1',
        ];

        $response = $this->postJson('/register', $userData);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Welcome! Your account has been created successfully.',
                ]);

        $this->assertDatabaseHas('users', [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
        ]);

        $user = User::where('email', '<EMAIL>')->first();
        $this->assertEquals('customer', $user->role->name);
        $this->assertTrue($user->is_active);
        $this->assertFalse($user->is_deleted);
    }
    #[Test]
    public function registration_fails_with_missing_required_fields()
    {
        $response = $this->postJson('/register', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors([
                    'first_name',
                    'last_name', 
                    'email',
                    'password',
                    'terms'
                ]);
    }
    #[Test]
    public function registration_fails_with_invalid_email()
    {
        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => 'invalid-email',
            'password' => 'Password123!',
            'password_confirmation' => 'Password123!',
            'terms' => '1',
        ];

        $response = $this->postJson('/register', $userData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
    }
    #[Test]
    public function registration_fails_with_duplicate_email()
    {
        // Create existing user
        User::factory()->create(['email' => '<EMAIL>']);

        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'Password123!',
            'password_confirmation' => 'Password123!',
            'terms' => '1',
        ];

        $response = $this->postJson('/register', $userData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
    }
    #[Test]
    public function registration_fails_with_weak_password()
    {
        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => '123',
            'password_confirmation' => '123',
            'terms' => '1',
        ];

        $response = $this->postJson('/register', $userData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['password']);
    }
    #[Test]
    public function registration_fails_with_password_mismatch()
    {
        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'Password123!',
            'password_confirmation' => 'DifferentPassword123!',
            'terms' => '1',
        ];

        $response = $this->postJson('/register', $userData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['password']);
    }
    #[Test]
    public function registration_fails_without_accepting_terms()
    {
        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'Password123!',
            'password_confirmation' => 'Password123!',
            'terms' => '0', // Not accepted
        ];

        $response = $this->postJson('/register', $userData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['terms']);
    }
    #[Test]
    public function registration_works_with_optional_phone()
    {
        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'Password123!',
            'password_confirmation' => 'Password123!',
            'phone' => '+1234567890',
            'terms' => '1',
        ];

        $response = $this->postJson('/register', $userData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
        ]);
    }
    #[Test]
    public function registration_fails_when_customer_role_missing()
    {
        // Delete the customer role
        Role::where('name', 'customer')->delete();

        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'Password123!',
            'password_confirmation' => 'Password123!',
            'terms' => '1',
        ];

        $response = $this->postJson('/register', $userData);

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                ]);
    }


}
