<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class OrderHistory extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'order_id',
        'event_type',
        'field_name',
        'old_value',
        'new_value',
        'description',
        'metadata',
        'triggered_by',
        'user_id',
        'user_name',
        'user_email',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'metadata' => 'array',
    ];

    // Event types
    const EVENT_ORDER_CREATED = 'order_created';
    const EVENT_STATUS_CHANGED = 'status_changed';
    const EVENT_PAYMENT_STATUS_CHANGED = 'payment_status_changed';
    const EVENT_SHIPPED = 'shipped';
    const EVENT_DELIVERED = 'delivered';
    const EVENT_CANCELLED = 'cancelled';
    const EVENT_NOTE_ADDED = 'note_added';
    const EVENT_REFUNDED = 'refunded';

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($history) {
            if (empty($history->uuid)) {
                $history->uuid = Str::uuid();
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Get the order that owns this history entry.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the user who made the change.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get formatted event description.
     */
    public function getFormattedDescriptionAttribute(): string
    {
        $description = $this->description;

        if ($this->user_name) {
            $description .= " by {$this->user_name}";
        } elseif ($this->triggered_by === 'system') {
            $description .= " (automatically)";
        }

        return $description;
    }

    /**
     * Get event icon based on event type.
     */
    public function getEventIconAttribute(): string
    {
        return match($this->event_type) {
            self::EVENT_ORDER_CREATED => 'check-circle',
            self::EVENT_STATUS_CHANGED => 'arrow-right',
            self::EVENT_PAYMENT_STATUS_CHANGED => 'credit-card',
            self::EVENT_SHIPPED => 'truck',
            self::EVENT_DELIVERED => 'check',
            self::EVENT_CANCELLED => 'x-circle',
            self::EVENT_NOTE_ADDED => 'message-circle',
            self::EVENT_REFUNDED => 'arrow-left',
            default => 'circle',
        };
    }

    /**
     * Get event color based on event type.
     */
    public function getEventColorAttribute(): string
    {
        return match($this->event_type) {
            self::EVENT_ORDER_CREATED => 'green',
            self::EVENT_STATUS_CHANGED => 'blue',
            self::EVENT_PAYMENT_STATUS_CHANGED => 'purple',
            self::EVENT_SHIPPED => 'indigo',
            self::EVENT_DELIVERED => 'green',
            self::EVENT_CANCELLED => 'red',
            self::EVENT_NOTE_ADDED => 'gray',
            self::EVENT_REFUNDED => 'yellow',
            default => 'gray',
        };
    }
}
