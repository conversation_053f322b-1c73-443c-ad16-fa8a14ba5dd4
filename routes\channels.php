<?php

use Illuminate\Support\Facades\Broadcast;
use App\Models\ChatRoom;
use App\Models\ChatParticipant;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

// Chat room private channels
Broadcast::channel('chat.room.{roomUuid}', function ($user, $roomUuid) {
    // Find the chat room
    $room = ChatRoom::where('uuid', $roomUuid)->first();
    
    if (!$room) {
        return false;
    }
    
    // Check if user is a participant in this room
    $participant = ChatParticipant::where('room_id', $room->id)
        ->where('user_id', $user->id)
        ->where('user_type', get_class($user))
        ->first();
    
    if ($participant) {
        return [
            'id' => $user->id,
            'name' => $user->name,
            'type' => get_class($user),
            'role' => $participant->role ?? 'participant',
        ];
    }
    
    // Allow staff/admin to join any room
    if (method_exists($user, 'hasRole') && ($user->hasRole('staff') || $user->hasRole('admin'))) {
        return [
            'id' => $user->id,
            'name' => $user->name,
            'type' => get_class($user),
            'role' => 'staff',
        ];
    }
    
    return false;
});

// General chat presence channel for online users
Broadcast::channel('chat.presence', function ($user) {
    return [
        'id' => $user->id,
        'name' => $user->name,
        'type' => get_class($user),
    ];
});

// Staff-only channel for assignments and notifications
Broadcast::channel('chat.staff', function ($user) {
    if (method_exists($user, 'hasRole') && ($user->hasRole('staff') || $user->hasRole('admin'))) {
        return [
            'id' => $user->id,
            'name' => $user->name,
            'type' => get_class($user),
            'role' => $user->hasRole('admin') ? 'admin' : 'staff',
        ];
    }
    
    return false;
});

// Admin-only channel for system management
Broadcast::channel('chat.admin', function ($user) {
    if (method_exists($user, 'hasRole') && $user->hasRole('admin')) {
        return [
            'id' => $user->id,
            'name' => $user->name,
            'type' => get_class($user),
            'role' => 'admin',
        ];
    }
    
    return false;
});
