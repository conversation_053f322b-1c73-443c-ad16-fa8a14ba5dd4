<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_reviews', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();

            // Core relationships
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('order_id')->constrained('orders')->onDelete('cascade');

            // Review content
            $table->tinyInteger('rating')->comment('1-5 star rating');
            $table->text('review_content');

            // Purchase verification
            $table->boolean('is_verified_purchase')->default(true)->comment('Always true since only verified purchases can review');
            $table->timestamp('purchase_date')->nullable()->comment('Date of original purchase');
            $table->timestamp('delivery_date')->nullable()->comment('Date order was delivered');

            // Review status
            $table->boolean('is_approved')->default(true)->comment('Auto-approved for verified purchases');
            $table->boolean('is_featured')->default(false)->comment('Featured review flag');
            $table->boolean('is_deleted')->default(false);

            // Engagement tracking
            $table->unsignedInteger('helpful_count')->default(0)->comment('Number of users who found this helpful');
            $table->json('helpful_users')->nullable()->comment('Array of user IDs who marked as helpful');

            // Moderation (if needed)
            $table->boolean('is_flagged')->default(false);
            $table->unsignedInteger('flag_count')->default(0);
            $table->json('flagged_by')->nullable()->comment('Array of user IDs who flagged this review');
            $table->text('admin_notes')->nullable();

            $table->timestamps();

            // Indexes for performance
            $table->index(['product_id', 'is_approved', 'is_deleted']);
            $table->index(['user_id', 'created_at']);
            $table->index(['order_id']);
            $table->index(['rating']);
            $table->index(['is_verified_purchase']);
            $table->index(['is_featured', 'created_at']);

            // Unique constraint: one review per user per product per order
            $table->unique(['product_id', 'user_id', 'order_id'], 'unique_product_user_order_review');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_reviews');
    }
};
