<?php

namespace App\Services;

use App\Models\VisitorAnalytic;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Cache;
use Jenssegers\Agent\Agent;

class VisitorAnalytics
{
    protected Request $request;
    protected Agent $agent;

    public function __construct(Request $request)
    {
        $this->request = $request;
        $this->agent = new Agent();
    }

    /**
     * Track a page visit.
     */
    public function trackPageVisit(
        string $pageTitle = null,
        array $additionalData = [],
        bool $trackPerformance = true
    ): VisitorAnalytic {
        $visitorId = $this->getOrCreateVisitorId();
        $fingerprint = $this->generateFingerprint();
        $isReturning = $this->isReturningVisitor($visitorId, $fingerprint);
        
        $deviceInfo = $this->getDeviceInfo();
        $locationInfo = $this->getLocationInfo();
        $performanceData = $trackPerformance ? $this->getPerformanceData() : [];
        $utmData = $this->getUtmData();
        
        // Get campaign data from UTM parameters
        $campaignData = $this->getCampaignData();

        $data = array_merge([
            'visitor_id' => $visitorId,
            'fingerprint' => $fingerprint,
            'is_returning_visitor' => $isReturning,
            'first_visit' => $isReturning ? $this->getFirstVisit($visitorId) : now(),
            'last_visit' => $isReturning ? $this->getLastVisit($visitorId) : null,
            'visit_count' => $this->getVisitCount($visitorId) + 1,
            'page_url' => $this->request->fullUrl(),
            'page_title' => $pageTitle,
            'route_name' => $this->request->route()?->getName(),
            'route_parameters' => $this->request->route()?->parameters() ?? [],
            'referrer_url' => $this->request->header('referer'),
            'ip_address' => $this->request->ip(),
            'user_agent' => $this->request->userAgent() ?? 'Missing or Unknown User-Agent',
            'method' => $this->request->method(),
            'session_id' => $this->getSessionId(),
            'is_bot' => $this->agent->isRobot(),
            'bot_name' => $this->agent->robot(),
            'visited_at' => now(),
        ], $deviceInfo, $locationInfo, $performanceData, $utmData, $campaignData, $additionalData);

        // Initialize journey and lead scoring
        $data['user_journey'] = [[
            'step' => $pageTitle ?: 'Page Visit',
            'type' => 'page_visit',
            'timestamp' => now()->toISOString(),
            'data' => $additionalData,
            'page_url' => $this->request->fullUrl(),
            'route_name' => $this->request->route()?->getName(),
        ]];
        $data['journey_length'] = 1;
        $data['lead_score'] = $this->calculateInitialLeadScore($data);
        $data['lead_status'] = $this->determineLeadStatus($data['lead_score']);

        // Calculate risk score
        $data['risk_score'] = $this->calculateRiskScore($data);
        $data['is_suspicious'] = $data['risk_score'] >= 70;

        $visit = VisitorAnalytic::create($data);

        return $visit;
    }

    /**
     * Track page exit/leave.
     */
    public function trackPageExit(
        string $visitorId,
        int $timeSpent = null,
        array $engagementData = []
    ): void {
        $latestVisit = VisitorAnalytic::where('visitor_id', $visitorId)
            ->where('session_id', $this->getSessionId())
            ->latest('visited_at')
            ->first();

        if ($latestVisit && !$latestVisit->left_at) {
            $updateData = [
                'left_at' => now(),
                'session_duration' => $timeSpent,
            ];

            if (!empty($engagementData)) {
                $updateData = array_merge($updateData, $engagementData);
            }

            $latestVisit->update($updateData);
        }
    }

    /**
     * Track error occurrence.
     */
    public function trackError(
        string $errorType,
        string $errorMessage,
        array $errorContext = []
    ): void {
        $visitorId = $this->getOrCreateVisitorId();

        $latestVisit = VisitorAnalytic::where('visitor_id', $visitorId)
            ->where('session_id', $this->getSessionId())
            ->latest('visited_at')
            ->first();

        if ($latestVisit) {
            $errors = $latestVisit->errors ?? [];
            $errors[] = [
                'type' => $errorType,
                'message' => $errorMessage,
                'context' => $errorContext,
                'timestamp' => now()->toISOString(),
            ];

            $latestVisit->update([
                'has_errors' => true,
                'errors' => $errors,
                'error_details' => $errorMessage,
            ]);
        }
    }

    /**
     * Track form interaction.
     */
    public function trackFormInteraction(
        string $formName,
        string $action = 'submit',
        bool $successful = true,
        array $formData = []
    ): void {
        $visitorId = $this->getOrCreateVisitorId();

        $latestVisit = VisitorAnalytic::where('visitor_id', $visitorId)
            ->where('session_id', $this->getSessionId())
            ->latest('visited_at')
            ->first();

        if ($latestVisit) {
            $interactions = $latestVisit->form_interactions ?? [];
            $interactions[] = [
                'form_name' => $formName,
                'action' => $action,
                'successful' => $successful,
                'data' => $this->sanitizeFormData($formData),
                'timestamp' => now()->toISOString(),
            ];

            // Update conversion tracking
            $conversions = $latestVisit->conversions ?? [];
            if ($action === 'submit' && $successful) {
                $conversions[] = [
                    'type' => $this->getConversionType($formName),
                    'form_name' => $formName,
                    'timestamp' => now()->toISOString(),
                    'data' => $this->sanitizeFormData($formData),
                ];
            }

            $latestVisit->update([
                'form_submitted' => $action === 'submit' && $successful,
                'form_interactions' => $interactions,
                'conversions' => $conversions,
                'converted' => !empty($conversions),
            ]);
        }
    }

    /**
     * Track user journey step.
     */
    public function trackJourneyStep(
        string $stepName,
        string $stepType = 'page_visit',
        array $stepData = []
    ): void {
        $visitorId = $this->getOrCreateVisitorId();

        $latestVisit = VisitorAnalytic::where('visitor_id', $visitorId)
            ->where('session_id', $this->getSessionId())
            ->latest('visited_at')
            ->first();

        if ($latestVisit) {
            $journey = $latestVisit->user_journey ?? [];
            $journey[] = [
                'step' => $stepName,
                'type' => $stepType,
                'timestamp' => now()->toISOString(),
                'data' => $stepData,
                'page_url' => $this->request->fullUrl(),
                'route_name' => $this->request->route()?->getName(),
            ];

            $latestVisit->update([
                'user_journey' => $journey,
                'journey_length' => count($journey),
            ]);
        }
    }

    /**
     * Track conversion event.
     */
    public function trackConversion(
        string $conversionType,
        array $conversionData = []
    ): void {
        $visitorId = $this->getOrCreateVisitorId();

        $latestVisit = VisitorAnalytic::where('visitor_id', $visitorId)
            ->where('session_id', $this->getSessionId())
            ->latest('visited_at')
            ->first();

        if ($latestVisit) {
            $conversions = $latestVisit->conversions ?? [];
            $conversions[] = [
                'type' => $conversionType,
                'timestamp' => now()->toISOString(),
                'data' => $this->sanitizeFormData($conversionData),
                'page_url' => $this->request->fullUrl(),
                'route_name' => $this->request->route()?->getName(),
            ];

            $latestVisit->update([
                'conversions' => $conversions,
                'converted' => true,
                'conversion_type' => $conversionType,
            ]);
        }
    }

    /**
     * Track user exit intent.
     */
    public function trackExitIntent(array $exitData = []): void
    {
        $visitorId = $this->getOrCreateVisitorId();

        $latestVisit = VisitorAnalytic::where('visitor_id', $visitorId)
            ->where('session_id', $this->getSessionId())
            ->latest('visited_at')
            ->first();

        if ($latestVisit) {
            $latestVisit->update([
                'exit_intent' => true,
                'exit_data' => $exitData,
                'exit_timestamp' => now(),
            ]);
        }
    }

    /**
     * Get conversion type from form name.
     */
    private function getConversionType(string $formName): string
    {
        $conversionMap = [
            'contact_form' => 'contact_inquiry',
            'newsletter_subscription' => 'newsletter_signup',
            'project_application_form' => 'project_application',
            'job_application_form' => 'job_application',
            'add_to_cart' => 'cart_addition',
            'checkout_form' => 'purchase',
            'quote_request' => 'quote_request',
            'consultation_booking' => 'consultation_booking',
        ];

        return $conversionMap[$formName] ?? 'form_submission';
    }

    /**
     * Get campaign data from request.
     */
    private function getCampaignData(): array
    {
        return [
            'campaign_source' => $this->request->get('utm_source'),
            'campaign_medium' => $this->request->get('utm_medium'),
            'campaign_name' => $this->request->get('utm_campaign'),
            'campaign_term' => $this->request->get('utm_term'),
            'campaign_content' => $this->request->get('utm_content'),
        ];
    }

    /**
     * Calculate initial lead score.
     */
    private function calculateInitialLeadScore(array $data): int
    {
        $score = 0;

        // Source scoring
        if (!empty($data['campaign_source'])) {
            $score += match($data['campaign_source']) {
                'google', 'bing' => 15,
                'facebook', 'linkedin' => 10,
                'email' => 20,
                'direct' => 25,
                default => 5
            };
        }

        // Page scoring
        $routeName = $data['route_name'] ?? '';
        $score += match(true) {
            str_contains($routeName, 'contact') => 20,
            str_contains($routeName, 'services') => 15,
            str_contains($routeName, 'projects') => 10,
            str_contains($routeName, 'about') => 8,
            str_contains($routeName, 'shop') => 12,
            default => 5
        };

        // Returning visitor bonus
        if ($data['is_returning_visitor']) {
            $score += 10;
        }

        // Device type scoring
        $score += match($data['device_type'] ?? '') {
            'desktop' => 10,
            'tablet' => 8,
            'mobile' => 5,
            default => 0
        };

        return min(100, $score);
    }

    /**
     * Determine lead status from score.
     */
    private function determineLeadStatus(int $score): string
    {
        return match(true) {
            $score >= 80 => 'hot',
            $score >= 60 => 'warm',
            $score >= 40 => 'cold',
            default => 'cold'
        };
    }

    /**
     * Track checkout funnel step.
     */
    public function trackCheckoutFunnelStep(
        string $step,
        array $stepData = [],
        bool $successful = true
    ): void {
        $visitorId = $this->getOrCreateVisitorId();

        $latestVisit = VisitorAnalytic::where('visitor_id', $visitorId)
            ->where('session_id', $this->getSessionId())
            ->latest('visited_at')
            ->first();

        if ($latestVisit) {
            $checkoutFunnel = $latestVisit->checkout_funnel ?? [];
            $checkoutFunnel[] = [
                'step' => $step,
                'successful' => $successful,
                'timestamp' => now()->toISOString(),
                'data' => $this->sanitizeFormData($stepData),
                'page_url' => $this->request->fullUrl(),
                'route_name' => $this->request->route()?->getName(),
            ];

            // Calculate funnel progress
            $funnelProgress = $this->calculateCheckoutFunnelProgress($checkoutFunnel);

            // Update checkout analytics
            $checkoutAnalytics = $latestVisit->checkout_analytics ?? [];
            $checkoutAnalytics = array_merge($checkoutAnalytics, [
                'current_step' => $step,
                'steps_completed' => count(array_filter($checkoutFunnel, fn($s) => $s['successful'])),
                'total_steps' => count($checkoutFunnel),
                'funnel_progress' => $funnelProgress,
                'last_step_timestamp' => now()->toISOString(),
                'has_errors' => !$successful,
            ]);

            $latestVisit->update([
                'checkout_funnel' => $checkoutFunnel,
                'checkout_analytics' => $checkoutAnalytics,
                'checkout_started' => true,
                'checkout_completed' => $step === 'completed' && $successful,
            ]);

            // Update lead score for checkout progress
            if ($successful) {
                $this->updateLeadScore($this->getCheckoutActivityType($step), $stepData);
            }
        }
    }

    /**
     * Track checkout abandonment.
     */
    public function trackCheckoutAbandonment(
        string $abandonedAtStep,
        array $abandonmentData = []
    ): void {
        $visitorId = $this->getOrCreateVisitorId();

        $latestVisit = VisitorAnalytic::where('visitor_id', $visitorId)
            ->where('session_id', $this->getSessionId())
            ->latest('visited_at')
            ->first();

        if ($latestVisit) {
            $checkoutAnalytics = $latestVisit->checkout_analytics ?? [];
            $checkoutAnalytics = array_merge($checkoutAnalytics, [
                'abandoned' => true,
                'abandoned_at_step' => $abandonedAtStep,
                'abandonment_timestamp' => now()->toISOString(),
                'abandonment_data' => $this->sanitizeFormData($abandonmentData),
                'time_to_abandonment' => $this->calculateTimeToAbandonment($latestVisit),
            ]);

            $latestVisit->update([
                'checkout_analytics' => $checkoutAnalytics,
                'checkout_abandoned' => true,
            ]);
        }
    }

    /**
     * Track checkout performance metrics.
     */
    public function trackCheckoutPerformance(
        string $metric,
        $value,
        array $context = []
    ): void {
        $visitorId = $this->getOrCreateVisitorId();

        $latestVisit = VisitorAnalytic::where('visitor_id', $visitorId)
            ->where('session_id', $this->getSessionId())
            ->latest('visited_at')
            ->first();

        if ($latestVisit) {
            $performanceMetrics = $latestVisit->checkout_performance ?? [];
            $performanceMetrics[$metric] = [
                'value' => $value,
                'timestamp' => now()->toISOString(),
                'context' => $context,
            ];

            $latestVisit->update([
                'checkout_performance' => $performanceMetrics,
            ]);
        }
    }

    /**
     * Update lead score based on new activity.
     */
    public function updateLeadScore(string $activity, array $activityData = []): void
    {
        $visitorId = $this->getOrCreateVisitorId();

        $latestVisit = VisitorAnalytic::where('visitor_id', $visitorId)
            ->where('session_id', $this->getSessionId())
            ->latest('visited_at')
            ->first();

        if ($latestVisit) {
            $currentScore = $latestVisit->lead_score ?? 0;
            $scoreIncrease = $this->getScoreIncrease($activity, $activityData);
            $newScore = min(100, $currentScore + $scoreIncrease);

            $latestVisit->update([
                'lead_score' => $newScore,
                'lead_status' => $this->determineLeadStatus($newScore),
            ]);
        }
    }

    /**
     * Get score increase for activity.
     */
    private function getScoreIncrease(string $activity, array $data): int
    {
        return match($activity) {
            'form_view' => 5,
            'form_start' => 10,
            'form_submit_success' => 25,
            'newsletter_signup' => 15,
            'contact_form' => 30,
            'project_application' => 35,
            'job_application' => 20,
            'cart_addition' => 20,
            'checkout_start' => 25,
            'checkout_initiated' => 25,
            'checkout_cart_review' => 15,
            'checkout_form_validation' => 20,
            'checkout_billing_processed' => 25,
            'checkout_payment_initiated' => 30,
            'checkout_payment_completed' => 35,
            'checkout_completed' => 40,
            'purchase' => 40,
            'page_scroll_deep' => 5,
            'time_on_page_long' => 8,
            'multiple_pages' => 10,
            default => 0
        };
    }

    /**
     * Calculate checkout funnel progress percentage.
     */
    private function calculateCheckoutFunnelProgress(array $checkoutFunnel): float
    {
        if (empty($checkoutFunnel)) {
            return 0.0;
        }

        $totalSteps = [
            'initiated',
            'cart_review',
            'form_validation',
            'billing_processed',
            'payment_initiated',
            'payment_completed',
            'completed'
        ];

        $completedSteps = array_filter($checkoutFunnel, fn($step) => $step['successful']);
        $uniqueCompletedSteps = array_unique(array_column($completedSteps, 'step'));

        return (count($uniqueCompletedSteps) / count($totalSteps)) * 100;
    }

    /**
     * Get checkout activity type for lead scoring.
     */
    private function getCheckoutActivityType(string $step): string
    {
        return match($step) {
            'initiated' => 'checkout_initiated',
            'cart_review' => 'checkout_cart_review',
            'form_validation' => 'checkout_form_validation',
            'billing_processed' => 'checkout_billing_processed',
            'payment_initiated' => 'checkout_payment_initiated',
            'payment_completed' => 'checkout_payment_completed',
            'completed' => 'checkout_completed',
            default => 'checkout_step'
        };
    }

    /**
     * Calculate time to abandonment in minutes.
     */
    private function calculateTimeToAbandonment(VisitorAnalytic $visit): ?int
    {
        $checkoutFunnel = $visit->checkout_funnel ?? [];
        if (empty($checkoutFunnel)) {
            return null;
        }

        $firstStep = reset($checkoutFunnel);
        $firstStepTime = \Carbon\Carbon::parse($firstStep['timestamp']);

        return $firstStepTime->diffInMinutes(now());
    }

    /**
     * Get or create visitor ID.
     */
    protected function getOrCreateVisitorId(): string
    {
        $sessionKey = 'visitor_analytics_id';

        // Check if session is available and has the visitor ID
        if ($this->hasSession()) {
            try {
                if (session()->has($sessionKey)) {
                    return session($sessionKey);
                }
            } catch (\Exception $e) {
                // Session error, continue without session
            }
        }

        $visitorId = Str::uuid();

        // Only store in session if session is available
        if ($this->hasSession()) {
            try {
                session([$sessionKey => $visitorId]);
            } catch (\Exception $e) {
                // Session error, continue without storing
            }
        }

        return $visitorId;
    }

    /**
     * Check if session is available.
     */
    protected function hasSession(): bool
    {
        try {
            // Check if request has session and if session is properly configured
            if (!$this->request->hasSession()) {
                return false;
            }

            // Try to access session to see if it's properly configured
            $session = session();
            return $session && $session->isStarted();
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get session ID safely.
     */
    protected function getSessionId(string $fallbackPrefix = 'api_session'): string
    {
        if ($this->hasSession()) {
            return session()->getId();
        }

        // For API requests, create a consistent session ID based on visitor ID
        $visitorId = $this->getOrCreateVisitorId();
        return $fallbackPrefix . '_' . $visitorId;
    }

    /**
     * Generate browser fingerprint.
     */
    protected function generateFingerprint(): string
    {
        $components = [
            $this->request->userAgent(),
            $this->request->header('Accept-Language'),
            $this->request->header('Accept-Encoding'),
            $this->request->ip(),
        ];

        return hash('sha256', implode('|', array_filter($components)));
    }

    /**
     * Check if visitor is returning.
     */
    protected function isReturningVisitor(string $visitorId, string $fingerprint): bool
    {
        return VisitorAnalytic::where(function ($query) use ($visitorId, $fingerprint) {
            $query->where('visitor_id', $visitorId)
                  ->orWhere('fingerprint', $fingerprint);
        })->exists();
    }

    /**
     * Get device information.
     */
    protected function getDeviceInfo(): array
    {
        return [
            'device_type' => $this->agent->isMobile() ? 'mobile' : ($this->agent->isTablet() ? 'tablet' : 'desktop'),
            'device_model' => $this->agent->device(),
            'browser' => $this->agent->browser(),
            'browser_version' => $this->agent->version($this->agent->browser()),
            'platform' => $this->agent->platform(),
            'platform_version' => $this->agent->version($this->agent->platform()),
            'language' => $this->request->header('Accept-Language'),
        ];
    }

    /**
     * Get location information from IP.
     */
    protected function getLocationInfo(): array
    {
        $ip = $this->request->ip();

        // Skip location lookup for local IPs
        if ($this->isLocalIp($ip)) {
            return [
                'country' => 'Local',
                'country_code' => 'LC',
                'region' => 'Local',
                'city' => 'Local',
                'latitude' => null,
                'longitude' => null,
                'isp' => 'Local Network',
            ];
        }

        // Use circuit breaker for external API calls
        try {
            $circuitBreaker = new \App\Services\CircuitBreakerService('ip_geolocation', 3, 300);

            return $circuitBreaker->call(
                function () use ($ip) {
                    $response = \Illuminate\Support\Facades\Http::timeout(5)->get("http://ip-api.com/json/{$ip}?fields=status,message,country,countryCode,region,regionName,city,lat,lon,isp");

                    if ($response->successful()) {
                        $data = $response->json();

                        if ($data['status'] === 'success') {
                            return [
                                'country' => $data['country'] ?? null,
                                'country_code' => $data['countryCode'] ?? null,
                                'region' => $data['regionName'] ?? null,
                                'city' => $data['city'] ?? null,
                                'latitude' => $data['lat'] ?? null,
                                'longitude' => $data['lon'] ?? null,
                                'isp' => $data['isp'] ?? null,
                            ];
                        }
                    }

                    throw new \Exception('Geolocation API returned unsuccessful response');
                },
                function (\Exception $e = null) {
                    // Fallback when circuit breaker is open or service fails
                    \Log::warning('Visitor Analytics: Geolocation service unavailable, using fallback', [
                        'ip' => $ip,
                        'error' => $e?->getMessage(),
                    ]);

                    return [
                        'country' => 'Unknown',
                        'country_code' => 'UN',
                        'region' => 'Unknown',
                        'city' => 'Unknown',
                        'latitude' => null,
                        'longitude' => null,
                        'isp' => 'Unknown',
                    ];
                }
            );
        } catch (\Exception $e) {
            // Final fallback if everything fails
            \Log::error('Visitor Analytics: Complete geolocation failure', [
                'ip' => $ip,
                'error' => $e->getMessage()
            ]);

            return [
                'country' => null,
                'country_code' => null,
                'region' => null,
                'city' => null,
                'latitude' => null,
                'longitude' => null,
                'isp' => null,
            ];
        }
    }

    /**
     * Get UTM parameters.
     */
    protected function getUtmData(): array
    {
        return [
            'utm_source' => $this->request->get('utm_source'),
            'utm_medium' => $this->request->get('utm_medium'),
            'utm_campaign' => $this->request->get('utm_campaign'),
        ];
    }

    /**
     * Get performance data.
     */
    protected function getPerformanceData(): array
    {
        // This would be populated by frontend JavaScript
        return [
            'response_time_ms' => null,
            'dom_load_time' => null,
            'page_load_time' => null,
            'time_to_first_byte' => null,
        ];
    }

    /**
     * Calculate risk score.
     */
    protected function calculateRiskScore(array $data): int
    {
        $score = 0;

        // Bot detection
        if ($data['is_bot']) {
            $score += 30;
        }

        // Suspicious user agent
        if (empty($data['user_agent']) || strlen($data['user_agent']) < 10) {
            $score += 20;
        }

        // Multiple rapid requests from same IP
        $recentRequests = VisitorAnalytic::where('ip_address', $data['ip_address'])
            ->where('visited_at', '>=', now()->subMinutes(5))
            ->count();

        if ($recentRequests > 50) {
            $score += 40;
        } elseif ($recentRequests > 20) {
            $score += 20;
        }

        return min($score, 100);
    }

    /**
     * Get first visit timestamp.
     */
    protected function getFirstVisit(string $visitorId): ?\Carbon\Carbon
    {
        $firstVisit = VisitorAnalytic::where('visitor_id', $visitorId)
            ->orderBy('visited_at')
            ->first();

        return $firstVisit?->visited_at;
    }

    /**
     * Get last visit timestamp.
     */
    protected function getLastVisit(string $visitorId): ?\Carbon\Carbon
    {
        $lastVisit = VisitorAnalytic::where('visitor_id', $visitorId)
            ->where('visited_at', '<', now())
            ->orderBy('visited_at', 'desc')
            ->first();

        return $lastVisit?->visited_at;
    }

    /**
     * Get visit count for visitor.
     */
    protected function getVisitCount(string $visitorId): int
    {
        return VisitorAnalytic::where('visitor_id', $visitorId)->count();
    }

    /**
     * Sanitize form data for storage.
     */
    protected function sanitizeFormData(array $data): array
    {
        $sensitiveFields = ['password', 'password_confirmation', 'token', 'credit_card', 'ssn'];
        
        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '[REDACTED]';
            }
        }

        return $data;
    }

    /**
     * Check if IP address is local/private.
     */
    protected function isLocalIp(string $ip): bool
    {
        // Check for localhost
        if (in_array($ip, ['127.0.0.1', '::1', 'localhost'])) {
            return true;
        }

        // Check for private IP ranges
        return !filter_var(
            $ip,
            FILTER_VALIDATE_IP,
            FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE
        );
    }
}
