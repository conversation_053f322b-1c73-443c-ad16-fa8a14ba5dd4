<?php

namespace App\Http\Controllers;

use App\Models\EmailCampaignSend;
use App\Models\NewsletterSubscription;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class EmailTrackingController extends Controller
{
    /**
     * Track email open via tracking pixel.
     */
    public function trackOpen(Request $request, string $token): Response
    {
        try {
            $send = EmailCampaignSend::where('tracking_token', $token)->first();

            if ($send) {
                // Track the open
                $send->trackOpen(
                    $request->userAgent(),
                    $request->ip()
                );

                // Update campaign statistics
                $this->updateCampaignStatistics($send->emailCampaign);

                // Update subscriber engagement
                $this->updateSubscriberEngagement($send->newsletterSubscription, 'opened');

                Log::info('Email open tracked', [
                    'campaign_id' => $send->email_campaign_id,
                    'subscriber_id' => $send->newsletter_subscription_id,
                    'token' => $token,
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Email open tracking failed', [
                'token' => $token,
                'error' => $e->getMessage(),
            ]);
        }

        // Return a 1x1 transparent pixel
        $pixel = base64_decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7');

        return response($pixel)
            ->header('Content-Type', 'image/gif')
            ->header('Content-Length', strlen($pixel))
            ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
            ->header('Pragma', 'no-cache')
            ->header('Expires', '0');
    }

    /**
     * Track email link clicks.
     */
    public function trackClick(Request $request, string $token): \Illuminate\Http\RedirectResponse
    {
        $url = $request->get('url');

        if (!$url) {
            abort(404);
        }

        try {
            $send = EmailCampaignSend::where('tracking_token', $token)->first();

            if ($send) {
                // Track the click
                $send->trackClick(
                    $url,
                    $request->userAgent(),
                    $request->ip()
                );

                // Update campaign statistics
                $this->updateCampaignStatistics($send->emailCampaign);

                // Update subscriber engagement
                $this->updateSubscriberEngagement($send->newsletterSubscription, 'clicked');

                Log::info('Email click tracked', [
                    'campaign_id' => $send->email_campaign_id,
                    'subscriber_id' => $send->newsletter_subscription_id,
                    'token' => $token,
                    'url' => $url,
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Email click tracking failed', [
                'token' => $token,
                'url' => $url,
                'error' => $e->getMessage(),
            ]);
        }

        // Redirect to the original URL
        return redirect($url);
    }

    /**
     * Handle email unsubscribe.
     */
    public function unsubscribe(Request $request, string $token): \Illuminate\View\View|\Illuminate\Http\RedirectResponse
    {
        $send = EmailCampaignSend::where('unsubscribe_token', $token)->first();

        if (!$send) {
            abort(404, 'Invalid unsubscribe link.');
        }

        $subscription = $send->newsletterSubscription;

        if ($request->isMethod('post')) {
            // Process unsubscribe
            $subscription->unsubscribe(
                $request->ip(),
                $request->userAgent()
            );

            // Track unsubscribe in campaign send
            $send->trackUnsubscribe();

            // Update campaign statistics
            $this->updateCampaignStatistics($send->emailCampaign);

            Log::info('Email unsubscribe processed', [
                'campaign_id' => $send->email_campaign_id,
                'subscriber_id' => $send->newsletter_subscription_id,
                'token' => $token,
                'ip' => $request->ip(),
            ]);

            return redirect()->route('unsubscribe.success');
        }

        // Show unsubscribe confirmation page
        return view('emails.unsubscribe', compact('subscription', 'token'));
    }

    /**
     * Show unsubscribe success page.
     */
    public function unsubscribeSuccess(): \Illuminate\View\View
    {
        return view('emails.unsubscribe-success');
    }

    /**
     * Update campaign statistics.
     */
    private function updateCampaignStatistics($campaign): void
    {
        if (!$campaign) {
            return;
        }

        $statistics = $campaign->sends()
            ->selectRaw('
                COUNT(*) as total_sent,
                SUM(CASE WHEN delivered_at IS NOT NULL THEN 1 ELSE 0 END) as delivered,
                SUM(CASE WHEN bounced_at IS NOT NULL THEN 1 ELSE 0 END) as bounced,
                SUM(CASE WHEN opened_at IS NOT NULL THEN 1 ELSE 0 END) as opened,
                SUM(CASE WHEN first_clicked_at IS NOT NULL THEN 1 ELSE 0 END) as clicked,
                SUM(CASE WHEN unsubscribed_at IS NOT NULL THEN 1 ELSE 0 END) as unsubscribed
            ')
            ->first();

        $campaign->update([
            'emails_sent' => $statistics->total_sent,
            'emails_delivered' => $statistics->delivered,
            'emails_bounced' => $statistics->bounced,
            'emails_opened' => $statistics->opened,
            'emails_clicked' => $statistics->clicked,
            'unsubscribes' => $statistics->unsubscribed,
        ]);
    }

    /**
     * Update subscriber engagement.
     */
    private function updateSubscriberEngagement($subscription, string $activity): void
    {
        if (!$subscription) {
            return;
        }

        $subscription->trackEmailActivity($activity);
    }
}
