<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Chat Performance Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for optimizing chat system
    | performance including caching, rate limiting, and resource management.
    |
    */

    'caching' => [
        /*
        |--------------------------------------------------------------------------
        | Cache Configuration
        |--------------------------------------------------------------------------
        |
        | Configure caching strategies for different chat components.
        |
        */
        'enabled' => env('CHAT_CACHE_ENABLED', true),
        'default_ttl' => env('CHAT_CACHE_TTL', 3600), // 1 hour
        
        'strategies' => [
            'analytics' => [
                'ttl' => env('CHAT_ANALYTICS_CACHE_TTL', 1800), // 30 minutes
                'tags' => ['chat', 'analytics'],
            ],
            'ai_responses' => [
                'ttl' => env('CHAT_AI_CACHE_TTL', 7200), // 2 hours
                'tags' => ['chat', 'ai'],
            ],
            'user_sessions' => [
                'ttl' => env('CHAT_SESSION_CACHE_TTL', 900), // 15 minutes
                'tags' => ['chat', 'sessions'],
            ],
            'staff_assignments' => [
                'ttl' => env('CHAT_ASSIGNMENTS_CACHE_TTL', 600), // 10 minutes
                'tags' => ['chat', 'assignments'],
            ],
        ],
    ],

    'rate_limiting' => [
        /*
        |--------------------------------------------------------------------------
        | Rate Limiting Configuration
        |--------------------------------------------------------------------------
        |
        | Configure rate limits for different chat operations to prevent abuse.
        |
        */
        'enabled' => env('CHAT_RATE_LIMITING_ENABLED', true),
        
        'limits' => [
            'message_sending' => [
                'max_attempts' => env('CHAT_MESSAGE_RATE_LIMIT', 60),
                'decay_minutes' => env('CHAT_MESSAGE_RATE_DECAY', 1),
            ],
            'room_creation' => [
                'max_attempts' => env('CHAT_ROOM_RATE_LIMIT', 5),
                'decay_minutes' => env('CHAT_ROOM_RATE_DECAY', 60),
            ],
            'ai_requests' => [
                'max_attempts' => env('CHAT_AI_RATE_LIMIT', 30),
                'decay_minutes' => env('CHAT_AI_RATE_DECAY', 1),
            ],
            'file_uploads' => [
                'max_attempts' => env('CHAT_FILE_RATE_LIMIT', 10),
                'decay_minutes' => env('CHAT_FILE_RATE_DECAY', 5),
            ],
        ],
    ],

    'database' => [
        /*
        |--------------------------------------------------------------------------
        | Database Optimization
        |--------------------------------------------------------------------------
        |
        | Configure database optimization settings for chat operations.
        |
        */
        'query_optimization' => [
            'enabled' => env('CHAT_QUERY_OPTIMIZATION', true),
            'eager_loading' => true,
            'chunk_size' => env('CHAT_QUERY_CHUNK_SIZE', 100),
        ],
        
        'indexing' => [
            'auto_index' => env('CHAT_AUTO_INDEX', true),
            'index_suggestions' => true,
        ],
        
        'cleanup' => [
            'enabled' => env('CHAT_CLEANUP_ENABLED', true),
            'old_messages_days' => env('CHAT_CLEANUP_MESSAGES_DAYS', 365),
            'old_sessions_days' => env('CHAT_CLEANUP_SESSIONS_DAYS', 30),
            'old_logs_days' => env('CHAT_CLEANUP_LOGS_DAYS', 90),
        ],
    ],

    'real_time' => [
        /*
        |--------------------------------------------------------------------------
        | Real-time Performance
        |--------------------------------------------------------------------------
        |
        | Configure real-time messaging performance settings.
        |
        */
        'broadcasting' => [
            'enabled' => env('CHAT_BROADCASTING_ENABLED', true),
            'driver' => env('CHAT_BROADCAST_DRIVER', 'pusher'),
            'batch_size' => env('CHAT_BROADCAST_BATCH_SIZE', 100),
            'retry_attempts' => env('CHAT_BROADCAST_RETRIES', 3),
        ],
        
        'websockets' => [
            'enabled' => env('CHAT_WEBSOCKETS_ENABLED', true),
            'max_connections' => env('CHAT_MAX_CONNECTIONS', 1000),
            'heartbeat_interval' => env('CHAT_HEARTBEAT_INTERVAL', 30),
        ],
    ],

    'ai_performance' => [
        /*
        |--------------------------------------------------------------------------
        | AI Performance Configuration
        |--------------------------------------------------------------------------
        |
        | Configure AI service performance and optimization settings.
        |
        */
        'circuit_breaker' => [
            'enabled' => env('CHAT_AI_CIRCUIT_BREAKER', true),
            'failure_threshold' => env('CHAT_AI_FAILURE_THRESHOLD', 5),
            'recovery_timeout' => env('CHAT_AI_RECOVERY_TIMEOUT', 60),
            'expected_exception_types' => [
                'OpenAI\Exceptions\ErrorException',
                'GuzzleHttp\Exception\RequestException',
            ],
        ],
        
        'response_optimization' => [
            'max_response_time' => env('CHAT_AI_MAX_RESPONSE_TIME', 10), // seconds
            'streaming_enabled' => env('CHAT_AI_STREAMING', false),
            'parallel_processing' => env('CHAT_AI_PARALLEL', false),
        ],
        
        'model_optimization' => [
            'default_model' => env('CHAT_AI_DEFAULT_MODEL', 'gpt-3.5-turbo'),
            'fallback_model' => env('CHAT_AI_FALLBACK_MODEL', 'gpt-3.5-turbo'),
            'max_tokens' => env('CHAT_AI_MAX_TOKENS', 150),
            'temperature' => env('CHAT_AI_TEMPERATURE', 0.7),
        ],
    ],

    'monitoring' => [
        /*
        |--------------------------------------------------------------------------
        | Performance Monitoring
        |--------------------------------------------------------------------------
        |
        | Configure performance monitoring and alerting.
        |
        */
        'enabled' => env('CHAT_MONITORING_ENABLED', true),
        
        'metrics' => [
            'response_time_threshold' => env('CHAT_RESPONSE_TIME_THRESHOLD', 2.0), // seconds
            'memory_usage_threshold' => env('CHAT_MEMORY_THRESHOLD', 128), // MB
            'cpu_usage_threshold' => env('CHAT_CPU_THRESHOLD', 80), // percentage
        ],
        
        'alerts' => [
            'enabled' => env('CHAT_ALERTS_ENABLED', true),
            'email_notifications' => env('CHAT_ALERT_EMAIL', true),
            'slack_notifications' => env('CHAT_ALERT_SLACK', false),
        ],
        
        'logging' => [
            'performance_logs' => env('CHAT_PERFORMANCE_LOGS', true),
            'slow_query_threshold' => env('CHAT_SLOW_QUERY_THRESHOLD', 1.0), // seconds
            'log_level' => env('CHAT_LOG_LEVEL', 'info'),
        ],
    ],

    'optimization' => [
        /*
        |--------------------------------------------------------------------------
        | General Optimization Settings
        |--------------------------------------------------------------------------
        |
        | General optimization settings for the chat system.
        |
        */
        'lazy_loading' => [
            'enabled' => env('CHAT_LAZY_LOADING', true),
            'message_pagination' => env('CHAT_MESSAGE_PAGINATION', 50),
            'user_pagination' => env('CHAT_USER_PAGINATION', 20),
        ],
        
        'compression' => [
            'enabled' => env('CHAT_COMPRESSION', true),
            'gzip_level' => env('CHAT_GZIP_LEVEL', 6),
            'minify_responses' => env('CHAT_MINIFY', true),
        ],
        
        'cdn' => [
            'enabled' => env('CHAT_CDN_ENABLED', false),
            'url' => env('CHAT_CDN_URL', ''),
            'static_assets' => env('CHAT_CDN_STATIC', true),
        ],
    ],

    'load_balancing' => [
        /*
        |--------------------------------------------------------------------------
        | Load Balancing Configuration
        |--------------------------------------------------------------------------
        |
        | Configure load balancing for high-traffic scenarios.
        |
        */
        'enabled' => env('CHAT_LOAD_BALANCING', false),
        'strategy' => env('CHAT_LB_STRATEGY', 'round_robin'), // round_robin, least_connections, ip_hash
        'health_checks' => env('CHAT_HEALTH_CHECKS', true),
        'failover' => env('CHAT_FAILOVER', true),
    ],

    'security_performance' => [
        /*
        |--------------------------------------------------------------------------
        | Security Performance Balance
        |--------------------------------------------------------------------------
        |
        | Balance security measures with performance requirements.
        |
        */
        'encryption' => [
            'message_encryption' => env('CHAT_MESSAGE_ENCRYPTION', false),
            'file_encryption' => env('CHAT_FILE_ENCRYPTION', false),
            'encryption_algorithm' => env('CHAT_ENCRYPTION_ALGO', 'AES-256-CBC'),
        ],
        
        'validation' => [
            'strict_validation' => env('CHAT_STRICT_VALIDATION', true),
            'sanitization_level' => env('CHAT_SANITIZATION_LEVEL', 'medium'), // low, medium, high
            'xss_protection' => env('CHAT_XSS_PROTECTION', true),
        ],
    ],
];
