# 🗄️ ChiSolution Database Architecture
## Professional Database Design for Digital Agency & E-commerce Platform

### 📊 Database Overview

The ChiSolution database is architected with the following core modules:

1. **🔐 Authentication & User Management**
2. **🛍️ E-commerce & Product Catalog**
3. **📦 Order Management & Fulfillment**
4. **🎯 Project Management & Client Services**
5. **💼 Professional Accounting Services**
6. **📝 Content Management System (Blog & CMS)**
7. **🌍 Internationalization & Localization**
8. **📊 Analytics & Activity Tracking**
9. **📧 Email Marketing & Newsletter System**
10. **💼 Career Management & Job Applications**
11. **🔍 Visitor Analytics & Journey Tracking**
12. **⚙️ System Configuration**

---

## 🏗️ Core Database Modules

### 🔐 Module 1: Authentication & User Management

#### users
**Purpose:** Central user management with role-based access control
```sql
users
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL) -- Public identifier
├── first_name (VARCHAR(100) NOT NULL)
├── last_name (VARCHAR(100) NOT NULL)
├── email (VARCHAR(255) UNIQUE NOT NULL)
├── email_verified_at (TIMESTAMP NULL)
├── password (VARCHAR(255) NOT NULL)
├── phone (VARCHAR(20) NULL)
├── avatar (VARCHAR(500) NULL) -- File path or URL
├── role_id (BIGINT UNSIGNED NOT NULL FK→roles.id)
├── is_active (BOOLEAN DEFAULT TRUE)
├── is_deleted (BOOLEAN DEFAULT FALSE) -- Soft delete
├── last_login_at (TIMESTAMP NULL)
├── login_count (INT UNSIGNED DEFAULT 0)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_users_email_active (email, is_active, is_deleted)
INDEX idx_users_role_active (role_id, is_active)
INDEX idx_users_uuid (uuid)
INDEX idx_users_login (last_login_at)
```

#### roles
**Purpose:** Role-based permission system
```sql
roles
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── name (VARCHAR(50) UNIQUE NOT NULL) -- admin, staff, client, customer
├── slug (VARCHAR(50) UNIQUE NOT NULL)
├── display_name (VARCHAR(100) NOT NULL)
├── description (TEXT NULL)
├── permissions (JSON NOT NULL) -- Structured permissions
├── is_active (BOOLEAN DEFAULT TRUE)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_roles_name_active (name, is_active)
INDEX idx_roles_slug (slug)
```

#### user_addresses
**Purpose:** Multi-address management for billing/shipping
```sql
user_addresses
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── user_id (BIGINT UNSIGNED NOT NULL FK→users.id CASCADE)
├── type (ENUM('billing', 'shipping', 'both') DEFAULT 'both')
├── label (VARCHAR(50) NULL) -- "Home", "Office", etc.
├── first_name (VARCHAR(100) NOT NULL)
├── last_name (VARCHAR(100) NOT NULL)
├── company (VARCHAR(200) NULL)
├── address_line_1 (VARCHAR(255) NOT NULL)
├── address_line_2 (VARCHAR(255) NULL)
├── city (VARCHAR(100) NOT NULL)
├── state_province (VARCHAR(100) NOT NULL)
├── postal_code (VARCHAR(20) NOT NULL)
├── country_code (CHAR(2) NOT NULL) -- ISO 3166-1 alpha-2
├── phone (VARCHAR(20) NULL)
├── is_default (BOOLEAN DEFAULT FALSE)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_user_addresses_user_type (user_id, type)
INDEX idx_user_addresses_default (user_id, is_default)
INDEX idx_user_addresses_country (country_code)
```

---

### 🌍 Module 2: Internationalization & Localization

#### languages
**Purpose:** Multi-language support system
```sql
languages
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── code (VARCHAR(5) UNIQUE NOT NULL) -- en, fr, es, en-US
├── name (VARCHAR(100) NOT NULL) -- English, French, Spanish
├── native_name (VARCHAR(100) NOT NULL) -- English, Français, Español
├── direction (ENUM('ltr', 'rtl') DEFAULT 'ltr')
├── is_active (BOOLEAN DEFAULT TRUE)
├── is_default (BOOLEAN DEFAULT FALSE)
├── sort_order (INT UNSIGNED DEFAULT 0)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_languages_code_active (code, is_active)
INDEX idx_languages_default (is_default)
INDEX idx_languages_sort (sort_order)
```

#### currencies
**Purpose:** Multi-currency support with exchange rates
```sql
currencies
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── code (CHAR(3) UNIQUE NOT NULL) -- ZAR, USD, EUR, GBP, RWF
├── name (VARCHAR(100) NOT NULL) -- South African Rand
├── symbol (VARCHAR(10) NOT NULL) -- R, $, €, £
├── symbol_position (ENUM('before', 'after') DEFAULT 'before')
├── decimal_places (TINYINT UNSIGNED DEFAULT 2)
├── exchange_rate (DECIMAL(15,6) DEFAULT 1.000000) -- Base currency rate
├── is_default (BOOLEAN DEFAULT FALSE)
├── is_active (BOOLEAN DEFAULT TRUE)
├── last_updated (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_currencies_code_active (code, is_active)
INDEX idx_currencies_default (is_default)
INDEX idx_currencies_rate_updated (last_updated)
```

#### translations
**Purpose:** Polymorphic translation system for all content
```sql
translations
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── translatable_type (VARCHAR(100) NOT NULL) -- Model class name
├── translatable_id (BIGINT UNSIGNED NOT NULL) -- Model ID
├── language_id (BIGINT UNSIGNED NOT NULL FK→languages.id CASCADE)
├── field_name (VARCHAR(100) NOT NULL) -- Field being translated
├── field_value (LONGTEXT NOT NULL) -- Translated content
├── is_approved (BOOLEAN DEFAULT TRUE)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_translations_polymorphic (translatable_type, translatable_id)
INDEX idx_translations_language_field (language_id, field_name)
UNIQUE KEY uk_translations_unique (translatable_type, translatable_id, language_id, field_name)
```

---

### 🛍️ Module 3: E-commerce & Product Catalog

#### product_categories
**Purpose:** Hierarchical product categorization system
```sql
product_categories
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── name (VARCHAR(200) NOT NULL)
├── slug (VARCHAR(255) UNIQUE NOT NULL)
├── description (TEXT NULL)
├── image (VARCHAR(500) NULL)
├── parent_id (BIGINT UNSIGNED NULL FK→product_categories.id CASCADE)
├── level (TINYINT UNSIGNED DEFAULT 0) -- Denormalized depth
├── path (VARCHAR(1000) NULL) -- Materialized path: /1/5/12/
├── meta_title (VARCHAR(255) NULL)
├── meta_description (TEXT NULL)
├── meta_keywords (TEXT NULL)
├── is_active (BOOLEAN DEFAULT TRUE)
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── sort_order (INT UNSIGNED DEFAULT 0)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_categories_slug_active (slug, is_active, is_deleted)
INDEX idx_categories_parent_sort (parent_id, sort_order)
INDEX idx_categories_path (path)
INDEX idx_categories_level (level)
INDEX idx_categories_uuid (uuid)
```

#### products
**Purpose:** Core product catalog with comprehensive attributes
```sql
products
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── name (VARCHAR(300) NOT NULL)
├── slug (VARCHAR(400) UNIQUE NOT NULL)
├── short_description (TEXT NULL)
├── description (LONGTEXT NULL)
├── sku (VARCHAR(100) UNIQUE NOT NULL)
├── barcode (VARCHAR(100) NULL)
├── brand (VARCHAR(100) NULL)
├── model (VARCHAR(100) NULL)
├── price (DECIMAL(12,2) NOT NULL)
├── compare_price (DECIMAL(12,2) NULL) -- Original/MSRP price
├── cost_price (DECIMAL(12,2) NULL) -- Internal cost
├── profit_margin (DECIMAL(5,2) NULL) -- Calculated margin %
├── track_inventory (BOOLEAN DEFAULT TRUE)
├── inventory_quantity (INT DEFAULT 0)
├── low_stock_threshold (INT DEFAULT 5)
├── weight (DECIMAL(8,3) NULL) -- In grams
├── dimensions (JSON NULL) -- {length, width, height, unit}
├── featured_image (VARCHAR(500) NULL)
├── gallery (JSON NULL) -- Array of image URLs
├── attributes (JSON NULL) -- Custom product attributes
├── is_active (BOOLEAN DEFAULT TRUE)
├── is_featured (BOOLEAN DEFAULT FALSE)
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── meta_title (VARCHAR(255) NULL)
├── meta_description (TEXT NULL)
├── meta_keywords (TEXT NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_products_slug_active (slug, is_active, is_deleted)
INDEX idx_products_sku_active (sku, is_active)
INDEX idx_products_featured_active (is_featured, is_active)
INDEX idx_products_price_range (price, is_active)
INDEX idx_products_inventory (inventory_quantity, track_inventory)
INDEX idx_products_uuid (uuid)
FULLTEXT idx_products_search (name, description, sku, brand, model)
```

#### product_category_relations
**Purpose:** Many-to-many relationship between products and categories
```sql
product_category_relations
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── product_id (BIGINT UNSIGNED NOT NULL FK→products.id CASCADE)
├── category_id (BIGINT UNSIGNED NOT NULL FK→product_categories.id CASCADE)
├── is_primary (BOOLEAN DEFAULT FALSE) -- Primary category for product
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
UNIQUE KEY uk_product_category (product_id, category_id)
INDEX idx_product_category_product (product_id)
INDEX idx_product_category_category (category_id)
INDEX idx_product_category_primary (product_id, is_primary)
```

#### product_variants
**Purpose:** Product variations (size, color, etc.)
```sql
product_variants
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── product_id (BIGINT UNSIGNED NOT NULL FK→products.id CASCADE)
├── name (VARCHAR(200) NOT NULL)
├── sku (VARCHAR(100) UNIQUE NOT NULL)
├── barcode (VARCHAR(100) NULL)
├── price (DECIMAL(12,2) NULL) -- Override product price
├── compare_price (DECIMAL(12,2) NULL)
├── cost_price (DECIMAL(12,2) NULL)
├── inventory_quantity (INT DEFAULT 0)
├── weight (DECIMAL(8,3) NULL)
├── dimensions (JSON NULL)
├── image (VARCHAR(500) NULL) -- Variant-specific image
├── attributes (JSON NOT NULL) -- {color: "red", size: "XL"}
├── is_active (BOOLEAN DEFAULT TRUE)
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_variants_product_active (product_id, is_active, is_deleted)
INDEX idx_variants_sku (sku)
INDEX idx_variants_inventory (inventory_quantity)
```

---

### 📦 Module 4: Shopping Cart & Order Management

#### shopping_carts
**Purpose:** Persistent shopping cart for users and sessions
```sql
shopping_carts
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── user_id (BIGINT UNSIGNED NULL FK→users.id CASCADE)
├── session_id (VARCHAR(255) NULL) -- For guest users
├── currency_code (CHAR(3) NOT NULL DEFAULT 'ZAR')
├── subtotal (DECIMAL(12,2) DEFAULT 0.00)
├── tax_amount (DECIMAL(12,2) DEFAULT 0.00)
├── shipping_amount (DECIMAL(12,2) DEFAULT 0.00)
├── discount_amount (DECIMAL(12,2) DEFAULT 0.00)
├── total (DECIMAL(12,2) DEFAULT 0.00)
├── expires_at (TIMESTAMP NULL) -- Cart expiration
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_carts_user (user_id)
INDEX idx_carts_session (session_id)
INDEX idx_carts_expires (expires_at)
INDEX idx_carts_uuid (uuid)
```
#### cart_items
**Purpose:** Items in shopping carts
```sql
cart_items
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── shopping_cart_id (BIGINT UNSIGNED NOT NULL FK→shopping_carts.id CASCADE)
├── product_id (BIGINT UNSIGNED NOT NULL FK→products.id CASCADE)
├── variant_id (BIGINT UNSIGNED NULL FK→product_variants.id CASCADE)
├── quantity (INT UNSIGNED NOT NULL DEFAULT 1)
├── unit_price (DECIMAL(12,2) NOT NULL) -- Price at time of adding
├── total_price (DECIMAL(12,2) NOT NULL) -- quantity * unit_price
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_cart_items_cart (shopping_cart_id)
INDEX idx_cart_items_product (product_id)
INDEX idx_cart_items_variant (variant_id)
UNIQUE KEY uk_cart_item_unique (shopping_cart_id, product_id, variant_id)
```

#### orders
**Purpose:** Customer orders with comprehensive tracking
```sql
orders
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── order_number (VARCHAR(50) UNIQUE NOT NULL) -- Human-readable order number
├── user_id (BIGINT UNSIGNED NULL FK→users.id SET NULL)
├── customer_email (VARCHAR(255) NOT NULL)
├── customer_first_name (VARCHAR(100) NOT NULL)
├── customer_last_name (VARCHAR(100) NOT NULL)
├── customer_phone (VARCHAR(20) NULL)
├── customer_company (VARCHAR(200) NULL)
├── status (ENUM('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded') DEFAULT 'pending')
├── payment_status (ENUM('pending', 'paid', 'failed', 'refunded', 'partially_refunded') DEFAULT 'pending')
├── payment_method (VARCHAR(50) NULL) -- stripe, paypal, bank_transfer, etc.
├── payment_reference (VARCHAR(255) NULL) -- External payment ID
├── currency_code (CHAR(3) NOT NULL DEFAULT 'ZAR')
├── exchange_rate (DECIMAL(15,6) DEFAULT 1.000000) -- Rate at time of order
├── subtotal (DECIMAL(12,2) NOT NULL)
├── tax_amount (DECIMAL(12,2) DEFAULT 0.00)
├── shipping_amount (DECIMAL(12,2) DEFAULT 0.00)
├── discount_amount (DECIMAL(12,2) DEFAULT 0.00)
├── total (DECIMAL(12,2) NOT NULL)
├── billing_address (JSON NOT NULL) -- Complete billing address
├── shipping_address (JSON NOT NULL) -- Complete shipping address
├── notes (TEXT NULL) -- Customer notes
├── admin_notes (TEXT NULL) -- Internal notes
├── shipped_at (TIMESTAMP NULL)
├── delivered_at (TIMESTAMP NULL)
├── paid_at (TIMESTAMP NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_orders_number (order_number)
INDEX idx_orders_user_status (user_id, status)
INDEX idx_orders_email_status (customer_email, status)
INDEX idx_orders_payment_status (payment_status)
INDEX idx_orders_dates (created_at, status)
INDEX idx_orders_uuid (uuid)
```

#### order_items
**Purpose:** Line items for orders with product snapshots
```sql
order_items
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── order_id (BIGINT UNSIGNED NOT NULL FK→orders.id CASCADE)
├── product_id (BIGINT UNSIGNED NOT NULL FK→products.id RESTRICT)
├── variant_id (BIGINT UNSIGNED NULL FK→product_variants.id RESTRICT)
├── quantity (INT UNSIGNED NOT NULL)
├── unit_price (DECIMAL(12,2) NOT NULL) -- Price at time of order
├── total_price (DECIMAL(12,2) NOT NULL) -- quantity * unit_price
├── product_snapshot (JSON NOT NULL) -- Product details at time of order
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_order_items_order (order_id)
INDEX idx_order_items_product (product_id)
INDEX idx_order_items_variant (variant_id)
```

---

### 🎯 Module 5: Project Management & Client Services

#### services
**Purpose:** Digital agency service offerings
```sql
services
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── name (VARCHAR(200) NOT NULL)
├── slug (VARCHAR(255) UNIQUE NOT NULL)
├── short_description (TEXT NULL)
├── description (LONGTEXT NULL)
├── icon (VARCHAR(500) NULL) -- Icon file path or class
├── featured_image (VARCHAR(500) NULL)
├── gallery (JSON NULL) -- Array of image URLs
├── price_from (DECIMAL(12,2) NULL) -- Starting price
├── features (JSON NULL) -- Service features list
├── is_featured (BOOLEAN DEFAULT FALSE)
├── is_active (BOOLEAN DEFAULT TRUE)
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── sort_order (INT UNSIGNED DEFAULT 0)
├── meta_title (VARCHAR(255) NULL)
├── meta_description (TEXT NULL)
├── meta_keywords (TEXT NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_services_slug_active (slug, is_active, is_deleted)
INDEX idx_services_featured_active (is_featured, is_active)
INDEX idx_services_sort (sort_order)
INDEX idx_services_uuid (uuid)
```

#### projects
**Purpose:** Client project portfolio and management
```sql
projects
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── title (VARCHAR(300) NOT NULL)
├── slug (VARCHAR(400) UNIQUE NOT NULL)
├── description (TEXT NULL)
├── content (LONGTEXT NULL) -- Full project description
├── client_name (VARCHAR(200) NULL) -- Display name
├── client_id (BIGINT UNSIGNED NULL FK→users.id SET NULL)
├── service_id (BIGINT UNSIGNED NULL FK→services.id SET NULL)
├── featured_image (VARCHAR(500) NULL)
├── gallery (JSON NULL) -- Project images
├── project_url (VARCHAR(500) NULL) -- Live project URL
├── start_date (DATE NULL)
├── end_date (DATE NULL)
├── estimated_hours (DECIMAL(8,2) NULL)
├── actual_hours (DECIMAL(8,2) NULL)
├── hourly_rate (DECIMAL(8,2) NULL)
├── total_amount (DECIMAL(12,2) NULL)
├── currency_code (CHAR(3) DEFAULT 'ZAR')
├── status (ENUM('planning', 'in_progress', 'review', 'completed', 'on_hold', 'cancelled') DEFAULT 'planning')
├── priority (ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium')
├── is_featured (BOOLEAN DEFAULT FALSE)
├── is_published (BOOLEAN DEFAULT FALSE) -- Show in portfolio
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── meta_title (VARCHAR(255) NULL)
├── meta_description (TEXT NULL)
├── meta_keywords (TEXT NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_projects_slug_published (slug, is_published, is_deleted)
INDEX idx_projects_client_status (client_id, status)
INDEX idx_projects_service (service_id)
INDEX idx_projects_status_priority (status, priority)
INDEX idx_projects_featured_published (is_featured, is_published)
INDEX idx_projects_uuid (uuid)
```

#### accounting_clients
**Purpose:** Accounting service client management
```sql
accounting_clients
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── user_id (BIGINT UNSIGNED NULL FK→users.id SET NULL) -- Link to user account
├── company_name (VARCHAR(200) NOT NULL)
├── registration_number (VARCHAR(50) NULL) -- Company registration
├── tax_number (VARCHAR(50) NULL) -- Tax/VAT number
├── contact_person (VARCHAR(200) NULL)
├── email (VARCHAR(255) NOT NULL)
├── phone (VARCHAR(20) NULL)
├── address (TEXT NULL)
├── city (VARCHAR(100) NULL)
├── province (VARCHAR(100) NULL)
├── postal_code (VARCHAR(20) NULL)
├── country (VARCHAR(100) DEFAULT 'South Africa')
├── industry (VARCHAR(100) NULL)
├── business_type (ENUM('sole_proprietorship', 'partnership', 'company', 'trust', 'npo') NOT NULL)
├── employee_count (INT UNSIGNED NULL)
├── annual_turnover (DECIMAL(15,2) NULL)
├── accounting_software (VARCHAR(100) NULL) -- Current software used
├── service_package (ENUM('basic', 'professional', 'enterprise', 'custom') NOT NULL)
├── billing_cycle (ENUM('monthly', 'quarterly', 'annually', 'one_off') DEFAULT 'monthly')
├── monthly_fee (DECIMAL(10,2) NULL)
├── start_date (DATE NOT NULL)
├── end_date (DATE NULL)
├── status (ENUM('active', 'suspended', 'terminated', 'pending') DEFAULT 'pending')
├── assigned_accountant_id (BIGINT UNSIGNED NULL FK→users.id SET NULL)
├── notes (TEXT NULL) -- Internal notes
├── is_active (BOOLEAN DEFAULT TRUE)
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_accounting_clients_user (user_id)
INDEX idx_accounting_clients_status (status, is_active)
INDEX idx_accounting_clients_accountant (assigned_accountant_id)
INDEX idx_accounting_clients_package (service_package)
INDEX idx_accounting_clients_uuid (uuid)
```

#### accounting_services
**Purpose:** Specific accounting service offerings and pricing
```sql
accounting_services
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── name (VARCHAR(200) NOT NULL) -- e.g., "Bookkeeping", "Payroll", "Tax Filing"
├── slug (VARCHAR(255) UNIQUE NOT NULL)
├── category (ENUM('bookkeeping', 'payroll', 'tax', 'reporting', 'consulting', 'audit') NOT NULL)
├── description (TEXT NULL)
├── features (JSON NULL) -- Service features list
├── base_price (DECIMAL(10,2) NULL) -- Base monthly price
├── hourly_rate (DECIMAL(8,2) NULL) -- For hourly services
├── one_off_price (DECIMAL(10,2) NULL) -- One-time service price
├── billing_unit (ENUM('monthly', 'hourly', 'per_transaction', 'fixed') DEFAULT 'monthly')
├── is_addon (BOOLEAN DEFAULT FALSE) -- Can be added to packages
├── requires_package (VARCHAR(100) NULL) -- Required base package
├── is_active (BOOLEAN DEFAULT TRUE)
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── sort_order (INT UNSIGNED DEFAULT 0)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_accounting_services_category (category, is_active)
INDEX idx_accounting_services_slug (slug)
INDEX idx_accounting_services_addon (is_addon, is_active)
INDEX idx_accounting_services_uuid (uuid)
```

#### accounting_client_services
**Purpose:** Junction table for client-service relationships with custom pricing
```sql
accounting_client_services
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── client_id (BIGINT UNSIGNED NOT NULL FK→accounting_clients.id CASCADE)
├── service_id (BIGINT UNSIGNED NOT NULL FK→accounting_services.id CASCADE)
├── custom_price (DECIMAL(10,2) NULL) -- Override default pricing
├── billing_cycle (ENUM('monthly', 'quarterly', 'annually', 'one_off') DEFAULT 'monthly')
├── start_date (DATE NOT NULL)
├── end_date (DATE NULL)
├── status (ENUM('active', 'suspended', 'completed') DEFAULT 'active')
├── notes (TEXT NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_client_services_client (client_id, status)
INDEX idx_client_services_service (service_id)
UNIQUE KEY uk_client_service_active (client_id, service_id, status)
```

#### accounting_transactions
**Purpose:** Financial transaction records for accounting clients
```sql
accounting_transactions
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── client_id (BIGINT UNSIGNED NOT NULL FK→accounting_clients.id CASCADE)
├── transaction_date (DATE NOT NULL)
├── reference_number (VARCHAR(100) NULL)
├── description (TEXT NOT NULL)
├── account_code (VARCHAR(20) NULL) -- Chart of accounts code
├── account_name (VARCHAR(200) NULL)
├── debit_amount (DECIMAL(15,2) DEFAULT 0.00)
├── credit_amount (DECIMAL(15,2) DEFAULT 0.00)
├── balance (DECIMAL(15,2) DEFAULT 0.00) -- Running balance
├── transaction_type (ENUM('income', 'expense', 'asset', 'liability', 'equity') NOT NULL)
├── category (VARCHAR(100) NULL) -- Transaction category
├── tax_amount (DECIMAL(15,2) DEFAULT 0.00)
├── tax_rate (DECIMAL(5,2) DEFAULT 0.00)
├── is_reconciled (BOOLEAN DEFAULT FALSE)
├── reconciled_at (TIMESTAMP NULL)
├── reconciled_by (BIGINT UNSIGNED NULL FK→users.id SET NULL)
├── supporting_document (VARCHAR(500) NULL) -- File path
├── notes (TEXT NULL)
├── created_by (BIGINT UNSIGNED NOT NULL FK→users.id)
├── updated_by (BIGINT UNSIGNED NULL FK→users.id SET NULL)
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_accounting_transactions_client_date (client_id, transaction_date)
INDEX idx_accounting_transactions_type (transaction_type, client_id)
INDEX idx_accounting_transactions_reconciled (is_reconciled, client_id)
INDEX idx_accounting_transactions_created_by (created_by)
INDEX idx_accounting_transactions_uuid (uuid)
```

#### payroll_employees
**Purpose:** Employee records for payroll processing
```sql
payroll_employees
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── client_id (BIGINT UNSIGNED NOT NULL FK→accounting_clients.id CASCADE)
├── employee_number (VARCHAR(50) NOT NULL)
├── first_name (VARCHAR(100) NOT NULL)
├── last_name (VARCHAR(100) NOT NULL)
├── id_number (VARCHAR(20) UNIQUE NOT NULL) -- South African ID
├── email (VARCHAR(255) NULL)
├── phone (VARCHAR(20) NULL)
├── address (TEXT NULL)
├── position (VARCHAR(200) NULL)
├── department (VARCHAR(100) NULL)
├── employment_type (ENUM('permanent', 'contract', 'temporary', 'intern') NOT NULL)
├── start_date (DATE NOT NULL)
├── end_date (DATE NULL)
├── basic_salary (DECIMAL(10,2) NOT NULL)
├── hourly_rate (DECIMAL(8,2) NULL)
├── tax_number (VARCHAR(20) NULL)
├── uif_number (VARCHAR(20) NULL)
├── medical_aid_number (VARCHAR(50) NULL)
├── pension_fund_number (VARCHAR(50) NULL)
├── bank_name (VARCHAR(100) NULL)
├── bank_account_number (VARCHAR(20) NULL)
├── bank_branch_code (VARCHAR(10) NULL)
├── status (ENUM('active', 'inactive', 'terminated') DEFAULT 'active')
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_payroll_employees_client (client_id, status)
INDEX idx_payroll_employees_id_number (id_number)
INDEX idx_payroll_employees_employee_number (employee_number, client_id)
INDEX idx_payroll_employees_uuid (uuid)
```

#### payroll_runs
**Purpose:** Monthly payroll processing records
```sql
payroll_runs
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── client_id (BIGINT UNSIGNED NOT NULL FK→accounting_clients.id CASCADE)
├── pay_period_start (DATE NOT NULL)
├── pay_period_end (DATE NOT NULL)
├── pay_date (DATE NOT NULL)
├── total_gross_pay (DECIMAL(15,2) DEFAULT 0.00)
├── total_paye (DECIMAL(15,2) DEFAULT 0.00)
├── total_uif (DECIMAL(15,2) DEFAULT 0.00)
├── total_sdl (DECIMAL(15,2) DEFAULT 0.00) -- Skills Development Levy
├── total_net_pay (DECIMAL(15,2) DEFAULT 0.00)
├── status (ENUM('draft', 'processed', 'paid', 'cancelled') DEFAULT 'draft')
├── processed_by (BIGINT UNSIGNED NULL FK→users.id SET NULL)
├── processed_at (TIMESTAMP NULL)
├── notes (TEXT NULL)
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_payroll_runs_client_period (client_id, pay_period_start, pay_period_end)
INDEX idx_payroll_runs_status (status, client_id)
INDEX idx_payroll_runs_processed_by (processed_by)
INDEX idx_payroll_runs_uuid (uuid)
```

#### financial_reports
**Purpose:** Generated financial reports for clients
```sql
financial_reports
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── client_id (BIGINT UNSIGNED NOT NULL FK→accounting_clients.id CASCADE)
├── report_type (ENUM('balance_sheet', 'income_statement', 'cash_flow', 'trial_balance', 'aged_debtors', 'aged_creditors', 'vat_return') NOT NULL)
├── report_name (VARCHAR(200) NOT NULL)
├── period_start (DATE NOT NULL)
├── period_end (DATE NOT NULL)
├── report_data (JSON NOT NULL) -- Structured report data
├── file_path (VARCHAR(500) NULL) -- PDF/Excel file path
├── status (ENUM('draft', 'final', 'sent') DEFAULT 'draft')
├── generated_by (BIGINT UNSIGNED NOT NULL FK→users.id)
├── generated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── sent_at (TIMESTAMP NULL)
├── notes (TEXT NULL)
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_financial_reports_client_type (client_id, report_type)
INDEX idx_financial_reports_period (period_start, period_end, client_id)
INDEX idx_financial_reports_generated_by (generated_by)
INDEX idx_financial_reports_status (status, client_id)
INDEX idx_financial_reports_uuid (uuid)
```

---

### 📝 Module 6: Content Management System

#### pages
**Purpose:** Static page content management
```sql
pages
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── title (VARCHAR(300) NOT NULL)
├── slug (VARCHAR(400) UNIQUE NOT NULL)
├── content (LONGTEXT NULL)
├── excerpt (TEXT NULL)
├── featured_image (VARCHAR(500) NULL)
├── template (VARCHAR(100) NULL) -- Custom template name
├── is_published (BOOLEAN DEFAULT FALSE)
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── published_at (TIMESTAMP NULL)
├── meta_title (VARCHAR(255) NULL)
├── meta_description (TEXT NULL)
├── meta_keywords (TEXT NULL)
├── created_by (BIGINT UNSIGNED NULL FK→users.id SET NULL)
├── updated_by (BIGINT UNSIGNED NULL FK→users.id SET NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_pages_slug_published (slug, is_published, is_deleted)
INDEX idx_pages_published_date (is_published, published_at)
INDEX idx_pages_created_by (created_by)
INDEX idx_pages_uuid (uuid)
```

#### blog_categories
**Purpose:** Blog post categorization
```sql
blog_categories
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── name (VARCHAR(200) NOT NULL)
├── slug (VARCHAR(255) UNIQUE NOT NULL)
├── description (TEXT NULL)
├── image (VARCHAR(500) NULL)
├── parent_id (BIGINT UNSIGNED NULL FK→blog_categories.id CASCADE)
├── sort_order (INT UNSIGNED DEFAULT 0)
├── is_active (BOOLEAN DEFAULT TRUE)
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── meta_title (VARCHAR(255) NULL)
├── meta_description (TEXT NULL)
├── meta_keywords (TEXT NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_blog_categories_slug_active (slug, is_active, is_deleted)
INDEX idx_blog_categories_parent_sort (parent_id, sort_order)
```

#### blog_posts
**Purpose:** Blog content management
```sql
blog_posts
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── title (VARCHAR(300) NOT NULL)
├── slug (VARCHAR(400) UNIQUE NOT NULL)
├── excerpt (TEXT NULL)
├── content (LONGTEXT NOT NULL)
├── featured_image (VARCHAR(500) NULL)
├── category_id (BIGINT UNSIGNED NULL FK→blog_categories.id SET NULL)
├── author_id (BIGINT UNSIGNED NOT NULL FK→users.id RESTRICT)
├── is_published (BOOLEAN DEFAULT FALSE)
├── is_featured (BOOLEAN DEFAULT FALSE)
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── published_at (TIMESTAMP NULL)
├── meta_title (VARCHAR(255) NULL)
├── meta_description (TEXT NULL)
├── meta_keywords (TEXT NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_blog_posts_slug_published (slug, is_published, is_deleted)
INDEX idx_blog_posts_category_published (category_id, is_published)
INDEX idx_blog_posts_author (author_id)
INDEX idx_blog_posts_featured_published (is_featured, is_published)
INDEX idx_blog_posts_published_date (is_published, published_at)
INDEX idx_blog_posts_uuid (uuid)
```

---

### 📊 Module 7: Analytics & Activity Tracking

#### activity_logs
**Purpose:** Comprehensive system activity tracking
```sql
activity_logs
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── user_id (BIGINT UNSIGNED NULL FK→users.id SET NULL)
├── user_email (VARCHAR(255) NULL) -- Denormalized for deleted users
├── user_name (VARCHAR(255) NULL) -- Denormalized for deleted users
├── activity_type (VARCHAR(100) NOT NULL) -- login, logout, create_order, etc.
├── activity_description (TEXT NOT NULL)
├── status (ENUM('success', 'failed', 'pending') DEFAULT 'success')
├── failure_reason (TEXT NULL)
├── ip_address (VARCHAR(45) NULL) -- IPv4/IPv6
├── user_agent (TEXT NULL)
├── device_type (VARCHAR(50) NULL) -- mobile, desktop, tablet
├── browser (VARCHAR(100) NULL)
├── platform (VARCHAR(100) NULL) -- Windows, macOS, Linux, etc.
├── country (VARCHAR(100) NULL)
├── region (VARCHAR(100) NULL)
├── city (VARCHAR(100) NULL)
├── latitude (DECIMAL(10,8) NULL)
├── longitude (DECIMAL(11,8) NULL)
├── url (VARCHAR(1000) NULL)
├── method (VARCHAR(10) NULL) -- GET, POST, etc.
├── request_data (JSON NULL)
├── response_data (JSON NULL)
├── session_id (VARCHAR(255) NULL)
├── request_id (VARCHAR(100) NULL) -- Unique request identifier
├── is_suspicious (BOOLEAN DEFAULT FALSE)
├── security_notes (TEXT NULL)
├── risk_score (TINYINT UNSIGNED DEFAULT 0) -- 0-100
├── occurred_at (TIMESTAMP NOT NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_activity_logs_user_type_date (user_id, activity_type, occurred_at)
INDEX idx_activity_logs_email_type_date (user_email, activity_type, occurred_at)
INDEX idx_activity_logs_ip_date (ip_address, occurred_at)
INDEX idx_activity_logs_type_status_date (activity_type, status, occurred_at)
INDEX idx_activity_logs_suspicious_date (is_suspicious, occurred_at)
INDEX idx_activity_logs_occurred (occurred_at)
INDEX idx_activity_logs_uuid (uuid)
```

---

### ⚙️ Module 8: System Configuration

#### settings
**Purpose:** Application configuration management
```sql
settings
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── key (VARCHAR(255) UNIQUE NOT NULL)
├── value (LONGTEXT NULL)
├── type (ENUM('string', 'integer', 'boolean', 'json', 'array') DEFAULT 'string')
├── group (VARCHAR(100) NULL) -- general, email, payment, etc.
├── label (VARCHAR(255) NULL) -- Human-readable label
├── description (TEXT NULL)
├── is_public (BOOLEAN DEFAULT FALSE) -- Can be accessed by frontend
├── is_encrypted (BOOLEAN DEFAULT FALSE) -- Sensitive data
├── validation_rules (JSON NULL) -- Validation constraints
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_settings_key (key)
INDEX idx_settings_group_public (group, is_public)
```

#### contact_submissions
**Purpose:** Contact form submissions management
```sql
contact_submissions
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── name (VARCHAR(200) NOT NULL)
├── email (VARCHAR(255) NOT NULL)
├── phone (VARCHAR(20) NULL)
├── company (VARCHAR(200) NULL)
├── subject (VARCHAR(300) NOT NULL)
├── message (LONGTEXT NOT NULL)
├── ip_address (VARCHAR(45) NULL)
├── user_agent (TEXT NULL)
├── referrer (VARCHAR(1000) NULL)
├── is_read (BOOLEAN DEFAULT FALSE)
├── is_spam (BOOLEAN DEFAULT FALSE)
├── replied_at (TIMESTAMP NULL)
├── replied_by (BIGINT UNSIGNED NULL FK→users.id SET NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_contact_submissions_email_date (email, created_at)
INDEX idx_contact_submissions_read_spam (is_read, is_spam)
INDEX idx_contact_submissions_replied (replied_by, replied_at)
```

---

## 📧 Module 9: Email Marketing & Newsletter System

#### email_templates
**Purpose:** Email template management for campaigns and transactional emails
```sql
email_templates
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── name (VARCHAR(255) NOT NULL)
├── slug (VARCHAR(255) UNIQUE NOT NULL)
├── description (TEXT NULL)
├── subject (VARCHAR(255) NOT NULL)
├── html_content (LONGTEXT NOT NULL)
├── text_content (LONGTEXT NULL)
├── variables (JSON NULL) -- Available template variables
├── category (VARCHAR(255) DEFAULT 'general') -- newsletter, promotional, transactional
├── type (VARCHAR(255) DEFAULT 'custom') -- custom, system, automated
├── is_active (BOOLEAN DEFAULT TRUE)
├── is_default (BOOLEAN DEFAULT FALSE)
├── design_settings (JSON NULL)
├── preview_image (VARCHAR(500) NULL)
├── created_by (BIGINT UNSIGNED NULL FK→users.id SET NULL)
├── updated_by (BIGINT UNSIGNED NULL FK→users.id SET NULL)
├── last_used_at (TIMESTAMP NULL)
├── usage_count (INT UNSIGNED DEFAULT 0)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_email_templates_active_category (is_active, category)
INDEX idx_email_templates_slug (slug)
INDEX idx_email_templates_type (type)
```

#### email_campaigns
**Purpose:** Email campaign management and scheduling
```sql
email_campaigns
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── name (VARCHAR(255) NOT NULL)
├── subject (VARCHAR(255) NOT NULL)
├── template_id (BIGINT UNSIGNED NULL FK→email_templates.id SET NULL)
├── content (LONGTEXT NOT NULL)
├── recipient_type (ENUM('all', 'segment', 'tags', 'custom') DEFAULT 'all')
├── recipient_criteria (JSON NULL)
├── status (ENUM('draft', 'scheduled', 'sending', 'sent', 'paused', 'cancelled') DEFAULT 'draft')
├── scheduled_at (TIMESTAMP NULL)
├── sent_at (TIMESTAMP NULL)
├── total_recipients (INT UNSIGNED DEFAULT 0)
├── total_sent (INT UNSIGNED DEFAULT 0)
├── total_delivered (INT UNSIGNED DEFAULT 0)
├── total_opened (INT UNSIGNED DEFAULT 0)
├── total_clicked (INT UNSIGNED DEFAULT 0)
├── total_bounced (INT UNSIGNED DEFAULT 0)
├── total_unsubscribed (INT UNSIGNED DEFAULT 0)
├── created_by (BIGINT UNSIGNED NULL FK→users.id SET NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_email_campaigns_status_scheduled (status, scheduled_at)
INDEX idx_email_campaigns_created_by (created_by)
```

#### email_campaign_sends
**Purpose:** Individual email send tracking
```sql
email_campaign_sends
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── campaign_id (BIGINT UNSIGNED NOT NULL FK→email_campaigns.id CASCADE)
├── subscription_id (BIGINT UNSIGNED NOT NULL FK→newsletter_subscriptions.id CASCADE)
├── email (VARCHAR(255) NOT NULL)
├── status (ENUM('pending', 'sent', 'delivered', 'bounced', 'failed') DEFAULT 'pending')
├── sent_at (TIMESTAMP NULL)
├── delivered_at (TIMESTAMP NULL)
├── opened_at (TIMESTAMP NULL)
├── clicked_at (TIMESTAMP NULL)
├── bounced_at (TIMESTAMP NULL)
├── error_message (TEXT NULL)
├── tracking_token (VARCHAR(255) UNIQUE NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_email_campaign_sends_campaign_status (campaign_id, status)
INDEX idx_email_campaign_sends_tracking_token (tracking_token)
INDEX idx_email_campaign_sends_email (email)
```

#### subscriber_tags
**Purpose:** Newsletter subscriber tagging system
```sql
subscriber_tags
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── name (VARCHAR(255) NOT NULL)
├── slug (VARCHAR(255) UNIQUE NOT NULL)
├── description (TEXT NULL)
├── color (VARCHAR(7) DEFAULT '#3B82F6') -- Hex color code
├── is_active (BOOLEAN DEFAULT TRUE)
├── subscriber_count (INT UNSIGNED DEFAULT 0)
├── created_by (BIGINT UNSIGNED NULL FK→users.id SET NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_subscriber_tags_active (is_active)
INDEX idx_subscriber_tags_slug (slug)
```

---

## 💼 Module 10: Career Management & Job Applications

#### career_jobs
**Purpose:** Job posting management
```sql
career_jobs
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── title (VARCHAR(255) NOT NULL)
├── slug (VARCHAR(255) UNIQUE NOT NULL)
├── description (LONGTEXT NOT NULL)
├── requirements (LONGTEXT NULL)
├── responsibilities (LONGTEXT NULL)
├── location (VARCHAR(255) NULL)
├── employment_type (ENUM('full-time', 'part-time', 'contract', 'internship') DEFAULT 'full-time')
├── experience_level (ENUM('entry', 'mid', 'senior', 'executive') DEFAULT 'mid')
├── department (VARCHAR(255) NULL)
├── salary_min (DECIMAL(10,2) NULL)
├── salary_max (DECIMAL(10,2) NULL)
├── salary_currency (VARCHAR(3) DEFAULT 'ZAR')
├── salary_period (ENUM('hour', 'month', 'year') DEFAULT 'month')
├── benefits (TEXT NULL)
├── application_deadline (DATE NULL)
├── is_remote (BOOLEAN DEFAULT FALSE)
├── is_featured (BOOLEAN DEFAULT FALSE)
├── is_active (BOOLEAN DEFAULT TRUE)
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── sort_order (INT UNSIGNED DEFAULT 0)
├── meta_title (VARCHAR(255) NULL)
├── meta_description (TEXT NULL)
├── meta_keywords (TEXT NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_career_jobs_active_featured (is_active, is_featured, is_deleted)
INDEX idx_career_jobs_slug (slug)
INDEX idx_career_jobs_location (location)
INDEX idx_career_jobs_employment_type (employment_type)
```

---

## 📝 Module 11: Blog & Content Management System

#### blog_categories
**Purpose:** Blog post categorization
```sql
blog_categories
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── name (VARCHAR(200) NOT NULL)
├── slug (VARCHAR(255) UNIQUE NOT NULL)
├── description (TEXT NULL)
├── image (VARCHAR(500) NULL)
├── parent_id (BIGINT UNSIGNED NULL FK→blog_categories.id CASCADE)
├── sort_order (INT UNSIGNED DEFAULT 0)
├── is_active (BOOLEAN DEFAULT TRUE)
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── meta_title (VARCHAR(255) NULL)
├── meta_description (TEXT NULL)
├── meta_keywords (TEXT NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_blog_categories_active_parent (is_active, parent_id, is_deleted)
INDEX idx_blog_categories_slug (slug)
INDEX idx_blog_categories_sort (sort_order)
```

#### blog_posts
**Purpose:** Blog post management
```sql
blog_posts
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── title (VARCHAR(300) NOT NULL)
├── slug (VARCHAR(400) UNIQUE NOT NULL)
├── excerpt (TEXT NULL)
├── content (LONGTEXT NOT NULL)
├── featured_image (VARCHAR(500) NULL)
├── gallery_images (JSON NULL) -- Array of image URLs
├── category_id (BIGINT UNSIGNED NULL FK→blog_categories.id SET NULL)
├── author_id (BIGINT UNSIGNED NOT NULL FK→users.id RESTRICT)
├── service_ids (JSON NULL) -- Associated service IDs
├── view_count (BIGINT UNSIGNED DEFAULT 0)
├── is_published (BOOLEAN DEFAULT FALSE)
├── is_featured (BOOLEAN DEFAULT FALSE)
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── published_at (TIMESTAMP NULL)
├── meta_title (VARCHAR(255) NULL)
├── meta_description (TEXT NULL)
├── meta_keywords (TEXT NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_blog_posts_published_featured (is_published, is_featured, is_deleted)
INDEX idx_blog_posts_category_published (category_id, is_published)
INDEX idx_blog_posts_author (author_id)
INDEX idx_blog_posts_slug (slug)
INDEX idx_blog_posts_view_count (view_count)
FULLTEXT idx_blog_posts_search (title, content)
```

#### blog_comments
**Purpose:** Blog post comments with moderation
```sql
blog_comments
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── post_id (BIGINT UNSIGNED NOT NULL FK→blog_posts.id CASCADE)
├── parent_id (BIGINT UNSIGNED NULL FK→blog_comments.id CASCADE)
├── user_id (BIGINT UNSIGNED NULL FK→users.id SET NULL)
├── author_name (VARCHAR(255) NOT NULL)
├── author_email (VARCHAR(255) NOT NULL)
├── author_website (VARCHAR(500) NULL)
├── content (TEXT NOT NULL)
├── attachments (JSON NULL) -- File attachments
├── status (ENUM('pending', 'approved', 'rejected', 'spam') DEFAULT 'pending')
├── helpful_count (INT UNSIGNED DEFAULT 0)
├── flag_count (INT UNSIGNED DEFAULT 0)
├── ip_address (VARCHAR(45) NOT NULL)
├── user_agent (TEXT NULL)
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── moderated_by (BIGINT UNSIGNED NULL FK→users.id SET NULL)
├── moderated_at (TIMESTAMP NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_blog_comments_post_status (post_id, status, is_deleted)
INDEX idx_blog_comments_user (user_id)
INDEX idx_blog_comments_parent (parent_id)
INDEX idx_blog_comments_uuid (uuid)
```

---

## 🔍 Module 12: Visitor Analytics & Journey Tracking

#### visitor_analytics
**Purpose:** Comprehensive visitor tracking and analytics
```sql
visitor_analytics
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── visitor_id (VARCHAR(255) NOT NULL) -- Unique visitor identifier
├── fingerprint (VARCHAR(255) NULL) -- Browser fingerprint
├── is_returning_visitor (BOOLEAN DEFAULT FALSE)
├── first_visit (TIMESTAMP NULL)
├── last_visit (TIMESTAMP NULL)
├── visit_count (INT UNSIGNED DEFAULT 1)
├── page_url (VARCHAR(2048) NOT NULL)
├── page_title (VARCHAR(500) NULL)
├── route_name (VARCHAR(255) NULL)
├── route_parameters (JSON NULL)
├── referrer_url (VARCHAR(2048) NULL)
├── utm_source (VARCHAR(255) NULL)
├── utm_medium (VARCHAR(255) NULL)
├── utm_campaign (VARCHAR(255) NULL)
├── ip_address (VARCHAR(45) NOT NULL)
├── user_agent (TEXT NOT NULL)
├── method (VARCHAR(10) DEFAULT 'GET')
├── response_status (INT UNSIGNED DEFAULT 200)
├── response_time_ms (INT UNSIGNED NULL)
├── device_type (VARCHAR(50) NULL) -- mobile, desktop, tablet
├── device_model (VARCHAR(255) NULL)
├── browser (VARCHAR(255) NULL)
├── browser_version (VARCHAR(255) NULL)
├── platform (VARCHAR(255) NULL) -- OS
├── platform_version (VARCHAR(255) NULL)
├── screen_width (INT UNSIGNED NULL)
├── screen_height (INT UNSIGNED NULL)
├── language (VARCHAR(10) NULL)
├── timezone (VARCHAR(50) NULL)
├── country (VARCHAR(255) NULL)
├── country_code (CHAR(2) NULL)
├── region (VARCHAR(255) NULL)
├── city (VARCHAR(255) NULL)
├── latitude (DECIMAL(10,8) NULL)
├── longitude (DECIMAL(11,8) NULL)
├── isp (VARCHAR(255) NULL)
├── session_id (VARCHAR(255) NULL)
├── session_duration (INT UNSIGNED NULL)
├── is_bounce (BOOLEAN DEFAULT FALSE)
├── page_views_in_session (INT UNSIGNED DEFAULT 1)
├── has_errors (BOOLEAN DEFAULT FALSE)
├── errors (JSON NULL)
├── error_details (TEXT NULL)
├── dom_load_time (INT UNSIGNED NULL)
├── page_load_time (INT UNSIGNED NULL)
├── time_to_first_byte (INT UNSIGNED NULL)
├── scroll_depth (INT UNSIGNED NULL)
├── clicks_count (INT UNSIGNED DEFAULT 0)
├── clicked_elements (JSON NULL)
├── form_submitted (BOOLEAN DEFAULT FALSE)
├── form_interactions (JSON NULL)
├── is_bot (BOOLEAN DEFAULT FALSE)
├── bot_name (VARCHAR(255) NULL)
├── is_suspicious (BOOLEAN DEFAULT FALSE)
├── suspicious_reasons (TEXT NULL)
├── risk_score (INT UNSIGNED DEFAULT 0)
├── user_journey (JSON NULL) -- Journey steps
├── journey_length (INT UNSIGNED DEFAULT 1)
├── lead_score (INT UNSIGNED DEFAULT 0)
├── lead_status (ENUM('cold', 'warm', 'hot', 'qualified') DEFAULT 'cold')
├── conversion_events (JSON NULL)
├── checkout_funnel (JSON NULL)
├── checkout_analytics (JSON NULL)
├── checkout_started (BOOLEAN DEFAULT FALSE)
├── checkout_completed (BOOLEAN DEFAULT FALSE)
├── checkout_abandoned (BOOLEAN DEFAULT FALSE)
├── checkout_started_at (TIMESTAMP NULL)
├── checkout_completed_at (TIMESTAMP NULL)
├── checkout_abandoned_at (TIMESTAMP NULL)
├── checkout_performance (JSON NULL)
├── checkout_duration (INT UNSIGNED NULL)
├── checkout_exit_step (VARCHAR(255) NULL)
├── visited_at (TIMESTAMP NULL)
├── left_at (TIMESTAMP NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes (Performance optimized for analytics queries)
INDEX idx_visitor_analytics_visitor_visited (visitor_id, visited_at)
INDEX idx_visitor_analytics_ip_visited (ip_address, visited_at)
INDEX idx_visitor_analytics_page_visited (page_url, visited_at)
INDEX idx_visitor_analytics_route_visited (route_name, visited_at)
INDEX idx_visitor_analytics_country_visited (country, visited_at)
INDEX idx_visitor_analytics_device_visited (device_type, visited_at)
INDEX idx_visitor_analytics_returning_visited (is_returning_visitor, visited_at)
INDEX idx_visitor_analytics_bot_visited (is_bot, visited_at)
INDEX idx_visitor_analytics_errors_visited (has_errors, visited_at)
INDEX idx_visitor_analytics_checkout_started (checkout_started, visited_at)
INDEX idx_visitor_analytics_checkout_completed (checkout_completed, visited_at)
INDEX idx_visitor_analytics_checkout_abandoned (checkout_abandoned, visited_at)
INDEX idx_visitor_analytics_checkout_started_at (checkout_started_at)
INDEX idx_visitor_analytics_checkout_completed_at (checkout_completed_at)
INDEX idx_visitor_analytics_checkout_abandoned_at (checkout_abandoned_at)
INDEX idx_visitor_analytics_visited_at (visited_at)
INDEX idx_visitor_analytics_uuid (uuid)
```

---

## ⭐ Module 13: Product Reviews & Ratings

#### product_reviews
**Purpose:** Customer product reviews and ratings
```sql
product_reviews
├── id (PK, BIGINT UNSIGNED AUTO_INCREMENT)
├── uuid (VARCHAR(36) UNIQUE NOT NULL)
├── product_id (BIGINT UNSIGNED NOT NULL FK→products.id CASCADE)
├── user_id (BIGINT UNSIGNED NULL FK→users.id SET NULL)
├── order_id (BIGINT UNSIGNED NULL FK→orders.id SET NULL) -- Verified purchase
├── reviewer_name (VARCHAR(255) NOT NULL)
├── reviewer_email (VARCHAR(255) NOT NULL)
├── rating (TINYINT UNSIGNED NOT NULL) -- 1-5 stars
├── title (VARCHAR(255) NULL)
├── content (TEXT NOT NULL)
├── pros (TEXT NULL)
├── cons (TEXT NULL)
├── images (JSON NULL) -- Review images
├── verified_purchase (BOOLEAN DEFAULT FALSE)
├── status (ENUM('pending', 'approved', 'rejected', 'spam') DEFAULT 'pending')
├── helpful_count (INT UNSIGNED DEFAULT 0)
├── flag_count (INT UNSIGNED DEFAULT 0)
├── ip_address (VARCHAR(45) NOT NULL)
├── user_agent (TEXT NULL)
├── is_deleted (BOOLEAN DEFAULT FALSE)
├── moderated_by (BIGINT UNSIGNED NULL FK→users.id SET NULL)
├── moderated_at (TIMESTAMP NULL)
├── created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
├── updated_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)

-- Indexes
INDEX idx_product_reviews_product_status (product_id, status, is_deleted)
INDEX idx_product_reviews_user (user_id)
INDEX idx_product_reviews_rating (rating)
INDEX idx_product_reviews_verified (verified_purchase)
INDEX idx_product_reviews_uuid (uuid)
```

---

## 🔗 Entity Relationships & Business Rules

### Core Relationships
1. **users (M) ←→ (1) roles** - Each user has one role
2. **users (1) ←→ (M) user_addresses** - Users can have multiple addresses
3. **products (M) ←→ (M) product_categories** - Many-to-many through pivot table
4. **products (1) ←→ (M) product_variants** - Products can have multiple variants
5. **users (1) ←→ (M) shopping_carts** - Users can have multiple carts (expired/active)
6. **shopping_carts (1) ←→ (M) cart_items** - Cart contains multiple items
7. **users (1) ←→ (M) orders** - Users can place multiple orders
8. **orders (1) ←→ (M) order_items** - Orders contain multiple line items
9. **users (1) ←→ (M) projects** - Clients can have multiple projects
10. **services (1) ←→ (M) projects** - Services can be used in multiple projects
11. **languages (1) ←→ (M) translations** - Polymorphic translation system
12. **users (1) ←→ (M) activity_logs** - Comprehensive activity tracking

### E-commerce & Product Relationships
13. **products (1) ←→ (M) product_reviews** - Products can have multiple reviews
14. **users (1) ←→ (M) product_reviews** - Users can write multiple reviews
15. **orders (1) ←→ (M) product_reviews** - Reviews can be linked to verified purchases
16. **products (1) ←→ (M) cart_items** - Products can be in multiple carts
17. **product_variants (1) ←→ (M) cart_items** - Variants can be in multiple carts
18. **currencies (1) ←→ (M) orders** - Orders use specific currencies
19. **coupons (1) ←→ (M) orders** - Coupons can be used in multiple orders
20. **coupons (1) ←→ (M) shopping_carts** - Coupons can be applied to carts

### Content Management Relationships
21. **blog_categories (1) ←→ (M) blog_posts** - Categories contain multiple posts
22. **blog_categories (1) ←→ (M) blog_categories** - Self-referencing parent/child
23. **users (1) ←→ (M) blog_posts** - Users author blog posts
24. **blog_posts (1) ←→ (M) blog_comments** - Posts can have multiple comments
25. **blog_comments (1) ←→ (M) blog_comments** - Self-referencing parent/child for threaded comments
26. **users (1) ←→ (M) blog_comments** - Users can write comments

### Email Marketing Relationships
27. **email_templates (1) ←→ (M) email_campaigns** - Templates used in campaigns
28. **email_campaigns (1) ←→ (M) email_campaign_sends** - Campaigns have multiple sends
29. **newsletter_subscriptions (1) ←→ (M) email_campaign_sends** - Subscribers receive campaign emails
30. **newsletter_subscriptions (M) ←→ (M) subscriber_tags** - Many-to-many tagging system
31. **users (1) ←→ (M) email_templates** - Users create/update templates
32. **users (1) ←→ (M) email_campaigns** - Users create/manage campaigns

### Career Management Relationships
33. **career_jobs (1) ←→ (M) job_applications** - Jobs can have multiple applications
34. **users (1) ←→ (M) job_applications** - Users can apply to multiple jobs

### Project Management Relationships
35. **users (1) ←→ (M) project_applications** - Users can submit multiple applications
36. **project_applications (1) ←→ (1) projects** - Approved applications become projects
37. **users (1) ←→ (M) contact_submissions** - Users can submit multiple contact forms

### Accounting Services Relationships (Planned - Not Yet Implemented)
38. **users (1) ←→ (M) accounting_clients** - Users can be linked to accounting clients
39. **accounting_clients (M) ←→ (M) accounting_services** - Many-to-many through accounting_client_services
40. **accounting_clients (1) ←→ (M) accounting_transactions** - Clients have multiple transactions
41. **accounting_clients (1) ←→ (M) payroll_employees** - Clients have multiple employees
42. **accounting_clients (1) ←→ (M) payroll_runs** - Clients have multiple payroll runs
43. **accounting_clients (1) ←→ (M) financial_reports** - Clients have multiple reports
44. **users (1) ←→ (M) accounting_clients** - Accountants assigned to clients (assigned_accountant_id)
45. **users (1) ←→ (M) accounting_transactions** - Users create/update transactions
46. **users (1) ←→ (M) payroll_runs** - Users process payroll runs
47. **users (1) ←→ (M) financial_reports** - Users generate reports

### Analytics & Tracking Relationships
48. **visitor_analytics** - Standalone visitor tracking (no direct FK relationships)
49. **users (1) ←→ (M) newsletter_subscriptions** - Users can subscribe to newsletters
50. **payments (M) ←→ (1) orders** - Orders can have multiple payment attempts
51. **users (1) ←→ (M) payments** - Users make payments (for authenticated users)

### Business Rules
- **Soft Deletes**: All tables have `is_deleted` boolean flag for soft delete functionality
  - Only admin users can perform hard deletes
  - All other users (staff, client, customer) can only soft delete
  - Queries automatically filter out soft-deleted records unless explicitly included
- **UUID Public IDs**: All public-facing entities have UUID for security
- **Audit Trail**: All critical operations logged in activity_logs
- **Multi-Currency**: All monetary values support multiple currencies
- **Multi-Language**: All content can be translated
- **Inventory Tracking**: Products can optionally track inventory
- **Guest Checkout**: Orders can be placed without user accounts
- **Role-Based Access**: Permissions controlled through roles table

### Soft Delete Implementation
All tables include `is_deleted` column with the following specifications:
- **Column Type**: `BOOLEAN DEFAULT FALSE`
- **Indexing**: Composite indexes include `is_deleted` for query optimization
- **Model Scopes**: Global scopes filter out soft-deleted records by default
- **Admin Override**: Admin users can view and restore soft-deleted records
- **Cascade Behavior**: Soft deleting parent records soft deletes related children

### Performance Optimizations
- **Composite Indexes**: Optimized for common query patterns
- **Denormalized Data**: Strategic denormalization for performance
- **Full-Text Search**: Product and content search capabilities
- **Materialized Paths**: Category hierarchy optimization
- **JSON Columns**: Flexible attribute storage
- **Timestamp Indexing**: Optimized for date-range queries
