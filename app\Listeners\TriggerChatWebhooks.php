<?php

namespace App\Listeners;

use App\Events\Chat\WebhookEvent;
use App\Services\ChatWebhookService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class TriggerChatWebhooks implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct(
        protected ChatWebhookService $webhookService
    ) {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(WebhookEvent $event): void
    {
        $this->webhookService->triggerEvent($event->eventType, $event->data);
    }
}
