<?php

return [
    // Page Meta
    'page_title' => 'Web Development - ChiSolution',
    'meta_description' => 'Leading web development company specializing in professional web development, WordPress development, API development, progressive web apps, headless CMS, and serverless architecture. Expert Laravel, React, Vue.js, Django, and ASP.NET development services.',
    'meta_keywords' => 'web development, professional web development, WordPress development, API development, progressive web apps, headless CMS, serverless architecture, JAMstack development, custom web development, Laravel development, React development, Vue.js development, Django development, ASP.NET development, JSP development, responsive web design, e-commerce development, web application development, modern web technologies 2025',

    // Hero Section
    'hero_title' => 'Professional Web Development & WordPress Solutions',
    'hero_description' => 'Leading web development company specializing in WordPress development, API development, progressive web apps, headless CMS solutions, and serverless architecture. We leverage cutting-edge technologies like WordPress, Laravel, React, Vue.js, Django, and ASP.NET to build fast, secure, and scalable digital solutions.',
    'get_started' => 'Get Started',
    'get_quote' => 'Get Quote',
    'view_portfolio' => 'View Portfolio',

    // Modern Solutions Section
    'modern_solutions_title' => 'Modern Web Development <span class="text-blue-600">Solutions</span>',
    'modern_solutions_description' => 'From professional web applications to progressive web apps and headless CMS solutions, we create cutting-edge digital experiences that drive business growth and user engagement.',
    'pwa_title' => 'Progressive Web Apps (PWAs)',
    'pwa_description' => 'App-like web experiences with offline functionality, push notifications, and native device features for enhanced user engagement.',
    'ecommerce_platforms_title' => 'E-commerce Platforms',
    'ecommerce_platforms_description' => 'Complete online stores with payment processing, inventory management, and customer accounts.',
    'ai_web_apps_title' => 'AI-Powered Web Applications',
    'ai_web_apps_description' => 'Intelligent web applications with machine learning integration, chatbots, and automated workflows for enhanced user experiences.',
    'landing_pages_title' => 'Landing Pages',
    'landing_pages_description' => 'High-converting landing pages designed to capture leads and drive specific actions.',
    'portfolio_websites_title' => 'Portfolio Websites',
    'portfolio_websites_description' => 'Showcase your work and skills with beautiful, interactive portfolio websites.',
    'headless_cms_title' => 'Headless CMS Solutions',
    'headless_cms_description' => 'Modern content management with API-first architecture, enabling content delivery across multiple platforms and devices.',
    'api_development_title' => 'API Development',
    'api_development_description' => 'RESTful and GraphQL APIs for seamless data integration, third-party service connections, and mobile app backends.',
    'wordpress_development_title' => 'WordPress Development',
    'wordpress_development_description' => 'Custom WordPress themes, plugins, and enterprise solutions with modern development practices and security best practices.',

    // Technologies Section
    'technologies_title' => '2025 Web Development <span class="text-blue-600">Technologies</span>',
    'technologies_description' => 'We leverage cutting-edge 2025 technologies including JAMstack, serverless architecture, AI integration, and modern frameworks to build lightning-fast, secure, and scalable web solutions.',

    // WordPress Excellence Section
    'wordpress_excellence_title' => 'WordPress Development Excellence',
    'wordpress_excellence_description' => 'Harness the power of WordPress with custom themes, plugins, and enterprise solutions. We combine WordPress flexibility with modern development practices for exceptional results.',
    'custom_theme_title' => 'Custom Theme Development',
    'custom_theme_description' => 'Bespoke WordPress themes built from scratch with modern coding standards, responsive design, and optimized performance.',
    'custom_plugin_title' => 'Custom Plugin Development',
    'custom_plugin_description' => 'Extend WordPress functionality with custom plugins tailored to your specific business requirements and workflows.',
    'woocommerce_title' => 'WooCommerce Solutions',
    'woocommerce_description' => 'Complete e-commerce solutions with custom WooCommerce development, payment integrations, and inventory management.',
    'performance_optimization_title' => 'Performance Optimization',
    'performance_optimization_description' => 'Speed optimization, caching solutions, database optimization, and Core Web Vitals improvements for better user experience.',

    // WordPress Development Section
    'custom_theme_development_title' => 'Custom Theme Development',
    'custom_theme_development_description' => 'Bespoke WordPress themes built from scratch with modern coding standards, responsive design, and optimized performance.',
    'custom_plugin_development_title' => 'Custom Plugin Development',
    'custom_plugin_development_description' => 'Extend WordPress functionality with custom plugins tailored to your specific business requirements and workflows.',
    'woocommerce_solutions_title' => 'WooCommerce Solutions',
    'woocommerce_solutions_description' => 'Complete e-commerce solutions with custom WooCommerce development, payment integrations, and inventory management.',
    'start_wordpress_project' => 'Start WordPress Project',

    // Web App Enhancement Features Section
    'web_app_features_title' => 'Web App Enhancement Features',
    'web_app_features_description' => 'Transform your web applications with cutting-edge features that enhance user experience, improve performance, and drive business growth.',
    'progressive_web_apps_title' => 'Progressive Web Apps',
    'progressive_web_apps_description' => 'App-like web experiences with offline functionality, push notifications, and native device features for enhanced user engagement.',
    'real_time_features_title' => 'Real-time Features',
    'real_time_features_description' => 'Live chat, real-time notifications, collaborative editing, and instant data synchronization for dynamic user experiences.',
    'api_integration_title' => 'API Integration',
    'api_integration_description' => 'Seamless integration with third-party services, payment gateways, social media platforms, and business tools.',
    'database_optimization_title' => 'Database Optimization',
    'database_optimization_description' => 'Query optimization, indexing strategies, caching solutions, and database performance tuning for faster applications.',
    'security_implementation_title' => 'Security Implementation',
    'security_implementation_description' => 'Advanced security measures including SSL certificates, data encryption, secure authentication, and vulnerability protection.',
    'enhance_web_app' => 'Enhance Your Web App',

    // 2025 Trends Section
    'technologies' => 'Technologies',
    'trends_2025_description' => 'Stay ahead with the latest 2025 web development trends including AI integration, WebAssembly, edge computing, and micro-frontends.',
    'ai_integration_title' => 'AI Integration',
    'ai_integration_description' => 'Machine learning models, chatbots, automated content generation, and intelligent user experiences.',
    'webassembly_title' => 'WebAssembly',
    'webassembly_description' => 'High-performance web applications with near-native speed using WebAssembly for complex computations.',
    'edge_computing_title' => 'Edge Computing',
    'edge_computing_description' => 'Faster content delivery and reduced latency with edge computing and CDN optimization.',
    'micro_frontends_title' => 'Micro-Frontends',
    'micro_frontends_description' => 'Scalable frontend architecture with independent, deployable micro-frontend components.',
    'explore_future_tech' => 'Explore Future Technologies',

    // Why Choose Us Section
    'why_choose_us_title' => 'Why Choose ChiSolution for Web Development',
    'why_choose_us_description' => 'Partner with industry experts who deliver exceptional web solutions using cutting-edge technologies and proven methodologies.',
    'expert_team_title' => 'Expert Development Team',
    'expert_team_description' => 'Certified developers with 10+ years of experience in modern web technologies and industry best practices.',
    'proven_track_record_title' => 'Proven Track Record',
    'proven_track_record_description' => '500+ successful projects delivered on time and within budget with 99.9% client satisfaction rate.',
    'support_24_7_title' => '24/7 Support & Maintenance',
    'support_24_7_description' => 'Round-the-clock technical support, regular updates, security monitoring, and performance optimization.',
    'start_your_project' => 'Start Your Project',

    // FAQ Section
    'faq_title' => 'Frequently Asked Questions',
    'faq_description' => 'Get answers to common questions about our web development services, processes, and technologies.',
    'faq_1_question' => 'How long does it take to develop a custom website?',
    'faq_1_answer' => 'Development time varies based on project complexity. A simple website takes 2-4 weeks, while complex web applications can take 8-16 weeks. We provide detailed timelines during project planning.',
    'faq_2_question' => 'Do you provide ongoing maintenance and support?',
    'faq_2_answer' => 'Yes, we offer comprehensive maintenance packages including security updates, performance monitoring, content updates, and technical support to keep your website running smoothly.',
    'faq_3_question' => 'Can you integrate third-party services and APIs?',
    'faq_3_answer' => 'Absolutely! We specialize in API integrations including payment gateways, CRM systems, social media platforms, analytics tools, and custom business applications.',
    'faq_4_question' => 'What technologies do you use for web development?',
    'faq_4_answer' => 'We use modern technologies including WordPress, Laravel, React, Vue.js, Node.js, Python Django, and cloud platforms like AWS and Azure for scalable, secure solutions.',
    'faq_5_question' => 'Do you offer mobile-responsive design?',
    'faq_5_answer' => 'Yes, all our websites are built with mobile-first responsive design, ensuring optimal user experience across all devices and screen sizes.',
    'have_more_questions' => 'Have More Questions?',
    'security_maintenance_title' => 'Security & Maintenance',
    'security_maintenance_description' => 'Comprehensive security hardening, regular updates, backups, and ongoing maintenance to keep your site secure and updated.',
    'headless_wordpress_title' => 'Headless WordPress',
    'headless_wordpress_description' => 'Modern headless WordPress implementations with React, Vue.js, or Next.js frontends for enhanced performance and flexibility.',

    // Enhancement Features Section
    'enhance_title' => 'Enhance Your Existing <span class="text-blue-600">Web Applications</span>',
    'enhance_description' => 'Transform your current web applications with powerful features and integrations. Add new functionality to boost user engagement, streamline operations, and drive business growth.',
    'live_chat_title' => 'Live Chat & AI Chatbots',
    'live_chat_description' => 'Real-time customer support with live chat functionality and intelligent AI chatbots for 24/7 automated assistance and lead generation.',
    'inventory_management_title' => 'Inventory Management System',
    'inventory_management_description' => 'Complete inventory tracking with stock management, automated reordering, barcode scanning, and real-time inventory reports.',
    'ecommerce_features_title' => 'E-commerce Features',
    'ecommerce_features_description' => 'Transform your website into a selling platform with shopping cart, payment processing, order management, and customer accounts.',
    'user_auth_title' => 'User Authentication & Authorization',
    'user_auth_description' => 'Secure user management with registration, login, role-based access control, and social media authentication integration.',
    'analytics_dashboard_title' => 'Analytics & Reporting Dashboard',
    'analytics_dashboard_description' => 'Comprehensive analytics dashboard with real-time data visualization, custom reports, and business intelligence insights.',
    'cms_title' => 'Content Management System',
    'cms_description' => 'Easy-to-use content management interface for updating website content, blog posts, images, and media files without technical knowledge.',
    'booking_system_title' => 'Booking & Appointment System',
    'booking_system_description' => 'Online booking system with calendar integration, automated confirmations, payment processing, and customer management.',
    'email_marketing_title' => 'Email Marketing Integration',
    'email_marketing_description' => 'Automated email campaigns, newsletter subscriptions, drip marketing sequences, and email analytics integration.',
    'advanced_search_title' => 'Advanced Search & Filtering',
    'advanced_search_description' => 'Powerful search functionality with filters, faceted search, autocomplete, and intelligent search suggestions for better user experience.',

    // Enhancement Feature Titles and Descriptions
    'live_chat_chatbots_title' => 'Live Chat & AI Chatbots',
    'live_chat_chatbots_description' => 'Real-time customer support with live chat functionality and intelligent AI chatbots for 24/7 automated assistance and lead generation.',
    'inventory_system_title' => 'Inventory Management System',
    'inventory_system_description' => 'Complete inventory tracking with stock management, automated reordering, barcode scanning, and real-time inventory reports.',
    'ecommerce_enhancement_title' => 'E-commerce Features',
    'ecommerce_enhancement_description' => 'Transform your website into a selling platform with shopping cart, payment processing, order management, and customer accounts.',
    'user_authentication_title' => 'User Authentication & Authorization',
    'user_authentication_description' => 'Secure user management with registration, login, role-based access control, and social media authentication integration.',
    'analytics_reporting_title' => 'Analytics & Reporting Dashboard',
    'analytics_reporting_description' => 'Comprehensive analytics dashboard with real-time data visualization, custom reports, and business intelligence insights.',
    'content_management_title' => 'Content Management System',
    'content_management_description' => 'Easy-to-use content management interface for updating website content, blog posts, images, and media files without technical knowledge.',
    'booking_appointment_title' => 'Booking & Appointment System',
    'booking_appointment_description' => 'Online booking system with calendar integration, automated confirmations, payment processing, and customer management.',
    'email_marketing_integration_title' => 'Email Marketing Integration',
    'email_marketing_integration_description' => 'Automated email campaigns, newsletter subscriptions, drip marketing sequences, and email analytics integration.',
    'advanced_search_filtering_title' => 'Advanced Search & Filtering',
    'advanced_search_filtering_description' => 'Powerful search functionality with filters, faceted search, autocomplete, and intelligent search suggestions for better user experience.',

    // Enhancement CTA
    'enhance_cta_title' => 'Ready to Enhance Your Web Application?',
    'enhance_cta_description' => 'Our team can seamlessly integrate these features into your existing web application without disrupting your current operations. Get a custom quote for your specific requirements.',
    'get_custom_quote' => 'Get Custom Quote',
    'view_enhancement_examples' => 'View Enhancement Examples',

    // Trends Section
    'trends_title' => '2025 Web Development <span class="text-blue-600">Trends</span>',
    'trends_description' => 'Stay ahead with cutting-edge web development trends that are shaping the digital landscape in 2025.',
    'serverless_title' => 'Serverless Architecture',
    'serverless_description' => 'Build scalable applications without managing servers. Automatic scaling, reduced costs, and improved performance.',
    'voice_search_title' => 'Voice Search Optimization',
    'voice_search_description' => 'Optimize websites for voice search queries with conversational content and structured data markup.',
    'webassembly_title' => 'WebAssembly Integration',
    'webassembly_description' => 'High-performance web applications with near-native speed using WebAssembly for complex computations.',

    // Why Choose Section
    'why_choose_title' => 'Why Choose Our <span class="text-blue-600">Web Development</span> Services?',
    'why_choose_description' => 'We combine technical expertise with creative innovation to deliver web solutions that drive business growth.',
    'lightning_fast_title' => 'Lightning Fast',
    'lightning_fast_description' => 'Optimized for speed and performance',
    'secure_reliable_title' => 'Secure & Reliable',
    'secure_reliable_description' => 'Built with security best practices',
    'mobile_first_title' => 'Mobile-First',
    'mobile_first_description' => 'Responsive design for all devices',
    'seo_optimized_title' => 'SEO Optimized',
    'seo_optimized_description' => 'Built for search engine visibility',

    // Live Chat Features
    'live_chat_features' => [
        'Real-time messaging' => 'Real-time messaging',
        'AI-powered responses' => 'AI-powered responses',
        'Multi-language support' => 'Multi-language support',
        'Integration with CRM systems' => 'Integration with CRM systems',
    ],

    // Enhancement Feature Lists
    'inventory_features' => [
        'Stock level tracking' => 'Stock level tracking',
        'Automated alerts' => 'Automated alerts',
        'Barcode integration' => 'Barcode integration',
        'Supplier management' => 'Supplier management',
    ],

    'ecommerce_features_list' => [
        'Shopping cart functionality' => 'Shopping cart functionality',
        'Payment gateway integration' => 'Payment gateway integration',
        'Order tracking system' => 'Order tracking system',
        'Customer account portal' => 'Customer account portal',
    ],

    'user_auth_features' => [
        'Multi-factor authentication' => 'Multi-factor authentication',
        'Social login integration' => 'Social login integration',
        'Role-based permissions' => 'Role-based permissions',
        'Password recovery system' => 'Password recovery system',
    ],

    'analytics_features' => [
        'Real-time data visualization' => 'Real-time data visualization',
        'Custom report generation' => 'Custom report generation',
        'KPI tracking' => 'KPI tracking',
        'Export functionality' => 'Export functionality',
    ],

    'cms_features' => [
        'WYSIWYG editor' => 'WYSIWYG editor',
        'Media library management' => 'Media library management',
        'Content scheduling' => 'Content scheduling',
        'SEO optimization tools' => 'SEO optimization tools',
    ],

    'booking_features' => [
        'Calendar integration' => 'Calendar integration',
        'Automated notifications' => 'Automated notifications',
        'Payment processing' => 'Payment processing',
        'Recurring appointments' => 'Recurring appointments',
    ],

    'email_marketing_features' => [
        'Automated campaigns' => 'Automated campaigns',
        'Subscriber management' => 'Subscriber management',
        'Email templates' => 'Email templates',
        'Performance tracking' => 'Performance tracking',
    ],

    'advanced_search_features' => [
        'Elasticsearch integration' => 'Elasticsearch integration',
        'Faceted search filters' => 'Faceted search filters',
        'Autocomplete suggestions' => 'Autocomplete suggestions',
        'Search analytics' => 'Search analytics',
    ],

    // WordPress Features
    'custom_theme_development_title' => 'Custom Theme Development',
    'custom_theme_development_description' => 'Bespoke WordPress themes built from scratch with modern coding standards, responsive design, and optimized performance.',
    'custom_plugin_development_title' => 'Custom Plugin Development',
    'custom_plugin_development_description' => 'Extend WordPress functionality with custom plugins tailored to your specific business requirements and workflows.',
    'woocommerce_solutions_title' => 'WooCommerce Solutions',
    'woocommerce_solutions_description' => 'Complete e-commerce solutions with custom WooCommerce development, payment integrations, and inventory management.',
    'performance_optimization_title' => 'Performance Optimization',
    'performance_optimization_description' => 'Speed optimization, caching solutions, database optimization, and Core Web Vitals improvements for better user experience.',
    'security_maintenance_title' => 'Security & Maintenance',
    'security_maintenance_description' => 'Comprehensive security hardening, regular updates, backups, and ongoing maintenance to keep your site secure and updated.',
    'headless_wordpress_title' => 'Headless WordPress',
    'headless_wordpress_description' => 'Modern headless WordPress implementations with React, Vue.js, or Next.js frontends for enhanced performance and flexibility.',

    // WordPress Button
    'start_wordpress_project' => 'Start Your WordPress Project',
];
