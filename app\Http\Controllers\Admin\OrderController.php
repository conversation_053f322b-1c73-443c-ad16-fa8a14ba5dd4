<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class OrderController extends Controller
{
    /**
     * Display a listing of orders.
     */
    public function index(Request $request): View
    {
        $query = Order::with(['user', 'items.product', 'currency', 'coupon'])
                     ->where('is_deleted', false);

        // Filter by status if provided
        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        // Filter by payment status if provided
        if ($request->filled('payment_status') && $request->payment_status !== 'all') {
            $query->where('payment_status', $request->payment_status);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('first_name', 'like', "%{$search}%")
                               ->orWhere('last_name', 'like', "%{$search}%");
                  });
            });
        }

        // Sort by
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        
        $allowedSorts = ['created_at', 'order_number', 'total_amount', 'status', 'payment_status'];
        if (!in_array($sortBy, $allowedSorts)) {
            $sortBy = 'created_at';
        }

        $query->orderBy($sortBy, $sortDirection);

        $orders = $query->paginate(15)->withQueryString();

        // Get status counts for filter tabs
        $statusCounts = [
            'all' => Order::where('is_deleted', false)->count(),
            'pending' => Order::where('is_deleted', false)->where('status', 'pending')->count(),
            'processing' => Order::where('is_deleted', false)->where('status', 'processing')->count(),
            'shipped' => Order::where('is_deleted', false)->where('status', 'shipped')->count(),
            'delivered' => Order::where('is_deleted', false)->where('status', 'delivered')->count(),
            'cancelled' => Order::where('is_deleted', false)->where('status', 'cancelled')->count(),
        ];

        // Get payment status counts
        $paymentStatusCounts = [
            'all' => Order::where('is_deleted', false)->count(),
            'pending' => Order::where('is_deleted', false)->where('payment_status', 'pending')->count(),
            'paid' => Order::where('is_deleted', false)->where('payment_status', 'paid')->count(),
            'failed' => Order::where('is_deleted', false)->where('payment_status', 'failed')->count(),
            'refunded' => Order::where('is_deleted', false)->where('payment_status', 'refunded')->count(),
        ];

        return view('admin.orders.index', compact('orders', 'statusCounts', 'paymentStatusCounts'));
    }

    /**
     * Display the specified order.
     */
    public function show(Order $order): View
    {
        // Check if order is deleted
        if ($order->is_deleted) {
            abort(404);
        }

        // Load relationships
        $order->load(['user', 'items.product', 'items.variant', 'currency', 'coupon', 'payments']);

        // Load order history
        $orderHistory = $order->history()
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get available status transitions for admin
        $availableStatuses = $order->getAvailableStatusTransitions(true);
        $availablePaymentStatuses = [
            'pending' => 'Pending',
            'paid' => 'Paid',
            'failed' => 'Failed',
            'refunded' => 'Refunded',
        ];

        return view('admin.orders.show', compact('order', 'orderHistory', 'availableStatuses', 'availablePaymentStatuses'));
    }

    /**
     * Update the specified order status.
     */
    public function update(Request $request, Order $order): RedirectResponse
    {
        // Check if order is deleted
        if ($order->is_deleted) {
            abort(404);
        }

        $request->validate([
            'status' => 'required|in:pending,processing,shipped,delivered,cancelled',
            'payment_status' => 'nullable|in:pending,paid,failed,refunded',
            'notes' => 'nullable|string|max:1000',
            'shipped_at' => 'nullable|date',
            'delivered_at' => 'nullable|date',
        ]);

        $historyService = app(\App\Services\OrderHistoryService::class);
        $oldStatus = $order->status;
        $oldPaymentStatus = $order->payment_status;
        $oldNotes = $order->notes;

        // Validate status transition before attempting to update (with admin override)
        if ($request->status !== $oldStatus) {
            if (!$order->canTransitionTo($request->status, true)) {
                return back()->withErrors([
                    'status' => "Cannot change order status from '{$oldStatus}' to '{$request->status}'. This transition is not allowed."
                ])->withInput();
            }

            try {
                // Use direct update for admin changes to bypass strict validation
                $order->status = $request->status;

                // Handle status-specific logic
                if ($request->status === 'shipped' && !$order->shipped_at) {
                    $order->shipped_at = now();
                }

                if ($request->status === 'delivered' && !$order->delivered_at) {
                    $order->delivered_at = now();
                }

                $order->save();

                // Log the status change
                $historyService->logStatusChange($order, $oldStatus, $request->status);

                // Log specific events
                if ($request->status === 'shipped') {
                    $historyService->logOrderShipped($order);
                } elseif ($request->status === 'delivered') {
                    $historyService->logOrderDelivered($order);
                } elseif ($request->status === 'cancelled') {
                    $historyService->logOrderCancelled($order);
                }

            } catch (\Exception $e) {
                return back()->withErrors([
                    'status' => 'Failed to update order status: ' . $e->getMessage()
                ])->withInput();
            }
        }

        // Update payment status if provided and different
        if ($request->filled('payment_status') && $request->payment_status !== $oldPaymentStatus) {
            $order->payment_status = $request->payment_status;
            $order->save();
            $historyService->logPaymentStatusChange($order, $oldPaymentStatus, $request->payment_status);
        }

        // Update notes if provided and different
        if ($request->filled('notes') && $request->notes !== $oldNotes) {
            $order->notes = $request->notes;
            $order->save();
            $historyService->logNoteAdded($order, $request->notes);
        }

        // Update shipped_at if status is shipped and date is provided
        if ($request->status === 'shipped' && $request->filled('shipped_at')) {
            $order->shipped_at = $request->shipped_at;
            $order->save();
        }

        // Update delivered_at if status is delivered and date is provided
        if ($request->status === 'delivered' && $request->filled('delivered_at')) {
            $order->delivered_at = $request->delivered_at;
            $order->save();
        }

        return back()->with('success', 'Order updated successfully.');
    }

    /**
     * Update order status via AJAX.
     */
    public function updateStatus(Request $request, Order $order)
    {
        // Check if order is deleted
        if ($order->is_deleted) {
            return response()->json(['error' => 'Order not found'], 404);
        }

        $request->validate([
            'status' => 'required|in:pending,processing,shipped,delivered,cancelled',
        ]);

        $updateData = ['status' => $request->status];

        // Auto-set timestamps based on status
        if ($request->status === 'shipped' && !$order->shipped_at) {
            $updateData['shipped_at'] = now();
        }

        if ($request->status === 'delivered' && !$order->delivered_at) {
            $updateData['delivered_at'] = now();
        }

        $order->update($updateData);

        return response()->json([
            'success' => true,
            'message' => 'Order status updated successfully.',
            'status' => $order->status,
            'status_badge' => $this->getStatusBadge($order->status),
        ]);
    }

    /**
     * Get status badge HTML.
     */
    private function getStatusBadge(string $status): string
    {
        $badges = [
            'pending' => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Pending</span>',
            'processing' => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Processing</span>',
            'shipped' => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">Shipped</span>',
            'delivered' => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Delivered</span>',
            'cancelled' => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Cancelled</span>',
        ];

        return $badges[$status] ?? $badges['pending'];
    }
}
