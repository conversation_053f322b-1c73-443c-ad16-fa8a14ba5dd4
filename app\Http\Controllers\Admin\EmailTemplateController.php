<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\EmailTemplate;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class EmailTemplateController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): View|JsonResponse
    {
        $query = EmailTemplate::with(['creator', 'updater'])
                              ->withCount('campaigns');

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%");
            });
        }

        if ($request->filled('category')) {
            $query->where('category', $request->get('category'));
        }

        if ($request->filled('type')) {
            $query->where('type', $request->get('type'));
        }

        if ($request->filled('status')) {
            $isActive = $request->get('status') === 'active';
            $query->where('is_active', $isActive);
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $templates = $query->paginate(15)->withQueryString();

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'data' => $templates,
                'categories' => EmailTemplate::getCategories(),
                'types' => EmailTemplate::getTypes(),
            ]);
        }

        return view('admin.email-templates.index', compact('templates'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        $categories = EmailTemplate::getCategories();
        $types = EmailTemplate::getTypes();

        return view('admin.email-templates.create', compact('categories', 'types'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse|RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'subject' => 'required|string|max:255',
            'html_content' => 'required|string',
            'text_content' => 'nullable|string',
            'category' => 'required|string|in:' . implode(',', array_keys(EmailTemplate::getCategories())),
            'type' => 'required|string|in:' . implode(',', array_keys(EmailTemplate::getTypes())),
            'variables' => 'nullable|array',
            'design_settings' => 'nullable|array',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
        ]);

        $validated['created_by'] = auth()->id();
        $validated['updated_by'] = auth()->id();

        // Handle default template logic
        if ($validated['is_default'] ?? false) {
            EmailTemplate::where('category', $validated['category'])
                         ->where('is_default', true)
                         ->update(['is_default' => false]);
        }

        $template = EmailTemplate::create($validated);

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Email template created successfully.',
                'data' => $template->load(['creator', 'updater']),
                'redirect' => route('admin.email-templates.show', $template),
            ]);
        }

        return redirect()->route('admin.email-templates.show', $template)
                        ->with('success', 'Email template created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(EmailTemplate $emailTemplate): View
    {
        $emailTemplate->load(['creator', 'updater', 'campaigns' => function ($query) {
            $query->latest()->limit(5);
        }]);

        return view('admin.email-templates.show', compact('emailTemplate'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(EmailTemplate $emailTemplate): View
    {
        $categories = EmailTemplate::getCategories();
        $types = EmailTemplate::getTypes();

        return view('admin.email-templates.edit', compact('emailTemplate', 'categories', 'types'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, EmailTemplate $emailTemplate): JsonResponse|RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'subject' => 'required|string|max:255',
            'html_content' => 'required|string',
            'text_content' => 'nullable|string',
            'category' => 'required|string|in:' . implode(',', array_keys(EmailTemplate::getCategories())),
            'type' => 'required|string|in:' . implode(',', array_keys(EmailTemplate::getTypes())),
            'variables' => 'nullable|array',
            'design_settings' => 'nullable|array',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
        ]);

        $validated['updated_by'] = auth()->id();

        // Handle default template logic
        if ($validated['is_default'] ?? false) {
            EmailTemplate::where('category', $validated['category'])
                         ->where('id', '!=', $emailTemplate->id)
                         ->where('is_default', true)
                         ->update(['is_default' => false]);
        }

        $emailTemplate->update($validated);

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Email template updated successfully.',
                'data' => $emailTemplate->fresh(['creator', 'updater']),
            ]);
        }

        $action = $request->get('submit_action', 'save');

        if ($action === 'save_and_continue') {
            return redirect()->route('admin.email-templates.edit', $emailTemplate)
                            ->with('success', 'Email template updated successfully.');
        }

        return redirect()->route('admin.email-templates.show', $emailTemplate)
                        ->with('success', 'Email template updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(EmailTemplate $emailTemplate): JsonResponse|RedirectResponse
    {
        if (!$emailTemplate->canBeDeleted()) {
            if (request()->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'This template cannot be deleted because it is being used by active campaigns or is a system template.',
                ], 422);
            }

            return redirect()->back()
                            ->with('error', 'This template cannot be deleted because it is being used by active campaigns or is a system template.');
        }

        $emailTemplate->delete();

        if (request()->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Email template deleted successfully.',
            ]);
        }

        return redirect()->route('admin.email-templates.index')
                        ->with('success', 'Email template deleted successfully.');
    }

    /**
     * Duplicate the specified template.
     */
    public function duplicate(EmailTemplate $emailTemplate): JsonResponse|RedirectResponse
    {
        $newTemplate = $emailTemplate->duplicate();

        if (request()->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Email template duplicated successfully.',
                'data' => $newTemplate->load(['creator', 'updater']),
                'redirect' => route('admin.email-templates.edit', $newTemplate),
            ]);
        }

        return redirect()->route('admin.email-templates.edit', $newTemplate)
                        ->with('success', 'Email template duplicated successfully.');
    }

    /**
     * Preview the template.
     */
    public function preview(EmailTemplate $emailTemplate, Request $request): View|JsonResponse
    {
        $variables = $request->get('variables', []);
        $rendered = $emailTemplate->render($variables);

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'data' => $rendered,
            ]);
        }

        return view('admin.email-templates.preview', compact('emailTemplate', 'rendered', 'variables'));
    }

    /**
     * Toggle template status.
     */
    public function toggleStatus(EmailTemplate $emailTemplate): JsonResponse
    {
        $emailTemplate->update(['is_active' => !$emailTemplate->is_active]);

        return response()->json([
            'success' => true,
            'message' => 'Template status updated successfully.',
            'is_active' => $emailTemplate->is_active,
        ]);
    }

    /**
     * Set as default template.
     */
    public function setDefault(EmailTemplate $emailTemplate): JsonResponse
    {
        // Remove default from other templates in the same category
        EmailTemplate::where('category', $emailTemplate->category)
                     ->where('id', '!=', $emailTemplate->id)
                     ->update(['is_default' => false]);

        // Set this template as default
        $emailTemplate->update(['is_default' => true]);

        return response()->json([
            'success' => true,
            'message' => 'Template set as default successfully.',
        ]);
    }

    /**
     * Get template variables for AJAX.
     */
    public function getVariables(EmailTemplate $emailTemplate): JsonResponse
    {
        return response()->json([
            'success' => true,
            'variables' => $emailTemplate->getAvailableVariables(),
        ]);
    }

    /**
     * Upload template preview image.
     */
    public function uploadPreview(EmailTemplate $emailTemplate, Request $request): JsonResponse
    {
        $request->validate([
            'preview_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($request->hasFile('preview_image')) {
            // Delete old preview image
            if ($emailTemplate->preview_image) {
                Storage::disk('public')->delete($emailTemplate->preview_image);
            }

            // Store new preview image
            $path = $request->file('preview_image')->store('email-templates/previews', 'public');
            $emailTemplate->update(['preview_image' => $path]);

            return response()->json([
                'success' => true,
                'message' => 'Preview image uploaded successfully.',
                'preview_url' => $emailTemplate->preview_url,
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'No image file provided.',
        ], 422);
    }

    /**
     * Bulk actions for templates.
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $request->validate([
            'action' => 'required|string|in:activate,deactivate,delete',
            'template_ids' => 'required|array|min:1',
            'template_ids.*' => 'exists:email_templates,id',
        ]);

        $templateIds = $request->get('template_ids');
        $action = $request->get('action');

        $templates = EmailTemplate::whereIn('id', $templateIds);

        switch ($action) {
            case 'activate':
                $templates->update(['is_active' => true]);
                $message = 'Templates activated successfully.';
                break;
            case 'deactivate':
                $templates->update(['is_active' => false]);
                $message = 'Templates deactivated successfully.';
                break;
            case 'delete':
                $deletableTemplates = $templates->get()->filter(fn($template) => $template->canBeDeleted());
                $deletableTemplates->each(fn($template) => $template->delete());
                $deletedCount = $deletableTemplates->count();
                $totalCount = count($templateIds);

                if ($deletedCount === $totalCount) {
                    $message = 'All selected templates deleted successfully.';
                } else {
                    $skippedCount = $totalCount - $deletedCount;
                    $message = "{$deletedCount} templates deleted successfully. {$skippedCount} templates could not be deleted.";
                }
                break;
        }

        return response()->json([
            'success' => true,
            'message' => $message,
        ]);
    }
}
