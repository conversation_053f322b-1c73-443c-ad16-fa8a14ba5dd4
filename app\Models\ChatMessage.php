<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class ChatMessage extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'uuid',
        'chat_room_id',
        'user_id',
        'message_type',
        'content',
        'metadata',
        'is_ai_generated',
        'ai_confidence',
        'ai_model',
        'reply_to_message_id',
        'is_edited',
        'edit_count',
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_ai_generated' => 'boolean',
        'ai_confidence' => 'decimal:2',
        'is_edited' => 'boolean',
        'edit_count' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $attributes = [
        'message_type' => 'text',
        'is_ai_generated' => false,
        'is_edited' => false,
        'edit_count' => 0,
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = (string) Str::uuid();
            }
        });

        static::updating(function ($model) {
            if ($model->isDirty('content') && !$model->is_ai_generated) {
                $model->is_edited = true;
                $model->edit_count++;
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Scope for text messages.
     */
    public function scopeText($query)
    {
        return $query->where('message_type', 'text');
    }

    /**
     * Scope for AI generated messages.
     */
    public function scopeAiGenerated($query)
    {
        return $query->where('is_ai_generated', true);
    }

    /**
     * Scope for human messages.
     */
    public function scopeHuman($query)
    {
        return $query->where('is_ai_generated', false);
    }

    /**
     * Scope for file messages.
     */
    public function scopeFiles($query)
    {
        return $query->whereIn('message_type', ['file', 'image']);
    }

    /**
     * Scope for system messages.
     */
    public function scopeSystem($query)
    {
        return $query->where('message_type', 'system');
    }

    /**
     * Get the chat room this message belongs to.
     */
    public function chatRoom(): BelongsTo
    {
        return $this->belongsTo(ChatRoom::class);
    }

    /**
     * Get the user who sent this message.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the message this is replying to.
     */
    public function replyToMessage(): BelongsTo
    {
        return $this->belongsTo(ChatMessage::class, 'reply_to_message_id');
    }

    /**
     * Get replies to this message.
     */
    public function replies(): HasMany
    {
        return $this->hasMany(ChatMessage::class, 'reply_to_message_id');
    }

    /**
     * Get files attached to this message.
     */
    public function files(): HasMany
    {
        return $this->hasMany(ChatFile::class);
    }

    /**
     * Get AI conversation log for this message.
     */
    public function aiLog(): HasOne
    {
        return $this->hasOne(AiConversationLog::class);
    }

    /**
     * Check if message is from a staff member.
     */
    public function isFromStaff(): bool
    {
        return $this->user && $this->user->hasRole(['staff', 'admin']);
    }

    /**
     * Check if message is from a customer.
     */
    public function isFromCustomer(): bool
    {
        return !$this->isFromStaff() && !$this->is_ai_generated;
    }

    /**
     * Check if message is AI generated.
     */
    public function isAiGenerated(): bool
    {
        return $this->is_ai_generated;
    }

    /**
     * Check if message has files.
     */
    public function hasFiles(): bool
    {
        return $this->files()->exists();
    }

    /**
     * Check if message is edited.
     */
    public function isEdited(): bool
    {
        return $this->is_edited;
    }

    /**
     * Get sender name.
     */
    public function getSenderNameAttribute(): string
    {
        if ($this->is_ai_generated) {
            return 'AI Assistant';
        }

        if ($this->user) {
            return $this->user->name;
        }

        // For anonymous visitors
        $visitorInfo = $this->chatRoom->visitor_info ?? [];
        return $visitorInfo['name'] ?? 'Anonymous Visitor';
    }

    /**
     * Get sender type.
     */
    public function getSenderTypeAttribute(): string
    {
        if ($this->is_ai_generated) {
            return 'ai';
        }

        if ($this->user) {
            if ($this->user->hasRole(['staff', 'admin'])) {
                return 'staff';
            }
            return 'customer';
        }

        return 'visitor';
    }

    /**
     * Get formatted content based on message type.
     */
    public function getFormattedContentAttribute(): string
    {
        return match($this->message_type) {
            'file' => $this->formatFileContent(),
            'image' => $this->formatImageContent(),
            'system' => $this->formatSystemContent(),
            default => $this->content
        };
    }

    /**
     * Format file content.
     */
    private function formatFileContent(): string
    {
        $file = $this->files()->first();
        if (!$file) {
            return $this->content;
        }

        return "📎 {$file->original_filename} ({$this->formatFileSize($file->file_size)})";
    }

    /**
     * Format image content.
     */
    private function formatImageContent(): string
    {
        $file = $this->files()->first();
        if (!$file) {
            return $this->content;
        }

        return "🖼️ {$file->original_filename}";
    }

    /**
     * Format system content.
     */
    private function formatSystemContent(): string
    {
        return "ℹ️ {$this->content}";
    }

    /**
     * Format file size.
     */
    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Mark message as read by user.
     */
    public function markAsRead(User $user): void
    {
        // This would typically update a read_receipts table
        // For now, we'll just fire an event
        event(new \App\Events\MessageRead($this, $user));
    }
}
