<?php

namespace App\Services;

use App\Models\ChatRoom;
use App\Models\ChatAssignment;
use App\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ChatAssignmentService
{
    protected ActivityLogger $activityLogger;
    protected DashboardCacheService $cacheService;

    public function __construct(
        ActivityLogger $activityLogger,
        DashboardCacheService $cacheService
    ) {
        $this->activityLogger = $activityLogger;
        $this->cacheService = $cacheService;
    }

    /**
     * Automatically assign chat to best available staff member.
     */
    public function autoAssign(ChatRoom $room): ?ChatAssignment
    {
        return DB::transaction(function () use ($room) {
            $bestStaff = $this->findBestAvailableStaff($room);
            
            if (!$bestStaff) {
                $this->activityLogger->logActivity(
                    'chat_auto_assignment_failed',
                    "Auto-assignment failed for chat room {$room->id}: no available staff",
                    'failed',
                    'no_available_staff',
                    [
                        'room_id' => $room->id,
                        'room_priority' => $room->priority,
                        'room_language' => $room->language,
                        'room_type' => $room->type,
                    ]
                );
                return null;
            }

            return $this->assignToStaff($room, $bestStaff, 'automatic');
        });
    }

    /**
     * Manually assign chat to specific staff member.
     */
    public function manualAssign(ChatRoom $room, User $staff, ?User $assignedBy = null): ChatAssignment
    {
        return $this->assignToStaff($room, $staff, 'manual', $assignedBy);
    }

    /**
     * Transfer chat assignment to another staff member.
     */
    public function transferAssignment(
        ChatAssignment $assignment,
        User $newStaff,
        ?string $reason = null,
        ?User $transferredBy = null
    ): ChatAssignment {
        return DB::transaction(function () use ($assignment, $newStaff, $reason, $transferredBy) {
            // Create new assignment
            $newAssignment = $assignment->transferTo($newStaff, $reason);

            // Log activity
            $this->activityLogger->logActivity(
                'chat_assignment_transferred',
                "Chat assignment transferred from staff {$assignment->assigned_to} to {$newStaff->name} ({$newStaff->id})",
                'success',
                null,
                [
                    'assignment_id' => $newAssignment->id,
                    'from_staff_id' => $assignment->assigned_to,
                    'to_staff_id' => $newStaff->id,
                    'reason' => $reason,
                    'room_id' => $assignment->chat_room_id,
                ],
                [],
                null,
                $transferredBy ?? auth()->user()
            );

            // Clear caches
            $this->clearAssignmentCaches();

            return $newAssignment;
        });
    }

    /**
     * Complete chat assignment.
     */
    public function completeAssignment(ChatAssignment $assignment): bool
    {
        return DB::transaction(function () use ($assignment) {
            $success = $assignment->complete();

            if ($success) {
                $this->activityLogger->logActivity(
                    'chat_assignment_completed',
                    "Chat assignment completed for room {$assignment->chat_room_id} by staff {$assignment->assigned_to}",
                    'success',
                    null,
                    [
                        'assignment_id' => $assignment->id,
                        'staff_id' => $assignment->assigned_to,
                        'room_id' => $assignment->chat_room_id,
                        'duration_seconds' => $assignment->duration,
                    ]
                );

                $this->clearAssignmentCaches();
            }

            return $success;
        });
    }

    /**
     * Find best available staff member for assignment.
     */
    protected function findBestAvailableStaff(ChatRoom $room): ?User
    {
        return $this->cacheService->remember(
            "best_staff_for_room_{$room->id}",
            function () use ($room) {
                // Get all available staff members
                $availableStaff = $this->getAvailableStaff();

                if ($availableStaff->isEmpty()) {
                    return null;
                }

                // Score each staff member
                $scoredStaff = $availableStaff->map(function ($staff) use ($room) {
                    return [
                        'staff' => $staff,
                        'score' => $this->calculateStaffScore($staff, $room),
                    ];
                });

                // Sort by score (highest first) and return best match
                $bestMatch = $scoredStaff->sortByDesc('score')->first();
                
                return $bestMatch ? $bestMatch['staff'] : null;
            },
            60 // 1 minute cache
        );
    }

    /**
     * Get available staff members.
     */
    protected function getAvailableStaff(): Collection
    {
        return $this->cacheService->remember(
            'available_chat_staff',
            function () {
                return User::whereHas('role', function($query) {
                              $query->whereIn('name', ['staff', 'admin']);
                          })
                          ->where('is_active', true)
                          ->with(['role'])
                          ->get()
                          ->filter(function ($staff) {
                              return $this->isStaffAvailable($staff);
                          });
            },
            30 // 30 seconds cache
        );
    }

    /**
     * Check if staff member is available.
     */
    protected function isStaffAvailable(User $staff): bool
    {
        // Check current workload
        $currentAssignments = ChatAssignment::where('assigned_to', $staff->id)
                                           ->active()
                                           ->count();

        $maxAssignments = $this->getMaxAssignmentsForStaff($staff);
        
        if ($currentAssignments >= $maxAssignments) {
            return false;
        }

        // Check if staff is online (last seen within 5 minutes)
        if (!$staff->isOnline()) {
            return false;
        }

        // Check business hours
        if (!$this->isWithinBusinessHours()) {
            // Only admins available outside business hours
            return $staff->hasRole('admin');
        }

        return true;
    }

    /**
     * Calculate staff score for assignment.
     */
    protected function calculateStaffScore(User $staff, ChatRoom $room): int
    {
        $score = 100; // Base score

        // Workload factor (lower workload = higher score)
        $currentWorkload = ChatAssignment::calculateWorkloadScore($staff);
        $score -= $currentWorkload;

        // Language preference
        if ($this->staffSpeaksLanguage($staff, $room->language)) {
            $score += 20;
        }

        // Experience factor (based on completed assignments)
        $completedAssignments = ChatAssignment::where('assigned_to', $staff->id)
                                             ->completed()
                                             ->count();
        $score += min(30, $completedAssignments * 2);

        // Priority handling capability
        if ($room->priority >= 3 && $staff->hasRole('admin')) {
            $score += 15;
        }

        // Recent performance (average rating)
        $avgRating = $this->getStaffAverageRating($staff);
        if ($avgRating > 0) {
            $score += ($avgRating - 3) * 10; // +/- 20 points based on rating
        }

        return max(0, $score);
    }

    /**
     * Assign chat to staff member.
     */
    protected function assignToStaff(
        ChatRoom $room,
        User $staff,
        string $assignmentType,
        ?User $assignedBy = null
    ): ChatAssignment {
        return DB::transaction(function () use ($room, $staff, $assignmentType, $assignedBy) {
            $assignment = ChatAssignment::create([
                'chat_room_id' => $room->id,
                'assigned_to' => $staff->id,
                'assigned_by' => $assignedBy?->id ?? auth()->id(),
                'assignment_type' => $assignmentType,
                'workload_score' => ChatAssignment::calculateWorkloadScore($staff),
            ]);

            // Update room status if needed
            if ($room->status === 'waiting') {
                $room->update(['status' => 'active']);
            }

            // Log activity
            $this->activityLogger->logActivity(
                'chat_assignment_created',
                "Chat room {$room->id} assigned to {$staff->name} ({$staff->id})",
                'success',
                null,
                [
                    'assignment_id' => $assignment->id,
                    'staff_id' => $staff->id,
                    'staff_name' => $staff->name,
                    'room_id' => $room->id,
                    'assignment_type' => $assignmentType,
                    'workload_score' => $assignment->workload_score,
                ],
                [],
                null,
                $assignedBy ?? auth()->user()
            );

            // Clear caches
            $this->clearAssignmentCaches();

            return $assignment;
        });
    }

    /**
     * Get maximum assignments for staff member.
     */
    protected function getMaxAssignmentsForStaff(User $staff): int
    {
        if ($staff->hasRole('admin')) {
            return 10; // Admins can handle more
        }
        
        return 5; // Regular staff limit
    }

    /**
     * Check if staff speaks the language.
     */
    protected function staffSpeaksLanguage(User $staff, string $language): bool
    {
        // This would typically check staff language preferences
        // For now, assume all staff speak English and Afrikaans
        return in_array($language, ['en', 'af']);
    }

    /**
     * Check if within business hours.
     */
    protected function isWithinBusinessHours(): bool
    {
        $config = config('chat.business_hours');
        $now = now($config['timezone']);
        
        // Check if weekend
        if ($now->isWeekend() && $config['weekends'] === 'closed') {
            return false;
        }

        // Parse business hours
        $hours = $now->isWeekend() ? $config['weekends'] : $config['weekdays'];
        
        if ($hours === 'closed') {
            return false;
        }

        if ($hours === '24/7') {
            return true;
        }

        // Parse time range (e.g., "08:00-18:00")
        if (preg_match('/(\d{2}):(\d{2})-(\d{2}):(\d{2})/', $hours, $matches)) {
            $startHour = (int) $matches[1];
            $startMinute = (int) $matches[2];
            $endHour = (int) $matches[3];
            $endMinute = (int) $matches[4];
            
            $currentTime = $now->hour * 60 + $now->minute;
            $startTime = $startHour * 60 + $startMinute;
            $endTime = $endHour * 60 + $endMinute;
            
            return $currentTime >= $startTime && $currentTime <= $endTime;
        }

        return true; // Default to available
    }

    /**
     * Get staff average rating.
     */
    protected function getStaffAverageRating(User $staff): float
    {
        return $this->cacheService->remember(
            "staff_rating_{$staff->id}",
            function () use ($staff) {
                return \App\Models\ChatRating::getStaffAverageRating($staff);
            },
            3600 // 1 hour cache
        );
    }

    /**
     * Get assignment statistics.
     */
    public function getAssignmentStatistics(array $filters = []): array
    {
        $cacheKey = 'assignment_stats_' . md5(serialize($filters));
        
        return $this->cacheService->remember(
            $cacheKey,
            function () use ($filters) {
                $query = ChatAssignment::query();

                if (isset($filters['date_from'])) {
                    $query->where('created_at', '>=', $filters['date_from']);
                }

                if (isset($filters['date_to'])) {
                    $query->where('created_at', '<=', $filters['date_to']);
                }

                $total = $query->count();
                $automatic = $query->clone()->automatic()->count();
                $manual = $query->clone()->manual()->count();
                $completed = $query->clone()->completed()->count();
                $avgDuration = $query->clone()->completed()->avg('duration_seconds') ?? 0;

                return [
                    'total_assignments' => $total,
                    'automatic_assignments' => $automatic,
                    'manual_assignments' => $manual,
                    'completed_assignments' => $completed,
                    'completion_rate' => $total > 0 ? round(($completed / $total) * 100, 2) : 0,
                    'average_duration_seconds' => round($avgDuration, 2),
                ];
            },
            300 // 5 minutes cache
        );
    }

    /**
     * Clear assignment-related caches.
     */
    protected function clearAssignmentCaches(): void
    {
        $this->cacheService->forget('available_chat_staff');
        $this->cacheService->forgetPattern('best_staff_for_room_*');
        $this->cacheService->forgetPattern('assignment_stats_*');
    }
}
