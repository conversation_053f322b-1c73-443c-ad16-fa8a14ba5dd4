<?php

namespace Tests\Feature\Admin;

use App\Models\User;
use App\Models\Role;
use App\Models\ProjectApplication;
use App\Models\Service;
use App\Models\ActivityLog;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ProjectApplicationControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $admin;
    protected User $customer;
    protected Service $service;
    protected ProjectApplication $projectApplication;
    protected Role $adminRole;
    protected Role $customerRole;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles with unique slugs to avoid conflicts when running multiple tests
        $this->adminRole = Role::firstOrCreate(
            ['name' => 'admin'],
            [
                'slug' => 'admin',
                'description' => 'Administrator role with full permissions',
                'permissions' => [
                    'users' => ['create', 'read', 'update', 'delete'],
                    'roles' => ['create', 'read', 'update', 'delete'],
                    'projects' => ['create', 'read', 'update', 'delete'],
                    'orders' => ['create', 'read', 'update', 'delete'],
                    'services' => ['create', 'read', 'update', 'delete'],
                    'activity_logs' => ['read', 'delete'],
                    'dashboard' => ['access'],
                ],
                'is_active' => true,
            ]
        );

        $this->customerRole = Role::firstOrCreate(
            ['name' => 'customer'],
            [
                'slug' => 'customer',
                'description' => 'Customer role with basic permissions',
                'permissions' => [
                    'profile' => ['read', 'update'],
                    'projects' => ['create', 'read'],
                    'orders' => ['create', 'read'],
                    'dashboard' => ['access'],
                ],
                'is_active' => true,
            ]
        );

        // Create admin user
        $this->admin = User::factory()->create([
            'role_id' => $this->adminRole->id,
            'email_verified_at' => now(),
        ]);

        // Create customer user
        $this->customer = User::factory()->create([
            'role_id' => $this->customerRole->id,
            'email_verified_at' => now(),
        ]);

        // Create service
        $this->service = Service::factory()->create([
            'name' => 'Web Development',
            'is_active' => true,
        ]);

        // Create project application
        $this->projectApplication = ProjectApplication::factory()->create([
            'user_id' => $this->customer->id,
            'service_id' => $this->service->id,
            'title' => 'Test Project Application',
            'description' => 'This is a test project application',
            'status' => 'pending',
            'priority' => 'medium',
        ]);

        Storage::fake('public');
    }

    #[Test]
    public function admin_can_view_project_applications_index()
    {
        $this->actingAs($this->admin)
            ->get(route('admin.project-applications.index'))
            ->assertStatus(200)
            ->assertViewIs('admin.project-applications.index')
            ->assertSee($this->projectApplication->title);
    }

    #[Test]
    public function non_admin_cannot_access_project_applications()
    {
        $this->actingAs($this->customer)
            ->get(route('admin.project-applications.index'))
            ->assertStatus(403);
    }

    #[Test]
    public function admin_can_view_project_application_details()
    {
        $this->actingAs($this->admin)
            ->get(route('admin.project-applications.show', $this->projectApplication))
            ->assertStatus(200)
            ->assertViewIs('admin.project-applications.show')
            ->assertSee($this->projectApplication->title)
            ->assertSee($this->projectApplication->description);
    }

    #[Test]
    public function admin_can_update_project_application_status()
    {
        $this->actingAs($this->admin)
            ->patch(route('admin.project-applications.update-status', $this->projectApplication), [
                'status' => 'approved',
                'admin_notes' => 'Application looks good'
            ])
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'new_status' => 'approved'
            ]);

        $this->projectApplication->refresh();
        $this->assertEquals('approved', $this->projectApplication->status);
        $this->assertEquals('Application looks good', $this->projectApplication->admin_notes);
    }

    #[Test]
    public function admin_can_download_project_application_attachments()
    {
        // Create a project application with attachments
        $projectApplication = ProjectApplication::factory()->create([
            'user_id' => $this->customer->id,
            'attachments' => [
                [
                    'original_name' => 'test-document.pdf',
                    'stored_name' => 'stored-document.pdf',
                    'file_path' => 'project-applications/stored-document.pdf',
                    'file_size' => 1024,
                    'mime_type' => 'application/pdf'
                ]
            ]
        ]);

        // Create fake file
        Storage::disk('public')->put('project-applications/stored-document.pdf', 'fake pdf content');

        $this->actingAs($this->admin)
            ->get(route('admin.project-applications.download', [$projectApplication, 0]))
            ->assertStatus(200)
            ->assertHeader('Content-Type', 'application/pdf')
            ->assertHeader('Content-Disposition', 'attachment; filename=test-document.pdf');
    }

    #[Test]
    public function admin_can_get_project_application_statistics()
    {
        // Create additional project applications with different statuses
        ProjectApplication::factory()->create(['status' => 'pending']);
        ProjectApplication::factory()->create(['status' => 'approved']);
        ProjectApplication::factory()->create(['status' => 'rejected']);

        $this->actingAs($this->admin)
            ->get(route('admin.project-applications.statistics'))
            ->assertStatus(200)
            ->assertJsonStructure([
                'total',
                'pending',
                'under_review',
                'approved',
                'rejected',
                'in_progress',
                'completed',
                'cancelled'
            ]);
    }

    #[Test]
    public function admin_can_filter_project_applications()
    {
        // Create additional project applications
        ProjectApplication::factory()->create([
            'status' => 'approved',
            'priority' => 'high'
        ]);

        $this->actingAs($this->admin)
            ->get(route('admin.project-applications.index', [
                'status' => 'pending',
                'priority' => 'medium'
            ]))
            ->assertStatus(200)
            ->assertSee($this->projectApplication->title);
    }

    #[Test]
    public function admin_can_search_project_applications()
    {
        $this->actingAs($this->admin)
            ->get(route('admin.project-applications.index', [
                'search' => 'Test Project'
            ]))
            ->assertStatus(200)
            ->assertSee($this->projectApplication->title);
    }

    #[Test]
    public function status_update_creates_activity_log()
    {
        $this->actingAs($this->admin)
            ->patch(route('admin.project-applications.update-status', $this->projectApplication), [
                'status' => 'approved',
                'admin_notes' => 'Application approved'
            ]);

        $this->assertDatabaseHas('activity_logs', [
            'user_email' => $this->admin->email,
            'activity_type' => 'admin_project_application_status_update',
            'activity_description' => 'Updated project application status'
        ]);
    }

    #[Test]
    public function admin_can_update_project_application()
    {
        $updateData = [
            'title' => 'Updated Project Title',
            'description' => 'Updated description',
            'status' => 'pending',
            'priority' => 'high',
            'estimated_cost' => 5000.00,
            'estimated_hours' => 100,
            'admin_notes' => 'Updated admin notes'
        ];

        $this->actingAs($this->admin)
            ->put(route('admin.project-applications.update', $this->projectApplication), $updateData)
            ->assertRedirect(route('admin.project-applications.show', $this->projectApplication))
            ->assertSessionHas('success');

        $this->projectApplication->refresh();
        $this->assertEquals('Updated Project Title', $this->projectApplication->title);
        $this->assertEquals('high', $this->projectApplication->priority);
        $this->assertEquals(5000.00, $this->projectApplication->estimated_cost);
    }

    #[Test]
    public function admin_can_delete_project_application()
    {
        $this->actingAs($this->admin)
            ->delete(route('admin.project-applications.destroy', $this->projectApplication))
            ->assertRedirect(route('admin.project-applications.index'))
            ->assertSessionHas('success');

        $this->assertSoftDeleted('project_applications', [
            'id' => $this->projectApplication->id
        ]);
    }

    #[Test]
    public function invalid_status_update_returns_error()
    {
        $this->actingAs($this->admin)
            ->patch(route('admin.project-applications.update-status', $this->projectApplication), [
                'status' => 'invalid_status'
            ])
            ->assertStatus(422);
    }

    #[Test]
    public function download_nonexistent_attachment_returns_404()
    {
        $this->actingAs($this->admin)
            ->get(route('admin.project-applications.download', [$this->projectApplication, 999]))
            ->assertStatus(404);
    }

    #[Test]
    public function admin_actions_are_logged_with_proper_risk_scores()
    {
        // Test status update logging
        $this->actingAs($this->admin)
            ->patch(route('admin.project-applications.update-status', $this->projectApplication), [
                'status' => 'approved'
            ]);

        $log = ActivityLog::where('activity_type', 'admin_project_application_status_update')->first();
        $this->assertNotNull($log);
        $this->assertLessThanOrEqual(30, $log->risk_score); // Admin actions should have low risk

        // Test view logging
        $this->actingAs($this->admin)
            ->get(route('admin.project-applications.show', $this->projectApplication));

        $viewLog = ActivityLog::where('activity_type', 'admin_project_application_view')->first();
        $this->assertNotNull($viewLog);
        $this->assertLessThanOrEqual(10, $viewLog->risk_score); // View actions should have very low risk
    }

    #[Test]
    public function project_application_statistics_are_accurate()
    {
        // Create project applications with known statuses
        ProjectApplication::factory()->count(3)->create(['status' => 'pending']);
        ProjectApplication::factory()->count(2)->create(['status' => 'approved']);
        ProjectApplication::factory()->count(1)->create(['status' => 'rejected']);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.project-applications.statistics'))
            ->assertStatus(200);

        $data = $response->json();

        // Including the one created in setUp
        $this->assertEquals(7, $data['total']); // 1 + 3 + 2 + 1
        $this->assertEquals(4, $data['pending']); // 1 + 3
        $this->assertEquals(2, $data['approved']);
        $this->assertEquals(1, $data['rejected']);
    }
}
