<?php

namespace App\Events;

use App\Models\User;
use App\Models\Role;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PermissionChanged
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public User $user;
    public ?Role $oldRole;
    public Role $newRole;
    public User $changedBy;
    public string $changeType;
    public array $metadata;

    /**
     * Create a new event instance.
     */
    public function __construct(
        User $user,
        ?Role $oldRole,
        Role $newRole,
        User $changedBy,
        string $changeType = 'role_change',
        array $metadata = []
    ) {
        $this->user = $user;
        $this->oldRole = $oldRole;
        $this->newRole = $newRole;
        $this->changedBy = $changedBy;
        $this->changeType = $changeType;
        $this->metadata = $metadata;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('admin.permissions'),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'user' => [
                'id' => $this->user->id,
                'name' => $this->user->first_name . ' ' . $this->user->last_name,
                'email' => $this->user->email,
            ],
            'old_role' => $this->oldRole ? [
                'id' => $this->oldRole->id,
                'name' => $this->oldRole->name,
            ] : null,
            'new_role' => [
                'id' => $this->newRole->id,
                'name' => $this->newRole->name,
            ],
            'changed_by' => [
                'id' => $this->changedBy->id,
                'name' => $this->changedBy->first_name . ' ' . $this->changedBy->last_name,
            ],
            'change_type' => $this->changeType,
            'timestamp' => now()->toISOString(),
            'metadata' => $this->metadata,
        ];
    }
}
