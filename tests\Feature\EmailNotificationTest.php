<?php

namespace Tests\Feature;

use App\Mail\JobApplicationReceived;
use App\Mail\OrderConfirmation;
use App\Mail\OrderStatusUpdate;
use App\Mail\PaymentReceived;
use App\Mail\ProjectApplicationReceived;
use App\Models\JobApplication;
use App\Models\Order;
use App\Models\Payment;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProjectApplication;
use App\Models\User;
use App\Services\EmailNotificationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;

class EmailNotificationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $emailService;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        Mail::fake();
        $this->emailService = new EmailNotificationService();
        $this->user = User::factory()->create();
    }
    #[Test]
    public function it_sends_order_confirmation_email()
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'email' => '<EMAIL>',
            'order_number' => 'ORD-12345',
        ]);

        $result = $this->emailService->sendOrderConfirmation($order);

        $this->assertTrue($result);
        Mail::assertQueued(OrderConfirmation::class, function ($mail) use ($order) {
            return $mail->order->id === $order->id;
        });
    }
    #[Test]
    public function it_sends_order_status_update_email()
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'email' => '<EMAIL>',
            'status' => 'shipped',
        ]);

        $result = $this->emailService->sendOrderStatusUpdate($order, 'processing');

        $this->assertTrue($result);
        Mail::assertQueued(OrderStatusUpdate::class, function ($mail) use ($order) {
            return $mail->order->id === $order->id && $mail->previousStatus === 'processing';
        });
    }
    #[Test]
    public function it_sends_payment_received_email()
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'email' => '<EMAIL>',
            'payment_status' => 'paid',
        ]);

        $payment = Payment::factory()->create([
            'order_id' => $order->id,
            'amount' => $order->total_amount,
        ]);

        $result = $this->emailService->sendPaymentReceived($order, $payment);

        $this->assertTrue($result);
        Mail::assertQueued(PaymentReceived::class, function ($mail) use ($order, $payment) {
            return $mail->order->id === $order->id && $mail->payment->id === $payment->id;
        });
    }
    #[Test]
    public function it_sends_job_application_received_email()
    {
        $application = JobApplication::factory()->create([
            'email' => '<EMAIL>',
            'reference_number' => 'JOB-12345',
        ]);

        $result = $this->emailService->sendJobApplicationReceived($application);

        $this->assertTrue($result);
        Mail::assertQueued(JobApplicationReceived::class, function ($mail) use ($application) {
            return $mail->application->id === $application->id;
        });
    }
    #[Test]
    public function it_sends_project_application_received_email()
    {
        $application = ProjectApplication::factory()->create([
            'email' => '<EMAIL>',
            'reference_number' => 'PROJ-12345',
        ]);

        $result = $this->emailService->sendProjectApplicationReceived($application);

        $this->assertTrue($result);
        Mail::assertQueued(ProjectApplicationReceived::class, function ($mail) use ($application) {
            return $mail->application->id === $application->id;
        });
    }
    #[Test]
    public function it_handles_email_sending_failures_gracefully()
    {
        // Mock Mail to throw an exception to simulate failure
        Mail::shouldReceive('to')->andThrow(new \Exception('SMTP connection failed'));

        $order = Order::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $result = $this->emailService->sendOrderConfirmation($order);

        // Should return false but not throw exception
        $this->assertFalse($result);
    }
    #[Test]
    public function order_confirmation_email_contains_correct_data()
    {
        $category = ProductCategory::factory()->create();
        $product = Product::factory()->create(['price' => 100.00]);
        $product->categories()->attach($category);

        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'email' => '<EMAIL>',
            'order_number' => 'ORD-12345',
            'total_amount' => 115.00,
        ]);

        $order->items()->create([
            'product_id' => $product->id,
            'quantity' => 1,
            'unit_price' => 100.00,
            'total_price' => 100.00,
            'product_snapshot' => [
                'name' => $product->name,
                'price' => $product->price,
                'sku' => $product->sku,
            ],
        ]);

        $this->emailService->sendOrderConfirmation($order);

        Mail::assertQueued(OrderConfirmation::class, function ($mail) use ($order) {
            $this->assertEquals('Order Confirmation - ' . $order->order_number, $mail->envelope()->subject);
            $this->assertEquals($order->id, $mail->order->id);
            return true;
        });
    }
    #[Test]
    public function payment_received_email_shows_correct_amount()
    {
        $order = Order::factory()->create([
            'email' => '<EMAIL>',
            'total_amount' => 250.00,
            'payment_status' => 'paid',
        ]);

        $payment = Payment::factory()->create([
            'order_id' => $order->id,
            'amount' => 250.00,
            'transaction_id' => 'txn_12345',
        ]);

        $this->emailService->sendPaymentReceived($order, $payment);

        Mail::assertQueued(PaymentReceived::class, function ($mail) use ($order, $payment) {
            $this->assertEquals('Payment Received - ' . $order->order_number, $mail->envelope()->subject);
            $this->assertEquals($payment->amount, $mail->payment->amount);
            return true;
        });
    }
    #[Test]
    public function job_application_email_includes_reference_number()
    {
        $application = JobApplication::factory()->create([
            'email' => '<EMAIL>',
            'reference_number' => 'JOB-98765',
            'first_name' => 'Jane',
            'last_name' => 'Smith',
        ]);

        $this->emailService->sendJobApplicationReceived($application);

        Mail::assertQueued(JobApplicationReceived::class, function ($mail) use ($application) {
            $this->assertEquals('Job Application Received - ' . $application->reference_number, $mail->envelope()->subject);
            $this->assertEquals($application->reference_number, $mail->application->reference_number);
            return true;
        });
    }
    #[Test]
    public function project_application_email_includes_project_details()
    {
        $application = ProjectApplication::factory()->create([
            'email' => '<EMAIL>',
            'reference_number' => 'PROJ-54321',
            'full_name' => 'Bob Johnson',
            'project_type' => 'Website Development',
            'estimated_budget' => '$5,000 - $10,000',
        ]);

        $this->emailService->sendProjectApplicationReceived($application);

        Mail::assertQueued(ProjectApplicationReceived::class, function ($mail) use ($application) {
            $this->assertEquals('Project Application Received - ' . $application->reference_number, $mail->envelope()->subject);
            $this->assertEquals($application->project_type, $mail->application->project_type);
            $this->assertEquals($application->budget_range, $mail->application->budget_range);
            return true;
        });
    }
    #[Test]
    public function order_status_update_email_shows_status_change()
    {
        $order = Order::factory()->create([
            'email' => '<EMAIL>',
            'status' => 'delivered',
        ]);

        $this->emailService->sendOrderStatusUpdate($order, 'shipped');

        Mail::assertQueued(OrderStatusUpdate::class, function ($mail) use ($order) {
            $this->assertStringContainsString('delivered', strtolower($mail->envelope()->subject));
            $this->assertEquals('shipped', $mail->previousStatus);
            $this->assertEquals('delivered', $mail->order->status);
            return true;
        });
    }
    #[Test]
    public function emails_are_queued_for_background_processing()
    {
        $order = Order::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $this->emailService->sendOrderConfirmation($order);

        Mail::assertQueued(OrderConfirmation::class);
    }
    #[Test]
    public function bulk_email_notifications_work()
    {
        $emails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
        $order = Order::factory()->create();
        $mailable = new OrderConfirmation($order);

        $result = $this->emailService->sendBulkNotifications($emails, $mailable);

        $this->assertTrue($result);
        Mail::assertQueued(OrderConfirmation::class, 3);
    }
}
