#!/bin/bash

# =============================================================================
# ChiSolution Digital Agency - Staging Deployment Script
# =============================================================================

set -e  # Exit on any error

echo "🚀 Starting Staging Deployment..."

# Configuration
APP_DIR="/var/www/staging.chisolution"
BACKUP_DIR="/var/backups/chisolution-staging"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Pre-deployment checks
log_info "Running pre-deployment checks..."

# Check if we're in the correct directory
if [ ! -f "artisan" ]; then
    log_error "artisan file not found. Are you in the Laravel project root?"
    exit 1
fi

# Check if .env.staging exists
if [ ! -f ".env.staging" ]; then
    log_error ".env.staging file not found!"
    exit 1
fi

# Backup current deployment
log_info "Creating backup..."
mkdir -p $BACKUP_DIR
if [ -d "$APP_DIR" ]; then
    tar -czf "$BACKUP_DIR/staging_backup_$TIMESTAMP.tar.gz" -C "$APP_DIR" .
    log_info "Backup created: $BACKUP_DIR/staging_backup_$TIMESTAMP.tar.gz"
fi

# Put application in maintenance mode
log_info "Enabling maintenance mode..."
php artisan down --message="Updating staging environment..." --retry=30

# Git operations
log_info "Pulling latest code from repository..."
git fetch origin
git reset --hard origin/develop

# Copy staging environment file
log_info "Setting up staging environment..."
cp .env.staging .env

# Install/update dependencies
log_info "Installing Composer dependencies..."
composer install --optimize-autoloader --no-interaction

log_info "Installing NPM dependencies..."
npm ci

# Build assets
log_info "Building staging assets..."
npm run build

# Database operations
log_info "Running database migrations..."
php artisan migrate --force

# Seed test data if needed
log_info "Seeding test data..."
php artisan db:seed --class=TestDataSeeder --force

# Clear and cache configurations
log_info "Optimizing application..."
php artisan config:cache
php artisan route:cache
php artisan view:clear  # Don't cache views in staging for easier debugging

# Set proper permissions
log_info "Setting file permissions..."
chown -R www-data:www-data storage bootstrap/cache
chmod -R 775 storage bootstrap/cache

# Queue restart (if using queue workers)
log_info "Restarting queue workers..."
php artisan queue:restart

# Clear application cache
log_info "Clearing application cache..."
php artisan cache:clear

# Disable maintenance mode
log_info "Disabling maintenance mode..."
php artisan up

# Health check
log_info "Running health check..."
if curl -f -s http://staging.chisolution.co.za/up > /dev/null; then
    log_info "✅ Health check passed!"
else
    log_error "❌ Health check failed!"
    log_warning "Check the application logs"
fi

# Run tests
log_info "Running test suite..."
php artisan test --parallel

# Cleanup old backups (keep last 10 for staging)
log_info "Cleaning up old backups..."
cd $BACKUP_DIR
ls -t staging_backup_*.tar.gz | tail -n +11 | xargs -r rm

log_info "🎉 Staging deployment completed successfully!"
log_info "Backup location: $BACKUP_DIR/staging_backup_$TIMESTAMP.tar.gz"

echo ""
echo "Staging environment ready for testing:"
echo "- URL: https://staging.chisolution.co.za"
echo "- Admin: <EMAIL> / staging_password"
echo "- Check logs: tail -f storage/logs/laravel.log"
echo "- Test payment flows with test cards"
