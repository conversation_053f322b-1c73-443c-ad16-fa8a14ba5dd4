/**
 * Standalone Chat Widget
 * 
 * A self-contained chat widget that doesn't require build tools
 */

(function() {
    'use strict';

    console.log('Chat widget standalone script loaded');

    class SimpleChatWidget {
        constructor(options = {}) {
            this.options = {
                apiBaseUrl: options.apiBaseUrl || '/api/v1/chat',
                position: options.position || 'bottom-right',
                enableNotifications: options.enableNotifications || false,
                enableSound: options.enableSound || false,
                ...options
            };

            this.isOpen = false;
            this.currentRoom = null;
            this.messages = [];
            this.unreadCount = 0;

            this.init();
        }

        init() {
            this.createWidget();
            this.attachEventListeners();
            this.restoreChatState();
        }

        createWidget() {
            // Remove existing widget if any
            const existingWidget = document.getElementById('simple-chat-widget');
            if (existingWidget) {
                existingWidget.remove();
            }

            const widgetHTML = `
                <div id="simple-chat-widget" style="position: fixed; bottom: 20px; right: 20px; z-index: 999999; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                    <!-- Chat Toggle Button -->
                    <div id="chat-toggle" style="width: 60px; height: 60px; background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%); border-radius: 50%; cursor: pointer; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease; position: relative;">
                        <svg id="chat-icon" style="width: 24px; height: 24px; color: white; transition: all 0.3s ease;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                        </svg>
                        <svg id="close-icon" style="width: 24px; height: 24px; color: white; position: absolute; opacity: 0; transform: rotate(90deg); transition: all 0.3s ease;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                        <div id="unread-badge" style="position: absolute; top: -5px; right: -5px; background: #ef4444; color: white; border-radius: 50%; min-width: 20px; height: 20px; display: none; align-items: center; justify-content: center; font-size: 11px; font-weight: 600; border: 2px solid white;">
                            <span id="unread-count">0</span>
                        </div>
                    </div>

                    <!-- Chat Window -->
                    <div id="chat-window" style="position: absolute; bottom: 80px; right: 0; width: 380px; height: 600px; background: white; border-radius: 16px; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1); display: none; flex-direction: column; overflow: hidden;">
                        <!-- Header -->
                        <div style="background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%); color: white; padding: 16px 20px; display: flex; align-items: center; justify-content: space-between; border-radius: 16px 16px 0 0;">
                            <div>
                                <div style="font-size: 16px; font-weight: 600; margin-bottom: 2px;">Live Support</div>
                                <div id="chat-status" style="font-size: 12px; opacity: 0.9;">We're here to help!</div>
                            </div>
                            <button id="close-btn" style="background: rgba(255, 255, 255, 0.2); border: none; border-radius: 6px; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; cursor: pointer; color: white;">
                                <svg style="width: 16px; height: 16px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="18" y1="6" x2="6" y2="18"></line>
                                    <line x1="6" y1="6" x2="18" y2="18"></line>
                                </svg>
                            </button>
                        </div>

                        <!-- Messages -->
                        <div id="chat-messages" style="flex: 1; padding: 20px; overflow-y: auto;">
                            <div id="welcome-message" style="display: flex; align-items: flex-start; gap: 12px; margin-bottom: 20px; padding: 16px; background: #f8fafc; border-radius: 12px; border-left: 4px solid #0891b2;">
                                <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; flex-shrink: 0;">
                                    <svg style="width: 20px; height: 20px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                        <circle cx="12" cy="7" r="4"></circle>
                                    </svg>
                                </div>
                                <div>
                                    <h4 style="margin: 0 0 4px 0; font-size: 16px; font-weight: 600; color: #1f2937;">Welcome to Live Support!</h4>
                                    <p style="margin: 0; color: #6b7280; font-size: 14px;">How can we help you today?</p>
                                </div>
                            </div>
                        </div>

                        <!-- Input Area -->
                        <div style="border-top: 1px solid #e5e7eb; background: white; border-radius: 0 0 16px 16px; padding: 16px 20px;">
                            <div style="display: flex; align-items: flex-end; gap: 12px;">
                                <textarea id="chat-input" placeholder="Type your message..." style="flex: 1; border: 1px solid #d1d5db; border-radius: 20px; padding: 10px 16px; font-size: 14px; resize: none; outline: none; min-height: 40px; max-height: 120px; font-family: inherit;" rows="1"></textarea>
                                <button id="send-btn" style="width: 36px; height: 36px; border: none; background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; color: white;">
                                    <svg style="width: 18px; height: 18px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <line x1="22" y1="2" x2="11" y2="13"></line>
                                        <polygon points="22,2 15,22 11,13 2,9 22,2"></polygon>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', widgetHTML);
        }

        attachEventListeners() {
            // Toggle button
            document.getElementById('chat-toggle').addEventListener('click', () => {
                this.toggle();
            });

            // Close button
            document.getElementById('close-btn').addEventListener('click', () => {
                this.close();
            });

            // Send button
            document.getElementById('send-btn').addEventListener('click', () => {
                this.sendMessage();
            });

            // Input handling
            const chatInput = document.getElementById('chat-input');
            chatInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });

            // Keyboard shortcuts
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.isOpen) {
                    this.close();
                }
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    this.toggle();
                }
            });
        }

        toggle() {
            if (this.isOpen) {
                this.close();
            } else {
                this.open();
            }
        }

        async open() {
            if (!this.currentRoom) {
                await this.createRoom();
            }

            const chatWindow = document.getElementById('chat-window');
            const chatToggle = document.getElementById('chat-toggle');
            const chatIcon = document.getElementById('chat-icon');
            const closeIcon = document.getElementById('close-icon');
            
            chatWindow.style.display = 'flex';
            chatToggle.style.background = 'linear-gradient(135deg, #14b8a6 0%, #0d9488 100%)';
            chatIcon.style.opacity = '0';
            chatIcon.style.transform = 'rotate(-90deg)';
            closeIcon.style.opacity = '1';
            closeIcon.style.transform = 'rotate(0deg)';
            
            this.isOpen = true;
            
            // Clear unread count
            this.unreadCount = 0;
            this.updateUnreadBadge();
            
            // Focus input
            setTimeout(() => {
                document.getElementById('chat-input').focus();
            }, 300);
        }

        close() {
            const chatWindow = document.getElementById('chat-window');
            const chatToggle = document.getElementById('chat-toggle');
            const chatIcon = document.getElementById('chat-icon');
            const closeIcon = document.getElementById('close-icon');
            
            chatWindow.style.display = 'none';
            chatToggle.style.background = 'linear-gradient(135deg, #0891b2 0%, #0e7490 100%)';
            chatIcon.style.opacity = '1';
            chatIcon.style.transform = 'rotate(0deg)';
            closeIcon.style.opacity = '0';
            closeIcon.style.transform = 'rotate(90deg)';
            
            this.isOpen = false;
        }

        async createRoom() {
            try {
                document.getElementById('chat-status').textContent = 'Connecting...';
                
                const user = this.getAuthenticatedUser();
                const payload = this.buildRoomPayload(user);
                
                console.log('Creating chat room...', payload);

                const response = await fetch(`${this.options.apiBaseUrl}/rooms`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(payload)
                });

                console.log('Room creation response:', response.status, response.statusText);

                if (!response.ok) {
                    const errorData = await response.text();
                    console.error('Room creation failed:', errorData);
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('Room created successfully:', data);

                if (!data.success || !data.data?.room) {
                    throw new Error('Invalid response format from server');
                }

                this.currentRoom = data.data.room;
                document.getElementById('chat-status').textContent = 'Connected';
                this.saveChatState();
                
            } catch (error) {
                console.error('Failed to create chat room:', error);
                document.getElementById('chat-status').textContent = 'Connection failed';
                this.showError('Failed to connect to chat. Please try again.');
            }
        }

        buildRoomPayload(user) {
            if (user) {
                return {
                    type: 'visitor',
                    priority: 1,
                    language: document.documentElement.lang || 'en',
                    visitor_info: {
                        name: user.first_name || user.name || 'User',
                        email: user.email,
                        user_id: user.id,
                        page: window.location.href,
                        user_agent: navigator.userAgent,
                        referrer: document.referrer || '',
                        timestamp: new Date().toISOString(),
                        authenticated: true
                    },
                    metadata: {
                        source: 'chat_widget',
                        version: '1.0.0',
                        user_type: 'authenticated'
                    }
                };
            } else {
                return {
                    type: 'visitor',
                    priority: 1,
                    language: document.documentElement.lang || 'en',
                    visitor_info: {
                        name: 'Website Visitor',
                        email: '<EMAIL>',
                        page: window.location.href,
                        user_agent: navigator.userAgent,
                        referrer: document.referrer || '',
                        timestamp: new Date().toISOString(),
                        authenticated: false
                    },
                    metadata: {
                        source: 'chat_widget',
                        version: '1.0.0',
                        user_type: 'anonymous'
                    }
                };
            }
        }

        getAuthenticatedUser() {
            try {
                const userMeta = document.querySelector('meta[name="user"]');
                if (userMeta) {
                    return JSON.parse(userMeta.getAttribute('content'));
                }
                return null;
            } catch (error) {
                console.warn('Failed to get authenticated user:', error);
                return null;
            }
        }

        async sendMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();

            if (!message || !this.currentRoom) return;

            try {
                // Add message to UI immediately
                this.addMessage({
                    content: message,
                    message_type: 'text',
                    user: { name: 'You' },
                    created_at: new Date().toISOString(),
                    is_own: true
                });

                // Clear input
                input.value = '';

                // Send to API
                const response = await fetch(`${this.options.apiBaseUrl}/rooms/${this.currentRoom.uuid}/messages`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        content: message,
                        message_type: 'text'
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                console.log('Message sent successfully');

            } catch (error) {
                console.error('Failed to send message:', error);
                this.showError('Failed to send message. Please try again.');
            }
        }

        addMessage(message) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageElement = document.createElement('div');
            
            const isOwn = message.is_own;
            const timestamp = new Date(message.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            
            messageElement.style.cssText = `
                display: flex;
                align-items: flex-start;
                gap: 8px;
                margin-bottom: 16px;
                ${isOwn ? 'flex-direction: row-reverse;' : ''}
            `;
            
            messageElement.innerHTML = `
                ${!isOwn ? `
                    <div style="width: 32px; height: 32px; background: #e5e7eb; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #6b7280; flex-shrink: 0;">
                        <svg style="width: 16px; height: 16px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                            <circle cx="12" cy="7" r="4"></circle>
                        </svg>
                    </div>
                ` : ''}
                <div style="max-width: 280px; border-radius: 18px; padding: 12px 16px; ${isOwn ? 'background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%); color: white;' : 'background: #f1f5f9; color: #374151;'}">
                    <div style="word-wrap: break-word; line-height: 1.4;">${this.escapeHtml(message.content)}</div>
                    <div style="margin-top: 4px; font-size: 11px; opacity: 0.7;">${timestamp}</div>
                </div>
            `;

            messagesContainer.appendChild(messageElement);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        showError(message) {
            this.addMessage({
                content: message,
                message_type: 'system',
                user: { name: 'System' },
                created_at: new Date().toISOString(),
                is_own: false
            });
        }

        updateUnreadBadge() {
            const badge = document.getElementById('unread-badge');
            const count = document.getElementById('unread-count');
            
            if (this.unreadCount > 0) {
                count.textContent = this.unreadCount > 99 ? '99+' : this.unreadCount;
                badge.style.display = 'flex';
            } else {
                badge.style.display = 'none';
            }
        }

        escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        saveChatState() {
            if (this.currentRoom) {
                localStorage.setItem('chat_room_uuid', this.currentRoom.uuid);
            }
        }

        restoreChatState() {
            const roomUuid = localStorage.getItem('chat_room_uuid');
            if (roomUuid) {
                this.currentRoom = { uuid: roomUuid };
            }
        }
    }

    // Initialize chat widget when DOM is loaded
    console.log('Chat widget standalone script loaded, waiting for DOM...');

    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, checking if should initialize chat widget...');
        console.log('Current pathname:', window.location.pathname);

        // Only initialize if not in admin area
        if (!window.location.pathname.includes('/admin')) {
            try {
                console.log('Initializing simple chat widget...');
                window.chatWidget = new SimpleChatWidget({
                    apiBaseUrl: '/api/v1/chat',
                    enableNotifications: false,
                    enableSound: false
                });

                // Global helper functions
                window.openChat = function() {
                    console.log('openChat called');
                    if (window.chatWidget) {
                        window.chatWidget.open();
                    }
                };

                window.closeChat = function() {
                    console.log('closeChat called');
                    if (window.chatWidget) {
                        window.chatWidget.close();
                    }
                };

                window.toggleChat = function() {
                    console.log('toggleChat called');
                    if (window.chatWidget) {
                        window.chatWidget.toggle();
                    }
                };

                console.log('Simple chat widget initialized successfully');
                console.log('Chat widget object:', window.chatWidget);
            } catch (error) {
                console.error('Failed to initialize simple chat widget:', error);

                // Create fallback functions even if widget fails
                window.openChat = function() {
                    console.error('Chat widget failed to initialize');
                };
                window.closeChat = function() {
                    console.error('Chat widget failed to initialize');
                };
                window.toggleChat = function() {
                    console.error('Chat widget failed to initialize');
                };
            }
        } else {
            console.log('Skipping chat widget initialization - in admin area');
        }
    });

    // Global fallback functions (available immediately)
    window.openChat = window.openChat || function() {
        console.log('Chat widget not ready yet');
    };

    window.closeChat = window.closeChat || function() {
        console.log('Chat widget not ready yet');
    };

    window.toggleChat = window.toggleChat || function() {
        console.log('Chat widget not ready yet');
    };

})();
