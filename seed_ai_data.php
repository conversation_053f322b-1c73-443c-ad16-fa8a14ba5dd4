<?php

require_once 'vendor/autoload.php';

use App\Models\AiTrainingData;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

$trainingData = [
    // Greetings
    [
        'intent' => 'greeting',
        'input_text' => 'hello',
        'expected_response' => 'Hello! Welcome to ChiSolution. How can I help you today?',
        'language' => 'en',
        'confidence_threshold' => 0.95,
        'category' => 'greeting',
        'tags' => ['greeting', 'welcome'],
    ],
    [
        'intent' => 'greeting',
        'input_text' => 'hi',
        'expected_response' => 'Hi there! I\'m here to assist you. What can I help you with?',
        'language' => 'en',
        'confidence_threshold' => 0.95,
        'category' => 'greeting',
        'tags' => ['greeting', 'welcome'],
    ],
    [
        'intent' => 'greeting',
        'input_text' => 'hey',
        'expected_response' => 'Hey! Thanks for reaching out to ChiSolution. How can I assist you today?',
        'language' => 'en',
        'confidence_threshold' => 0.95,
        'category' => 'greeting',
        'tags' => ['greeting', 'welcome'],
    ],
    [
        'intent' => 'greeting',
        'input_text' => 'good morning',
        'expected_response' => 'Good morning! Welcome to ChiSolution. What can I help you with today?',
        'language' => 'en',
        'confidence_threshold' => 0.95,
        'category' => 'greeting',
        'tags' => ['greeting', 'welcome', 'morning'],
    ],

    // Services
    [
        'intent' => 'services',
        'input_text' => 'what services do you offer',
        'expected_response' => 'ChiSolution offers web development, mobile app development, digital marketing, and custom software solutions. Would you like to know more about any specific service?',
        'language' => 'en',
        'confidence_threshold' => 0.90,
        'category' => 'services',
        'tags' => ['services', 'offerings'],
    ],
    [
        'intent' => 'services',
        'input_text' => 'what do you do',
        'expected_response' => 'We\'re a digital agency specializing in websites, mobile apps, and digital solutions. We help businesses grow their online presence. What type of project are you interested in?',
        'language' => 'en',
        'confidence_threshold' => 0.85,
        'category' => 'services',
        'tags' => ['services', 'about'],
    ],

    // Business Hours
    [
        'intent' => 'business_hours',
        'input_text' => 'what are your hours',
        'expected_response' => 'We\'re available Monday to Friday, 9 AM to 6 PM (SAST). For urgent matters, you can reach us via email anytime. How can I help you today?',
        'language' => 'en',
        'confidence_threshold' => 0.90,
        'category' => 'business_info',
        'tags' => ['hours', 'availability'],
    ],

    // Contact Information
    [
        'intent' => 'contact',
        'input_text' => 'how can i contact you',
        'expected_response' => 'You can reach us at +************** or email <NAME_EMAIL>. You\'re also welcome to continue chatting here! What would you prefer?',
        'language' => 'en',
        'confidence_threshold' => 0.90,
        'category' => 'contact',
        'tags' => ['contact', 'phone', 'email'],
    ],

    // Support
    [
        'intent' => 'support',
        'input_text' => 'i need help',
        'expected_response' => 'I\'m here to help! Can you tell me more about what you need assistance with? Whether it\'s about our services, a current project, or something else, I\'ll do my best to assist you.',
        'language' => 'en',
        'confidence_threshold' => 0.85,
        'category' => 'support',
        'tags' => ['support', 'help'],
    ],

    // About Company
    [
        'intent' => 'about',
        'input_text' => 'who are you',
        'expected_response' => 'I\'m an AI assistant for ChiSolution, a digital agency that creates amazing websites, mobile apps, and digital solutions. We help businesses succeed online. What would you like to know about us?',
        'language' => 'en',
        'confidence_threshold' => 0.90,
        'category' => 'about',
        'tags' => ['about', 'company', 'ai'],
    ],
];

$count = 0;
foreach ($trainingData as $data) {
    try {
        AiTrainingData::updateOrCreate(
            [
                'intent' => $data['intent'],
                'input_text' => $data['input_text'],
                'language' => $data['language'],
            ],
            $data
        );
        $count++;
        echo "Added: {$data['input_text']}\n";
    } catch (Exception $e) {
        echo "Error adding {$data['input_text']}: " . $e->getMessage() . "\n";
    }
}

echo "\nSeeded {$count} AI training data entries successfully!\n";
