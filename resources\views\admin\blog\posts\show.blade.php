@extends('layouts.dashboard')

@section('title', $post->title . ' - Admin Dashboard')
@section('page_title', 'Blog Post Details')

@section('content')
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ $post->title }}</h1>
            <p class="text-gray-600">{{ $post->slug }}</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('blog.show', ['locale' => 'en', 'post' => $post->slug]) }}" 
               target="_blank"
               class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                </svg>
                View Live
            </a>
            @can('update', $post)
            <a href="{{ route('admin.blog.posts.edit', $post) }}" 
               class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                </svg>
                Edit Post
            </a>
            @endcan
            <a href="{{ route('admin.blog.posts.index') }}" 
               class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                Back to Posts
            </a>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-bold">👁️</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Views</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($post->view_count) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-bold">💬</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Comments</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $post->comments->count() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-bold">⏱️</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Reading Time</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $post->reading_time }} min</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-bold">⭐</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Rating</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($post->average_rating, 1) }}/5</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Post Content -->
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Content</h2>
                
                @if($post->featured_image)
                <div class="mb-6">
                    <img src="{{ asset('storage/' . $post->featured_image) }}" 
                         alt="{{ $post->title }}"
                         class="w-full rounded-lg shadow-lg">
                </div>
                @endif

                @if($post->excerpt)
                <div class="mb-6 p-4 bg-gray-50 rounded-lg border-l-4 border-blue-500">
                    <p class="text-gray-700 italic">{{ $post->excerpt }}</p>
                </div>
                @endif

                <div class="prose max-w-none">
                    {!! $post->content !!}
                </div>
            </div>

            <!-- Gallery -->
            @if($post->gallery_images && count($post->gallery_images) > 0)
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Gallery ({{ count($post->gallery_images) }} images)</h2>
                <div class="grid grid-cols-3 gap-4">
                    @foreach($post->gallery_images as $image)
                    <img src="{{ asset('storage/' . $image) }}" 
                         alt="Gallery image"
                         class="w-full h-32 object-cover rounded-lg shadow cursor-pointer hover:opacity-75 transition-opacity">
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Comments -->
            @if($post->comments->count() > 0)
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold text-gray-900">
                        Comments ({{ $post->comments->count() }})
                    </h2>
                    <a href="{{ route('admin.blog.comments.index', ['post_id' => $post->id]) }}"
                       class="text-blue-600 hover:text-blue-800 text-sm">
                        View All
                    </a>
                </div>
                
                <div class="space-y-4">
                    @foreach($post->comments->take(5) as $comment)
                    <div class="border-l-4 {{ $comment->is_approved ? 'border-green-500' : 'border-yellow-500' }} pl-4 py-2">
                        <div class="flex items-center justify-between mb-1">
                            <div class="flex items-center space-x-2">
                                <span class="font-medium text-gray-900">{{ $comment->user->first_name ?? $comment->name }}</span>
                                @if($comment->is_approved)
                                <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">Approved</span>
                                @else
                                <span class="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded">Pending</span>
                                @endif
                            </div>
                            <span class="text-sm text-gray-500">{{ $comment->created_at->diffForHumans() }}</span>
                        </div>
                        <p class="text-gray-700 text-sm">{{ Str::limit($comment->content, 150) }}</p>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1 space-y-6">
            <!-- Status & Actions -->
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Status & Actions</h2>
                
                <div class="space-y-3">
                    <!-- Status Badges -->
                    <div class="flex flex-wrap gap-2">
                        @if($post->is_published)
                        <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                            ✅ Published
                        </span>
                        @else
                        <span class="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm font-medium">
                            📄 Draft
                        </span>
                        @endif

                        @if($post->is_featured)
                        <span class="px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm font-medium">
                            ⭐ Featured
                        </span>
                        @endif

                        @if($post->scheduled_at && $post->scheduled_at->isFuture())
                        <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                            🕐 Scheduled
                        </span>
                        @endif
                    </div>

                    <!-- Quick Actions -->
                    @can('update', $post)
                    <div class="pt-3 border-t space-y-2">
                        <form action="{{ route('admin.blog.posts.toggle-published', $post) }}" method="POST">
                            @csrf
                            <button type="submit" 
                                    class="w-full px-4 py-2 {{ $post->is_published ? 'bg-gray-600' : 'bg-green-600' }} text-white rounded-lg hover:opacity-90 transition-opacity">
                                {{ $post->is_published ? 'Unpublish' : 'Publish' }}
                            </button>
                        </form>

                        <form action="{{ route('admin.blog.posts.toggle-featured', $post) }}" method="POST">
                            @csrf
                            <button type="submit" 
                                    class="w-full px-4 py-2 {{ $post->is_featured ? 'bg-gray-600' : 'bg-orange-600' }} text-white rounded-lg hover:opacity-90 transition-opacity">
                                {{ $post->is_featured ? 'Unfeature' : 'Feature' }}
                            </button>
                        </form>
                    </div>
                    @endcan

                    @can('delete', $post)
                    <div class="pt-3 border-t">
                        <form action="{{ route('admin.blog.posts.destroy', $post) }}" 
                              method="POST"
                              onsubmit="return confirm('Are you sure you want to delete this post?');">
                            @csrf
                            @method('DELETE')
                            <button type="submit" 
                                    class="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                                Delete Post
                            </button>
                        </form>
                    </div>
                    @endcan
                </div>
            </div>

            <!-- Post Information -->
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Information</h2>
                
                <dl class="space-y-3">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Author</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $post->author->first_name }} {{ $post->author->last_name }}</dd>
                    </div>

                    @if($post->category)
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Category</dt>
                        <dd class="mt-1">
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">
                                {{ $post->category->name }}
                            </span>
                        </dd>
                    </div>
                    @endif

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Created</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $post->created_at->format('M d, Y H:i') }}</dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $post->updated_at->format('M d, Y H:i') }}</dd>
                    </div>

                    @if($post->published_at)
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Published</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $post->published_at->format('M d, Y H:i') }}</dd>
                    </div>
                    @endif

                    @if($post->scheduled_at)
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Scheduled For</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $post->scheduled_at->format('M d, Y H:i') }}</dd>
                    </div>
                    @endif
                </dl>
            </div>

            <!-- SEO Information -->
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">SEO</h2>
                
                <dl class="space-y-3">
                    @if($post->meta_title)
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Meta Title</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $post->meta_title }}</dd>
                    </div>
                    @endif

                    @if($post->meta_description)
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Meta Description</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $post->meta_description }}</dd>
                    </div>
                    @endif

                    @if($post->focus_keyword)
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Focus Keyword</dt>
                        <dd class="mt-1">
                            <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-sm">
                                {{ $post->focus_keyword }}
                            </span>
                        </dd>
                    </div>
                    @endif

                    @if($post->canonical_url)
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Canonical URL</dt>
                        <dd class="mt-1 text-sm text-gray-900 break-all">{{ $post->canonical_url }}</dd>
                    </div>
                    @endif
                </dl>
            </div>
        </div>
    </div>
</div>
@endsection

