<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class ActivityLog extends Model
{
    use HasFactory;
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'uuid',
        'user_id',
        'user_email',
        'user_name',
        'activity_type',
        'activity_description',
        'status',
        'failure_reason',
        'ip_address',
        'user_agent',
        'device_type',
        'browser',
        'platform',
        'country',
        'region',
        'city',
        'latitude',
        'longitude',
        'url',
        'method',
        'request_data',
        'response_data',
        'session_id',
        'request_id',
        'is_suspicious',
        'security_notes',
        'risk_score',
        'occurred_at',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'request_data' => 'array',
            'response_data' => 'array',
            'is_suspicious' => 'boolean',
            'latitude' => 'decimal:8',
            'longitude' => 'decimal:8',
            'occurred_at' => 'datetime',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($activityLog) {
            if (empty($activityLog->uuid)) {
                $activityLog->uuid = Str::uuid();
            }
            if (empty($activityLog->occurred_at)) {
                $activityLog->occurred_at = now();
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Get the user that performed the activity.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for password reset activities.
     */
    public function scopePasswordResetActivities($query)
    {
        return $query->whereIn('activity_type', [
            'password_reset_request',
            'password_reset_success',
            'password_reset_failed'
        ]);
    }

    /**
     * Scope for suspicious activities.
     */
    public function scopeSuspicious($query)
    {
        return $query->where('is_suspicious', true);
    }

    /**
     * Scope for activities by IP address.
     */
    public function scopeByIpAddress($query, string $ipAddress)
    {
        return $query->where('ip_address', $ipAddress);
    }

    /**
     * Scope for activities by email.
     */
    public function scopeByEmail($query, string $email)
    {
        return $query->where('user_email', $email);
    }

    /**
     * Scope for recent activities.
     */
    public function scopeRecent($query, int $hours = 24)
    {
        return $query->where('occurred_at', '>=', now()->subHours($hours));
    }

    /**
     * Get device information as array.
     */
    public function getDeviceInfoAttribute(): array
    {
        return [
            'device_type' => $this->device_type,
            'browser' => $this->browser,
            'platform' => $this->platform,
        ];
    }

    /**
     * Get location information as array.
     */
    public function getLocationInfoAttribute(): array
    {
        return [
            'country' => $this->country,
            'region' => $this->region,
            'city' => $this->city,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
        ];
    }

    /**
     * Get formatted device information as string.
     */
    public function getDeviceInfoStringAttribute(): string
    {
        $parts = array_filter([
            $this->browser,
            $this->platform,
            $this->device_type
        ]);

        return implode(' on ', $parts) ?: 'Unknown Device';
    }

    /**
     * Get formatted location information as string.
     */
    public function getLocationInfoStringAttribute(): string
    {
        $parts = array_filter([
            $this->city,
            $this->region,
            $this->country
        ]);

        return implode(', ', $parts) ?: 'Unknown Location';
    }

    /**
     * Get risk level based on risk score.
     */
    public function getRiskLevelAttribute(): string
    {
        if ($this->risk_score >= 80) {
            return 'high';
        } elseif ($this->risk_score >= 50) {
            return 'medium';
        } elseif ($this->risk_score >= 20) {
            return 'low';
        }

        return 'minimal';
    }

    /**
     * Check if this activity is related to password reset.
     */
    public function isPasswordResetActivity(): bool
    {
        return in_array($this->activity_type, [
            'password_reset_request',
            'password_reset_success',
            'password_reset_failed'
        ]);
    }

    /**
     * Get the status color for UI display.
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'success' => 'green',
            'failed' => 'red',
            'blocked' => 'orange',
            'pending' => 'yellow',
            default => 'gray'
        };
    }
}
