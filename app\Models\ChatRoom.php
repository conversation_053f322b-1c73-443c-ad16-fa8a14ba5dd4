<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Str;

class ChatRoom extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'type',
        'status',
        'title',
        'visitor_info',
        'metadata',
        'priority',
        'language',
        'closed_at',
    ];

    protected $casts = [
        'visitor_info' => 'array',
        'metadata' => 'array',
        'closed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $attributes = [
        'type' => 'visitor',
        'status' => 'active',
        'priority' => 1,
        // language default is handled in FormRequest
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Scope for active rooms.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for waiting rooms.
     */
    public function scopeWaiting($query)
    {
        return $query->where('status', 'waiting');
    }

    /**
     * Scope for closed rooms.
     */
    public function scopeClosed($query)
    {
        return $query->where('status', 'closed');
    }

    /**
     * Scope for high priority rooms.
     */
    public function scopeHighPriority($query)
    {
        return $query->where('priority', '>=', 3);
    }

    /**
     * Scope for specific language.
     */
    public function scopeLanguage($query, $language)
    {
        return $query->where('language', $language);
    }

    /**
     * Get all messages for this room.
     */
    public function messages(): HasMany
    {
        return $this->hasMany(ChatMessage::class)->orderBy('created_at');
    }

    /**
     * Get recent messages for this room.
     */
    public function recentMessages(): HasMany
    {
        return $this->hasMany(ChatMessage::class)
                    ->orderBy('created_at', 'desc')
                    ->limit(50);
    }

    /**
     * Get the last message for this room.
     */
    public function lastMessage(): HasOne
    {
        return $this->hasOne(ChatMessage::class)->latestOfMany();
    }

    /**
     * Get all participants for this room.
     */
    public function participants(): HasMany
    {
        return $this->hasMany(ChatParticipant::class);
    }

    /**
     * Get active participants for this room.
     */
    public function activeParticipants(): HasMany
    {
        return $this->hasMany(ChatParticipant::class)->where('is_active', true);
    }

    /**
     * Get staff participants for this room.
     */
    public function staffParticipants(): HasMany
    {
        return $this->hasMany(ChatParticipant::class)
                    ->whereIn('participant_type', ['staff', 'admin']);
    }

    /**
     * Get current assignment for this room.
     */
    public function currentAssignment(): HasOne
    {
        return $this->hasOne(ChatAssignment::class)
                    ->where('status', 'active')
                    ->latest();
    }

    /**
     * Get all assignments for this room.
     */
    public function assignments(): HasMany
    {
        return $this->hasMany(ChatAssignment::class);
    }

    /**
     * Get rating for this room.
     */
    public function rating(): HasOne
    {
        return $this->hasOne(ChatRating::class);
    }

    /**
     * Get session data for this room.
     */
    public function session(): HasOne
    {
        return $this->hasOne(ChatSession::class);
    }

    /**
     * Get files uploaded in this room.
     */
    public function files(): HasMany
    {
        return $this->hasMany(ChatFile::class);
    }

    /**
     * Get typing indicators for this room.
     */
    public function typingIndicators(): HasMany
    {
        return $this->hasMany(ChatTypingIndicator::class);
    }

    /**
     * Check if room is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if room is closed.
     */
    public function isClosed(): bool
    {
        return $this->status === 'closed';
    }

    /**
     * Check if room has staff assigned.
     */
    public function hasStaffAssigned(): bool
    {
        return $this->currentAssignment()->exists();
    }

    /**
     * Get priority label.
     */
    public function getPriorityLabelAttribute(): string
    {
        return match($this->priority) {
            1 => 'Low',
            2 => 'Medium',
            3 => 'High',
            4 => 'Urgent',
            default => 'Unknown'
        };
    }

    /**
     * Get participant count.
     */
    public function getParticipantCountAttribute(): int
    {
        return $this->activeParticipants()->count();
    }

    /**
     * Get message count.
     */
    public function getMessageCountAttribute(): int
    {
        return $this->messages()->count();
    }

    /**
     * Close the room.
     */
    public function close(): bool
    {
        $this->status = 'closed';
        $this->closed_at = now();
        return $this->save();
    }

    /**
     * Reopen the room.
     */
    public function reopen(): bool
    {
        $this->status = 'active';
        $this->closed_at = null;
        return $this->save();
    }

    /**
     * Archive the room.
     */
    public function archive(): bool
    {
        $this->status = 'archived';
        return $this->save();
    }
}
