<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class LoginHistory extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'uuid',
        'user_id',
        'login_status',
        'ip_address',
        'location',
        'latitude',
        'longitude',
        'device_info',
        'device_fingerprint',
        'user_agent',
        'os_info',
        'browser_info',
        'device_type',
        'login_method',
        'failed_attempts',
        'is_vpn',
        'is_tor',
        'security_alert',
        'login_timezone',
        'login_ip_class',
        'risk_score',
        'is_device_known',
        'is_location_known',
        'is_ip_blacklisted',
        'is_ip_whitelisted',
        'twofa_used',
        'twofa_code_verified',
        'session_id',
        'referrer',
        'session_started_at',
        'session_ended_at',
        'session_duration',
        'failure_reason',
        'security_flags',
        'geolocation_data',
        'isp',
        'organization',
        'is_suspicious',
        'admin_notes',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'device_info' => 'array',
        'security_flags' => 'array',
        'geolocation_data' => 'array',
        'latitude' => 'decimal:6',
        'longitude' => 'decimal:6',
        'is_vpn' => 'boolean',
        'is_tor' => 'boolean',
        'security_alert' => 'boolean',
        'is_device_known' => 'boolean',
        'is_location_known' => 'boolean',
        'is_ip_blacklisted' => 'boolean',
        'is_ip_whitelisted' => 'boolean',
        'twofa_used' => 'boolean',
        'twofa_code_verified' => 'boolean',
        'is_suspicious' => 'boolean',
        'session_started_at' => 'datetime',
        'session_ended_at' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($loginHistory) {
            if (empty($loginHistory->uuid)) {
                $loginHistory->uuid = Str::uuid();
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Get the user that owns the login history.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for successful logins.
     */
    public function scopeSuccessful($query)
    {
        return $query->where('login_status', 'success');
    }

    /**
     * Scope for failed logins.
     */
    public function scopeFailed($query)
    {
        return $query->where('login_status', 'failed');
    }

    /**
     * Scope for suspicious activities.
     */
    public function scopeSuspicious($query)
    {
        return $query->where('is_suspicious', true);
    }

    /**
     * Scope for high risk activities.
     */
    public function scopeHighRisk($query)
    {
        return $query->where('risk_score', '>=', 70);
    }

    /**
     * Scope for recent activities.
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Get formatted location string.
     */
    public function getFormattedLocationAttribute(): string
    {
        if (!$this->location) {
            return 'Unknown Location';
        }

        return $this->location;
    }

    /**
     * Get risk level description.
     */
    public function getRiskLevelAttribute(): string
    {
        if ($this->risk_score >= 80) {
            return 'Critical';
        } elseif ($this->risk_score >= 60) {
            return 'High';
        } elseif ($this->risk_score >= 40) {
            return 'Medium';
        } elseif ($this->risk_score >= 20) {
            return 'Low';
        }

        return 'Minimal';
    }

    /**
     * Get risk level color for UI.
     */
    public function getRiskColorAttribute(): string
    {
        if ($this->risk_score >= 80) {
            return 'red';
        } elseif ($this->risk_score >= 60) {
            return 'orange';
        } elseif ($this->risk_score >= 40) {
            return 'yellow';
        } elseif ($this->risk_score >= 20) {
            return 'blue';
        }

        return 'green';
    }

    /**
     * Check if login is from a new device.
     */
    public function isNewDevice(): bool
    {
        return !$this->is_device_known;
    }

    /**
     * Check if login is from a new location.
     */
    public function isNewLocation(): bool
    {
        return !$this->is_location_known;
    }

    /**
     * Get session duration in human readable format.
     */
    public function getFormattedSessionDurationAttribute(): string
    {
        if (!$this->session_duration) {
            return 'Unknown';
        }

        $hours = floor($this->session_duration / 3600);
        $minutes = floor(($this->session_duration % 3600) / 60);
        $seconds = $this->session_duration % 60;

        if ($hours > 0) {
            return sprintf('%dh %dm %ds', $hours, $minutes, $seconds);
        } elseif ($minutes > 0) {
            return sprintf('%dm %ds', $minutes, $seconds);
        }

        return sprintf('%ds', $seconds);
    }

    /**
     * Get browser name from user agent.
     */
    public function getBrowserNameAttribute(): string
    {
        return $this->browser_info ?? 'Unknown Browser';
    }

    /**
     * Get OS name from user agent.
     */
    public function getOsNameAttribute(): string
    {
        return $this->os_info ?? 'Unknown OS';
    }
}
