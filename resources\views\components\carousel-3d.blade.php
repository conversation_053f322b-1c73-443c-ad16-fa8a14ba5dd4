@php
// Calculate average rating from all clients
function calculateAverageRating($clients) {
    if ($clients->isEmpty()) return 0;

    $totalRating = $clients->sum('rating');
    $ratedClients = $clients->where('rating', '>', 0)->count();

    return $ratedClients > 0 ? round($totalRating / $ratedClients, 1) : 0;
}

// Generate professional gradient colors (not used in new design but kept for compatibility)
function getProfessionalGradient($index) {
    $gradients = [
        'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
        'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
        'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
        'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
        'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)'
    ];

    return $gradients[$index % count($gradients)];
}

$averageRating = calculateAverageRating($clients);
$totalClients = $clients->count();
@endphp

@props([
    'clients' => collect(),
    'autoplay' => true,
    'interval' => 5000,
    'enableTouch' => true,
    'enableKeyboard' => true,
    'title' => 'Our Valued Clients',
    'subtitle' => 'Trusted by industry leaders worldwide'
])

@if($clients->count() > 0)
<section class="py-20 bg-gradient-to-br from-gray-50 to-white">
    <div class="container mx-auto px-4">
        <!-- Section Header -->
        <div class="text-center mb-16">
            <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Our Valued <span class="text-blue-600">Clients</span>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">{{ $subtitle }}</p>

            <!-- Client Statistics -->
            <div class="flex justify-center items-center space-x-8 mb-8">
                <div class="text-center">
                    <div class="text-3xl font-bold text-blue-600">{{ $totalClients }}+</div>
                    <div class="text-sm text-gray-500">Happy Clients</div>
                </div>
                <div class="text-center">
                    <div class="flex items-center justify-center mb-1">
                        <span class="text-3xl font-bold text-yellow-500 mr-2">{{ $averageRating }}</span>
                        <div class="flex">
                            @for($i = 1; $i <= 5; $i++)
                                <svg class="w-5 h-5 {{ $i <= $averageRating ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                            @endfor
                        </div>
                    </div>
                    <div class="text-sm text-gray-500">Average Rating</div>
                </div>
            </div>
        </div>

        <!-- Professional Client Carousel -->
        <div class="relative max-w-7xl mx-auto">
            <div class="client-carousel-container overflow-hidden"
                 data-autoplay="{{ $autoplay ? 'true' : 'false' }}"
                 data-interval="{{ $interval }}"
                 data-total-items="{{ $clients->count() }}">

                <!-- Carousel Track -->
                <div class="carousel-track flex transition-transform duration-500 ease-in-out" style="transform: translateX(0%);">
                    @foreach($clients as $index => $client)
                    <div class="carousel-slide flex-shrink-0 w-full sm:w-1/2 lg:w-1/3 xl:w-1/4 px-4"
                         data-index="{{ $index }}">

                        <!-- Client Card -->
                        <div class="client-card bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 h-full border border-gray-100 cursor-pointer">

                            <!-- Client Logo -->
                            <div class="client-logo-wrapper mb-4 text-center">
                                @if($client->logo_path)
                                    <img src="{{ $client->logo_url }}"
                                         alt="{{ $client->company }} Logo"
                                         class="client-logo w-16 h-16 object-contain mx-auto rounded-lg">
                                @else
                                    <div class="client-logo-placeholder w-16 h-16 mx-auto rounded-lg bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center">
                                        <span class="text-xl font-bold text-blue-600">{{ substr($client->company ?? $client->name, 0, 1) }}</span>
                                    </div>
                                @endif
                            </div>

                            <!-- Client Info (Always Visible) -->
                            <div class="text-center mb-4">
                                <h3 class="text-lg font-bold text-gray-900 mb-1 line-clamp-2">{{ $client->company ?? $client->name }}</h3>
                                @if($client->industry)
                                <p class="text-gray-500 text-sm">{{ $client->industry }}</p>
                                @endif
                            </div>

                            <!-- Rating (Always Visible) -->
                            @if($client->rating)
                            <div class="flex justify-center mb-4">
                                @for($i = 1; $i <= 5; $i++)
                                    <svg class="w-4 h-4 {{ $i <= $client->rating ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                    </svg>
                                @endfor
                                <span class="ml-2 text-sm text-gray-600">({{ $client->rating }})</span>
                            </div>
                            @endif

                            <!-- Expandable Details (Hidden by default) -->
                            <div class="client-details hidden">
                                <!-- Contact Person -->
                                @if($client->company && $client->name !== $client->company)
                                <div class="text-center mb-3">
                                    <p class="text-gray-700 text-sm font-medium">{{ $client->name }}</p>
                                </div>
                                @endif

                                <!-- Testimonial -->
                                @if($client->testimonial)
                                <blockquote class="text-gray-600 text-sm italic text-center mb-4 px-2">
                                    "{{ Str::limit($client->testimonial, 120) }}"
                                </blockquote>
                                @endif

                                <!-- Project Status -->
                                <div class="text-center mb-3">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        {{ $client->project_status === 'completed' ? 'bg-green-100 text-green-800' :
                                           ($client->project_status === 'ongoing' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800') }}">
                                        {{ ucfirst($client->project_status) }}
                                    </span>
                                </div>
                            </div>

                            <!-- Expand Button -->
                            <div class="text-center mt-auto">
                                <button class="expand-btn text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200 focus:outline-none">
                                    <span class="expand-text">View Details</span>
                                    <svg class="expand-icon w-4 h-4 inline-block ml-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>

                <!-- Navigation Controls -->
                <button class="carousel-nav carousel-prev absolute left-4 top-1/2 transform -translate-y-1/2 bg-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-200 z-10 focus:outline-none focus:ring-2 focus:ring-blue-500" aria-label="Previous clients">
                    <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>

                <button class="carousel-nav carousel-next absolute right-4 top-1/2 transform -translate-y-1/2 bg-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-200 z-10 focus:outline-none focus:ring-2 focus:ring-blue-500" aria-label="Next clients">
                    <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>

                <!-- Indicators -->
                <div class="flex justify-center mt-8 space-x-2">
                    @foreach($clients->chunk(4) as $chunkIndex => $chunk)
                    <button class="carousel-indicator w-3 h-3 rounded-full transition-all duration-200 {{ $chunkIndex === 0 ? 'bg-blue-600' : 'bg-gray-300 hover:bg-gray-400' }}"
                            data-slide="{{ $chunkIndex }}"
                            aria-label="Go to clients {{ $chunkIndex * 4 + 1 }}-{{ min(($chunkIndex + 1) * 4, $clients->count()) }}"></button>
                    @endforeach
                </div>
            </div>
        </div>
        
        <!-- Client Stats -->
        <div class="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div class="bg-white rounded-lg p-6 shadow-soft">
                <div class="text-3xl font-bold text-primary-600 mb-2">{{ $clients->count() }}+</div>
                <div class="text-gray-600">Happy Clients</div>
            </div>
            <div class="bg-white rounded-lg p-6 shadow-soft">
                <div class="text-3xl font-bold text-primary-600 mb-2">{{ $clients->where('project_status', 'completed')->count() }}+</div>
                <div class="text-gray-600">Projects Completed</div>
            </div>
            <div class="bg-white rounded-lg p-6 shadow-soft">
                <div class="text-3xl font-bold text-primary-600 mb-2">{{ number_format($clients->avg('rating'), 1) }}</div>
                <div class="text-gray-600">Average Rating</div>
            </div>
        </div>
    </div>
</section>

@push('styles')
<link rel="stylesheet" href="{{ asset('css/professional-carousel.css') }}">
@endpush

@push('scripts')
<script src="{{ asset('js/professional-carousel.js') }}"></script>
@endpush

@endif
