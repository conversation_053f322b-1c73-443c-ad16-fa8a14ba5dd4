<?php

namespace Tests\Feature;

use App\Models\LoginHistoryPermission;
use App\Models\User;
use App\Models\Role;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;

class LoginHistoryPermissionTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected User $staff;
    protected Role $adminRole;
    protected Role $staffRole;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles
        $this->adminRole = Role::factory()->create([
            'name' => 'admin',
            'slug' => 'admin',
            'is_active' => true,
        ]);

        $this->staffRole = Role::factory()->create([
            'name' => 'staff',
            'slug' => 'staff',
            'is_active' => true,
        ]);

        // Create users
        $this->admin = User::factory()->create([
            'role_id' => $this->adminRole->id,
            'email_verified_at' => now(),
        ]);

        $this->staff = User::factory()->create([
            'role_id' => $this->staffRole->id,
            'email_verified_at' => now(),
        ]);
    }

    #[Test]
    public function admin_can_create_login_history_permission()
    {
        Sanctum::actingAs($this->admin);

        $response = $this->postJson('/api/v1/login-history-permissions', [
            'user_id' => $this->staff->id,
            'access_level' => 'partial',
            'reason' => 'Staff member needs access to monitor login activities',
            'expires_at' => now()->addDays(30)->toDateTimeString(),
        ]);

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'Login history permission granted successfully',
            ]);

        $this->assertDatabaseHas('login_history_permissions', [
            'user_id' => $this->staff->id,
            'granted_by_user_id' => $this->admin->id,
            'access_level' => 'partial',
            'is_active' => true,
        ]);
    }

    #[Test]
    public function admin_can_view_all_permissions()
    {
        Sanctum::actingAs($this->admin);

        // Create some permissions
        LoginHistoryPermission::factory()->create([
            'user_id' => $this->staff->id,
            'granted_by_user_id' => $this->admin->id,
            'access_level' => 'partial',
        ]);

        $response = $this->getJson('/api/v1/login-history-permissions');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'data' => [
                        '*' => [
                            'id',
                            'user',
                            'granted_by',
                            'access_level',
                            'access_level_display',
                            'is_active',
                            'is_valid',
                        ]
                    ]
                ]
            ]);
    }

    #[Test]
    public function admin_can_update_permission()
    {
        Sanctum::actingAs($this->admin);

        $permission = LoginHistoryPermission::factory()->create([
            'user_id' => $this->staff->id,
            'granted_by_user_id' => $this->admin->id,
            'access_level' => 'partial',
        ]);

        $response = $this->putJson("/api/v1/login-history-permissions/{$permission->uuid}", [
            'access_level' => 'full',
            'reason' => 'Upgraded to full access',
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Login history permission updated successfully',
            ]);

        $permission->refresh();
        $this->assertEquals('full', $permission->access_level);
    }

    #[Test]
    public function admin_can_revoke_permission()
    {
        Sanctum::actingAs($this->admin);

        $permission = LoginHistoryPermission::factory()->create([
            'user_id' => $this->staff->id,
            'granted_by_user_id' => $this->admin->id,
            'access_level' => 'partial',
        ]);

        $response = $this->deleteJson("/api/v1/login-history-permissions/{$permission->uuid}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Login history permission revoked successfully',
            ]);

        $permission->refresh();
        $this->assertFalse($permission->is_active);
    }

    #[Test]
    public function staff_cannot_manage_permissions()
    {
        Sanctum::actingAs($this->staff);

        $response = $this->getJson('/api/v1/login-history-permissions');

        $response->assertStatus(403);
    }

    #[Test]
    public function staff_with_permission_can_access_admin_login_history()
    {
        // Grant permission to staff
        LoginHistoryPermission::factory()->create([
            'user_id' => $this->staff->id,
            'granted_by_user_id' => $this->admin->id,
            'access_level' => 'partial',
            'specific_permissions' => ['view_basic_info'],
        ]);

        Sanctum::actingAs($this->staff);

        $response = $this->getJson('/api/v1/admin/login-history');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data',
                'meta' => [
                    'access_level',
                ]
            ]);
    }

    #[Test]
    public function staff_without_permission_cannot_access_admin_login_history()
    {
        Sanctum::actingAs($this->staff);

        $response = $this->getJson('/api/v1/admin/login-history');

        $response->assertStatus(403)
            ->assertJson([
                'error' => 'Forbidden',
                'message' => 'You do not have permission to access login history data.',
            ]);
    }

    #[Test]
    public function permission_model_validates_access_correctly()
    {
        $permission = LoginHistoryPermission::factory()->create([
            'user_id' => $this->staff->id,
            'granted_by_user_id' => $this->admin->id,
            'access_level' => 'partial',
            'specific_permissions' => ['view_basic_info', 'view_statistics'],
            'is_active' => true,
            'expires_at' => now()->addDays(30),
        ]);

        $this->assertTrue($permission->isValid());
        $this->assertFalse($permission->isExpired());
        $this->assertTrue($permission->hasSpecificPermission('view_basic_info'));
        $this->assertTrue($permission->hasSpecificPermission('view_statistics'));
        $this->assertFalse($permission->hasSpecificPermission('view_ip_addresses'));
    }

    #[Test]
    public function expired_permission_is_invalid()
    {
        $permission = LoginHistoryPermission::factory()->create([
            'user_id' => $this->staff->id,
            'granted_by_user_id' => $this->admin->id,
            'access_level' => 'partial',
            'is_active' => true,
            'expires_at' => now()->subDays(1), // Expired
        ]);

        $this->assertFalse($permission->isValid());
        $this->assertTrue($permission->isExpired());
    }

    #[Test]
    public function full_access_permission_allows_all_actions()
    {
        $permission = LoginHistoryPermission::factory()->create([
            'user_id' => $this->staff->id,
            'granted_by_user_id' => $this->admin->id,
            'access_level' => 'full',
            'is_active' => true,
        ]);

        $this->assertTrue($permission->hasSpecificPermission('view_basic_info'));
        $this->assertTrue($permission->hasSpecificPermission('view_ip_addresses'));
        $this->assertTrue($permission->hasSpecificPermission('view_risk_scores'));
        $this->assertTrue($permission->hasSpecificPermission('export_data'));
    }

    #[Test]
    public function cannot_grant_permission_to_customer_user()
    {
        $customerRole = Role::factory()->create([
            'name' => 'customer',
            'slug' => 'customer',
            'is_active' => true,
        ]);

        $customer = User::factory()->create([
            'role_id' => $customerRole->id,
        ]);

        Sanctum::actingAs($this->admin);

        $response = $this->postJson('/api/v1/login-history-permissions', [
            'user_id' => $customer->id,
            'access_level' => 'partial',
            'reason' => 'Test permission',
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'error' => 'Invalid user role',
                'message' => 'Login history permissions can only be granted to admin or staff users.',
            ]);
    }
}
