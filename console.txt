 2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectApplicationControllerTest > non admin cannot access project applications            UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Customer role with appropriate permissions, {"profile":["read","update"],"projects":["create","read"],"orders":["create","read"],"dashboard":["access"]}, 1, 2025-07-14 08:58:35, 2025-07-14 08:58:35))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectApplicationControllerTest > admin can view project application details              UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Client role with appropriate permissions, {"profile":["read","update"],"projects":["create","read"],"orders":["create","read"],"dashboard":["access"]}, 1, 2025-07-14 08:58:35, 2025-07-14 08:58:35))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectApplicationControllerTest > admin can update project application status             UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Admin role with appropriate permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:35, 2025-07-14 08:58:35))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectApplicationControllerTest > admin can download project application attachments      UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Client role with appropriate permissions, {"profile":["read","update"],"projects":["create","read"],"orders":["create","read"],"dashboard":["access"]}, 1, 2025-07-14 08:58:35, 2025-07-14 08:58:35))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectApplicationControllerTest > admin can get project application statistics            UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Staff role with appropriate permissions, {"projects":["read","update"],"orders":["read","update"],"services":["read"],"activity_logs":["read"],"dashboard":["access"]}, 1, 2025-07-14 08:58:35, 2025-07-14 08:58:35))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectApplicationControllerTest > admin can filter project applications                   UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Admin role with appropriate permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:35, 2025-07-14 08:58:35))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectApplicationControllerTest > admin can search project applications                   UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Customer role with appropriate permissions, {"profile":["read","update"],"projects":["create","read"],"orders":["create","read"],"dashboard":["access"]}, 1, 2025-07-14 08:58:35, 2025-07-14 08:58:35))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectApplicationControllerTest > status update creates activity log                      UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Client role with appropriate permissions, {"profile":["read","update"],"projects":["create","read"],"orders":["create","read"],"dashboard":["access"]}, 1, 2025-07-14 08:58:35, 2025-07-14 08:58:35))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectApplicationControllerTest > admin can update project application                    UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Admin role with appropriate permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:35, 2025-07-14 08:58:35))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectApplicationControllerTest > admin can delete project application                    UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Client role with appropriate permissions, {"profile":["read","update"],"projects":["create","read"],"orders":["create","read"],"dashboard":["access"]}, 1, 2025-07-14 08:58:35, 2025-07-14 08:58:35))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectApplicationControllerTest > invalid status update returns error                     UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Client role with appropriate permissions, {"profile":["read","update"],"projects":["create","read"],"orders":["create","read"],"dashboard":["access"]}, 1, 2025-07-14 08:58:35, 2025-07-14 08:58:35))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectApplicationControllerTest > download nonexistent attachment returns 404             UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Customer role with appropriate permissions, {"profile":["read","update"],"projects":["create","read"],"orders":["create","read"],"dashboard":["access"]}, 1, 2025-07-14 08:58:36, 2025-07-14 08:58:36))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectApplicationControllerTest > admin actions are logged with proper risk scores        UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Admin role with appropriate permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:36, 2025-07-14 08:58:36))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectApplicationControllerTest > project application statistics are accurate             UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Client role with appropriate permissions, {"profile":["read","update"],"projects":["create","read"],"orders":["create","read"],"dashboard":["access"]}, 1, 2025-07-14 08:58:36, 2025-07-14 08:58:36))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > admin can view projects index                                      UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:36, 2025-07-14 08:58:36))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > non admin cannot access projects index                             UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:36, 2025-07-14 08:58:36))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > admin can view project details                                     UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:36, 2025-07-14 08:58:36))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > admin can view project edit form                                   UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:36, 2025-07-14 08:58:36))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > admin can update project basic information                         UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:36, 2025-07-14 08:58:36))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > admin can update project with time and budget                      UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:36, 2025-07-14 08:58:36))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > admin can update project with dates                                UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:36, 2025-07-14 08:58:36))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > admin can update project seo information                           UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:36, 2025-07-14 08:58:36))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > project slug is updated when title changes                         UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:36, 2025-07-14 08:58:36))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > project update validates required fields                           UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:36, 2025-07-14 08:58:36))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > project update validates date logic                                UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:36, 2025-07-14 08:58:36))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > project update validates numeric fields                            UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:36, 2025-07-14 08:58:36))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > project update validates status values                             UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:36, 2025-07-14 08:58:36))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > admin can delete project                                           UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:36, 2025-07-14 08:58:36))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > projects index can be filtered by service                          UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:36, 2025-07-14 08:58:36))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > projects index can be filtered by status                           UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:36, 2025-07-14 08:58:36))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > projects index can be searched                                     UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:36, 2025-07-14 08:58:36))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > admin can update project with featured image                       UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:36, 2025-07-14 08:58:36))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > project update validates image file type                           UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:36, 2025-07-14 08:58:36))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > project update validates image file size                           UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:37, 2025-07-14 08:58:37))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > admin can toggle project published status                          UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:37, 2025-07-14 08:58:37))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > admin can toggle project featured status                           UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:37, 2025-07-14 08:58:37))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > non admin cannot toggle project status                             UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:37, 2025-07-14 08:58:37))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > project update handles missing client name gracefully              UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:37, 2025-07-14 08:58:37))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > project update handles invalid client id                           UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:37, 2025-07-14 08:58:37))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectControllerTest > project update handles invalid service id                          UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin, Administrator role with full permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:37, 2025-07-14 08:58:37))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.slug")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectUpdateFixTest > project update works with correct image service response            UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.name (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin-6874c6bee743e, Client role with appropriate permissions, {"profile":["read","update"],"projects":["create","read"],"orders":["create","read"],"dashboard":["access"]}, 1, 2025-07-14 08:58:38, 2025-07-14 08:58:38))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.name")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectUpdateFixTest > project update handles image service failure gracefully             UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.name (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin-6874c6bef3bba, Client role with appropriate permissions, {"profile":["read","update"],"projects":["create","read"],"orders":["create","read"],"dashboard":["access"]}, 1, 2025-07-14 08:58:38, 2025-07-14 08:58:38))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.name")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectUpdateFixTest > project update works without image upload                           UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.name (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin-6874c6bf0f917, Customer role with appropriate permissions, {"profile":["read","update"],"projects":["create","read"],"orders":["create","read"],"dashboard":["access"]}, 1, 2025-07-14 08:58:39, 2025-07-14 08:58:39))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.name")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectUpdateFixTest > project update validates client name field                          UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.name (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin-6874c6bf1987b, Customer role with appropriate permissions, {"profile":["read","update"],"projects":["create","read"],"orders":["create","read"],"dashboard":["access"]}, 1, 2025-07-14 08:58:39, 2025-07-14 08:58:39))

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.name")

  ───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────  
   FAILED  Tests\Feature\Admin\ProjectUpdateFixTest > project update preserves existing image when no new upload          UniqueConstraintViolationException   
  SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.name (Connection: sqlite, SQL: insert into "roles" ("name", "slug", "description", "permissions", "is_active", "updated_at", "created_at") values (admin, admin-6874c6bf24fb1, Admin role with appropriate permissions, {"users":["create","read","update","delete"],"roles":["create","read","update","delete"],"projects":["create","read","update","delete"],"orders":["create","read","update","delete"],"services":["create","read","update","delete"],"activity_logs":["read","delete"],"dashboard":["access"]}, 1, 2025-07-14 08:58:39, 2025-07-14 08:58:39))  

  at vendor\laravel\framework\src\Illuminate\Database\Connection.php:817
    813▕         // message to include the bindings with SQL, which will make this exception a
    814▕         // lot more helpful to the developer instead of just the database's errors.
    815▕         catch (Exception $e) {
    816▕             if ($this->isUniqueConstraintError($e)) {
  ➜ 817▕                 throw new UniqueConstraintViolationException(
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕

  1   vendor\laravel\framework\src\Illuminate\Database\Connection.php:817

  2   vendor\laravel\framework\src\Illuminate\Database\Connection.php:568
      NunoMaduro\Collision\Exceptions\TestException::("SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: roles.name")


  Tests:    74 failed, 29 passed (93 assertions)
  Duration: 7.66s