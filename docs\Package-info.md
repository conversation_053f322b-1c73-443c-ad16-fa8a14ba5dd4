This document provides an overview of the various packages and dependencies used in the ChiSolution project. It includes information on the purpose of each package, its version, and any relevant configuration details. The document is divided into sections, each covering a specific area of the project. The document is intended for developers and other technical stakeholders who are involved in the project. The document also show the use of each package, why it is needed and how to use it.

## Core Laravel Packages

### Laravel Framework
- **Purpose**: The main Laravel PHP framework.
- **Version**: ^12.28.x
- **Why**: Provides the core functionality for building web applications.
- **How to Use**: Import and use <PERSON><PERSON>'s built-in classes and methods.
- **Example**: `use Illuminate\Support\Facades\Route;`

### Sanctum
- **Purpose**: API authentication for Laravel (token-based auth).
- **Version**: ^4.1
- **Why**: Needed for secure API authentication.
- **How to Use**: Configure Sanctum in `config/sanctum.php` and use it in controllers.
- **Example**: `use <PERSON>vel\Sanctum\HasApiTokens;`

### Tinker
- **Purpose**: REPL for <PERSON><PERSON> (interactive shell).
- **Version**: ^2.10.1
- **Why**: Useful for testing and debugging.
- **How to Use**: Run `php artisan tinker` in the terminal.
- **Example**: `$user = App\Models\User::first();`

## Image & File Processing

### Intervention Image
- **Purpose**: Image manipulation and optimization.
- **Version**: ^1.5
- **Why**: Needed for image processing tasks.
- **How to Use**: Use the `Image` facade to manipulate images.
- **Example**: `use Intervention\Image\Facades\Image;`

### Spatie Image Optimizer
- **Purpose**: Image optimization and compression.
- **Version**: ^1.8
- **Why**: Needed for optimizing images for web delivery.
- **How to Use**: Use the `OptimizerChainFactory` to optimize images.
- **Example**: `use Spatie\ImageOptimizer\OptimizerChainFactory;`

### Spatie Laravel Sitemap
- **Purpose**: Generate sitemaps for your website.
- **Version**: ^7.3
- **Why**: Needed for SEO purposes.
- **How to Use**: Use the `SitemapGenerator` to generate sitemaps.
- **Example**: `use Spatie\Sitemap\SitemapGenerator;`

## AI & External Services

### OpenAI PHP Client
- **Purpose**: Interact with the OpenAI API.
- **Version**: ^0.14.0
- **Why**: Needed for AI-powered features.
- **How to Use**: Use the `OpenAI` facade to interact with the OpenAI API.
- **Example**: `use OpenAI\Laravel\Facades\OpenAI;`

### OpenAI Laravel
- **Purpose**: Laravel integration for OpenAI.
- **Version**: ^0.14.0
- **Why**: Needed for AI-powered features.
- **How to Use**: Use the `OpenAI` facade to interact with the OpenAI API.
- **Example**: `use OpenAI\Laravel\Facades\OpenAI;`

### Stripe PHP
- **Purpose**: Interact with the Stripe API.
- **Version**: ^17.6.0
- **Why**: Needed for payment processing.
- **How to Use**: Use the `Stripe` facade to interact with the Stripe API.
- **Example**: `use Stripe\Stripe;`

## SEO & Analytics

### Artesaos SEO Tools
- **Purpose**: SEO tools for Laravel.
- **Version**: ^1.3
- **Why**: Needed for SEO purposes.
- **How to Use**: Use the `SEO` facade to manage SEO-related tasks.
- **Example**: `use Artesaos\SEOTools\Facades\SEO;`

### Spatie Schema.org
- **Purpose**: Generate Schema.org markup.
- **Version**: ^3.23
- **Why**: Needed for structured data on your website.
- **How to Use**: Use the `Schema` facade to generate Schema.org markup.
- **Example**: `use Spatie\SchemaOrg\Schema;`

### Jenssegers Agent
- **Purpose**: Device detection and user agent parsing.
- **Version**: *
- **Why**: Needed for device detection and user agent parsing.
- **How to Use**: Use the `Agent` facade to detect devices and parse user agents.
- **Example**: `use Jenssegers\Agent\Facades\Agent;`

### Spatie Laravel Analytics
- **Purpose**: Google Analytics integration for Laravel.
- **Version**: ^4.0
- **Why**: Needed for analytics purposes like tracking page views, events, etc.
- **How to Use**: Use the `Analytics` facade to interact with Google Analytics.
- **Example**: `use Spatie\Analytics\Analytics;`

## Development & Testing

### PHPUnit
- **Purpose**: Testing framework for PHP.
- **Version**: ^11.5
- **Why**: Needed for testing.
- **How to Use**: Write tests using PHPUnit and run them with `php artisan test`.
- **Example**: `use PHPUnit\Framework\TestCase;`


