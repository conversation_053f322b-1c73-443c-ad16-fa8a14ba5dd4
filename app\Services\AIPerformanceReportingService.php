<?php

namespace App\Services;

use App\Models\AiConversationLog;
use App\Models\ChatRoom;
use App\Models\ChatMessage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AIPerformanceReportingService
{
    /**
     * Get comprehensive AI performance report.
     */
    public function getPerformanceReport(array $filters = []): array
    {
        $startDate = $filters['start_date'] ?? now()->subDays(30);
        $endDate = $filters['end_date'] ?? now();
        $model = $filters['model'] ?? null;
        $language = $filters['language'] ?? null;

        return [
            'summary' => $this->getPerformanceSummary($startDate, $endDate, $model, $language),
            'success_metrics' => $this->getSuccessMetrics($startDate, $endDate, $model, $language),
            'escalation_analysis' => $this->getEscalationAnalysis($startDate, $endDate, $model, $language),
            'response_time_analysis' => $this->getResponseTimeAnalysis($startDate, $endDate, $model, $language),
            'confidence_analysis' => $this->getConfidenceAnalysis($startDate, $endDate, $model, $language),
            'language_performance' => $this->getLanguagePerformance($startDate, $endDate, $model),
            'model_comparison' => $this->getModelComparison($startDate, $endDate, $language),
            'trending_topics' => $this->getTrendingTopics($startDate, $endDate, $model, $language),
            'failure_analysis' => $this->getFailureAnalysis($startDate, $endDate, $model, $language),
        ];
    }

    /**
     * Get AI performance summary.
     */
    protected function getPerformanceSummary(Carbon $startDate, Carbon $endDate, ?string $model, ?string $language): array
    {
        // Create a base query builder function to reuse
        $baseQuery = function() use ($startDate, $endDate, $model, $language) {
            $query = AiConversationLog::whereBetween('created_at', [$startDate, $endDate]);

            if ($model) {
                $query->where('model_used', $model);
            }

            if ($language) {
                $query->where('detected_language', $language);
            }

            return $query;
        };

        $totalInteractions = $baseQuery()->count();
        $successfulInteractions = $baseQuery()->where('escalated_to_human', false)->count();
        $escalatedInteractions = $baseQuery()->where('escalated_to_human', true)->count();
        $avgConfidence = $baseQuery()->avg('confidence_score') ?? 0;
        $avgResponseTime = $baseQuery()->avg('processing_time_ms') ?? 0;

        $successRate = $totalInteractions > 0 ? ($successfulInteractions / $totalInteractions) * 100 : 0;
        $escalationRate = $totalInteractions > 0 ? ($escalatedInteractions / $totalInteractions) * 100 : 0;

        return [
            'total_interactions' => $totalInteractions,
            'successful_interactions' => $successfulInteractions,
            'escalated_interactions' => $escalatedInteractions,
            'success_rate' => round($successRate, 2),
            'escalation_rate' => round($escalationRate, 2),
            'avg_confidence_score' => round($avgConfidence, 2),
            'avg_response_time_ms' => round($avgResponseTime, 2),
            'period_start' => $startDate->toDateString(),
            'period_end' => $endDate->toDateString(),
        ];
    }

    /**
     * Get detailed success metrics.
     */
    protected function getSuccessMetrics(Carbon $startDate, Carbon $endDate, ?string $model, ?string $language): array
    {
        $query = AiConversationLog::whereBetween('created_at', [$startDate, $endDate]);
        
        if ($model) {
            $query->where('model_used', $model);
        }
        
        if ($language) {
            $query->where('detected_language', $language);
        }

        // Success by response type (using model_used since response_type doesn't exist)
        $successByType = $query->select('model_used as response_type', DB::raw('COUNT(*) as total'),
                                      DB::raw('SUM(CASE WHEN escalated_to_human = 0 THEN 1 ELSE 0 END) as successful'))
                              ->groupBy('model_used')
                              ->get()
                              ->map(function ($item) {
                                  $successRate = $item->total > 0 ? ($item->successful / $item->total) * 100 : 0;
                                  return [
                                      'type' => $item->response_type,
                                      'total' => $item->total,
                                      'successful' => $item->successful,
                                      'success_rate' => round($successRate, 2),
                                  ];
                              });

        // Success by confidence ranges
        $confidenceRanges = [
            'high' => ['min' => 0.8, 'max' => 1.0],
            'medium' => ['min' => 0.5, 'max' => 0.8],
            'low' => ['min' => 0.0, 'max' => 0.5],
        ];

        $successByConfidence = [];
        foreach ($confidenceRanges as $range => $bounds) {
            $rangeQuery = clone $query;
            $rangeQuery->whereBetween('confidence_score', [$bounds['min'], $bounds['max']]);
            
            $total = $rangeQuery->count();
            $successful = $rangeQuery->where('escalated_to_human', false)->count();
            $successRate = $total > 0 ? ($successful / $total) * 100 : 0;

            $successByConfidence[$range] = [
                'total' => $total,
                'successful' => $successful,
                'success_rate' => round($successRate, 2),
                'confidence_range' => $bounds,
            ];
        }

        return [
            'by_response_type' => $successByType,
            'by_confidence_level' => $successByConfidence,
        ];
    }

    /**
     * Get escalation analysis.
     */
    protected function getEscalationAnalysis(Carbon $startDate, Carbon $endDate, ?string $model, ?string $language): array
    {
        $query = AiConversationLog::whereBetween('created_at', [$startDate, $endDate])
                                 ->where('escalated_to_human', true);
        
        if ($model) {
            $query->where('model_used', $model);
        }
        
        if ($language) {
            $query->where('detected_language', $language);
        }

        // Escalation reasons (based on escalation_reason field)
        $escalationReasons = $query->select('escalation_reason', DB::raw('COUNT(*) as count'))
                                  ->whereNotNull('escalation_reason')
                                  ->groupBy('escalation_reason')
                                  ->get()
                                  ->pluck('count', 'escalation_reason')
                                  ->toArray();

        // Escalation by time of day
        $escalationByHour = $query->select(DB::raw('strftime("%H", created_at) as hour'), 
                                         DB::raw('COUNT(*) as count'))
                                 ->groupBy(DB::raw('strftime("%H", created_at)'))
                                 ->get()
                                 ->pluck('count', 'hour')
                                 ->toArray();

        // Fill missing hours with 0
        $hourlyData = [];
        for ($i = 0; $i < 24; $i++) {
            $hourlyData[sprintf('%02d', $i)] = $escalationByHour[sprintf('%02d', $i)] ?? 0;
        }

        // Common escalation triggers (simplified)
        $commonTriggers = [
            'low_confidence' => $query->where('confidence_score', '<', 0.5)->count(),
            'long_processing' => $query->where('processing_time_ms', '>', 5000)->count(),
            'complex_query' => $query->whereRaw('LENGTH(user_message) > 200')->count(),
            'manual_escalation' => $query->whereNotNull('escalation_reason')->count(),
        ];

        return [
            'total_escalations' => $query->count(),
            'escalation_reasons' => $escalationReasons,
            'escalation_by_hour' => $hourlyData,
            'common_triggers' => $commonTriggers,
        ];
    }

    /**
     * Get response time analysis.
     */
    protected function getResponseTimeAnalysis(Carbon $startDate, Carbon $endDate, ?string $model, ?string $language): array
    {
        $query = AiConversationLog::whereBetween('created_at', [$startDate, $endDate]);
        
        if ($model) {
            $query->where('model_used', $model);
        }
        
        if ($language) {
            $query->where('detected_language', $language);
        }

        $responseTimes = $query->pluck('processing_time_ms')->filter()->toArray();
        
        if (empty($responseTimes)) {
            return [
                'avg_response_time' => 0,
                'median_response_time' => 0,
                'p95_response_time' => 0,
                'p99_response_time' => 0,
                'min_response_time' => 0,
                'max_response_time' => 0,
                'response_time_distribution' => [],
            ];
        }

        sort($responseTimes);
        $count = count($responseTimes);

        $avg = array_sum($responseTimes) / $count;
        $median = $count % 2 === 0 
            ? ($responseTimes[$count / 2 - 1] + $responseTimes[$count / 2]) / 2
            : $responseTimes[floor($count / 2)];
        
        $p95Index = (int) ceil(0.95 * $count) - 1;
        $p99Index = (int) ceil(0.99 * $count) - 1;
        
        $p95 = $responseTimes[$p95Index] ?? end($responseTimes);
        $p99 = $responseTimes[$p99Index] ?? end($responseTimes);

        // Response time distribution
        $distribution = [
            'fast' => count(array_filter($responseTimes, fn($t) => $t < 1000)), // < 1s
            'normal' => count(array_filter($responseTimes, fn($t) => $t >= 1000 && $t < 3000)), // 1-3s
            'slow' => count(array_filter($responseTimes, fn($t) => $t >= 3000 && $t < 10000)), // 3-10s
            'very_slow' => count(array_filter($responseTimes, fn($t) => $t >= 10000)), // > 10s
        ];

        return [
            'avg_response_time' => round($avg, 2),
            'median_response_time' => round($median, 2),
            'p95_response_time' => round($p95, 2),
            'p99_response_time' => round($p99, 2),
            'min_response_time' => min($responseTimes),
            'max_response_time' => max($responseTimes),
            'response_time_distribution' => $distribution,
        ];
    }

    /**
     * Get confidence score analysis.
     */
    protected function getConfidenceAnalysis(Carbon $startDate, Carbon $endDate, ?string $model, ?string $language): array
    {
        $query = AiConversationLog::whereBetween('created_at', [$startDate, $endDate]);
        
        if ($model) {
            $query->where('model_used', $model);
        }
        
        if ($language) {
            $query->where('detected_language', $language);
        }

        $confidenceScores = $query->pluck('confidence_score')->filter()->toArray();
        
        if (empty($confidenceScores)) {
            return [
                'avg_confidence' => 0,
                'confidence_distribution' => [],
                'low_confidence_rate' => 0,
            ];
        }

        $avgConfidence = array_sum($confidenceScores) / count($confidenceScores);
        
        $distribution = [
            'very_high' => count(array_filter($confidenceScores, fn($c) => $c >= 0.9)), // 90%+
            'high' => count(array_filter($confidenceScores, fn($c) => $c >= 0.7 && $c < 0.9)), // 70-90%
            'medium' => count(array_filter($confidenceScores, fn($c) => $c >= 0.5 && $c < 0.7)), // 50-70%
            'low' => count(array_filter($confidenceScores, fn($c) => $c < 0.5)), // < 50%
        ];

        $lowConfidenceRate = (count(array_filter($confidenceScores, fn($c) => $c < 0.5)) / count($confidenceScores)) * 100;

        return [
            'avg_confidence' => round($avgConfidence, 3),
            'confidence_distribution' => $distribution,
            'low_confidence_rate' => round($lowConfidenceRate, 2),
        ];
    }

    /**
     * Get language-specific performance.
     */
    protected function getLanguagePerformance(Carbon $startDate, Carbon $endDate, ?string $model): array
    {
        $query = AiConversationLog::whereBetween('created_at', [$startDate, $endDate]);
        
        if ($model) {
            $query->where('model_used', $model);
        }

        // Since detected_language doesn't exist, we'll use a simplified approach
        $languageStats = $query->select(DB::raw("'en' as detected_language"),
                                       DB::raw('COUNT(*) as total'),
                                       DB::raw('AVG(confidence_score) as avg_confidence'),
                                       DB::raw('AVG(processing_time_ms) as avg_response_time'),
                                       DB::raw('SUM(CASE WHEN escalated_to_human = 0 THEN 1 ELSE 0 END) as successful'))
                              ->groupBy(DB::raw("'en'"))
                              ->get()
                              ->map(function ($item) {
                                  $successRate = $item->total > 0 ? ($item->successful / $item->total) * 100 : 0;
                                  return [
                                      'language' => $item->detected_language ?? 'unknown',
                                      'total_interactions' => $item->total,
                                      'success_rate' => round($successRate, 2),
                                      'avg_confidence' => round($item->avg_confidence ?? 0, 3),
                                      'avg_response_time' => round($item->avg_response_time ?? 0, 2),
                                  ];
                              });

        return $languageStats->toArray();
    }

    /**
     * Get model comparison.
     */
    protected function getModelComparison(Carbon $startDate, Carbon $endDate, ?string $language): array
    {
        $query = AiConversationLog::whereBetween('created_at', [$startDate, $endDate]);
        
        if ($language) {
            $query->where('detected_language', $language);
        }

        $modelStats = $query->select('model_used',
                                    DB::raw('COUNT(*) as total'),
                                    DB::raw('AVG(confidence_score) as avg_confidence'),
                                    DB::raw('AVG(processing_time_ms) as avg_response_time'),
                                    DB::raw('SUM(CASE WHEN escalated_to_human = 0 THEN 1 ELSE 0 END) as successful'))
                            ->groupBy('model_used')
                            ->get()
                            ->map(function ($item) {
                                $successRate = $item->total > 0 ? ($item->successful / $item->total) * 100 : 0;
                                return [
                                    'model' => $item->model_used ?? 'unknown',
                                    'total_interactions' => $item->total,
                                    'success_rate' => round($successRate, 2),
                                    'avg_confidence' => round($item->avg_confidence ?? 0, 3),
                                    'avg_response_time' => round($item->avg_response_time ?? 0, 2),
                                ];
                            });

        return $modelStats->toArray();
    }

    /**
     * Get trending topics and common queries.
     */
    protected function getTrendingTopics(Carbon $startDate, Carbon $endDate, ?string $model, ?string $language): array
    {
        // For now, return sample data
        // In a real implementation, this would use NLP to extract topics
        return [
            'common_queries' => [
                'pricing' => 45,
                'support' => 38,
                'features' => 32,
                'billing' => 28,
                'technical_issues' => 25,
            ],
            'trending_keywords' => [
                'refund' => 15,
                'upgrade' => 12,
                'integration' => 10,
                'api' => 8,
                'security' => 7,
            ],
        ];
    }

    /**
     * Get failure analysis.
     */
    protected function getFailureAnalysis(Carbon $startDate, Carbon $endDate, ?string $model, ?string $language): array
    {
        $query = AiConversationLog::whereBetween('created_at', [$startDate, $endDate])
                                 ->where('escalated_to_human', true);
        
        if ($model) {
            $query->where('model_used', $model);
        }
        
        if ($language) {
            $query->where('detected_language', $language);
        }

        $failures = $query->get();
        
        $failurePatterns = [
            'low_confidence_failures' => $failures->where('confidence_score', '<', 0.3)->count(),
            'timeout_failures' => $failures->where('processing_time_ms', '>', 10000)->count(),
            'manual_escalation_failures' => $failures->whereNotNull('escalation_reason')->count(),
            'complex_query_failures' => $failures->filter(function ($log) {
                return strlen($log->user_message) > 300;
            })->count(),
        ];

        return [
            'total_failures' => $failures->count(),
            'failure_patterns' => $failurePatterns,
            'failure_rate_trend' => $this->getFailureRateTrend($startDate, $endDate, $model, $language),
        ];
    }

    /**
     * Get failure rate trend over time.
     */
    protected function getFailureRateTrend(Carbon $startDate, Carbon $endDate, ?string $model, ?string $language): array
    {
        // Simplified trend - in real implementation would calculate daily failure rates
        return [
            'daily_failure_rates' => [], // Would contain daily failure rate percentages
            'trend_direction' => 'stable', // 'improving', 'declining', 'stable'
        ];
    }

    /**
     * Export performance report.
     */
    public function exportReport(array $filters = [], string $format = 'json'): array
    {
        $report = $this->getPerformanceReport($filters);
        
        $exportData = [
            'report_generated_at' => now()->toISOString(),
            'filters_applied' => $filters,
            'format' => $format,
            'data' => $report,
        ];

        return $exportData;
    }
}
