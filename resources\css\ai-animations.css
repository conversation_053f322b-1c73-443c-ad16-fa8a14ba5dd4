/* AI Services Animations - CSS-based for performance */

.ai-service-card,
.ai-3d-icon {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.ai-service-card.animate-in,
.ai-3d-icon.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.ai-3d-icon {
    transition: transform 0.3s ease-in-out;
}

.ai-3d-icon:hover {
    transform: scale(1.05) rotateY(10deg);
}

/* Staggered animation delays */
.ai-service-card:nth-child(1) { transition-delay: 0.1s; }
.ai-service-card:nth-child(2) { transition-delay: 0.2s; }
.ai-service-card:nth-child(3) { transition-delay: 0.3s; }
.ai-service-card:nth-child(4) { transition-delay: 0.4s; }

.ai-3d-icon:nth-child(1) { transition-delay: 0.1s; }
.ai-3d-icon:nth-child(2) { transition-delay: 0.2s; }
.ai-3d-icon:nth-child(3) { transition-delay: 0.3s; }
.ai-3d-icon:nth-child(4) { transition-delay: 0.4s; }

/* Hero animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.ai-hero-title {
    animation: fadeInUp 1s ease-out 0.2s both;
}

.ai-hero-subtitle {
    animation: fadeInUp 1s ease-out 0.4s both;
}

.ai-hero-description {
    animation: fadeInUp 1s ease-out 0.6s both;
}

.ai-hero-buttons {
    animation: fadeInUp 1s ease-out 0.8s both;
}