<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Service extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'name',
        'slug',
        'short_description',
        'description',
        'icon',
        'featured_image',
        'gallery',
        'price_from',
        'features',
        'is_featured',
        'is_active',
        'is_deleted',
        'sort_order',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    protected $casts = [
        'price_from' => 'decimal:2',
        'gallery' => 'array',
        'features' => 'array',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'is_deleted' => 'boolean',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($service) {
            if (empty($service->uuid)) {
                $service->uuid = Str::uuid();
            }
            if (empty($service->slug)) {
                $service->slug = Str::slug($service->name);
            }
        });

        static::updating(function ($service) {
            if ($service->isDirty('name') && empty($service->slug)) {
                $service->slug = Str::slug($service->name);
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Retrieve the model for a bound value.
     */
    public function resolveRouteBinding($value, $field = null)
    {
        return $this->where($field ?? $this->getRouteKeyName(), $value)
                    ->where('is_active', true)
                    ->where('is_deleted', false)
                    ->first();
    }

    /**
     * Scope a query to only include active services.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('is_deleted', false);
    }

    /**
     * Scope a query to only include featured services.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get the projects for this service.
     */
    public function projects(): HasMany
    {
        return $this->hasMany(Project::class);
    }

    /**
     * Get the active projects for this service.
     */
    public function activeProjects(): HasMany
    {
        return $this->projects()->where('is_deleted', false);
    }

    /**
     * Get the published projects for this service.
     */
    public function publishedProjects(): HasMany
    {
        return $this->projects()->where('is_published', true)->where('is_deleted', false);
    }

    /**
     * Get the formatted price.
     */
    public function getFormattedPriceAttribute(): string
    {
        if (!$this->price_from) {
            return 'Contact for Quote';
        }

        return 'From R' . number_format($this->price_from, 2);
    }

    /**
     * Get the primary image URL.
     */
    public function getPrimaryImageAttribute(): string
    {
        return $this->featured_image ?: asset('images/services/placeholder.jpg');
    }

    /**
     * Get all service images.
     */
    public function getAllImagesAttribute(): array
    {
        $images = [];
        
        if ($this->featured_image) {
            $images[] = $this->featured_image;
        }
        
        if ($this->gallery && is_array($this->gallery)) {
            $images = array_merge($images, $this->gallery);
        }
        
        return array_unique($images);
    }

    /**
     * Get the service icon URL or class.
     */
    public function getIconUrlAttribute(): string
    {
        if (!$this->icon) {
            return 'fas fa-cog'; // Default icon class
        }

        // If it's a file path, return the URL
        if (str_starts_with($this->icon, '/') || str_contains($this->icon, '.')) {
            return asset($this->icon);
        }

        // Otherwise, assume it's a CSS class
        return $this->icon;
    }

    /**
     * Get the projects count for this service.
     */
    public function getProjectsCountAttribute(): int
    {
        return $this->activeProjects()->count();
    }

    /**
     * Get the completed projects count for this service.
     */
    public function getCompletedProjectsCountAttribute(): int
    {
        return $this->activeProjects()->where('status', 'completed')->count();
    }

    /**
     * Check if the service has any active projects.
     */
    public function hasActiveProjects(): bool
    {
        return $this->activeProjects()->exists();
    }

    /**
     * Get the service features as a formatted list.
     */
    public function getFormattedFeaturesAttribute(): array
    {
        if (!$this->features || !is_array($this->features)) {
            return [];
        }

        return $this->features;
    }
}
