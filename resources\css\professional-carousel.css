/* Professional Client Carousel Styles */
.client-carousel-container {
    position: relative;
    width: 100%;
    margin: 0 auto;
}

.carousel-track {
    display: flex;
    transition: transform 0.5s ease-in-out;
    will-change: transform;
}

.carousel-slide {
    flex-shrink: 0;
    padding: 0 12px;
}

/* Client Card Styles */
.client-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(229, 231, 235, 0.8);
    height: 280px;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

.client-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-color: rgba(59, 130, 246, 0.3);
}

.client-card.expanded {
    height: auto;
    min-height: 320px;
}

/* Logo Styles */
.client-logo-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80px;
}

.client-logo {
    max-width: 64px;
    max-height: 64px;
    object-fit: contain;
    border-radius: 8px;
    transition: transform 0.2s ease;
}

.client-logo-placeholder {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    color: #2563eb;
    font-size: 1.5rem;
}

/* Text Styles */
.client-card h3 {
    font-size: 1.125rem;
    font-weight: 700;
    color: #111827;
    line-height: 1.4;
    margin-bottom: 4px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.client-card p {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.4;
}

/* Expandable Details */
.client-details {
    opacity: 0;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease-in-out;
}

.client-details.expanded {
    opacity: 1;
    max-height: 200px;
    margin-top: 16px;
}

.client-details blockquote {
    font-style: italic;
    color: #4b5563;
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 12px;
    padding: 0 8px;
    position: relative;
}

/* Expand Button */
.expand-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    color: #2563eb;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    margin-top: auto;
}

.expand-btn:hover {
    color: #1d4ed8;
    background-color: #eff6ff;
}

.expand-btn.expanded .expand-icon {
    transform: rotate(180deg);
}

.expand-icon {
    width: 16px;
    height: 16px;
    transition: transform 0.2s ease;
}

/* Navigation Controls */
.carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: white;
    border: none;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    z-index: 10;
}

.carousel-nav:hover {
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.carousel-nav:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: translateY(-50%);
}

.carousel-prev {
    left: -24px;
}

.carousel-next {
    right: -24px;
}

/* Indicators */
.carousel-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: #d1d5db;
}

.carousel-indicator:hover {
    background-color: #9ca3af;
    transform: scale(1.1);
}

.carousel-indicator.active {
    background-color: #2563eb;
    transform: scale(1.2);
}

/* Responsive Design */
@media (max-width: 1280px) {
    .carousel-slide {
        width: 33.333333%; /* 3 items per row */
    }
}

@media (max-width: 1024px) {
    .carousel-slide {
        width: 50%; /* 2 items per row */
    }
    
    .carousel-prev {
        left: -20px;
    }
    
    .carousel-next {
        right: -20px;
    }
}

@media (max-width: 768px) {
    .carousel-slide {
        width: 100%; /* 1 item per row */
        padding: 0 8px;
    }
    
    .client-card {
        height: 260px;
    }
    
    .carousel-nav {
        width: 40px;
        height: 40px;
    }
    
    .carousel-prev {
        left: -16px;
    }
    
    .carousel-next {
        right: -16px;
    }
}

@media (max-width: 640px) {
    .carousel-prev {
        left: 8px;
    }
    
    .carousel-next {
        right: 8px;
    }
    
    .client-card {
        margin: 0 8px;
        height: 240px;
    }
    
    .client-logo-wrapper {
        height: 60px;
    }
    
    .client-logo,
    .client-logo-placeholder {
        width: 48px;
        height: 48px;
    }
    
    .client-card h3 {
        font-size: 1rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
