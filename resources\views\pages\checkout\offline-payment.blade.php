@extends('layouts.app')

@section('title', 'Payment Instructions - ChiSolution')
@section('meta_description', 'Complete your order payment using our offline payment methods including bank transfer, eWallet, mobile money, and Western Union.')

@section('content')
<section class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="text-center mb-12">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h1 class="text-3xl font-bold text-gray-900 mb-4">Order Confirmed!</h1>
                <p class="text-lg text-gray-600">Your order has been successfully placed. Please complete payment using one of the methods below.</p>
            </div>

            <!-- Order Summary -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Order Summary</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <p class="text-sm text-gray-600">Order Number</p>
                        <p class="font-semibold text-gray-900">{{ $order->order_number }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">Total Amount</p>
                        <p class="font-semibold text-gray-900 text-lg">R{{ number_format($order->total_amount, 2) }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">Order Date</p>
                        <p class="font-semibold text-gray-900">{{ $order->created_at->format('M j, Y \a\t g:i A') }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">Payment Status</p>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            Awaiting Payment
                        </span>
                    </div>
                </div>
            </div>

            <!-- Payment Instructions -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold text-gray-900 mb-6">Payment Instructions</h2>
                
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                        </svg>
                        <div>
                            <h3 class="text-sm font-medium text-blue-800 mb-1">Important Information</h3>
                            <ul class="text-sm text-blue-700 space-y-1">
                                <li>• Please include your order number <strong>{{ $order->order_number }}</strong> as reference</li>
                                <li>• Your order will be processed once payment is confirmed</li>
                                <li>• Allow 1-2 business days for payment verification</li>
                                <li>• Keep your payment receipt for reference</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Payment Methods -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Bank Transfer -->
                    <div class="border border-gray-200 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"></path>
                                    <path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900">Bank Transfer / EFT</h3>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Bank:</span>
                                <span class="font-medium">First National Bank</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Account Name:</span>
                                <span class="font-medium">ChiSolution (Pty) Ltd</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Account Number:</span>
                                <span class="font-medium font-mono">***********</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Branch Code:</span>
                                <span class="font-medium font-mono">250655</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Reference:</span>
                                <span class="font-medium font-mono">{{ $order->order_number }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- eWallet -->
                    <div class="border border-gray-200 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900">eWallet</h3>
                        </div>
                        <div class="space-y-3 text-sm">
                            <div>
                                <p class="font-medium text-gray-900 mb-1">FNB eWallet</p>
                                <p class="text-gray-600">Cell: <span class="font-mono">************</span></p>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900 mb-1">Capitec Pay</p>
                                <p class="text-gray-600">Cell: <span class="font-mono">************</span></p>
                            </div>
                            <div class="bg-gray-50 p-3 rounded">
                                <p class="text-xs text-gray-600">
                                    <strong>Reference:</strong> {{ $order->order_number }}
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile Money -->
                    <div class="border border-gray-200 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900">Mobile Money</h3>
                        </div>
                        <div class="space-y-3 text-sm">
                            <div>
                                <p class="font-medium text-gray-900 mb-1">MTN Mobile Money</p>
                                <p class="text-gray-600">Number: <span class="font-mono">************</span></p>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900 mb-1">Vodacom M-Pesa</p>
                                <p class="text-gray-600">Number: <span class="font-mono">************</span></p>
                            </div>
                            <div class="bg-gray-50 p-3 rounded">
                                <p class="text-xs text-gray-600">
                                    <strong>Reference:</strong> {{ $order->order_number }}
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Western Union -->
                    <div class="border border-gray-200 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900">Western Union</h3>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Receiver Name:</span>
                                <span class="font-medium">John Doe</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Country:</span>
                                <span class="font-medium">South Africa</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">City:</span>
                                <span class="font-medium">Johannesburg</span>
                            </div>
                            <div class="bg-gray-50 p-3 rounded">
                                <p class="text-xs text-gray-600">
                                    <strong>Reference:</strong> {{ $order->order_number }}<br>
                                    <strong>Note:</strong> Send MTCN number via email
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Next Steps -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Next Steps</h2>
                <div class="space-y-4">
                    <div class="flex items-start">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                            <span class="text-sm font-semibold text-blue-600">1</span>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-900">Make Payment</h3>
                            <p class="text-sm text-gray-600">Use any of the payment methods above to complete your payment.</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                            <span class="text-sm font-semibold text-blue-600">2</span>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-900">Send Proof of Payment</h3>
                            <p class="text-sm text-gray-600">Email your payment receipt to <a href="mailto:<EMAIL>" class="text-blue-600 hover:underline"><EMAIL></a></p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                            <span class="text-sm font-semibold text-blue-600">3</span>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-900">Order Processing</h3>
                            <p class="text-sm text-gray-600">We'll verify your payment and process your order within 1-2 business days.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="bg-gray-100 rounded-lg p-6 text-center">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Need Help?</h3>
                <p class="text-gray-600 mb-4">If you have any questions about your payment or order, please contact us:</p>
                <div class="flex flex-col sm:flex-row justify-center items-center space-y-2 sm:space-y-0 sm:space-x-6">
                    <a href="mailto:<EMAIL>" class="text-blue-600 hover:underline flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                        </svg>
                        <EMAIL>
                    </a>
                    <a href="tel:+27123456789" class="text-blue-600 hover:underline flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                        </svg>
                        +27 12 345 6789
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Offline Payment Logger
    class OfflinePaymentLogger {
        constructor() {
            this.apiEndpoint = '/api/log-checkout-activity';
            this.sessionId = this.getSessionId();
            this.orderId = '{{ $order->uuid }}';
            this.orderNumber = '{{ $order->order_number }}';
            this.paymentMethod = '{{ $order->payment_method }}';
            this.totalAmount = {{ $order->total_amount }};
            this.userAuthenticated = {{ $order->user_id ? 'true' : 'false' }};
            this.customerType = '{{ $order->user_id ? "registered" : "guest" }}';
            this.cartType = '{{ $order->user_id ? "user" : "session" }}';
            this.init();
        }

        getSessionId() {
            let sessionId = sessionStorage.getItem('checkout_session_id');
            if (!sessionId) {
                sessionId = 'offline_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                sessionStorage.setItem('checkout_session_id', sessionId);
            }
            return sessionId;
        }

        init() {
            this.setupErrorHandling();
            this.logOfflinePaymentPageLoad();
            this.setupInteractionTracking();
        }

        setupErrorHandling() {
            window.addEventListener('error', (event) => {
                this.logError('javascript_error', {
                    message: event.message,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    stack: event.error?.stack,
                    timestamp: Date.now()
                });
            });

            window.addEventListener('unhandledrejection', (event) => {
                this.logError('promise_rejection', {
                    reason: event.reason?.toString(),
                    stack: event.reason?.stack,
                    timestamp: Date.now()
                });
            });
        }

        logOfflinePaymentPageLoad() {
            this.pageLoadTime = Date.now();

            this.logInteraction('offline_payment_page_load', {
                order_id: this.orderId,
                order_number: this.orderNumber,
                payment_method: this.paymentMethod,
                total_amount: this.totalAmount,
                page_url: window.location.href,
                referrer: document.referrer,
                timestamp: this.pageLoadTime
            });

            // Log checkout completion for offline payment
            this.logInteraction('checkout_completed_offline', {
                order_id: this.orderId,
                order_number: this.orderNumber,
                payment_method: this.paymentMethod,
                total_amount: this.totalAmount,
                requires_manual_payment: true,
                payment_instructions_displayed: true,
                timestamp: Date.now()
            });
        }

        setupInteractionTracking() {
            // Track clicks on payment method sections
            document.addEventListener('click', (event) => {
                const element = event.target;

                // Track contact information clicks
                if (element.tagName === 'A' && (element.href?.includes('mailto:') || element.href?.includes('tel:'))) {
                    this.logInteraction('contact_method_clicked', {
                        contact_type: element.href.includes('mailto:') ? 'email' : 'phone',
                        contact_value: element.textContent?.trim(),
                        timestamp: Date.now()
                    });
                }

                // Track general interactions
                if (element.tagName === 'A' || element.tagName === 'BUTTON') {
                    this.logInteraction('offline_payment_interaction', {
                        element_type: element.tagName.toLowerCase(),
                        element_id: element.id,
                        element_class: element.className,
                        element_text: element.textContent?.trim().substring(0, 50),
                        element_href: element.href || null,
                        timestamp: Date.now()
                    });
                }
            });

            // Track copy actions (if any copy buttons are added)
            document.addEventListener('copy', (event) => {
                this.logInteraction('payment_info_copied', {
                    copied_text_length: event.clipboardData?.getData('text/plain')?.length || 0,
                    timestamp: Date.now()
                });
            });

            // Track page unload
            window.addEventListener('beforeunload', () => {
                this.logInteraction('offline_payment_page_unload', {
                    time_on_page: Date.now() - this.pageLoadTime,
                    timestamp: Date.now()
                });
            });
        }

        logError(type, data) {
            this.sendLog('error', {
                type: type,
                data: Object.assign(data, {
                    order_id: this.orderId,
                    order_number: this.orderNumber,
                    payment_method: this.paymentMethod
                }),
                session_id: this.sessionId,
                timestamp: Date.now(),
                page_url: window.location.href
            });
        }

        logInteraction(type, data) {
            this.sendLog('interaction', {
                type: type,
                data: Object.assign(data, {
                    order_id: this.orderId,
                    order_number: this.orderNumber,
                    payment_method: this.paymentMethod
                }),
                session_id: this.sessionId,
                timestamp: Date.now()
            });
        }

        async sendLog(logType, logData) {
            try {
                await fetch(this.apiEndpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                    },
                    body: JSON.stringify({
                        log_type: logType,
                        log_data: logData
                    })
                });
            } catch (error) {
                console.error('Failed to send offline payment log:', error);
            }
        }
    }

    // Initialize offline payment logger
    const offlinePaymentLogger = new OfflinePaymentLogger();
});
</script>
@endpush
@endsection
