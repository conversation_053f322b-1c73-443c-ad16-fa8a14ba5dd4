<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\LoginHistory;
use App\Models\Role;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Carbon\Carbon;

class ProfileLoginHistoryTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Set locale for testing
        app()->setLocale('en');

        // Create roles
        Role::create(['name' => 'admin', 'slug' => 'admin', 'display_name' => 'Administrator']);
        Role::create(['name' => 'staff', 'slug' => 'staff', 'display_name' => 'Staff']);
        Role::create(['name' => 'customer', 'slug' => 'customer', 'display_name' => 'Customer']);
        Role::create(['name' => 'client', 'slug' => 'client', 'display_name' => 'Client']);
    }

    /** @test */
    public function user_can_access_login_history_page()
    {
        $user = User::factory()->create(['role_id' => Role::where('name', 'customer')->first()->id]);
        
        $response = $this->actingAs($user)->get(route('profile.login-history'));
        
        $response->assertStatus(200);
        $response->assertViewIs('profile.login-history');
        $response->assertViewHas('stats');
    }

    /** @test */
    public function user_can_view_their_own_login_history_data()
    {
        $user = User::factory()->create(['role_id' => Role::where('name', 'customer')->first()->id]);
        
        // Create some login history for this user
        LoginHistory::factory()->count(5)->create([
            'user_id' => $user->id,
            'login_successful' => true,
            'created_at' => now()->subDays(1)
        ]);
        
        // Create login history for another user (should not be visible)
        $otherUser = User::factory()->create(['role_id' => Role::where('name', 'customer')->first()->id]);
        LoginHistory::factory()->count(3)->create([
            'user_id' => $otherUser->id,
            'login_successful' => true,
            'created_at' => now()->subDays(2)
        ]);
        
        $response = $this->actingAs($user)->get(route('profile.login-history.data'));
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                '*' => [
                    'uuid',
                    'created_at',
                    'login_successful',
                    'device_type',
                    'device_info',
                    'formatted_location',
                    'ip_address'
                ]
            ],
            'pagination'
        ]);
        
        $data = $response->json();
        $this->assertTrue($data['success']);
        $this->assertCount(5, $data['data']); // Only user's own data
    }

    /** @test */
    public function user_can_filter_login_history_by_status()
    {
        $user = User::factory()->create(['role_id' => Role::where('name', 'customer')->first()->id]);
        
        // Create successful and failed logins
        LoginHistory::factory()->count(3)->create([
            'user_id' => $user->id,
            'login_successful' => true,
            'created_at' => now()->subDays(1)
        ]);
        
        LoginHistory::factory()->count(2)->create([
            'user_id' => $user->id,
            'login_successful' => false,
            'created_at' => now()->subDays(1)
        ]);
        
        // Test successful logins filter
        $response = $this->actingAs($user)->get(route('profile.login-history.data', ['status' => 'success']));
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertCount(3, $data['data']);
        
        // Test failed logins filter
        $response = $this->actingAs($user)->get(route('profile.login-history.data', ['status' => 'failed']));
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertCount(2, $data['data']);
    }

    /** @test */
    public function user_can_filter_login_history_by_device_type()
    {
        $user = User::factory()->create(['role_id' => Role::where('name', 'customer')->first()->id]);
        
        // Create logins with different device types
        LoginHistory::factory()->count(2)->create([
            'user_id' => $user->id,
            'device_type' => 'desktop',
            'created_at' => now()->subDays(1)
        ]);
        
        LoginHistory::factory()->count(3)->create([
            'user_id' => $user->id,
            'device_type' => 'mobile',
            'created_at' => now()->subDays(1)
        ]);
        
        // Test desktop filter
        $response = $this->actingAs($user)->get(route('profile.login-history.data', ['device_type' => 'desktop']));
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertCount(2, $data['data']);
        
        // Test mobile filter
        $response = $this->actingAs($user)->get(route('profile.login-history.data', ['device_type' => 'mobile']));
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertCount(3, $data['data']);
    }

    /** @test */
    public function user_can_filter_login_history_by_date_range()
    {
        $user = User::factory()->create(['role_id' => Role::where('name', 'customer')->first()->id]);
        
        // Create logins on different dates
        LoginHistory::factory()->count(2)->create([
            'user_id' => $user->id,
            'created_at' => now() // Today
        ]);
        
        LoginHistory::factory()->count(3)->create([
            'user_id' => $user->id,
            'created_at' => now()->subDays(2) // 2 days ago
        ]);
        
        LoginHistory::factory()->count(1)->create([
            'user_id' => $user->id,
            'created_at' => now()->subDays(10) // 10 days ago
        ]);
        
        // Test today filter
        $response = $this->actingAs($user)->get(route('profile.login-history.data', ['date_range' => 'today']));
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertCount(2, $data['data']);
        
        // Test 7 days filter
        $response = $this->actingAs($user)->get(route('profile.login-history.data', ['date_range' => '7_days']));
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertCount(5, $data['data']); // Today + 2 days ago
    }

    /** @test */
    public function user_can_filter_login_history_by_custom_date_range()
    {
        $user = User::factory()->create(['role_id' => Role::where('name', 'customer')->first()->id]);
        
        // Create logins on specific dates
        LoginHistory::factory()->create([
            'user_id' => $user->id,
            'created_at' => Carbon::parse('2024-01-15 10:00:00')
        ]);
        
        LoginHistory::factory()->create([
            'user_id' => $user->id,
            'created_at' => Carbon::parse('2024-01-20 10:00:00')
        ]);
        
        LoginHistory::factory()->create([
            'user_id' => $user->id,
            'created_at' => Carbon::parse('2024-01-25 10:00:00')
        ]);
        
        // Test custom date range
        $response = $this->actingAs($user)->get(route('profile.login-history.data', [
            'date_range' => 'custom',
            'start_date' => '2024-01-18',
            'end_date' => '2024-01-22'
        ]));
        
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertCount(1, $data['data']); // Only the login on 2024-01-20
    }

    /** @test */
    public function user_can_search_login_history_by_ip_address()
    {
        $user = User::factory()->create(['role_id' => Role::where('name', 'customer')->first()->id]);
        
        // Create logins with different IP addresses
        LoginHistory::factory()->create([
            'user_id' => $user->id,
            'ip_address' => '*************',
            'created_at' => now()->subDays(1)
        ]);
        
        LoginHistory::factory()->create([
            'user_id' => $user->id,
            'ip_address' => '*********',
            'created_at' => now()->subDays(1)
        ]);
        
        LoginHistory::factory()->create([
            'user_id' => $user->id,
            'ip_address' => '*************',
            'created_at' => now()->subDays(1)
        ]);
        
        // Test IP search
        $response = $this->actingAs($user)->get(route('profile.login-history.data', ['ip_address' => '192.168']));
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertCount(2, $data['data']); // Both 192.168.x.x addresses
    }

    /** @test */
    public function login_history_data_is_paginated()
    {
        $user = User::factory()->create(['role_id' => Role::where('name', 'customer')->first()->id]);
        
        // Create more login history than the default page size
        LoginHistory::factory()->count(20)->create([
            'user_id' => $user->id,
            'created_at' => now()->subDays(1)
        ]);
        
        $response = $this->actingAs($user)->get(route('profile.login-history.data', ['per_page' => 10]));
        
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertCount(10, $data['data']);
        $this->assertEquals(1, $data['pagination']['current_page']);
        $this->assertEquals(2, $data['pagination']['last_page']);
        $this->assertEquals(20, $data['pagination']['total']);
    }

    /** @test */
    public function profile_edit_page_shows_login_history_tab()
    {
        $user = User::factory()->create(['role_id' => Role::where('name', 'customer')->first()->id]);
        
        $response = $this->actingAs($user)->get(route('profile.edit'));
        
        $response->assertStatus(200);
        $response->assertSee('Login History');
        $response->assertSee(route('profile.login-history'));
    }

    /** @test */
    public function login_history_page_shows_profile_information_tab()
    {
        $user = User::factory()->create(['role_id' => Role::where('name', 'customer')->first()->id]);
        
        $response = $this->actingAs($user)->get(route('profile.login-history'));
        
        $response->assertStatus(200);
        $response->assertSee('Profile Information');
        $response->assertSee(route('profile.edit'));
    }

    /** @test */
    public function unauthenticated_user_cannot_access_login_history()
    {
        $response = $this->get(route('profile.login-history'));
        $response->assertRedirect(route('login'));
        
        $response = $this->get(route('profile.login-history.data'));
        $response->assertRedirect(route('login'));
    }
}
