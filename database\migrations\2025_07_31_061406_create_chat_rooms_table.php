<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_rooms', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->enum('type', ['visitor', 'customer', 'internal', 'support'])->default('visitor');
            $table->enum('status', ['active', 'waiting', 'closed', 'archived'])->default('active');
            $table->string('title')->nullable();
            $table->json('visitor_info')->nullable()->comment('Anonymous visitor information');
            $table->json('metadata')->nullable()->comment('Additional room metadata');
            $table->tinyInteger('priority')->unsigned()->default(1)->comment('1=Low, 2=Medium, 3=High, 4=Urgent');
            $table->char('language', 2)->default('en');
            $table->timestamp('closed_at')->nullable();
            $table->timestamps();

            // Performance indexes
            $table->index(['status', 'updated_at'], 'idx_status_updated');
            $table->index(['type', 'status'], 'idx_type_status');
            $table->index(['priority', 'created_at'], 'idx_priority_created');
            $table->index('language', 'idx_language');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_rooms');
    }
};
