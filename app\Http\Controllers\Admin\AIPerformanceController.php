<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\AIPerformanceReportingService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Carbon\Carbon;

class AIPerformanceController extends Controller
{
    protected AIPerformanceReportingService $performanceService;

    public function __construct(AIPerformanceReportingService $performanceService)
    {
        $this->performanceService = $performanceService;
        $this->middleware('auth');
    }

    /**
     * Display the AI performance dashboard.
     */
    public function index(): View
    {
        $defaultFilters = [
            'start_date' => now()->subDays(30),
            'end_date' => now(),
        ];

        $report = $this->performanceService->getPerformanceReport($defaultFilters);

        return view('admin.chat.ai-performance.index', compact('report', 'defaultFilters'));
    }

    /**
     * Get AI performance report with filters.
     */
    public function getReport(Request $request): JsonResponse
    {
        $filters = $this->buildFilters($request);
        
        try {
            $report = $this->performanceService->getPerformanceReport($filters);
            
            return response()->json([
                'success' => true,
                'data' => $report,
                'filters' => $filters,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to generate performance report',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get performance summary.
     */
    public function getSummary(Request $request): JsonResponse
    {
        $filters = $this->buildFilters($request);
        
        try {
            $report = $this->performanceService->getPerformanceReport($filters);
            
            return response()->json([
                'success' => true,
                'data' => $report['summary'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get performance summary',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get success metrics.
     */
    public function getSuccessMetrics(Request $request): JsonResponse
    {
        $filters = $this->buildFilters($request);
        
        try {
            $report = $this->performanceService->getPerformanceReport($filters);
            
            return response()->json([
                'success' => true,
                'data' => $report['success_metrics'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get success metrics',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get escalation analysis.
     */
    public function getEscalationAnalysis(Request $request): JsonResponse
    {
        $filters = $this->buildFilters($request);
        
        try {
            $report = $this->performanceService->getPerformanceReport($filters);
            
            return response()->json([
                'success' => true,
                'data' => $report['escalation_analysis'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get escalation analysis',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get response time analysis.
     */
    public function getResponseTimeAnalysis(Request $request): JsonResponse
    {
        $filters = $this->buildFilters($request);
        
        try {
            $report = $this->performanceService->getPerformanceReport($filters);
            
            return response()->json([
                'success' => true,
                'data' => $report['response_time_analysis'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get response time analysis',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get confidence analysis.
     */
    public function getConfidenceAnalysis(Request $request): JsonResponse
    {
        $filters = $this->buildFilters($request);
        
        try {
            $report = $this->performanceService->getPerformanceReport($filters);
            
            return response()->json([
                'success' => true,
                'data' => $report['confidence_analysis'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get confidence analysis',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get language performance.
     */
    public function getLanguagePerformance(Request $request): JsonResponse
    {
        $filters = $this->buildFilters($request);
        
        try {
            $report = $this->performanceService->getPerformanceReport($filters);
            
            return response()->json([
                'success' => true,
                'data' => $report['language_performance'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get language performance',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get model comparison.
     */
    public function getModelComparison(Request $request): JsonResponse
    {
        $filters = $this->buildFilters($request);
        
        try {
            $report = $this->performanceService->getPerformanceReport($filters);
            
            return response()->json([
                'success' => true,
                'data' => $report['model_comparison'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get model comparison',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get trending topics.
     */
    public function getTrendingTopics(Request $request): JsonResponse
    {
        $filters = $this->buildFilters($request);
        
        try {
            $report = $this->performanceService->getPerformanceReport($filters);
            
            return response()->json([
                'success' => true,
                'data' => $report['trending_topics'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get trending topics',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get failure analysis.
     */
    public function getFailureAnalysis(Request $request): JsonResponse
    {
        $filters = $this->buildFilters($request);
        
        try {
            $report = $this->performanceService->getPerformanceReport($filters);
            
            return response()->json([
                'success' => true,
                'data' => $report['failure_analysis'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get failure analysis',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export performance report.
     */
    public function exportReport(Request $request): JsonResponse
    {
        $filters = $this->buildFilters($request);
        $format = $request->get('format', 'json');
        
        try {
            $exportData = $this->performanceService->exportReport($filters, $format);
            
            return response()->json([
                'success' => true,
                'data' => $exportData,
                'download_url' => $this->generateDownloadUrl($exportData, $format),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to export report',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Build filters from request.
     */
    protected function buildFilters(Request $request): array
    {
        $filters = [];

        if ($request->has('start_date')) {
            $filters['start_date'] = Carbon::parse($request->get('start_date'));
        }

        if ($request->has('end_date')) {
            $filters['end_date'] = Carbon::parse($request->get('end_date'));
        }

        if ($request->has('model') && $request->get('model') !== 'all') {
            $filters['model'] = $request->get('model');
        }

        if ($request->has('language') && $request->get('language') !== 'all') {
            $filters['language'] = $request->get('language');
        }

        return $filters;
    }

    /**
     * Generate download URL for exported report.
     */
    protected function generateDownloadUrl(array $exportData, string $format): ?string
    {
        // In a real implementation, this would save the file and return a download URL
        // For now, return null to indicate inline download
        return null;
    }
}
