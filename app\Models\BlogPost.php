<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;
use App\Facades\ImageService;

class BlogPost extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'uuid',
        'title',
        'slug',
        'excerpt',
        'content',
        'featured_image',
        'gallery_images',
        'videos',
        'social_embeds',
        'category_id',
        'author_id',
        'service_ids',
        'view_count',
        'is_published',
        'is_featured',
        'is_deleted',
        'published_at',
        'scheduled_at',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'og_title',
        'og_description',
        'og_image',
        'og_type',
        'twitter_card',
        'twitter_title',
        'twitter_description',
        'twitter_image',
        'canonical_url',
        'focus_keyword',
        'schema_data',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'gallery_images' => 'array',
            'videos' => 'array',
            'social_embeds' => 'array',
            'service_ids' => 'array',
            'schema_data' => 'array',
            'view_count' => 'integer',
            'is_published' => 'boolean',
            'is_featured' => 'boolean',
            'is_deleted' => 'boolean',
            'published_at' => 'datetime',
            'scheduled_at' => 'datetime',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($post) {
            if (empty($post->uuid)) {
                $post->uuid = Str::uuid();
            }
            if (empty($post->slug)) {
                $post->slug = Str::slug($post->title);
            }
            if (empty($post->excerpt) && $post->content) {
                $post->excerpt = Str::limit(strip_tags($post->content), 160);
            }
        });

        static::updating(function ($post) {
            if ($post->isDirty('title') && empty($post->slug)) {
                $post->slug = Str::slug($post->title);
            }
            if ($post->isDirty('content') && empty($post->excerpt)) {
                $post->excerpt = Str::limit(strip_tags($post->content), 160);
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Retrieve the model for a bound value.
     */
    public function resolveRouteBinding($value, $field = null)
    {
        return $this->where($field ?? $this->getRouteKeyName(), $value)
                    ->where('is_published', true)
                    ->where('is_deleted', false)
                    ->first();
    }

    /**
     * Get the category that owns the blog post.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(BlogCategory::class);
    }

    /**
     * Get the author that owns the blog post.
     */
    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    /**
     * Get the services attribute.
     * This returns the services collection and is accessed as an attribute.
     */
    public function getServicesAttribute()
    {
        // Cache the result to avoid multiple queries
        if (!isset($this->attributes['_services_cache'])) {
            if (empty($this->service_ids)) {
                $this->attributes['_services_cache'] = collect();
            } else {
                $this->attributes['_services_cache'] = Service::whereIn('id', $this->service_ids)
                    ->active()
                    ->get();
            }
        }

        return $this->attributes['_services_cache'];
    }

    /**
     * Get the services relationship.
     * This method returns a query builder that can be used for relationship queries.
     */
    public function services()
    {
        // Return a query builder that filters services by the service_ids JSON column
        return Service::whereIn('id', $this->service_ids ?? [])
            ->where('is_active', true)
            ->where('is_deleted', false);
    }

    /**
     * Get the services relationship for eager loading.
     * This method is kept for relationship-based queries and eager loading.
     */
    public function servicesRelation()
    {
        return $this->belongsToMany(Service::class, 'blog_post_services', 'blog_post_id', 'service_id')
            ->where('services.is_active', true)
            ->where('services.is_deleted', false);
    }

    /**
     * Check if blog post is associated with a specific service.
     */
    public function hasService(int $serviceId): bool
    {
        return in_array($serviceId, $this->service_ids ?? []);
    }

    /**
     * Scope a query to only include published posts.
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true)
            ->where('is_deleted', false)
            ->whereNotNull('published_at');
    }

    /**
     * Scope a query to only include featured posts.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to order by published date.
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('published_at', 'desc');
    }

    /**
     * Scope a query to include posts with specific service IDs (MySQL version compatible).
     */
    public function scopeWithServices($query, array $serviceIds)
    {
        if (empty($serviceIds)) {
            return $query;
        }

        return $query->where(function($subQuery) use ($serviceIds) {
            try {
                // Use JSON_CONTAINS for MySQL 5.7+
                foreach ($serviceIds as $serviceId) {
                    $subQuery->orWhereRaw('JSON_CONTAINS(service_ids, ?)', [json_encode($serviceId)]);
                }
            } catch (\Exception $e) {
                // Fallback for older MySQL versions
                \Log::warning('JSON_CONTAINS not available, using LIKE fallback for service filtering', [
                    'service_ids' => $serviceIds,
                    'error' => $e->getMessage()
                ]);

                foreach ($serviceIds as $serviceId) {
                    $subQuery->orWhere('service_ids', 'LIKE', '%"' . $serviceId . '"%');
                }
            }
        });
    }

    /**
     * Scope a query to include posts with overlapping services to a given post.
     */
    public function scopeWithOverlappingServices($query, BlogPost $post)
    {
        if (empty($post->service_ids)) {
            return $query;
        }

        return $query->withServices($post->service_ids);
    }

    /**
     * Get the featured image URL.
     */
    public function getFeaturedImageUrlAttribute(): string
    {
        if (!$this->featured_image) {
            return asset('images/blog/post-placeholder.jpg');
        }

        if (Str::startsWith($this->featured_image, ['http://', 'https://'])) {
            return $this->featured_image;
        }

        return asset('storage/' . $this->featured_image);
    }

    /**
     * Get the reading time estimate.
     */
    public function getReadingTimeAttribute(): int
    {
        $wordCount = str_word_count(strip_tags($this->content));
        return max(1, ceil($wordCount / 200)); // Assuming 200 words per minute
    }

    /**
     * Get the formatted published date.
     */
    public function getFormattedPublishedDateAttribute(): string
    {
        return $this->published_at ? $this->published_at->format('M j, Y') : '';
    }

    /**
     * Get all gallery image URLs.
     */
    public function getGalleryImageUrlsAttribute(): array
    {
        if (empty($this->gallery_images)) {
            return [];
        }

        return array_map(function ($image) {
            if (Str::startsWith($image, ['http://', 'https://'])) {
                return $image;
            }
            return asset('storage/' . $image);
        }, $this->gallery_images);
    }

    /**
     * Get the first gallery image URL.
     */
    public function getFirstGalleryImageUrlAttribute(): ?string
    {
        $urls = $this->gallery_image_urls;
        return !empty($urls) ? $urls[0] : null;
    }

    /**
     * Add image to gallery.
     */
    public function addGalleryImage(string $imagePath): void
    {
        $images = $this->gallery_images ?? [];
        $images[] = $imagePath;
        $this->gallery_images = $images;
    }

    /**
     * Remove image from gallery.
     */
    public function removeGalleryImage(string $imagePath): void
    {
        $images = $this->gallery_images ?? [];
        $this->gallery_images = array_values(array_filter($images, fn($img) => $img !== $imagePath));
    }

    /**
     * Get total image count (featured + gallery).
     */
    public function getTotalImageCountAttribute(): int
    {
        $count = 0;
        if ($this->featured_image) {
            $count++;
        }
        if ($this->gallery_images) {
            $count += count($this->gallery_images);
        }
        return $count;
    }

    /**
     * Increment view count.
     */
    public function incrementViewCount(): void
    {
        $this->increment('view_count');
    }

    /**
     * Process and set featured image using ImageService.
     */
    public function processFeaturedImage(\Illuminate\Http\UploadedFile $file): array
    {
        $result = ImageService::processUploadedImage($file, [
            'subdirectory' => "blog/posts/{$this->id}/featured",
            'create_variants' => true,
            'create_webp' => true,
            'sizes' => [
                'thumbnail' => ['width' => 300, 'height' => 200, 'crop' => true],
                'medium' => ['width' => 600, 'height' => 400, 'crop' => true],
                'large' => ['width' => 1200, 'height' => 800, 'crop' => false],
            ],
            'quality' => 85,
            'strip_metadata' => true
        ]);

        if ($result['success']) {
            // Delete old featured image if exists
            if ($this->featured_image) {
                ImageService::deleteImage($this->featured_image, true);
            }

            $this->update(['featured_image' => $result['path']]);
        }

        return $result;
    }

    /**
     * Add image to gallery using ImageService.
     */
    public function addGalleryImageFromUpload(\Illuminate\Http\UploadedFile $file): array
    {
        $result = ImageService::processUploadedImage($file, [
            'subdirectory' => "blog/posts/{$this->id}/gallery",
            'create_variants' => true,
            'create_webp' => true,
            'sizes' => [
                'thumbnail' => ['width' => 300, 'height' => 300, 'crop' => true],
                'medium' => ['width' => 600, 'height' => 600, 'crop' => false],
                'large' => ['width' => 1200, 'height' => 1200, 'crop' => false],
            ],
            'quality' => 85,
            'strip_metadata' => true
        ]);

        if ($result['success']) {
            $this->addGalleryImage($result['path']);
            $this->save();
        }

        return $result;
    }

    /**
     * Remove gallery image and delete files using ImageService.
     */
    public function removeGalleryImageWithCleanup(string $imagePath): bool
    {
        // Remove from gallery array
        $this->removeGalleryImage($imagePath);
        $this->save();

        // Delete physical files
        return ImageService::deleteImage($imagePath, true);
    }

    /**
     * Get optimized image URL using ImageService variants.
     */
    public function getOptimizedImageUrl(string $imagePath, string $size = 'medium'): string
    {
        // Check if variant exists
        $pathInfo = pathinfo($imagePath);
        $variantPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_' . $size . '.' . $pathInfo['extension'];

        if (file_exists(storage_path('app/public/' . $variantPath))) {
            return asset('storage/' . $variantPath);
        }

        // Fallback to original
        return asset('storage/' . $imagePath);
    }

    /**
     * Get WebP version URL if available.
     */
    public function getWebPImageUrl(string $imagePath): ?string
    {
        $pathInfo = pathinfo($imagePath);
        $webpPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '.webp';

        if (file_exists(storage_path('app/public/' . $webpPath))) {
            return asset('storage/' . $webpPath);
        }

        return null;
    }

    /**
     * Clean up all images when post is deleted.
     */
    public function cleanupImages(): bool
    {
        $success = true;

        // Delete featured image
        if ($this->featured_image) {
            if (!ImageService::deleteImage($this->featured_image, true)) {
                $success = false;
            }
        }

        // Delete gallery images
        if ($this->gallery_images) {
            foreach ($this->gallery_images as $imagePath) {
                if (!ImageService::deleteImage($imagePath, true)) {
                    $success = false;
                }
            }
        }

        return $success;
    }

    // ==================== COMMENT RELATIONSHIPS ====================

    /**
     * Get all comments for this blog post.
     */
    public function comments(): HasMany
    {
        return $this->hasMany(BlogComment::class);
    }

    /**
     * Get approved comments for this blog post.
     */
    public function approvedComments(): HasMany
    {
        return $this->hasMany(BlogComment::class)
            ->where('is_approved', true)
            ->where('is_deleted', false)
            ->orderBy('created_at', 'desc');
    }

    /**
     * Get top-level approved comments (not replies).
     */
    public function topLevelComments(): HasMany
    {
        return $this->hasMany(BlogComment::class)
            ->whereNull('parent_comment_id')
            ->where('is_approved', true)
            ->where('is_deleted', false)
            ->orderBy('created_at', 'desc');
    }

    /**
     * Get pending comments for admin review.
     */
    public function pendingComments(): HasMany
    {
        return $this->hasMany(BlogComment::class)
            ->where('is_approved', false)
            ->where('is_deleted', false)
            ->orderBy('created_at', 'desc');
    }

    /**
     * Get comments with ratings.
     */
    public function ratedComments(): HasMany
    {
        return $this->hasMany(BlogComment::class)
            ->whereNotNull('rating')
            ->where('is_approved', true)
            ->where('is_deleted', false);
    }

    // ==================== RATING METHODS ====================

    /**
     * Get the average rating for this blog post.
     */
    public function getAverageRatingAttribute(): float
    {
        return $this->ratedComments()->avg('rating') ?? 0.0;
    }

    /**
     * Get the total number of ratings.
     */
    public function getTotalRatingsAttribute(): int
    {
        return $this->ratedComments()->count();
    }

    /**
     * Get the rating distribution (1-5 stars).
     */
    public function getRatingDistributionAttribute(): array
    {
        $distribution = [];
        for ($i = 1; $i <= 5; $i++) {
            $distribution[$i] = $this->ratedComments()
                ->where('rating', $i)
                ->count();
        }
        return $distribution;
    }

    /**
     * Get the formatted average rating.
     */
    public function getFormattedAverageRatingAttribute(): string
    {
        return number_format($this->average_rating, 1);
    }

    /**
     * Get the star rating display.
     */
    public function getStarRatingAttribute(): string
    {
        $rating = round($this->average_rating);
        $stars = '';
        for ($i = 1; $i <= 5; $i++) {
            $stars .= $i <= $rating ? '★' : '☆';
        }
        return $stars;
    }

    // ==================== COMMENT COUNTS ====================

    /**
     * Get the total number of approved comments.
     */
    public function getCommentCountAttribute(): int
    {
        return $this->approvedComments()->count();
    }

    /**
     * Get the total number of pending comments.
     */
    public function getPendingCommentCountAttribute(): int
    {
        return $this->pendingComments()->count();
    }

    /**
     * Check if user has already rated this post.
     */
    public function hasUserRated(int $userId): bool
    {
        return $this->comments()
            ->where('user_id', $userId)
            ->whereNotNull('rating')
            ->where('is_deleted', false)
            ->exists();
    }

    /**
     * Get user's rating for this post.
     */
    public function getUserRating(int $userId): ?int
    {
        $comment = $this->comments()
            ->where('user_id', $userId)
            ->whereNotNull('rating')
            ->where('is_deleted', false)
            ->first();

        return $comment ? $comment->rating : null;
    }

    /**
     * Check if user has already commented on this post.
     */
    public function hasUserCommented(int $userId): bool
    {
        return $this->comments()
            ->where('user_id', $userId)
            ->where('is_deleted', false)
            ->exists();
    }
}
