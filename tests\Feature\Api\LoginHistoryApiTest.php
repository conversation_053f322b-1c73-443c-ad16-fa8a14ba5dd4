<?php

namespace Tests\Feature\Api;

use App\Models\LoginHistory;
use App\Models\User;
use App\Models\Role;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;

class LoginHistoryApiTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a customer role
        $customerRole = Role::factory()->create([
            'name' => 'customer',
            'slug' => 'customer',
            'is_active' => true,
        ]);

        $this->user = User::factory()->create([
            'role_id' => $customerRole->id,
            'email_verified_at' => now(),
        ]);
    }

    #[Test]
    public function it_requires_authentication_to_access_login_history()
    {
        $response = $this->getJson('/api/v1/login-history');

        $response->assertStatus(401);
    }

    #[Test]
    public function it_returns_user_login_history()
    {
        Sanctum::actingAs($this->user);

        // Create some login history
        LoginHistory::factory()->count(3)->create([
            'user_id' => $this->user->id,
            'login_status' => 'success',
        ]);

        LoginHistory::factory()->create([
            'user_id' => $this->user->id,
            'login_status' => 'failed',
            'failure_reason' => 'Invalid credentials',
        ]);

        $response = $this->getJson('/api/v1/login-history');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'data' => [
                        '*' => [
                            'id',
                            'status',
                            'timestamp',
                            'location',
                            'device_type',
                            'browser',
                            'os',
                            'ip_address',
                            'risk_level',
                            'risk_color',
                            'is_new_device',
                            'is_new_location',
                            'session_duration',
                            'failure_reason',
                        ]
                    ],
                    'current_page',
                    'per_page',
                    'total',
                ],
                'meta'
            ])
            ->assertJson([
                'success' => true,
            ]);

        $this->assertEquals(4, $response->json('data.total'));
    }

    #[Test]
    public function it_filters_login_history_by_status()
    {
        Sanctum::actingAs($this->user);

        // Create mixed login history
        LoginHistory::factory()->count(2)->create([
            'user_id' => $this->user->id,
            'login_status' => 'success',
        ]);

        LoginHistory::factory()->create([
            'user_id' => $this->user->id,
            'login_status' => 'failed',
        ]);

        $response = $this->getJson('/api/v1/login-history?status=failed');

        $response->assertStatus(200);
        $this->assertEquals(1, $response->json('data.total'));
        $this->assertEquals('failed', $response->json('data.data.0.status'));
    }

    #[Test]
    public function it_filters_login_history_by_days()
    {
        Sanctum::actingAs($this->user);

        // Create old login history
        LoginHistory::factory()->create([
            'user_id' => $this->user->id,
            'created_at' => now()->subDays(10),
        ]);

        // Create recent login history
        LoginHistory::factory()->create([
            'user_id' => $this->user->id,
            'created_at' => now()->subDays(2),
        ]);

        $response = $this->getJson('/api/v1/login-history?days=5');

        $response->assertStatus(200);
        $this->assertEquals(1, $response->json('data.total'));
    }

    #[Test]
    public function it_filters_suspicious_activities_only()
    {
        Sanctum::actingAs($this->user);

        // Create normal login
        LoginHistory::factory()->create([
            'user_id' => $this->user->id,
            'is_suspicious' => false,
        ]);

        // Create suspicious login
        LoginHistory::factory()->create([
            'user_id' => $this->user->id,
            'is_suspicious' => true,
        ]);

        $response = $this->getJson('/api/v1/login-history?suspicious_only=true');

        $response->assertStatus(200);
        $this->assertEquals(1, $response->json('data.total'));
    }

    #[Test]
    public function it_returns_login_statistics()
    {
        Sanctum::actingAs($this->user);

        // Create login history - be explicit about counts
        LoginHistory::factory()->count(3)->create([
            'user_id' => $this->user->id,
            'login_status' => 'success',
            'is_suspicious' => false,
        ]);

        LoginHistory::factory()->count(2)->create([
            'user_id' => $this->user->id,
            'login_status' => 'failed',
            'is_suspicious' => false,
        ]);

        LoginHistory::factory()->count(2)->create([
            'user_id' => $this->user->id,
            'login_status' => 'success', // Can be successful but still suspicious
            'is_suspicious' => true,
        ]);

        $response = $this->getJson('/api/v1/login-history/statistics');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'total_logins',
                    'successful_logins',
                    'failed_logins',
                    'success_rate',
                    'unique_devices',
                    'unique_locations',
                    'recent_logins',
                    'suspicious_activities',
                    'period_days',
                ]
            ])
            ->assertJson([
                'success' => true,
                'data' => [
                    'total_logins' => 7,
                    'successful_logins' => 5, // 3 normal + 2 suspicious successful
                    'failed_logins' => 2,
                    'suspicious_activities' => 2,
                ]
            ]);
    }

    #[Test]
    public function it_returns_detailed_login_information()
    {
        Sanctum::actingAs($this->user);

        $loginHistory = LoginHistory::factory()->create([
            'user_id' => $this->user->id,
            'login_status' => 'success',
            'device_info' => ['is_mobile' => false],
            'geolocation_data' => [
                'country' => 'United States',
                'region' => 'California',
                'city' => 'San Francisco',
            ],
        ]);

        $response = $this->getJson("/api/v1/login-history/{$loginHistory->uuid}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'id',
                    'status',
                    'timestamp',
                    'location' => [
                        'formatted',
                        'country',
                        'region',
                        'city',
                    ],
                    'device' => [
                        'type',
                        'browser',
                        'os',
                        'is_mobile',
                    ],
                    'security' => [
                        'ip_address',
                        'risk_score',
                        'risk_level',
                        'is_suspicious',
                        'is_new_device',
                        'is_new_location',
                        'security_alert',
                    ],
                    'session' => [
                        'duration',
                        'started_at',
                        'ended_at',
                    ],
                    'failure_reason',
                ]
            ])
            ->assertJson([
                'success' => true,
                'data' => [
                    'id' => $loginHistory->uuid,
                    'status' => 'success',
                ]
            ]);
    }

    #[Test]
    public function it_returns_security_alerts()
    {
        Sanctum::actingAs($this->user);

        // Create normal login
        LoginHistory::factory()->create([
            'user_id' => $this->user->id,
            'security_alert' => false,
            'created_at' => now()->subDays(1), // Within 7 days
        ]);

        // Create security alert
        LoginHistory::factory()->create([
            'user_id' => $this->user->id,
            'security_alert' => true,
            'login_status' => 'failed',
            'is_vpn' => true,
            'created_at' => now()->subDays(1), // Within 7 days
        ]);

        $response = $this->getJson('/api/v1/login-history/alerts');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => [
                        'id',
                        'timestamp',
                        'type',
                        'location',
                        'device_type',
                        'risk_level',
                        'description',
                    ]
                ],
                'meta' => [
                    'count',
                    'period_days',
                ]
            ])
            ->assertJson([
                'success' => true,
                'meta' => [
                    'count' => 1,
                ]
            ]);
    }

    #[Test]
    public function it_tracks_login_attempt_via_api()
    {
        $response = $this->postJson('/api/v1/track-login', [
            'user_id' => $this->user->id,
            'status' => 'success',
            'login_method' => 'standard',
        ]);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'login_history_id',
                    'risk_score',
                    'risk_level',
                    'is_suspicious',
                    'security_alert',
                ]
            ])
            ->assertJson([
                'success' => true,
            ]);

        $this->assertDatabaseHas('login_histories', [
            'user_id' => $this->user->id,
            'login_status' => 'success',
            'login_method' => 'standard',
        ]);
    }

    #[Test]
    public function it_tracks_failed_login_by_email()
    {
        $response = $this->postJson('/api/v1/track-login', [
            'email' => $this->user->email,
            'status' => 'failed',
            'failure_reason' => 'Invalid password',
        ]);

        $response->assertStatus(201);

        $this->assertDatabaseHas('login_histories', [
            'user_id' => $this->user->id,
            'login_status' => 'failed',
            'failure_reason' => 'Invalid password',
        ]);
    }

    #[Test]
    public function it_masks_ip_addresses_in_responses()
    {
        Sanctum::actingAs($this->user);

        $loginHistory = LoginHistory::factory()->create([
            'user_id' => $this->user->id,
            'ip_address' => '*************',
        ]);

        $response = $this->getJson('/api/v1/login-history');

        $response->assertStatus(200);
        
        $ipAddress = $response->json('data.data.0.ip_address');
        $this->assertStringContainsString('***', $ipAddress);
        $this->assertStringNotContainsString('*************', $ipAddress);
    }

    #[Test]
    public function it_only_returns_user_own_login_history()
    {
        $otherUser = User::factory()->create([
            'role_id' => $this->user->role_id, // Use same role to avoid unique constraint
        ]);

        Sanctum::actingAs($this->user);

        // Create login history for both users
        LoginHistory::factory()->create(['user_id' => $this->user->id]);
        LoginHistory::factory()->create(['user_id' => $otherUser->id]);

        $response = $this->getJson('/api/v1/login-history');

        $response->assertStatus(200);
        $this->assertEquals(1, $response->json('data.total'));

        // Verify that only the authenticated user's data is returned
        // by checking that we get exactly 1 record (not 2)
        $this->assertCount(1, $response->json('data.data'));
    }
}
