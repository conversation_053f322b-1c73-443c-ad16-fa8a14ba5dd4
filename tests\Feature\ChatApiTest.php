<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\ChatRoom;
use App\Models\ChatSystemSetting;
use Lara<PERSON>\Sanctum\Sanctum;
use PHPUnit\Framework\Attributes\Test;

class ChatApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Enable chat system for tests
        ChatSystemSetting::updateOrCreate(
            ['setting_key' => 'chat_enabled'],
            [
                'setting_value' => '1',
                'setting_type' => 'boolean',
                'description' => 'Enable or disable the chat system',
                'is_public' => true,
            ]
        );
    }

    
     //Test creating a chat room via API endpoint.
    #[Test]
    public function test_api_creates_chat_room_successfully(): void
    {
        $payload = [
            'type' => 'visitor',
            'priority' => 2,
            'language' => 'en',
            'visitor_info' => [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
            ],
        ];

        $response = $this->postJson('/api/v1/chat/rooms', $payload);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'room' => [
                            'id',
                            'uuid',
                            'type',
                            'status',
                            'priority',
                            'language',
                            'visitor_info',
                            'created_at',
                        ]
                    ]
                ])
                ->assertJsonFragment([
                    'success' => true,
                    'type' => 'visitor',
                    'priority' => 2,
                    'language' => 'en',
                ]);

        $this->assertDatabaseHas('chat_rooms', [
            'type' => 'visitor',
            'priority' => 2,
            'language' => 'en',
        ]);
    }

    
     // Test sending a message to a chat room via API endpoint.
    #[Test]
    public function test_api_sends_message_to_chat_room_successfully(): void
    {
        // Create a chat room first
        $room = ChatRoom::create([
            'type' => 'visitor',
            'status' => 'active',
            'priority' => 1,
            'language' => 'en',
        ]);

        $payload = [
            'content' => 'Hello, I need help!',
            'message_type' => 'text',
        ];

        $response = $this->postJson("/api/v1/chat/rooms/{$room->uuid}/messages", $payload);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'message' => [
                            'id',
                            'uuid',
                            'content',
                            'message_type',
                            'created_at',
                        ]
                    ]
                ])
                ->assertJsonFragment([
                    'success' => true,
                    'content' => 'Hello, I need help!',
                    'message_type' => 'text',
                ]);

        $this->assertDatabaseHas('chat_messages', [
            'chat_room_id' => $room->id,
            'content' => 'Hello, I need help!',
            'message_type' => 'text',
        ]);
    }

     // Test retrieving chat room messages via API endpoint.
    #[Test]
    public function test_api_retrieves_chat_room_messages_successfully(): void
    {
        $room = ChatRoom::create([
            'type' => 'visitor',
            'status' => 'active',
            'priority' => 1,
            'language' => 'en',
        ]);

        // Create some test messages
        $room->messages()->create([
            'content' => 'First message',
            'message_type' => 'text',
        ]);

        $room->messages()->create([
            'content' => 'Second message',
            'message_type' => 'text',
        ]);

        $response = $this->getJson("/api/v1/chat/rooms/{$room->uuid}/messages");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'messages' => [
                            '*' => [
                                'id',
                                'content',
                                'message_type',
                                'created_at',
                            ]
                        ],
                        'room'
                    ]
                ])
                ->assertJsonFragment(['success' => true])
                ->assertJsonCount(2, 'data.messages');
    }

    /**
     Test API rejects requests when chat system is disabled.
    #[Test]
    public function test_api_rejects_requests_when_chat_system_disabled(): void
    {
        // Disable chat system
        ChatSystemSetting::updateOrCreate(
            ['setting_key' => 'chat_enabled'],
            [
                'setting_value' => '0',
                'setting_type' => 'boolean',
                'description' => 'Enable or disable the chat system',
                'is_public' => true,
            ]
        );

        $payload = [
            'type' => 'visitor',
            'visitor_info' => [
                'name' => 'John Doe',
            ],
        ];

        $response = $this->postJson('/api/v1/chat/rooms', $payload);

        $response->assertStatus(503)
                ->assertJson([
                    'success' => false,
                    'message' => 'Chat system is currently disabled',
                ]);
    }

    /**
     * Test API validates input data and returns proper errors.
     */
    #[Test]
    public function test_api_validates_input_data_and_returns_errors(): void
    {
        $payload = [
            'type' => 'invalid_type',
            'priority' => 10, // Invalid priority (should be 1-4)
            'language' => 'invalid_lang',
        ];

        $response = $this->postJson('/api/v1/chat/rooms', $payload);

        $response->assertStatus(422)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'errors',
                ])
                ->assertJsonFragment(['success' => false]);
    }

    /**
     * Test authenticated user can access protected endpoints.
     */
    #[Test]
    public function test_authenticated_user_can_access_protected_endpoints(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->getJson('/api/v1/chat/rooms');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data',
                ])
                ->assertJsonFragment(['success' => true]);
    }

    /**
     * Test unauthenticated user cannot access protected endpoints.
     */
    #[Test]
    public function test_unauthenticated_user_cannot_access_protected_endpoints(): void
    {
        $response = $this->getJson('/api/v1/chat/rooms');

        $response->assertStatus(401)
                ->assertJson([
                    'success' => false,
                    'message' => 'Authentication required',
                ]);
    }

    /**
     * Test API handles missing chat room gracefully.
     */
    #[Test]
    public function test_api_handles_missing_chat_room_gracefully(): void
    {
        $nonExistentUuid = 'non-existent-uuid';

        $response = $this->getJson("/api/v1/chat/rooms/{$nonExistentUuid}/messages");

        $response->assertStatus(404)
                ->assertJson([
                    'success' => false,
                    'message' => 'Chat room not found',
                ]);
    }

    /**
     * Test API supports different languages.
     */
    #[Test]
    public function test_api_supports_different_languages(): void
    {
        $languages = ['en', 'af', 'zu', 'xh'];

        foreach ($languages as $language) {
            $payload = [
                'type' => 'visitor',
                'priority' => 1,
                'language' => $language,
                'visitor_info' => [
                    'name' => 'Test User',
                    'email' => '<EMAIL>',
                ],
            ];

            $response = $this->postJson('/api/v1/chat/rooms', $payload);

            $response->assertStatus(201)
                    ->assertJsonPath('data.room.language', $language);
        }
    }

    /**
     * Test API rate limiting works.
     */
    #[Test]
    public function test_api_rate_limiting_works(): void
    {
        // Enable middleware for this test
        $this->withMiddleware();
        $payload = [
            'type' => 'visitor',
            'priority' => 1,
            'language' => 'en',
            'visitor_info' => [
                'name' => 'Test User',
                'email' => '<EMAIL>',
            ],
        ];

        // Make requests up to the limit (10 per hour based on middleware)
        for ($i = 0; $i < 11; $i++) {
            $response = $this->postJson('/api/v1/chat/rooms', $payload);

            if ($i < 10) {
                $response->assertStatus(201);
            } else {
                // 11th request should be rate limited
                $response->assertStatus(429);
            }
        }
    }
}
