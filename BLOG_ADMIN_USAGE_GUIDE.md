# Blog Admin Management - Usage Guide

## Quick Start

### 1. Apply Database Changes
```bash
php artisan migrate
```

### 2. Access Admin Panel
Navigate to: `/admin/blog/posts`

### 3. Required Permissions
Ensure your user has the `content` permission with appropriate actions:
- `read` - View blog posts
- `create` - Create new posts
- `update` - Edit existing posts
- `delete` - Delete posts

---

## Creating a Blog Post

### Step 1: Navigate to Create Form
- Go to `/admin/blog/posts`
- Click "Create New Post" button
- Or directly visit `/admin/blog/posts/create`

### Step 2: Fill Basic Information
**Required Fields:**
- **Title** - Post title (max 300 characters)
- **Content** - Main post content (rich text)

**Optional Fields:**
- **Slug** - URL-friendly identifier (auto-generated from title if empty)
- **Excerpt** - Short summary (auto-generated from content if empty)
- **Category** - Select from dropdown
- **Author** - Defaults to current user
- **Related Services** - Multi-select services

### Step 3: Add Media

**Featured Image:**
- Upload: Max 5MB, formats: JPEG, PNG, GIF, WebP
- Automatically optimized and converted to WebP
- Multiple sizes generated (thumbnail, medium, large)

**Gallery Images:**
- Upload up to 10 images
- Same size and format restrictions as featured image
- Each image optimized separately

**Videos:**
```json
{
  "type": "youtube",
  "url": "https://www.youtube.com/watch?v=VIDEO_ID",
  "title": "Video Title",
  "thumbnail": "https://img.youtube.com/vi/VIDEO_ID/maxresdefault.jpg"
}
```

Supported types:
- `uploaded` - Video file uploaded to server
- `youtube` - YouTube embed
- `vimeo` - Vimeo embed

**Social Media Embeds:**
```json
{
  "platform": "twitter",
  "url": "https://twitter.com/user/status/123456",
  "embed_code": "<blockquote>...</blockquote>"
}
```

Supported platforms:
- `twitter` - Twitter/X posts
- `instagram` - Instagram posts
- `facebook` - Facebook posts
- `linkedin` - LinkedIn posts

### Step 4: Configure SEO

**Basic SEO:**
- **Meta Title** - SEO title (max 255 chars, defaults to post title)
- **Meta Description** - SEO description (max 500 chars)
- **Meta Keywords** - Comma-separated keywords
- **Focus Keyword** - Primary keyword for optimization
- **Canonical URL** - Canonical URL (auto-generated if empty)

**Open Graph (Facebook, LinkedIn):**
- **OG Title** - Defaults to meta title
- **OG Description** - Defaults to meta description
- **OG Image** - Defaults to featured image
- **OG Type** - Usually "article"

**Twitter Cards:**
- **Card Type** - summary, summary_large_image, app, player
- **Twitter Title** - Defaults to OG title
- **Twitter Description** - Defaults to OG description
- **Twitter Image** - Defaults to OG image

### Step 5: Set Publishing Options

**Status:**
- **Draft** - `is_published = false`
- **Published** - `is_published = true`, sets `published_at` to now
- **Scheduled** - Set `scheduled_at` to future date

**Featured:**
- Toggle `is_featured` to show on homepage/featured sections

### Step 6: Save
- Click "Create Post" button
- Redirects to post detail view on success
- Shows validation errors if any

---

## Editing a Blog Post

### Step 1: Navigate to Edit Form
- From list: Click "Edit" button on post row
- From detail: Click "Edit" button
- Or directly visit `/admin/blog/posts/{post}/edit`

### Step 2: Update Fields
- All fields can be updated
- Slug can be changed (must remain unique)
- Author can be reassigned

### Step 3: Manage Media

**Replace Featured Image:**
- Upload new image
- Old image automatically deleted

**Manage Gallery:**
- Upload new images (added to existing)
- Delete individual images via delete button
- Reorder images (if UI supports)

**Update Videos/Embeds:**
- Edit JSON data directly
- Add new entries
- Remove unwanted entries

### Step 4: Save Changes
- Click "Update Post" button
- Redirects to post detail view on success

---

## Quick Actions

### Toggle Published Status
**From List View:**
- Click "Publish" or "Unpublish" button
- Status updates immediately
- Sets `published_at` on first publish

**From Detail View:**
- Click "Toggle Published" button
- Confirmation may be required

### Toggle Featured Status
**From List View:**
- Click "Feature" or "Unfeature" button
- Status updates immediately

**From Detail View:**
- Click "Toggle Featured" button

### Delete Post (Soft Delete)
**From List View:**
- Click "Delete" button
- Confirmation required
- Post marked as deleted (`is_deleted = true`)
- Post automatically unpublished

**From Detail View:**
- Click "Delete" button
- Confirmation required

### Restore Deleted Post
- Access deleted posts list (filter by deleted)
- Click "Restore" button
- Post marked as not deleted
- Publish status remains unpublished

---

## Filtering and Search

### Available Filters

**Search:**
- Searches in: title, content, excerpt
- Case-insensitive
- Partial match

**Category:**
- Filter by specific category
- Shows only active categories

**Status:**
- `published` - Published posts only
- `draft` - Unpublished posts only
- `featured` - Featured posts only
- `scheduled` - Posts with future scheduled_at

**Author:**
- Filter by specific author
- Shows only admin/staff users

### Sorting

**Sort By:**
- `created_at` - Creation date (default)
- `updated_at` - Last update date
- `published_at` - Publish date
- `title` - Alphabetical
- `view_count` - Popularity

**Sort Order:**
- `desc` - Descending (default)
- `asc` - Ascending

---

## Rich Text Editor

### Image Upload
1. Click image icon in editor toolbar
2. Select image file
3. Image uploaded via AJAX to `/admin/blog/posts/upload-image`
4. Image automatically inserted into content
5. Image stored in `storage/app/public/blog/content/`

### Supported Features
- Bold, italic, underline
- Headings (H1-H6)
- Lists (ordered, unordered)
- Links
- Images
- Code blocks
- Blockquotes
- Tables
- Alignment
- Text color
- Background color

---

## Comment Moderation

### Access Comments
Navigate to: `/admin/comments`

### View Comments
**Filter by Status:**
- `pending` - Awaiting approval
- `approved` - Approved comments
- `flagged` - Flagged by users
- `all` - All comments

### Moderate Individual Comment
1. Click on comment to view details
2. Click "Approve" or "Reject" button
3. Add admin notes if needed

### Bulk Moderation
1. Select multiple comments (checkboxes)
2. Click "Bulk Approve" or "Bulk Reject"
3. Confirm action

### Comment Statistics
- View stats at `/admin/comments/stats/data`
- Shows approval rate, pending count, etc.

---

## Activity Logging

All blog post operations are logged:

**Logged Activities:**
- `blog_post_created` - Post creation
- `blog_post_updated` - Post updates
- `blog_post_deleted` - Post deletion
- `blog_post_published` - Publishing
- `blog_post_unpublished` - Unpublishing
- `blog_post_featured` - Featuring
- `blog_post_unfeatured` - Unfeaturing
- `blog_gallery_image_deleted` - Gallery image deletion
- `blog_post_restored` - Post restoration
- `blog_posts_viewed` - List view access
- `blog_post_viewed` - Detail view access

**View Activity Logs:**
Navigate to: `/admin/activity-logs`

**Filter by Activity Type:**
Search for "blog_post" to see all blog-related activities

---

## API Endpoints (AJAX)

### Upload Image for Editor
```javascript
POST /admin/blog/posts/upload-image
Content-Type: multipart/form-data

{
  "image": File
}

Response:
{
  "success": true,
  "url": "https://example.com/storage/blog/content/image.jpg",
  "path": "blog/content/image.jpg"
}
```

### Delete Gallery Image
```javascript
DELETE /admin/blog/posts/{post}/gallery/{image}
Content-Type: application/json

{
  "image_path": "blog/gallery/image.jpg"
}

Response:
{
  "success": true,
  "message": "Image deleted successfully"
}
```

---

## Troubleshooting

### Route Model Binding Error
**Error:** "TypeError: Argument must be of type BlogPost, string given"

**Solution:** 
- Route parameter name must match controller parameter name
- Use `{post}` not `{blogPost}` or `{slug}`
- Model's `getRouteKeyName()` handles slug resolution

### Image Upload Fails
**Possible Causes:**
- File too large (max 5MB)
- Invalid file type (must be JPEG, PNG, GIF, WebP)
- Storage directory not writable
- Virus scan failed

**Solution:**
- Check file size and type
- Verify storage permissions: `storage/app/public/blog/`
- Check error logs for details

### Permission Denied
**Error:** "You do not have permission to {action} content"

**Solution:**
- Verify user has `content` permission with required action
- Check role permissions in `/admin/permissions`
- Contact admin to grant permissions

### Slug Already Exists
**Error:** "This slug is already in use"

**Solution:**
- Change the slug to a unique value
- Or leave empty to auto-generate from title
- Check existing posts for conflicts

---

## Best Practices

### SEO Optimization
1. **Title:** 50-60 characters, include focus keyword
2. **Meta Description:** 150-160 characters, compelling summary
3. **Focus Keyword:** Research and choose wisely
4. **Headings:** Use H1 for title, H2-H3 for sections
5. **Images:** Add alt text, optimize file size
6. **Internal Links:** Link to related posts/pages
7. **URL Slug:** Short, descriptive, keyword-rich

### Content Quality
1. **Length:** Aim for 1000+ words for SEO
2. **Readability:** Short paragraphs, bullet points
3. **Media:** Include images, videos for engagement
4. **Formatting:** Use headings, lists, bold text
5. **Proofreading:** Check spelling and grammar

### Performance
1. **Images:** Upload optimized images (WebP conversion is automatic)
2. **Videos:** Use embeds instead of uploads when possible
3. **Gallery:** Limit to 5-10 images per post
4. **Content:** Avoid extremely long posts (split if needed)

### Security
1. **Images:** System automatically scans for viruses
2. **Content:** Avoid inline scripts (XSS protection)
3. **Links:** Verify external links are safe
4. **Embeds:** Only use trusted platforms

---

## Support

For issues or questions:
1. Check activity logs for error details
2. Review validation messages
3. Contact system administrator
4. Check Laravel logs: `storage/logs/laravel.log`

