<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

class AutoLocalizeMiddleware
{
    /**
     * Supported language codes
     */
    private const SUPPORTED_LANGUAGES = ['en', 'fr', 'es'];

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $detectedLocale = $this->detectPreferredLocale($request);
        
        // Bind the detected locale to the service container so routes can access it
        App::instance('auto.locale', $detectedLocale);
        
        return $next($request);
    }

    /**
     * Detect the user's preferred locale using multiple sources
     */
    private function detectPreferredLocale(Request $request): string
    {
        // 1. Check session first (user has previously selected a language)
        if (Session::has('locale')) {
            $sessionLocale = Session::get('locale');
            if ($this->isValidLanguage($sessionLocale)) {
                return $sessionLocale;
            }
        }

        // 2. Check user preference (if authenticated)
        if ($request->user() && isset($request->user()->preferred_language)) {
            $userLocale = $request->user()->preferred_language;
            if ($this->isValidLanguage($userLocale)) {
                return $userLocale;
            }
        }

        // 3. Check Accept-Language header (browser preference)
        $acceptLanguage = $request->header('Accept-Language');
        if ($acceptLanguage) {
            $preferredLanguages = $this->parseAcceptLanguage($acceptLanguage);
            foreach ($preferredLanguages as $langCode) {
                if ($this->isValidLanguage($langCode)) {
                    return $langCode;
                }
            }
        }

        // 4. Check for common language indicators in user agent or other headers
        $detectedFromHeaders = $this->detectFromHeaders($request);
        if ($detectedFromHeaders && $this->isValidLanguage($detectedFromHeaders)) {
            return $detectedFromHeaders;
        }

        // 5. Default to English
        return 'en';
    }

    /**
     * Check if language code is valid and supported
     */
    private function isValidLanguage(string $langCode): bool
    {
        return in_array($langCode, self::SUPPORTED_LANGUAGES);
    }

    /**
     * Parse Accept-Language header to extract preferred languages
     */
    private function parseAcceptLanguage(string $acceptLanguage): array
    {
        $languages = [];
        $parts = explode(',', $acceptLanguage);

        foreach ($parts as $part) {
            $part = trim($part);
            
            // Remove quality values (e.g., "en-US;q=0.9" becomes "en-US")
            if (strpos($part, ';') !== false) {
                $part = explode(';', $part)[0];
            }
            
            // Extract main language code (e.g., "en-US" becomes "en")
            if (strpos($part, '-') !== false) {
                $part = explode('-', $part)[0];
            }
            
            $languages[] = strtolower(trim($part));
        }

        return array_unique(array_filter($languages));
    }

    /**
     * Detect language from other headers (fallback method)
     */
    private function detectFromHeaders(Request $request): ?string
    {
        // Check for common patterns in User-Agent or other headers
        $userAgent = $request->header('User-Agent', '');
        
        // Look for language indicators in user agent
        if (stripos($userAgent, 'fr') !== false || stripos($userAgent, 'french') !== false) {
            return 'fr';
        }
        
        if (stripos($userAgent, 'es') !== false || stripos($userAgent, 'spanish') !== false) {
            return 'es';
        }
        
        // Check CloudFlare country header if available
        $cfCountry = $request->header('CF-IPCountry');
        if ($cfCountry) {
            return $this->mapCountryToLanguage($cfCountry);
        }
        
        // Check other common country/language headers
        $country = $request->header('X-Country-Code') ?: $request->header('X-GeoIP-Country');
        if ($country) {
            return $this->mapCountryToLanguage($country);
        }
        
        return null;
    }

    /**
     * Map country codes to preferred languages
     */
    private function mapCountryToLanguage(string $countryCode): ?string
    {
        $countryCode = strtoupper($countryCode);
        
        // French-speaking countries
        $frenchCountries = ['FR', 'BE', 'CH', 'CA', 'LU', 'MC', 'SN', 'CI', 'ML', 'BF', 'NE', 'TD', 'MG', 'CM', 'TG', 'BJ', 'BI', 'CF', 'CG', 'DJ', 'GA', 'GN', 'HT', 'SC', 'VU'];
        
        // Spanish-speaking countries
        $spanishCountries = ['ES', 'MX', 'AR', 'CO', 'PE', 'VE', 'CL', 'EC', 'GT', 'CU', 'BO', 'DO', 'HN', 'PY', 'SV', 'NI', 'CR', 'PA', 'UY', 'PR', 'GQ'];
        
        if (in_array($countryCode, $frenchCountries)) {
            return 'fr';
        }
        
        if (in_array($countryCode, $spanishCountries)) {
            return 'es';
        }
        
        // Default to English for all other countries
        return 'en';
    }
}

