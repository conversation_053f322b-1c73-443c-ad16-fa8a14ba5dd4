<?php

namespace Tests\Feature\Admin;

use App\Models\User;
use App\Models\Role;
use App\Models\Order;
use App\Models\Product;
use App\Models\Project;
use App\Models\Job;
use App\Models\JobApplication;
use App\Models\Coupon;
use App\Models\Payment;
use App\Models\ActivityLog;
use App\Models\VisitorAnalytic;
use App\Models\Currency;
use App\Services\GlobalSearchService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

use PHPUnit\Framework\Attributes\Test;
class GlobalSearchTest extends TestCase
{
    use RefreshDatabase;

    protected $seed = true;
    protected User $admin;
    protected GlobalSearchService $searchService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin user
        $adminRole = Role::where('name', 'admin')->first();
        $this->admin = User::factory()->create([
            'role_id' => $adminRole->id,
            'first_name' => 'Admin',
            'last_name' => 'User',
            'email' => '<EMAIL>'
        ]);

        $this->searchService = new GlobalSearchService();
    }
    #[Test]
    public function it_can_search_users()
    {
        // Create test users
        $customerRole = Role::where('name', 'customer')->first();
        $user1 = User::factory()->create([
            'role_id' => $customerRole->id,
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>'
        ]);

        $user2 = User::factory()->create([
            'role_id' => $customerRole->id,
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'email' => '<EMAIL>'
        ]);

        // Search for users
        $results = $this->searchService->search('john', 10);
        
        $this->assertGreaterThan(0, $results->count());
        
        $userResult = $results->where('type', 'user')->first();
        $this->assertNotNull($userResult);
        $this->assertEquals('user', $userResult['type']);
        $this->assertStringContainsString('John', $userResult['title']);
        $this->assertArrayHasKey('url', $userResult);
        $this->assertArrayHasKey('relevance', $userResult);
    }
    #[Test]
    public function it_can_search_orders()
    {
        // Create test order
        $currency = Currency::first();
        $order = Order::factory()->create([
            'order_number' => 'ORD-12345',
            'email' => '<EMAIL>',
            'currency_id' => $currency->id,
            'total_amount' => 100.00
        ]);

        // Search for orders
        $results = $this->searchService->search('ORD-12345', 10);
        
        $this->assertGreaterThan(0, $results->count());
        
        $orderResult = $results->where('type', 'order')->first();
        $this->assertNotNull($orderResult);
        $this->assertEquals('order', $orderResult['type']);
        $this->assertStringContainsString('ORD-12345', $orderResult['title']);
    }
    #[Test]
    public function it_can_search_products()
    {
        // Create test product
        $product = Product::factory()->create([
            'name' => 'Test Laptop Computer',
            'sku' => 'LAP-001',
            'description' => 'High performance laptop for testing',
            'price' => 999.99
        ]);

        // Search for products
        $results = $this->searchService->search('laptop', 10);
        
        $this->assertGreaterThan(0, $results->count());
        
        $productResult = $results->where('type', 'product')->first();
        $this->assertNotNull($productResult);
        $this->assertEquals('product', $productResult['type']);
        $this->assertStringContainsString('Laptop', $productResult['title']);
    }
    #[Test]
    public function it_can_search_with_type_filter()
    {
        // Create test data
        $customerRole = Role::where('name', 'customer')->first();
        User::factory()->create([
            'role_id' => $customerRole->id,
            'first_name' => 'Test',
            'last_name' => 'User'
        ]);

        Product::factory()->create([
            'name' => 'Test Product'
        ]);

        // Search with type filter
        $results = $this->searchService->search('test', 10, ['type' => 'user']);
        
        $this->assertGreaterThan(0, $results->count());
        
        // All results should be users
        foreach ($results as $result) {
            $this->assertEquals('user', $result['type']);
        }
    }
    #[Test]
    public function it_can_search_activity_logs()
    {
        // Create test activity log
        $activityLog = ActivityLog::create([
            'user_id' => $this->admin->id,
            'user_email' => $this->admin->email,
            'user_name' => $this->admin->first_name . ' ' . $this->admin->last_name,
            'activity_type' => 'login',
            'activity_description' => 'User logged into admin dashboard',
            'ip_address' => '*************',
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'url' => '/admin/dashboard',
            'method' => 'GET',
            'browser' => 'Chrome',
            'platform' => 'Windows',
            'country' => 'South Africa',
            'city' => 'Cape Town',
            'status' => 'success',
            'occurred_at' => now()
        ]);

        // Search for activity logs
        $results = $this->searchService->search('login', 10);

        $this->assertGreaterThan(0, $results->count());

        $activityResult = $results->where('type', 'activity_log')->first();
        $this->assertNotNull($activityResult);
        $this->assertEquals('activity_log', $activityResult['type']);
        $this->assertStringContainsString('Login', $activityResult['title']);
    }
    #[Test]
    public function it_can_search_visitor_analytics()
    {
        // Create test visitor analytic
        $visitorAnalytic = VisitorAnalytic::create([
            'visitor_id' => 'visitor_' . uniqid(),
            'fingerprint' => 'fp_' . uniqid(),
            'page_url' => '/products/laptop-computers',
            'page_title' => 'Laptop Computers - ChiSolution',
            'referrer_url' => 'https://google.com',
            'ip_address' => '*************',
            'user_agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'browser' => 'Firefox',
            'platform' => 'MacOS',
            'device_type' => 'desktop',
            'country' => 'South Africa',
            'city' => 'Johannesburg',
            'utm_source' => 'google',
            'utm_campaign' => 'laptop_ads',
            'visited_at' => now(),
            'converted' => false,
            'is_bounce' => false,
            'is_suspicious' => false,
            'is_bot' => false, // Explicitly set to false to ensure it's included in search
        ]);

        // Search for visitor analytics specifically by type
        $results = $this->searchService->search('laptop', 10, ['type' => 'visitor_analytic']);

        $this->assertGreaterThan(0, $results->count());

        $visitorResult = $results->where('type', 'visitor_analytic')->first();
        $this->assertNotNull($visitorResult);
        $this->assertEquals('visitor_analytic', $visitorResult['type']);
        $this->assertStringContainsString('Laptop', $visitorResult['title']);
    }
    #[Test]
    public function search_controller_returns_json_response()
    {
        $response = $this->actingAs($this->admin)
            ->getJson('/admin/search?q=test');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'results',
                'total',
                'query'
            ]);
    }
    #[Test]
    public function search_controller_validates_input()
    {
        // Test with short query
        $response = $this->actingAs($this->admin)
            ->getJson('/admin/search?q=a');

        $response->assertStatus(422);

        // Test with missing query
        $response = $this->actingAs($this->admin)
            ->getJson('/admin/search');

        $response->assertStatus(422);
    }
    #[Test]
    public function search_requires_admin_authentication()
    {
        $customerRole = Role::where('name', 'customer')->first();
        $customer = User::factory()->create(['role_id' => $customerRole->id]);

        // Test unauthenticated
        $response = $this->getJson('/admin/search?q=test');
        $response->assertStatus(401);

        // Test customer access
        $response = $this->actingAs($customer)
            ->getJson('/admin/search?q=test');
        $response->assertStatus(403);
    }
    #[Test]
    public function advanced_search_page_loads()
    {
        $response = $this->actingAs($this->admin)
            ->get('/admin/search/advanced');

        $response->assertStatus(200)
            ->assertViewIs('admin.search.index');
    }
    #[Test]
    public function search_filters_endpoint_works()
    {
        $response = $this->actingAs($this->admin)
            ->getJson('/admin/search/filters');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'filters' => [
                    'types',
                    'order_statuses',
                    'payment_statuses',
                    'user_roles',
                    'project_statuses'
                ]
            ]);
    }
    #[Test]
    public function search_suggestions_endpoint_works()
    {
        // Create test data
        Product::factory()->create(['name' => 'Test Product']);

        $response = $this->actingAs($this->admin)
            ->getJson('/admin/search/suggestions?q=test');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'suggestions',
                'query'
            ]);
    }
    #[Test]
    public function relevance_scoring_works()
    {
        // Create products with different relevance
        $exactMatch = Product::factory()->create(['name' => 'laptop']);
        $startsWithMatch = Product::factory()->create(['name' => 'laptop computer']);
        $containsMatch = Product::factory()->create(['name' => 'gaming laptop pro']);

        $results = $this->searchService->search('laptop', 10);
        $productResults = $results->where('type', 'product')->sortByDesc('relevance');

        $this->assertGreaterThan(0, $productResults->count());
        
        // Exact match should have highest relevance
        $firstResult = $productResults->first();
        $this->assertGreaterThan(50, $firstResult['relevance']);
    }
}
