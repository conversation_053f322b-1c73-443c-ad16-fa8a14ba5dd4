/**
 * ChiSolution Chat SDK
 * A comprehensive JavaScript SDK for integrating with the ChiSolution Chat API
 * 
 * @version 1.0.0
 * <AUTHOR> Team
 */

class ChiChatSDK {
    constructor(options = {}) {
        this.baseUrl = options.baseUrl || '/api/v1/chat';
        this.apiKey = options.apiKey || null;
        this.token = options.token || null;
        this.timeout = options.timeout || 30000;
        this.retryAttempts = options.retryAttempts || 3;
        this.retryDelay = options.retryDelay || 1000;
        
        // Event listeners
        this.eventListeners = {};
        
        // WebSocket connection for real-time features
        this.websocket = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        
        // Initialize
        this.init();
    }

    /**
     * Initialize the SDK
     */
    init() {
        // Set up default headers
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        };

        if (this.apiKey) {
            this.defaultHeaders['X-API-Key'] = this.apiKey;
        }

        if (this.token) {
            this.defaultHeaders['Authorization'] = `Bearer ${this.token}`;
        }

        // Add CSRF token if available
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            this.defaultHeaders['X-CSRF-TOKEN'] = csrfToken.getAttribute('content');
        }
    }

    /**
     * Make HTTP request with retry logic
     */
    async request(method, endpoint, data = null, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            method: method.toUpperCase(),
            headers: { ...this.defaultHeaders, ...options.headers },
            ...options
        };

        if (data && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
            config.body = JSON.stringify(data);
        }

        for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), this.timeout);
                
                config.signal = controller.signal;
                
                const response = await fetch(url, config);
                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                return await response.json();
            } catch (error) {
                if (attempt === this.retryAttempts) {
                    throw error;
                }
                
                // Wait before retry
                await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
            }
        }
    }

    /**
     * Create a new chat room
     */
    async createRoom(roomData) {
        return await this.request('POST', '/rooms', roomData);
    }

    /**
     * Get chat room details
     */
    async getRoom(roomUuid) {
        return await this.request('GET', `/rooms/${roomUuid}`);
    }

    /**
     * Send a message to a chat room
     */
    async sendMessage(roomUuid, messageData) {
        return await this.request('POST', `/rooms/${roomUuid}/messages`, messageData);
    }

    /**
     * Get messages from a chat room
     */
    async getMessages(roomUuid, options = {}) {
        const params = new URLSearchParams(options);
        const endpoint = `/rooms/${roomUuid}/messages${params.toString() ? '?' + params.toString() : ''}`;
        return await this.request('GET', endpoint);
    }

    /**
     * Join a chat room
     */
    async joinRoom(roomUuid) {
        return await this.request('POST', `/rooms/${roomUuid}/join`);
    }

    /**
     * Leave a chat room
     */
    async leaveRoom(roomUuid) {
        return await this.request('POST', `/rooms/${roomUuid}/leave`);
    }

    /**
     * Send typing indicator
     */
    async sendTyping(roomUuid, isTyping = true) {
        return await this.request('POST', `/rooms/${roomUuid}/typing`, { is_typing: isTyping });
    }

    /**
     * Mark message as read
     */
    async markAsRead(roomUuid, messageUuid) {
        return await this.request('POST', `/rooms/${roomUuid}/messages/${messageUuid}/read`);
    }

    /**
     * Upload file to chat room
     */
    async uploadFile(roomUuid, file, messageContent = '') {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('message_content', messageContent);

        const headers = { ...this.defaultHeaders };
        delete headers['Content-Type']; // Let browser set it for FormData

        return await fetch(`${this.baseUrl}/rooms/${roomUuid}/files`, {
            method: 'POST',
            headers,
            body: formData
        }).then(response => response.json());
    }

    /**
     * Get chat statistics
     */
    async getStatistics() {
        return await this.request('GET', '/statistics');
    }

    /**
     * Event listener management
     */
    on(event, callback) {
        if (!this.eventListeners[event]) {
            this.eventListeners[event] = [];
        }
        this.eventListeners[event].push(callback);
    }

    off(event, callback) {
        if (this.eventListeners[event]) {
            this.eventListeners[event] = this.eventListeners[event].filter(cb => cb !== callback);
        }
    }

    emit(event, data) {
        if (this.eventListeners[event]) {
            this.eventListeners[event].forEach(callback => callback(data));
        }
    }

    /**
     * Connect to WebSocket for real-time updates
     */
    connectWebSocket(roomUuid) {
        if (this.websocket) {
            this.websocket.close();
        }

        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/chat/${roomUuid}`;

        this.websocket = new WebSocket(wsUrl);

        this.websocket.onopen = () => {
            this.reconnectAttempts = 0;
            this.emit('websocket:connected', { roomUuid });
        };

        this.websocket.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.emit(`websocket:${data.type}`, data);
            } catch (error) {
                console.error('Failed to parse WebSocket message:', error);
            }
        };

        this.websocket.onclose = () => {
            this.emit('websocket:disconnected', { roomUuid });
            this.attemptReconnect(roomUuid);
        };

        this.websocket.onerror = (error) => {
            this.emit('websocket:error', { error, roomUuid });
        };
    }

    /**
     * Attempt to reconnect WebSocket
     */
    attemptReconnect(roomUuid) {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = Math.pow(2, this.reconnectAttempts) * 1000; // Exponential backoff
            
            setTimeout(() => {
                this.connectWebSocket(roomUuid);
            }, delay);
        }
    }

    /**
     * Disconnect WebSocket
     */
    disconnectWebSocket() {
        if (this.websocket) {
            this.websocket.close();
            this.websocket = null;
        }
    }

    /**
     * Utility method to create a chat widget
     */
    createWidget(options = {}) {
        return new ChiChatWidget(this, options);
    }
}

/**
 * Chat Widget Component
 */
class ChiChatWidget {
    constructor(sdk, options = {}) {
        this.sdk = sdk;
        this.options = {
            container: options.container || 'body',
            theme: options.theme || 'default',
            position: options.position || 'bottom-right',
            autoOpen: options.autoOpen || false,
            ...options
        };

        this.isOpen = false;
        this.currentRoom = null;
        
        this.init();
    }

    init() {
        this.createWidgetHTML();
        this.attachEventListeners();
        
        if (this.options.autoOpen) {
            this.open();
        }
    }

    createWidgetHTML() {
        const container = document.querySelector(this.options.container);
        
        const widgetHTML = `
            <div id="chi-chat-widget" class="chi-chat-widget chi-chat-widget--${this.options.position} chi-chat-widget--${this.options.theme}">
                <div class="chi-chat-toggle" id="chi-chat-toggle">
                    <i class="fas fa-comments"></i>
                </div>
                <div class="chi-chat-window" id="chi-chat-window" style="display: none;">
                    <div class="chi-chat-header">
                        <h4>Chat Support</h4>
                        <button class="chi-chat-close" id="chi-chat-close">×</button>
                    </div>
                    <div class="chi-chat-messages" id="chi-chat-messages"></div>
                    <div class="chi-chat-input">
                        <input type="text" id="chi-chat-input-field" placeholder="Type your message...">
                        <button id="chi-chat-send">Send</button>
                    </div>
                </div>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', widgetHTML);
        this.loadStyles();
    }

    loadStyles() {
        if (document.getElementById('chi-chat-widget-styles')) return;

        const styles = `
            <style id="chi-chat-widget-styles">
                .chi-chat-widget { position: fixed; z-index: 9999; }
                .chi-chat-widget--bottom-right { bottom: 20px; right: 20px; }
                .chi-chat-toggle { 
                    width: 60px; height: 60px; border-radius: 50%; 
                    background: #007bff; color: white; border: none; 
                    cursor: pointer; display: flex; align-items: center; 
                    justify-content: center; font-size: 24px; 
                }
                .chi-chat-window { 
                    width: 350px; height: 500px; background: white; 
                    border-radius: 10px; box-shadow: 0 5px 30px rgba(0,0,0,0.3); 
                    display: flex; flex-direction: column; 
                }
                .chi-chat-header { 
                    background: #007bff; color: white; padding: 15px; 
                    border-radius: 10px 10px 0 0; display: flex; 
                    justify-content: space-between; align-items: center; 
                }
                .chi-chat-messages { 
                    flex: 1; padding: 15px; overflow-y: auto; 
                }
                .chi-chat-input { 
                    display: flex; padding: 15px; border-top: 1px solid #eee; 
                }
                .chi-chat-input input { 
                    flex: 1; padding: 10px; border: 1px solid #ddd; 
                    border-radius: 5px; margin-right: 10px; 
                }
                .chi-chat-input button { 
                    padding: 10px 20px; background: #007bff; 
                    color: white; border: none; border-radius: 5px; 
                    cursor: pointer; 
                }
            </style>
        `;

        document.head.insertAdjacentHTML('beforeend', styles);
    }

    attachEventListeners() {
        document.getElementById('chi-chat-toggle').addEventListener('click', () => {
            this.toggle();
        });

        document.getElementById('chi-chat-close').addEventListener('click', () => {
            this.close();
        });

        document.getElementById('chi-chat-send').addEventListener('click', () => {
            this.sendMessage();
        });

        document.getElementById('chi-chat-input-field').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendMessage();
            }
        });
    }

    async open() {
        if (!this.currentRoom) {
            await this.createRoom();
        }

        document.getElementById('chi-chat-window').style.display = 'flex';
        document.getElementById('chi-chat-toggle').style.display = 'none';
        this.isOpen = true;
    }

    close() {
        document.getElementById('chi-chat-window').style.display = 'none';
        document.getElementById('chi-chat-toggle').style.display = 'flex';
        this.isOpen = false;
    }

    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    async createRoom() {
        try {
            const response = await this.sdk.createRoom({
                type: 'visitor',
                visitor_info: {
                    name: 'Website Visitor',
                    page: window.location.href
                }
            });

            this.currentRoom = response.data.room;
            this.sdk.connectWebSocket(this.currentRoom.uuid);
            
            // Listen for new messages
            this.sdk.on('websocket:message', (data) => {
                this.displayMessage(data.message);
            });

        } catch (error) {
            console.error('Failed to create chat room:', error);
        }
    }

    async sendMessage() {
        const input = document.getElementById('chi-chat-input-field');
        const message = input.value.trim();

        if (!message || !this.currentRoom) return;

        try {
            await this.sdk.sendMessage(this.currentRoom.uuid, {
                content: message,
                message_type: 'text'
            });

            this.displayMessage({
                content: message,
                user: { name: 'You' },
                created_at: new Date().toISOString()
            }, true);

            input.value = '';
        } catch (error) {
            console.error('Failed to send message:', error);
        }
    }

    displayMessage(message, isOwn = false) {
        const messagesContainer = document.getElementById('chi-chat-messages');
        const messageElement = document.createElement('div');
        messageElement.className = `message ${isOwn ? 'message--own' : 'message--other'}`;
        
        messageElement.innerHTML = `
            <div class="message-content">${message.content}</div>
            <div class="message-time">${new Date(message.created_at).toLocaleTimeString()}</div>
        `;

        messagesContainer.appendChild(messageElement);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ChiChatSDK, ChiChatWidget };
} else {
    window.ChiChatSDK = ChiChatSDK;
    window.ChiChatWidget = ChiChatWidget;
}
