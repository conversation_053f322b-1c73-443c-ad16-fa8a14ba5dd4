@extends('layouts.dashboard')

@section('title', 'Subscriber Tags')

@section('content')
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Subscriber Tags</h1>
            <p class="mt-1 text-sm text-gray-600">Organize and segment your subscribers with tags</p>
        </div>
        <div class="mt-4 sm:mt-0">
            <a href="{{ route('admin.subscriber-tags.create') }}" 
               class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
                Create Tag
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <form method="GET" action="{{ route('admin.subscriber-tags.index') }}" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    <input type="text" 
                           id="search" 
                           name="search" 
                           value="{{ request('search') }}"
                           placeholder="Search tags..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select id="status" 
                            name="status" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">All Statuses</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                    </select>
                </div>

                <!-- Sort -->
                <div>
                    <label for="sort_by" class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                    <select id="sort_by" 
                            name="sort_by" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="created_at" {{ request('sort_by') === 'created_at' ? 'selected' : '' }}>Created Date</option>
                        <option value="name" {{ request('sort_by') === 'name' ? 'selected' : '' }}>Name</option>
                        <option value="subscribers_count" {{ request('sort_by') === 'subscribers_count' ? 'selected' : '' }}>Subscriber Count</option>
                    </select>
                </div>
            </div>

            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div class="flex items-center space-x-2">
                    <button type="submit" 
                            class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                        Filter
                    </button>
                    <a href="{{ route('admin.subscriber-tags.index') }}" 
                       class="inline-flex items-center px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors duration-200">
                        Clear
                    </a>
                </div>

                <!-- Bulk Actions -->
                <div class="mt-4 sm:mt-0 flex items-center space-x-2">
                    <select id="bulk-action" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">Bulk Actions</option>
                        <option value="activate">Activate</option>
                        <option value="deactivate">Deactivate</option>
                        <option value="delete">Delete</option>
                    </select>
                    <button type="button" 
                            id="apply-bulk-action"
                            class="inline-flex items-center px-4 py-2 bg-secondary-600 text-white rounded-lg hover:bg-secondary-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                            disabled>
                        Apply
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Tags Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        @forelse($tags as $tag)
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200">
                <!-- Tag Header -->
                <div class="p-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 rounded-full" style="background-color: {{ $tag->color }}"></div>
                            <div class="flex-1 min-w-0">
                                <h3 class="text-lg font-semibold text-gray-900 truncate">
                                    <a href="{{ route('admin.subscriber-tags.show', $tag) }}" class="hover:text-primary-600">
                                        {{ $tag->name }}
                                    </a>
                                </h3>
                            </div>
                        </div>
                        <div class="flex-shrink-0">
                            <input type="checkbox" 
                                   class="tag-checkbox rounded border-gray-300 text-primary-600 focus:ring-primary-500" 
                                   value="{{ $tag->id }}">
                        </div>
                    </div>
                    @if($tag->description)
                        <p class="mt-2 text-sm text-gray-600 line-clamp-2">{{ $tag->description }}</p>
                    @endif
                </div>

                <!-- Tag Stats -->
                <div class="p-4">
                    <div class="flex items-center justify-between">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-gray-900">{{ number_format($tag->subscribers_count) }}</div>
                            <div class="text-xs text-gray-500">Subscribers</div>
                        </div>
                        <div class="text-center">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $tag->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ $tag->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="mt-4 flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <button type="button" 
                                    class="toggle-status-btn text-sm {{ $tag->is_active ? 'text-red-600 hover:text-red-700' : 'text-green-600 hover:text-green-700' }}"
                                    data-tag-id="{{ $tag->id }}"
                                    data-current-status="{{ $tag->is_active ? 'active' : 'inactive' }}">
                                {{ $tag->is_active ? 'Deactivate' : 'Activate' }}
                            </button>
                        </div>
                        <div class="flex items-center space-x-1">
                            <a href="{{ route('admin.subscriber-tags.edit', $tag) }}" 
                               class="p-1 text-gray-400 hover:text-gray-600"
                               title="Edit">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                </svg>
                            </a>
                            @if($tag->subscribers_count === 0)
                                <button type="button" 
                                        class="delete-btn p-1 text-gray-400 hover:text-red-600"
                                        data-tag-id="{{ $tag->id }}"
                                        title="Delete">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                    </svg>
                                </button>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Tag Footer -->
                <div class="px-4 py-3 bg-gray-50 text-xs text-gray-500">
                    Created {{ $tag->created_at->format('M j, Y') }}
                </div>
            </div>
        @empty
            <div class="col-span-full">
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No subscriber tags</h3>
                    <p class="mt-1 text-sm text-gray-500">Get started by creating your first subscriber tag.</p>
                    <div class="mt-6">
                        <a href="{{ route('admin.subscriber-tags.create') }}" 
                           class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                            </svg>
                            Create Tag
                        </a>
                    </div>
                </div>
            </div>
        @endforelse
    </div>

    <!-- Pagination -->
    @if($tags->hasPages())
        <div class="flex justify-center">
            {{ $tags->links() }}
        </div>
    @endif
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Bulk action functionality
    const checkboxes = document.querySelectorAll('.tag-checkbox');
    const bulkActionSelect = document.getElementById('bulk-action');
    const applyBulkActionBtn = document.getElementById('apply-bulk-action');

    function updateBulkActionButton() {
        const checkedBoxes = document.querySelectorAll('.tag-checkbox:checked');
        applyBulkActionBtn.disabled = checkedBoxes.length === 0;
    }

    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActionButton);
    });

    applyBulkActionBtn.addEventListener('click', function() {
        const action = bulkActionSelect.value;
        const checkedBoxes = document.querySelectorAll('.tag-checkbox:checked');
        const tagIds = Array.from(checkedBoxes).map(cb => cb.value);

        if (!action || tagIds.length === 0) {
            return;
        }

        if (action === 'delete' && !confirm('Are you sure you want to delete the selected tags?')) {
            return;
        }

        // Perform bulk action via AJAX
        fetch('{{ route("admin.subscriber-tags.bulk-action") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                action: action,
                tag_ids: tagIds
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'An error occurred');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    });

    // Individual action handlers
    document.querySelectorAll('.toggle-status-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const tagId = this.dataset.tagId;
            
            fetch(`/admin/subscriber-tags/${tagId}/toggle-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || 'An error occurred');
                }
            });
        });
    });

    document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            if (!confirm('Are you sure you want to delete this tag?')) {
                return;
            }

            const tagId = this.dataset.tagId;
            
            fetch(`/admin/subscriber-tags/${tagId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || 'An error occurred');
                }
            });
        });
    });
});
</script>
@endpush
@endsection
