<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\EmailCampaign;
use App\Models\EmailTemplate;
use App\Models\NewsletterSubscription;
use App\Models\SubscriberTag;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class EmailCampaignController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): View|JsonResponse
    {
        $query = EmailCampaign::with(['emailTemplate', 'creator', 'updater', 'parentCampaign'])
                              ->withCount(['sends', 'childCampaigns']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%");
            });
        }

        if ($request->filled('type')) {
            $query->where('type', $request->get('type'));
        }

        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        if ($request->filled('template')) {
            $query->where('email_template_id', $request->get('template'));
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->get('date_from'));
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->get('date_to'));
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $campaigns = $query->paginate(15)->withQueryString();

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'data' => $campaigns,
                'types' => EmailCampaign::getTypes(),
                'statuses' => EmailCampaign::getStatuses(),
            ]);
        }

        $templates = EmailTemplate::active()->get(['id', 'name']);

        return view('admin.email-campaigns.index', compact('campaigns', 'templates'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request): View
    {
        $templates = EmailTemplate::active()->get(['id', 'uuid', 'name', 'category', 'subject']);
        $tags = SubscriberTag::active()->get(['id', 'uuid', 'name', 'color']);
        $types = EmailCampaign::getTypes();

        // Get subscriber counts for targeting
        $totalSubscribers = NewsletterSubscription::active()->count();
        $subscriberStats = [
            'total' => $totalSubscribers,
            'by_lifecycle' => NewsletterSubscription::active()
                ->select('lifecycle_stage', DB::raw('count(*) as count'))
                ->groupBy('lifecycle_stage')
                ->pluck('count', 'lifecycle_stage')
                ->toArray(),
        ];

        // If creating a drip sequence email
        $parentCampaign = null;
        if ($request->filled('parent_campaign')) {
            $parentCampaign = EmailCampaign::where('uuid', $request->get('parent_campaign'))->first();
        }

        return view('admin.email-campaigns.create', compact(
            'templates',
            'tags',
            'types',
            'subscriberStats',
            'parentCampaign'
        ));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse|RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'type' => 'required|string|in:' . implode(',', array_keys(EmailCampaign::getTypes())),
            'email_template_id' => 'nullable|exists:email_templates,id',
            'subject' => 'required|string|max:255',
            'from_name' => 'nullable|string|max:255',
            'from_email' => 'nullable|email|max:255',
            'reply_to' => 'nullable|email|max:255',

            // Scheduling
            'scheduled_at' => 'nullable|date|after:now',
            'is_recurring' => 'boolean',
            'recurring_frequency' => 'nullable|string|in:daily,weekly,monthly',
            'recurring_settings' => 'nullable|array',

            // Targeting
            'send_to_all' => 'boolean',
            'target_segments' => 'nullable|array',
            'target_criteria' => 'nullable|array',

            // Tracking
            'track_opens' => 'boolean',
            'track_clicks' => 'boolean',

            // Drip campaign
            'parent_campaign_id' => 'nullable|exists:email_campaigns,id',
            'sequence_order' => 'nullable|integer|min:1',
            'delay_days' => 'nullable|integer|min:0',
            'trigger_conditions' => 'nullable|array',
        ]);

        $validated['created_by'] = auth()->id();
        $validated['updated_by'] = auth()->id();
        $validated['status'] = 'draft';

        // Calculate total recipients based on targeting
        $validated['total_recipients'] = $this->calculateTargetedSubscribers($validated);

        $campaign = EmailCampaign::create($validated);

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Email campaign created successfully.',
                'data' => $campaign->load(['emailTemplate', 'creator', 'parentCampaign']),
                'redirect' => route('admin.email-campaigns.show', $campaign),
            ]);
        }

        return redirect()->route('admin.email-campaigns.show', $campaign)
                        ->with('success', 'Email campaign created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(EmailCampaign $emailCampaign): View
    {
        $emailCampaign->load([
            'emailTemplate',
            'creator',
            'updater',
            'parentCampaign',
            'childCampaigns' => function ($query) {
                $query->orderBy('sequence_order');
            },
            'sends' => function ($query) {
                $query->latest()->limit(10);
            }
        ]);

        $statistics = $emailCampaign->getStatistics();

        return view('admin.email-campaigns.show', compact('emailCampaign', 'statistics'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(EmailCampaign $emailCampaign): View
    {
        if (!$emailCampaign->canBeEdited()) {
            abort(403, 'This campaign cannot be edited in its current status.');
        }

        $templates = EmailTemplate::active()->get(['id', 'uuid', 'name', 'category', 'subject']);
        $tags = SubscriberTag::active()->get(['id', 'uuid', 'name', 'color']);
        $types = EmailCampaign::getTypes();

        // Get subscriber counts for targeting
        $totalSubscribers = NewsletterSubscription::active()->count();
        $subscriberStats = [
            'total' => $totalSubscribers,
            'by_lifecycle' => NewsletterSubscription::active()
                ->select('lifecycle_stage', DB::raw('count(*) as count'))
                ->groupBy('lifecycle_stage')
                ->pluck('count', 'lifecycle_stage')
                ->toArray(),
        ];

        return view('admin.email-campaigns.edit', compact(
            'emailCampaign',
            'templates',
            'tags',
            'types',
            'subscriberStats'
        ));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, EmailCampaign $emailCampaign): JsonResponse|RedirectResponse
    {
        if (!$emailCampaign->canBeEdited()) {
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'This campaign cannot be edited in its current status.',
                ], 403);
            }

            return redirect()->back()
                            ->with('error', 'This campaign cannot be edited in its current status.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'type' => 'required|string|in:' . implode(',', array_keys(EmailCampaign::getTypes())),
            'email_template_id' => 'nullable|exists:email_templates,id',
            'subject' => 'required|string|max:255',
            'from_name' => 'nullable|string|max:255',
            'from_email' => 'nullable|email|max:255',
            'reply_to' => 'nullable|email|max:255',

            // Scheduling
            'scheduled_at' => 'nullable|date|after:now',
            'is_recurring' => 'boolean',
            'recurring_frequency' => 'nullable|string|in:daily,weekly,monthly',
            'recurring_settings' => 'nullable|array',

            // Targeting
            'send_to_all' => 'boolean',
            'target_segments' => 'nullable|array',
            'target_criteria' => 'nullable|array',

            // Tracking
            'track_opens' => 'boolean',
            'track_clicks' => 'boolean',

            // Drip campaign
            'delay_days' => 'nullable|integer|min:0',
            'trigger_conditions' => 'nullable|array',
        ]);

        $validated['updated_by'] = auth()->id();

        // Recalculate total recipients if targeting changed
        if ($request->has(['send_to_all', 'target_segments', 'target_criteria'])) {
            $validated['total_recipients'] = $this->calculateTargetedSubscribers($validated);
        }

        $emailCampaign->update($validated);

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Email campaign updated successfully.',
                'data' => $emailCampaign->fresh(['emailTemplate', 'creator', 'updater']),
            ]);
        }

        $action = $request->get('submit_action', 'save');

        if ($action === 'save_and_continue') {
            return redirect()->route('admin.email-campaigns.edit', $emailCampaign)
                            ->with('success', 'Email campaign updated successfully.');
        }

        return redirect()->route('admin.email-campaigns.show', $emailCampaign)
                        ->with('success', 'Email campaign updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(EmailCampaign $emailCampaign): JsonResponse|RedirectResponse
    {
        if (!$emailCampaign->canBeDeleted()) {
            if (request()->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'This campaign cannot be deleted in its current status.',
                ], 422);
            }

            return redirect()->back()
                            ->with('error', 'This campaign cannot be deleted in its current status.');
        }

        $emailCampaign->delete();

        if (request()->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Email campaign deleted successfully.',
            ]);
        }

        return redirect()->route('admin.email-campaigns.index')
                        ->with('success', 'Email campaign deleted successfully.');
    }

    /**
     * Schedule campaign for sending.
     */
    public function schedule(EmailCampaign $emailCampaign, Request $request): JsonResponse
    {
        $request->validate([
            'scheduled_at' => 'required|date|after:now',
        ]);

        if (!$emailCampaign->canBeSent()) {
            return response()->json([
                'success' => false,
                'message' => 'This campaign cannot be scheduled. Please ensure all required fields are completed.',
            ], 422);
        }

        $emailCampaign->update([
            'status' => 'scheduled',
            'scheduled_at' => $request->get('scheduled_at'),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Campaign scheduled successfully.',
            'scheduled_at' => $emailCampaign->scheduled_at->format('M j, Y g:i A'),
        ]);
    }

    /**
     * Send campaign immediately.
     */
    public function sendNow(EmailCampaign $emailCampaign): JsonResponse
    {
        if (!$emailCampaign->canBeSent()) {
            return response()->json([
                'success' => false,
                'message' => 'This campaign cannot be sent. Please ensure all required fields are completed.',
            ], 422);
        }

        // Mark campaign as started
        $emailCampaign->markAsStarted();

        // Dispatch job to send emails (we'll create this job later)
        // SendEmailCampaignJob::dispatch($emailCampaign);

        return response()->json([
            'success' => true,
            'message' => 'Campaign is being sent. You will receive a notification when complete.',
        ]);
    }

    /**
     * Pause campaign.
     */
    public function pause(EmailCampaign $emailCampaign): JsonResponse
    {
        $emailCampaign->pause();

        return response()->json([
            'success' => true,
            'message' => 'Campaign paused successfully.',
        ]);
    }

    /**
     * Resume campaign.
     */
    public function resume(EmailCampaign $emailCampaign): JsonResponse
    {
        $emailCampaign->resume();

        return response()->json([
            'success' => true,
            'message' => 'Campaign resumed successfully.',
        ]);
    }

    /**
     * Cancel campaign.
     */
    public function cancel(EmailCampaign $emailCampaign): JsonResponse
    {
        $emailCampaign->cancel();

        return response()->json([
            'success' => true,
            'message' => 'Campaign cancelled successfully.',
        ]);
    }

    /**
     * Duplicate campaign.
     */
    public function duplicate(EmailCampaign $emailCampaign): JsonResponse|RedirectResponse
    {
        $newCampaign = $emailCampaign->replicate();
        $newCampaign->uuid = \Illuminate\Support\Str::uuid();
        $newCampaign->name = $emailCampaign->name . ' (Copy)';
        $newCampaign->slug = \Illuminate\Support\Str::slug($newCampaign->name);
        $newCampaign->status = 'draft';
        $newCampaign->scheduled_at = null;
        $newCampaign->started_at = null;
        $newCampaign->completed_at = null;
        $newCampaign->emails_sent = 0;
        $newCampaign->emails_delivered = 0;
        $newCampaign->emails_bounced = 0;
        $newCampaign->emails_opened = 0;
        $newCampaign->emails_clicked = 0;
        $newCampaign->unsubscribes = 0;
        $newCampaign->created_by = auth()->id();
        $newCampaign->updated_by = auth()->id();
        $newCampaign->save();

        if (request()->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Campaign duplicated successfully.',
                'data' => $newCampaign->load(['emailTemplate', 'creator']),
                'redirect' => route('admin.email-campaigns.edit', $newCampaign),
            ]);
        }

        return redirect()->route('admin.email-campaigns.edit', $newCampaign)
                        ->with('success', 'Campaign duplicated successfully.');
    }

    /**
     * Get campaign analytics.
     */
    public function analytics(EmailCampaign $emailCampaign): JsonResponse
    {
        $statistics = $emailCampaign->getStatistics();

        // Get performance over time
        $performanceData = $emailCampaign->sends()
            ->selectRaw('DATE(sent_at) as date, COUNT(*) as sent, SUM(CASE WHEN opened_at IS NOT NULL THEN 1 ELSE 0 END) as opened, SUM(CASE WHEN first_clicked_at IS NOT NULL THEN 1 ELSE 0 END) as clicked')
            ->whereNotNull('sent_at')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return response()->json([
            'success' => true,
            'statistics' => $statistics,
            'performance_data' => $performanceData,
        ]);
    }

    /**
     * Preview campaign content.
     */
    public function preview(EmailCampaign $emailCampaign, Request $request): View|JsonResponse
    {
        $variables = $request->get('variables', []);

        // Get template content
        $content = $emailCampaign->emailTemplate
            ? $emailCampaign->emailTemplate->render($variables)
            : [
                'subject' => $emailCampaign->subject,
                'html_content' => 'No template selected',
                'text_content' => 'No template selected',
            ];

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'data' => $content,
            ]);
        }

        return view('admin.email-campaigns.preview', compact('emailCampaign', 'content', 'variables'));
    }

    /**
     * Get targeted subscribers count.
     */
    public function getTargetedSubscribers(Request $request): JsonResponse
    {
        $count = $this->calculateTargetedSubscribers($request->all());

        return response()->json([
            'success' => true,
            'count' => $count,
        ]);
    }

    /**
     * Create drip sequence email.
     */
    public function createDripEmail(EmailCampaign $emailCampaign): RedirectResponse
    {
        if ($emailCampaign->type !== 'drip') {
            return redirect()->back()
                            ->with('error', 'Only drip campaigns can have sequence emails.');
        }

        $nextSequenceOrder = $emailCampaign->childCampaigns()->max('sequence_order') + 1;

        return redirect()->route('admin.email-campaigns.create', [
            'parent_campaign' => $emailCampaign->uuid,
            'sequence_order' => $nextSequenceOrder,
        ]);
    }

    /**
     * Calculate targeted subscribers based on criteria.
     */
    private function calculateTargetedSubscribers(array $criteria): int
    {
        if ($criteria['send_to_all'] ?? false) {
            return NewsletterSubscription::active()->count();
        }

        $query = NewsletterSubscription::active();

        // Apply segment filters
        if (!empty($criteria['target_segments'])) {
            $query->whereHas('tags', function ($q) use ($criteria) {
                $q->whereIn('subscriber_tags.id', $criteria['target_segments']);
            });
        }

        // Apply additional criteria
        if (!empty($criteria['target_criteria'])) {
            $targetCriteria = $criteria['target_criteria'];

            if (isset($targetCriteria['lifecycle_stage'])) {
                $query->whereIn('lifecycle_stage', $targetCriteria['lifecycle_stage']);
            }

            if (isset($targetCriteria['engagement_score_min'])) {
                $query->where('engagement_score', '>=', $targetCriteria['engagement_score_min']);
            }

            if (isset($targetCriteria['engagement_score_max'])) {
                $query->where('engagement_score', '<=', $targetCriteria['engagement_score_max']);
            }

            if (isset($targetCriteria['last_activity_days'])) {
                $date = Carbon::now()->subDays($targetCriteria['last_activity_days']);
                $query->where('last_activity_at', '>=', $date);
            }

            if (isset($targetCriteria['allow_marketing']) && $targetCriteria['allow_marketing']) {
                $query->where('allow_marketing', true);
            }

            if (isset($targetCriteria['allow_promotional']) && $targetCriteria['allow_promotional']) {
                $query->where('allow_promotional', true);
            }
        }

        return $query->count();
    }

    /**
     * Bulk actions for campaigns.
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $request->validate([
            'action' => 'required|string|in:pause,resume,cancel,delete',
            'campaign_ids' => 'required|array|min:1',
            'campaign_ids.*' => 'exists:email_campaigns,id',
        ]);

        $campaignIds = $request->get('campaign_ids');
        $action = $request->get('action');

        $campaigns = EmailCampaign::whereIn('id', $campaignIds);

        switch ($action) {
            case 'pause':
                $campaigns->where('status', 'sending')->update(['status' => 'paused']);
                $message = 'Campaigns paused successfully.';
                break;
            case 'resume':
                $campaigns->where('status', 'paused')->update(['status' => 'sending']);
                $message = 'Campaigns resumed successfully.';
                break;
            case 'cancel':
                $campaigns->whereIn('status', ['draft', 'scheduled', 'paused'])->update(['status' => 'cancelled']);
                $message = 'Campaigns cancelled successfully.';
                break;
            case 'delete':
                $deletableCampaigns = $campaigns->get()->filter(fn($campaign) => $campaign->canBeDeleted());
                $deletableCampaigns->each(fn($campaign) => $campaign->delete());
                $deletedCount = $deletableCampaigns->count();
                $totalCount = count($campaignIds);

                if ($deletedCount === $totalCount) {
                    $message = 'All selected campaigns deleted successfully.';
                } else {
                    $skippedCount = $totalCount - $deletedCount;
                    $message = "{$deletedCount} campaigns deleted successfully. {$skippedCount} campaigns could not be deleted.";
                }
                break;
        }

        return response()->json([
            'success' => true,
            'message' => $message,
        ]);
    }
}
