/**
 * Checkout Activity Logger
 * Handles client-side error logging and user interaction tracking for checkout pages
 */
class CheckoutLogger {
    constructor() {
        this.apiEndpoint = '/api/log-checkout-activity';
        this.sessionId = this.getSessionId();
        this.pageLoadTime = Date.now();
        this.userInteractions = [];
        this.errors = [];
        this.formValidationErrors = [];
        
        this.init();
    }

    /**
     * Initialize the logger
     */
    init() {
        this.setupGlobalErrorHandling();
        this.setupFormValidationLogging();
        this.setupUserInteractionTracking();
        this.setupAjaxErrorLogging();
        this.setupPaymentGatewayLogging();
        this.logPageLoad();
    }

    /**
     * Get or generate session ID
     */
    getSessionId() {
        let sessionId = sessionStorage.getItem('checkout_session_id');
        if (!sessionId) {
            sessionId = 'checkout_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            sessionStorage.setItem('checkout_session_id', sessionId);
        }
        return sessionId;
    }

    /**
     * Setup global error handling
     */
    setupGlobalErrorHandling() {
        window.addEventListener('error', (event) => {
            this.logError('javascript_error', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error?.stack,
                timestamp: Date.now(),
                page_url: window.location.href,
                user_agent: navigator.userAgent
            });
        });

        window.addEventListener('unhandledrejection', (event) => {
            this.logError('promise_rejection', {
                reason: event.reason?.toString(),
                stack: event.reason?.stack,
                timestamp: Date.now(),
                page_url: window.location.href
            });
        });
    }

    /**
     * Setup form validation logging
     */
    setupFormValidationLogging() {
        // Monitor form submission attempts
        document.addEventListener('submit', (event) => {
            const form = event.target;
            if (form.id === 'checkout-form' || form.classList.contains('checkout-form')) {
                this.logUserInteraction('form_submit_attempt', {
                    form_id: form.id,
                    form_data: this.getFormData(form),
                    timestamp: Date.now()
                });
            }
        });

        // Monitor validation errors
        document.addEventListener('invalid', (event) => {
            const field = event.target;
            this.logFormValidationError(field.name, {
                field_name: field.name,
                field_type: field.type,
                field_value: field.value ? '[REDACTED]' : '',
                validation_message: field.validationMessage,
                timestamp: Date.now()
            });
        });

        // Monitor field changes for validation
        document.addEventListener('blur', (event) => {
            const field = event.target;
            if (field.form && (field.form.id === 'checkout-form' || field.form.classList.contains('checkout-form'))) {
                if (!field.checkValidity()) {
                    this.logFormValidationError(field.name, {
                        field_name: field.name,
                        field_type: field.type,
                        validation_message: field.validationMessage,
                        trigger: 'blur',
                        timestamp: Date.now()
                    });
                }
            }
        });
    }

    /**
     * Setup user interaction tracking
     */
    setupUserInteractionTracking() {
        // Track button clicks
        document.addEventListener('click', (event) => {
            const element = event.target;
            if (this.isCheckoutElement(element)) {
                this.logUserInteraction('button_click', {
                    element_id: element.id,
                    element_class: element.className,
                    element_text: element.textContent?.trim().substring(0, 50),
                    element_type: element.tagName.toLowerCase(),
                    timestamp: Date.now()
                });
            }
        });

        // Track form field focus
        document.addEventListener('focus', (event) => {
            const element = event.target;
            if (this.isCheckoutFormField(element)) {
                this.logUserInteraction('field_focus', {
                    field_name: element.name,
                    field_type: element.type,
                    timestamp: Date.now()
                });
            }
        });

        // Track form field changes
        document.addEventListener('change', (event) => {
            const element = event.target;
            if (this.isCheckoutFormField(element)) {
                this.logUserInteraction('field_change', {
                    field_name: element.name,
                    field_type: element.type,
                    has_value: !!element.value,
                    timestamp: Date.now()
                });
            }
        });

        // Track page navigation
        window.addEventListener('beforeunload', () => {
            this.logUserInteraction('page_unload', {
                time_on_page: Date.now() - this.pageLoadTime,
                interactions_count: this.userInteractions.length,
                errors_count: this.errors.length,
                timestamp: Date.now()
            });
            this.flushLogs();
        });
    }

    /**
     * Setup AJAX error logging
     */
    setupAjaxErrorLogging() {
        // Override fetch for error logging
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            try {
                const response = await originalFetch(...args);
                
                // Log failed requests
                if (!response.ok) {
                    this.logError('ajax_error', {
                        url: args[0],
                        status: response.status,
                        status_text: response.statusText,
                        method: args[1]?.method || 'GET',
                        timestamp: Date.now()
                    });
                }
                
                return response;
            } catch (error) {
                this.logError('ajax_network_error', {
                    url: args[0],
                    error_message: error.message,
                    method: args[1]?.method || 'GET',
                    timestamp: Date.now()
                });
                throw error;
            }
        };

        // Monitor XMLHttpRequest
        const originalXHROpen = XMLHttpRequest.prototype.open;
        const originalXHRSend = XMLHttpRequest.prototype.send;

        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            this._method = method;
            this._url = url;
            return originalXHROpen.call(this, method, url, ...args);
        };

        XMLHttpRequest.prototype.send = function(...args) {
            this.addEventListener('error', () => {
                window.checkoutLogger?.logError('xhr_error', {
                    url: this._url,
                    method: this._method,
                    status: this.status,
                    timestamp: Date.now()
                });
            });

            this.addEventListener('load', () => {
                if (this.status >= 400) {
                    window.checkoutLogger?.logError('xhr_http_error', {
                        url: this._url,
                        method: this._method,
                        status: this.status,
                        response: this.responseText?.substring(0, 500),
                        timestamp: Date.now()
                    });
                }
            });

            return originalXHRSend.call(this, ...args);
        };
    }

    /**
     * Setup payment gateway logging
     */
    setupPaymentGatewayLogging() {
        // Monitor Stripe errors
        if (window.Stripe) {
            const originalCreateToken = window.Stripe.prototype.createToken;
            if (originalCreateToken) {
                window.Stripe.prototype.createToken = function(...args) {
                    return originalCreateToken.call(this, ...args).then(
                        result => {
                            if (result.error) {
                                window.checkoutLogger?.logError('stripe_error', {
                                    error_type: result.error.type,
                                    error_code: result.error.code,
                                    error_message: result.error.message,
                                    timestamp: Date.now()
                                });
                            }
                            return result;
                        }
                    );
                };
            }
        }

        // Monitor PayPal errors (if implemented)
        if (window.paypal) {
            // PayPal error logging would go here
        }
    }

    /**
     * Log page load
     */
    logPageLoad() {
        this.logUserInteraction('page_load', {
            page_url: window.location.href,
            page_title: document.title,
            referrer: document.referrer,
            user_agent: navigator.userAgent,
            screen_resolution: `${screen.width}x${screen.height}`,
            viewport_size: `${window.innerWidth}x${window.innerHeight}`,
            timestamp: Date.now()
        });
    }

    /**
     * Check if element is part of checkout
     */
    isCheckoutElement(element) {
        return element.closest('#checkout-form') || 
               element.closest('.checkout-form') ||
               element.closest('.payment-form') ||
               element.id?.includes('checkout') ||
               element.className?.includes('checkout');
    }

    /**
     * Check if element is a checkout form field
     */
    isCheckoutFormField(element) {
        return (element.tagName === 'INPUT' || element.tagName === 'SELECT' || element.tagName === 'TEXTAREA') &&
               this.isCheckoutElement(element);
    }

    /**
     * Get sanitized form data
     */
    getFormData(form) {
        const formData = new FormData(form);
        const data = {};
        const sensitiveFields = ['password', 'password_confirmation', 'card_number', 'cvv'];
        
        for (let [key, value] of formData.entries()) {
            if (sensitiveFields.includes(key)) {
                data[key] = '[REDACTED]';
            } else {
                data[key] = value ? '[HAS_VALUE]' : '[EMPTY]';
            }
        }
        
        return data;
    }

    /**
     * Log an error
     */
    logError(type, data) {
        const errorLog = {
            type: type,
            data: data,
            session_id: this.sessionId,
            timestamp: Date.now(),
            page_url: window.location.href
        };

        this.errors.push(errorLog);
        this.sendLog('error', errorLog);
        
        console.error('Checkout Error:', errorLog);
    }

    /**
     * Log form validation error
     */
    logFormValidationError(fieldName, data) {
        const validationError = {
            field_name: fieldName,
            data: data,
            session_id: this.sessionId,
            timestamp: Date.now()
        };

        this.formValidationErrors.push(validationError);
        this.sendLog('form_validation_error', validationError);
    }

    /**
     * Log user interaction
     */
    logUserInteraction(type, data) {
        const interaction = {
            type: type,
            data: data,
            session_id: this.sessionId,
            timestamp: Date.now()
        };

        this.userInteractions.push(interaction);
        
        // Send important interactions immediately
        if (['form_submit_attempt', 'page_unload', 'payment_error'].includes(type)) {
            this.sendLog('interaction', interaction);
        }
    }

    /**
     * Send log to server
     */
    async sendLog(logType, logData) {
        const payload = JSON.stringify({
            log_type: logType,
            log_data: logData
        });

        // For critical events like form submission, use sendBeacon to ensure delivery
        // even if the page is being unloaded
        if (logType === 'interaction' && logData.type === 'form_submit_attempt') {
            try {
                // Use sendBeacon for reliable delivery during page transitions
                // Note: sendBeacon doesn't support custom headers, but our API doesn't require CSRF token
                const success = navigator.sendBeacon(this.apiEndpoint, payload);
                if (!success) {
                    console.warn('sendBeacon failed, falling back to fetch');
                    // Fallback to regular fetch if sendBeacon fails
                    await this.sendLogWithFetch(payload);
                }
                return; // Exit early for sendBeacon
            } catch (error) {
                console.error('Failed to send checkout log with sendBeacon:', error);
                // Fall through to regular fetch on error
            }
        }

        // Use regular fetch for non-critical logs
        await this.sendLogWithFetch(payload);
    }

    /**
     * Send log using regular fetch
     */
    async sendLogWithFetch(payload) {
        try {
            await fetch(this.apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                },
                body: payload
            });
        } catch (error) {
            console.error('Failed to send checkout log:', error);
        }
    }

    /**
     * Flush all pending logs
     */
    flushLogs() {
        if (this.userInteractions.length > 0 || this.errors.length > 0) {
            navigator.sendBeacon(this.apiEndpoint, JSON.stringify({
                log_type: 'batch',
                log_data: {
                    interactions: this.userInteractions,
                    errors: this.errors,
                    form_validation_errors: this.formValidationErrors,
                    session_id: this.sessionId,
                    timestamp: Date.now()
                }
            }));
        }
    }
}

// Initialize checkout logger when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize if the inline checkout form logger is not already active
    if (!window.checkoutFormLoggerActive) {
        window.checkoutLogger = new CheckoutLogger();
    }
});

export default CheckoutLogger;
