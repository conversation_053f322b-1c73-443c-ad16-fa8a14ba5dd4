@extends('layouts.dashboard')

@section('title', 'Login History Management')

@section('content')
<div class="p-6 space-y-6">
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Login History Management</h1>
                <p class="mt-2 text-gray-600">Monitor and analyze user login activity across the platform</p>
            </div>
            <div class="flex space-x-3">
                <button id="export-btn" class="bg-secondary-600 text-white px-4 py-2 rounded-lg hover:bg-secondary-700 focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2 transition-colors">
                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    Export Data
                </button>
                <button id="refresh-btn" class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors">
                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    Refresh
                </button>
            </div>
        </div>

        <!-- Access Level Indicator -->
        <div class="mt-4">
            <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                {{ $accessLevel === 'full' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"/>
                </svg>
                {{ ucfirst($accessLevel) }} Access
                @if($accessLevel !== 'full')
                    <span class="ml-2 text-xs">(Some data may be masked)</span>
                @endif
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"/>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Logins</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['total_logins']) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Success Rate</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ $stats['success_rate'] }}%</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-red-100 rounded-lg">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Failed Logins</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['failed_logins']) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-orange-100 rounded-lg">
                    <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Suspicious Activity</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['suspicious_logins']) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Advanced Filters</h3>
            <p class="text-sm text-gray-600">Filter login history data by various criteria</p>
        </div>
        <div class="p-6">
            <form id="filter-form" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                <!-- User Filter -->
                <div>
                    <label for="user-filter" class="block text-sm font-medium text-gray-700 mb-2">User</label>
                    <select id="user-filter" name="user_id" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">All Users</option>
                        @foreach($users as $user)
                            <option value="{{ $user->id }}">{{ $user->first_name }} {{ $user->last_name }} ({{ $user->email }})</option>
                        @endforeach
                    </select>
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select id="status-filter" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="all">All Status</option>
                        <option value="success">Successful</option>
                        <option value="failed">Failed</option>
                    </select>
                </div>

                <!-- Device Type Filter -->
                <div>
                    <label for="device-filter" class="block text-sm font-medium text-gray-700 mb-2">Device Type</label>
                    <select id="device-filter" name="device_type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="all">All Devices</option>
                        <option value="desktop">Desktop</option>
                        <option value="mobile">Mobile</option>
                        <option value="tablet">Tablet</option>
                    </select>
                </div>

                <!-- Risk Level Filter -->
                <div>
                    <label for="risk-filter" class="block text-sm font-medium text-gray-700 mb-2">Risk Level</label>
                    <select id="risk-filter" name="risk_level" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="all">All Risk Levels</option>
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                        <option value="critical">Critical</option>
                    </select>
                </div>

                <!-- Location Filter -->
                <div>
                    <label for="location-filter" class="block text-sm font-medium text-gray-700 mb-2">Location</label>
                    <input type="text" id="location-filter" name="location" placeholder="Search location..." 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                </div>

                <!-- IP Address Filter -->
                <div>
                    <label for="ip-filter" class="block text-sm font-medium text-gray-700 mb-2">IP Address</label>
                    <input type="text" id="ip-filter" name="ip_address" placeholder="Search IP address..." 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                </div>

                <!-- Date Range Filter -->
                <div>
                    <label for="date-range-filter" class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                    <select id="date-range-filter" name="date_range" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">All Time</option>
                        <option value="today">Today</option>
                        <option value="this_week">This Week</option>
                        <option value="this_year">This Year</option>
                        <option value="7_days">Last 7 Days</option>
                        <option value="custom">Custom Range</option>
                    </select>
                </div>

                <!-- Suspicious Only Filter -->
                <div class="flex items-center">
                    <input type="checkbox" id="suspicious-filter" name="suspicious_only" value="1" 
                           class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                    <label for="suspicious-filter" class="ml-2 block text-sm text-gray-900">
                        Suspicious Activity Only
                    </label>
                </div>

                <!-- Custom Date Range (Hidden by default) -->
                <div id="custom-date-range" class="hidden xl:col-span-4 lg:col-span-3 md:col-span-2">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div>
                            <label for="start-date" class="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                            <input type="date" id="start-date" name="start_date" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        </div>
                        <div>
                            <label for="end-date" class="block text-sm font-medium text-gray-700 mb-2">End Date</label>
                            <input type="date" id="end-date" name="end_date" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        </div>
                    </div>
                </div>

                <!-- Filter Actions -->
                <div class="xl:col-span-4 lg:col-span-3 md:col-span-2 flex justify-end space-x-3 mt-4">
                    <button type="button" id="clear-filters" 
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                        Clear Filters
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-lg hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                        Apply Filters
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Login History Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <div>
                <h2 class="text-lg font-medium text-gray-900">Login History Data</h2>
                <p class="text-sm text-gray-600">Comprehensive view of user login activity</p>
            </div>
            <div class="flex items-center space-x-4">
                <!-- Sorting Controls -->
                <div class="flex items-center space-x-2">
                    <label for="sort-by" class="text-sm font-medium text-gray-700">Sort by:</label>
                    <select id="sort-by" class="px-3 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="date">Date</option>
                        <option value="user">User</option>
                        <option value="risk_score">Risk Score</option>
                        <option value="location">Location</option>
                    </select>
                    <button id="sort-direction" class="p-1 text-gray-400 hover:text-gray-600" title="Toggle sort direction">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"/>
                        </svg>
                    </button>
                </div>
                
                <div id="loading-indicator" class="hidden">
                    <svg class="animate-spin h-5 w-5 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Device</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Risk</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Flags</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody id="login-history-table-body" class="bg-white divide-y divide-gray-200">
                    <!-- Content will be loaded via AJAX -->
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div id="pagination-container" class="px-6 py-4 border-t border-gray-200">
            <!-- Pagination will be loaded via AJAX -->
        </div>
    </div>
</div>

<!-- Export Modal -->
<div id="export-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Export Login History</h3>
            <form id="export-form">
                <div class="mb-4">
                    <label for="export-format" class="block text-sm font-medium text-gray-700 mb-2">Export Format</label>
                    <select id="export-format" name="format" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="csv">CSV</option>
                        <option value="json">JSON</option>
                    </select>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancel-export" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-lg hover:bg-primary-700">
                        Export
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Detail Modal -->
<div id="detail-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Login History Details</h3>
                <button id="close-detail-modal" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <div id="detail-content">
                <!-- Content will be loaded via AJAX -->
            </div>
        </div>
    </div>
</div>

<script>
// Global variables
let currentPage = 1;
let currentSort = { by: 'date', direction: 'desc' };
let accessLevel = '{{ $accessLevel }}';

/**
 * Sanitize HTML to prevent XSS attacks
 * @param {string} str - The string to sanitize
 * @returns {string} - The sanitized string
 */
function escapeHtml(str) {
    if (str === null || str === undefined) {
        return '';
    }
    const div = document.createElement('div');
    div.textContent = str;
    return div.innerHTML;
}

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the page
    initializeEventListeners();
    loadLoginHistory();
});

function initializeEventListeners() {
    // Filter form
    document.getElementById('filter-form').addEventListener('submit', function(e) {
        e.preventDefault();
        currentPage = 1;
        loadLoginHistory();
    });

    // Clear filters
    document.getElementById('clear-filters').addEventListener('click', function() {
        document.getElementById('filter-form').reset();
        document.getElementById('custom-date-range').classList.add('hidden');
        currentPage = 1;
        loadLoginHistory();
    });

    // Date range toggle
    document.getElementById('date-range-filter').addEventListener('change', function() {
        const customRange = document.getElementById('custom-date-range');
        if (this.value === 'custom') {
            customRange.classList.remove('hidden');
        } else {
            customRange.classList.add('hidden');
        }
    });

    // Sorting
    document.getElementById('sort-by').addEventListener('change', function() {
        currentSort.by = this.value;
        currentPage = 1;
        loadLoginHistory();
    });

    document.getElementById('sort-direction').addEventListener('click', function() {
        currentSort.direction = currentSort.direction === 'desc' ? 'asc' : 'desc';
        currentPage = 1;
        loadLoginHistory();
        
        // Update icon
        const icon = this.querySelector('svg');
        if (currentSort.direction === 'asc') {
            icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"/>';
        } else {
            icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8V20m0 0l4-4m-4 4l-4-4M7 4v12m0 0l-4-4m4 4l4-4"/>';
        }
    });

    // Export functionality
    document.getElementById('export-btn').addEventListener('click', function() {
        document.getElementById('export-modal').classList.remove('hidden');
    });

    document.getElementById('cancel-export').addEventListener('click', function() {
        document.getElementById('export-modal').classList.add('hidden');
    });

    document.getElementById('export-form').addEventListener('submit', function(e) {
        e.preventDefault();
        exportData();
    });

    // Refresh button
    document.getElementById('refresh-btn').addEventListener('click', function() {
        loadLoginHistory();
    });

    // Detail modal close
    document.getElementById('close-detail-modal').addEventListener('click', function() {
        document.getElementById('detail-modal').classList.add('hidden');
    });
}

function loadLoginHistory(page = 1) {
    showLoading(true);

    // Collect filter data
    const formData = new FormData(document.getElementById('filter-form'));
    const params = new URLSearchParams();

    for (let [key, value] of formData.entries()) {
        if (value) {
            params.append(key, value);
        }
    }

    params.append('page', page);
    params.append('per_page', 25);
    params.append('sort_by', currentSort.by);
    params.append('sort_direction', currentSort.direction);

    fetch(`{{ route('admin.login-histories.data') }}?${params.toString()}`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderTable(data.data);
            renderPagination(data.pagination);
            currentPage = page;
        } else {
            showError('Failed to load login history data');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('An error occurred while loading data');
    })
    .finally(() => {
        showLoading(false);
    });
}

function renderTable(data) {
    const tableBody = document.getElementById('login-history-table-body');

    if (data.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="px-6 py-12 text-center">
                    <div class="text-gray-500">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No login history found</h3>
                        <p class="mt-1 text-sm text-gray-500">Try adjusting your filters or check back later.</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    tableBody.innerHTML = data.map(history => {
        const loginDate = new Date(history.created_at);
        const statusBadge = history.login_successful
            ? `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                 <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                     <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                 </svg>
                 Success
               </span>`
            : `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                 <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                     <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                 </svg>
                 Failed
               </span>`;

        const riskBadge = getRiskBadge(history.risk_level);
        const riskScoreDisplay = history.risk_score !== null && history.risk_score !== undefined
            ? escapeHtml(history.risk_score.toString())
            : 'N/A';
        const flags = getFlags(history);

        // Sanitize all user-generated content
        const userName = escapeHtml(history.user?.name || 'Unknown');
        const userEmail = escapeHtml(history.user?.email || 'N/A');
        const deviceInfo = escapeHtml(history.device_info || 'Unknown');
        const ipAddress = escapeHtml(history.ip_address || 'N/A');
        const location = escapeHtml(history.formatted_location || 'Unknown');
        const uuid = escapeHtml(history.uuid);

        return `
            <tr class="hover:bg-gray-50">
                <td class="px-6 py-4">
                    <div class="text-sm font-medium text-gray-900">${userName}</div>
                    <div class="text-sm text-gray-500">${userEmail}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">${loginDate.toLocaleDateString()}</div>
                    <div class="text-sm text-gray-500">${loginDate.toLocaleTimeString()}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">${statusBadge}</td>
                <td class="px-6 py-4">
                    <div class="text-sm text-gray-900">${deviceInfo}</div>
                    <div class="text-sm text-gray-500">${ipAddress}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">${location}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    ${riskBadge}
                    <div class="text-xs text-gray-500 mt-1">Score: ${riskScoreDisplay}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex flex-wrap gap-1">${flags}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button onclick="viewDetails('${uuid}')" class="text-primary-600 hover:text-primary-900">
                        View Details
                    </button>
                </td>
            </tr>
        `;
    }).join('');
}

function getRiskBadge(riskLevel) {
    const badges = {
        'low': '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Low</span>',
        'medium': '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Medium</span>',
        'high': '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">High</span>',
        'critical': '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">Critical</span>'
    };
    return badges[riskLevel] || '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Unknown</span>';
}

function getFlags(history) {
    const flags = [];
    if (history.is_new_device) {
        flags.push('<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">New Device</span>');
    }
    if (history.is_new_location) {
        flags.push('<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">New Location</span>');
    }
    if (history.is_suspicious) {
        flags.push('<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">Suspicious</span>');
    }
    return flags.join('');
}

function renderPagination(pagination) {
    const container = document.getElementById('pagination-container');

    if (pagination.last_page <= 1) {
        container.innerHTML = '<div class="text-sm text-gray-700">Showing all results</div>';
        return;
    }

    let paginationHTML = '<nav class="flex items-center justify-between">';
    paginationHTML += `<div class="text-sm text-gray-700">
        Showing ${pagination.from || 0} to ${pagination.to || 0} of ${pagination.total} results
    </div>`;

    paginationHTML += '<div class="flex space-x-2">';

    // Previous button
    if (pagination.current_page > 1) {
        paginationHTML += `<button onclick="loadLoginHistory(${pagination.current_page - 1})"
            class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
            Previous
        </button>`;
    }

    // Page numbers
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.last_page, pagination.current_page + 2);

    for (let i = startPage; i <= endPage; i++) {
        const isActive = i === pagination.current_page;
        paginationHTML += `<button onclick="loadLoginHistory(${i})"
            class="px-3 py-2 text-sm font-medium ${isActive ? 'text-white bg-primary-600 border-primary-600' : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50'} border rounded-md">
            ${i}
        </button>`;
    }

    // Next button
    if (pagination.current_page < pagination.last_page) {
        paginationHTML += `<button onclick="loadLoginHistory(${pagination.current_page + 1})"
            class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
            Next
        </button>`;
    }

    paginationHTML += '</div></nav>';
    container.innerHTML = paginationHTML;
}

function viewDetails(uuid) {
    const detailUrlTemplate = `{{ route('admin.login-histories.show', ['uuid' => ':uuid']) }}`;
    const url = detailUrlTemplate.replace(':uuid', uuid);

    fetch(url, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderDetailModal(data.data);
            document.getElementById('detail-modal').classList.remove('hidden');
        } else {
            showError('Failed to load login details');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('An error occurred while loading details');
    });
}

function renderDetailModal(data) {
    const loginDate = new Date(data.created_at);
    const logoutDate = data.logout_at ? new Date(data.logout_at) : null;

    // Sanitize all user-generated content
    const userName = escapeHtml(data.user?.name || 'Unknown');
    const userEmail = escapeHtml(data.user?.email || 'N/A');
    const deviceType = escapeHtml(data.device_type || 'Unknown');
    const deviceInfo = escapeHtml(data.device_info || 'N/A');
    const ipAddress = escapeHtml(data.ip_address || 'N/A');
    const location = escapeHtml(data.formatted_location || 'Unknown');
    const riskLevel = escapeHtml(data.risk_level || 'Unknown');
    const riskScore = data.risk_score !== null && data.risk_score !== undefined
        ? escapeHtml(data.risk_score.toString())
        : 'N/A';
    const sessionDuration = escapeHtml(data.session_duration_human || 'N/A');
    const failureReason = escapeHtml(data.failure_reason || '');

    let content = `
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
                <h4 class="text-lg font-medium text-gray-900">Basic Information</h4>
                <div class="bg-gray-50 p-4 rounded-lg space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">User</label>
                        <p class="text-sm text-gray-900">${userName} (${userEmail})</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Login Time</label>
                        <p class="text-sm text-gray-900">${loginDate.toLocaleString()}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Status</label>
                        <p class="text-sm text-gray-900">${data.login_successful ? 'Successful' : 'Failed'}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Session Duration</label>
                        <p class="text-sm text-gray-900">${sessionDuration}</p>
                    </div>
                    ${logoutDate ? `
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Logout Time</label>
                        <p class="text-sm text-gray-900">${logoutDate.toLocaleString()}</p>
                    </div>
                    ` : ''}
                </div>
            </div>

            <div class="space-y-4">
                <h4 class="text-lg font-medium text-gray-900">Device & Location</h4>
                <div class="bg-gray-50 p-4 rounded-lg space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Device Type</label>
                        <p class="text-sm text-gray-900">${deviceType}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Device Info</label>
                        <p class="text-sm text-gray-900">${deviceInfo}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">IP Address</label>
                        <p class="text-sm text-gray-900">${ipAddress}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Location</label>
                        <p class="text-sm text-gray-900">${location}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-6 space-y-4">
            <h4 class="text-lg font-medium text-gray-900">Security Information</h4>
            <div class="bg-gray-50 p-4 rounded-lg">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Risk Level</label>
                        <p class="text-sm text-gray-900">${riskLevel}</p>
                    </div>
    `;

    if (accessLevel === 'full') {
        content += `
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Risk Score</label>
                        <p class="text-sm text-gray-900">${riskScore}</p>
                    </div>
        `;
    }

    content += `
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Flags</label>
                        <div class="flex flex-wrap gap-1 mt-1">
                            ${data.is_new_device ? '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">New Device</span>' : ''}
                            ${data.is_new_location ? '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">New Location</span>' : ''}
                            ${data.is_suspicious ? '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">Suspicious</span>' : ''}
                        </div>
                    </div>
                </div>
    `;

    if (!data.login_successful && failureReason) {
        content += `
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700">Failure Reason</label>
                    <p class="text-sm text-gray-900">${failureReason}</p>
                </div>
        `;
    }

    content += `
            </div>
        </div>
    `;

    document.getElementById('detail-content').innerHTML = content;
}

function exportData() {
    const format = document.getElementById('export-format').value;
    const formData = new FormData(document.getElementById('filter-form'));
    const params = new URLSearchParams();

    for (let [key, value] of formData.entries()) {
        if (value) {
            params.append(key, value);
        }
    }

    params.append('format', format);

    // Create a temporary link to download the file
    const link = document.createElement('a');
    link.href = `{{ route('admin.login-histories.export') }}?${params.toString()}`;
    link.download = `login_history_${new Date().toISOString().split('T')[0]}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Close modal
    document.getElementById('export-modal').classList.add('hidden');
}

function showLoading(show) {
    const indicator = document.getElementById('loading-indicator');
    if (show) {
        indicator.classList.remove('hidden');
    } else {
        indicator.classList.add('hidden');
    }
}

function showError(message) {
    // Simple alert for now - you can implement a toast system
    alert(message);
}

// Make functions globally available
window.loadLoginHistory = loadLoginHistory;
window.viewDetails = viewDetails;
</script>
@endsection
