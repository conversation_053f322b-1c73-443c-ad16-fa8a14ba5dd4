<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class CreateChatRoomRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Allow all for now, can add authorization logic later
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'type' => 'required|string|in:visitor,support,internal',
            'priority' => 'sometimes|integer|min:1|max:5',
            'language' => 'sometimes|string|max:10|in:en,af,zu,xh,fr,de,es,it,pt,nl',
            'title' => 'sometimes|string|max:255',
            'description' => 'sometimes|string|max:1000',
            'metadata' => 'sometimes|array',
            
            // Visitor info (for visitor type rooms)
            'visitor_info' => 'sometimes|array',
            'visitor_info.name' => 'required_if:type,visitor|string|max:255',
            'visitor_info.email' => 'required_if:type,visitor|email|max:255',
            'visitor_info.phone' => 'sometimes|string|max:50',
            'visitor_info.company' => 'sometimes|string|max:255',
            
            // Customer info (alternative naming)
            'customer_name' => 'sometimes|string|max:255',
            'customer_email' => 'sometimes|email|max:255',
            'customer_phone' => 'sometimes|string|max:50',
            'customer_company' => 'sometimes|string|max:255',
            
            // Department and assignment
            'department_id' => 'sometimes|integer|exists:departments,id',
            'assigned_to' => 'sometimes|integer|exists:users,id',
            
            // Additional fields
            'tags' => 'sometimes|array',
            'tags.*' => 'string|max:50',
            'source' => 'sometimes|string|max:100',
            'referrer' => 'sometimes|string|max:500',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'type.required' => 'Chat room type is required.',
            'type.in' => 'Chat room type must be one of: visitor, support, internal.',
            'visitor_info.name.required_if' => 'Visitor name is required for visitor chat rooms.',
            'visitor_info.email.required_if' => 'Visitor email is required for visitor chat rooms.',
            'visitor_info.email.email' => 'Please provide a valid email address.',
            'customer_email.email' => 'Please provide a valid customer email address.',
            'priority.min' => 'Priority must be at least 1.',
            'priority.max' => 'Priority cannot be greater than 5.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values only if not provided
        $defaults = [];

        if (!$this->has('priority')) {
            $defaults['priority'] = 1;
        }

        if (!$this->has('language')) {
            $defaults['language'] = 'en';
        }

        $defaults['status'] = 'active';

        $this->merge($defaults);

        // Handle customer_* fields by converting them to visitor_info
        if ($this->has('customer_name') || $this->has('customer_email')) {
            $visitorInfo = $this->visitor_info ?? [];
            
            if ($this->has('customer_name')) {
                $visitorInfo['name'] = $this->customer_name;
            }
            if ($this->has('customer_email')) {
                $visitorInfo['email'] = $this->customer_email;
            }
            if ($this->has('customer_phone')) {
                $visitorInfo['phone'] = $this->customer_phone;
            }
            if ($this->has('customer_company')) {
                $visitorInfo['company'] = $this->customer_company;
            }
            
            $this->merge(['visitor_info' => $visitorInfo]);
        }
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422)
        );
    }
}
