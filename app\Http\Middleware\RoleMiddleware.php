<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  ...$roles
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        if (!$request->user()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'UNAUTHENTICATED',
                        'message' => 'Authentication required.',
                        'timestamp' => now()->toISOString()
                    ]
                ], 401);
            }

            return redirect()->route('login');
        }

        $user = $request->user();

        // Check if user is active and not deleted
        if (!$user->is_active || $user->is_deleted) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'ACCOUNT_INACTIVE',
                        'message' => 'Your account is inactive.',
                        'timestamp' => now()->toISOString()
                    ]
                ], 403);
            }

            abort(403, 'Your account is inactive.');
        }

        // Check if user has required role
        if (!empty($roles) && (!$user->role || !in_array($user->role->name, $roles))) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'INSUFFICIENT_PERMISSIONS',
                        'message' => 'You do not have permission to access this resource.',
                        'timestamp' => now()->toISOString()
                    ]
                ], 403);
            }

            abort(403, 'You do not have permission to access this resource.');
        }

        return $next($request);
    }
}
