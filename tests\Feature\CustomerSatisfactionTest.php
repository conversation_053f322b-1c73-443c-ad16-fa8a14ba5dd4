<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\ChatRoom;
use App\Models\ChatRating;
use App\Models\ChatAssignment;
use App\Services\CustomerSatisfactionService;
use App\Services\ActivityLogger;
use App\Services\DashboardCacheService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use PHPUnit\Framework\Attributes\Test;
use Carbon\Carbon;

class CustomerSatisfactionTest extends TestCase
{
    use RefreshDatabase;

    protected CustomerSatisfactionService $satisfactionService;
    protected User $admin;
    protected User $staff;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles with unique slugs
        $adminRole = Role::factory()->create([
            'name' => 'admin',
            'slug' => 'admin-satisfaction'
        ]);
        $staffRole = Role::factory()->create([
            'name' => 'staff', 
            'slug' => 'staff-satisfaction'
        ]);
        $userRole = Role::factory()->create([
            'name' => 'user',
            'slug' => 'user-satisfaction'
        ]);

        // Create users
        $this->admin = User::factory()->create(['role_id' => $adminRole->id]);
        $this->staff = User::factory()->create(['role_id' => $staffRole->id]);
        $this->user = User::factory()->create(['role_id' => $userRole->id]);

        // Mock dependencies
        $this->mock(ActivityLogger::class, function ($mock) {
            $mock->shouldReceive('log')->andReturn(new \App\Models\ActivityLog());
        });

        $this->mock(DashboardCacheService::class, function ($mock) {
            $mock->shouldReceive('invalidatePattern')->andReturn(true);
            $mock->shouldReceive('remember')->andReturnUsing(function ($key, $callback, $ttl = null) {
                return $callback();
            });
        });

        // Create satisfaction service
        $this->satisfactionService = app(CustomerSatisfactionService::class);

        // Clear cache
        Cache::flush();
    }

    #[Test]
    public function admin_can_access_satisfaction_dashboard()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.chat.satisfaction.index'));

        $response->assertStatus(200);
        
        // Check that we get a successful response with the expected content
        $content = $response->getContent();
        $this->assertStringContainsString('Customer Satisfaction Dashboard', $content);
        $this->assertStringContainsString('Average Rating', $content);
        $this->assertStringContainsString('Satisfaction Rate', $content);
    }

    #[Test]
    public function staff_can_access_satisfaction_dashboard()
    {
        $response = $this->actingAs($this->staff)
            ->get(route('admin.chat.satisfaction.index'));

        $response->assertStatus(200);
    }

    #[Test]
    public function regular_user_cannot_access_satisfaction_dashboard()
    {
        $response = $this->actingAs($this->user)
            ->get(route('admin.chat.satisfaction.index'));

        $response->assertStatus(403);
    }

    #[Test]
    public function satisfaction_service_provides_comprehensive_metrics()
    {
        // Create test data
        $room = ChatRoom::factory()->create();
        $assignment = ChatAssignment::factory()->create([
            'chat_room_id' => $room->id,
            'assigned_to' => $this->staff->id,
        ]);
        
        ChatRating::create([
            'chat_room_id' => $room->id,
            'user_id' => $this->user->id,
            'rating' => 5,
            'comment' => 'Excellent service!',
        ]);

        $metrics = $this->satisfactionService->getSatisfactionMetrics();

        $this->assertArrayHasKey('overview', $metrics);
        $this->assertArrayHasKey('ratings_breakdown', $metrics);
        $this->assertArrayHasKey('satisfaction_trends', $metrics);
        $this->assertArrayHasKey('feedback_analysis', $metrics);
        $this->assertArrayHasKey('staff_performance', $metrics);
        $this->assertArrayHasKey('response_time_impact', $metrics);
        $this->assertArrayHasKey('resolution_satisfaction', $metrics);
        $this->assertArrayHasKey('nps_metrics', $metrics);
    }

    #[Test]
    public function satisfaction_api_returns_metrics_data()
    {
        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.satisfaction.api.v1.metrics.index'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'overview',
                'ratings_breakdown',
                'satisfaction_trends',
                'feedback_analysis',
                'staff_performance',
                'response_time_impact',
                'resolution_satisfaction',
                'nps_metrics',
            ],
            'filters',
        ]);
    }

    #[Test]
    public function satisfaction_api_returns_overview_data()
    {
        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.satisfaction.api.v1.metrics.overview'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'total_ratings',
                'average_rating',
                'satisfaction_rate',
                'dissatisfaction_rate',
                'response_rate',
                'satisfied_customers',
                'dissatisfied_customers',
                'total_conversations',
            ],
        ]);
    }

    #[Test]
    public function satisfaction_api_returns_ratings_breakdown()
    {
        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.satisfaction.api.v1.metrics.ratings'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'distribution',
                'percentage_distribution',
                'total_ratings',
            ],
        ]);
    }

    #[Test]
    public function satisfaction_api_returns_trends_data()
    {
        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.satisfaction.api.v1.metrics.trends'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'daily_trends',
                'trend_direction',
            ],
        ]);
    }

    #[Test]
    public function satisfaction_api_returns_feedback_analysis()
    {
        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.satisfaction.api.v1.analytics.feedback'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'total_feedback_count',
                'feedback_by_rating',
                'common_keywords',
                'feedback_rate',
            ],
        ]);
    }

    #[Test]
    public function satisfaction_api_returns_staff_performance()
    {
        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.satisfaction.api.v1.performance.staff'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data',
        ]);
    }

    #[Test]
    public function satisfaction_api_returns_nps_metrics()
    {
        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.satisfaction.api.v1.metrics.nps'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'nps_score',
                'promoters',
                'passives',
                'detractors',
                'promoter_percentage',
                'detractor_percentage',
                'total_ratings',
            ],
        ]);
    }

    #[Test]
    public function satisfaction_api_returns_survey_questions()
    {
        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.satisfaction.api.v1.survey.questions'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                '*' => [
                    'id',
                    'type',
                    'question',
                    'required',
                ],
            ],
        ]);
    }

    #[Test]
    public function user_can_submit_rating()
    {
        $room = ChatRoom::factory()->create();

        $response = $this->actingAs($this->admin)
            ->postJson(route('admin.chat.satisfaction.api.v1.ratings.store'), [
                'chat_room_id' => $room->id,
                'rating' => 5,
                'comment' => 'Great service!',
            ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data',
            'message',
        ]);

        $this->assertDatabaseHas('chat_ratings', [
            'chat_room_id' => $room->id,
            'user_id' => $this->admin->id,
            'rating' => 5,
            'feedback' => 'Great service!',
        ]);
    }

    #[Test]
    public function rating_submission_validates_required_fields()
    {
        $response = $this->actingAs($this->admin)
            ->postJson(route('admin.chat.satisfaction.api.v1.ratings.store'), [
                'rating' => 6, // Invalid rating
            ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['chat_room_id', 'rating']);
    }

    #[Test]
    public function satisfaction_export_returns_report_data()
    {
        $response = $this->actingAs($this->admin)
            ->postJson(route('admin.chat.satisfaction.api.v1.reports.export'), [
                'format' => 'json',
                'start_date' => now()->subDays(7)->format('Y-m-d'),
                'end_date' => now()->format('Y-m-d'),
            ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'report_generated_at',
                'filters_applied',
                'format',
                'data',
            ],
        ]);
    }

    #[Test]
    public function satisfaction_service_calculates_metrics_correctly()
    {
        // Create test data with known ratings
        $room1 = ChatRoom::factory()->create();
        $room2 = ChatRoom::factory()->create();
        $room3 = ChatRoom::factory()->create();

        // Create assignments
        ChatAssignment::factory()->create(['chat_room_id' => $room1->id, 'assigned_to' => $this->staff->id]);
        ChatAssignment::factory()->create(['chat_room_id' => $room2->id, 'assigned_to' => $this->staff->id]);
        ChatAssignment::factory()->create(['chat_room_id' => $room3->id, 'assigned_to' => $this->staff->id]);

        // Create ratings: 5, 4, 2 (average = 3.67, satisfaction rate = 66.67%)
        ChatRating::create(['chat_room_id' => $room1->id, 'user_id' => $this->user->id, 'rating' => 5, 'comment' => 'Excellent!']);
        ChatRating::create(['chat_room_id' => $room2->id, 'user_id' => $this->user->id, 'rating' => 4, 'comment' => 'Good service']);
        ChatRating::create(['chat_room_id' => $room3->id, 'user_id' => $this->user->id, 'rating' => 2, 'comment' => 'Poor experience']);

        $metrics = $this->satisfactionService->getSatisfactionMetrics();

        $this->assertEquals(3, $metrics['overview']['total_ratings']);
        $this->assertEquals(3.67, round($metrics['overview']['average_rating'], 2));
        $this->assertEquals(66.67, round($metrics['overview']['satisfaction_rate'], 2));
        $this->assertEquals(33.33, round($metrics['overview']['dissatisfaction_rate'], 2));
    }

    #[Test]
    public function satisfaction_service_handles_filters_correctly()
    {
        // Create test data with different staff members
        $staff2 = User::factory()->create(['role_id' => $this->staff->role_id]);
        
        $room1 = ChatRoom::factory()->create();
        $room2 = ChatRoom::factory()->create();

        ChatAssignment::factory()->create(['chat_room_id' => $room1->id, 'assigned_to' => $this->staff->id]);
        ChatAssignment::factory()->create(['chat_room_id' => $room2->id, 'assigned_to' => $staff2->id]);

        ChatRating::create(['chat_room_id' => $room1->id, 'user_id' => $this->user->id, 'rating' => 5]);
        ChatRating::create(['chat_room_id' => $room2->id, 'user_id' => $this->user->id, 'rating' => 3]);

        // Test with staff filter
        $metricsWithStaffFilter = $this->satisfactionService->getSatisfactionMetrics([
            'staff_id' => $this->staff->id,
            'start_date' => now()->subDay(),
            'end_date' => now(),
        ]);

        $this->assertEquals(1, $metricsWithStaffFilter['overview']['total_ratings']);
        $this->assertEquals(5.0, $metricsWithStaffFilter['overview']['average_rating']);
    }

    #[Test]
    public function satisfaction_service_calculates_nps_correctly()
    {
        // Create test data for NPS calculation
        $rooms = ChatRoom::factory()->count(10)->create();
        
        foreach ($rooms as $index => $room) {
            ChatAssignment::factory()->create(['chat_room_id' => $room->id, 'assigned_to' => $this->staff->id]);
            
            // Create ratings: 2 promoters (5), 3 passives (4), 5 detractors (1-3)
            $rating = match($index) {
                0, 1 => 5,      // Promoters
                2, 3, 4 => 4,   // Passives  
                default => 2,   // Detractors
            };
            
            ChatRating::create([
                'chat_room_id' => $room->id,
                'user_id' => $this->user->id,
                'rating' => $rating,
            ]);
        }

        $metrics = $this->satisfactionService->getSatisfactionMetrics();
        $nps = $metrics['nps_metrics'];

        $this->assertEquals(2, $nps['promoters']);
        $this->assertEquals(3, $nps['passives']);
        $this->assertEquals(5, $nps['detractors']);
        $this->assertEquals(-30.0, $nps['nps_score']); // 20% - 50% = -30%
    }

    #[Test]
    public function unauthorized_users_cannot_access_satisfaction_api()
    {
        $response = $this->getJson(route('admin.chat.satisfaction.api.v1.metrics.overview'));
        $response->assertStatus(401);

        $response = $this->actingAs($this->user)
            ->getJson(route('admin.chat.satisfaction.api.v1.metrics.overview'));
        $response->assertStatus(403);
    }
}
