<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class Client extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'company',
        'description',
        'logo_path',
        'project_images',
        'website_url',
        'industry',
        'project_start_date',
        'project_end_date',
        'project_status',
        'testimonial',
        'rating',
        'is_featured',
        'is_active',
        'sort_order',
        'is_deleted',
    ];

    protected $casts = [
        'project_images' => 'array',
        'project_start_date' => 'date',
        'project_end_date' => 'date',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'is_deleted' => 'boolean',
        'rating' => 'integer',
        'sort_order' => 'integer',
    ];



    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('is_deleted', false);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('created_at', 'desc');
    }

    // Accessors
    public function getLogoUrlAttribute()
    {
        if ($this->logo_path) {
            return Storage::url($this->logo_path);
        }
        return asset('images/default-client-logo.png');
    }

    public function getProjectImageUrlsAttribute()
    {
        if ($this->project_images && is_array($this->project_images)) {
            return array_map(function ($path) {
                return Storage::url($path);
            }, $this->project_images);
        }
        return [];
    }

    public function getProjectDurationAttribute()
    {
        if ($this->project_start_date && $this->project_end_date) {
            return $this->project_start_date->diffInDays($this->project_end_date) + 1;
        }
        return null;
    }

    public function getStatusBadgeAttribute()
    {
        $badges = [
            'completed' => 'success',
            'ongoing' => 'warning',
            'paused' => 'danger',
        ];

        return $badges[$this->project_status] ?? 'secondary';
    }

    // Mutators
    public function setProjectImagesAttribute($value)
    {
        if (is_string($value)) {
            $this->attributes['project_images'] = json_encode(explode(',', $value));
        } elseif (is_array($value)) {
            $this->attributes['project_images'] = json_encode($value);
        } else {
            $this->attributes['project_images'] = null;
        }
    }

    // Helper methods
    public function toggleFeatured()
    {
        $this->is_featured = !$this->is_featured;
        return $this->save();
    }

    public function toggleActive()
    {
        $this->is_active = !$this->is_active;
        return $this->save();
    }

    public function softDelete()
    {
        $this->is_deleted = true;
        return $this->save();
    }

    public function restore()
    {
        $this->is_deleted = false;
        return $this->save();
    }
}
