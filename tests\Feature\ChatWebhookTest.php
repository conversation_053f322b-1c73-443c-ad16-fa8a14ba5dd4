<?php

namespace Tests\Feature;

use App\Models\ChatWebhook;
use App\Models\ChatWebhookDelivery;
use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\User;
use App\Models\Role;
use App\Services\ChatWebhookService;
use App\Events\Chat\WebhookEvent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class ChatWebhookTest extends TestCase
{
    use RefreshDatabase;

    protected ChatWebhookService $webhookService;
    protected User $admin;

    protected function setUp(): void
    {
        parent::setUp();

        $this->webhookService = app(ChatWebhookService::class);

        // Create admin user
        $adminRole = Role::factory()->create(['name' => 'admin', 'slug' => 'admin']);
        $this->admin = User::factory()->create(['role_id' => $adminRole->id]);
    }

    /** @test */
    public function it_can_create_a_webhook()
    {
        $webhookData = [
            'name' => 'Test Webhook',
            'url' => 'https://example.com/webhook',
            'events' => ['message.sent', 'room.created'],
            'secret' => 'test-secret',
            'is_active' => true,
        ];

        $webhook = ChatWebhook::create($webhookData);

        $this->assertDatabaseHas('chat_webhooks', [
            'name' => 'Test Webhook',
            'url' => 'https://example.com/webhook',
            'is_active' => true,
        ]);

        $this->assertEquals(['message.sent', 'room.created'], $webhook->events);
        $this->assertTrue($webhook->shouldReceiveEvent('message.sent'));
        $this->assertFalse($webhook->shouldReceiveEvent('room.closed'));
    }

    /** @test */
    public function it_generates_hmac_signature_correctly()
    {
        $webhook = ChatWebhook::factory()->create([
            'secret' => 'test-secret'
        ]);

        $payload = '{"test": "data"}';
        $signature = $webhook->generateSignature($payload);

        $expectedSignature = 'sha256=' . hash_hmac('sha256', $payload, 'test-secret');
        $this->assertEquals($expectedSignature, $signature);
    }

    /** @test */
    public function it_triggers_webhook_events()
    {
        Queue::fake();

        $webhook = ChatWebhook::factory()->create([
            'events' => ['message.sent'],
            'is_active' => true,
        ]);

        $eventData = [
            'message_id' => 123,
            'content' => 'Test message',
        ];

        $this->webhookService->triggerEvent('message.sent', $eventData);

        $this->assertDatabaseHas('chat_webhook_deliveries', [
            'chat_webhook_id' => $webhook->id,
            'event_type' => 'message.sent',
            'status' => 'pending',
        ]);
    }

    /** @test */
    public function it_delivers_webhook_successfully()
    {
        Http::fake([
            'https://example.com/webhook' => Http::response(['success' => true], 200)
        ]);

        $webhook = ChatWebhook::factory()->create([
            'url' => 'https://example.com/webhook',
            'secret' => 'test-secret',
        ]);

        $delivery = ChatWebhookDelivery::factory()->create([
            'chat_webhook_id' => $webhook->id,
            'event_type' => 'message.sent',
            'payload' => ['test' => 'data'],
            'status' => 'pending',
        ]);

        $result = $this->webhookService->deliverWebhook($delivery);

        $this->assertTrue($result);
        $delivery->refresh();
        $this->assertEquals('delivered', $delivery->status);
        $this->assertEquals(200, $delivery->response_status);
    }

    /** @test */
    public function it_handles_webhook_delivery_failure()
    {
        Http::fake([
            'https://example.com/webhook' => Http::response(['error' => 'Bad request'], 400)
        ]);

        $webhook = ChatWebhook::factory()->create([
            'url' => 'https://example.com/webhook',
            'max_retries' => 3,
        ]);

        $delivery = ChatWebhookDelivery::factory()->create([
            'chat_webhook_id' => $webhook->id,
            'status' => 'pending',
        ]);

        $result = $this->webhookService->deliverWebhook($delivery);

        $this->assertFalse($result);
        $delivery->refresh();
        $this->assertEquals('retrying', $delivery->status);
        $this->assertEquals(1, $delivery->attempts);
        $this->assertEquals(400, $delivery->response_status);
    }

    /** @test */
    public function it_calculates_delivery_statistics()
    {
        $webhook = ChatWebhook::factory()->create();

        // Create some deliveries
        ChatWebhookDelivery::factory()->create([
            'chat_webhook_id' => $webhook->id,
            'status' => 'delivered',
        ]);
        ChatWebhookDelivery::factory()->create([
            'chat_webhook_id' => $webhook->id,
            'status' => 'delivered',
        ]);
        ChatWebhookDelivery::factory()->create([
            'chat_webhook_id' => $webhook->id,
            'status' => 'failed',
        ]);

        $stats = $webhook->getDeliveryStats();

        $this->assertEquals(3, $stats['total']);
        $this->assertEquals(2, $stats['delivered']);
        $this->assertEquals(1, $stats['failed']);
        $this->assertEquals(66.67, $stats['success_rate']);
    }

    /** @test */
    public function it_tests_webhook_endpoint()
    {
        Http::fake([
            'https://example.com/webhook' => Http::response(['success' => true], 200)
        ]);

        $webhook = ChatWebhook::factory()->create([
            'url' => 'https://example.com/webhook',
        ]);

        $result = $this->webhookService->testWebhook($webhook);

        $this->assertTrue($result['success']);
        $this->assertEquals(200, $result['status_code']);
    }

    /** @test */
    public function admin_can_manage_webhooks()
    {
        $this->actingAs($this->admin);

        // Test creating webhook
        $response = $this->postJson(route('admin.chat.webhooks.store'), [
            'name' => 'Test Webhook',
            'url' => 'https://example.com/webhook',
            'events' => ['message.sent'],
            'max_retries' => 3,
            'timeout_seconds' => 30,
        ]);

        $response->assertStatus(201)
            ->assertJsonFragment(['success' => true]);

        $webhook = ChatWebhook::first();

        // Test updating webhook
        $response = $this->putJson(route('admin.chat.webhooks.update', $webhook), [
            'name' => 'Updated Webhook',
            'url' => 'https://example.com/updated-webhook',
            'events' => ['message.sent', 'room.created'],
            'max_retries' => 5,
            'timeout_seconds' => 60,
            'is_active' => false,
        ]);

        $response->assertStatus(200)
            ->assertJsonFragment(['success' => true]);

        $webhook->refresh();
        $this->assertEquals('Updated Webhook', $webhook->name);
        $this->assertFalse($webhook->is_active);

        // Test deleting webhook
        $response = $this->deleteJson(route('admin.chat.webhooks.destroy', $webhook));

        $response->assertStatus(200)
            ->assertJsonFragment(['success' => true]);

        $this->assertDatabaseMissing('chat_webhooks', ['id' => $webhook->id]);
    }

    /** @test */
    public function webhook_events_are_triggered_on_chat_actions()
    {
        Queue::fake();

        $webhook = ChatWebhook::factory()->create([
            'events' => ['room.created', 'message.sent'],
            'is_active' => true,
        ]);

        // Test room creation event
        event(new WebhookEvent('room.created', [
            'room_id' => 123,
            'type' => 'visitor',
        ]));

        // Test message sent event
        event(new WebhookEvent('message.sent', [
            'message_id' => 456,
            'content' => 'Test message',
        ]));

        $this->assertDatabaseHas('chat_webhook_deliveries', [
            'chat_webhook_id' => $webhook->id,
            'event_type' => 'room.created',
        ]);

        $this->assertDatabaseHas('chat_webhook_deliveries', [
            'chat_webhook_id' => $webhook->id,
            'event_type' => 'message.sent',
        ]);
    }
}
