<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\LoginHistory;
use App\Models\User;
use App\Services\LoginHistoryService;
use App\Services\ActivityLogger;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class LoginHistoryController extends Controller
{
    protected LoginHistoryService $loginHistoryService;
    protected ActivityLogger $activityLogger;

    public function __construct(LoginHistoryService $loginHistoryService, ActivityLogger $activityLogger)
    {
        $this->loginHistoryService = $loginHistoryService;
        $this->activityLogger = $activityLogger;
        
        $this->middleware('auth');
        $this->middleware('check.login.history.permission:view_basic_info,partial');
    }

    /**
     * Display the admin login history management page.
     */
    public function index(): View
    {
        $user = Auth::user();
        
        // Log the access - temporarily disabled for testing
        // $this->activityLogger->log(
        //     'admin_login_history_access',
        //     $user,
        //     [
        //         'description' => 'Admin accessed login history management',
        //         'status' => 'success'
        //     ]
        // );

        // Get permission details
        $permission = $user->isAdmin() ? null : $user->loginHistoryPermission;
        $accessLevel = $user->isAdmin() ? 'full' : ($permission ? $permission->access_level : 'none');

        // Get basic statistics
        $totalLogins = LoginHistory::count();
        $successfulLogins = LoginHistory::where('login_status', 'success')->count();
        $failedLogins = LoginHistory::where('login_status', 'failed')->count();
        $suspiciousLogins = LoginHistory::where('is_suspicious', true)->count();
        
        $stats = [
            'total_logins' => $totalLogins,
            'successful_logins' => $successfulLogins,
            'failed_logins' => $failedLogins,
            'suspicious_logins' => $suspiciousLogins,
            'success_rate' => $totalLogins > 0 ? round(($successfulLogins / $totalLogins) * 100, 1) : 0,
        ];

        // Get users for filter dropdown
        $users = User::select('id', 'first_name', 'last_name', 'email')
            ->orderBy('first_name')
            ->orderBy('last_name')
            ->get();

        return view('admin.login-histories.index', compact('stats', 'users', 'accessLevel'));
    }

    /**
     * Get login history data for AJAX requests.
     */
    public function data(Request $request): JsonResponse
    {
        $user = Auth::user();
        $permission = $user->isAdmin() ? null : $user->loginHistoryPermission;
        $accessLevel = $user->isAdmin() ? 'full' : ($permission ? $permission->access_level : 'none');

        $query = LoginHistory::with('user:id,first_name,last_name,email')
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->has('user_id') && !empty($request->user_id)) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->has('status') && $request->status !== 'all') {
            if ($request->status === 'success') {
                $query->where('login_status', 'success');
            } elseif ($request->status === 'failed') {
                $query->where('login_status', 'failed');
            }
        }

        if ($request->has('device_type') && $request->device_type !== 'all') {
            $query->where('device_type', $request->device_type);
        }

        if ($request->has('risk_level') && $request->risk_level !== 'all') {
            switch ($request->risk_level) {
                case 'critical':
                    $query->where('risk_score', '>=', 80);
                    break;
                case 'high':
                    $query->whereBetween('risk_score', [60, 79]);
                    break;
                case 'medium':
                    $query->whereBetween('risk_score', [40, 59]);
                    break;
                case 'low':
                    $query->where('risk_score', '<', 40);
                    break;
            }
        }

        if ($request->has('location') && !empty($request->location)) {
            $query->where('location', 'like', '%' . $request->location . '%');
        }

        if ($request->has('ip_address') && !empty($request->ip_address)) {
            $query->where('ip_address', 'like', '%' . $request->ip_address . '%');
        }

        if ($request->has('suspicious_only') && $request->boolean('suspicious_only')) {
            $query->where('is_suspicious', true);
        }

        // Date range filter
        if ($request->has('date_range')) {
            switch ($request->date_range) {
                case 'today':
                    $query->whereDate('created_at', today());
                    break;
                case 'this_week':
                    $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
                    break;
                case 'this_year':
                    $query->whereYear('created_at', now()->year);
                    break;
                case '7_days':
                    $query->where('created_at', '>=', now()->subDays(7));
                    break;
                case 'custom':
                    if ($request->has('start_date') && $request->has('end_date')) {
                        $query->whereBetween('created_at', [
                            $request->start_date . ' 00:00:00',
                            $request->end_date . ' 23:59:59'
                        ]);
                    }
                    break;
            }
        }

        // Apply sorting
        if ($request->has('sort_by')) {
            $sortDirection = $request->get('sort_direction', 'desc');
            switch ($request->sort_by) {
                case 'date':
                    $query->orderBy('created_at', $sortDirection);
                    break;
                case 'user':
                    $query->join('users', 'login_histories.user_id', '=', 'users.id')
                          ->orderBy('users.first_name', $sortDirection)
                          ->orderBy('users.last_name', $sortDirection)
                          ->select('login_histories.*');
                    break;
                case 'risk_score':
                    $query->orderBy('risk_score', $sortDirection);
                    break;
                case 'location':
                    $query->orderBy('location', $sortDirection);
                    break;
            }
        }

        $loginHistories = $query->paginate($request->get('per_page', 25));

        // Filter data based on permission level
        $data = $loginHistories->getCollection()->map(function ($history) use ($accessLevel) {
            $item = [
                'uuid' => $history->uuid,
                'user' => [
                    'id' => $history->user->id,
                    'name' => $history->user->first_name . ' ' . $history->user->last_name,
                    'email' => $history->user->email,
                ],
                'created_at' => $history->created_at->toISOString(),
                'login_status' => $history->login_status,
                'device_type' => $history->device_type,
                'device_info' => $history->device_info,
                'formatted_location' => $history->formatted_location,
                'session_duration_human' => $history->session_duration_human,
                'logout_at' => $history->logout_at?->toISOString(),
                'is_new_device' => $history->is_new_device,
                'is_new_location' => $history->is_new_location,
                'is_suspicious' => $history->is_suspicious,
                'risk_level' => $history->risk_level,
            ];

            // Add sensitive data only for full access
            if ($accessLevel === 'full') {
                $item['ip_address'] = $history->ip_address;
                $item['risk_score'] = $history->risk_score;
                $item['failure_reason'] = $history->failure_reason;
                $item['security_metadata'] = $history->security_metadata;
            } else {
                // Mask IP for partial access
                $item['ip_address'] = $this->maskIpAddress($history->ip_address);
            }

            return $item;
        });

        // Log the data access
        $this->activityLogger->log(
            'admin_login_history_data_access',
            $user,
            [
                'description' => 'Admin accessed login history data',
                'status' => 'success',
                'filters' => $request->only(['user_id', 'status', 'device_type', 'date_range']),
                'access_level' => $accessLevel,
                'results_count' => $data->count()
            ]
        );

        return response()->json([
            'success' => true,
            'data' => $data,
            'pagination' => [
                'current_page' => $loginHistories->currentPage(),
                'last_page' => $loginHistories->lastPage(),
                'per_page' => $loginHistories->perPage(),
                'total' => $loginHistories->total(),
                'from' => $loginHistories->firstItem(),
                'to' => $loginHistories->lastItem(),
            ],
            'access_level' => $accessLevel
        ]);
    }

    /**
     * Show detailed information about a specific login.
     */
    public function show(Request $request, string $uuid): JsonResponse
    {
        $user = Auth::user();
        $permission = $user->isAdmin() ? null : $user->loginHistoryPermission;
        $accessLevel = $user->isAdmin() ? 'full' : ($permission ? $permission->access_level : 'none');

        $loginHistory = LoginHistory::with('user:id,first_name,last_name,email')
            ->where('uuid', $uuid)
            ->firstOrFail();

        $data = [
            'uuid' => $loginHistory->uuid,
            'user' => [
                'id' => $loginHistory->user->id,
                'name' => $loginHistory->user->first_name . ' ' . $loginHistory->user->last_name,
                'email' => $loginHistory->user->email,
            ],
            'created_at' => $loginHistory->created_at->toISOString(),
            'login_status' => $loginHistory->login_status,
            'device_type' => $loginHistory->device_type,
            'device_info' => $loginHistory->device_info,
            'formatted_location' => $loginHistory->formatted_location,
            'session_duration_human' => $loginHistory->session_duration_human,
            'logout_at' => $loginHistory->logout_at?->toISOString(),
            'is_new_device' => $loginHistory->is_new_device,
            'is_new_location' => $loginHistory->is_new_location,
            'is_suspicious' => $loginHistory->is_suspicious,
            'risk_level' => $loginHistory->risk_level,
        ];

        // Add sensitive data only for full access
        if ($accessLevel === 'full') {
            $data['ip_address'] = $loginHistory->ip_address;
            $data['risk_score'] = $loginHistory->risk_score;
            $data['failure_reason'] = $loginHistory->failure_reason;
            $data['security_metadata'] = $loginHistory->security_metadata;
            $data['request_metadata'] = $loginHistory->request_metadata;
        } else {
            $data['ip_address'] = $this->maskIpAddress($loginHistory->ip_address);
        }

        // Log the detailed view access
        $this->activityLogger->log(
            'admin_login_history_detail_access',
            $user,
            [
                'description' => 'Admin viewed login history details',
                'status' => 'success',
                'login_history_uuid' => $uuid,
                'target_user_id' => $loginHistory->user_id,
                'access_level' => $accessLevel
            ]
        );

        return response()->json([
            'success' => true,
            'data' => $data,
            'access_level' => $accessLevel
        ]);
    }

    /**
     * Mask IP address for partial access users.
     */
    private function maskIpAddress(?string $ipAddress): ?string
    {
        if (!$ipAddress) {
            return null;
        }

        // For IPv4, mask the last octet
        if (filter_var($ipAddress, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            $parts = explode('.', $ipAddress);
            $parts[3] = 'xxx';
            return implode('.', $parts);
        }

        // For IPv6, mask the last 4 groups
        if (filter_var($ipAddress, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
            $parts = explode(':', $ipAddress);
            for ($i = max(0, count($parts) - 4); $i < count($parts); $i++) {
                $parts[$i] = 'xxxx';
            }
            return implode(':', $parts);
        }

        return 'xxx.xxx.xxx.xxx';
    }

    /**
     * Get statistics for the admin dashboard.
     */
    public function statistics(Request $request): JsonResponse
    {
        $user = Auth::user();
        $days = min((int) $request->get('days', 30), 365);

        // Basic statistics
        $totalLogins = LoginHistory::where('created_at', '>=', now()->subDays($days))->count();
        $successfulLogins = LoginHistory::where('created_at', '>=', now()->subDays($days))
            ->where('login_status', 'success')->count();
        $failedLogins = LoginHistory::where('created_at', '>=', now()->subDays($days))
            ->where('login_status', 'failed')->count();
        $suspiciousLogins = LoginHistory::where('created_at', '>=', now()->subDays($days))
            ->where('is_suspicious', true)->count();

        // Top locations
        $topLocations = LoginHistory::where('created_at', '>=', now()->subDays($days))
            ->whereNotNull('location')
            ->selectRaw('location, COUNT(*) as count')
            ->groupBy('location')
            ->orderByDesc('count')
            ->limit(10)
            ->get();

        // Device type breakdown
        $deviceTypes = LoginHistory::where('created_at', '>=', now()->subDays($days))
            ->selectRaw('device_type, COUNT(*) as count')
            ->groupBy('device_type')
            ->get();

        // Risk level distribution
        $riskLevels = LoginHistory::where('created_at', '>=', now()->subDays($days))
            ->selectRaw('
                CASE
                    WHEN risk_score >= 80 THEN "critical"
                    WHEN risk_score >= 60 THEN "high"
                    WHEN risk_score >= 40 THEN "medium"
                    ELSE "low"
                END as risk_level,
                COUNT(*) as count
            ')
            ->groupBy('risk_level')
            ->get();

        // Daily login trends
        $dailyTrends = LoginHistory::where('created_at', '>=', now()->subDays($days))
            ->selectRaw('DATE(created_at) as date, COUNT(*) as total, SUM(CASE WHEN login_status = "success" THEN 1 ELSE 0 END) as successful')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $stats = [
            'total_logins' => $totalLogins,
            'successful_logins' => $successfulLogins,
            'failed_logins' => $failedLogins,
            'suspicious_logins' => $suspiciousLogins,
            'success_rate' => $totalLogins > 0 ? round(($successfulLogins / $totalLogins) * 100, 1) : 0,
            'top_locations' => $topLocations,
            'device_types' => $deviceTypes,
            'risk_levels' => $riskLevels,
            'daily_trends' => $dailyTrends,
            'period_days' => $days,
        ];

        // Log statistics access
        $this->activityLogger->log(
            'admin_login_history_statistics_access',
            $user,
            [
                'description' => 'Admin accessed login history statistics',
                'status' => 'success',
                'period_days' => $days
            ]
        );

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Export login history data.
     */
    public function export(Request $request): StreamedResponse|JsonResponse
    {
        $user = Auth::user();
        $permission = $user->isAdmin() ? null : $user->loginHistoryPermission;
        $accessLevel = $user->isAdmin() ? 'full' : ($permission ? $permission->access_level : 'none');

        // Check if user has export permission
        if ($accessLevel !== 'full') {
            abort(403, 'You do not have permission to export login history data.');
        }

        $format = $request->get('format', 'csv');
        $query = LoginHistory::with('user:id,first_name,last_name,email')
            ->orderBy('created_at', 'desc');

        // Apply same filters as data method
        if ($request->has('user_id') && !empty($request->user_id)) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->has('status') && $request->status !== 'all') {
            if ($request->status === 'success') {
                $query->where('login_status', 'success');
            } elseif ($request->status === 'failed') {
                $query->where('login_status', 'failed');
            }
        }

        if ($request->has('date_range')) {
            switch ($request->date_range) {
                case 'today':
                    $query->whereDate('created_at', today());
                    break;
                case 'this_week':
                    $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
                    break;
                case 'this_year':
                    $query->whereYear('created_at', now()->year);
                    break;
                case '7_days':
                    $query->where('created_at', '>=', now()->subDays(7));
                    break;
                case 'custom':
                    if ($request->has('start_date') && $request->has('end_date')) {
                        $query->whereBetween('created_at', [
                            $request->start_date . ' 00:00:00',
                            $request->end_date . ' 23:59:59'
                        ]);
                    }
                    break;
            }
        }

        $loginHistories = $query->get();

        // Log the export
        $this->activityLogger->log(
            'admin_login_history_export',
            $user,
            [
                'description' => 'Admin exported login history data',
                'status' => 'success',
                'format' => $format,
                'records_count' => $loginHistories->count(),
                'filters' => $request->only(['user_id', 'status', 'date_range'])
            ]
        );

        if ($format === 'json') {
            return $this->exportAsJson($loginHistories);
        } else {
            return $this->exportAsCsv($loginHistories);
        }
    }

    /**
     * Export data as CSV.
     */
    private function exportAsCsv($loginHistories): StreamedResponse
    {
        $filename = 'login_history_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($loginHistories) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Date/Time',
                'User Name',
                'User Email',
                'Status',
                'IP Address',
                'Device Type',
                'Device Info',
                'Location',
                'Risk Level',
                'Risk Score',
                'Session Duration',
                'New Device',
                'New Location',
                'Suspicious',
                'Failure Reason'
            ]);

            foreach ($loginHistories as $history) {
                fputcsv($file, [
                    $history->created_at->format('Y-m-d H:i:s'),
                    $history->user->first_name . ' ' . $history->user->last_name,
                    $history->user->email,
                    $history->login_status,
                    $history->ip_address,
                    $history->device_type,
                    $history->device_info,
                    $history->formatted_location,
                    $history->risk_level,
                    $history->risk_score,
                    $history->session_duration_human,
                    $history->is_new_device ? 'Yes' : 'No',
                    $history->is_new_location ? 'Yes' : 'No',
                    $history->is_suspicious ? 'Yes' : 'No',
                    $history->failure_reason
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export data as JSON.
     */
    private function exportAsJson($loginHistories): JsonResponse
    {
        $filename = 'login_history_' . now()->format('Y-m-d_H-i-s') . '.json';

        $data = $loginHistories->map(function ($history) {
            return [
                'uuid' => $history->uuid,
                'user' => [
                    'id' => $history->user->id,
                    'name' => $history->user->first_name . ' ' . $history->user->last_name,
                    'email' => $history->user->email,
                ],
                'created_at' => $history->created_at->toISOString(),
                'login_status' => $history->login_status,
                'ip_address' => $history->ip_address,
                'device_type' => $history->device_type,
                'device_info' => $history->device_info,
                'formatted_location' => $history->formatted_location,
                'risk_level' => $history->risk_level,
                'risk_score' => $history->risk_score,
                'session_duration_human' => $history->session_duration_human,
                'logout_at' => $history->logout_at?->toISOString(),
                'is_new_device' => $history->is_new_device,
                'is_new_location' => $history->is_new_location,
                'is_suspicious' => $history->is_suspicious,
                'failure_reason' => $history->failure_reason,
                'security_metadata' => $history->security_metadata,
                'request_metadata' => $history->request_metadata,
            ];
        });

        $headers = [
            'Content-Type' => 'application/json',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        return response()->json([
            'export_info' => [
                'generated_at' => now()->toISOString(),
                'total_records' => $data->count(),
                'format' => 'json'
            ],
            'data' => $data
        ], 200, $headers);
    }
}
