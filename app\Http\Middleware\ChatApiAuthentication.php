<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use App\Models\User;

class ChatApiAuthentication
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Try multiple authentication methods
        $user = $this->authenticateRequest($request);
        
        if ($user) {
            Auth::setUser($user);
            
            // Track API usage
            $this->trackApiUsage($request, $user);
            
            return $next($request);
        }

        // For anonymous visitors, allow certain endpoints
        if ($this->isAnonymousAllowed($request)) {
            return $next($request);
        }

        return response()->json([
            'success' => false,
            'message' => 'Authentication required',
            'error' => 'Please provide valid authentication credentials',
        ], 401);
    }

    /**
     * Authenticate request using multiple methods.
     */
    protected function authenticateRequest(Request $request): ?User
    {
        // 0. Check if user is already authenticated (for tests with Sanctum::actingAs)
        if (Auth::check()) {
            return Auth::user();
        }

        // 1. Bearer Token (for web applications)
        if ($token = $request->bearerToken()) {
            return $this->validateBearerToken($token);
        }

        // 2. API Key (for server-to-server)
        if ($apiKey = $request->header('X-API-Key')) {
            return $this->validateApiKey($apiKey);
        }

        // 3. Session (for same-domain integration)
        if ($request->hasSession() && Auth::check()) {
            return Auth::user();
        }

        return null;
    }

    /**
     * Validate bearer token.
     */
    protected function validateBearerToken(string $token): ?User
    {
        try {
            // Use Laravel Sanctum for bearer token validation
            $tokenModel = \Laravel\Sanctum\PersonalAccessToken::findToken($token);
            
            if ($tokenModel && $tokenModel->tokenable) {
                return $tokenModel->tokenable;
            }
        } catch (\Exception $e) {
            // Token validation failed
        }

        return null;
    }

    /**
     * Validate API key.
     */
    protected function validateApiKey(string $apiKey): ?User
    {
        // Cache API key lookups for performance
        return Cache::remember(
            "api_key_user_{$apiKey}",
            300, // 5 minutes
            function () use ($apiKey) {
                // In production, you'd have an api_keys table
                // For now, we'll use a simple configuration-based approach
                $apiKeys = config('chat.api_keys', []);
                
                if (isset($apiKeys[$apiKey])) {
                    $userId = $apiKeys[$apiKey]['user_id'];
                    return User::find($userId);
                }

                return null;
            }
        );
    }

    /**
     * Check if anonymous access is allowed for this endpoint.
     */
    protected function isAnonymousAllowed(Request $request): bool
    {
        $allowedRoutes = [
            'POST:/api/v1/chat/rooms',
            'GET:/api/v1/chat/rooms/*',
            'POST:/api/v1/chat/rooms/*/messages',
            'GET:/api/v1/chat/rooms/*/messages',
            'POST:/api/v1/chat/rooms/*/files',
            'GET:/api/v1/chat/files/*/download',
            'GET:/api/v1/chat/files/*/preview',
        ];

        $method = $request->method();
        $path = $request->path();

        foreach ($allowedRoutes as $route) {
            if ($this->matchesRoute($method . ':/' . $path, $route)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if request matches route pattern.
     */
    protected function matchesRoute(string $requestRoute, string $pattern): bool
    {
        // Convert pattern to regex
        $regex = str_replace('*', '[^/]+', $pattern);
        $regex = '/^' . str_replace('/', '\/', $regex) . '$/';
        
        return preg_match($regex, $requestRoute);
    }

    /**
     * Track API usage for analytics.
     */
    protected function trackApiUsage(Request $request, ?User $user): void
    {
        if (!config('chat.api.usage_tracking', true)) {
            return;
        }

        $data = [
            'user_id' => $user?->id,
            'application' => $request->header('X-Application-Name', 'unknown'),
            'endpoint' => $request->path(),
            'method' => $request->method(),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'timestamp' => now(),
        ];

        // Store in cache for batch processing
        $cacheKey = 'api_usage_' . date('Y-m-d-H');
        $usage = Cache::get($cacheKey, []);
        $usage[] = $data;
        Cache::put($cacheKey, $usage, 3600); // 1 hour
    }

    /**
     * Create guest user for anonymous visitors.
     */
    protected function createGuestUser(Request $request): User
    {
        // Create a temporary guest user object (not saved to database)
        $guest = new User();
        $guest->id = 0;
        $guest->name = 'Anonymous Visitor';
        $guest->email = '<EMAIL>';
        $guest->is_guest = true;
        
        return $guest;
    }
}
