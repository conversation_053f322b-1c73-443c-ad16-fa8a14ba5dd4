<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;

class SubscriberTag extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'name',
        'slug',
        'description',
        'color',
        'is_active',
        'subscriber_count',
        'created_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($tag) {
            if (empty($tag->uuid)) {
                $tag->uuid = Str::uuid();
            }
            if (empty($tag->slug)) {
                $tag->slug = Str::slug($tag->name);
            }
        });

        static::updating(function ($tag) {
            if ($tag->isDirty('name') && empty($tag->slug)) {
                $tag->slug = Str::slug($tag->name);
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Scope a query to only include active tags.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the user who created this tag.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the newsletter subscriptions with this tag.
     */
    public function newsletterSubscriptions(): BelongsToMany
    {
        return $this->belongsToMany(NewsletterSubscription::class, 'newsletter_subscription_tags')
                   ->withPivot('tagged_at', 'tagged_by')
                   ->withTimestamps();
    }

    /**
     * Alias for newsletterSubscriptions relationship.
     */
    public function subscribers(): BelongsToMany
    {
        return $this->newsletterSubscriptions();
    }

    /**
     * Update subscriber count.
     */
    public function updateSubscriberCount(): void
    {
        $count = $this->newsletterSubscriptions()
                     ->where('newsletter_subscriptions.is_active', true)
                     ->count();

        $this->update(['subscriber_count' => $count]);
    }

    /**
     * Get available tag colors.
     */
    public static function getColors(): array
    {
        return [
            '#3B82F6' => 'Blue',
            '#10B981' => 'Green',
            '#F59E0B' => 'Yellow',
            '#EF4444' => 'Red',
            '#8B5CF6' => 'Purple',
            '#06B6D4' => 'Cyan',
            '#F97316' => 'Orange',
            '#84CC16' => 'Lime',
            '#EC4899' => 'Pink',
            '#6B7280' => 'Gray',
        ];
    }

    /**
     * Get color name.
     */
    public function getColorNameAttribute(): string
    {
        $colors = self::getColors();
        return $colors[$this->color] ?? 'Custom';
    }

    /**
     * Check if tag can be deleted.
     */
    public function canBeDeleted(): bool
    {
        return $this->subscriber_count === 0;
    }

    /**
     * Add subscribers to tag.
     */
    public function addSubscribers(array $subscriberIds, int $taggedBy = null): void
    {
        $syncData = [];
        foreach ($subscriberIds as $subscriberId) {
            $syncData[$subscriberId] = [
                'tagged_at' => now(),
                'tagged_by' => $taggedBy,
            ];
        }

        $this->newsletterSubscriptions()->syncWithoutDetaching($syncData);
        $this->updateSubscriberCount();
    }

    /**
     * Remove subscribers from tag.
     */
    public function removeSubscribers(array $subscriberIds): void
    {
        $this->newsletterSubscriptions()->detach($subscriberIds);
        $this->updateSubscriberCount();
    }

    /**
     * Get subscribers with this tag.
     */
    public function getActiveSubscribers()
    {
        return $this->newsletterSubscriptions()
                   ->where('newsletter_subscriptions.is_active', true)
                   ->get();
    }
}
