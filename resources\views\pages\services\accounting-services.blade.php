@extends('layouts.app')

@section('title', __('accounting-services.page_title'))
@section('meta_description', __('accounting-services.meta_description'))
@section('meta_keywords', __('accounting-services.meta_keywords'))

@push('structured_data')
@verbatim
<script type="application/ld+json">
{
  "@type": "Service",
  "name": "Professional Accounting Services",
  "description": "Comprehensive accounting and bookkeeping services for all business sizes using modern accounting systems and tools",
  "provider": {
    "@type": "Organization",
    "name": @json(__('common.company_name')),
    "url": @json(url('/'))
  },
  "serviceType": "Accounting Services",
  "areaServed": "Worldwide",
  "offers": {
    "@type": "Offer",
    "availability": "https://schema.org/InStock"
  }
}
</script>
@endverbatim
@endpush

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-to-br from-green-600 via-green-700 to-green-800 text-white overflow-hidden">
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width=&quot;60&quot; height=&quot;60&quot; viewBox=&quot;0 0 60 60&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><g fill=&quotnone&quot; fill-rule=&quotevenodd&quot;><g fill=&quot%23ffffff&quot; fill-opacity=&quot0.1&quot;><circle cx=&quot30&quot; cy=&quot30&quot; r=&quot2&quot;/></g></g></svg>');"></div>
    </div>
    
    <div class="container mx-auto px-4 py-20 lg:py-32 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div class="text-center lg:text-left">
                <h1 class="text-4xl lg:text-6xl font-bold mb-6 leading-tight">
                    {{ __('accounting-services.hero_title') }}
                </h1>
                <p class="text-xl lg:text-2xl text-green-100 mb-8 leading-relaxed">
                    {{ __('accounting-services.hero_description') }}
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <a href="{{ route('contact', ['locale' => app()->getLocale()]) }}" class="bg-white text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-green-50 transition-colors inline-flex items-center justify-center">
                        {{ __('accounting-services.get_consultation') }}
                        <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </a>
                    <a href="#services" class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-green-600 transition-colors inline-flex items-center justify-center">
                        {{ __('accounting-services.view_services') }}
                    </a>
                </div>
            </div>
            
            <div class="relative">
                <div class="relative z-10">
                    <div class="bg-white rounded-xl shadow-xl p-8">
                        <div class="space-y-6">
                            <div class="group bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-6 hover:shadow-lg transition-all duration-300 border border-green-200">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center">
                                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"></path>
                                            <path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-800">{{ __('accounting-services.certified') }}</h3>
                                        <p class="text-gray-600">{{ __('accounting-services.qualified_accountants') }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="group bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-6 hover:shadow-lg transition-all duration-300 border border-blue-200">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-800">{{ __('accounting-services.flexible') }}</h3>
                                        <p class="text-gray-600">{{ __('accounting-services.flexible_services') }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="group bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-6 hover:shadow-lg transition-all duration-300 border border-purple-200">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center">
                                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-800">{{ __('accounting-services.reports') }}</h3>
                                        <p class="text-gray-600">{{ __('accounting-services.detailed_statements') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Floating Elements -->
                <div class="absolute top-10 right-10 w-16 h-16 bg-green-400 rounded-full opacity-20 animate-pulse"></div>
                <div class="absolute bottom-10 left-10 w-12 h-12 bg-green-300 rounded-full opacity-30 animate-bounce"></div>
            </div>
        </div>
    </div>
</section>

<!-- Services Overview -->
<section id="services" class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                {{ __('accounting-services.services_title') }}
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                {{ __('accounting-services.services_description') }}
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Bookkeeping Services -->
            <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow group">
                <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-green-600 transition-colors">
                    <svg class="w-8 h-8 text-green-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"></path>
                        <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3h4v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ __('accounting-services.bookkeeping_title') }}</h3>
                <p class="text-gray-600 mb-6">{{ __('accounting-services.bookkeeping_description') }}</p>
                <ul class="text-sm text-gray-600 space-y-2">
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.bookkeeping_1') }}</li>
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.bookkeeping_2') }}</li>
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.bookkeeping_3') }}</li>
                </ul>
            </div>

            <!-- Payroll Management -->
            <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow group">
                <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-green-600 transition-colors">
                    <svg class="w-8 h-8 text-green-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ __('accounting-services.payroll_title') }}</h3>
                <p class="text-gray-600 mb-6">{{ __('accounting-services.payroll_description') }}</p>
                <ul class="text-sm text-gray-600 space-y-2">
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.payroll_1') }}</li>
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.payroll_2') }}</li>
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.payroll_3') }}</li>
                </ul>
            </div>

            <!-- Tax Preparation -->
            <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow group">
                <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-green-600 transition-colors">
                    <svg class="w-8 h-8 text-green-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ __('accounting-services.tax_title') }}</h3>
                <p class="text-gray-600 mb-6">{{ __('accounting-services.tax_description') }}</p>
                <ul class="text-sm text-gray-600 space-y-2">
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.tax_1') }}</li>
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.tax_2') }}</li>
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.tax_3') }}</li>
                </ul>
            </div>
            <!-- Financial Reporting -->
            <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow group">
                <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-green-600 transition-colors">
                    <svg class="w-8 h-8 text-green-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ __('accounting-services.financial_title') }}</h3>
                <p class="text-gray-600 mb-6">{{ __('accounting-services.financial_description') }}</p>
                <ul class="text-sm text-gray-600 space-y-2">
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.financial_1') }}</li>
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.financial_2') }}</li>
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.financial_3') }}</li>
                </ul>
            </div>

            <!-- Business Consulting -->
            <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow group">
                <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-green-600 transition-colors">
                    <svg class="w-8 h-8 text-green-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ __('accounting-services.consulting_title') }}</h3>
                <p class="text-gray-600 mb-6">{{ __('accounting-services.consulting_description') }}</p>
                <ul class="text-sm text-gray-600 space-y-2">
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.consulting_1') }}</li>
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.consulting_2') }}</li>
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.consulting_3') }}</li>
                </ul>
            </div>

            <!-- Audit Support -->
            <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow group">
                <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-green-600 transition-colors">
                    <svg class="w-8 h-8 text-green-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ __('accounting-services.audit_title') }}</h3>
                <p class="text-gray-600 mb-6">{{ __('accounting-services.audit_description') }}</p>
                <ul class="text-sm text-gray-600 space-y-2">
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.audit_1') }}</li>
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.audit_2') }}</li>
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.audit_3') }}</li>
                </ul>
            </div>

            <!-- Feasibility Studies -->
            <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow group">
                <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-green-600 transition-colors">
                    <svg class="w-8 h-8 text-green-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ __('accounting-services.feasibility_title') }}</h3>
                <p class="text-gray-600 mb-6">{{ __('accounting-services.feasibility_description') }}</p>
                <ul class="text-sm text-gray-600 space-y-2">
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.feasibility_1') }}</li>
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.feasibility_2') }}</li>
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.feasibility_3') }}</li>
                </ul>
            </div>

            <!-- Strategic Setup -->
            <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow group">
                <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-green-600 transition-colors">
                    <svg class="w-8 h-8 text-green-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ __('accounting-services.strategic_title') }}</h3>
                <p class="text-gray-600 mb-6">{{ __('accounting-services.strategic_description') }}</p>
                <ul class="text-sm text-gray-600 space-y-2">
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.strategic_1') }}</li>
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.strategic_2') }}</li>
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.strategic_3') }}</li>
                </ul>
            </div>

            <!-- Accounting Consultancy -->
            <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow group">
                <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-green-600 transition-colors">
                    <svg class="w-8 h-8 text-green-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ __('accounting-services.consultancy_title') }}</h3>
                <p class="text-gray-600 mb-6">{{ __('accounting-services.consultancy_description') }}</p>
                <ul class="text-sm text-gray-600 space-y-2">
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.consultancy_1') }}</li>
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.consultancy_2') }}</li>
                    <li class="flex items-center"><span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>{{ __('accounting-services.consultancy_3') }}</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Pricing Plans -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                {{ __('accounting-services.pricing_title') }}
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                {{ __('accounting-services.pricing_description') }}
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <!-- Basic Package -->
            <div class="bg-gray-50 p-8 rounded-lg border-2 border-gray-200 hover:border-green-500 transition-colors">
                <div class="text-center mb-8">
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ __('accounting-services.basic_package') }}</h3>
                    <p class="text-gray-600 mb-4">{{ __('accounting-services.basic_package_desc') }}</p>
                    <div class="text-4xl font-bold text-green-600 mb-2">{{ __('accounting-services.starting_at') }}</div>
                    <p class="text-gray-500">$699{{ __('accounting-services.per_month') }}</p>
                </div>
                <ul class="space-y-3 mb-8">
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('accounting-services.basic_bookkeeping') }}
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('accounting-services.monthly_reports') }}
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('accounting-services.tax_support') }}
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('accounting-services.email_support') }}
                    </li>
                </ul>
                <a href="{{ route('contact', ['locale' => app()->getLocale()]) }}" class="block w-full text-center bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    {{ __('accounting-services.get_started') }}
                </a>
            </div>

            <!-- Professional Package -->
            <div class="bg-green-50 p-8 rounded-lg border-2 border-green-500 relative">
                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span class="bg-green-500 text-white px-4 py-1 rounded-full text-sm font-semibold">{{ __('accounting-services.most_popular') }}</span>
                </div>
                <div class="text-center mb-8">
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ __('accounting-services.professional_package') }}</h3>
                    <p class="text-gray-600 mb-4">{{ __('accounting-services.professional_package_desc') }}</p>
                    <div class="text-4xl font-bold text-green-600 mb-2">{{ __('accounting-services.starting_at') }}</div>
                    <p class="text-gray-500">$999{{ __('accounting-services.per_month') }}</p>
                </div>
                <ul class="space-y-3 mb-8">
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('accounting-services.complete_bookkeeping') }}
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('accounting-services.payroll_management') }}
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('accounting-services.financial_reporting') }}
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('accounting-services.tax_compliance') }}
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('accounting-services.phone_email_support') }}
                    </li>
                </ul>
                <a href="{{ route('contact', ['locale' => app()->getLocale()]) }}" class="block w-full text-center bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    Get Started
                </a>
            </div>

            <!-- Enterprise Package -->
            <div class="bg-gray-50 p-8 rounded-lg border-2 border-gray-200 hover:border-green-500 transition-colors">
                <div class="text-center mb-8">
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ __('accounting-services.enterprise_package') }}</h3>
                    <p class="text-gray-600 mb-4">{{ __('accounting-services.enterprise_package_desc') }}</p>
                    <div class="text-4xl font-bold text-green-600 mb-2">{{ __('accounting-services.custom_pricing') }}</div>
                    <p class="text-gray-500"></p>
                </div>
                <ul class="space-y-3 mb-8">
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('accounting-services.full_accounting_suite') }}
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('accounting-services.dedicated_accountant') }}
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('accounting-services.business_consulting') }}
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('accounting-services.priority_support') }}
                    </li>
                </ul>
                <a href="{{ route('contact', ['locale' => app()->getLocale()]) }}" class="block w-full text-center bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    {{ __('accounting-services.contact_us') }}
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Why Choose Us -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                {{ __('accounting-services.why_choose_title') }}
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                {{ __('accounting-services.why_choose_description') }}
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-10 h-10 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ __('accounting-services.certified_professionals') }}</h3>
                <p class="text-gray-600">{{ __('accounting-services.certified_professionals_desc') }}</p>
            </div>

            <div class="text-center">
                <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-10 h-10 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ __('accounting-services.compliance_100') }}</h3>
                <p class="text-gray-600">{{ __('accounting-services.compliance_100_desc') }}</p>
            </div>

            <div class="text-center">
                <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-10 h-10 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ __('accounting-services.detailed_reporting') }}</h3>
                <p class="text-gray-600">{{ __('accounting-services.detailed_reporting_desc') }}</p>
            </div>

            <div class="text-center">
                <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-10 h-10 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ __('accounting-services.dedicated_support') }}</h3>
                <p class="text-gray-600">{{ __('accounting-services.dedicated_support_desc') }}</p>
            </div>
        </div>
    </div>
</section>

<!-- Tools & Systems Section -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                {{ __('accounting-services.tools_title') }}
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                {{ __('accounting-services.tools_description') }}
            </p>
        </div>

        <!-- First Row - Original Tools -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
            <!-- QuickBooks -->
            <div class="text-center p-6 bg-gray-50 rounded-lg hover:shadow-lg transition-shadow">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"></path>
                        <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3h4v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">QuickBooks</h3>
                <p class="text-gray-600 text-sm">{{ __('accounting-services.quickbooks_desc') }}</p>
            </div>

            <!-- Xero -->
            <div class="text-center p-6 bg-gray-50 rounded-lg hover:shadow-lg transition-shadow">
                <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Xero</h3>
                <p class="text-gray-600 text-sm">{{ __('accounting-services.xero_desc') }}</p>
            </div>

            <!-- Sage -->
            <div class="text-center p-6 bg-gray-50 rounded-lg hover:shadow-lg transition-shadow">
                <div class="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Sage</h3>
                <p class="text-gray-600 text-sm">{{ __('accounting-services.sage_desc') }}</p>
            </div>

            <!-- FreshBooks -->
            <div class="text-center p-6 bg-gray-50 rounded-lg hover:shadow-lg transition-shadow">
                <div class="w-16 h-16 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">FreshBooks</h3>
                <p class="text-gray-600 text-sm">{{ __('accounting-services.freshbooks_desc') }}</p>
            </div>
        </div>

        <!-- Second Row - New Tools -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            <!-- MS 365 Dynamics -->
            <div class="text-center p-6 bg-gray-50 rounded-lg hover:shadow-lg transition-shadow">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">MS 365 Dynamics</h3>
                <p class="text-gray-600 text-sm">{{ __('accounting-services.ms365_dynamics_desc') }}</p>
            </div>

            <!-- Zoho -->
            <div class="text-center p-6 bg-gray-50 rounded-lg hover:shadow-lg transition-shadow">
                <div class="w-16 h-16 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Zoho</h3>
                <p class="text-gray-600 text-sm">{{ __('accounting-services.zoho_desc') }}</p>
            </div>

            <!-- Taly9 -->
            <div class="text-center p-6 bg-gray-50 rounded-lg hover:shadow-lg transition-shadow">
                <div class="w-16 h-16 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 000 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Taly9</h3>
                <p class="text-gray-600 text-sm">{{ __('accounting-services.taly9_desc') }}</p>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Cloud-Based Solutions -->
            <div class="text-center">
                <div class="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-10 h-10 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M5.5 16a3.5 3.5 0 01-.369-6.98 4 4 0 117.753-1.977A4.5 4.5 0 1113.5 16h-8z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ __('accounting-services.cloud_solutions') }}</h3>
                <p class="text-gray-600">{{ __('accounting-services.cloud_solutions_desc') }}</p>
            </div>

            <!-- Integration Capabilities -->
            <div class="text-center">
                <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-10 h-10 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ __('accounting-services.system_integration') }}</h3>
                <p class="text-gray-600">{{ __('accounting-services.system_integration_desc') }}</p>
            </div>

            <!-- Security & Compliance -->
            <div class="text-center">
                <div class="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-10 h-10 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ __('accounting-services.security_compliance') }}</h3>
                <p class="text-gray-600">{{ __('accounting-services.security_compliance_desc') }}</p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-green-600 text-white">
    <div class="container mx-auto px-4 text-center">
        <h2 class="text-3xl lg:text-4xl font-bold mb-6">
            {{ __('accounting-services.cta_title') }}
        </h2>
        <p class="text-xl text-green-100 mb-8 max-w-2xl mx-auto">
            {{ __('accounting-services.cta_description') }}
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('contact', ['locale' => app()->getLocale()]) }}" class="bg-white text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-green-50 transition-colors inline-flex items-center justify-center">
                {{ __('accounting-services.get_free_consultation') }}
                <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </a>
            <a href="tel:******-123-4567" class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-green-600 transition-colors inline-flex items-center justify-center">
                <svg class="mr-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                </svg>
                {{ __('accounting-services.call_now') }}
            </a>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});
</script>
@endpush
