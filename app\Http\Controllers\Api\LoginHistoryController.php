<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\LoginHistory;
use App\Models\User;
use App\Services\LoginHistoryService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class LoginHistoryController extends Controller
{
    protected LoginHistoryService $loginHistoryService;

    public function __construct(LoginHistoryService $loginHistoryService)
    {
        $this->loginHistoryService = $loginHistoryService;

        // Apply permission middleware to admin endpoints
        $this->middleware('auth:sanctum');
        $this->middleware('check.login.history.permission:view_basic_info,partial')->only(['adminIndex', 'adminShow']);
        $this->middleware('check.login.history.permission:view_statistics,partial')->only(['adminStatistics']);
        $this->middleware('check.login.history.permission:view_security_alerts,partial')->only(['adminAlerts']);
        $this->middleware('check.login.history.permission:export_data,full')->only(['export']);
    }

    /**
     * Get login history for the authenticated user.
     */
    public function index(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        $query = LoginHistory::where('user_id', $user->id)
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->has('status')) {
            $query->where('login_status', $request->status);
        }

        if ($request->has('days')) {
            $days = min((int) $request->days, 365); // Max 1 year
            $query->where('created_at', '>=', now()->subDays($days));
        }

        if ($request->has('suspicious_only') && $request->boolean('suspicious_only')) {
            $query->where('is_suspicious', true);
        }

        $loginHistories = $query->paginate($request->get('per_page', 15));

        // Transform data for API response (sanitize sensitive information)
        $loginHistories->getCollection()->transform(function ($login) {
            return [
                'id' => $login->uuid,
                'status' => $login->login_status,
                'timestamp' => $login->created_at->toISOString(),
                'location' => $login->formatted_location,
                'device_type' => $login->device_type,
                'browser' => $login->browser_name,
                'os' => $login->os_name,
                'ip_address' => $this->maskIpAddress($login->ip_address),
                'risk_level' => $login->risk_level,
                'risk_color' => $login->risk_color,
                'is_new_device' => $login->isNewDevice(),
                'is_new_location' => $login->isNewLocation(),
                'session_duration' => $login->formatted_session_duration,
                'failure_reason' => $login->failure_reason,
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $loginHistories,
            'meta' => [
                'total_count' => $loginHistories->total(),
                'current_page' => $loginHistories->currentPage(),
                'per_page' => $loginHistories->perPage(),
                'last_page' => $loginHistories->lastPage(),
            ]
        ]);
    }

    /**
     * Get login statistics for the authenticated user.
     */
    public function statistics(Request $request): JsonResponse
    {
        $user = Auth::user();
        $days = min((int) $request->get('days', 30), 365);

        $stats = $this->loginHistoryService->getLoginStats($user, $days);

        // Additional statistics
        $recentLogins = LoginHistory::where('user_id', $user->id)
            ->where('created_at', '>=', now()->subDays($days))
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($login) {
                return [
                    'timestamp' => $login->created_at->toISOString(),
                    'location' => $login->formatted_location,
                    'device_type' => $login->device_type,
                    'status' => $login->login_status,
                    'risk_level' => $login->risk_level,
                ];
            });

        $suspiciousActivities = LoginHistory::where('user_id', $user->id)
            ->where('is_suspicious', true)
            ->where('created_at', '>=', now()->subDays($days))
            ->count();

        return response()->json([
            'success' => true,
            'data' => array_merge($stats, [
                'recent_logins' => $recentLogins,
                'suspicious_activities' => $suspiciousActivities,
                'period_days' => $days,
            ])
        ]);
    }

    /**
     * Get detailed information about a specific login.
     */
    public function show(Request $request, string $uuid): JsonResponse
    {
        $user = Auth::user();
        
        $loginHistory = LoginHistory::where('uuid', $uuid)
            ->where('user_id', $user->id)
            ->firstOrFail();

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $loginHistory->uuid,
                'status' => $loginHistory->login_status,
                'timestamp' => $loginHistory->created_at->toISOString(),
                'location' => [
                    'formatted' => $loginHistory->formatted_location,
                    'country' => $loginHistory->geolocation_data['country'] ?? null,
                    'region' => $loginHistory->geolocation_data['region'] ?? null,
                    'city' => $loginHistory->geolocation_data['city'] ?? null,
                ],
                'device' => [
                    'type' => $loginHistory->device_type,
                    'browser' => $loginHistory->browser_name,
                    'os' => $loginHistory->os_name,
                    'is_mobile' => $loginHistory->device_info['is_mobile'] ?? false,
                ],
                'security' => [
                    'ip_address' => $this->maskIpAddress($loginHistory->ip_address),
                    'risk_score' => $loginHistory->risk_score,
                    'risk_level' => $loginHistory->risk_level,
                    'is_suspicious' => $loginHistory->is_suspicious,
                    'is_new_device' => $loginHistory->isNewDevice(),
                    'is_new_location' => $loginHistory->isNewLocation(),
                    'security_alert' => $loginHistory->security_alert,
                ],
                'session' => [
                    'duration' => $loginHistory->formatted_session_duration,
                    'started_at' => $loginHistory->session_started_at?->toISOString(),
                    'ended_at' => $loginHistory->session_ended_at?->toISOString(),
                ],
                'failure_reason' => $loginHistory->failure_reason,
            ]
        ]);
    }

    /**
     * Track a login attempt (for external systems).
     */
    public function track(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'nullable|exists:users,id',
            'email' => 'nullable|email',
            'status' => 'required|in:success,failed',
            'failure_reason' => 'nullable|string|max:255',
            'login_method' => 'nullable|in:standard,oauth,2fa',
            'twofa_used' => 'nullable|boolean',
            'twofa_verified' => 'nullable|boolean',
        ]);

        // Find user by ID or email
        $user = null;
        if ($request->user_id) {
            $user = User::find($request->user_id);
        } elseif ($request->email) {
            $user = User::where('email', $request->email)->first();
        }

        $loginHistory = $this->loginHistoryService->trackLoginAttempt(
            $request,
            $user,
            $request->status,
            $request->failure_reason,
            [
                'login_method' => $request->login_method ?? 'standard',
                'twofa_used' => $request->boolean('twofa_used'),
                'twofa_verified' => $request->boolean('twofa_verified'),
            ]
        );

        return response()->json([
            'success' => true,
            'data' => [
                'login_history_id' => $loginHistory->uuid,
                'risk_score' => $loginHistory->risk_score,
                'risk_level' => $loginHistory->risk_level,
                'is_suspicious' => $loginHistory->is_suspicious,
                'security_alert' => $loginHistory->security_alert,
            ]
        ], 201);
    }

    /**
     * Get security alerts for the authenticated user.
     */
    public function alerts(Request $request): JsonResponse
    {
        $user = Auth::user();
        $days = min((int) $request->get('days', 7), 30);

        $alerts = LoginHistory::where('user_id', $user->id)
            ->where('security_alert', true)
            ->where('created_at', '>=', now()->subDays($days))
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($login) {
                return [
                    'id' => $login->uuid,
                    'timestamp' => $login->created_at->toISOString(),
                    'type' => $login->login_status === 'failed' ? 'failed_login' : 'suspicious_login',
                    'location' => $login->formatted_location,
                    'device_type' => $login->device_type,
                    'risk_level' => $login->risk_level,
                    'description' => $this->getAlertDescription($login),
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $alerts,
            'meta' => [
                'count' => $alerts->count(),
                'period_days' => $days,
            ]
        ]);
    }

    /**
     * Mask IP address for privacy.
     */
    protected function maskIpAddress(string $ipAddress): string
    {
        if (filter_var($ipAddress, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            $parts = explode('.', $ipAddress);
            return $parts[0] . '.' . $parts[1] . '.***.' . $parts[3];
        } elseif (filter_var($ipAddress, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
            $parts = explode(':', $ipAddress);
            return implode(':', array_slice($parts, 0, 4)) . ':****';
        }

        return '***';
    }

    /**
     * Get alert description based on login data.
     */
    protected function getAlertDescription(LoginHistory $login): string
    {
        $reasons = [];

        if ($login->login_status === 'failed') {
            $reasons[] = 'Failed login attempt';
        }

        if ($login->isNewDevice()) {
            $reasons[] = 'New device detected';
        }

        if ($login->isNewLocation()) {
            $reasons[] = 'New location detected';
        }

        if ($login->is_vpn) {
            $reasons[] = 'VPN usage detected';
        }

        if ($login->is_tor) {
            $reasons[] = 'Tor network detected';
        }

        if ($login->risk_score >= 80) {
            $reasons[] = 'High risk score';
        }

        return implode(', ', $reasons) ?: 'Suspicious activity detected';
    }

    /**
     * Get login history for any user (admin/staff with permissions).
     */
    public function adminIndex(Request $request): JsonResponse
    {
        $permission = $request->get('login_history_permission');
        $accessLevel = $request->get('login_history_access_level');

        $query = LoginHistory::with('user')
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->has('status')) {
            $query->where('login_status', $request->status);
        }

        if ($request->has('days')) {
            $days = min((int) $request->days, 365);
            $query->where('created_at', '>=', now()->subDays($days));
        }

        if ($request->has('suspicious_only') && $request->boolean('suspicious_only')) {
            $query->where('is_suspicious', true);
        }

        if ($request->has('risk_level')) {
            $riskLevel = $request->risk_level;
            $query->where(function ($q) use ($riskLevel) {
                switch ($riskLevel) {
                    case 'minimal':
                        $q->whereBetween('risk_score', [0, 19]);
                        break;
                    case 'low':
                        $q->whereBetween('risk_score', [20, 39]);
                        break;
                    case 'medium':
                        $q->whereBetween('risk_score', [40, 59]);
                        break;
                    case 'high':
                        $q->whereBetween('risk_score', [60, 79]);
                        break;
                    case 'critical':
                        $q->whereBetween('risk_score', [80, 100]);
                        break;
                }
            });
        }

        $loginHistories = $query->paginate($request->get('per_page', 15));

        // Determine current user's admin status and create safe permission helpers.
        $currentUser = Auth::user();
        $isAdmin = $currentUser ? $currentUser->hasRole('admin') : false;
        $hasFullAccess = ($accessLevel === 'full') || $isAdmin;

        // Safe permission checker: returns true for admins, or if permission exists and grants it.
        $permAllows = function (string $permName) use ($permission, $isAdmin) {
            if ($isAdmin) {
                return true;
            }

            if (! $permission) {
                return false;
            }

            if (! method_exists($permission, 'hasSpecificPermission')) {
                return false;
            }

            return $permission->hasSpecificPermission($permName);
        };

        // Transform data based on permission level
        $loginHistories->getCollection()->transform(function ($login) use ($permission, $accessLevel, $hasFullAccess, $permAllows) {
            $data = [
                'id' => $login->uuid,
                'status' => $login->login_status,
                'timestamp' => $login->created_at->toISOString(),
                'location' => $login->formatted_location,
                'device_type' => $login->device_type,
                'browser' => $login->browser_name,
                'os' => $login->os_name,
                'user' => [
                    'id' => $login->user?->uuid,
                    'name' => $login->user?->name,
                    'email' => $login->user?->email,
                ],
                'failure_reason' => $login->failure_reason,
            ];

            // Add sensitive data based on permission level
            if ($hasFullAccess || $permAllows('view_ip_addresses')) {
                $data['ip_address'] = $login->ip_address;
            } else {
                $data['ip_address'] = $this->maskIpAddress($login->ip_address);
            }

            if ($hasFullAccess || $permAllows('view_risk_scores')) {
                $data['risk_level'] = $login->risk_level;
                $data['risk_score'] = $login->risk_score;
                $data['risk_color'] = $login->risk_color;
                $data['is_suspicious'] = $login->is_suspicious;
                $data['security_alert'] = $login->security_alert;
            }

            if ($hasFullAccess || $permAllows('view_device_details')) {
                $data['device_fingerprint'] = $login->device_fingerprint;
                $data['user_agent'] = $login->user_agent;
                $data['is_new_device'] = $login->isNewDevice();
            }

            if ($hasFullAccess || $permAllows('view_location_data')) {
                $data['geolocation'] = $login->geolocation_data;
                $data['is_new_location'] = $login->isNewLocation();
            }

            if ($hasFullAccess || $permAllows('view_session_data')) {
                $data['session_duration'] = $login->formatted_session_duration;
                $data['session_id'] = $login->session_id;
            }

            return $data;
        });

        return response()->json([
            'success' => true,
            'data' => $loginHistories,
            'meta' => [
                'total_count' => $loginHistories->total(),
                'current_page' => $loginHistories->currentPage(),
                'per_page' => $loginHistories->perPage(),
                'last_page' => $loginHistories->lastPage(),
                'access_level' => $accessLevel,
            ]
        ]);
    }
}
