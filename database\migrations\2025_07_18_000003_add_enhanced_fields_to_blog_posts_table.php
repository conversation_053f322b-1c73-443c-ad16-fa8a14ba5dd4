<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('blog_posts', function (Blueprint $table) {
            // Multi-image support
            $table->json('gallery_images')->nullable()->after('featured_image');
            
            // Service associations
            $table->json('service_ids')->nullable()->after('author_id');
            
            // Analytics
            $table->unsignedBigInteger('view_count')->default(0)->after('service_ids');
            
            // Add indexes for performance
            $table->index('view_count');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('blog_posts', function (Blueprint $table) {
            $table->dropIndex(['view_count']);
            $table->dropColumn(['gallery_images', 'service_ids', 'view_count']);
        });
    }
};
