<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default AI Provider
    |--------------------------------------------------------------------------
    |
    | This option controls the default AI provider that will be used by the
    | chat system. You may specify any of the providers defined in the
    | "providers" array below.
    |
    */

    'default' => env('AI_DEFAULT_PROVIDER', 'openai'),

    /*
    |--------------------------------------------------------------------------
    | AI Provider Configurations
    |--------------------------------------------------------------------------
    |
    | Here you may configure the AI providers for your application. Each
    | provider has its own configuration including API keys, models, and
    | specific settings.
    |
    */

    'providers' => [
        'openai' => [
            'name' => 'OpenAI',
            'api_key' => env('OPENAI_API_KEY'),
            'base_url' => env('OPENAI_BASE_URL', 'https://api.openai.com/v1'),
            'organization' => env('OPENAI_ORGANIZATION'),
            'models' => [
                // GPT Models
                'gpt-4.1' => [
                    'name' => 'GPT-4.1',
                    'description' => 'Smartest model for complex tasks',
                    'type' => 'chat',
                    'context_length' => 1000000,
                    'supports_vision' => true,
                    'supports_function_calling' => true,
                    'input_cost_per_1m_tokens' => 2.00,
                    'output_cost_per_1m_tokens' => 8.00,
                    'tier' => 'premium',
                ],
                'gpt-4.1-mini' => [
                    'name' => 'GPT-4.1 Mini',
                    'description' => 'Affordable model balancing speed and intelligence',
                    'type' => 'chat',
                    'context_length' => 1000000,
                    'supports_vision' => true,
                    'supports_function_calling' => true,
                    'input_cost_per_1m_tokens' => 0.40,
                    'output_cost_per_1m_tokens' => 1.60,
                    'tier' => 'standard',
                ],
                'gpt-4.1-nano' => [
                    'name' => 'GPT-4.1 Nano',
                    'description' => 'Fastest, most cost-effective model for low-latency tasks',
                    'type' => 'chat',
                    'context_length' => 1000000,
                    'supports_vision' => true,
                    'supports_function_calling' => true,
                    'input_cost_per_1m_tokens' => 0.10,
                    'output_cost_per_1m_tokens' => 0.40,
                    'tier' => 'basic',
                ],
                // Reasoning Models
                'o3' => [
                    'name' => 'OpenAI o3',
                    'description' => 'Most powerful reasoning model with leading performance',
                    'type' => 'reasoning',
                    'context_length' => 200000,
                    'supports_vision' => true,
                    'supports_function_calling' => true,
                    'input_cost_per_1m_tokens' => 2.00,
                    'output_cost_per_1m_tokens' => 8.00,
                    'tier' => 'premium',
                ],
                'o4-mini' => [
                    'name' => 'OpenAI o4-mini',
                    'description' => 'Faster, cost-efficient reasoning model',
                    'type' => 'reasoning',
                    'context_length' => 200000,
                    'supports_vision' => true,
                    'supports_function_calling' => true,
                    'input_cost_per_1m_tokens' => 1.10,
                    'output_cost_per_1m_tokens' => 4.40,
                    'tier' => 'standard',
                ],
            ],
        ],

        'anthropic' => [
            'name' => 'Anthropic Claude',
            'api_key' => env('ANTHROPIC_API_KEY'),
            'base_url' => env('ANTHROPIC_BASE_URL', 'https://api.anthropic.com'),
            'models' => [
                'claude-opus-4-20250514' => [
                    'name' => 'Claude Opus 4',
                    'description' => 'Most capable and intelligent model yet',
                    'type' => 'chat',
                    'context_length' => 200000,
                    'supports_vision' => true,
                    'supports_function_calling' => true,
                    'input_cost_per_1m_tokens' => 15.00,
                    'output_cost_per_1m_tokens' => 75.00,
                    'tier' => 'premium',
                ],
                'claude-sonnet-4-20250514' => [
                    'name' => 'Claude Sonnet 4',
                    'description' => 'High-performance model with exceptional reasoning',
                    'type' => 'chat',
                    'context_length' => 200000,
                    'supports_vision' => true,
                    'supports_function_calling' => true,
                    'input_cost_per_1m_tokens' => 3.00,
                    'output_cost_per_1m_tokens' => 15.00,
                    'tier' => 'standard',
                ],
                'claude-3-7-sonnet-20250219' => [
                    'name' => 'Claude Sonnet 3.7',
                    'description' => 'High-performance model with early extended thinking',
                    'type' => 'chat',
                    'context_length' => 200000,
                    'supports_vision' => true,
                    'supports_function_calling' => true,
                    'input_cost_per_1m_tokens' => 3.00,
                    'output_cost_per_1m_tokens' => 15.00,
                    'tier' => 'standard',
                ],
                'claude-3-5-haiku-20241022' => [
                    'name' => 'Claude Haiku 3.5',
                    'description' => 'Fastest model with intelligence at blazing speeds',
                    'type' => 'chat',
                    'context_length' => 200000,
                    'supports_vision' => true,
                    'supports_function_calling' => true,
                    'input_cost_per_1m_tokens' => 0.80,
                    'output_cost_per_1m_tokens' => 4.00,
                    'tier' => 'basic',
                ],
            ],
        ],

        'google' => [
            'name' => 'Google Gemini',
            'api_key' => env('GOOGLE_AI_API_KEY'),
            'base_url' => env('GOOGLE_AI_BASE_URL', 'https://generativelanguage.googleapis.com/v1beta'),
            'models' => [
                'gemini-2.5-pro' => [
                    'name' => 'Gemini 2.5 Pro',
                    'description' => 'Most powerful thinking model with maximum response accuracy',
                    'type' => 'chat',
                    'context_length' => 1048576,
                    'supports_vision' => true,
                    'supports_function_calling' => true,
                    'supports_thinking' => true,
                    'input_cost_per_1m_tokens' => 1.25,
                    'output_cost_per_1m_tokens' => 5.00,
                    'tier' => 'premium',
                ],
                'gemini-2.5-flash' => [
                    'name' => 'Gemini 2.5 Flash',
                    'description' => 'Best model in terms of price-performance',
                    'type' => 'chat',
                    'context_length' => 1048576,
                    'supports_vision' => true,
                    'supports_function_calling' => true,
                    'supports_thinking' => true,
                    'input_cost_per_1m_tokens' => 0.075,
                    'output_cost_per_1m_tokens' => 0.30,
                    'tier' => 'standard',
                ],
                'gemini-2.5-flash-lite' => [
                    'name' => 'Gemini 2.5 Flash-Lite',
                    'description' => 'Optimized for cost efficiency and low latency',
                    'type' => 'chat',
                    'context_length' => 1048576,
                    'supports_vision' => true,
                    'supports_function_calling' => true,
                    'supports_thinking' => true,
                    'input_cost_per_1m_tokens' => 0.0375,
                    'output_cost_per_1m_tokens' => 0.15,
                    'tier' => 'basic',
                ],
                'gemini-2.0-flash' => [
                    'name' => 'Gemini 2.0 Flash',
                    'description' => 'Next generation features, speed, and realtime streaming',
                    'type' => 'chat',
                    'context_length' => 1048576,
                    'supports_vision' => true,
                    'supports_function_calling' => true,
                    'input_cost_per_1m_tokens' => 0.075,
                    'output_cost_per_1m_tokens' => 0.30,
                    'tier' => 'standard',
                ],
            ],
        ],

        'xai' => [
            'name' => 'xAI Grok',
            'api_key' => env('XAI_API_KEY'),
            'base_url' => env('XAI_BASE_URL', 'https://api.x.ai/v1'),
            'models' => [
                'grok-4' => [
                    'name' => 'Grok 4',
                    'description' => 'The worlds best model, at your fingertips',
                    'type' => 'chat',
                    'context_length' => 256000,
                    'supports_vision' => true,
                    'supports_function_calling' => true,
                    'supports_search' => true,
                    'input_cost_per_1m_tokens' => 3.00,
                    'output_cost_per_1m_tokens' => 15.00,
                    'tier' => 'premium',
                ],
                'grok-3' => [
                    'name' => 'Grok 3',
                    'description' => 'Flagship model that excels at enterprise tasks',
                    'type' => 'chat',
                    'context_length' => 131072,
                    'supports_vision' => true,
                    'supports_function_calling' => true,
                    'supports_search' => true,
                    'input_cost_per_1m_tokens' => 3.00,
                    'output_cost_per_1m_tokens' => 15.00,
                    'tier' => 'standard',
                ],
                'grok-3-mini' => [
                    'name' => 'Grok 3 Mini',
                    'description' => 'Lightweight model that thinks before responding',
                    'type' => 'chat',
                    'context_length' => 131072,
                    'supports_vision' => false,
                    'supports_function_calling' => true,
                    'supports_search' => true,
                    'input_cost_per_1m_tokens' => 0.30,
                    'output_cost_per_1m_tokens' => 0.50,
                    'tier' => 'basic',
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Model Tiers
    |--------------------------------------------------------------------------
    |
    | Define different tiers of models with their characteristics and
    | access levels. This helps in organizing models by capability and cost.
    |
    */

    'tiers' => [
        'basic' => [
            'name' => 'Basic',
            'description' => 'Cost-effective models for simple tasks',
            'max_requests_per_minute' => 100,
            'max_tokens_per_request' => 4000,
        ],
        'standard' => [
            'name' => 'Standard',
            'description' => 'Balanced performance and cost',
            'max_requests_per_minute' => 200,
            'max_tokens_per_request' => 8000,
        ],
        'premium' => [
            'name' => 'Premium',
            'description' => 'Most capable models for complex tasks',
            'max_requests_per_minute' => 500,
            'max_tokens_per_request' => 32000,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Fallback Configuration
    |--------------------------------------------------------------------------
    |
    | Configure fallback behavior when the primary provider fails.
    |
    */

    'fallback' => [
        'enabled' => env('AI_FALLBACK_ENABLED', true),
        'providers' => [
            'openai' => ['anthropic', 'google'],
            'anthropic' => ['openai', 'google'],
            'google' => ['openai', 'anthropic'],
            'xai' => ['openai', 'anthropic'],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Configure rate limiting for AI API calls to prevent abuse and
    | manage costs.
    |
    */

    'rate_limiting' => [
        'enabled' => env('AI_RATE_LIMITING_ENABLED', true),
        'requests_per_minute' => env('AI_REQUESTS_PER_MINUTE', 60),
        'tokens_per_minute' => env('AI_TOKENS_PER_MINUTE', 100000),
        'cost_limit_per_hour' => env('AI_COST_LIMIT_PER_HOUR', 10.00),
    ],

    /*
    |--------------------------------------------------------------------------
    | Caching Configuration
    |--------------------------------------------------------------------------
    |
    | Configure caching for AI responses to improve performance and
    | reduce costs.
    |
    */

    'caching' => [
        'enabled' => env('AI_CACHING_ENABLED', true),
        'ttl' => env('AI_CACHE_TTL', 3600), // 1 hour
        'driver' => env('AI_CACHE_DRIVER', 'redis'),
    ],
];
