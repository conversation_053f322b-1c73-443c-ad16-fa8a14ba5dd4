<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class SendMessageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Allow all for now, can add authorization logic later
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'content' => 'required|string|max:5000',
            'message_type' => 'sometimes|string|in:text,image,file,system,ai_response',
            'type' => 'sometimes|string|in:text,image,file,system,ai_response', // Alternative naming
            
            // File attachments
            'file' => 'sometimes|file|max:10240', // 10MB max
            'file_url' => 'sometimes|url',
            'file_name' => 'sometimes|string|max:255',
            'file_size' => 'sometimes|integer|min:0',
            'file_type' => 'sometimes|string|max:100',
            
            // Message metadata
            'metadata' => 'sometimes|array',
            'reply_to' => 'sometimes|integer|exists:chat_messages,id',
            'is_private' => 'sometimes|boolean',
            'is_system' => 'sometimes|boolean',
            
            // AI-related fields
            'is_ai_generated' => 'sometimes|boolean',
            'ai_confidence' => 'sometimes|numeric|min:0|max:1',
            'ai_model' => 'sometimes|string|max:100',
            
            // Sender info (for anonymous users)
            'sender_name' => 'sometimes|string|max:255',
            'sender_email' => 'sometimes|email|max:255',
            
            // Additional fields
            'language' => 'sometimes|string|max:10',
            'urgency' => 'sometimes|string|in:low,normal,high,urgent',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'content.required' => 'Message content is required.',
            'content.max' => 'Message content cannot exceed 5000 characters.',
            'message_type.in' => 'Message type must be one of: text, image, file, system, ai_response.',
            'type.in' => 'Message type must be one of: text, image, file, system, ai_response.',
            'file.max' => 'File size cannot exceed 10MB.',
            'sender_email.email' => 'Please provide a valid email address.',
            'ai_confidence.min' => 'AI confidence must be between 0 and 1.',
            'ai_confidence.max' => 'AI confidence must be between 0 and 1.',
            'urgency.in' => 'Urgency must be one of: low, normal, high, urgent.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        $this->merge([
            'message_type' => $this->message_type ?? $this->type ?? 'text',
            'is_private' => $this->is_private ?? false,
            'is_system' => $this->is_system ?? false,
            'is_ai_generated' => $this->is_ai_generated ?? false,
            'language' => $this->language ?? 'en',
            'urgency' => $this->urgency ?? 'normal',
        ]);
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422)
        );
    }
}
