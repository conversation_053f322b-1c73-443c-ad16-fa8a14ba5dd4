<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\AiConversationLog;
use App\Services\AIPerformanceReportingService;
use App\Services\ActivityLogger;
use App\Services\DashboardCacheService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use PHPUnit\Framework\Attributes\Test;
use Carbon\Carbon;

class AIPerformanceReportingTest extends TestCase
{
    use RefreshDatabase;

    protected AIPerformanceReportingService $performanceService;
    protected User $admin;
    protected User $staff;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles with unique slugs
        $adminRole = Role::factory()->create([
            'name' => 'admin',
            'slug' => 'admin-perf'
        ]);
        $staffRole = Role::factory()->create([
            'name' => 'staff', 
            'slug' => 'staff-perf'
        ]);
        $userRole = Role::factory()->create([
            'name' => 'user',
            'slug' => 'user-perf'
        ]);

        // Create users
        $this->admin = User::factory()->create(['role_id' => $adminRole->id]);
        $this->staff = User::factory()->create(['role_id' => $staffRole->id]);
        $this->user = User::factory()->create(['role_id' => $userRole->id]);

        // Mock dependencies
        $this->mock(ActivityLogger::class, function ($mock) {
            $mock->shouldReceive('log')->andReturn(new \App\Models\ActivityLog());
        });

        $this->mock(DashboardCacheService::class, function ($mock) {
            $mock->shouldReceive('invalidatePattern')->andReturn(true);
            $mock->shouldReceive('remember')->andReturnUsing(function ($key, $callback, $ttl = null) {
                return $callback();
            });
        });

        // Create performance service
        $this->performanceService = app(AIPerformanceReportingService::class);

        // Clear cache
        Cache::flush();
    }

    #[Test]
    public function admin_can_access_ai_performance_dashboard()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.chat.ai.performance.index'));

        $response->assertStatus(200);
        
        // Check that we get a successful response with the expected content
        $content = $response->getContent();
        $this->assertStringContainsString('AI Performance Dashboard', $content);
        $this->assertStringContainsString('Total Interactions', $content);
        $this->assertStringContainsString('Success Rate', $content);
    }

    #[Test]
    public function staff_can_access_ai_performance_dashboard()
    {
        $response = $this->actingAs($this->staff)
            ->get(route('admin.chat.ai.performance.index'));

        $response->assertStatus(200);
    }

    #[Test]
    public function regular_user_cannot_access_ai_performance_dashboard()
    {
        $response = $this->actingAs($this->user)
            ->get(route('admin.chat.ai.performance.index'));

        $response->assertStatus(403);
    }

    #[Test]
    public function performance_service_provides_comprehensive_report()
    {
        // Create test data
        $room = ChatRoom::factory()->create();
        $message = ChatMessage::factory()->create(['chat_room_id' => $room->id]);
        
        AiConversationLog::create([
            'chat_room_id' => $room->id,
            'chat_message_id' => $message->id,
            'user_message' => 'Test message',
            'ai_response' => 'Test response',
            'confidence_score' => 0.8,
            'processing_time_ms' => 1500,
            'model_used' => 'gpt-3.5-turbo',
            'escalated_to_human' => false,
        ]);

        $report = $this->performanceService->getPerformanceReport();

        $this->assertArrayHasKey('summary', $report);
        $this->assertArrayHasKey('success_metrics', $report);
        $this->assertArrayHasKey('escalation_analysis', $report);
        $this->assertArrayHasKey('response_time_analysis', $report);
        $this->assertArrayHasKey('confidence_analysis', $report);
        $this->assertArrayHasKey('language_performance', $report);
        $this->assertArrayHasKey('model_comparison', $report);
        $this->assertArrayHasKey('trending_topics', $report);
        $this->assertArrayHasKey('failure_analysis', $report);
    }

    #[Test]
    public function performance_api_returns_report_data()
    {
        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.ai.performance.api.v1.reports.index'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'summary',
                'success_metrics',
                'escalation_analysis',
                'response_time_analysis',
                'confidence_analysis',
                'language_performance',
                'model_comparison',
                'trending_topics',
                'failure_analysis',
            ],
            'filters',
        ]);
    }

    #[Test]
    public function performance_api_returns_summary_data()
    {
        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.ai.performance.api.v1.reports.summary'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'total_interactions',
                'successful_interactions',
                'escalated_interactions',
                'success_rate',
                'escalation_rate',
                'avg_confidence_score',
                'avg_response_time_ms',
                'period_start',
                'period_end',
            ],
        ]);
    }

    #[Test]
    public function performance_api_returns_success_metrics()
    {
        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.ai.performance.api.v1.metrics.success'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'by_response_type',
                'by_confidence_level',
            ],
        ]);
    }

    #[Test]
    public function performance_api_returns_escalation_analysis()
    {
        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.ai.performance.api.v1.metrics.escalation'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'total_escalations',
                'escalation_reasons',
                'escalation_by_hour',
                'common_triggers',
            ],
        ]);
    }

    #[Test]
    public function performance_api_returns_response_time_analysis()
    {
        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.ai.performance.api.v1.metrics.response_time'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'avg_response_time',
                'median_response_time',
                'p95_response_time',
                'p99_response_time',
                'min_response_time',
                'max_response_time',
                'response_time_distribution',
            ],
        ]);
    }

    #[Test]
    public function performance_api_returns_confidence_analysis()
    {
        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.ai.performance.api.v1.metrics.confidence'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'avg_confidence',
                'confidence_distribution',
                'low_confidence_rate',
            ],
        ]);
    }

    #[Test]
    public function performance_api_returns_language_performance()
    {
        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.ai.performance.api.v1.performance.languages'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data',
        ]);
    }

    #[Test]
    public function performance_api_returns_model_comparison()
    {
        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.ai.performance.api.v1.performance.models'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data',
        ]);
    }

    #[Test]
    public function performance_api_returns_trending_topics()
    {
        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.ai.performance.api.v1.analytics.topics'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'common_queries',
                'trending_keywords',
            ],
        ]);
    }

    #[Test]
    public function performance_api_returns_failure_analysis()
    {
        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.ai.performance.api.v1.analytics.failures'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'total_failures',
                'failure_patterns',
                'failure_rate_trend',
            ],
        ]);
    }

    #[Test]
    public function performance_export_returns_report_data()
    {
        $response = $this->actingAs($this->admin)
            ->postJson(route('admin.chat.ai.performance.api.v1.reports.export'), [
                'format' => 'json',
                'start_date' => now()->subDays(7)->format('Y-m-d'),
                'end_date' => now()->format('Y-m-d'),
            ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'report_generated_at',
                'filters_applied',
                'format',
                'data',
            ],
        ]);
    }

    #[Test]
    public function performance_service_handles_filters_correctly()
    {
        // Create test data with different models and languages
        $room = ChatRoom::factory()->create();
        $message1 = ChatMessage::factory()->create(['chat_room_id' => $room->id]);
        $message2 = ChatMessage::factory()->create(['chat_room_id' => $room->id]);
        
        AiConversationLog::create([
            'chat_room_id' => $room->id,
            'chat_message_id' => $message1->id,
            'user_message' => 'English message',
            'ai_response' => 'English response',
            'confidence_score' => 0.9,
            'processing_time_ms' => 1000,
            'model_used' => 'gpt-4',
            'escalated_to_human' => false,
        ]);

        AiConversationLog::create([
            'chat_room_id' => $room->id,
            'chat_message_id' => $message2->id,
            'user_message' => 'Spanish message',
            'ai_response' => 'Spanish response',
            'confidence_score' => 0.7,
            'processing_time_ms' => 1500,
            'model_used' => 'gpt-3.5-turbo',
            'escalated_to_human' => true,
        ]);

        // Test with model filter
        $reportWithModelFilter = $this->performanceService->getPerformanceReport([
            'model' => 'gpt-4',
            'start_date' => now()->subDay(),
            'end_date' => now(),
        ]);

        $this->assertEquals(1, $reportWithModelFilter['summary']['total_interactions']);

        // Test with language filter (since we don't have detected_language column, this will return all data)
        $reportWithLanguageFilter = $this->performanceService->getPerformanceReport([
            'language' => 'es',
            'start_date' => now()->subDay(),
            'end_date' => now(),
        ]);

        // Since we don't have language detection implemented, this should return all data
        $this->assertEquals(2, $reportWithLanguageFilter['summary']['total_interactions']);
    }

    #[Test]
    public function unauthorized_users_cannot_access_performance_api()
    {
        $response = $this->getJson(route('admin.chat.ai.performance.api.v1.reports.summary'));
        $response->assertStatus(401);

        $response = $this->actingAs($this->user)
            ->getJson(route('admin.chat.ai.performance.api.v1.reports.summary'));
        $response->assertStatus(403);
    }
}
