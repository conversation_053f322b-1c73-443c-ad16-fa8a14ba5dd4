<?php

namespace Database\Factories;

use App\Models\ChatRoom;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ChatRoom>
 */
class ChatRoomFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ChatRoom::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'uuid' => (string) Str::uuid(),
            'type' => $this->faker->randomElement(['visitor', 'support', 'sales']),
            'status' => $this->faker->randomElement(['active', 'waiting', 'closed']),
            'title' => $this->faker->optional()->sentence(3),
            'visitor_info' => [
                'name' => $this->faker->name(),
                'email' => $this->faker->email(),
                'ip_address' => $this->faker->ipv4(),
                'user_agent' => $this->faker->userAgent(),
                'referrer' => $this->faker->optional()->url(),
            ],
            'metadata' => [
                'source' => $this->faker->randomElement(['website', 'mobile_app', 'api']),
                'initial_message' => $this->faker->optional()->sentence(),
            ],
            'priority' => $this->faker->numberBetween(1, 5),
            'language' => $this->faker->randomElement(['en', 'es', 'fr', 'de']),
            'closed_at' => $this->faker->optional()->dateTimeBetween('-1 week', 'now'),
        ];
    }

    /**
     * Indicate that the chat room is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
            'closed_at' => null,
        ]);
    }

    /**
     * Indicate that the chat room is waiting.
     */
    public function waiting(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'waiting',
            'closed_at' => null,
        ]);
    }

    /**
     * Indicate that the chat room is closed.
     */
    public function closed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'closed',
            'closed_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    /**
     * Indicate that the chat room is for a visitor.
     */
    public function visitor(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'visitor',
        ]);
    }

    /**
     * Indicate that the chat room is for support.
     */
    public function support(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'support',
        ]);
    }

    /**
     * Indicate that the chat room is for sales.
     */
    public function sales(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'sales',
        ]);
    }
}
