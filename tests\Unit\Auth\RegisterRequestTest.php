<?php

namespace Tests\Unit\Auth;

use App\Http\Requests\Auth\RegisterRequest;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Validator;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;

class RegisterRequestTest extends TestCase
{
    use WithFaker;

    /**
     * Get validation rules without database-dependent rules for unit testing
     */
    private function getRulesWithoutDatabase(): array
    {
        $request = new RegisterRequest();
        $rules = $request->rules();

        // Remove unique constraint for unit testing
        $rules['email'] = array_filter($rules['email'], function($rule) {
            return !str_contains($rule, 'unique');
        });

        return $rules;
    }

    #[Test]
    public function it_validates_required_fields()
    {
        $rules = $this->getRulesWithoutDatabase();

        $validator = Validator::make([], $rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('first_name', $validator->errors()->toArray());
        $this->assertArrayHasKey('last_name', $validator->errors()->toArray());
        $this->assertArrayHasKey('email', $validator->errors()->toArray());
        $this->assertArrayHasKey('password', $validator->errors()->toArray());
        $this->assertArrayHasKey('terms', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_email_format()
    {
        $rules = $this->getRulesWithoutDatabase();

        $data = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => 'invalid-email',
            'password' => 'Password123!',
            'password_confirmation' => 'Password123!',
            'terms' => '1',
        ];

        $validator = Validator::make($data, $rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('email', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_password_confirmation()
    {
        $rules = $this->getRulesWithoutDatabase();

        $data = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'Password123!',
            'password_confirmation' => 'DifferentPassword123!',
            'terms' => '1',
        ];

        $validator = Validator::make($data, $rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('password', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_terms_acceptance()
    {
        $rules = $this->getRulesWithoutDatabase();

        $data = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'Password123!',
            'password_confirmation' => 'Password123!',
            'terms' => '0', // Not accepted
        ];

        $validator = Validator::make($data, $rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('terms', $validator->errors()->toArray());
    }

    #[Test]
    public function it_passes_validation_with_valid_data()
    {
        $rules = $this->getRulesWithoutDatabase();

        $data = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'Password123!',
            'password_confirmation' => 'Password123!',
            'terms' => '1',
        ];

        $validator = Validator::make($data, $rules);

        $this->assertFalse($validator->fails());
    }

    #[Test]
    public function it_validates_name_with_regex()
    {
        $rules = $this->getRulesWithoutDatabase();

        // Test valid names (should pass)
        $validData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'Password123!',
            'password_confirmation' => 'Password123!',
            'terms' => '1',
        ];

        $validator = Validator::make($validData, $rules);
        $this->assertFalse($validator->fails());
    }

    #[Test]
    public function debug_password_validation_rules()
    {
        $request = new RegisterRequest();
        $rules = $request->rules();

        // Just verify the password rules exist and are correct
        $this->assertArrayHasKey('password', $rules);
        $this->assertContains('required', $rules['password']);
        $this->assertContains('string', $rules['password']);
        $this->assertContains('confirmed', $rules['password']);
        $this->assertContains('min:8', $rules['password']);
    }
}
