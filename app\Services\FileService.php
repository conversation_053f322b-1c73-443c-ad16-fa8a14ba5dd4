<?php

namespace App\Services;

use App\Services\File\Traits\FileValidation;
use App\Services\File\Traits\FileSecurity;
use App\Services\File\Traits\FileProcessing;
use App\Services\File\Traits\FileLogging;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Storage;

class FileService
{
    use FileValidation, FileSecurity, FileProcessing, FileLogging;
    
    /**
     * Process uploaded file
     */
    public function processUploadedFile(UploadedFile $file, array $options = []): array
    {
        $startTime = microtime(true);
        
        try {
            // Set performance limits
            $this->setPerformanceLimits();
            
            // Validate file
            $validation = $this->validateFile($file);
            $this->logValidation($validation, $validation['file_info']);
            
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'errors' => $validation['errors'],
                    'file_info' => $validation['file_info'],
                ];
            }
            
            $fileInfo = $validation['file_info'];
            
            // Scan for viruses
            $scanResult = $this->scanForViruses($file->getRealPath());
            $this->logVirusScan($scanResult, $file->getRealPath());
            
            if (!$scanResult['clean']) {
                return [
                    'success' => false,
                    'errors' => ['Virus scan failed: ' . $scanResult['message']],
                    'file_info' => $fileInfo,
                ];
            }
            
            // Sanitize filename
            $sanitizedName = $this->sanitizeFilename($file->getClientOriginalName());
            
            // Determine storage path
            $storagePath = $this->buildStoragePath($sanitizedName, $options);
            
            // Store file
            $disk = Config::get('file.storage.disk', 'public');
            $filePath = $file->storeAs(dirname($storagePath), basename($storagePath), $disk);
            
            if (!$filePath) {
                return [
                    'success' => false,
                    'errors' => ['Failed to store file'],
                    'file_info' => $fileInfo,
                ];
            }
            
            $fullPath = Storage::disk($disk)->path($filePath);
            
            // Remove metadata if enabled
            if (Config::get('file.security.remove_metadata', false)) {
                $metadataRemoved = $this->removeMetadata($fullPath);
                $this->logMetadataRemoval($fullPath, $metadataRemoved);
            }
            
            // Process archive if applicable
            $archiveResult = null;
            if ($this->isArchiveFile($file)) {
                $archiveResult = $this->processArchive($fullPath);
                $this->logArchiveProcessing($archiveResult, $fullPath);
            }
            
            // Extract text content if enabled
            $textResult = null;
            if (Config::get('file.content_analysis.extract_text', false)) {
                $textResult = $this->extractTextContent($fullPath);
                $this->logTextExtraction($textResult, $fullPath);
            }
            
            $processingTime = (microtime(true) - $startTime) * 1000;
            
            $result = [
                'success' => true,
                'file_path' => $filePath,
                'full_path' => $fullPath,
                'file_info' => $fileInfo,
                'sanitized_name' => $sanitizedName,
                'storage_disk' => $disk,
                'processing_time_ms' => round($processingTime, 2),
                'virus_scan' => $scanResult,
            ];
            
            if ($archiveResult) {
                $result['archive_analysis'] = $archiveResult;
            }
            
            if ($textResult) {
                $result['text_content'] = $textResult;
            }
            
            $this->logUploadCompletion($result, $fileInfo);
            $this->logProcessingTime('file_upload', $processingTime, ['file_name' => $fileInfo['original_name']]);
            
            return $result;
            
        } catch (\Exception $e) {
            $this->logError('File processing failed: ' . $e->getMessage(), [
                'file_name' => $file->getClientOriginalName(),
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return [
                'success' => false,
                'errors' => ['File processing failed: ' . $e->getMessage()],
                'file_info' => $fileInfo ?? [],
            ];
        }
    }
    
    /**
     * Quick file upload (basic validation and storage)
     */
    public function quickUpload(UploadedFile $file, string $subdirectory = ''): array
    {
        return $this->processUploadedFile($file, [
            'subdirectory' => $subdirectory,
            'extract_text' => false,
            'process_archive' => false,
        ]);
    }
    
    /**
     * Secure file upload (full validation and processing)
     */
    public function secureUpload(UploadedFile $file, string $subdirectory = ''): array
    {
        return $this->processUploadedFile($file, [
            'subdirectory' => $subdirectory,
            'extract_text' => true,
            'process_archive' => true,
            'remove_metadata' => true,
        ]);
    }
    
    /**
     * Get file URL
     */
    public function getFileUrl(string $filePath): string
    {
        $disk = Config::get('file.storage.disk', 'public');
        return Storage::disk($disk)->url($filePath);
    }
    
    /**
     * Delete file
     */
    public function deleteFile(string $filePath): bool
    {
        $disk = Config::get('file.storage.disk', 'public');
        if (!Storage::disk($disk)->exists($filePath)) {
            $this->logFileDeletion($filePath, false);
            return false;
        }
        $deleted = Storage::disk($disk)->delete($filePath);
        $this->logFileDeletion($filePath, $deleted);
        return $deleted;
    }
    
    /**
     * Get file information
     */
    public function getFileInfo(string $filePath): array
    {
        $disk = Config::get('file.storage.disk', 'public');
        
        if (!Storage::disk($disk)->exists($filePath)) {
            return [
                'exists' => false,
                'message' => 'File not found',
            ];
        }
        
        $fullPath = Storage::disk($disk)->path($filePath);
        
        return [
            'exists' => true,
            'size' => Storage::disk($disk)->size($filePath),
            'last_modified' => Storage::disk($disk)->lastModified($filePath),
            'mime_type' => Storage::disk($disk)->mimeType($filePath),
            'url' => $this->getFileUrl($filePath),
            'full_path' => $fullPath,
        ];
    }
    
    /**
     * Build storage path
     */
    protected function buildStoragePath(string $filename, array $options = []): string
    {
        $basePath = Config::get('file.storage.path', 'files');
        $subdirectory = $options['subdirectory'] ?? '';
        
        $path = $basePath;
        
        // Add subdirectory if specified
        if (!empty($subdirectory)) {
            $path .= '/' . trim($subdirectory, '/');
        }
        
        // Organize by date if enabled
        if (Config::get('file.storage.organize_by_date', true)) {
            $path .= '/' . date('Y/m/d');
        }
        
        // Organize by type if enabled
        if (Config::get('file.storage.organize_by_type', true)) {
            $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
            $fileTypes = Config::get('file.file_types', []);
            
            foreach ($fileTypes as $type => $config) {
                if (in_array($extension, $config['extensions'])) {
                    $path .= '/' . $type;
                    break;
                }
            }
        }
        
        return $path . '/' . $filename;
    }
    
    /**
     * Check if file is an archive
     */
    protected function isArchiveFile(UploadedFile $file): bool
    {
        $extension = strtolower($file->getClientOriginalExtension());
        $archiveExtensions = ['zip', 'rar', '7z'];
        
        return in_array($extension, $archiveExtensions);
    }
    
    /**
     * Set performance limits
     */
    protected function setPerformanceLimits(): void
    {
        $memoryLimit = Config::get('file.performance.memory_limit', '512M');
        $executionTime = Config::get('file.performance.max_execution_time', 300);
        
        ini_set('memory_limit', $memoryLimit);
        set_time_limit($executionTime);
    }
}
