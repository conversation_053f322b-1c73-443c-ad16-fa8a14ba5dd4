<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BlogComment;
use App\Services\ActivityLogger;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class CommentController extends Controller
{
    protected ActivityLogger $activityLogger;

    public function __construct(ActivityLogger $activityLogger)
    {
        $this->activityLogger = $activityLogger;
        
        // Require admin authentication
        $this->middleware('auth');
        // Add admin role check middleware here if you have role-based access
    }

    /**
     * Display pending comments for moderation.
     */
    public function index(Request $request)
    {
        $status = $request->get('status', 'pending');
        
        $query = BlogComment::with(['blogPost', 'user', 'approvedBy', 'deletedBy'])
            ->where('is_deleted', false);

        switch ($status) {
            case 'pending':
                $query->where('is_approved', false);
                break;
            case 'approved':
                $query->where('is_approved', true);
                break;
            case 'flagged':
                $query->where('is_flagged', true);
                break;
            case 'all':
                // No additional filter
                break;
        }

        $comments = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('admin.comments.index', compact('comments', 'status'));
    }

    /**
     * Show comment details for moderation.
     */
    public function show(BlogComment $comment)
    {
        $comment->load(['blogPost', 'user', 'replies.user', 'approvedBy', 'deletedBy']);
        
        return view('admin.comments.show', compact('comment'));
    }

    /**
     * Approve a comment.
     */
    public function approve(Request $request, BlogComment $comment): JsonResponse
    {
        try {
            $notes = $request->input('notes');
            
            if ($comment->approve(Auth::id(), $notes)) {
                // Log the approval
                $this->activityLogger->log(
                    'comment_approved',
                    'BlogComment',
                    $comment->id,
                    [
                        'approved_by' => Auth::user()->first_name . ' ' . Auth::user()->last_name,
                        'blog_post_title' => $comment->blogPost->title,
                        'comment_content' => \Str::limit($comment->content, 100),
                        'admin_notes' => $notes,
                    ]
                );

                return response()->json([
                    'success' => true,
                    'message' => 'Comment approved successfully.'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to approve comment.'
            ], 500);

        } catch (\Exception $e) {
            \Log::error('Comment approval failed', [
                'comment_id' => $comment->id,
                'admin_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while approving the comment.'
            ], 500);
        }
    }

    /**
     * Reject/delete a comment.
     */
    public function reject(Request $request, BlogComment $comment): JsonResponse
    {
        try {
            $reason = $request->input('reason');
            
            if ($comment->reject(Auth::id(), $reason)) {
                // Log the rejection
                $this->activityLogger->log(
                    'comment_rejected',
                    'BlogComment',
                    $comment->id,
                    [
                        'rejected_by' => Auth::user()->first_name . ' ' . Auth::user()->last_name,
                        'blog_post_title' => $comment->blogPost->title,
                        'comment_content' => \Str::limit($comment->content, 100),
                        'rejection_reason' => $reason,
                    ]
                );

                return response()->json([
                    'success' => true,
                    'message' => 'Comment rejected successfully.'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to reject comment.'
            ], 500);

        } catch (\Exception $e) {
            \Log::error('Comment rejection failed', [
                'comment_id' => $comment->id,
                'admin_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while rejecting the comment.'
            ], 500);
        }
    }

    /**
     * Bulk approve comments.
     */
    public function bulkApprove(Request $request): JsonResponse
    {
        try {
            $commentIds = $request->input('comment_ids', []);
            $notes = $request->input('notes');
            
            $approved = 0;
            foreach ($commentIds as $commentId) {
                $comment = BlogComment::find($commentId);
                if ($comment && $comment->approve(Auth::id(), $notes)) {
                    $approved++;
                    
                    // Log each approval
                    $this->activityLogger->log(
                        'comment_bulk_approved',
                        'BlogComment',
                        $comment->id,
                        [
                            'approved_by' => Auth::user()->first_name . ' ' . Auth::user()->last_name,
                            'blog_post_title' => $comment->blogPost->title,
                            'admin_notes' => $notes,
                        ]
                    );
                }
            }

            return response()->json([
                'success' => true,
                'message' => "Successfully approved {$approved} comments."
            ]);

        } catch (\Exception $e) {
            \Log::error('Bulk comment approval failed', [
                'comment_ids' => $request->input('comment_ids', []),
                'admin_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred during bulk approval.'
            ], 500);
        }
    }

    /**
     * Bulk reject comments.
     */
    public function bulkReject(Request $request): JsonResponse
    {
        try {
            $commentIds = $request->input('comment_ids', []);
            $reason = $request->input('reason');
            
            $rejected = 0;
            foreach ($commentIds as $commentId) {
                $comment = BlogComment::find($commentId);
                if ($comment && $comment->reject(Auth::id(), $reason)) {
                    $rejected++;
                    
                    // Log each rejection
                    $this->activityLogger->log(
                        'comment_bulk_rejected',
                        'BlogComment',
                        $comment->id,
                        [
                            'rejected_by' => Auth::user()->first_name . ' ' . Auth::user()->last_name,
                            'blog_post_title' => $comment->blogPost->title,
                            'rejection_reason' => $reason,
                        ]
                    );
                }
            }

            return response()->json([
                'success' => true,
                'message' => "Successfully rejected {$rejected} comments."
            ]);

        } catch (\Exception $e) {
            \Log::error('Bulk comment rejection failed', [
                'comment_ids' => $request->input('comment_ids', []),
                'admin_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred during bulk rejection.'
            ], 500);
        }
    }

    /**
     * Get comment statistics for dashboard.
     */
    public function stats(): JsonResponse
    {
        try {
            $stats = [
                'pending' => BlogComment::pending()->count(),
                'approved' => BlogComment::approved()->active()->count(),
                'flagged' => BlogComment::flagged()->count(),
                'total' => BlogComment::active()->count(),
                'today' => BlogComment::whereDate('created_at', today())->count(),
                'this_week' => BlogComment::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
                'this_month' => BlogComment::whereMonth('created_at', now()->month)->count(),
            ];

            return response()->json([
                'success' => true,
                'stats' => $stats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load statistics.'
            ], 500);
        }
    }
}
