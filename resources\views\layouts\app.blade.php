<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="ltr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @auth
    <meta name="user" content="{{ json_encode([
        'id' => auth()->user()->id,
        'name' => auth()->user()->first_name . ' ' . auth()->user()->last_name,
        'first_name' => auth()->user()->first_name,
        'last_name' => auth()->user()->last_name,
        'email' => auth()->user()->email
    ]) }}">
    @endauth

    <!-- SEO Meta Tags -->
    <title>@yield('title', __('common.company_name') . ' - ' . __('common.company_tagline'))</title>
    <meta name="description" content="@yield('meta_description', __('common.company_description'))">
    <meta name="keywords" content="@yield('meta_keywords', 'digital agency, web development, mobile apps, e-commerce')">
    <meta name="author" content="{{ __('common.company_name') }}">

    <!-- 2025 SEO Best Practices -->
    <meta name="theme-color" content="#1e40af">
    <meta name="color-scheme" content="light dark">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="referrer" content="strict-origin-when-cross-origin">
    <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1">
    <meta name="content-language" content="{{ app()->getLocale() }}">

    <!-- Canonical URL -->
    <link rel="canonical" href="{{ url()->current() }}">

    <!-- Hreflang for multilingual SEO -->
    @foreach(['en', 'fr', 'es'] as $locale)
        <link rel="alternate" hreflang="{{ $locale }}" href="{{ url('/' . $locale . request()->getPathInfo()) }}">
    @endforeach
    <link rel="alternate" hreflang="x-default" href="{{ url('/en' . request()->getPathInfo()) }}">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="@yield('title', __('common.company_name') . ' - ' . __('common.company_tagline'))">
    <meta property="og:description" content="@yield('meta_description', __('common.company_description'))">
    <meta property="og:type" content="@yield('og_type', 'website')">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:site_name" content="{{ __('common.company_name') }}">
    <meta property="og:locale" content="{{ str_replace('-', '_', app()->getLocale()) }}">
    <meta property="og:image" content="@yield('og_image', asset('images/og-image-default.jpg'))">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:alt" content="@yield('og_image_alt', __('common.company_name') . ' - Digital Agency Excellence')">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@chisolution">
    <meta name="twitter:creator" content="@chisolution">
    <meta name="twitter:title" content="@yield('title', __('common.company_name') . ' - ' . __('common.company_tagline'))">
    <meta name="twitter:description" content="@yield('meta_description', __('common.company_description'))">
    <meta name="twitter:image" content="@yield('twitter_image', asset('images/twitter-card-default.jpg'))">
    <meta name="twitter:image:alt" content="@yield('twitter_image_alt', __('common.company_name') . ' - Digital Agency Excellence')">

    <!-- Additional SEO Meta Tags -->
    <meta name="application-name" content="{{ __('common.company_name') }}">
    <meta name="msapplication-TileColor" content="#1e40af">
    <meta name="msapplication-config" content="{{ asset('browserconfig.xml') }}">

    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://www.google-analytics.com">
    <link rel="preconnect" href="https://www.googletagmanager.com">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="@yield('og_title', __('common.company_name'))">
    <meta property="og:description" content="@yield('og_description', __('common.company_description'))">
    <meta property="og:image" content="@yield('og_image', asset('images/og-image.jpg'))">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="{{ __('common.company_name') }}">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="@yield('twitter_title', __('common.company_name'))">
    <meta name="twitter:description" content="@yield('twitter_description', __('common.company_description'))">
    <meta name="twitter:image" content="@yield('twitter_image', asset('images/twitter-image.jpg'))">

    <!-- Hreflang Tags for SEO -->
    <link rel="alternate" hreflang="en" href="{{ App\Http\Middleware\LocalizationMiddleware::switchLocaleUrl('en') }}">
    <link rel="alternate" hreflang="fr" href="{{ App\Http\Middleware\LocalizationMiddleware::switchLocaleUrl('fr') }}">
    <link rel="alternate" hreflang="es" href="{{ App\Http\Middleware\LocalizationMiddleware::switchLocaleUrl('es') }}">
    <link rel="alternate" hreflang="x-default" href="{{ App\Http\Middleware\LocalizationMiddleware::switchLocaleUrl('en') }}">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('images/apple-touch-icon.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('images/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('images/favicon-16x16.png') }}">
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://www.google-analytics.com">
    <link rel="preconnect" href="https://www.googletagmanager.com">

    <!-- Preload critical assets -->
    <link rel="preload" href="{{ asset('js/chat-widget-standalone.js') }}" as="script">
    <link rel="preload" href="{{ asset('images/hero-illustration.svg') }}" as="image">
    <link rel="preload" href="{{ asset('images/about-illustration.svg') }}" as="image">
    <link rel="preload" href="{{ asset('images/logo.svg') }}" as="image">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <!-- Additional Styles -->
    @stack('styles')
    
    <!-- Structured Data -->
    @stack('structured_data')
</head>
<body class="font-inter antialiased bg-white text-gray-900 selection:bg-blue-100 selection:text-blue-900">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-md z-50">
        Skip to main content
    </a>
    
    <!-- Language & Currency Switcher (Mobile) -->
    <div id="mobile-switchers" class="fixed top-0 left-0 w-full bg-gray-50 border-b border-gray-200 z-40 transform -translate-y-full transition-transform duration-300 lg:hidden">
        <div class="container mx-auto px-4 py-2 flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <!-- Language Switcher -->
                <div class="relative">
                    <select class="text-sm border-0 bg-transparent focus:ring-0" onchange="changeLanguage(this.value)">
                        <option value="en" {{ app()->getLocale() === 'en' ? 'selected' : '' }}>English</option>
                        <option value="fr" {{ app()->getLocale() === 'fr' ? 'selected' : '' }}>Français</option>
                        <option value="es" {{ app()->getLocale() === 'es' ? 'selected' : '' }}>Español</option>
                    </select>
                </div>
                
                <!-- Currency Switcher -->
                <div class="relative">
                    <select class="text-sm border-0 bg-transparent focus:ring-0" onchange="changeCurrency(this.value)">
                        <option value="ZAR">ZAR (R)</option>
                        <option value="USD">USD ($)</option>
                        <option value="EUR">EUR (€)</option>
                        <option value="GBP">GBP (£)</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Header -->
    @include('partials.header')
    
    <!-- Main Content -->
    <main id="main-content" class="min-h-screen">
        @yield('content')
    </main>
    
    <!-- Footer -->
    @include('partials.footer')
    
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50 hidden">
        <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p class="mt-4 text-gray-600">{{ __('common.loading') }}</p>
        </div>
    </div>
    
    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Chat Widget (will be initialized by JavaScript) -->
    <div id="chat-widget-container"></div>

    <!-- Debug Script -->
    <script>
        console.log('Inline script loaded');
        console.log('Current URL:', window.location.href);
        console.log('Pathname:', window.location.pathname);
    </script>

    <!-- Inline Chat Widget Script -->
    <script src="{{ asset('js/chat-widget-standalone.js') }}"></script>

    <!-- Debug Script After -->
    <!-- <script>
        console.log('After chat widget script');
        setTimeout(() => {
            console.log('Chat widget after timeout:', window.chatWidget);
            console.log('Toggle function:', window.toggleChat);

            // Add test button if not in admin
            if (!window.location.pathname.includes('/admin')) {
                const testBtn = document.createElement('button');
                testBtn.textContent = 'Test Chat Toggle';
                testBtn.style.cssText = 'position: fixed; top: 10px; right: 10px; z-index: 10000; background: red; color: white; padding: 10px; border: none; border-radius: 5px; cursor: pointer;';
                testBtn.onclick = function() {
                    console.log('Test button clicked');
                    if (window.toggleChat) {
                        window.toggleChat();
                    } else {
                        console.error('toggleChat function not found');
                    }
                };
                document.body.appendChild(testBtn);
            }
        }, 1000);
    </script> -->
    
    <!-- Global JavaScript Configuration -->
    <script>
        // Global app configuration
        window.appConfig = {
            routes: {
                cartAdd: '{{ route("cart.add", ["locale" => app()->getLocale()]) }}',
                cartCount: '{{ route("cart.count", ["locale" => app()->getLocale()]) }}',
                cartIndex: '{{ route("cart.index", ["locale" => app()->getLocale()]) }}',
                cartUpdate: '{{ url(app()->getLocale() . "/cart/update") }}',
                cartRemove: '{{ url(app()->getLocale() . "/cart/remove") }}',
                cartClear: '{{ route("cart.clear", ["locale" => app()->getLocale()]) }}',
                cartCoupon: '{{ route("cart.coupon", ["locale" => app()->getLocale()]) }}',
                cartCouponRemove: '{{ route("cart.coupon.remove", ["locale" => app()->getLocale()]) }}',
                checkoutIndex: '{{ route("checkout.index", ["locale" => app()->getLocale()]) }}',
                checkoutProcess: '{{ route("checkout.process", ["locale" => app()->getLocale()]) }}',
            },
            locale: '{{ app()->getLocale() }}',
            csrfToken: '{{ csrf_token() }}'
        };

        // Language switcher
        function changeLanguage(locale) {
            const currentPath = window.location.pathname;
            const supportedLocales = ['en', 'fr', 'es'];

            // Check if current path starts with a locale
            const pathSegments = currentPath.split('/').filter(segment => segment !== '');
            let newPath = '';

            if (pathSegments.length > 0 && supportedLocales.includes(pathSegments[0])) {
                // Replace existing locale
                pathSegments[0] = locale;
                newPath = '/' + pathSegments.join('/');
            } else {
                // Add locale to path
                newPath = '/' + locale + currentPath;
            }

            // Preserve query parameters and hash
            const search = window.location.search;
            const hash = window.location.hash;

            window.location.href = newPath + search + hash;
        }
        
        // Currency switcher
        function changeCurrency(currency) {
            // Store in localStorage and reload
            localStorage.setItem('preferred_currency', currency);
            window.location.reload();
        }
        
        // Toast notification system
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            const bgColor = {
                'success': 'bg-green-500',
                'error': 'bg-red-500',
                'warning': 'bg-yellow-500',
                'info': 'bg-blue-500'
            }[type] || 'bg-blue-500';
            
            toast.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full`;
            toast.innerHTML = `
                <div class="flex items-center justify-between">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            `;
            
            document.getElementById('toast-container').appendChild(toast);
            
            // Animate in
            setTimeout(() => toast.classList.remove('translate-x-full'), 100);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => toast.remove(), 300);
            }, 5000);
        }
        
        // Loading overlay functions
        function showLoading() {
            document.getElementById('loading-overlay').classList.remove('hidden');
        }
        
        function hideLoading() {
            document.getElementById('loading-overlay').classList.add('hidden');
        }
        
        // CSRF token for AJAX requests
        window.Laravel = {
            csrfToken: '{{ csrf_token() }}'
        };
        
        // Set up AJAX defaults
        if (typeof axios !== 'undefined') {
            axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
            axios.defaults.headers.common['X-CSRF-TOKEN'] = window.Laravel.csrfToken;
        }
    </script>

    <!-- Cart State Management -->
    <script src="{{ asset('js/cart-state-manager.js') }}"></script>
    <script src="{{ asset('js/cart-utils.js') }}"></script>

    <!-- Additional Scripts -->
    @stack('scripts')

    <!-- Footer Newsletter AJAX -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const footerNewsletterForm = document.getElementById('footer-newsletter-form');
        const footerNewsletterMessage = document.getElementById('footer-newsletter-message');
        const footerNewsletterMessageContent = document.getElementById('footer-newsletter-message-content');

        if (footerNewsletterForm) {
            footerNewsletterForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const email = this.querySelector('input[name="email"]').value;
                const consent = this.querySelector('input[name="consent"]').checked;
                const submitBtn = document.getElementById('footer-newsletter-submit');
                const originalText = submitBtn.textContent;

                // Basic validation
                if (!email || !consent) {
                    showFooterNewsletterMessage('Please fill in your email and agree to receive marketing emails.', 'error');
                    return;
                }

                // Show loading state
                submitBtn.innerHTML = `
                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Subscribing...
                `;
                submitBtn.disabled = true;

                // Hide previous messages
                footerNewsletterMessage.classList.add('hidden');

                // Submit form via AJAX
                fetch(this.action, {
                    method: 'POST',
                    body: new FormData(this),
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showFooterNewsletterMessage(data.message, 'success');
                        // Clear form on success
                        this.reset();
                    } else {
                        let errorMessage = data.message || 'An error occurred. Please try again.';

                        // Show field errors if any
                        if (data.errors) {
                            const errorList = Object.values(data.errors).flat();
                            if (errorList.length > 0) {
                                errorMessage += ' ' + errorList.join(' ');
                            }
                        }

                        showFooterNewsletterMessage(errorMessage, data.type === 'info' ? 'info' : 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showFooterNewsletterMessage('An unexpected error occurred. Please try again later.', 'error');
                })
                .finally(() => {
                    // Restore button
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                });
            });
        }

        function showFooterNewsletterMessage(message, type) {
            footerNewsletterMessage.classList.remove('hidden');

            // Remove existing classes
            footerNewsletterMessageContent.className = 'p-3 rounded-lg text-sm';

            // Add appropriate classes based on type
            if (type === 'success') {
                footerNewsletterMessageContent.classList.add('bg-green-100', 'border', 'border-green-400', 'text-green-700');
            } else if (type === 'info') {
                footerNewsletterMessageContent.classList.add('bg-blue-100', 'border', 'border-blue-400', 'text-blue-700');
            } else {
                footerNewsletterMessageContent.classList.add('bg-red-100', 'border', 'border-red-400', 'text-red-700');
            }

            footerNewsletterMessageContent.textContent = message;

            // Auto-hide success messages after 5 seconds
            if (type === 'success') {
                setTimeout(() => {
                    footerNewsletterMessage.classList.add('hidden');
                }, 5000);
            }
        }
    });
    </script>

    <!-- Analytics -->
    @if(config('app.env') === 'production')
        <!-- Google Analytics -->
        <script async src="https://www.googletagmanager.com/gtag/js?id={{ config('services.google.analytics_id') }}"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '{{ config('services.google.analytics_id') }}');
        </script>
    @endif
</body>
</html>
