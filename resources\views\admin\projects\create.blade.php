@extends('layouts.dashboard')

@section('title', 'Create Project - Admin Dashboard')
@section('page_title', 'Create Project')

@section('content')
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
            <nav class="flex items-center space-x-2 text-sm text-gray-500 mb-2">
                <a href="{{ route('admin.projects.index') }}" class="hover:text-gray-700">Projects</a>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <span class="text-gray-900">Create</span>
            </nav>
            <h1 class="text-2xl font-bold text-gray-900">Create Project</h1>
            <p class="text-gray-600">Add a new project to your portfolio</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('admin.projects.index') }}"
               class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-lg font-medium text-white hover:bg-gray-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                Back to Projects
            </a>
        </div>
    </div>

    <!-- Form Messages -->
    <div id="form-messages" class="hidden"></div>

    <!-- Create Form -->
    <form id="project-form" action="{{ route('admin.projects.store') }}" method="POST" enctype="multipart/form-data" class="space-y-6">
        @csrf

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information -->
                <div class="bg-white rounded-lg border border-neutral-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
                    
                    <div class="space-y-4">
                        <!-- Title -->
                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                                Project Title <span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   id="title" 
                                   name="title" 
                                   value="{{ old('title') }}"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('title') border-red-500 @enderror"
                                   placeholder="Enter project title"
                                   required>
                            @error('title')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                Short Description <span class="text-red-500">*</span>
                            </label>
                            <textarea id="description" 
                                      name="description" 
                                      rows="3"
                                      maxlength="1000"
                                      class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('description') border-red-500 @enderror"
                                      placeholder="Brief project description"
                                      required>{{ old('description') }}</textarea>
                            <div class="flex justify-between mt-1">
                                <span class="text-xs text-gray-500">Brief description for listings</span>
                                <span id="description-count" class="text-xs text-gray-500">0/1000</span>
                            </div>
                            @error('description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Content -->
                        <div>
                            <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
                                Detailed Content
                            </label>
                            <textarea id="content" 
                                      name="content" 
                                      rows="8"
                                      maxlength="50000"
                                      class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('content') border-red-500 @enderror"
                                      placeholder="Detailed project description, requirements, deliverables, etc.">{{ old('content') }}</textarea>
                            <div class="flex justify-between mt-1">
                                <span class="text-xs text-gray-500">Detailed project information</span>
                                <span id="content-count" class="text-xs text-gray-500">0/50000</span>
                            </div>
                            @error('content')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Project URL -->
                        <div>
                            <label for="project_url" class="block text-sm font-medium text-gray-700 mb-2">
                                Project URL
                            </label>
                            <input type="url" 
                                   id="project_url" 
                                   name="project_url" 
                                   value="{{ old('project_url') }}"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('project_url') border-red-500 @enderror"
                                   placeholder="https://example.com">
                            @error('project_url')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Client Information -->
                <div class="bg-white rounded-lg border border-neutral-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Client Information</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Client Selection -->
                        <div>
                            <label for="client_id" class="block text-sm font-medium text-gray-700 mb-2">
                                Select Client
                            </label>
                            <select id="client_id" 
                                    name="client_id" 
                                    class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('client_id') border-red-500 @enderror">
                                <option value="">Select existing client (optional)</option>
                                @foreach($clients as $client)
                                    <option value="{{ $client->id }}" 
                                            data-name="{{ $client->first_name }} {{ $client->last_name }}"
                                            {{ old('client_id') == $client->id ? 'selected' : '' }}>
                                        {{ $client->first_name }} {{ $client->last_name }} ({{ $client->email }})
                                    </option>
                                @endforeach
                            </select>
                            @error('client_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Client Name -->
                        <div>
                            <label for="client_name" class="block text-sm font-medium text-gray-700 mb-2">
                                Client Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   id="client_name" 
                                   name="client_name" 
                                   value="{{ old('client_name') }}"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('client_name') border-red-500 @enderror"
                                   placeholder="Client or company name"
                                   required>
                            @error('client_name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Project Details -->
                <div class="bg-white rounded-lg border border-neutral-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Project Details</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Service -->
                        <div>
                            <label for="service_id" class="block text-sm font-medium text-gray-700 mb-2">
                                Related Service
                            </label>
                            <select id="service_id" 
                                    name="service_id" 
                                    class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('service_id') border-red-500 @enderror">
                                <option value="">Select service (optional)</option>
                                @foreach($services as $service)
                                    <option value="{{ $service->id }}" {{ old('service_id') == $service->id ? 'selected' : '' }}>
                                        {{ $service->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('service_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Status -->
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                                Status <span class="text-red-500">*</span>
                            </label>
                            <select id="status" 
                                    name="status" 
                                    class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('status') border-red-500 @enderror"
                                    required>
                                @foreach($statuses as $value => $label)
                                    <option value="{{ $value }}" {{ old('status', 'planning') == $value ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                            @error('status')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Priority -->
                        <div>
                            <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">
                                Priority
                            </label>
                            <select id="priority" 
                                    name="priority" 
                                    class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('priority') border-red-500 @enderror">
                                <option value="">Select priority</option>
                                <option value="low" {{ old('priority') == 'low' ? 'selected' : '' }}>Low</option>
                                <option value="medium" {{ old('priority') == 'medium' ? 'selected' : '' }}>Medium</option>
                                <option value="high" {{ old('priority') == 'high' ? 'selected' : '' }}>High</option>
                                <option value="urgent" {{ old('priority') == 'urgent' ? 'selected' : '' }}>Urgent</option>
                            </select>
                            @error('priority')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Currency -->
                        <div>
                            <label for="currency_code" class="block text-sm font-medium text-gray-700 mb-2">
                                Currency
                            </label>
                            <select id="currency_code" 
                                    name="currency_code" 
                                    class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('currency_code') border-red-500 @enderror">
                                <option value="">Select currency</option>
                                <option value="USD" {{ old('currency_code', 'USD') == 'USD' ? 'selected' : '' }}>USD - US Dollar</option>
                                <option value="EUR" {{ old('currency_code') == 'EUR' ? 'selected' : '' }}>EUR - Euro</option>
                                <option value="GBP" {{ old('currency_code') == 'GBP' ? 'selected' : '' }}>GBP - British Pound</option>
                                <option value="ZAR" {{ old('currency_code') == 'ZAR' ? 'selected' : '' }}>ZAR - South African Rand</option>
                            </select>
                            @error('currency_code')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Timeline & Budget -->
                <div class="bg-white rounded-lg border border-neutral-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Timeline & Budget</h3>
                    
                    <div class="space-y-4">
                        <!-- Start Date -->
                        <div>
                            <label for="start_date" class="block text-sm font-medium text-gray-700 mb-2">
                                Start Date
                            </label>
                            <input type="date" 
                                   id="start_date" 
                                   name="start_date" 
                                   value="{{ old('start_date') }}"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('start_date') border-red-500 @enderror">
                            @error('start_date')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- End Date -->
                        <div>
                            <label for="end_date" class="block text-sm font-medium text-gray-700 mb-2">
                                End Date
                            </label>
                            <input type="date" 
                                   id="end_date" 
                                   name="end_date" 
                                   value="{{ old('end_date') }}"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('end_date') border-red-500 @enderror">
                            @error('end_date')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Estimated Hours -->
                        <div>
                            <label for="estimated_hours" class="block text-sm font-medium text-gray-700 mb-2">
                                Estimated Hours
                            </label>
                            <input type="number" 
                                   id="estimated_hours" 
                                   name="estimated_hours" 
                                   value="{{ old('estimated_hours') }}"
                                   min="0"
                                   step="0.5"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('estimated_hours') border-red-500 @enderror"
                                   placeholder="0.0">
                            @error('estimated_hours')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Actual Hours -->
                        <div>
                            <label for="actual_hours" class="block text-sm font-medium text-gray-700 mb-2">
                                Actual Hours
                            </label>
                            <input type="number" 
                                   id="actual_hours" 
                                   name="actual_hours" 
                                   value="{{ old('actual_hours') }}"
                                   min="0"
                                   step="0.5"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('actual_hours') border-red-500 @enderror"
                                   placeholder="0.0">
                            @error('actual_hours')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Hourly Rate -->
                        <div>
                            <label for="hourly_rate" class="block text-sm font-medium text-gray-700 mb-2">
                                Hourly Rate
                            </label>
                            <input type="number" 
                                   id="hourly_rate" 
                                   name="hourly_rate" 
                                   value="{{ old('hourly_rate') }}"
                                   min="0"
                                   step="0.01"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('hourly_rate') border-red-500 @enderror"
                                   placeholder="0.00">
                            @error('hourly_rate')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Total Amount -->
                        <div>
                            <label for="total_amount" class="block text-sm font-medium text-gray-700 mb-2">
                                Total Amount
                            </label>
                            <input type="number" 
                                   id="total_amount" 
                                   name="total_amount" 
                                   value="{{ old('total_amount') }}"
                                   min="0"
                                   step="0.01"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('total_amount') border-red-500 @enderror"
                                   placeholder="0.00">
                            <p class="mt-1 text-xs text-gray-500">Auto-calculated from hourly rate × actual hours</p>
                            @error('total_amount')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Featured Image -->
                <div class="bg-white rounded-lg border border-neutral-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Featured Image</h3>

                    <div>
                        <label for="featured_image" class="block text-sm font-medium text-gray-700 mb-2">
                            Project Image
                        </label>
                        <input type="file"
                               id="featured_image"
                               name="featured_image"
                               accept="image/*"
                               class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100 @error('featured_image') border-red-500 @enderror">
                        <p class="mt-1 text-xs text-gray-500">Recommended size: 1200x800px. Max file size: 5MB. Formats: JPEG, PNG, JPG, GIF, WebP</p>
                        @error('featured_image')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- SEO Settings -->
                <div class="bg-white rounded-lg border border-neutral-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">SEO Settings</h3>

                    <div class="space-y-4">
                        <!-- Meta Title -->
                        <div>
                            <label for="meta_title" class="block text-sm font-medium text-gray-700 mb-2">
                                Meta Title
                            </label>
                            <input type="text"
                                   id="meta_title"
                                   name="meta_title"
                                   value="{{ old('meta_title') }}"
                                   maxlength="255"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('meta_title') border-red-500 @enderror"
                                   placeholder="SEO title for search engines">
                            @error('meta_title')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Meta Description -->
                        <div>
                            <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">
                                Meta Description
                            </label>
                            <textarea id="meta_description"
                                      name="meta_description"
                                      rows="3"
                                      maxlength="500"
                                      class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('meta_description') border-red-500 @enderror"
                                      placeholder="Brief description for search engines (max 500 characters)">{{ old('meta_description') }}</textarea>
                            <div class="flex justify-between mt-1">
                                <span class="text-xs text-gray-500">SEO description</span>
                                <span id="meta-description-count" class="text-xs text-gray-500">0/500</span>
                            </div>
                            @error('meta_description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Meta Keywords -->
                        <div>
                            <label for="meta_keywords" class="block text-sm font-medium text-gray-700 mb-2">
                                Meta Keywords
                            </label>
                            <input type="text"
                                   id="meta_keywords"
                                   name="meta_keywords"
                                   value="{{ old('meta_keywords') }}"
                                   maxlength="500"
                                   class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent @error('meta_keywords') border-red-500 @enderror"
                                   placeholder="keyword1, keyword2, keyword3">
                            <p class="mt-1 text-xs text-gray-500">Separate keywords with commas</p>
                            @error('meta_keywords')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Publication Settings -->
                <div class="bg-white rounded-lg border border-neutral-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Publication Settings</h3>

                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox"
                                   id="is_published"
                                   name="is_published"
                                   value="1"
                                   {{ old('is_published') ? 'checked' : '' }}
                                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded">
                            <label for="is_published" class="ml-2 block text-sm text-gray-900">
                                Published (visible on website)
                            </label>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox"
                                   id="is_featured"
                                   name="is_featured"
                                   value="1"
                                   {{ old('is_featured') ? 'checked' : '' }}
                                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded">
                            <label for="is_featured" class="ml-2 block text-sm text-gray-900">
                                Featured (show in featured sections)
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-end space-x-4 pt-6 border-t border-neutral-200">
            <a href="{{ route('admin.projects.index') }}"
               class="px-6 py-2 border border-neutral-300 text-neutral-700 rounded-lg hover:bg-neutral-50 transition-colors duration-200">
                Cancel
            </a>
            <button type="submit"
                    id="submit-btn"
                    class="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                <span class="submit-text">Create Project</span>
                <span class="loading-text hidden">Creating...</span>
            </button>
        </div>
    </form>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Character counters
    const descriptionTextarea = document.getElementById('description');
    const descriptionCounter = document.getElementById('description-count');
    const contentTextarea = document.getElementById('content');
    const contentCounter = document.getElementById('content-count');
    const metaDescriptionTextarea = document.getElementById('meta_description');
    const metaDescriptionCounter = document.getElementById('meta-description-count');

    function updateCounter(textarea, counter) {
        const count = textarea.value.length;
        const max = textarea.getAttribute('maxlength');
        counter.textContent = `${count}/${max}`;

        if (count > max * 0.9) {
            counter.classList.add('text-red-500');
            counter.classList.remove('text-gray-500');
        } else {
            counter.classList.add('text-gray-500');
            counter.classList.remove('text-red-500');
        }
    }

    // Initialize counters
    updateCounter(descriptionTextarea, descriptionCounter);
    updateCounter(contentTextarea, contentCounter);
    updateCounter(metaDescriptionTextarea, metaDescriptionCounter);

    // Add event listeners
    descriptionTextarea.addEventListener('input', () => updateCounter(descriptionTextarea, descriptionCounter));
    contentTextarea.addEventListener('input', () => updateCounter(contentTextarea, contentCounter));
    metaDescriptionTextarea.addEventListener('input', () => updateCounter(metaDescriptionTextarea, metaDescriptionCounter));

    // Auto-fill client name when client is selected
    const clientSelect = document.getElementById('client_id');
    const clientNameInput = document.getElementById('client_name');

    clientSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value && selectedOption.dataset.name) {
            clientNameInput.value = selectedOption.dataset.name;
        }
    });

    // Auto-calculate total amount when hourly rate or actual hours change
    const hourlyRateInput = document.getElementById('hourly_rate');
    const actualHoursInput = document.getElementById('actual_hours');
    const totalAmountInput = document.getElementById('total_amount');

    function calculateTotal() {
        const hourlyRate = parseFloat(hourlyRateInput.value) || 0;
        const actualHours = parseFloat(actualHoursInput.value) || 0;
        const total = hourlyRate * actualHours;

        if (total > 0) {
            totalAmountInput.value = total.toFixed(2);
        }
    }

    hourlyRateInput.addEventListener('input', calculateTotal);
    actualHoursInput.addEventListener('input', calculateTotal);

    // AJAX Form Submission
    const form = document.getElementById('project-form');
    const submitBtn = document.getElementById('submit-btn');
    const submitText = submitBtn.querySelector('.submit-text');
    const loadingText = submitBtn.querySelector('.loading-text');
    const messagesContainer = document.getElementById('form-messages');

    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        // Show loading state
        submitBtn.disabled = true;
        submitText.classList.add('hidden');
        loadingText.classList.remove('hidden');

        // Clear previous messages
        messagesContainer.innerHTML = '';
        messagesContainer.classList.add('hidden');

        // Clear previous error states
        document.querySelectorAll('.border-red-500').forEach(el => {
            el.classList.remove('border-red-500');
        });
        document.querySelectorAll('.text-red-600').forEach(el => {
            if (el.classList.contains('error-message')) {
                el.remove();
            }
        });

        try {
            const formData = new FormData(form);

            const response = await fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (data.success) {
                // Show success message
                messagesContainer.innerHTML = `
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-green-800">${data.message}</p>
                            </div>
                        </div>
                    </div>
                `;
                messagesContainer.classList.remove('hidden');

                // Redirect after a short delay
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 1500);
            } else {
                // Show error message
                messagesContainer.innerHTML = `
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-red-800">${data.message}</p>
                            </div>
                        </div>
                    </div>
                `;
                messagesContainer.classList.remove('hidden');

                // Show field errors
                if (data.errors) {
                    Object.keys(data.errors).forEach(field => {
                        const input = document.querySelector(`[name="${field}"]`);
                        if (input) {
                            input.classList.add('border-red-500');

                            // Add error message
                            const errorDiv = document.createElement('p');
                            errorDiv.className = 'mt-1 text-sm text-red-600 error-message';
                            errorDiv.textContent = data.errors[field][0];
                            input.parentNode.appendChild(errorDiv);
                        }
                    });
                }
            }
        } catch (error) {
            console.error('Error:', error);
            messagesContainer.innerHTML = `
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-red-800">An error occurred. Please try again.</p>
                        </div>
                    </div>
                </div>
            `;
            messagesContainer.classList.remove('hidden');
        } finally {
            // Reset button state
            submitBtn.disabled = false;
            submitText.classList.remove('hidden');
            loadingText.classList.add('hidden');
        }
    });
});
</script>
@endpush
