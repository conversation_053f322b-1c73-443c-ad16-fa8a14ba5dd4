<?php

namespace App\Events;

use App\Models\Role;
use App\Models\User;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class RolePermissionsChanged
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Role $role;
    public array $oldPermissions;
    public array $newPermissions;
    public User $changedBy;
    public array $metadata;

    /**
     * Create a new event instance.
     */
    public function __construct(
        Role $role,
        array $oldPermissions,
        array $newPermissions,
        User $changedBy,
        array $metadata = []
    ) {
        $this->role = $role;
        $this->oldPermissions = $oldPermissions;
        $this->newPermissions = $newPermissions;
        $this->changedBy = $changedBy;
        $this->metadata = $metadata;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('admin.permissions'),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'role' => [
                'id' => $this->role->id,
                'name' => $this->role->name,
                'slug' => $this->role->slug,
            ],
            'old_permissions' => $this->oldPermissions,
            'new_permissions' => $this->newPermissions,
            'changed_by' => [
                'id' => $this->changedBy->id,
                'name' => $this->changedBy->first_name . ' ' . $this->changedBy->last_name,
            ],
            'timestamp' => now()->toISOString(),
            'metadata' => $this->metadata,
            'affected_users_count' => $this->role->users()->count(),
        ];
    }

    /**
     * Get the permission changes summary.
     */
    public function getChangesSummary(): array
    {
        $added = [];
        $removed = [];
        $modified = [];

        // Find added resources
        foreach ($this->newPermissions as $resource => $actions) {
            if (!isset($this->oldPermissions[$resource])) {
                $added[$resource] = $actions;
            } else {
                // Check for added actions within existing resources
                $oldActions = $this->oldPermissions[$resource];
                $newActions = array_diff($actions, $oldActions);
                if (!empty($newActions)) {
                    $modified[$resource]['added'] = $newActions;
                }

                // Check for removed actions within existing resources
                $removedActions = array_diff($oldActions, $actions);
                if (!empty($removedActions)) {
                    $modified[$resource]['removed'] = $removedActions;
                }
            }
        }

        // Find removed resources
        foreach ($this->oldPermissions as $resource => $actions) {
            if (!isset($this->newPermissions[$resource])) {
                $removed[$resource] = $actions;
            }
        }

        return [
            'added' => $added,
            'removed' => $removed,
            'modified' => $modified,
        ];
    }
}
