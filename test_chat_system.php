<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\AiTrainingData;
use App\Models\ChatSystemSetting;
use App\Services\ChatAIService;
use Illuminate\Support\Facades\DB;

echo "=== Chat System Test ===\n\n";

try {
    // 1. Check AI training data
    $trainingCount = AiTrainingData::count();
    echo "1. AI Training Data: {$trainingCount} records\n";
    
    if ($trainingCount > 0) {
        $sampleData = AiTrainingData::first();
        echo "   Sample: '{$sampleData->input_text}' -> '{$sampleData->expected_response}'\n";
    }
    
    // 2. Check AI enabled status
    $aiEnabled = ChatSystemSetting::isAiEnabled();
    echo "2. AI Enabled: " . ($aiEnabled ? 'YES' : 'NO') . "\n";
    
    // 3. Check chat enabled status
    $chatEnabled = ChatSystemSetting::isChatEnabled();
    echo "3. Chat Enabled: " . ($chatEnabled ? 'YES' : 'NO') . "\n";
    
    // 4. Test AI service
    $aiService = app(ChatAIService::class);
    $aiAvailable = $aiService->isAIAvailable();
    echo "4. AI Service Available: " . ($aiAvailable ? 'YES' : 'NO') . "\n";
    
    // 5. Create test room and message
    echo "\n5. Testing AI Response Generation:\n";
    
    $testRoom = ChatRoom::create([
        'uuid' => 'test-' . uniqid(),
        'type' => 'visitor',
        'status' => 'active',
        'title' => 'Test Room',
        'language' => 'en',
        'priority' => 1,
    ]);
    
    echo "   Created test room: {$testRoom->uuid}\n";
    
    // Test different messages
    $testMessages = [
        'hello',
        'hi',
        'what services do you offer',
        'who are you',
        'i need help'
    ];
    
    foreach ($testMessages as $messageText) {
        echo "\n   Testing message: '{$messageText}'\n";
        
        try {
            $response = $aiService->generateResponse($messageText, $testRoom, []);
            echo "   Response: '{$response['response']}'\n";
            echo "   Confidence: {$response['confidence']}\n";
            echo "   Type: {$response['response_type']}\n";
        } catch (Exception $e) {
            echo "   ERROR: " . $e->getMessage() . "\n";
        }
    }
    
    // Clean up
    $testRoom->delete();
    echo "\n   Cleaned up test room\n";
    
    echo "\n=== Test Complete ===\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
