<?php

namespace App\Services;

use App\Models\ChatRating;
use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CustomerSatisfactionService
{
    /**
     * Get comprehensive customer satisfaction metrics.
     */
    public function getSatisfactionMetrics(array $filters = []): array
    {
        $startDate = $filters['start_date'] ?? now()->subDays(30);
        $endDate = $filters['end_date'] ?? now();
        $staffId = $filters['staff_id'] ?? null;
        $department = $filters['department'] ?? null;

        return [
            'overview' => $this->getOverviewMetrics($startDate, $endDate, $staffId, $department),
            'ratings_breakdown' => $this->getRatingsBreakdown($startDate, $endDate, $staffId, $department),
            'satisfaction_trends' => $this->getSatisfactionTrends($startDate, $endDate, $staffId, $department),
            'feedback_analysis' => $this->getFeedbackAnalysis($startDate, $endDate, $staffId, $department),
            'staff_performance' => $this->getStaffSatisfactionPerformance($startDate, $endDate, $department),
            'response_time_impact' => $this->getResponseTimeImpact($startDate, $endDate, $staffId, $department),
            'resolution_satisfaction' => $this->getResolutionSatisfaction($startDate, $endDate, $staffId, $department),
            'nps_metrics' => $this->getNPSMetrics($startDate, $endDate, $staffId, $department),
        ];
    }

    /**
     * Get overview satisfaction metrics.
     */
    protected function getOverviewMetrics(Carbon $startDate, Carbon $endDate, ?int $staffId, ?string $department): array
    {
        // Create a base query builder function to reuse
        $baseQuery = function() use ($startDate, $endDate, $staffId) {
            $query = ChatRating::whereBetween('created_at', [$startDate, $endDate]);

            if ($staffId) {
                $query->whereHas('chatRoom', function ($q) use ($staffId) {
                    $q->whereHas('assignments', function ($aq) use ($staffId) {
                        $aq->where('assigned_to', $staffId);
                    });
                });
            }

            return $query;
        };

        $totalRatings = $baseQuery()->count();
        $averageRating = $baseQuery()->avg('rating') ?? 0;
        $satisfiedCustomers = $baseQuery()->where('rating', '>=', 4)->count();
        $dissatisfiedCustomers = $baseQuery()->where('rating', '<=', 2)->count();
        
        $satisfactionRate = $totalRatings > 0 ? ($satisfiedCustomers / $totalRatings) * 100 : 0;
        $dissatisfactionRate = $totalRatings > 0 ? ($dissatisfiedCustomers / $totalRatings) * 100 : 0;

        // Calculate response rate (ratings vs total conversations)
        $totalConversations = ChatRoom::whereBetween('created_at', [$startDate, $endDate])->count();
        $responseRate = $totalConversations > 0 ? ($totalRatings / $totalConversations) * 100 : 0;

        return [
            'total_ratings' => $totalRatings,
            'average_rating' => round($averageRating, 2),
            'satisfaction_rate' => round($satisfactionRate, 2),
            'dissatisfaction_rate' => round($dissatisfactionRate, 2),
            'response_rate' => round($responseRate, 2),
            'satisfied_customers' => $satisfiedCustomers,
            'dissatisfied_customers' => $dissatisfiedCustomers,
            'total_conversations' => $totalConversations,
        ];
    }

    /**
     * Get detailed ratings breakdown.
     */
    protected function getRatingsBreakdown(Carbon $startDate, Carbon $endDate, ?int $staffId, ?string $department): array
    {
        $query = ChatRating::whereBetween('created_at', [$startDate, $endDate]);
        
        if ($staffId) {
            $query->whereHas('chatRoom', function ($q) use ($staffId) {
                $q->whereHas('assignments', function ($aq) use ($staffId) {
                    $aq->where('assigned_to', $staffId);
                });
            });
        }

        $ratingsDistribution = $query->select('rating', DB::raw('COUNT(*) as count'))
                                   ->groupBy('rating')
                                   ->orderBy('rating')
                                   ->get()
                                   ->pluck('count', 'rating')
                                   ->toArray();

        // Fill missing ratings with 0
        $fullDistribution = [];
        for ($i = 1; $i <= 5; $i++) {
            $fullDistribution[$i] = $ratingsDistribution[$i] ?? 0;
        }

        // Calculate percentages
        $total = array_sum($fullDistribution);
        $percentageDistribution = [];
        foreach ($fullDistribution as $rating => $count) {
            $percentageDistribution[$rating] = $total > 0 ? round(($count / $total) * 100, 1) : 0;
        }

        return [
            'distribution' => $fullDistribution,
            'percentage_distribution' => $percentageDistribution,
            'total_ratings' => $total,
        ];
    }

    /**
     * Get satisfaction trends over time.
     */
    protected function getSatisfactionTrends(Carbon $startDate, Carbon $endDate, ?int $staffId, ?string $department): array
    {
        $query = ChatRating::whereBetween('created_at', [$startDate, $endDate]);
        
        if ($staffId) {
            $query->whereHas('chatRoom', function ($q) use ($staffId) {
                $q->whereHas('assignments', function ($aq) use ($staffId) {
                    $aq->where('assigned_to', $staffId);
                });
            });
        }

        // Daily trends
        $dailyTrends = $query->select(
                            DB::raw('DATE(created_at) as date'),
                            DB::raw('AVG(rating) as avg_rating'),
                            DB::raw('COUNT(*) as total_ratings'),
                            DB::raw('SUM(CASE WHEN rating >= 4 THEN 1 ELSE 0 END) as satisfied'),
                            DB::raw('SUM(CASE WHEN rating <= 2 THEN 1 ELSE 0 END) as dissatisfied')
                        )
                        ->groupBy(DB::raw('DATE(created_at)'))
                        ->orderBy('date')
                        ->get()
                        ->map(function ($item) {
                            $satisfactionRate = $item->total_ratings > 0 ? 
                                ($item->satisfied / $item->total_ratings) * 100 : 0;
                            
                            return [
                                'date' => $item->date,
                                'avg_rating' => round($item->avg_rating, 2),
                                'total_ratings' => $item->total_ratings,
                                'satisfaction_rate' => round($satisfactionRate, 2),
                                'satisfied' => $item->satisfied,
                                'dissatisfied' => $item->dissatisfied,
                            ];
                        });

        return [
            'daily_trends' => $dailyTrends->toArray(),
            'trend_direction' => $this->calculateTrendDirection($dailyTrends),
        ];
    }

    /**
     * Get feedback analysis from comments.
     */
    protected function getFeedbackAnalysis(Carbon $startDate, Carbon $endDate, ?int $staffId, ?string $department): array
    {
        $query = ChatRating::whereBetween('created_at', [$startDate, $endDate])
                          ->whereNotNull('comment')
                          ->where('comment', '!=', '');
        
        if ($staffId) {
            $query->whereHas('chatRoom', function ($q) use ($staffId) {
                $q->whereHas('assignments', function ($aq) use ($staffId) {
                    $aq->where('assigned_to', $staffId);
                });
            });
        }

        $feedbackWithRatings = $query->select('rating', 'comment', 'created_at')
                                   ->orderBy('created_at', 'desc')
                                   ->get();

        // Categorize feedback by rating
        $feedbackByRating = [
            'positive' => $feedbackWithRatings->where('rating', '>=', 4)->take(10),
            'neutral' => $feedbackWithRatings->where('rating', 3)->take(10),
            'negative' => $feedbackWithRatings->where('rating', '<=', 2)->take(10),
        ];

        // Common keywords analysis (simplified)
        $allComments = $feedbackWithRatings->pluck('comment')->implode(' ');
        $commonWords = $this->extractCommonWords($allComments);

        return [
            'total_feedback_count' => $feedbackWithRatings->count(),
            'feedback_by_rating' => [
                'positive' => $feedbackByRating['positive']->values()->toArray(),
                'neutral' => $feedbackByRating['neutral']->values()->toArray(),
                'negative' => $feedbackByRating['negative']->values()->toArray(),
            ],
            'common_keywords' => $commonWords,
            'feedback_rate' => $this->calculateFeedbackRate($startDate, $endDate, $staffId),
        ];
    }

    /**
     * Get staff satisfaction performance.
     */
    protected function getStaffSatisfactionPerformance(Carbon $startDate, Carbon $endDate, ?string $department): array
    {
        $staffPerformance = DB::table('chat_ratings')
            ->join('chat_rooms', 'chat_ratings.chat_room_id', '=', 'chat_rooms.id')
            ->join('chat_assignments', 'chat_rooms.id', '=', 'chat_assignments.chat_room_id')
            ->join('users', 'chat_assignments.assigned_to', '=', 'users.id')
            ->whereBetween('chat_ratings.created_at', [$startDate, $endDate])
            ->select(
                'users.id as staff_id',
                DB::raw('(users.first_name || " " || users.last_name) as staff_name'),
                DB::raw('AVG(chat_ratings.rating) as avg_rating'),
                DB::raw('COUNT(chat_ratings.id) as total_ratings'),
                DB::raw('SUM(CASE WHEN chat_ratings.rating >= 4 THEN 1 ELSE 0 END) as satisfied_customers'),
                DB::raw('SUM(CASE WHEN chat_ratings.rating <= 2 THEN 1 ELSE 0 END) as dissatisfied_customers')
            )
            ->groupBy('users.id', 'users.first_name', 'users.last_name')
            ->orderBy('avg_rating', 'desc')
            ->get()
            ->map(function ($item) {
                $satisfactionRate = $item->total_ratings > 0 ? 
                    ($item->satisfied_customers / $item->total_ratings) * 100 : 0;
                
                return [
                    'staff_id' => $item->staff_id,
                    'staff_name' => $item->staff_name,
                    'avg_rating' => round($item->avg_rating, 2),
                    'total_ratings' => $item->total_ratings,
                    'satisfaction_rate' => round($satisfactionRate, 2),
                    'satisfied_customers' => $item->satisfied_customers,
                    'dissatisfied_customers' => $item->dissatisfied_customers,
                ];
            });

        return $staffPerformance->toArray();
    }

    /**
     * Get response time impact on satisfaction.
     */
    protected function getResponseTimeImpact(Carbon $startDate, Carbon $endDate, ?int $staffId, ?string $department): array
    {
        // This would require response time data from chat messages
        // For now, return sample structure
        return [
            'correlation_coefficient' => 0.75, // Negative correlation between response time and satisfaction
            'response_time_brackets' => [
                'under_1_min' => ['avg_rating' => 4.5, 'count' => 120],
                '1_to_5_min' => ['avg_rating' => 4.2, 'count' => 200],
                '5_to_15_min' => ['avg_rating' => 3.8, 'count' => 150],
                'over_15_min' => ['avg_rating' => 3.2, 'count' => 80],
            ],
        ];
    }

    /**
     * Get resolution satisfaction metrics.
     */
    protected function getResolutionSatisfaction(Carbon $startDate, Carbon $endDate, ?int $staffId, ?string $department): array
    {
        $query = ChatRating::whereBetween('chat_ratings.created_at', [$startDate, $endDate]);
        
        if ($staffId) {
            $query->whereHas('chatRoom', function ($q) use ($staffId) {
                $q->whereHas('assignments', function ($aq) use ($staffId) {
                    $aq->where('assigned_to', $staffId);
                });
            });
        }

        // Analyze satisfaction by resolution status
        $resolutionSatisfaction = $query->join('chat_rooms', 'chat_ratings.chat_room_id', '=', 'chat_rooms.id')
                                       ->select(
                                           'chat_rooms.status',
                                           DB::raw('AVG(chat_ratings.rating) as avg_rating'),
                                           DB::raw('COUNT(chat_ratings.id) as total_ratings')
                                       )
                                       ->groupBy('chat_rooms.status')
                                       ->get()
                                       ->mapWithKeys(function ($item) {
                                           return [
                                               $item->status => [
                                                   'avg_rating' => round($item->avg_rating, 2),
                                                   'total_ratings' => $item->total_ratings,
                                               ]
                                           ];
                                       });

        return $resolutionSatisfaction->toArray();
    }

    /**
     * Get Net Promoter Score (NPS) metrics.
     */
    protected function getNPSMetrics(Carbon $startDate, Carbon $endDate, ?int $staffId, ?string $department): array
    {
        // Create a base query builder function to reuse
        $baseQuery = function() use ($startDate, $endDate, $staffId) {
            $query = ChatRating::whereBetween('created_at', [$startDate, $endDate]);

            if ($staffId) {
                $query->whereHas('chatRoom', function ($q) use ($staffId) {
                    $q->whereHas('assignments', function ($aq) use ($staffId) {
                        $aq->where('assigned_to', $staffId);
                    });
                });
            }

            return $query;
        };

        $totalRatings = $baseQuery()->count();

        if ($totalRatings === 0) {
            return [
                'nps_score' => 0,
                'promoters' => 0,
                'passives' => 0,
                'detractors' => 0,
                'promoter_percentage' => 0,
                'detractor_percentage' => 0,
                'total_ratings' => 0,
            ];
        }

        // Convert 5-star rating to NPS scale (assuming 5=10, 4=8, 3=6, 2=4, 1=2)
        $promoters = $baseQuery()->where('rating', 5)->count(); // Rating 5 = Promoters (9-10)
        $passives = $baseQuery()->where('rating', 4)->count();  // Rating 4 = Passives (7-8)
        $detractors = $baseQuery()->whereIn('rating', [1, 2, 3])->count(); // Rating 1-3 = Detractors (0-6)

        $promoterPercentage = ($promoters / $totalRatings) * 100;
        $detractorPercentage = ($detractors / $totalRatings) * 100;
        $npsScore = $promoterPercentage - $detractorPercentage;

        return [
            'nps_score' => round($npsScore, 1),
            'promoters' => $promoters,
            'passives' => $passives,
            'detractors' => $detractors,
            'promoter_percentage' => round($promoterPercentage, 1),
            'detractor_percentage' => round($detractorPercentage, 1),
            'total_ratings' => $totalRatings,
        ];
    }

    /**
     * Calculate trend direction.
     */
    protected function calculateTrendDirection($dailyTrends): string
    {
        if ($dailyTrends->count() < 2) {
            return 'stable';
        }

        $recent = $dailyTrends->take(-7)->avg('avg_rating');
        $previous = $dailyTrends->take(-14)->skip(-7)->avg('avg_rating');

        if ($recent > $previous + 0.1) {
            return 'improving';
        } elseif ($recent < $previous - 0.1) {
            return 'declining';
        }

        return 'stable';
    }

    /**
     * Extract common words from feedback.
     */
    protected function extractCommonWords(string $text): array
    {
        // Simple word extraction (in production, use proper NLP)
        $words = str_word_count(strtolower($text), 1);
        $stopWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'was', 'are', 'were', 'a', 'an'];
        $words = array_diff($words, $stopWords);
        $wordCounts = array_count_values($words);
        arsort($wordCounts);
        
        return array_slice($wordCounts, 0, 10, true);
    }

    /**
     * Calculate feedback rate.
     */
    protected function calculateFeedbackRate(Carbon $startDate, Carbon $endDate, ?int $staffId): float
    {
        $totalRatings = ChatRating::whereBetween('created_at', [$startDate, $endDate])->count();
        $ratingsWithFeedback = ChatRating::whereBetween('created_at', [$startDate, $endDate])
                                        ->whereNotNull('comment')
                                        ->where('comment', '!=', '')
                                        ->count();

        return $totalRatings > 0 ? round(($ratingsWithFeedback / $totalRatings) * 100, 2) : 0;
    }

    /**
     * Submit customer rating and feedback.
     */
    public function submitRating(int $chatRoomId, int $rating, ?string $comment = null, ?int $userId = null): ChatRating
    {
        return ChatRating::create([
            'chat_room_id' => $chatRoomId,
            'user_id' => $userId,
            'rating' => $rating,
            'feedback' => $comment,
        ]);
    }

    /**
     * Get satisfaction survey questions.
     */
    public function getSurveyQuestions(): array
    {
        return [
            [
                'id' => 'overall_satisfaction',
                'type' => 'rating',
                'question' => 'How satisfied are you with our chat support?',
                'scale' => 5,
                'required' => true,
            ],
            [
                'id' => 'response_time',
                'type' => 'rating',
                'question' => 'How would you rate our response time?',
                'scale' => 5,
                'required' => false,
            ],
            [
                'id' => 'staff_helpfulness',
                'type' => 'rating',
                'question' => 'How helpful was our support staff?',
                'scale' => 5,
                'required' => false,
            ],
            [
                'id' => 'problem_resolution',
                'type' => 'rating',
                'question' => 'How well did we resolve your issue?',
                'scale' => 5,
                'required' => false,
            ],
            [
                'id' => 'additional_feedback',
                'type' => 'text',
                'question' => 'Any additional feedback or suggestions?',
                'required' => false,
            ],
        ];
    }
}
