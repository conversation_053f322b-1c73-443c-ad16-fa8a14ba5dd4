<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\AI\AIProviderManager;
use App\Services\CircuitBreakerService;

class AIProviderServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register AIProviderManager as singleton
        $this->app->singleton(AIProviderManager::class, function ($app) {
            $circuitBreaker = new CircuitBreakerService(
                'ai_provider_manager',
                config('ai-providers.circuit_breaker.failure_threshold', 5),
                config('ai-providers.circuit_breaker.recovery_timeout', 300),
                config('ai-providers.circuit_breaker.expected_exception_threshold', 10)
            );
            
            return new AIProviderManager($circuitBreaker);
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Publish configuration if needed
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__.'/../../config/ai-providers.php' => config_path('ai-providers.php'),
            ], 'ai-providers-config');
        }
    }
}
