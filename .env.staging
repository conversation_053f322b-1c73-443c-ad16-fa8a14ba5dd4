# =============================================================================
# STAGING ENVIRONMENT CONFIGURATION
# =============================================================================
APP_NAME="ChiSolution Digital Agency (Staging)"
APP_ENV=staging
APP_KEY=base64:your_staging_app_key_here
APP_DEBUG=true
APP_TIMEZONE="Africa/Johannesburg"
APP_URL=https://staging.chisolution.co.za

# Localization
APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

# Maintenance
APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database

# Security
BCRYPT_ROUNDS=10

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_CHANNEL=stack
LOG_STACK=daily
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DB_CONNECTION=mysql
DB_HOST=your_staging_db_host
DB_PORT=3306
DB_DATABASE=chisolution_staging
DB_USERNAME=your_staging_db_user
DB_PASSWORD=your_staging_db_password

# =============================================================================
# SESSION CONFIGURATION
# =============================================================================
SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=.staging.chisolution.co.za
SESSION_SECURE_COOKIES=true

# =============================================================================
# CACHE & QUEUE CONFIGURATION
# =============================================================================
BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
CACHE_PREFIX=chisolution_staging_

# Redis Configuration (if available)
REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# =============================================================================
# MAIL CONFIGURATION
# =============================================================================
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_staging_mail_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# =============================================================================
# CLOUD STORAGE (AWS S3)
# =============================================================================
AWS_ACCESS_KEY_ID=your_staging_aws_key
AWS_SECRET_ACCESS_KEY=your_staging_aws_secret
AWS_DEFAULT_REGION=af-south-1
AWS_BUCKET=chisolution-staging-assets
AWS_USE_PATH_STYLE_ENDPOINT=false

# =============================================================================
# PAYMENT GATEWAYS
# =============================================================================
# Stripe Configuration (Test Mode)
STRIPE_KEY=pk_test_your_stripe_test_publishable_key
STRIPE_SECRET=sk_test_your_stripe_test_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_test_webhook_secret

# PayPal Configuration (Sandbox)
PAYPAL_CLIENT_ID=your_sandbox_paypal_client_id
PAYPAL_CLIENT_SECRET=your_sandbox_paypal_client_secret
PAYPAL_MODE=sandbox

# =============================================================================
# SOCIAL MEDIA & ANALYTICS
# =============================================================================
GOOGLE_ANALYTICS_ID=G-STAGING-ID
FACEBOOK_PIXEL_ID=
GOOGLE_TAG_MANAGER_ID=

# =============================================================================
# FRONTEND BUILD
# =============================================================================
VITE_APP_NAME="${APP_NAME}"
VITE_STRIPE_KEY="${STRIPE_KEY}"
VITE_GOOGLE_ANALYTICS_ID="${GOOGLE_ANALYTICS_ID}"
