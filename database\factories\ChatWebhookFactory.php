<?php

namespace Database\Factories;

use App\Models\ChatWebhook;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ChatWebhook>
 */
class ChatWebhookFactory extends Factory
{
    protected $model = ChatWebhook::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->words(3, true),
            'url' => $this->faker->url(),
            'events' => $this->faker->randomElements([
                'message.sent',
                'message.received',
                'room.created',
                'room.closed',
                'room.assigned',
                'user.joined',
                'user.left',
                'file.uploaded',
                'rating.submitted',
            ], $this->faker->numberBetween(1, 4)),
            'secret' => $this->faker->optional()->sha256(),
            'headers' => $this->faker->optional()->randomElement([
                ['Authorization' => 'Bearer token123'],
                ['X-API-Key' => 'api-key-123'],
                null,
            ]),
            'is_active' => $this->faker->boolean(80), // 80% chance of being active
            'max_retries' => $this->faker->numberBetween(1, 5),
            'timeout_seconds' => $this->faker->randomElement([15, 30, 60, 120]),
            'ip_whitelist' => $this->faker->optional()->randomElement([
                ['***********', '********'],
                ['***********'],
                null,
            ]),
            'content_type' => 'application/json',
            'description' => $this->faker->optional()->sentence(),
        ];
    }

    /**
     * Indicate that the webhook is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the webhook is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the webhook has a secret for HMAC.
     */
    public function withSecret(): static
    {
        return $this->state(fn (array $attributes) => [
            'secret' => 'webhook-secret-' . $this->faker->uuid(),
        ]);
    }

    /**
     * Indicate that the webhook listens to specific events.
     */
    public function forEvents(array $events): static
    {
        return $this->state(fn (array $attributes) => [
            'events' => $events,
        ]);
    }
}
