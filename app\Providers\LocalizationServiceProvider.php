<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\URL;

class LocalizationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register custom route macro for localized routes
        URL::macro('localized', function ($name, $parameters = [], $locale = null) {
            $locale = $locale ?: app()->getLocale();

            // Add locale to parameters if not already present
            if (!isset($parameters['locale'])) {
                $parameters['locale'] = $locale;
            }

            return route($name, $parameters);
        });

        // Register macro to get current URL with different locale
        URL::macro('switchLocale', function ($locale) {
            return \App\Http\Middleware\LocalizationMiddleware::switchLocaleUrl($locale);
        });
    }
}
