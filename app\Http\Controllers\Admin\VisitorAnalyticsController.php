<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\VisitorAnalytic;
use App\Services\ActivityLogger;
use App\Services\VisitorAnalyticsDashboard;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;

class VisitorAnalyticsController extends Controller
{
    protected VisitorAnalyticsDashboard $visitorAnalytics;
    protected ActivityLogger $activityLogger;

    public function __construct(VisitorAnalyticsDashboard $visitorAnalytics, ActivityLogger $activityLogger)
    {
        $this->middleware('auth');
        $this->middleware('role:admin,staff');
        $this->visitorAnalytics = $visitorAnalytics;
        $this->activityLogger = $activityLogger;
    }

    /**
     * Display a listing of visitor analytics.
     */
    public function index(Request $request): View
    {
        $request->validate([
            'search' => 'sometimes|string|max:255',
            'period' => 'sometimes|string|in:today,yesterday,week,month,custom',
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after_or_equal:start_date',
            'per_page' => 'sometimes|integer|min:10|max:100',
        ]);

        $query = VisitorAnalytic::query()->orderBy('visited_at', 'desc');

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('ip_address', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%")
                  ->orWhere('region', 'like', "%{$search}%")
                  ->orWhere('country', 'like', "%{$search}%")
                  ->orWhere('page_url', 'like', "%{$search}%")
                  ->orWhere('referrer_url', 'like', "%{$search}%")
                  ->orWhere('user_agent', 'like', "%{$search}%");
            });
        }

        // Apply date filters
        if ($request->filled('period')) {
            switch ($request->period) {
                case 'today':
                    $query->whereDate('visited_at', today());
                    break;
                case 'yesterday':
                    $query->whereDate('visited_at', today()->subDay());
                    break;
                case 'week':
                    $query->where('visited_at', '>=', now()->subWeek());
                    break;
                case 'month':
                    $query->where('visited_at', '>=', now()->subMonth());
                    break;
                case 'custom':
                    if ($request->filled('start_date')) {
                        $query->whereDate('visited_at', '>=', $request->start_date);
                    }
                    if ($request->filled('end_date')) {
                        $query->whereDate('visited_at', '<=', $request->end_date);
                    }
                    break;
            }
        }

        $perPage = $request->input('per_page', 20);
        $visitors = $query->paginate($perPage)->withQueryString();

        // Get statistics
        $stats = [
            'total_visitors' => VisitorAnalytic::count(),
            'unique_visitors' => VisitorAnalytic::distinct('ip_address')->count(),
            'today_visitors' => VisitorAnalytic::whereDate('visited_at', today())->count(),
            'suspicious_visitors' => VisitorAnalytic::where('is_suspicious', true)->count(),
        ];

        return view('admin.visitor-analytics.index', compact('visitors', 'stats'));
    }

    /**
     * Display the specified visitor analytics details.
     */
    public function show(VisitorAnalytic $visitorAnalytic): View
    {
        // Get related visits from the same IP
        $relatedVisits = VisitorAnalytic::where('ip_address', $visitorAnalytic->ip_address)
            ->where('id', '!=', $visitorAnalytic->id)
            ->orderBy('visited_at', 'desc')
            ->limit(10)
            ->get();

        // Get visit statistics for this IP
        $ipStats = [
            'total_visits' => VisitorAnalytic::where('ip_address', $visitorAnalytic->ip_address)->count(),
            'first_visit' => VisitorAnalytic::where('ip_address', $visitorAnalytic->ip_address)->min('visited_at'),
            'last_visit' => VisitorAnalytic::where('ip_address', $visitorAnalytic->ip_address)->max('visited_at'),
            'unique_pages' => VisitorAnalytic::where('ip_address', $visitorAnalytic->ip_address)->distinct('page_url')->count(),
        ];

        // Log admin access
        $this->activityLogger->logCustomerActivity(
            'admin_visitor_analytics_viewed',
            "Admin viewed visitor analytics details for IP: {$visitorAnalytic->ip_address}",
            'info',
            null,
            [
                'visitor_id' => $visitorAnalytic->id,
                'ip_address' => $visitorAnalytic->ip_address,
                'location' => $visitorAnalytic->city . ', ' . $visitorAnalytic->country,
                'admin_user' => auth()->user()->email,
            ]
        );

        return view('admin.visitor-analytics.show', compact('visitorAnalytic', 'relatedVisits', 'ipStats'));
    }

    /**
     * Get visitor analytics data for charts.
     */
    public function getChartData(Request $request): JsonResponse
    {
        $request->validate([
            'period' => 'sometimes|string|in:7,14,30,90,custom',
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after_or_equal:start_date',
        ]);

        if ($request->input('period') === 'custom' && $request->has('start_date') && $request->has('end_date')) {
            $startDate = $request->start_date;
            $endDate = $request->end_date;
            $chartData = $this->visitorAnalytics->getVisitorChartDataByDateRange($startDate, $endDate);
            $period = 'custom';
            $title = "Comprehensive Visitor Analytics (" . date('M j', strtotime($startDate)) . " - " . date('M j, Y', strtotime($endDate)) . ")";

            // Calculate days for location data
            $days = \Carbon\Carbon::parse($startDate)->diffInDays(\Carbon\Carbon::parse($endDate)) + 1;
        } else {
            $days = (int) $request->input('period', 30);
            $chartData = $this->visitorAnalytics->getVisitorChartData($days);
            $period = $days;
            $title = "Comprehensive Visitor Analytics (Last {$days} " . ($days === 1 ? 'Day' : 'Days') . ")";
        }

        // Get additional analytics data
        $locationData = $this->visitorAnalytics->getLocationAnalytics($days, 5);
        $deviceData = $this->visitorAnalytics->getDeviceBreakdown($days);

        // Log admin chart data access
        $this->activityLogger->logCustomerActivity(
            'admin_visitor_analytics_chart_data',
            "Admin requested visitor analytics chart data for period: {$period}",
            'success',
            null,
            [
                'period' => $period,
                'start_date' => $period === 'custom' ? ($startDate ?? null) : null,
                'end_date' => $period === 'custom' ? ($endDate ?? null) : null,
                'days' => $period !== 'custom' ? (int) $period : null,
            ],
            [
                'chart_title' => $title,
                'data_points' => count($chartData),
            ]
        );

        return response()->json([
            'success' => true,
            'data' => $chartData,
            'title' => $title,
            'period' => $period,
            'location_data' => $locationData,
            'device_data' => $deviceData,
            'summary' => [
                'total_data_points' => count($chartData),
                'top_locations' => array_slice($locationData, 0, 3),
                'device_breakdown' => $deviceData
            ]
        ]);
    }

    /**
     * Export visitor analytics data.
     */
    public function export(Request $request): \Symfony\Component\HttpFoundation\StreamedResponse
    {
        $request->validate([
            'search' => 'sometimes|string|max:255',
            'period' => 'sometimes|string|in:today,yesterday,week,month,custom',
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after_or_equal:start_date',
        ]);

        $query = VisitorAnalytic::query()->orderBy('visited_at', 'desc');

        // Apply same filters as index
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('ip_address', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%")
                  ->orWhere('region', 'like', "%{$search}%")
                  ->orWhere('country', 'like', "%{$search}%")
                  ->orWhere('page_url', 'like', "%{$search}%")
                  ->orWhere('referrer_url', 'like', "%{$search}%");
            });
        }

        // Apply date filters
        if ($request->filled('period')) {
            switch ($request->period) {
                case 'today':
                    $query->whereDate('visited_at', today());
                    break;
                case 'yesterday':
                    $query->whereDate('visited_at', today()->subDay());
                    break;
                case 'week':
                    $query->where('visited_at', '>=', now()->subWeek());
                    break;
                case 'month':
                    $query->where('visited_at', '>=', now()->subMonth());
                    break;
                case 'custom':
                    if ($request->filled('start_date')) {
                        $query->whereDate('visited_at', '>=', $request->start_date);
                    }
                    if ($request->filled('end_date')) {
                        $query->whereDate('visited_at', '<=', $request->end_date);
                    }
                    break;
            }
        }

        $filename = 'visitor-analytics-' . now()->format('Y-m-d-H-i-s') . '.csv';

        $this->activityLogger->logCustomerActivity(
            'admin_visitor_analytics_exported',
            "Admin exported visitor analytics data",
            'success',
            null,
            [
                'filename' => $filename,
                'filters' => $request->only(['search', 'period', 'start_date', 'end_date']),
                'admin_user' => auth()->user()->email,
            ]
        );

        return response()->streamDownload(function () use ($query) {
            $handle = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($handle, [
                'IP Address',
                'Location',
                'Page URL',
                'Referrer',
                'Device Type',
                'Browser',
                'Platform',
                'Visit Date',
                'Is Suspicious',
                'Session Duration'
            ]);

            // Stream data
            $query->chunk(1000, function ($visitors) use ($handle) {
                foreach ($visitors as $visitor) {
                    fputcsv($handle, [
                        $visitor->ip_address,
                        $visitor->city . ', ' . $visitor->region . ', ' . $visitor->country,
                        $visitor->page_url,
                        $visitor->referrer,
                        $visitor->device_type,
                        $visitor->browser,
                        $visitor->platform,
                        $visitor->visited_at->format('Y-m-d H:i:s'),
                        $visitor->is_suspicious ? 'Yes' : 'No',
                        $visitor->session_duration
                    ]);
                }
            });

            fclose($handle);
        }, $filename, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }
}
