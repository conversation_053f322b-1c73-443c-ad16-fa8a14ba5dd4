<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\File;
use App\Services\SeoService;

class ValidateSeo extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'seo:validate {--url=} {--all} {--fix}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Validate SEO implementation including meta tags, schema markup, and performance';

    protected array $testResults = [];
    protected int $passedTests = 0;
    protected int $failedTests = 0;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Starting comprehensive SEO validation...');
        $this->newLine();

        // Test sitemap
        $this->testSitemap();

        // Test robots.txt
        $this->testRobotsTxt();

        // Test schema markup
        $this->testSchemaMarkup();

        // Test meta tags
        $this->testMetaTags();

        // Test performance basics
        $this->testPerformance();

        // Display results
        $this->displayResults();

        return $this->failedTests === 0 ? Command::SUCCESS : Command::FAILURE;
    }

    /**
     * Test sitemap accessibility and structure
     */
    protected function testSitemap(): void
    {
        $this->info('📍 Testing sitemap...');

        $sitemapPath = public_path('sitemap.xml');

        if (!File::exists($sitemapPath)) {
            $this->recordFailure('Sitemap file does not exist');
            return;
        }

        $sitemapContent = File::get($sitemapPath);

        // Test XML validity
        libxml_use_internal_errors(true);
        $xml = simplexml_load_string($sitemapContent);

        if ($xml === false) {
            $this->recordFailure('Sitemap XML is invalid');
            return;
        }

        // Count URLs
        $urlCount = count($xml->url ?? []);

        if ($urlCount === 0) {
            $this->recordFailure('Sitemap contains no URLs');
            return;
        }

        $this->recordSuccess("Sitemap is valid with {$urlCount} URLs");
    }

    /**
     * Test robots.txt
     */
    protected function testRobotsTxt(): void
    {
        $this->info('🤖 Testing robots.txt...');

        $robotsPath = public_path('robots.txt');

        if (!File::exists($robotsPath)) {
            $this->recordFailure('robots.txt file does not exist');
            return;
        }

        $robotsContent = File::get($robotsPath);

        // Check for sitemap reference
        if (!str_contains($robotsContent, 'Sitemap:')) {
            $this->recordFailure('robots.txt does not reference sitemap');
            return;
        }

        $this->recordSuccess('robots.txt is properly configured');
    }

    /**
     * Test schema markup
     */
    protected function testSchemaMarkup(): void
    {
        $this->info('📋 Testing schema markup...');

        $testUrls = [
            '/' => 'Organization',
            '/about' => 'Organization',
            '/services' => 'Organization',
            '/services/web-development' => 'Service'
        ];

        foreach ($testUrls as $url => $expectedType) {
            $this->testSchemaForUrl($url, $expectedType);
        }
    }

    /**
     * Test schema for specific URL
     */
    protected function testSchemaForUrl(string $url, string $expectedType): void
    {
        try {
            $response = Http::timeout(10)->get(url($url));

            if (!$response->successful()) {
                $this->recordFailure("Failed to fetch {$url}");
                return;
            }

            $content = $response->body();

            // Look for JSON-LD
            if (!preg_match('/<script[^>]*type=["\']application\/ld\+json["\'][^>]*>(.*?)<\/script>/s', $content, $matches)) {
                $this->recordFailure("No JSON-LD found on {$url}");
                return;
            }

            $jsonLd = json_decode($matches[1], true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->recordFailure("Invalid JSON-LD on {$url}");
                return;
            }

            if (!isset($jsonLd['@type']) || $jsonLd['@type'] !== $expectedType) {
                $this->recordFailure("Expected {$expectedType} schema on {$url}, found " . ($jsonLd['@type'] ?? 'none'));
                return;
            }

            $this->recordSuccess("Valid {$expectedType} schema found on {$url}");

        } catch (\Exception $e) {
            $this->recordFailure("Error testing {$url}: " . $e->getMessage());
        }
    }

    /**
     * Test meta tags
     */
    protected function testMetaTags(): void
    {
        $this->info('🏷️ Testing meta tags...');

        $testUrls = ['/', '/about', '/services'];

        foreach ($testUrls as $url) {
            $this->testMetaTagsForUrl($url);
        }
    }

    /**
     * Test meta tags for specific URL
     */
    protected function testMetaTagsForUrl(string $url): void
    {
        try {
            $response = Http::timeout(10)->get(url($url));

            if (!$response->successful()) {
                $this->recordFailure("Failed to fetch {$url}");
                return;
            }

            $content = $response->body();

            // Check for essential meta tags
            $requiredTags = [
                'title' => '/<title[^>]*>(.+?)<\/title>/i',
                'description' => '/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i',
                'og:title' => '/<meta[^>]*property=["\']og:title["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i',
                'og:description' => '/<meta[^>]*property=["\']og:description["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i'
            ];

            $missingTags = [];

            foreach ($requiredTags as $tag => $pattern) {
                if (!preg_match($pattern, $content)) {
                    $missingTags[] = $tag;
                }
            }

            if (!empty($missingTags)) {
                $this->recordFailure("Missing meta tags on {$url}: " . implode(', ', $missingTags));
                return;
            }

            $this->recordSuccess("All essential meta tags found on {$url}");

        } catch (\Exception $e) {
            $this->recordFailure("Error testing meta tags on {$url}: " . $e->getMessage());
        }
    }

    /**
     * Test basic performance indicators
     */
    protected function testPerformance(): void
    {
        $this->info('⚡ Testing performance basics...');

        try {
            $start = microtime(true);
            $response = Http::timeout(30)->get(url('/'));
            $loadTime = microtime(true) - $start;

            if (!$response->successful()) {
                $this->recordFailure('Homepage failed to load');
                return;
            }

            // Check load time
            if ($loadTime > 3.0) {
                $this->recordFailure(sprintf('Homepage load time too slow: %.2fs', $loadTime));
            } else {
                $this->recordSuccess(sprintf('Homepage load time acceptable: %.2fs', $loadTime));
            }

            // Check response size
            $contentLength = strlen($response->body());
            $contentSizeMB = $contentLength / 1024 / 1024;

            if ($contentSizeMB > 2.0) {
                $this->recordFailure(sprintf('Homepage content size too large: %.2fMB', $contentSizeMB));
            } else {
                $this->recordSuccess(sprintf('Homepage content size acceptable: %.2fMB', $contentSizeMB));
            }

        } catch (\Exception $e) {
            $this->recordFailure('Error testing performance: ' . $e->getMessage());
        }
    }

    /**
     * Record a successful test
     */
    protected function recordSuccess(string $message): void
    {
        $this->testResults[] = ['status' => 'pass', 'message' => $message];
        $this->passedTests++;
        $this->line("  ✅ {$message}");
    }

    /**
     * Record a failed test
     */
    protected function recordFailure(string $message): void
    {
        $this->testResults[] = ['status' => 'fail', 'message' => $message];
        $this->failedTests++;
        $this->line("  ❌ {$message}");
    }

    /**
     * Display final results
     */
    protected function displayResults(): void
    {
        $this->newLine();
        $this->info('📊 SEO Validation Results');
        $this->line('========================');

        $totalTests = $this->passedTests + $this->failedTests;
        $successRate = $totalTests > 0 ? round(($this->passedTests / $totalTests) * 100, 1) : 0;

        $this->line("Total Tests: {$totalTests}");
        $this->line("Passed: {$this->passedTests}");
        $this->line("Failed: {$this->failedTests}");
        $this->line("Success Rate: {$successRate}%");

        if ($this->failedTests === 0) {
            $this->info('🎉 All SEO tests passed!');
        } else {
            $this->error('⚠️ Some SEO tests failed. Please review and fix the issues above.');
        }
    }
}
