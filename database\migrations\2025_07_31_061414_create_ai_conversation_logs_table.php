<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ai_conversation_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('chat_room_id')->constrained('chat_rooms')->onDelete('cascade');
            $table->foreignId('chat_message_id')->constrained('chat_messages')->onDelete('cascade');
            $table->text('user_message');
            $table->text('ai_response');
            $table->string('intent_detected', 100)->nullable();
            $table->decimal('confidence_score', 3, 2);
            $table->integer('processing_time_ms')->unsigned();
            $table->string('model_used', 50);
            $table->boolean('was_helpful')->nullable()->comment('User feedback on AI response');
            $table->boolean('escalated_to_human')->default(false);
            $table->string('escalation_reason')->nullable();
            $table->timestamps();
            
            // Performance indexes
            $table->index(['chat_room_id', 'created_at'], 'idx_ai_logs_room_created');
            $table->index('confidence_score', 'idx_ai_logs_confidence');
            $table->index('escalated_to_human', 'idx_ai_logs_escalated');
            $table->index('was_helpful', 'idx_ai_logs_helpful');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ai_conversation_logs');
    }
};
