<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class ChatSecurityMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $operation = 'general'): Response
    {
        // Rate limiting
        if (!$this->checkRateLimit($request, $operation)) {
            Log::warning('Chat rate limit exceeded', [
                'ip' => $request->ip(),
                'user_id' => $request->user()?->id,
                'operation' => $operation,
                'user_agent' => $request->userAgent(),
            ]);

            return response()->json([
                'error' => 'Rate limit exceeded. Please try again later.',
                'retry_after' => $this->getRateLimitRetryAfter($request, $operation),
            ], 429);
        }

        // Input validation and sanitization
        $this->validateAndSanitizeInput($request);

        // XSS Protection
        if (!$this->checkXSSProtection($request)) {
            Log::warning('Potential XSS attack detected', [
                'ip' => $request->ip(),
                'user_id' => $request->user()?->id,
                'input' => $request->all(),
                'user_agent' => $request->userAgent(),
            ]);

            return response()->json([
                'error' => 'Invalid input detected.',
            ], 400);
        }

        // CSRF Protection for state-changing operations
        if (in_array($request->method(), ['POST', 'PUT', 'PATCH', 'DELETE'])) {
            if (!$this->verifyCsrfToken($request)) {
                return response()->json([
                    'error' => 'CSRF token mismatch.',
                ], 419);
            }
        }

        // Content Security Policy
        $response = $next($request);
        $this->addSecurityHeaders($response);

        return $response;
    }

    /**
     * Check rate limiting for the operation.
     */
    protected function checkRateLimit(Request $request, string $operation): bool
    {
        $config = config('chat_performance.rate_limiting.limits.' . $operation, [
            'max_attempts' => 60,
            'decay_minutes' => 1,
        ]);

        $key = $this->getRateLimitKey($request, $operation);

        return RateLimiter::attempt(
            $key,
            $config['max_attempts'],
            function () {
                // Allow the request
            },
            $config['decay_minutes'] * 60
        );
    }

    /**
     * Get rate limit key for the request.
     */
    protected function getRateLimitKey(Request $request, string $operation): string
    {
        $userId = $request->user()?->id ?? 'guest';
        $ip = $request->ip();
        
        return "chat_rate_limit:{$operation}:{$userId}:{$ip}";
    }

    /**
     * Get retry after time for rate limit.
     */
    protected function getRateLimitRetryAfter(Request $request, string $operation): int
    {
        $key = $this->getRateLimitKey($request, $operation);
        return RateLimiter::availableIn($key);
    }

    /**
     * Validate and sanitize input data.
     */
    protected function validateAndSanitizeInput(Request $request): void
    {
        $sanitizationLevel = config('chat_performance.security_performance.validation.sanitization_level', 'medium');

        foreach ($request->all() as $key => $value) {
            if (is_string($value)) {
                $request->merge([
                    $key => $this->sanitizeString($value, $sanitizationLevel)
                ]);
            }
        }
    }

    /**
     * Sanitize string input based on level.
     */
    protected function sanitizeString(string $input, string $level): string
    {
        switch ($level) {
            case 'high':
                // Strict sanitization - remove all HTML and special characters
                return strip_tags(htmlspecialchars($input, ENT_QUOTES, 'UTF-8'));
                
            case 'medium':
                // Moderate sanitization - allow some safe HTML
                return strip_tags($input, '<b><i><u><em><strong>');
                
            case 'low':
                // Basic sanitization - just escape HTML
                return htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
                
            default:
                return htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
        }
    }

    /**
     * Check for XSS protection.
     */
    protected function checkXSSProtection(Request $request): bool
    {
        if (!config('chat_performance.security_performance.validation.xss_protection', true)) {
            return true;
        }

        $xssPatterns = [
            '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi',
            '/javascript:/i',
            '/on\w+\s*=/i',
            '/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/mi',
            '/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/mi',
            '/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/mi',
        ];

        foreach ($request->all() as $value) {
            if (is_string($value)) {
                foreach ($xssPatterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    /**
     * Verify CSRF token.
     */
    protected function verifyCsrfToken(Request $request): bool
    {
        // For API requests, we might use different token verification
        if ($request->expectsJson()) {
            return $this->verifyApiToken($request);
        }

        // For web requests, use Laravel's built-in CSRF verification
        return $request->session()->token() === $request->input('_token') ||
               $request->session()->token() === $request->header('X-CSRF-TOKEN');
    }

    /**
     * Verify API token for CSRF protection.
     */
    protected function verifyApiToken(Request $request): bool
    {
        // Implement API token verification logic
        // This could be JWT verification, API key validation, etc.
        return true; // Simplified for this example
    }

    /**
     * Add security headers to response.
     */
    protected function addSecurityHeaders(Response $response): void
    {
        $headers = [
            'X-Content-Type-Options' => 'nosniff',
            'X-Frame-Options' => 'DENY',
            'X-XSS-Protection' => '1; mode=block',
            'Referrer-Policy' => 'strict-origin-when-cross-origin',
            'Content-Security-Policy' => $this->getContentSecurityPolicy(),
        ];

        foreach ($headers as $header => $value) {
            $response->headers->set($header, $value);
        }
    }

    /**
     * Get Content Security Policy header value.
     */
    protected function getContentSecurityPolicy(): string
    {
        $policies = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'", // Adjust based on your needs
            "style-src 'self' 'unsafe-inline'",
            "img-src 'self' data: https:",
            "font-src 'self' https:",
            "connect-src 'self' wss: https:",
            "media-src 'self'",
            "object-src 'none'",
            "base-uri 'self'",
            "form-action 'self'",
            "frame-ancestors 'none'",
        ];

        return implode('; ', $policies);
    }
}
