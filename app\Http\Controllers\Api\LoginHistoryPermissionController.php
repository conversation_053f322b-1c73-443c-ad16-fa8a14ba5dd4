<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\LoginHistoryPermission;
use App\Models\User;
use App\Services\ActivityLogger;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class LoginHistoryPermissionController extends Controller
{
    protected ActivityLogger $activityLogger;

    public function __construct(ActivityLogger $activityLogger)
    {
        $this->activityLogger = $activityLogger;
    }

    /**
     * Get all login history permissions.
     */
    public function index(Request $request): JsonResponse
    {
        $query = LoginHistoryPermission::with(['user', 'grantedBy'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->has('access_level')) {
            $query->where('access_level', $request->access_level);
        }

        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        if ($request->has('expiring_soon') && $request->boolean('expiring_soon')) {
            $query->where('expires_at', '<=', now()->addDays(7))
                  ->where('expires_at', '>', now());
        }

        $permissions = $query->paginate($request->get('per_page', 15));

        // Transform data for API response
        $permissions->getCollection()->transform(function ($permission) {
            return [
                'id' => $permission->uuid,
                'user' => [
                    'id' => $permission->user->uuid,
                    'name' => $permission->user->name,
                    'email' => $permission->user->email,
                    'role' => $permission->user->role->name ?? 'Unknown',
                ],
                'granted_by' => [
                    'id' => $permission->grantedBy->uuid,
                    'name' => $permission->grantedBy->name,
                    'email' => $permission->grantedBy->email,
                ],
                'access_level' => $permission->access_level,
                'access_level_display' => $permission->access_level_display,
                'access_level_color' => $permission->access_level_color,
                'specific_permissions' => $permission->specific_permissions,
                'reason' => $permission->reason,
                'granted_at' => $permission->formatted_granted_at,
                'expires_at' => $permission->formatted_expires_at,
                'days_until_expiration' => $permission->days_until_expiration,
                'is_active' => $permission->is_active,
                'is_valid' => $permission->isValid(),
                'is_expired' => $permission->isExpired(),
                'is_expiring_soon' => $permission->isExpiringSoon(),
                'admin_notes' => $permission->admin_notes,
            ];
        });

        $this->activityLogger->log(
            'login_history_permissions_viewed',
            Auth::user(),
            [
                'description' => 'Viewed login history permissions list',
            ]
        );

        return response()->json([
            'success' => true,
            'data' => $permissions,
            'meta' => [
                'total_count' => $permissions->total(),
                'current_page' => $permissions->currentPage(),
                'per_page' => $permissions->perPage(),
                'last_page' => $permissions->lastPage(),
            ]
        ]);
    }

    /**
     * Get a specific permission.
     */
    public function show(LoginHistoryPermission $permission): JsonResponse
    {
        $permission->load(['user', 'grantedBy']);

        $this->activityLogger->log(
            'login_history_permission_viewed',
            Auth::user(),
            [
                'description' => 'Viewed login history permission details',
                'permission_id' => $permission->uuid,
            ]
        );

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $permission->uuid,
                'user' => [
                    'id' => $permission->user->uuid,
                    'name' => $permission->user->name,
                    'email' => $permission->user->email,
                    'role' => $permission->user->role->name ?? 'Unknown',
                ],
                'granted_by' => [
                    'id' => $permission->grantedBy->uuid,
                    'name' => $permission->grantedBy->name,
                    'email' => $permission->grantedBy->email,
                ],
                'access_level' => $permission->access_level,
                'access_level_display' => $permission->access_level_display,
                'access_level_color' => $permission->access_level_color,
                'specific_permissions' => $permission->specific_permissions,
                'available_permissions' => LoginHistoryPermission::getAvailableSpecificPermissions(),
                'reason' => $permission->reason,
                'granted_at' => $permission->formatted_granted_at,
                'expires_at' => $permission->formatted_expires_at,
                'days_until_expiration' => $permission->days_until_expiration,
                'is_active' => $permission->is_active,
                'is_valid' => $permission->isValid(),
                'is_expired' => $permission->isExpired(),
                'is_expiring_soon' => $permission->isExpiringSoon(),
                'admin_notes' => $permission->admin_notes,
            ]
        ]);
    }

    /**
     * Grant or update login history permission.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'access_level' => ['required', Rule::in(['none', 'partial', 'full'])],
            'specific_permissions' => 'nullable|array',
            'specific_permissions.*' => 'string',
            'reason' => 'required|string|max:1000',
            'expires_at' => 'nullable|date|after:now',
            'admin_notes' => 'nullable|string|max:2000',
        ]);

        // Check if user already has permission
        $existingPermission = LoginHistoryPermission::where('user_id', $validated['user_id'])->first();

        if ($existingPermission) {
            return response()->json([
                'error' => 'User already has login history permission',
                'message' => 'Use the update endpoint to modify existing permissions.'
            ], 422);
        }

        // Validate that user is admin or staff
        $targetUser = User::findOrFail($validated['user_id']);
        if (!$targetUser->hasRole(['admin', 'staff'])) {
            return response()->json([
                'error' => 'Invalid user role',
                'message' => 'Login history permissions can only be granted to admin or staff users.'
            ], 422);
        }

        // Set default specific permissions based on access level
        if (!isset($validated['specific_permissions'])) {
            $validated['specific_permissions'] = match ($validated['access_level']) {
                'partial' => LoginHistoryPermission::getDefaultPartialPermissions(),
                'full' => array_keys(LoginHistoryPermission::getAvailableSpecificPermissions()),
                default => [],
            };
        }

        $permission = LoginHistoryPermission::create([
            'user_id' => $validated['user_id'],
            'granted_by_user_id' => Auth::id(),
            'access_level' => $validated['access_level'],
            'specific_permissions' => $validated['specific_permissions'],
            'reason' => $validated['reason'],
            'expires_at' => $validated['expires_at'] ?? null,
            'admin_notes' => $validated['admin_notes'] ?? null,
        ]);

        $permission->load(['user', 'grantedBy']);

        $this->activityLogger->log(
            'login_history_permission_granted',
            Auth::user(),
            [
                'description' => 'Granted login history permission',
                'target_user_id' => $targetUser->uuid,
                'target_user_email' => $targetUser->email,
                'access_level' => $validated['access_level'],
                'reason' => $validated['reason'],
            ]
        );

        return response()->json([
            'success' => true,
            'message' => 'Login history permission granted successfully',
            'data' => [
                'id' => $permission->uuid,
                'access_level' => $permission->access_level,
                'user' => [
                    'name' => $permission->user->name,
                    'email' => $permission->user->email,
                ],
            ]
        ], 201);
    }

    /**
     * Update login history permission.
     */
    public function update(Request $request, LoginHistoryPermission $permission): JsonResponse
    {
        $validated = $request->validate([
            'access_level' => ['required', Rule::in(['none', 'partial', 'full'])],
            'specific_permissions' => 'nullable|array',
            'specific_permissions.*' => 'string',
            'reason' => 'nullable|string|max:1000',
            'expires_at' => 'nullable|date|after:now',
            'admin_notes' => 'nullable|string|max:2000',
        ]);

        $oldAccessLevel = $permission->access_level;

        // Update specific permissions based on access level if not provided
        if (!isset($validated['specific_permissions'])) {
            $validated['specific_permissions'] = match ($validated['access_level']) {
                'partial' => LoginHistoryPermission::getDefaultPartialPermissions(),
                'full' => array_keys(LoginHistoryPermission::getAvailableSpecificPermissions()),
                default => [],
            };
        }

        $permission->update([
            'access_level' => $validated['access_level'],
            'specific_permissions' => $validated['specific_permissions'],
            'reason' => $validated['reason'] ?? $permission->reason,
            'expires_at' => $validated['expires_at'] ?? $permission->expires_at,
            'admin_notes' => $validated['admin_notes'] ?? $permission->admin_notes,
        ]);

        $this->activityLogger->log(
            'login_history_permission_updated',
            Auth::user(),
            [
                'description' => 'Updated login history permission',
                'target_user_id' => $permission->user->uuid,
                'target_user_email' => $permission->user->email,
                'old_access_level' => $oldAccessLevel,
                'new_access_level' => $validated['access_level'],
            ]
        );

        return response()->json([
            'success' => true,
            'message' => 'Login history permission updated successfully',
            'data' => [
                'id' => $permission->uuid,
                'access_level' => $permission->access_level,
            ]
        ]);
    }

    /**
     * Revoke login history permission.
     */
    public function destroy(LoginHistoryPermission $permission): JsonResponse
    {
        $permission->revoke();

        $this->activityLogger->log(
            'login_history_permission_revoked',
            Auth::user(),
            [
                'description' => 'Revoked login history permission',
                'target_user_id' => $permission->user->uuid,
                'target_user_email' => $permission->user->email,
                'access_level' => $permission->access_level,
            ]
        );

        return response()->json([
            'success' => true,
            'message' => 'Login history permission revoked successfully'
        ]);
    }

    /**
     * Get available users for permission assignment.
     */
    public function availableUsers(): JsonResponse
    {
        $users = User::whereHas('role', function ($query) {
                $query->whereIn('name', ['admin', 'staff']);
            })
            ->whereDoesntHave('loginHistoryPermission')
            ->select('id', 'uuid', 'name', 'email')
            ->with('role:id,name')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $users->map(function ($user) {
                return [
                    'id' => $user->id,
                    'uuid' => $user->uuid,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role->name ?? 'Unknown',
                ];
            })
        ]);
    }

    /**
     * Get permission configuration options.
     */
    public function config(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'access_levels' => [
                    'none' => 'No Access',
                    'partial' => 'Partial Access',
                    'full' => 'Full Access',
                ],
                'available_permissions' => LoginHistoryPermission::getAvailableSpecificPermissions(),
                'default_partial_permissions' => LoginHistoryPermission::getDefaultPartialPermissions(),
            ]
        ]);
    }
}
