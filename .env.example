# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
APP_NAME="ChiSolution Digital Agency"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_TIMEZONE="Africa/Johannesburg"
APP_URL=http://localhost:8000

# Localization
APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

# Maintenance
APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database

# Performance
PHP_CLI_SERVER_WORKERS=4

# Security
BCRYPT_ROUNDS=12

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

# =============================================================================
# SESSION CONFIGURATION
# =============================================================================
SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

# =============================================================================
# CACHE & QUEUE CONFIGURATION
# =============================================================================
BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
CACHE_PREFIX=chisolution_

# Redis Configuration
REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Memcached Configuration
MEMCACHED_HOST=127.0.0.1

# =============================================================================
# MAIL CONFIGURATION
# =============================================================================
MAIL_MAILER=smtp
MAIL_SCHEME=null
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# =============================================================================
# CLOUD STORAGE (AWS S3)
# =============================================================================
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=af-south-1
AWS_BUCKET=chisolution-assets
AWS_USE_PATH_STYLE_ENDPOINT=false

# =============================================================================
# PAYMENT GATEWAYS
# =============================================================================
# Stripe Configuration
STRIPE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# PayPal Configuration
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_MODE=sandbox

# =============================================================================
# SOCIAL MEDIA & ANALYTICS
# =============================================================================
GOOGLE_ANALYTICS_ID=
FACEBOOK_PIXEL_ID=
GOOGLE_TAG_MANAGER_ID=

# =============================================================================
# FRONTEND BUILD
# =============================================================================
VITE_APP_NAME="${APP_NAME}"
VITE_STRIPE_KEY="${STRIPE_KEY}"
VITE_GOOGLE_ANALYTICS_ID="${GOOGLE_ANALYTICS_ID}"
