<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('visitor_analytics', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            
            // Visitor Identification
            $table->string('visitor_id')->index(); // Unique identifier for visitor session
            $table->string('fingerprint')->nullable(); // Browser fingerprint for returning visitor detection
            $table->boolean('is_returning_visitor')->default(false);
            $table->timestamp('first_visit')->nullable(); // When this visitor first visited
            $table->timestamp('last_visit')->nullable(); // Previous visit timestamp
            $table->integer('visit_count')->default(1); // Number of visits by this visitor
            
            // Page Information
            $table->string('page_url');
            $table->string('page_title')->nullable();
            $table->string('route_name')->nullable();
            $table->json('route_parameters')->nullable();
            $table->string('referrer_url')->nullable();
            $table->string('utm_source')->nullable();
            $table->string('utm_medium')->nullable();
            $table->string('utm_campaign')->nullable();
            
            // Request Information
            $table->string('ip_address', 45);
            $table->text('user_agent');
            $table->string('method', 10)->default('GET');
            $table->integer('response_status')->default(200);
            $table->integer('response_time_ms')->nullable(); // Page load time
            
            // Device & Browser Information
            $table->string('device_type')->nullable(); // mobile, desktop, tablet
            $table->string('device_model')->nullable();
            $table->string('browser')->nullable();
            $table->string('browser_version')->nullable();
            $table->string('platform')->nullable(); // OS
            $table->string('platform_version')->nullable();
            $table->integer('screen_width')->nullable();
            $table->integer('screen_height')->nullable();
            $table->string('language')->nullable();
            $table->string('timezone')->nullable();
            
            // Location Information
            $table->string('country')->nullable();
            $table->string('country_code', 2)->nullable();
            $table->string('region')->nullable();
            $table->string('city')->nullable();
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->string('isp')->nullable();
            
            // Session Information
            $table->string('session_id')->nullable();
            $table->integer('session_duration')->nullable(); // Time spent on page in seconds
            $table->boolean('is_bounce')->default(false); // Single page visit
            $table->integer('page_views_in_session')->default(1);
            
            // Error Tracking
            $table->boolean('has_errors')->default(false);
            $table->json('errors')->nullable(); // JavaScript errors, 404s, etc.
            $table->text('error_details')->nullable();
            
            // Performance Metrics
            $table->integer('dom_load_time')->nullable(); // DOM ready time
            $table->integer('page_load_time')->nullable(); // Full page load time
            $table->integer('time_to_first_byte')->nullable();
            
            // Engagement Metrics
            $table->integer('scroll_depth')->nullable(); // Percentage scrolled
            $table->integer('clicks_count')->default(0);
            $table->json('clicked_elements')->nullable(); // Elements clicked
            $table->boolean('form_submitted')->default(false);
            $table->json('form_interactions')->nullable();
            
            // Business Intelligence
            $table->boolean('is_bot')->default(false);
            $table->string('bot_name')->nullable();
            $table->boolean('is_suspicious')->default(false);
            $table->text('suspicious_reasons')->nullable();
            $table->integer('risk_score')->default(0); // 0-100
            
            // Timestamps
            $table->timestamp('visited_at')->nullable();
            $table->timestamp('left_at')->nullable();
            $table->timestamps();
            
            // Indexes for performance and analytics
            $table->index(['visitor_id', 'visited_at']);
            $table->index(['ip_address', 'visited_at']);
            $table->index(['page_url', 'visited_at']);
            $table->index(['route_name', 'visited_at']);
            $table->index(['country', 'visited_at']);
            $table->index(['device_type', 'visited_at']);
            $table->index(['is_returning_visitor', 'visited_at']);
            $table->index(['is_bot', 'visited_at']);
            $table->index(['has_errors', 'visited_at']);
            $table->index(['visited_at']);
            $table->index('uuid');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('visitor_analytics');
    }
};
