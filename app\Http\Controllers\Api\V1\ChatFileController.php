<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Services\ChatFileService;
use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\ChatFile;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\StreamedResponse;

class ChatFileController extends Controller
{
    protected ChatFileService $fileService;

    public function __construct(ChatFileService $fileService)
    {
        $this->fileService = $fileService;
        
        // Apply rate limiting
        $this->middleware('throttle:chat-file-upload')->only('upload');
    }

    /**
     * Upload file to chat room.
     */
    public function upload(Request $request, string $roomUuid): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|max:' . $this->parseFileSize(config('chat.files.max_size', '10MB')),
            'message_content' => 'sometimes|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $room = ChatRoom::where('uuid', $roomUuid)->firstOrFail();

            if ($room->isClosed()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot upload files to closed chat room',
                ], 400);
            }

            $file = $request->file('file');
            
            // Create message first
            $messageContent = $request->get('message_content', 'File: ' . $file->getClientOriginalName());
            $message = ChatMessage::create([
                'chat_room_id' => $room->id,
                'user_id' => auth()->id(),
                'message_type' => $this->getMessageType($file),
                'content' => $messageContent,
            ]);

            // Upload file
            $chatFile = $this->fileService->uploadFile($room, $message, $file);

            return response()->json([
                'success' => true,
                'message' => 'File uploaded successfully',
                'data' => [
                    'file' => $chatFile,
                    'message' => $message->fresh(),
                    'download_token' => $this->fileService->generateDownloadToken($chatFile),
                ],
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload file',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Download file with token.
     */
    public function download(string $token): StreamedResponse|JsonResponse
    {
        try {
            $fileData = $this->fileService->downloadFile($token);

            return response()->streamDownload(
                function () use ($fileData) {
                    echo $fileData['content'];
                },
                $fileData['filename'],
                [
                    'Content-Type' => $fileData['mime_type'],
                    'Content-Length' => $fileData['size'],
                ]
            );

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to download file',
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Get file preview (for images).
     */
    public function preview(string $fileUuid): StreamedResponse|JsonResponse
    {
        try {
            $file = ChatFile::where('uuid', $fileUuid)->firstOrFail();

            if (!$file->is_image) {
                return response()->json([
                    'success' => false,
                    'message' => 'File is not an image',
                ], 400);
            }

            $preview = $this->fileService->getFilePreview($file, [
                'width' => 300,
                'height' => 300,
                'quality' => 80,
            ]);

            if (!$preview) {
                return response()->json([
                    'success' => false,
                    'message' => 'Preview not available',
                ], 404);
            }

            return response()->streamDownload(
                function () use ($preview) {
                    echo $preview['content'];
                },
                'preview_' . $file->original_filename,
                [
                    'Content-Type' => $preview['mime_type'],
                    'Content-Length' => strlen($preview['content']),
                ]
            );

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate preview',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get file information.
     */
    public function show(string $fileUuid): JsonResponse
    {
        try {
            $file = ChatFile::with(['chatRoom', 'chatMessage', 'uploader'])
                           ->where('uuid', $fileUuid)
                           ->firstOrFail();

            return response()->json([
                'success' => true,
                'data' => [
                    'file' => $file,
                    'download_token' => $this->fileService->generateDownloadToken($file),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'File not found',
                'error' => $e->getMessage(),
            ], 404);
        }
    }

    /**
     * Delete file.
     */
    public function destroy(string $fileUuid): JsonResponse
    {
        try {
            $file = ChatFile::where('uuid', $fileUuid)->firstOrFail();

            // Check permissions (only uploader or admin can delete)
            if ($file->uploaded_by !== auth()->id() && !auth()->user()->hasRole('admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized to delete this file',
                ], 403);
            }

            $success = $this->fileService->deleteFile($file);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'File deleted successfully',
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete file',
            ], 500);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete file',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get files for a chat room.
     */
    public function getRoomFiles(Request $request, string $roomUuid): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'type' => 'sometimes|in:all,images,documents',
            'limit' => 'sometimes|integer|min:1|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $room = ChatRoom::where('uuid', $roomUuid)->firstOrFail();
            $type = $request->get('type', 'all');
            $limit = $request->get('limit', 50);

            $query = $room->files()->with(['uploader', 'chatMessage']);

            // Apply type filter
            if ($type === 'images') {
                $query->images();
            } elseif ($type === 'documents') {
                $query->documents();
            }

            $files = $query->orderBy('created_at', 'desc')
                          ->limit($limit)
                          ->get()
                          ->map(function ($file) {
                              return array_merge($file->toArray(), [
                                  'download_token' => $this->fileService->generateDownloadToken($file),
                              ]);
                          });

            return response()->json([
                'success' => true,
                'data' => [
                    'files' => $files,
                    'room' => $room,
                ],
                'meta' => [
                    'count' => $files->count(),
                    'type_filter' => $type,
                    'limit' => $limit,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve files',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get file statistics.
     */
    public function getStatistics(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'room_id' => 'sometimes|integer|exists:chat_rooms,id',
            'date_from' => 'sometimes|date',
            'date_to' => 'sometimes|date|after_or_equal:date_from',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $filters = $request->only(['room_id', 'date_from', 'date_to']);
            $statistics = $this->fileService->getFileStatistics($filters);

            return response()->json([
                'success' => true,
                'data' => $statistics,
                'meta' => [
                    'filters' => $filters,
                    'generated_at' => now(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Determine message type based on file.
     */
    protected function getMessageType(\Illuminate\Http\UploadedFile $file): string
    {
        $imageTypes = config('chat.files.allowed_types.images', []);
        $extension = strtolower($file->getClientOriginalExtension());
        
        return in_array($extension, $imageTypes) ? 'image' : 'file';
    }

    /**
     * Parse file size string to bytes.
     */
    protected function parseFileSize(string $size): int
    {
        $units = ['B' => 1, 'KB' => 1024, 'MB' => 1048576, 'GB' => 1073741824];
        
        if (preg_match('/^(\d+(?:\.\d+)?)\s*([KMGT]?B)$/i', trim($size), $matches)) {
            $value = (float) $matches[1];
            $unit = strtoupper($matches[2]);
            
            return (int) ($value * ($units[$unit] ?? 1));
        }
        
        return (int) $size;
    }
}
