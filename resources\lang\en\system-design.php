<?php

return [
    // Page Meta
    'page_title' => 'System Design Services - ChiSolution',
    'meta_description' => 'Professional system design services including scalable architecture, microservices design, cloud infrastructure, database design, API architecture, and performance optimization for modern applications.',
    'meta_keywords' => 'system design, software architecture, scalable systems, microservices, cloud architecture, database design, API design, system architecture, distributed systems, performance optimization',

    // Hero Section
    'hero_title' => 'Professional System Design Services',
    'hero_description' => 'Build scalable, reliable, and high-performance systems that grow with your business. Expert system architecture for modern applications and distributed systems.',
    'get_started' => 'Get Started',
    'get_quote' => 'Get Quote',
    'view_portfolio' => 'View Portfolio',

    // What is System Design Section
    'what_is_title' => 'What is <span class="text-blue-600">System Design</span>?',
    'what_is_description' => 'System design is the process of defining the architecture, components, modules, interfaces, and data for a system to satisfy specified requirements. It involves creating scalable, reliable, and maintainable systems.',
    'high_level_architecture_title' => 'High-level Architecture',
    'high_level_architecture_description' => 'Define the overall structure and components of your system, including how different services interact and communicate with each other.',
    'scalability_title' => 'Scalability',
    'scalability_description' => 'Design systems that can handle increased load by scaling horizontally or vertically without compromising performance.',
    'reliability_title' => 'Reliability',
    'reliability_description' => 'Build fault-tolerant systems with redundancy, failover mechanisms, and disaster recovery strategies.',
    'performance_title' => 'Performance',
    'performance_description' => 'Optimize system performance through efficient algorithms, caching strategies, and resource management.',
    'security_title' => 'Security',
    'security_description' => 'Implement comprehensive security measures including authentication, authorization, encryption, and threat protection.',
    'data_flow_title' => 'Data Flow',
    'data_flow_description' => 'Design efficient data pipelines and storage solutions that ensure data integrity and accessibility.',

    // System Design Example Section
    'example_title' => 'System Design Example: <span class="text-blue-600">E-commerce Platform</span>',
    'example_description' => 'Here\'s how we would design a scalable e-commerce platform that can handle millions of users and transactions.',
    'load_balancer_title' => 'Load Balancer',
    'load_balancer_description' => 'Distributes incoming requests across multiple servers',
    'api_gateway_title' => 'API Gateway',
    'api_gateway_description' => 'Single entry point for all client requests',
    'microservices_title' => 'Microservices',
    'microservices_description' => 'Independent services for users, products, orders, payments',
    'databases_title' => 'Databases',
    'databases_description' => 'Optimized data storage for different service needs',
    'cache_layer_title' => 'Cache Layer',
    'cache_layer_description' => 'Redis/Memcached for fast data retrieval',
    'message_queue_title' => 'Message Queue',
    'message_queue_description' => 'Asynchronous communication between services',
    'cdn_title' => 'CDN',
    'cdn_description' => 'Global content delivery for static assets',
    'monitoring_title' => 'Monitoring',
    'monitoring_description' => 'Real-time system health and performance tracking',

    // System Design Tools Section
    'tools_title' => 'System Design <span class="text-blue-600">Tools</span>',
    'tools_description' => 'We use industry-leading tools and platforms to design, implement, and monitor scalable systems.',
    'architecture_modeling_title' => 'Architecture Modeling',
    'architecture_modeling_description' => 'Visual system design using tools like Lucidchart, Draw.io, and Miro for creating comprehensive architecture diagrams.',
    'cloud_platforms_title' => 'Cloud Platforms',
    'cloud_platforms_description' => 'AWS, Azure, and Google Cloud Platform for scalable infrastructure and managed services.',
    'containerization_title' => 'Containerization',
    'containerization_description' => 'Docker and Kubernetes for application containerization and orchestration.',
    'database_design_title' => 'Database Design',
    'database_design_description' => 'SQL and NoSQL databases including PostgreSQL, MongoDB, Redis, and Elasticsearch.',
    'monitoring_tools_title' => 'Monitoring & Observability',
    'monitoring_tools_description' => 'Prometheus, Grafana, ELK Stack, and New Relic for comprehensive system monitoring.',
    'api_design_title' => 'API Design',
    'api_design_description' => 'RESTful APIs, GraphQL, and gRPC for efficient service communication.',

    // Technology Stack Section
    'tech_stack_title' => 'Technology <span class="text-blue-600">Stack</span>',
    'tech_stack_description' => 'We leverage modern technologies and frameworks to build robust, scalable systems.',
    'backend_title' => 'Backend Technologies',
    'backend_description' => 'Node.js, Python, Java, Go, .NET for building high-performance backend services.',
    'databases_stack_title' => 'Databases',
    'databases_stack_description' => 'PostgreSQL, MongoDB, Redis, Elasticsearch, InfluxDB for various data storage needs.',
    'cloud_devops_title' => 'Cloud & DevOps',
    'cloud_devops_description' => 'AWS, Azure, GCP, Docker, Kubernetes, Terraform for cloud infrastructure and deployment.',
    'message_queues_title' => 'Message Queues',
    'message_queues_description' => 'RabbitMQ, Apache Kafka, Amazon SQS for asynchronous communication.',

    // System Design Process Section
    'process_title' => 'Our System Design <span class="text-blue-600">Process</span>',
    'process_description' => 'We follow a systematic approach to design scalable systems that meet your business requirements.',
    'step_requirements_title' => 'Requirements Analysis',
    'step_requirements_description' => 'Understand functional and non-functional requirements, constraints, and business objectives.',
    'step_high_level_title' => 'High-Level Design',
    'step_high_level_description' => 'Create system architecture overview, identify major components and their interactions.',
    'step_detailed_title' => 'Detailed Design',
    'step_detailed_description' => 'Design individual components, APIs, data models, and define implementation details.',
    'step_implementation_title' => 'Implementation & Testing',
    'step_implementation_description' => 'Build the system following the design, conduct testing, and optimize performance.',

    // Why Choose Us Section
    'why_choose_title' => 'Why Choose Our System Design Services?',
    'why_choose_description' => 'We combine deep technical expertise with practical experience to deliver systems that scale and perform.',
    'scalable_solutions_title' => 'Scalable Solutions',
    'scalable_solutions_description' => 'Design systems that grow with your business and handle increasing load efficiently.',
    'proven_expertise_title' => 'Proven Expertise',
    'proven_expertise_description' => 'Years of experience designing systems for startups to enterprise-level applications.',
    'modern_technologies_title' => 'Modern Technologies',
    'modern_technologies_description' => 'Leverage cutting-edge technologies and best practices for optimal performance.',
    'cost_effective_title' => 'Cost-Effective',
    'cost_effective_description' => 'Optimize infrastructure costs while maintaining high performance and reliability.',
    'security_first_title' => 'Security-First',
    'security_first_description' => 'Built-in security measures and compliance with industry standards.',
    'ongoing_optimization_title' => 'Ongoing Optimization',
    'optimization_description' => 'Continuous monitoring and optimization to ensure peak performance.',

    // CTA Section
    'cta_title' => 'Ready to Build Your Scalable System?',
    'cta_description' => 'Let\'s design a system architecture that scales with your business and delivers exceptional performance.',
    'start_project' => 'Start Your System Design Project',
    'free_consultation' => 'Free Architecture Consultation',
    'view_work' => 'View Our Work',

    // System Components
    'system_components' => [
        'Load Balancers' => 'Load Balancers',
        'API Gateways' => 'API Gateways',
        'Microservices' => 'Microservices',
        'Databases' => 'Databases',
        'Caching' => 'Caching',
        'Message Queues' => 'Message Queues',
        'CDN' => 'CDN',
        'Monitoring' => 'Monitoring',
    ],

    // Architecture Patterns
    'architecture_patterns' => [
        'Microservices Architecture' => 'Microservices Architecture',
        'Event-Driven Architecture' => 'Event-Driven Architecture',
        'Serverless Architecture' => 'Serverless Architecture',
        'Layered Architecture' => 'Layered Architecture',
    ],

    // Scalability Features
    'scalability_features' => [
        'Horizontal Scaling' => 'Horizontal Scaling',
        'Vertical Scaling' => 'Vertical Scaling',
        'Auto-scaling' => 'Auto-scaling',
        'Load Distribution' => 'Load Distribution',
    ],

    // Performance Features
    'performance_features' => [
        'Caching Strategies' => 'Caching Strategies',
        'Database Optimization' => 'Database Optimization',
        'CDN Integration' => 'CDN Integration',
        'Performance Monitoring' => 'Performance Monitoring',
    ],
];
