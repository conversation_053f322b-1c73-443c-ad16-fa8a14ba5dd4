/**
 * Modern Chat Widget Styles
 *
 * Comprehensive styles for the floating chat widget with modern design,
 * animations, responsive behavior, and theme support
 *
 * @version 1.0.0
 * <AUTHOR>
 */

/* CSS Custom Properties for easy theming */
:root {
    --chat-primary-gradient: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
    --chat-secondary-gradient: linear-gradient(135deg, #14b8a6 0%, #0d9488 100%);
    --chat-success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --chat-border-radius: 16px;
    --chat-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --chat-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Base Widget Styles */
.modern-chat-widget {
    position: fixed;
    z-index: 9999;
    font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>I', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #374151;
}

/* Position Variants */
.modern-chat-widget--bottom-right {
    bottom: 20px;
    right: 20px;
}

.modern-chat-widget--bottom-left {
    bottom: 20px;
    left: 20px;
}

.modern-chat-widget--top-right {
    top: 20px;
    right: 20px;
}

.modern-chat-widget--top-left {
    top: 20px;
    left: 20px;
}

/* Chat Toggle Button */
.chat-toggle {
    position: relative;
    width: 60px;
    height: 60px;
    background: var(--chat-primary-gradient);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: var(--chat-transition);
    overflow: hidden;
}

.chat-toggle:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.chat-toggle.active {
    background: var(--chat-secondary-gradient);
}

.chat-toggle-icon {
    position: relative;
    width: 24px;
    height: 24px;
    color: white;
}

.chat-toggle-icon svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transition: all 0.3s ease;
}

.chat-toggle .close-icon {
    opacity: 0;
    transform: rotate(90deg);
}

.chat-toggle.active .chat-icon {
    opacity: 0;
    transform: rotate(-90deg);
}

.chat-toggle.active .close-icon {
    opacity: 1;
    transform: rotate(0deg);
}

/* Unread Badge */
.unread-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ef4444;
    color: white;
    border-radius: 50%;
    min-width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: 600;
    border: 2px solid white;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Online Indicator */
.online-indicator {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 12px;
    height: 12px;
    background: #10b981;
    border-radius: 50%;
    border: 2px solid white;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.online-indicator.online {
    opacity: 1;
}

/* Chat Window */
.chat-window {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 380px;
    height: 600px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    animation: slideInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: bottom right;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.chat-window.minimized {
    height: 60px;
    overflow: hidden;
}

/* Chat Header */
.chat-header {
    background: var(--chat-primary-gradient);
    color: white;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 16px 16px 0 0;
}

.chat-header-info {
    flex: 1;
}

.chat-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 2px;
}

.chat-status {
    font-size: 12px;
    opacity: 0.9;
}

.chat-header-actions {
    display: flex;
    gap: 8px;
}

.chat-action-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 6px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background 0.2s ease;
    color: white;
}

.chat-action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.chat-action-btn svg {
    width: 16px;
    height: 16px;
}

/* Messages Container */
.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    scroll-behavior: smooth;
}

.chat-messages::-webkit-scrollbar {
    width: 4px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f5f9;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 2px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Welcome Message */
.welcome-message {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 20px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 12px;
    border-left: 4px solid #667eea;
}

.welcome-avatar {
    width: 40px;
    height: 40px;
    background: var(--chat-primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.welcome-avatar svg {
    width: 20px;
    height: 20px;
}

.welcome-text h4 {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
}

.welcome-text p {
    margin: 0;
    color: #6b7280;
    font-size: 14px;
}

/* Chat Messages */
.chat-message {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    margin-bottom: 16px;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.chat-message.message-visible {
    opacity: 1;
    transform: translateY(0);
}

.chat-message--own {
    flex-direction: row-reverse;
}

.chat-message--own .message-content {
    background: var(--chat-primary-gradient);
    color: white;
}

.chat-message--other .message-content {
    background: #f1f5f9;
    color: #374151;
}

.chat-message--ai .message-content {
    background: var(--chat-success-gradient);
    color: white;
}

.message-avatar {
    width: 32px;
    height: 32px;
    background: #e5e7eb;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    flex-shrink: 0;
}

.message-avatar svg {
    width: 16px;
    height: 16px;
}

.message-content {
    max-width: 280px;
    border-radius: 18px;
    padding: 12px 16px;
    position: relative;
}

.message-sender {
    font-size: 11px;
    font-weight: 600;
    margin-bottom: 4px;
    opacity: 0.8;
}

.message-text {
    word-wrap: break-word;
    line-height: 1.4;
}

.message-text a {
    color: inherit;
    text-decoration: underline;
}

.message-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 4px;
    font-size: 11px;
    opacity: 0.7;
}

.message-time {
    font-size: 11px;
}

.message-status {
    margin-left: 4px;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0 20px 16px;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.typing-indicator.show {
    opacity: 1;
    transform: translateY(0);
}

.typing-avatar {
    width: 32px;
    height: 32px;
    background: #e5e7eb;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
}

.typing-avatar svg {
    width: 16px;
    height: 16px;
}

.typing-text {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #6b7280;
}

.typing-dots {
    display: flex;
    gap: 2px;
}

.typing-dots span {
    width: 4px;
    height: 4px;
    background: #9ca3af;
    border-radius: 50%;
    animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.4;
    }
    30% {
        transform: translateY(-8px);
        opacity: 1;
    }
}

/* Input Area */
.chat-input-area {
    border-top: 1px solid #e5e7eb;
    background: white;
    border-radius: 0 0 16px 16px;
}

.chat-input-container {
    padding: 16px 20px;
    display: flex;
    align-items: flex-end;
    gap: 12px;
}

.chat-input {
    flex: 1;
    border: 1px solid #d1d5db;
    border-radius: 20px;
    padding: 10px 16px;
    font-size: 14px;
    line-height: 1.4;
    resize: none;
    outline: none;
    transition: border-color 0.2s ease;
    min-height: 40px;
    max-height: 120px;
    font-family: inherit;
}

.chat-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.chat-input::placeholder {
    color: #9ca3af;
}

.chat-input-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.chat-input-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: #f3f4f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #6b7280;
}

.chat-input-btn:hover {
    background: #e5e7eb;
    color: #374151;
}

.chat-input-btn svg {
    width: 18px;
    height: 18px;
}

.chat-send-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: var(--chat-primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: white;
}

.chat-send-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.chat-send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.chat-send-btn svg {
    width: 18px;
    height: 18px;
}

.chat-input-footer {
    padding: 8px 20px 12px;
    text-align: center;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.powered-by {
    font-size: 11px;
    color: #9ca3af;
    text-decoration: none;
}

.powered-by:hover {
    color: #667eea;
}

.work-by {
    font-size: 11px;
    color: #0891b2;
    text-decoration: none;
    font-weight: 500;
}

.work-by:hover {
    color: #0e7490;
    text-decoration: underline;
}

/* Error Messages */
.chat-message--error .message-content {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

/* Tablet Responsive */
@media (max-width: 1024px) and (min-width: 769px) {
    .chat-window {
        width: 360px;
        height: 550px;
    }

    .modern-chat-widget--bottom-right,
    .modern-chat-widget--bottom-left {
        bottom: 15px;
        right: 15px;
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .modern-chat-widget--bottom-right,
    .modern-chat-widget--bottom-left {
        bottom: 10px;
        right: 10px;
        left: 10px;
    }

    .chat-window {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100%;
        height: 100%;
        border-radius: 0;
        animation: slideInUp 0.3s ease;
    }

    .chat-header {
        border-radius: 0;
        padding: 16px 20px;
        padding-top: calc(16px + env(safe-area-inset-top));
    }

    .chat-messages {
        padding: 16px;
        padding-bottom: calc(16px + env(safe-area-inset-bottom));
    }

    .chat-input-area {
        border-radius: 0;
        padding-bottom: env(safe-area-inset-bottom);
    }

    .message-content {
        max-width: calc(100vw - 120px);
    }

    .chat-toggle {
        width: 56px;
        height: 56px;
    }

    .chat-toggle-icon {
        width: 22px;
        height: 22px;
    }
}

/* Small Mobile Responsive */
@media (max-width: 480px) {
    .chat-toggle {
        width: 52px;
        height: 52px;
        bottom: 15px;
        right: 15px;
    }

    .chat-toggle-icon {
        width: 20px;
        height: 20px;
    }

    .message-content {
        max-width: calc(100vw - 100px);
        font-size: 14px;
    }

    .chat-input {
        font-size: 16px; /* Prevents zoom on iOS */
    }
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
    .modern-chat-widget--dark .chat-window {
        background: #1f2937;
        color: #f9fafb;
    }

    .modern-chat-widget--dark .chat-messages::-webkit-scrollbar-track {
        background: #374151;
    }

    .modern-chat-widget--dark .chat-messages::-webkit-scrollbar-thumb {
        background: #6b7280;
    }

    .modern-chat-widget--dark .welcome-message {
        background: #374151;
        border-left-color: #667eea;
    }

    .modern-chat-widget--dark .welcome-text h4 {
        color: #f9fafb;
    }

    .modern-chat-widget--dark .welcome-text p {
        color: #d1d5db;
    }

    .modern-chat-widget--dark .chat-message--other .message-content {
        background: #374151;
        color: #f9fafb;
    }

    .modern-chat-widget--dark .message-avatar {
        background: #4b5563;
        color: #d1d5db;
    }

    .modern-chat-widget--dark .typing-avatar {
        background: #4b5563;
        color: #d1d5db;
    }

    .modern-chat-widget--dark .typing-text {
        color: #d1d5db;
    }

    .modern-chat-widget--dark .chat-input-area {
        background: #1f2937;
        border-top-color: #374151;
    }

    .modern-chat-widget--dark .chat-input {
        background: #374151;
        border-color: #4b5563;
        color: #f9fafb;
    }

    .modern-chat-widget--dark .chat-input::placeholder {
        color: #9ca3af;
    }

    .modern-chat-widget--dark .chat-input:focus {
        border-color: #667eea;
    }

    .modern-chat-widget--dark .chat-input-btn {
        background: #374151;
        color: #d1d5db;
    }

    .modern-chat-widget--dark .chat-input-btn:hover {
        background: #4b5563;
        color: #f9fafb;
    }

    .modern-chat-widget--dark .powered-by {
        color: #9ca3af;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .chat-toggle {
        border: 2px solid #000;
    }

    .chat-window {
        border: 2px solid #000;
    }

    .chat-input {
        border: 2px solid #000;
    }

    .chat-input:focus {
        border-color: #0066cc;
        box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.3);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .chat-toggle,
    .chat-window,
    .chat-message,
    .typing-indicator,
    .chat-send-btn {
        animation: none;
        transition: none;
    }

    .typing-dots span {
        animation: none;
    }

    .unread-badge {
        animation: none;
    }
}

/* Print Styles */
@media print {
    .modern-chat-widget {
        display: none;
    }
}
