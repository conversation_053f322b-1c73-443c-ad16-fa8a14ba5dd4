<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Services\ChatAIService;
use App\Services\ChatService;
use App\Models\ChatRoom;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class ChatAIController extends Controller
{
    public function __construct(
        protected ChatAIService $chatAIService,
        protected ChatService $chatService
    ) {
        $this->middleware('auth:sanctum');
    }

    /**
     * Generate AI response with provider selection.
     */
    public function generateResponse(Request $request): JsonResponse
    {
        $request->validate([
            'room_uuid' => 'required|string|exists:chat_rooms,uuid',
            'message' => 'required|string|max:2000',
            'provider' => 'nullable|string|in:openai,anthropic,google,xai',
            'model' => 'nullable|string',
            'temperature' => 'nullable|numeric|min:0|max:2',
            'max_tokens' => 'nullable|integer|min:1|max:4000',
            'context' => 'nullable|array',
        ]);

        try {
            $room = ChatRoom::where('uuid', $request->input('room_uuid'))->firstOrFail();
            
            // Check if user has access to this room
            if (!$this->chatService->userCanAccessRoom(auth()->user(), $room)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied to this chat room.',
                ], 403);
            }

            $context = array_merge($request->input('context', []), [
                'temperature' => $request->input('temperature'),
                'max_tokens' => $request->input('max_tokens'),
            ]);

            $response = $this->chatAIService->generateResponseWithProvider(
                $request->input('message'),
                $room,
                $context,
                $request->input('provider'),
                $request->input('model')
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'response' => $response['response'],
                    'confidence' => $response['confidence'],
                    'intent' => $response['intent'],
                    'should_escalate' => $response['should_escalate'],
                    'provider' => $response['provider_used'],
                    'model' => $response['model_used'],
                    'processing_time_ms' => $response['processing_time_ms'],
                    'tokens_used' => $response['tokens_used'],
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate AI response: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Analyze sentiment of a message.
     */
    public function analyzeSentiment(Request $request): JsonResponse
    {
        $request->validate([
            'text' => 'required|string|max:2000',
            'provider' => 'nullable|string|in:openai,anthropic,google,xai',
            'model' => 'nullable|string',
        ]);

        try {
            $result = $this->chatAIService->analyzeSentimentWithAI(
                $request->input('text'),
                $request->input('provider'),
                $request->input('model')
            );

            return response()->json([
                'success' => true,
                'data' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Sentiment analysis failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Translate text using AI.
     */
    public function translate(Request $request): JsonResponse
    {
        $request->validate([
            'text' => 'required|string|max:2000',
            'target_language' => 'required|string|max:10',
            'source_language' => 'nullable|string|max:10',
            'provider' => 'nullable|string|in:openai,anthropic,google,xai',
            'model' => 'nullable|string',
        ]);

        try {
            $result = $this->chatAIService->translateWithAI(
                $request->input('text'),
                $request->input('target_language'),
                $request->input('source_language'),
                $request->input('provider'),
                $request->input('model')
            );

            return response()->json([
                'success' => true,
                'data' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Translation failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Summarize conversation.
     */
    public function summarizeConversation(Request $request): JsonResponse
    {
        $request->validate([
            'room_uuid' => 'required|string|exists:chat_rooms,uuid',
            'max_length' => 'nullable|integer|min:50|max:500',
            'provider' => 'nullable|string|in:openai,anthropic,google,xai',
            'model' => 'nullable|string',
        ]);

        try {
            $room = ChatRoom::where('uuid', $request->input('room_uuid'))->firstOrFail();
            
            // Check if user has access to this room
            if (!$this->chatService->userCanAccessRoom(auth()->user(), $room)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied to this chat room.',
                ], 403);
            }

            $result = $this->chatAIService->summarizeConversationWithAI(
                $room,
                $request->input('max_length', 150),
                $request->input('provider'),
                $request->input('model')
            );

            return response()->json([
                'success' => true,
                'data' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Conversation summary failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Extract conversation topics.
     */
    public function extractTopics(Request $request): JsonResponse
    {
        $request->validate([
            'room_uuid' => 'required|string|exists:chat_rooms,uuid',
            'max_topics' => 'nullable|integer|min:1|max:10',
            'provider' => 'nullable|string|in:openai,anthropic,google,xai',
            'model' => 'nullable|string',
        ]);

        try {
            $room = ChatRoom::where('uuid', $request->input('room_uuid'))->firstOrFail();
            
            // Check if user has access to this room
            if (!$this->chatService->userCanAccessRoom(auth()->user(), $room)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied to this chat room.',
                ], 403);
            }

            $result = $this->chatAIService->extractConversationTopics(
                $room,
                $request->input('max_topics', 5),
                $request->input('provider'),
                $request->input('model')
            );

            return response()->json([
                'success' => true,
                'data' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Topic extraction failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get available AI providers and models.
     */
    public function getProviders(): JsonResponse
    {
        try {
            $providers = $this->chatAIService->getAvailableProvidersAndModels();
            
            return response()->json([
                'success' => true,
                'data' => $providers,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch providers: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get AI usage statistics (for authenticated users).
     */
    public function getUsageStats(): JsonResponse
    {
        try {
            $stats = $this->chatAIService->getAIUsageStats();
            
            // Filter sensitive information for non-admin users
            if (!auth()->user()->hasRole('admin')) {
                foreach ($stats as $provider => &$providerStats) {
                    unset($providerStats['cost_today']);
                    unset($providerStats['tokens_used_today']);
                }
            }
            
            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch usage stats: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Test AI provider (admin only).
     */
    public function testProvider(Request $request): JsonResponse
    {
        if (!auth()->user()->hasRole('admin')) {
            return response()->json([
                'success' => false,
                'message' => 'Admin access required.',
            ], 403);
        }

        $request->validate([
            'provider' => 'required|string|in:openai,anthropic,google,xai',
            'model' => 'nullable|string',
            'message' => 'nullable|string|max:500',
        ]);

        try {
            $message = $request->input('message', 'Hello, this is a test message.');
            
            $response = $this->chatAIService->generateResponseWithProvider(
                $message,
                new ChatRoom(['language' => 'en']), // Temporary room for testing
                ['temperature' => 0.7, 'max_tokens' => 100],
                $request->input('provider'),
                $request->input('model')
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'response' => $response['response'],
                    'provider' => $response['provider_used'],
                    'model' => $response['model_used'],
                    'processing_time_ms' => $response['processing_time_ms'],
                    'tokens_used' => $response['tokens_used'],
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Provider test failed: ' . $e->getMessage(),
            ], 500);
        }
    }
}
