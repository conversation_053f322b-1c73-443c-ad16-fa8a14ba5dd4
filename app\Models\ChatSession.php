<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ChatSession extends Model
{
    use HasFactory;

    protected $fillable = [
        'chat_room_id',
        'session_start',
        'session_end',
        'duration_seconds',
        'message_count',
        'participant_count',
        'ai_message_count',
        'staff_response_time_avg',
        'customer_satisfaction',
        'resolution_status',
        'tags',
    ];

    protected $casts = [
        'session_start' => 'datetime',
        'session_end' => 'datetime',
        'duration_seconds' => 'integer',
        'message_count' => 'integer',
        'participant_count' => 'integer',
        'ai_message_count' => 'integer',
        'staff_response_time_avg' => 'integer',
        'customer_satisfaction' => 'integer',
        'tags' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->session_start)) {
                $model->session_start = now();
            }
        });
    }

    /**
     * Scope for active sessions.
     */
    public function scopeActive($query)
    {
        return $query->whereNull('session_end');
    }

    /**
     * Scope for completed sessions.
     */
    public function scopeCompleted($query)
    {
        return $query->whereNotNull('session_end');
    }

    /**
     * Scope for resolved sessions.
     */
    public function scopeResolved($query)
    {
        return $query->where('resolution_status', 'resolved');
    }

    /**
     * Scope for escalated sessions.
     */
    public function scopeEscalated($query)
    {
        return $query->where('resolution_status', 'escalated');
    }

    /**
     * Scope for abandoned sessions.
     */
    public function scopeAbandoned($query)
    {
        return $query->where('resolution_status', 'abandoned');
    }

    /**
     * Scope for sessions with high satisfaction.
     */
    public function scopeHighSatisfaction($query)
    {
        return $query->where('customer_satisfaction', '>=', 4);
    }

    /**
     * Get the chat room this session belongs to.
     */
    public function chatRoom(): BelongsTo
    {
        return $this->belongsTo(ChatRoom::class);
    }

    /**
     * Check if session is active.
     */
    public function isActive(): bool
    {
        return is_null($this->session_end);
    }

    /**
     * Check if session is completed.
     */
    public function isCompleted(): bool
    {
        return !is_null($this->session_end);
    }

    /**
     * Get session duration in human readable format.
     */
    public function getDurationHumanAttribute(): string
    {
        if (!$this->duration_seconds) {
            return 'N/A';
        }

        $hours = floor($this->duration_seconds / 3600);
        $minutes = floor(($this->duration_seconds % 3600) / 60);
        $seconds = $this->duration_seconds % 60;

        if ($hours > 0) {
            return sprintf('%dh %dm %ds', $hours, $minutes, $seconds);
        } elseif ($minutes > 0) {
            return sprintf('%dm %ds', $minutes, $seconds);
        } else {
            return sprintf('%ds', $seconds);
        }
    }

    /**
     * Get AI usage percentage.
     */
    public function getAiUsagePercentageAttribute(): float
    {
        if ($this->message_count === 0) {
            return 0;
        }

        return round(($this->ai_message_count / $this->message_count) * 100, 2);
    }

    /**
     * Get average response time in human readable format.
     */
    public function getStaffResponseTimeHumanAttribute(): string
    {
        if (!$this->staff_response_time_avg) {
            return 'N/A';
        }

        $minutes = floor($this->staff_response_time_avg / 60);
        $seconds = $this->staff_response_time_avg % 60;

        if ($minutes > 0) {
            return sprintf('%dm %ds', $minutes, $seconds);
        } else {
            return sprintf('%ds', $seconds);
        }
    }

    /**
     * Get resolution status label.
     */
    public function getResolutionStatusLabelAttribute(): string
    {
        return match($this->resolution_status) {
            'resolved' => 'Resolved',
            'unresolved' => 'Unresolved',
            'escalated' => 'Escalated',
            'abandoned' => 'Abandoned',
            default => 'Unknown'
        };
    }

    /**
     * Get satisfaction label.
     */
    public function getSatisfactionLabelAttribute(): string
    {
        if (!$this->customer_satisfaction) {
            return 'Not Rated';
        }

        return match($this->customer_satisfaction) {
            1 => 'Very Poor',
            2 => 'Poor',
            3 => 'Average',
            4 => 'Good',
            5 => 'Excellent',
            default => 'Unknown'
        };
    }

    /**
     * Get session efficiency score (0-100).
     */
    public function getEfficiencyScoreAttribute(): int
    {
        $score = 0;

        // Resolution status (40 points)
        if ($this->resolution_status === 'resolved') {
            $score += 40;
        } elseif ($this->resolution_status === 'escalated') {
            $score += 20;
        }

        // Response time (30 points)
        if ($this->staff_response_time_avg) {
            if ($this->staff_response_time_avg <= 30) {
                $score += 30;
            } elseif ($this->staff_response_time_avg <= 60) {
                $score += 20;
            } elseif ($this->staff_response_time_avg <= 120) {
                $score += 10;
            }
        }

        // Customer satisfaction (30 points)
        if ($this->customer_satisfaction) {
            $score += ($this->customer_satisfaction / 5) * 30;
        }

        return min(100, $score);
    }

    /**
     * Add tag to session.
     */
    public function addTag(string $tag): bool
    {
        $tags = $this->tags ?? [];
        
        if (!in_array($tag, $tags)) {
            $tags[] = $tag;
            $this->tags = $tags;
            return $this->save();
        }

        return true;
    }

    /**
     * Remove tag from session.
     */
    public function removeTag(string $tag): bool
    {
        $tags = $this->tags ?? [];
        
        if (($key = array_search($tag, $tags)) !== false) {
            unset($tags[$key]);
            $this->tags = array_values($tags);
            return $this->save();
        }

        return true;
    }

    /**
     * Check if session has tag.
     */
    public function hasTag(string $tag): bool
    {
        $tags = $this->tags ?? [];
        return in_array($tag, $tags);
    }

    /**
     * End the session.
     */
    public function end(): bool
    {
        if ($this->isActive()) {
            $this->session_end = now();
            $this->duration_seconds = $this->session_start->diffInSeconds($this->session_end);
            return $this->save();
        }

        return false;
    }

    /**
     * Update session statistics.
     */
    public function updateStatistics(): bool
    {
        $chatRoom = $this->chatRoom;
        
        // Update message count
        $this->message_count = $chatRoom->messages()->count();
        
        // Update AI message count
        $this->ai_message_count = $chatRoom->messages()->where('is_ai_generated', true)->count();
        
        // Update participant count
        $this->participant_count = $chatRoom->participants()->count();
        
        // Calculate average staff response time
        $this->calculateStaffResponseTime();
        
        // Get customer satisfaction from rating
        $rating = $chatRoom->rating;
        if ($rating) {
            $this->customer_satisfaction = $rating->rating;
        }

        return $this->save();
    }

    /**
     * Calculate average staff response time.
     */
    private function calculateStaffResponseTime(): void
    {
        $messages = $this->chatRoom->messages()
                         ->with('user')
                         ->orderBy('created_at')
                         ->get();

        $responseTimes = [];
        $lastCustomerMessage = null;

        foreach ($messages as $message) {
            if ($message->isFromCustomer()) {
                $lastCustomerMessage = $message;
            } elseif ($message->isFromStaff() && $lastCustomerMessage) {
                $responseTime = $lastCustomerMessage->created_at->diffInSeconds($message->created_at);
                $responseTimes[] = $responseTime;
                $lastCustomerMessage = null;
            }
        }

        if (!empty($responseTimes)) {
            $this->staff_response_time_avg = array_sum($responseTimes) / count($responseTimes);
        }
    }

    /**
     * Static method to get session statistics for a date range.
     */
    public static function getStatistics(\DateTime $startDate, \DateTime $endDate): array
    {
        $sessions = static::whereBetween('session_start', [$startDate, $endDate])->get();

        return [
            'total_sessions' => $sessions->count(),
            'completed_sessions' => $sessions->where('session_end', '!=', null)->count(),
            'average_duration' => $sessions->avg('duration_seconds'),
            'average_messages' => $sessions->avg('message_count'),
            'average_satisfaction' => $sessions->avg('customer_satisfaction'),
            'resolution_rate' => $sessions->count() > 0 ? 
                ($sessions->where('resolution_status', 'resolved')->count() / $sessions->count()) * 100 : 0,
            'ai_usage_rate' => $sessions->avg('ai_usage_percentage'),
        ];
    }
}
