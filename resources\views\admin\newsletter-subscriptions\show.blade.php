@extends('layouts.dashboard')

@section('title', 'Newsletter Subscription Details - Admin')

@section('content')
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-start">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Newsletter Subscription Details</h1>
            <p class="text-gray-600 mt-1">Subscribed {{ $newsletterSubscription->created_at->format('M j, Y \a\t g:i A') }}</p>
        </div>
        <div class="flex items-center space-x-2">
            @if($newsletterSubscription->is_active)
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Active
                </span>
            @else
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    Inactive
                </span>
            @endif
            @if($newsletterSubscription->email_verified_at)
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                    Verified
                </span>
            @else
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    Unverified
                </span>
            @endif
        </div>
    </div>

    <!-- Subscriber Information -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
        <h2 class="text-xl font-bold mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            Subscriber Information
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Email Address</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                        <a href="mailto:{{ $newsletterSubscription->email }}" class="text-blue-600 hover:text-blue-800">
                            {{ $newsletterSubscription->email }}
                        </a>
                    </dd>
                </div>
                @if($newsletterSubscription->name)
                <div>
                    <dt class="text-sm font-medium text-gray-500">Name</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ $newsletterSubscription->name }}</dd>
                </div>
                @endif
            </div>
            <div class="space-y-4">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Subscription Status</dt>
                    <dd class="mt-1">
                        @if($newsletterSubscription->is_active)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Active Subscriber
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Inactive Subscriber
                            </span>
                        @endif
                    </dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Email Verification</dt>
                    <dd class="mt-1">
                        @if($newsletterSubscription->email_verified_at)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                Verified
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                Unverified
                            </span>
                        @endif
                    </dd>
                </div>
            </div>
        </div>
    </div>

    <!-- Subscription Timeline -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
        <h2 class="text-xl font-bold mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Subscription Timeline
        </h2>

        @if($newsletterSubscription->history->count() > 0)
            <div class="space-y-4">
                @foreach($newsletterSubscription->history as $historyItem)
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-{{ $historyItem->action_color }}-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-{{ $historyItem->action_color }}-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{{ $historyItem->action_icon }}"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-3 flex-1">
                            <div class="flex items-center justify-between">
                                <p class="text-sm font-medium text-gray-900">{{ $historyItem->formatted_description }}</p>
                                @if($historyItem->triggered_by === 'admin' && $historyItem->adminUser)
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        Admin: {{ $historyItem->adminUser->name }}
                                    </span>
                                @elseif($historyItem->triggered_by === 'user')
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        User Action
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        System
                                    </span>
                                @endif
                            </div>
                            <p class="text-sm text-gray-500">{{ $historyItem->created_at->format('M j, Y \a\t g:i A') }}</p>

                            @if($historyItem->status_from && $historyItem->status_to)
                                <p class="text-xs text-gray-400 mt-1">
                                    Status changed from <span class="font-medium">{{ ucfirst($historyItem->status_from) }}</span>
                                    to <span class="font-medium">{{ ucfirst($historyItem->status_to) }}</span>
                                </p>
                            @endif

                            @if($historyItem->metadata && is_array($historyItem->metadata) && count($historyItem->metadata) > 0)
                                <div class="mt-2">
                                    <button type="button"
                                            onclick="toggleMetadata('{{ $historyItem->uuid }}')"
                                            class="text-xs text-blue-600 hover:text-blue-800 focus:outline-none">
                                        View Details
                                    </button>
                                    <div id="metadata-{{ $historyItem->uuid }}" class="hidden mt-2 p-2 bg-gray-50 rounded text-xs">
                                        @foreach($historyItem->metadata as $key => $value)
                                            <div class="flex justify-between py-1">
                                                <span class="font-medium text-gray-600">{{ ucfirst(str_replace('_', ' ', $key)) }}:</span>
                                                <span class="text-gray-800">{{ is_bool($value) ? ($value ? 'Yes' : 'No') : $value }}</span>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <!-- Fallback to basic timeline if no history exists -->
            <div class="space-y-4">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-900">Subscribed to Newsletter</p>
                        <p class="text-sm text-gray-500">{{ $newsletterSubscription->created_at->format('M j, Y \a\t g:i A') }}</p>
                    </div>
                </div>

                @if($newsletterSubscription->email_verified_at)
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-900">Email Verified</p>
                        <p class="text-sm text-gray-500">{{ $newsletterSubscription->email_verified_at->format('M j, Y \a\t g:i A') }}</p>
                    </div>
                </div>
                @endif

                @if(!$newsletterSubscription->is_active)
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-900">Subscription Deactivated</p>
                        <p class="text-sm text-gray-500">Status changed to inactive</p>
                    </div>
                </div>
                @endif
            </div>
        @endif
    </div>

    <!-- Technical Information -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
        <h2 class="text-xl font-bold mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Technical Information
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Subscription ID</dt>
                    <dd class="mt-1 text-sm text-gray-900 font-mono">{{ $newsletterSubscription->uuid }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">IP Address</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ $newsletterSubscription->ip_address ?: 'Not recorded' }}</dd>
                </div>
                @if($newsletterSubscription->verification_token)
                <div>
                    <dt class="text-sm font-medium text-gray-500">Verification Token</dt>
                    <dd class="mt-1 text-sm text-gray-900 font-mono break-all">{{ Str::limit($newsletterSubscription->verification_token, 50) }}</dd>
                </div>
                @endif
            </div>
            <div class="space-y-4">
                @if($newsletterSubscription->user_agent)
                <div>
                    <dt class="text-sm font-medium text-gray-500">User Agent</dt>
                    <dd class="mt-1 text-sm text-gray-900 break-all">{{ Str::limit($newsletterSubscription->user_agent, 100) }}</dd>
                </div>
                @endif
                <div>
                    <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ $newsletterSubscription->updated_at->format('M j, Y g:i A') }}</dd>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
        <h2 class="text-xl font-bold mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
            Quick Actions
        </h2>
        <div class="flex flex-wrap gap-3">
            <a href="mailto:{{ $newsletterSubscription->email }}?subject=Newsletter Update" 
               class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                Send Email
            </a>

            @if($newsletterSubscription->is_active)
                <button onclick="toggleStatus('{{ $newsletterSubscription->uuid }}', true)"
                        class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-red-600 text-white hover:bg-red-700 focus:ring-red-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Deactivate Subscription
                </button>
            @else
                <button onclick="toggleStatus('{{ $newsletterSubscription->uuid }}', false)"
                        class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-green-600 text-white hover:bg-green-700 focus:ring-green-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Activate Subscription
                </button>
            @endif

            <a href="{{ route('admin.newsletter-subscriptions.index') }}"
               class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to List
            </a>

            <form method="POST" action="{{ route('admin.newsletter-subscriptions.destroy', $newsletterSubscription) }}"
                  class="inline" onsubmit="return confirm('Are you sure you want to delete this subscription? This action cannot be undone.')">
                @csrf
                @method('DELETE')
                <button type="submit"
                        class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-red-600 text-white hover:bg-red-700 focus:ring-red-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Delete Subscription
                </button>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Toggle subscription status
function toggleStatus(uuid, currentStatus) {
    fetch(`/admin/newsletter-subscriptions/${uuid}/toggle-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            setTimeout(() => location.reload(), 1000); // Refresh to update UI
        } else {
            showToast('Failed to update subscription status', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred', 'error');
    });
}

// Toggle metadata visibility
function toggleMetadata(uuid) {
    const metadataDiv = document.getElementById(`metadata-${uuid}`);
    const button = event.target;

    if (metadataDiv.classList.contains('hidden')) {
        metadataDiv.classList.remove('hidden');
        button.textContent = 'Hide Details';
    } else {
        metadataDiv.classList.add('hidden');
        button.textContent = 'View Details';
    }
}

// Toast notification function
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    const bgColor = {
        'success': 'bg-green-500',
        'error': 'bg-red-500',
        'warning': 'bg-yellow-500',
        'info': 'bg-blue-500'
    }[type] || 'bg-blue-500';

    toast.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-transform duration-300 translate-x-full`;
    toast.textContent = message;

    document.body.appendChild(toast);

    // Slide in
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 100);

    // Slide out and remove
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 3000);
}
</script>
@endpush
