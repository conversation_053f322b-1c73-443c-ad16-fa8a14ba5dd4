@extends('layouts.dashboard')

@section('title', 'Activity Log Details - Admin Dashboard')
@section('page_title', 'Activity Log Details')

@push('styles')
<style>
    .risk-minimal { @apply bg-green-100 text-green-800; }
    .risk-low { @apply bg-yellow-100 text-yellow-800; }
    .risk-medium { @apply bg-orange-100 text-orange-800; }
    .risk-high { @apply bg-red-100 text-red-800; }

    .status-success { @apply bg-green-100 text-green-800; }
    .status-failed { @apply bg-red-100 text-red-800; }
    .status-blocked { @apply bg-orange-100 text-orange-800; }
    .status-pending { @apply bg-yellow-100 text-yellow-800; }
</style>
@endpush

@section('content')
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Activity Log Details</h1>
            <p class="text-gray-600">Detailed information about this security event</p>
        </div>
        <a href="{{ route('admin.activity-logs.index') }}"
                       class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                        ← Back to Logs
                    </a>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Main Details -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Activity Overview -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Activity Overview</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Activity Type</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ ucwords(str_replace('_', ' ', $activityLog->activity_type)) }}</dd>
                                </div>
                                
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                                    <dd class="mt-1">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium status-{{ $activityLog->status }}">
                                            {{ ucfirst($activityLog->status) }}
                                        </span>
                                    </dd>
                                </div>
                                
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Description</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $activityLog->activity_description }}</dd>
                                </div>
                                
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Occurred At</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $activityLog->occurred_at->format('M j, Y H:i:s T') }}</dd>
                                </div>
                                
                                @if($activityLog->failure_reason)
                                <div class="md:col-span-2">
                                    <dt class="text-sm font-medium text-gray-500">Failure Reason</dt>
                                    <dd class="mt-1 text-sm text-red-600">{{ $activityLog->failure_reason }}</dd>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- User Information -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">User Information</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Email</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $activityLog->user_email ?? 'Unknown' }}</dd>
                                </div>
                                
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Name</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $activityLog->user_name ?? 'Unknown' }}</dd>
                                </div>
                                
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">User ID</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $activityLog->user_id ?? 'N/A' }}</dd>
                                </div>
                                
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Account Status</dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        @if($activityLog->user)
                                            <span class="text-green-600">Active User</span>
                                        @else
                                            <span class="text-gray-500">User Not Found</span>
                                        @endif
                                    </dd>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Request Details -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Request Details</h3>
                            
                            <div class="space-y-4">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Request Data</dt>
                                    <dd class="mt-1">
                                        <pre class="text-xs bg-gray-100 p-3 rounded overflow-x-auto">{{ json_encode($activityLog->request_data, JSON_PRETTY_PRINT) }}</pre>
                                    </dd>
                                </div>
                                
                                @if($activityLog->response_data)
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Response Data</dt>
                                    <dd class="mt-1">
                                        <pre class="text-xs bg-gray-100 p-3 rounded overflow-x-auto">{{ json_encode($activityLog->response_data, JSON_PRETTY_PRINT) }}</pre>
                                    </dd>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Security Assessment -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Security Assessment</h3>
                            
                            <div class="space-y-3">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Risk Score</dt>
                                    <dd class="mt-1">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium risk-{{ $activityLog->risk_level }}">
                                            {{ $activityLog->risk_score }}/100 ({{ ucfirst($activityLog->risk_level) }})
                                        </span>
                                    </dd>
                                </div>
                                
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Suspicious Activity</dt>
                                    <dd class="mt-1">
                                        @if($activityLog->is_suspicious)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                ⚠️ Yes
                                            </span>
                                        @else
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                ✅ No
                                            </span>
                                        @endif
                                    </dd>
                                </div>
                                
                                @if($activityLog->security_notes)
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Security Notes</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $activityLog->security_notes }}</dd>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Technical Details -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Technical Details</h3>
                            
                            <div class="space-y-3">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">IP Address</dt>
                                    <dd class="mt-1 text-sm text-gray-900 font-mono">{{ $activityLog->ip_address }}</dd>
                                </div>
                                
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Location</dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        @if($activityLog->city || $activityLog->region || $activityLog->country)
                                            {{ collect([$activityLog->city, $activityLog->region, $activityLog->country])->filter()->implode(', ') }}
                                        @else
                                            Unknown Location
                                        @endif
                                    </dd>
                                </div>

                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Device</dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        @if($activityLog->device_type || $activityLog->browser || $activityLog->platform)
                                            {{ collect([$activityLog->device_type, $activityLog->browser, $activityLog->platform])->filter()->implode(' • ') }}
                                        @else
                                            Unknown Device
                                        @endif
                                    </dd>
                                </div>
                                
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">URL</dt>
                                    <dd class="mt-1 text-sm text-gray-900 break-all">{{ $activityLog->url }}</dd>
                                </div>
                                
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Method</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $activityLog->method }}</dd>
                                </div>
                                
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Session ID</dt>
                                    <dd class="mt-1 text-sm text-gray-900 font-mono">{{ $activityLog->session_id }}</dd>
                                </div>
                                
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Request ID</dt>
                                    <dd class="mt-1 text-sm text-gray-900 font-mono">{{ $activityLog->request_id }}</dd>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Metadata -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Metadata</h3>
                            
                            <div class="space-y-3">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Log ID</dt>
                                    <dd class="mt-1 text-sm text-gray-900 font-mono">{{ $activityLog->uuid }}</dd>
                                </div>
                                
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Created At</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $activityLog->created_at->format('M j, Y H:i:s T') }}</dd>
                                </div>
                                
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Updated At</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $activityLog->updated_at->format('M j, Y H:i:s T') }}</dd>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
