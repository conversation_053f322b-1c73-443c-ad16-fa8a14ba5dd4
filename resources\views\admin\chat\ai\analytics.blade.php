@extends('layouts.dashboard')

@section('title', 'AI Analytics Dashboard')

@push('styles')
<style>
/* AI Analytics Dashboard Styles */
.btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg;
    @apply transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
    @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
}

.btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn-sm {
    @apply px-3 py-1.5 text-xs;
}
</style>
@endpush

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">AI Analytics Dashboard</h1>
            <p class="text-gray-600">Detailed AI performance analytics and insights</p>
        </div>
        <div class="flex space-x-3">
            <select id="time-range" class="border border-gray-300 rounded-md px-3 py-2">
                <option value="7">Last 7 days</option>
                <option value="30">Last 30 days</option>
                <option value="90">Last 90 days</option>
            </select>
            <button onclick="refreshAnalytics()" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refresh
            </button>
            <button onclick="exportReport()" class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Export Report
            </button>
        </div>
    </div>

    <!-- Key Metrics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total AI Responses</p>
                    <p id="total-responses" class="text-2xl font-semibold text-gray-900">-</p>
                    <p id="responses-change" class="text-sm text-gray-500">-</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Success Rate</p>
                    <p id="success-rate" class="text-2xl font-semibold text-gray-900">-</p>
                    <p id="success-change" class="text-sm text-gray-500">-</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Avg Response Time</p>
                    <p id="avg-response-time" class="text-2xl font-semibold text-gray-900">-</p>
                    <p id="time-change" class="text-sm text-gray-500">-</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-red-100 rounded-lg">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Escalation Rate</p>
                    <p id="escalation-rate" class="text-2xl font-semibold text-gray-900">-</p>
                    <p id="escalation-change" class="text-sm text-gray-500">-</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Response Volume Chart -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">AI Response Volume</h3>
                <p class="text-sm text-gray-600">Daily AI response count over time</p>
            </div>
            <div class="p-6">
                <canvas id="response-volume-chart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Success Rate Chart -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Success Rate Trend</h3>
                <p class="text-sm text-gray-600">AI response success rate over time</p>
            </div>
            <div class="p-6">
                <canvas id="success-rate-chart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Provider Performance and Intent Analysis -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Provider Performance -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Provider Performance</h3>
                <p class="text-sm text-gray-600">Performance comparison across AI providers</p>
            </div>
            <div class="p-6">
                <div id="provider-performance" class="space-y-4">
                    <!-- Provider performance data will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Intent Analysis -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Top Intents</h3>
                <p class="text-sm text-gray-600">Most common conversation intents</p>
            </div>
            <div class="p-6">
                <canvas id="intent-chart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Response Time Analysis and Error Analysis -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Response Time Distribution -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Response Time Distribution</h3>
                <p class="text-sm text-gray-600">Distribution of AI response times</p>
            </div>
            <div class="p-6">
                <canvas id="response-time-chart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Error Analysis -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Error Analysis</h3>
                <p class="text-sm text-gray-600">Common errors and failure patterns</p>
            </div>
            <div class="p-6">
                <div id="error-analysis" class="space-y-3">
                    <!-- Error analysis data will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Analytics Table -->
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Detailed Analytics</h3>
            <p class="text-sm text-gray-600">Comprehensive AI performance data</p>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Provider</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Responses</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Success Rate</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Time (ms)</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tokens Used</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cost</th>
                    </tr>
                </thead>
                <tbody id="analytics-table" class="bg-white divide-y divide-gray-200">
                    <!-- Analytics data will be loaded here -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loading-overlay" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
        <svg class="animate-spin h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="text-gray-700">Loading analytics...</span>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let charts = {};
let analyticsData = {};

// Load analytics on page load
document.addEventListener('DOMContentLoaded', function() {
    loadAnalytics();
    
    // Setup event listeners
    document.getElementById('time-range').addEventListener('change', loadAnalytics);
});

// Load analytics data
function loadAnalytics() {
    showLoading();
    const days = document.getElementById('time-range').value;
    
    fetch(`{{ route('admin.chat.ai.enhanced-analytics') }}?days=${days}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                analyticsData = data.data;
                updateMetrics();
                updateCharts();
                updateProviderPerformance();
                updateErrorAnalysis();
                updateAnalyticsTable();
            } else {
                alert('Failed to load analytics: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error loading analytics:', error);
            alert('Failed to load analytics data');
        })
        .finally(() => {
            hideLoading();
        });
}

// Update key metrics
function updateMetrics() {
    const metrics = analyticsData.metrics || {};
    
    document.getElementById('total-responses').textContent = formatNumber(metrics.total_responses || 0);
    document.getElementById('success-rate').textContent = formatPercentage(metrics.success_rate || 0);
    document.getElementById('avg-response-time').textContent = formatTime(metrics.avg_response_time || 0);
    document.getElementById('escalation-rate').textContent = formatPercentage(metrics.escalation_rate || 0);
    
    // Update change indicators
    document.getElementById('responses-change').textContent = formatChange(metrics.responses_change || 0);
    document.getElementById('success-change').textContent = formatChange(metrics.success_change || 0);
    document.getElementById('time-change').textContent = formatChange(metrics.time_change || 0);
    document.getElementById('escalation-change').textContent = formatChange(metrics.escalation_change || 0);
}

// Update charts
function updateCharts() {
    updateResponseVolumeChart();
    updateSuccessRateChart();
    updateIntentChart();
    updateResponseTimeChart();
}

// Utility functions
function formatNumber(num) {
    return new Intl.NumberFormat().format(num);
}

function formatPercentage(num) {
    return (num * 100).toFixed(1) + '%';
}

function formatTime(ms) {
    if (ms < 1000) {
        return ms.toFixed(0) + 'ms';
    } else {
        return (ms / 1000).toFixed(1) + 's';
    }
}

function formatChange(change) {
    if (change > 0) {
        return `+${change.toFixed(1)}%`;
    } else if (change < 0) {
        return `${change.toFixed(1)}%`;
    } else {
        return '0%';
    }
}

function showLoading() {
    document.getElementById('loading-overlay').classList.remove('hidden');
}

function hideLoading() {
    document.getElementById('loading-overlay').classList.add('hidden');
}

function refreshAnalytics() {
    loadAnalytics();
}

function exportReport() {
    const days = document.getElementById('time-range').value;
    window.open(`{{ route('admin.chat.ai.enhanced-analytics') }}?days=${days}&export=true`, '_blank');
}

// Chart update functions
function updateResponseVolumeChart() {
    const ctx = document.getElementById('response-volume-chart').getContext('2d');
    const data = analyticsData.daily_responses || [];

    if (charts.responseVolume) {
        charts.responseVolume.destroy();
    }

    charts.responseVolume = new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.map(item => item.date),
            datasets: [{
                label: 'AI Responses',
                data: data.map(item => item.count),
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function updateSuccessRateChart() {
    const ctx = document.getElementById('success-rate-chart').getContext('2d');
    const data = analyticsData.daily_success_rate || [];

    if (charts.successRate) {
        charts.successRate.destroy();
    }

    charts.successRate = new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.map(item => item.date),
            datasets: [{
                label: 'Success Rate (%)',
                data: data.map(item => item.success_rate * 100),
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
}

function updateIntentChart() {
    const ctx = document.getElementById('intent-chart').getContext('2d');
    const data = analyticsData.top_intents || [];

    if (charts.intent) {
        charts.intent.destroy();
    }

    charts.intent = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: data.map(item => item.intent),
            datasets: [{
                data: data.map(item => item.count),
                backgroundColor: [
                    'rgb(59, 130, 246)',
                    'rgb(34, 197, 94)',
                    'rgb(251, 191, 36)',
                    'rgb(239, 68, 68)',
                    'rgb(168, 85, 247)',
                    'rgb(236, 72, 153)',
                    'rgb(14, 165, 233)',
                    'rgb(34, 197, 94)'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function updateResponseTimeChart() {
    const ctx = document.getElementById('response-time-chart').getContext('2d');
    const data = analyticsData.response_time_distribution || [];

    if (charts.responseTime) {
        charts.responseTime.destroy();
    }

    charts.responseTime = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: data.map(item => item.range),
            datasets: [{
                label: 'Response Count',
                data: data.map(item => item.count),
                backgroundColor: 'rgba(251, 191, 36, 0.8)',
                borderColor: 'rgb(251, 191, 36)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function updateProviderPerformance() {
    const container = document.getElementById('provider-performance');
    const data = analyticsData.provider_performance || [];

    container.innerHTML = '';

    data.forEach(provider => {
        const providerDiv = document.createElement('div');
        providerDiv.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg';
        providerDiv.innerHTML = `
            <div>
                <h4 class="font-medium text-gray-900">${provider.name}</h4>
                <p class="text-sm text-gray-600">${formatNumber(provider.responses)} responses</p>
            </div>
            <div class="text-right">
                <p class="text-sm font-medium text-gray-900">${formatPercentage(provider.success_rate)}</p>
                <p class="text-xs text-gray-500">${formatTime(provider.avg_time)}</p>
            </div>
        `;
        container.appendChild(providerDiv);
    });
}

function updateErrorAnalysis() {
    const container = document.getElementById('error-analysis');
    const data = analyticsData.error_analysis || [];

    container.innerHTML = '';

    if (data.length === 0) {
        container.innerHTML = '<p class="text-gray-500 text-center">No errors detected in the selected time period</p>';
        return;
    }

    data.forEach(error => {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'flex items-center justify-between p-3 bg-red-50 rounded-lg';
        errorDiv.innerHTML = `
            <div>
                <h4 class="font-medium text-red-900">${error.type}</h4>
                <p class="text-sm text-red-600">${error.message}</p>
            </div>
            <div class="text-right">
                <p class="text-sm font-medium text-red-900">${error.count}</p>
                <p class="text-xs text-red-500">occurrences</p>
            </div>
        `;
        container.appendChild(errorDiv);
    });
}

function updateAnalyticsTable() {
    const tbody = document.getElementById('analytics-table');
    const data = analyticsData.detailed_analytics || [];

    tbody.innerHTML = '';

    data.forEach(row => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${row.date}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${row.provider}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatNumber(row.responses)}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatPercentage(row.success_rate)}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatTime(row.avg_time)}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatNumber(row.tokens_used)}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$${row.cost.toFixed(2)}</td>
        `;
        tbody.appendChild(tr);
    });
}
</script>
@endsection
