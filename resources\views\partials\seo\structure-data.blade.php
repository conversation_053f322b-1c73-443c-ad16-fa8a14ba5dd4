{{-- Dynamic structured data for services --}}
@php
$structuredData = [
    '@context' => 'https://schema.org',
    '@type' => 'Service',
    'name' => $serviceName ?? 'Professional Service',
    'description' => $serviceDescription ?? 'Professional service offering',
    'provider' => [
        '@type' => 'Organization',
        'name' => config('app.name', 'ChiSolution'),
        'url' => url('/'),
        'logo' => asset('images/logo.png'),
        'contactPoint' => [
            '@type' => 'ContactPoint',
            'telephone' => '+27-11-123-4567',
            'contactType' => 'customer service',
            'availableLanguage' => ['English', 'French', 'Spanish']
        ]
    ],
    'serviceType' => $serviceType ?? 'Digital Service',
    'category' => $serviceCategory ?? 'Technology',
    'areaServed' => [
        '@type' => 'Country',
        'name' => 'South Africa'
    ],
    'offers' => [
        '@type' => 'Offer',
        'availability' => 'https://schema.org/InStock',
        'priceCurrency' => 'ZAR',
        'priceRange' => $priceRange ?? 'Contact for quote',
        'validFrom' => now()->toISOString()
    ],
    'aggregateRating' => [
        '@type' => 'AggregateRating',
        'ratingValue' => '4.9',
        'reviewCount' => '150',
        'bestRating' => '5',
        'worstRating' => '1'
    ]
];

if (isset($serviceFeatures) && is_array($serviceFeatures)) {
    $catalogItems = [];
    foreach ($serviceFeatures as $feature) {
        $catalogItems[] = [
            '@type' => 'Offer',
            'itemOffered' => [
                '@type' => 'Service',
                'name' => $feature
            ]
        ];
    }
    
    $structuredData['hasOfferCatalog'] = [
        '@type' => 'OfferCatalog',
        'name' => 'Service Features',
        'itemListElement' => $catalogItems
    ];
}
@endphp

@push('structured_data')
<script type="application/ld+json">
{!! json_encode($structuredData, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) !!}
</script>
@endpush
