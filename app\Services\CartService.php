<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\ShoppingCart;
use App\Models\CartItem;
use App\Models\User;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class CartService
{
    private const LOCK_TIMEOUT = 10; // seconds
    private const LOCK_RETRY_DELAY = 100; // milliseconds
    private const MAX_LOCK_RETRIES = 50;

    /**
     * Execute cart operation with distributed locking
     */
    private function withCartLock(string $cartIdentifier, callable $operation)
    {
        $lockKey = "cart_lock:{$cartIdentifier}";
        $lockValue = uniqid(php_uname('n'), true);

        $retries = 0;
        while ($retries < self::MAX_LOCK_RETRIES) {
            // Try to acquire lock
            if (Redis::set($lockKey, $lockValue, 'EX', self::LOCK_TIMEOUT, 'NX')) {
                try {
                    return $operation();
                } finally {
                    // Release lock only if we still own it
                    $script = "
                        if redis.call('get', KEYS[1]) == ARGV[1] then
                            return redis.call('del', KEYS[1])
                        else
                            return 0
                        end
                    ";
                    Redis::eval($script, 1, $lockKey, $lockValue);
                }
            }

            // Wait before retrying
            usleep(self::LOCK_RETRY_DELAY * 1000);
            $retries++;
        }

        throw new \RuntimeException('Could not acquire cart lock after ' . self::MAX_LOCK_RETRIES . ' retries');
    }

    /**
     * Get cart identifier for locking
     */
    private function getCartIdentifier(?User $user = null): string
    {
        return $user ? 'user_' . $user->id : 'session_' . Session::getId();
    }

    /**
     * Get or create cart for user or session with distributed locking.
     */
    public function getCart(?User $user = null): ShoppingCart
    {
        return $this->withCartLock($this->getCartIdentifier($user), function () use ($user) {
            if ($user) {
                // Get user's active cart
                $cart = ShoppingCart::where('user_id', $user->id)
                    ->where('expires_at', '>', now())
                    ->orWhereNull('expires_at')
                    ->first();
            } else {
                // Get session cart
                $sessionId = Session::getId();
                $cart = ShoppingCart::where('session_id', $sessionId)
                    ->where('expires_at', '>', now())
                    ->orWhereNull('expires_at')
                    ->first();
            }

            if (!$cart) {
                $cart = $this->createCart($user);
            }

            return $cart;
        });
    }

    /**
     * Create a new cart.
     */
    public function createCart(?User $user = null): ShoppingCart
    {
        return ShoppingCart::create([
            'uuid' => Str::uuid(),
            'user_id' => $user?->id,
            'session_id' => $user ? null : Session::getId(),
            'currency_code' => config('app.default_currency', 'ZAR'),
            'expires_at' => $user ? null : now()->addDays(30), // Session carts expire
        ]);
    }

    /**
     * Add item to cart with distributed locking and inventory validation.
     */
    public function addItem(
        ShoppingCart $cart,
        Product $product,
        int $quantity = 1,
        ?ProductVariant $variant = null
    ): CartItem {
        $user = $cart->user;
        return $this->withCartLock($this->getCartIdentifier($user), function () use ($cart, $product, $quantity, $variant) {
            return DB::transaction(function () use ($cart, $product, $quantity, $variant) {
                // Lock product/variant for inventory check
                if ($variant) {
                    $variant = ProductVariant::lockForUpdate()->find($variant->id);
                    if (!$variant || !$variant->is_active) {
                        throw new \InvalidArgumentException('Product variant is not available');
                    }
                    if ($variant->track_inventory && $variant->inventory_quantity < $quantity) {
                        throw new \InvalidArgumentException('Insufficient stock for variant');
                    }
                } else {
                    $product = Product::lockForUpdate()->find($product->id);
                    if (!$product || !$product->is_active) {
                        throw new \InvalidArgumentException('Product is not available');
                    }
                    if ($product->track_inventory && $product->inventory_quantity < $quantity) {
                        throw new \InvalidArgumentException('Insufficient stock');
                    }
                }

                // Check if item already exists in cart
                $existingItem = $cart->items()
                    ->where('product_id', $product->id)
                    ->where('variant_id', $variant?->id)
                    ->first();

                if ($existingItem) {
                    // Validate total quantity after addition
                    $newQuantity = $existingItem->quantity + $quantity;
                    $availableStock = $variant ? $variant->inventory_quantity : $product->inventory_quantity;

                    if (($variant && $variant->track_inventory) || (!$variant && $product->track_inventory)) {
                        if ($availableStock < $newQuantity) {
                            throw new \InvalidArgumentException('Insufficient stock for requested quantity');
                        }
                    }

                    // Update quantity
                    $existingItem->quantity = $newQuantity;
                    $existingItem->total_price = $existingItem->quantity * $existingItem->unit_price;
                    $existingItem->save();

                    $this->updateCartTotals($cart);
                    $this->clearCartCache($cart);
                    return $existingItem;
                }

                // Create new cart item
                $unitPrice = $variant ? $variant->price ?? $product->price : $product->price;

                $cartItem = $cart->items()->create([
                    'product_id' => $product->id,
                    'variant_id' => $variant?->id,
                    'quantity' => $quantity,
                    'unit_price' => $unitPrice,
                    'total_price' => $quantity * $unitPrice,
                ]);

                $this->updateCartTotals($cart);
                $this->clearCartCache($cart);
                return $cartItem;
            });
        });
    }

    /**
     * Update item quantity in cart.
     */
    public function updateItemQuantity(CartItem $cartItem, int $quantity): CartItem
    {
        if ($quantity <= 0) {
            return $this->removeItem($cartItem);
        }

        $cartItem->quantity = $quantity;
        $cartItem->total_price = $cartItem->quantity * $cartItem->unit_price;
        $cartItem->save();

        $this->updateCartTotals($cartItem->shoppingCart);
        return $cartItem;
    }

    /**
     * Remove item from cart.
     */
    public function removeItem(CartItem $cartItem): CartItem
    {
        $cart = $cartItem->shoppingCart;
        $cartItem->delete();
        
        $this->updateCartTotals($cart);
        return $cartItem;
    }

    /**
     * Clear all items from cart.
     */
    public function clearCart(ShoppingCart $cart): void
    {
        $cart->items()->delete();
        $this->updateCartTotals($cart);
    }

    /**
     * Update cart totals.
     */
    public function updateCartTotals(ShoppingCart $cart): void
    {
        $subtotal = $cart->items()->sum('total_price');
        
        // Calculate tax (implement your tax logic here)
        $taxRate = config('shop.tax_rate', 0.15); // 15% VAT
        $taxAmount = $subtotal * $taxRate;
        
        // Calculate shipping (implement your shipping logic here)
        $shippingAmount = $this->calculateShipping($cart, $subtotal);
        
        // Apply discounts (implement your discount logic here)
        $discountAmount = $this->calculateDiscounts($cart, $subtotal);
        
        $total = $subtotal + $taxAmount + $shippingAmount - $discountAmount;

        $cart->update([
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'shipping_amount' => $shippingAmount,
            'discount_amount' => $discountAmount,
            'total' => $total,
        ]);
    }

    /**
     * Clear cart-related cache entries
     */
    private function clearCartCache(ShoppingCart $cart): void
    {
        $cacheKeys = [
            'cart_count_user_' . $cart->user_id,
            'cart_count_session_' . $cart->session_id,
            'cart_' . $cart->uuid,
        ];

        foreach ($cacheKeys as $key) {
            if ($key) {
                Cache::forget($key);
            }
        }
    }

    /**
     * Calculate shipping amount
     */
    private function calculateShipping(ShoppingCart $cart, float $subtotal): float
    {
        // Implement your shipping logic here
        // For now, free shipping over R500
        return $subtotal >= 500 ? 0 : 50;
    }

    /**
     * Calculate discount amount
     */
    private function calculateDiscounts(ShoppingCart $cart, float $subtotal): float
    {
        // Implement your discount logic here
        return $cart->discount_amount ?? 0;
    }

    /**
     * Validate cart items and inventory
     */
    public function validateCartItems(ShoppingCart $cart): array
    {
        $issues = [];

        foreach ($cart->items as $item) {
            $product = $item->product;
            $variant = $item->productVariant;

            // Check if product is still active
            if (!$product->is_active) {
                $issues[] = [
                    'type' => 'unavailable',
                    'item' => $item,
                    'message' => 'Product is no longer available'
                ];
                continue;
            }

            // Check inventory
            if ($variant) {
                if (!$variant->is_active) {
                    $issues[] = [
                        'type' => 'unavailable',
                        'item' => $item,
                        'message' => 'Product variant is no longer available'
                    ];
                    continue;
                }

                if ($variant->track_inventory && $variant->inventory_quantity < $item->quantity) {
                    $issues[] = [
                        'type' => 'insufficient_stock',
                        'item' => $item,
                        'available' => $variant->inventory_quantity,
                        'requested' => $item->quantity,
                        'message' => 'Insufficient stock for variant'
                    ];
                }
            } else {
                if ($product->track_inventory && $product->inventory_quantity < $item->quantity) {
                    $issues[] = [
                        'type' => 'insufficient_stock',
                        'item' => $item,
                        'available' => $product->inventory_quantity,
                        'requested' => $item->quantity,
                        'message' => 'Insufficient stock'
                    ];
                }
            }
        }

        return $issues;
    }



    /**
     * Get cart item count.
     */
    public function getItemCount(ShoppingCart $cart): int
    {
        return $cart->items()->sum('quantity');
    }

    /**
     * Transfer session cart to user cart.
     */
    public function transferSessionCartToUser(User $user): void
    {
        $sessionId = Session::getId();
        $sessionCart = ShoppingCart::where('session_id', $sessionId)->first();
        
        if (!$sessionCart || $sessionCart->items()->count() === 0) {
            return;
        }

        $userCart = $this->getCart($user);
        
        // Transfer items
        foreach ($sessionCart->items as $sessionItem) {
            $this->addItem(
                $userCart,
                $sessionItem->product,
                $sessionItem->quantity,
                $sessionItem->variant
            );
        }

        // Delete session cart
        $sessionCart->items()->delete();
        $sessionCart->delete();
    }

    /**
     * Check if cart has items.
     */
    public function hasItems(ShoppingCart $cart): bool
    {
        return $cart->items()->count() > 0;
    }

    /**
     * Get cart summary.
     */
    public function getCartSummary(ShoppingCart $cart): array
    {
        return [
            'item_count' => $this->getItemCount($cart),
            'subtotal' => $cart->subtotal,
            'tax_amount' => $cart->tax_amount,
            'shipping_amount' => $cart->shipping_amount,
            'discount_amount' => $cart->discount_amount,
            'total' => $cart->total,
            'formatted_subtotal' => 'R' . number_format($cart->subtotal, 2),
            'formatted_tax' => 'R' . number_format($cart->tax_amount, 2),
            'formatted_shipping' => 'R' . number_format($cart->shipping_amount, 2),
            'formatted_discount' => 'R' . number_format($cart->discount_amount, 2),
            'formatted_total' => 'R' . number_format($cart->total, 2),
        ];
    }
}
