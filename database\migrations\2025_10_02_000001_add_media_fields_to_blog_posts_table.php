<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('blog_posts', function (Blueprint $table) {
            // Video support - both uploaded and embedded
            $table->json('videos')->nullable()->after('gallery_images')
                ->comment('Array of video objects with type (uploaded/embedded), url, thumbnail, metadata');
            
            // Social media embeds
            $table->json('social_embeds')->nullable()->after('videos')
                ->comment('Array of social media embed objects (Twitter/X, Instagram, Facebook, LinkedIn)');
            
            // Enhanced SEO fields for Open Graph and Twitter Cards
            $table->string('og_title', 255)->nullable()->after('meta_keywords')
                ->comment('Open Graph title (defaults to meta_title if empty)');
            $table->text('og_description')->nullable()->after('og_title')
                ->comment('Open Graph description (defaults to meta_description if empty)');
            $table->string('og_image', 500)->nullable()->after('og_description')
                ->comment('Open Graph image URL (defaults to featured_image if empty)');
            $table->string('og_type', 50)->default('article')->after('og_image')
                ->comment('Open Graph type (article, website, etc.)');
            
            $table->string('twitter_card', 50)->default('summary_large_image')->after('og_type')
                ->comment('Twitter card type (summary, summary_large_image, etc.)');
            $table->string('twitter_title', 255)->nullable()->after('twitter_card')
                ->comment('Twitter card title (defaults to og_title/meta_title if empty)');
            $table->text('twitter_description')->nullable()->after('twitter_title')
                ->comment('Twitter card description (defaults to og_description/meta_description if empty)');
            $table->string('twitter_image', 500)->nullable()->after('twitter_description')
                ->comment('Twitter card image URL (defaults to og_image/featured_image if empty)');
            
            // Canonical URL for SEO
            $table->string('canonical_url', 500)->nullable()->after('twitter_image')
                ->comment('Canonical URL for this post (auto-generated if empty)');
            
            // Focus keyword for SEO
            $table->string('focus_keyword', 100)->nullable()->after('canonical_url')
                ->comment('Primary keyword/keyphrase for SEO optimization');
            
            // Schema.org structured data
            $table->json('schema_data')->nullable()->after('focus_keyword')
                ->comment('Schema.org Article structured data (auto-generated if empty)');
            
            // Scheduled publishing
            $table->timestamp('scheduled_at')->nullable()->after('published_at')
                ->comment('Scheduled publish date/time (null if not scheduled)');
            
            // Add indexes for performance
            $table->index('scheduled_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('blog_posts', function (Blueprint $table) {
            $table->dropIndex(['scheduled_at']);
            
            $table->dropColumn([
                'videos',
                'social_embeds',
                'og_title',
                'og_description',
                'og_image',
                'og_type',
                'twitter_card',
                'twitter_title',
                'twitter_description',
                'twitter_image',
                'canonical_url',
                'focus_keyword',
                'schema_data',
                'scheduled_at',
            ]);
        });
    }
};

