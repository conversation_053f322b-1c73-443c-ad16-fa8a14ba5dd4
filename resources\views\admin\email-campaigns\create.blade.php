@extends('layouts.dashboard')

@section('title', 'Create Email Campaign')

@section('content')
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Create Email Campaign</h1>
            <p class="mt-1 text-sm text-gray-600">
                @if($parentCampaign)
                    Create a new email in the "{{ $parentCampaign->name }}" drip sequence
                @else
                    Create a new email marketing campaign
                @endif
            </p>
        </div>
        <a href="{{ route('admin.email-campaigns.index') }}" 
           class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
            </svg>
            Back to Campaigns
        </a>
    </div>

    <!-- Messages Container -->
    <div id="messages-container" class="hidden"></div>

    <!-- Form -->
    <form id="campaign-form" method="POST" action="{{ route('admin.email-campaigns.store') }}" class="space-y-6">
        @csrf

        @if($parentCampaign)
            <input type="hidden" name="parent_campaign_id" value="{{ $parentCampaign->id }}">
            <input type="hidden" name="type" value="drip">
        @endif

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Campaign Information -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Campaign Information</h3>
                    
                    <div class="space-y-4">
                        <!-- Campaign Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
                                Campaign Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   id="name" 
                                   name="name" 
                                   value="{{ old('name') }}"
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('name') border-red-500 @enderror">
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                            <textarea id="description" 
                                      name="description" 
                                      rows="3"
                                      placeholder="Brief description of this campaign..."
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('description') border-red-500 @enderror">{{ old('description') }}</textarea>
                            @error('description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Campaign Type -->
                        @if(!$parentCampaign)
                        <div>
                            <label for="type" class="block text-sm font-medium text-gray-700 mb-1">
                                Campaign Type <span class="text-red-500">*</span>
                            </label>
                            <select id="type" 
                                    name="type" 
                                    required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('type') border-red-500 @enderror">
                                <option value="">Select Type</option>
                                @foreach($types as $key => $label)
                                    <option value="{{ $key }}" {{ old('type') === $key ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                            @error('type')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Email Content -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Email Content</h3>
                    
                    <div class="space-y-4">
                        <!-- Email Template -->
                        <div>
                            <label for="email_template_id" class="block text-sm font-medium text-gray-700 mb-1">Email Template</label>
                            <select id="email_template_id" 
                                    name="email_template_id" 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('email_template_id') border-red-500 @enderror">
                                <option value="">Select Template (Optional)</option>
                                @foreach($templates as $template)
                                    <option value="{{ $template->id }}" 
                                            data-subject="{{ $template->subject }}"
                                            {{ old('email_template_id') == $template->id ? 'selected' : '' }}>
                                        {{ $template->name }} ({{ ucfirst($template->category) }})
                                    </option>
                                @endforeach
                            </select>
                            @error('email_template_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Subject Line -->
                        <div>
                            <label for="subject" class="block text-sm font-medium text-gray-700 mb-1">
                                Subject Line <span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   id="subject" 
                                   name="subject" 
                                   value="{{ old('subject') }}"
                                   required
                                   placeholder="Email subject line..."
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('subject') border-red-500 @enderror">
                            @error('subject')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Sender Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="from_name" class="block text-sm font-medium text-gray-700 mb-1">From Name</label>
                                <input type="text" 
                                       id="from_name" 
                                       name="from_name" 
                                       value="{{ old('from_name', config('mail.from.name')) }}"
                                       placeholder="Your Name"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('from_name') border-red-500 @enderror">
                                @error('from_name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            <div>
                                <label for="from_email" class="block text-sm font-medium text-gray-700 mb-1">From Email</label>
                                <input type="email" 
                                       id="from_email" 
                                       name="from_email" 
                                       value="{{ old('from_email', config('mail.from.address')) }}"
                                       placeholder="<EMAIL>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('from_email') border-red-500 @enderror">
                                @error('from_email')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Reply To -->
                        <div>
                            <label for="reply_to" class="block text-sm font-medium text-gray-700 mb-1">Reply To Email</label>
                            <input type="email" 
                                   id="reply_to" 
                                   name="reply_to" 
                                   value="{{ old('reply_to') }}"
                                   placeholder="<EMAIL> (optional)"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('reply_to') border-red-500 @enderror">
                            @error('reply_to')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Targeting -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Audience Targeting</h3>
                    
                    <div class="space-y-4">
                        <!-- Send to All -->
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="send_to_all" 
                                   name="send_to_all" 
                                   value="1"
                                   {{ old('send_to_all') ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <label for="send_to_all" class="ml-2 text-sm text-gray-700">
                                Send to all active subscribers ({{ number_format($subscriberStats['total']) }} subscribers)
                            </label>
                        </div>

                        <!-- Segment Targeting -->
                        <div id="segment-targeting" class="{{ old('send_to_all') ? 'hidden' : '' }}">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Target Segments</label>
                            
                            @if($tags->count() > 0)
                                <div class="space-y-2 max-h-40 overflow-y-auto border border-gray-200 rounded-lg p-3">
                                    @foreach($tags as $tag)
                                        <div class="flex items-center">
                                            <input type="checkbox" 
                                                   id="tag_{{ $tag->id }}" 
                                                   name="target_segments[]" 
                                                   value="{{ $tag->id }}"
                                                   {{ in_array($tag->id, old('target_segments', [])) ? 'checked' : '' }}
                                                   class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                            <label for="tag_{{ $tag->id }}" class="ml-2 text-sm text-gray-700 flex items-center">
                                                <span class="inline-block w-3 h-3 rounded-full mr-2" style="background-color: {{ $tag->color }}"></span>
                                                {{ $tag->name }}
                                            </label>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <p class="text-sm text-gray-500">No subscriber tags available. <a href="#" class="text-primary-600 hover:text-primary-700">Create tags</a> to segment your audience.</p>
                            @endif

                            <!-- Advanced Criteria -->
                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Additional Criteria</label>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <!-- Lifecycle Stage -->
                                    <div>
                                        <label for="lifecycle_stage" class="block text-xs font-medium text-gray-600 mb-1">Lifecycle Stage</label>
                                        <select id="lifecycle_stage" 
                                                name="target_criteria[lifecycle_stage][]" 
                                                multiple
                                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm">
                                            <option value="new">New</option>
                                            <option value="active">Active</option>
                                            <option value="engaged">Engaged</option>
                                            <option value="at_risk">At Risk</option>
                                            <option value="inactive">Inactive</option>
                                        </select>
                                    </div>

                                    <!-- Engagement Score -->
                                    <div>
                                        <label class="block text-xs font-medium text-gray-600 mb-1">Engagement Score</label>
                                        <div class="flex space-x-2">
                                            <input type="number" 
                                                   name="target_criteria[engagement_score_min]" 
                                                   placeholder="Min"
                                                   min="0" 
                                                   max="100"
                                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm">
                                            <input type="number" 
                                                   name="target_criteria[engagement_score_max]" 
                                                   placeholder="Max"
                                                   min="0" 
                                                   max="100"
                                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm">
                                        </div>
                                    </div>
                                </div>

                                <!-- Email Permissions -->
                                <div class="mt-4">
                                    <label class="block text-xs font-medium text-gray-600 mb-2">Email Permissions</label>
                                    <div class="flex flex-wrap gap-4">
                                        <div class="flex items-center">
                                            <input type="checkbox" 
                                                   id="allow_marketing" 
                                                   name="target_criteria[allow_marketing]" 
                                                   value="1"
                                                   class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                            <label for="allow_marketing" class="ml-2 text-sm text-gray-700">Marketing emails allowed</label>
                                        </div>
                                        <div class="flex items-center">
                                            <input type="checkbox" 
                                                   id="allow_promotional" 
                                                   name="target_criteria[allow_promotional]" 
                                                   value="1"
                                                   class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                            <label for="allow_promotional" class="ml-2 text-sm text-gray-700">Promotional emails allowed</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Targeted Count -->
                            <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-blue-900">Estimated Recipients:</span>
                                    <span id="targeted-count" class="text-sm font-bold text-blue-900">Calculating...</span>
                                </div>
                                <button type="button" 
                                        id="refresh-count"
                                        class="mt-2 text-xs text-blue-600 hover:text-blue-700">
                                    Refresh Count
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Campaign Settings -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Campaign Settings</h3>
                    
                    <div class="space-y-4">
                        <!-- Tracking Options -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Tracking</label>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <input type="checkbox" 
                                           id="track_opens" 
                                           name="track_opens" 
                                           value="1"
                                           {{ old('track_opens', true) ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                    <label for="track_opens" class="ml-2 text-sm text-gray-700">Track email opens</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" 
                                           id="track_clicks" 
                                           name="track_clicks" 
                                           value="1"
                                           {{ old('track_clicks', true) ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                    <label for="track_clicks" class="ml-2 text-sm text-gray-700">Track link clicks</label>
                                </div>
                            </div>
                        </div>

                        @if($parentCampaign)
                        <!-- Drip Sequence Settings -->
                        <div>
                            <label for="delay_days" class="block text-sm font-medium text-gray-700 mb-1">
                                Delay (Days) <span class="text-red-500">*</span>
                            </label>
                            <input type="number" 
                                   id="delay_days" 
                                   name="delay_days" 
                                   value="{{ old('delay_days', 1) }}"
                                   min="0"
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <p class="mt-1 text-xs text-gray-500">Days after the previous email in the sequence</p>
                        </div>
                        @else
                        <!-- Scheduling -->
                        <div>
                            <label for="scheduled_at" class="block text-sm font-medium text-gray-700 mb-1">Schedule</label>
                            <input type="datetime-local" 
                                   id="scheduled_at" 
                                   name="scheduled_at" 
                                   value="{{ old('scheduled_at') }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <p class="mt-1 text-xs text-gray-500">Leave empty to save as draft</p>
                        </div>

                        <!-- Recurring Campaign -->
                        <div>
                            <div class="flex items-center mb-2">
                                <input type="checkbox" 
                                       id="is_recurring" 
                                       name="is_recurring" 
                                       value="1"
                                       {{ old('is_recurring') ? 'checked' : '' }}
                                       class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                <label for="is_recurring" class="ml-2 text-sm text-gray-700">Recurring campaign</label>
                            </div>
                            
                            <div id="recurring-options" class="{{ old('is_recurring') ? '' : 'hidden' }}">
                                <select id="recurring_frequency" 
                                        name="recurring_frequency" 
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                                    <option value="">Select Frequency</option>
                                    <option value="daily" {{ old('recurring_frequency') === 'daily' ? 'selected' : '' }}>Daily</option>
                                    <option value="weekly" {{ old('recurring_frequency') === 'weekly' ? 'selected' : '' }}>Weekly</option>
                                    <option value="monthly" {{ old('recurring_frequency') === 'monthly' ? 'selected' : '' }}>Monthly</option>
                                </select>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="space-y-3">
                        <button type="submit" 
                                id="submit-btn"
                                name="submit_action"
                                value="save"
                                class="w-full inline-flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                            <span class="submit-text">Create Campaign</span>
                            <span class="loading-text hidden">Creating...</span>
                        </button>
                        
                        <button type="submit" 
                                id="submit-continue-btn"
                                name="submit_action"
                                value="save_and_continue"
                                class="w-full inline-flex items-center justify-center px-4 py-2 bg-secondary-600 text-white rounded-lg hover:bg-secondary-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                            <span class="submit-continue-text">Create & Continue Editing</span>
                            <span class="loading-continue-text hidden">Creating...</span>
                        </button>

                        <button type="button" 
                                id="preview-btn"
                                class="w-full inline-flex items-center justify-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                            </svg>
                            Preview Email
                        </button>
                    </div>
                </div>

                @if($parentCampaign)
                <!-- Parent Campaign Info -->
                <div class="bg-blue-50 rounded-lg border border-blue-200 p-6">
                    <h4 class="text-sm font-medium text-blue-900 mb-2">Drip Sequence</h4>
                    <p class="text-sm text-blue-700">{{ $parentCampaign->name }}</p>
                    <p class="text-xs text-blue-600 mt-1">{{ $parentCampaign->childCampaigns->count() + 1 }} emails in sequence</p>
                </div>
                @endif
            </div>
        </div>
    </form>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Template selection auto-fill subject
    const templateSelect = document.getElementById('email_template_id');
    const subjectInput = document.getElementById('subject');

    templateSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.dataset.subject && !subjectInput.value) {
            subjectInput.value = selectedOption.dataset.subject;
        }
    });

    // Send to all toggle
    const sendToAllCheckbox = document.getElementById('send_to_all');
    const segmentTargeting = document.getElementById('segment-targeting');

    sendToAllCheckbox.addEventListener('change', function() {
        if (this.checked) {
            segmentTargeting.classList.add('hidden');
            updateTargetedCount();
        } else {
            segmentTargeting.classList.remove('hidden');
            updateTargetedCount();
        }
    });

    // Recurring campaign toggle
    const recurringCheckbox = document.getElementById('is_recurring');
    const recurringOptions = document.getElementById('recurring-options');

    if (recurringCheckbox) {
        recurringCheckbox.addEventListener('change', function() {
            if (this.checked) {
                recurringOptions.classList.remove('hidden');
            } else {
                recurringOptions.classList.add('hidden');
            }
        });
    }

    // Targeted count calculation
    function updateTargetedCount() {
        const formData = new FormData(document.getElementById('campaign-form'));
        
        fetch('{{ route("admin.email-campaigns.get-targeted-subscribers") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('targeted-count').textContent = data.count.toLocaleString();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('targeted-count').textContent = 'Error';
        });
    }

    // Refresh count button
    document.getElementById('refresh-count').addEventListener('click', updateTargetedCount);

    // Update count when targeting changes
    document.querySelectorAll('input[name="target_segments[]"], input[name^="target_criteria"]').forEach(input => {
        input.addEventListener('change', updateTargetedCount);
    });

    // Initial count calculation
    if (!sendToAllCheckbox.checked) {
        updateTargetedCount();
    } else {
        document.getElementById('targeted-count').textContent = '{{ number_format($subscriberStats["total"]) }}';
    }

    // Form submission
    const form = document.getElementById('campaign-form');
    const submitBtn = document.getElementById('submit-btn');
    const submitContinueBtn = document.getElementById('submit-continue-btn');
    const messagesContainer = document.getElementById('messages-container');

    function handleFormSubmit(button, isSubmitBtn) {
        return async function(e) {
            e.preventDefault();

            // Show loading state
            button.disabled = true;
            if (isSubmitBtn) {
                document.querySelector('.submit-text').classList.add('hidden');
                document.querySelector('.loading-text').classList.remove('hidden');
            } else {
                document.querySelector('.submit-continue-text').classList.add('hidden');
                document.querySelector('.loading-continue-text').classList.remove('hidden');
            }

            // Clear previous messages
            messagesContainer.innerHTML = '';
            messagesContainer.classList.add('hidden');

            try {
                const formData = new FormData(form);
                formData.delete('submit_action');
                formData.append('submit_action', button.getAttribute('value'));

                const response = await fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    if (data.redirect) {
                        window.location.href = data.redirect;
                    } else {
                        location.reload();
                    }
                } else {
                    // Show error message
                    messagesContainer.innerHTML = `
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <div class="flex">
                                <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">Error</h3>
                                    <p class="mt-1 text-sm text-red-700">${data.message || 'An error occurred while creating the campaign.'}</p>
                                </div>
                            </div>
                        </div>
                    `;
                    messagesContainer.classList.remove('hidden');
                }
            } catch (error) {
                console.error('Error:', error);
                messagesContainer.innerHTML = `
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex">
                            <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">Error</h3>
                                <p class="mt-1 text-sm text-red-700">An unexpected error occurred. Please try again.</p>
                            </div>
                        </div>
                    </div>
                `;
                messagesContainer.classList.remove('hidden');
            } finally {
                // Reset loading state
                button.disabled = false;
                if (isSubmitBtn) {
                    document.querySelector('.submit-text').classList.remove('hidden');
                    document.querySelector('.loading-text').classList.add('hidden');
                } else {
                    document.querySelector('.submit-continue-text').classList.remove('hidden');
                    document.querySelector('.loading-continue-text').classList.add('hidden');
                }
            }
        };
    }

    submitBtn.addEventListener('click', handleFormSubmit(submitBtn, true));
    submitContinueBtn.addEventListener('click', handleFormSubmit(submitContinueBtn, false));

    // Preview functionality
    document.getElementById('preview-btn').addEventListener('click', function() {
        const templateId = document.getElementById('email_template_id').value;
        if (!templateId) {
            alert('Please select an email template first.');
            return;
        }

        // Open preview in new window (we'll implement this later)
        alert('Preview functionality will be implemented in the next phase.');
    });
});
</script>
@endpush
@endsection
