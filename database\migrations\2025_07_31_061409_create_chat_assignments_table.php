<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('chat_room_id')->constrained('chat_rooms')->onDelete('cascade');
            $table->foreignId('assigned_to')->constrained('users')->onDelete('cascade')
                  ->comment('Staff user ID');
            $table->foreignId('assigned_by')->nullable()->constrained('users')->onDelete('set null')
                  ->comment('Admin who made assignment');
            $table->enum('assignment_type', ['automatic', 'manual', 'transfer', 'escalation']);
            $table->enum('status', ['active', 'transferred', 'completed', 'cancelled'])->default('active');
            $table->tinyInteger('workload_score')->unsigned()->nullable()
                  ->comment('Staff workload at assignment time');
            $table->text('notes')->nullable()->comment('Assignment notes or transfer reason');
            $table->timestamp('assigned_at')->useCurrent();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();
            
            // Performance indexes
            $table->index(['assigned_to', 'status'], 'idx_assigned_to_status');
            $table->index(['chat_room_id', 'status'], 'idx_room_status');
            $table->index('assignment_type', 'idx_assignment_type');
            $table->index('assigned_at', 'idx_assigned_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_assignments');
    }
};
