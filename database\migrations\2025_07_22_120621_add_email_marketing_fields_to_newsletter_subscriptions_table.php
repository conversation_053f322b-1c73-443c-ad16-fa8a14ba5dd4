<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('newsletter_subscriptions', function (Blueprint $table) {
            // Subscriber preferences
            $table->json('preferences')->nullable(); // Email frequency, content types, etc.
            $table->string('language')->default('en');
            $table->string('timezone')->nullable();

            // Engagement tracking
            $table->timestamp('last_email_sent_at')->nullable();
            $table->timestamp('last_email_opened_at')->nullable();
            $table->timestamp('last_email_clicked_at')->nullable();
            $table->unsignedInteger('total_emails_sent')->default(0);
            $table->unsignedInteger('total_emails_opened')->default(0);
            $table->unsignedInteger('total_emails_clicked')->default(0);
            $table->decimal('engagement_score', 5, 2)->default(0.00); // 0-100 score

            // Subscriber lifecycle
            $table->string('lifecycle_stage')->default('new'); // new, active, engaged, at_risk, inactive
            $table->timestamp('last_activity_at')->nullable();
            $table->json('custom_fields')->nullable(); // Additional subscriber data

            // Marketing automation
            $table->boolean('allow_marketing')->default(true);
            $table->boolean('allow_promotional')->default(true);
            $table->boolean('allow_transactional')->default(true);
            $table->string('subscription_source')->nullable(); // website, api, import, etc.

            // Add indexes for performance
            $table->index(['lifecycle_stage', 'is_active']);
            $table->index(['last_activity_at']);
            $table->index(['engagement_score']);
            $table->index(['subscription_source']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('newsletter_subscriptions', function (Blueprint $table) {
            $table->dropIndex(['lifecycle_stage', 'is_active']);
            $table->dropIndex(['last_activity_at']);
            $table->dropIndex(['engagement_score']);
            $table->dropIndex(['subscription_source']);

            $table->dropColumn([
                'preferences',
                'language',
                'timezone',
                'last_email_sent_at',
                'last_email_opened_at',
                'last_email_clicked_at',
                'total_emails_sent',
                'total_emails_opened',
                'total_emails_clicked',
                'engagement_score',
                'lifecycle_stage',
                'last_activity_at',
                'custom_fields',
                'allow_marketing',
                'allow_promotional',
                'allow_transactional',
                'subscription_source',
            ]);
        });
    }
};
