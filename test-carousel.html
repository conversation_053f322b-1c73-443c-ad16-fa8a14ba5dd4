<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Featured Projects Carousel Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Featured Projects Carousel Styles */
        .carousel-track {
            display: flex;
            transition: transform 0.5s ease-in-out;
        }

        .carousel-slide {
            flex: 0 0 auto;
            width: 100%;
        }

        @media (min-width: 768px) {
            .carousel-slide {
                width: 50%;
            }
        }

        @media (min-width: 1024px) {
            .carousel-slide {
                width: 33.333333%;
            }
        }

        .carousel-prev,
        .carousel-next {
            transition: all 0.2s ease-in-out;
            z-index: 10;
        }

        .carousel-prev:hover,
        .carousel-next:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .carousel-prev:disabled,
        .carousel-next:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .carousel-indicator {
            transition: all 0.2s ease-in-out;
            cursor: pointer;
        }

        .carousel-indicator:hover {
            transform: scale(1.2);
        }

        .carousel-indicator.active {
            transform: scale(1.1);
        }

        /* Responsive carousel navigation positioning */
        @media (max-width: 767px) {
            .carousel-prev,
            .carousel-next {
                display: none;
            }
        }

        /* Ensure proper aspect ratio for project images */
        .aspect-w-16 {
            position: relative;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
        }

        .aspect-w-16 > * {
            position: absolute;
            height: 100%;
            width: 100%;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Featured Projects Section -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <!-- Section Header -->
            <div class="text-center mb-16">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    Featured <span class="text-blue-600">Projects</span>
                </h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    Discover some of our recent work and see how we've helped businesses transform their digital presence.
                </p>
            </div>

            <!-- Projects Carousel -->
            <div id="featured-projects-carousel" class="relative">
                <!-- Carousel Container -->
                <div class="overflow-hidden">
                    <div class="carousel-track flex transition-transform duration-500 ease-in-out">
                        <!-- Project 1 -->
                        <div class="carousel-slide w-full md:w-1/2 lg:w-1/3 flex-shrink-0 px-4">
                            <div class="group relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-shadow">
                                <div class="aspect-w-16 aspect-h-9">
                                    <div class="w-full h-64 bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                                        <svg class="w-16 h-16 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                <div class="absolute bottom-0 left-0 right-0 p-6 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                                    <h3 class="text-xl font-semibold mb-2">E-commerce Platform</h3>
                                    <p class="text-sm text-gray-200 mb-4">Modern online store with advanced features</p>
                                    <span class="inline-block bg-blue-600 text-white text-xs px-2 py-1 rounded mb-3">Web Development</span>
                                    <a href="#" class="inline-flex items-center text-blue-300 hover:text-blue-200">
                                        View Project
                                        <svg class="ml-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Project 2 -->
                        <div class="carousel-slide w-full md:w-1/2 lg:w-1/3 flex-shrink-0 px-4">
                            <div class="group relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-shadow">
                                <div class="aspect-w-16 aspect-h-9">
                                    <div class="w-full h-64 bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center">
                                        <svg class="w-16 h-16 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                <div class="absolute bottom-0 left-0 right-0 p-6 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                                    <h3 class="text-xl font-semibold mb-2">Mobile Banking App</h3>
                                    <p class="text-sm text-gray-200 mb-4">Secure and user-friendly banking solution</p>
                                    <span class="inline-block bg-green-600 text-white text-xs px-2 py-1 rounded mb-3">Mobile Development</span>
                                    <a href="#" class="inline-flex items-center text-blue-300 hover:text-blue-200">
                                        View Project
                                        <svg class="ml-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Project 3 -->
                        <div class="carousel-slide w-full md:w-1/2 lg:w-1/3 flex-shrink-0 px-4">
                            <div class="group relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-shadow">
                                <div class="aspect-w-16 aspect-h-9">
                                    <div class="w-full h-64 bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center">
                                        <svg class="w-16 h-16 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                <div class="absolute bottom-0 left-0 right-0 p-6 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                                    <h3 class="text-xl font-semibold mb-2">Corporate Website</h3>
                                    <p class="text-sm text-gray-200 mb-4">Professional business website with CMS</p>
                                    <span class="inline-block bg-purple-600 text-white text-xs px-2 py-1 rounded mb-3">Web Design</span>
                                    <a href="#" class="inline-flex items-center text-blue-300 hover:text-blue-200">
                                        View Project
                                        <svg class="ml-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Project 4 -->
                        <div class="carousel-slide w-full md:w-1/2 lg:w-1/3 flex-shrink-0 px-4">
                            <div class="group relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-shadow">
                                <div class="aspect-w-16 aspect-h-9">
                                    <div class="w-full h-64 bg-gradient-to-br from-red-500 to-red-600 flex items-center justify-center">
                                        <svg class="w-16 h-16 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                <div class="absolute bottom-0 left-0 right-0 p-6 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                                    <h3 class="text-xl font-semibold mb-2">Online Marketplace</h3>
                                    <p class="text-sm text-gray-200 mb-4">Multi-vendor e-commerce platform</p>
                                    <span class="inline-block bg-red-600 text-white text-xs px-2 py-1 rounded mb-3">E-commerce</span>
                                    <a href="#" class="inline-flex items-center text-blue-300 hover:text-blue-200">
                                        View Project
                                        <svg class="ml-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Project 5 -->
                        <div class="carousel-slide w-full md:w-1/2 lg:w-1/3 flex-shrink-0 px-4">
                            <div class="group relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-shadow">
                                <div class="aspect-w-16 aspect-h-9">
                                    <div class="w-full h-64 bg-gradient-to-br from-yellow-500 to-yellow-600 flex items-center justify-center">
                                        <svg class="w-16 h-16 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                <div class="absolute bottom-0 left-0 right-0 p-6 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                                    <h3 class="text-xl font-semibold mb-2">SaaS Dashboard</h3>
                                    <p class="text-sm text-gray-200 mb-4">Analytics and reporting platform</p>
                                    <span class="inline-block bg-yellow-600 text-white text-xs px-2 py-1 rounded mb-3">UI/UX Design</span>
                                    <a href="#" class="inline-flex items-center text-blue-300 hover:text-blue-200">
                                        View Project
                                        <svg class="ml-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navigation Arrows -->
                <button class="carousel-prev absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 bg-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-200 z-10">
                    <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
                <button class="carousel-next absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 bg-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-200 z-10">
                    <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>

                <!-- Indicators -->
                <div class="flex justify-center mt-8 space-x-2">
                    <button class="carousel-indicator w-3 h-3 rounded-full bg-blue-600 hover:bg-gray-400 transition-colors duration-200"></button>
                    <button class="carousel-indicator w-3 h-3 rounded-full bg-gray-300 hover:bg-gray-400 transition-colors duration-200"></button>
                </div>
            </div>

            <!-- View All Projects Button -->
            <div class="text-center mt-12">
                <a href="#" class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors inline-flex items-center">
                    View All Projects
                    <svg class="ml-2 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </a>
            </div>
        </div>
    </section>

    <script>
        // Featured Projects Carousel Component
        class FeaturedProjectsCarousel {
            constructor(containerId) {
                this.container = document.getElementById(containerId);
                if (!this.container) return;
                
                this.track = this.container.querySelector('.carousel-track');
                this.slides = this.container.querySelectorAll('.carousel-slide');
                this.prevBtn = this.container.querySelector('.carousel-prev');
                this.nextBtn = this.container.querySelector('.carousel-next');
                this.indicators = this.container.querySelectorAll('.carousel-indicator');
                
                this.currentSlide = 0;
                this.totalSlides = this.slides.length;
                this.slidesToShow = this.getSlidesToShow();
                this.maxSlide = Math.max(0, this.totalSlides - this.slidesToShow);
                
                this.init();
            }
            
            getSlidesToShow() {
                const width = window.innerWidth;
                if (width >= 1024) return 3; // lg: 3 slides
                if (width >= 768) return 2;  // md: 2 slides
                return 1; // sm: 1 slide
            }
            
            init() {
                if (this.totalSlides === 0) return;
                
                this.updateCarousel();
                this.bindEvents();
                this.startAutoPlay();
                
                // Handle window resize
                window.addEventListener('resize', () => {
                    this.slidesToShow = this.getSlidesToShow();
                    this.maxSlide = Math.max(0, this.totalSlides - this.slidesToShow);
                    this.currentSlide = Math.min(this.currentSlide, this.maxSlide);
                    this.updateCarousel();
                });
            }
            
            updateCarousel() {
                if (!this.track) return;
                
                const slideWidth = 100 / this.slidesToShow;
                const translateX = -(this.currentSlide * slideWidth);
                
                this.track.style.transform = `translateX(${translateX}%)`;
                
                // Update navigation buttons
                if (this.prevBtn) {
                    this.prevBtn.disabled = this.currentSlide === 0;
                    this.prevBtn.classList.toggle('opacity-50', this.currentSlide === 0);
                }
                
                if (this.nextBtn) {
                    this.nextBtn.disabled = this.currentSlide >= this.maxSlide;
                    this.nextBtn.classList.toggle('opacity-50', this.currentSlide >= this.maxSlide);
                }
                
                // Update indicators
                this.indicators.forEach((indicator, index) => {
                    const isActive = index === this.currentSlide;
                    indicator.classList.toggle('bg-blue-600', isActive);
                    indicator.classList.toggle('bg-gray-300', !isActive);
                });
            }
            
            nextSlide() {
                if (this.currentSlide < this.maxSlide) {
                    this.currentSlide++;
                    this.updateCarousel();
                }
            }
            
            prevSlide() {
                if (this.currentSlide > 0) {
                    this.currentSlide--;
                    this.updateCarousel();
                }
            }
            
            goToSlide(index) {
                this.currentSlide = Math.max(0, Math.min(index, this.maxSlide));
                this.updateCarousel();
            }
            
            bindEvents() {
                if (this.prevBtn) {
                    this.prevBtn.addEventListener('click', () => this.prevSlide());
                }
                
                if (this.nextBtn) {
                    this.nextBtn.addEventListener('click', () => this.nextSlide());
                }
                
                this.indicators.forEach((indicator, index) => {
                    indicator.addEventListener('click', () => this.goToSlide(index));
                });
                
                // Pause autoplay on hover
                this.container.addEventListener('mouseenter', () => this.stopAutoPlay());
                this.container.addEventListener('mouseleave', () => this.startAutoPlay());
            }
            
            startAutoPlay() {
                this.stopAutoPlay();
                if (this.totalSlides > this.slidesToShow) {
                    this.autoPlayInterval = setInterval(() => {
                        if (this.currentSlide >= this.maxSlide) {
                            this.currentSlide = 0;
                        } else {
                            this.currentSlide++;
                        }
                        this.updateCarousel();
                    }, 5000);
                }
            }
            
            stopAutoPlay() {
                if (this.autoPlayInterval) {
                    clearInterval(this.autoPlayInterval);
                    this.autoPlayInterval = null;
                }
            }
        }

        // Initialize carousel when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            new FeaturedProjectsCarousel('featured-projects-carousel');
        });
    </script>
</body>
</html>
