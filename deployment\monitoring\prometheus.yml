global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Application metrics
  - job_name: 'chisolution-app'
    static_configs:
      - targets: ['app:9000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Nginx metrics
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:9113']
    scrape_interval: 30s

  # MySQL metrics
  - job_name: 'mysql'
    static_configs:
      - targets: ['db:9104']
    scrape_interval: 30s

  # Redis metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:9121']
    scrape_interval: 30s

  # Node exporter for system metrics
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # Chat-specific metrics
  - job_name: 'chat-metrics'
    static_configs:
      - targets: ['app:8080']
    metrics_path: '/api/v1/metrics'
    scrape_interval: 60s
    params:
      format: ['prometheus']
