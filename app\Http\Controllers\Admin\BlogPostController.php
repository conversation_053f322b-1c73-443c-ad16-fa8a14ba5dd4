<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\BlogPostStoreRequest;
use App\Http\Requests\BlogPostUpdateRequest;
use App\Models\BlogPost;
use App\Models\BlogCategory;
use App\Models\Service;
use App\Models\User;
use App\Services\ImageService;
use App\Services\FileService;
use App\Services\ActivityLogger;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class BlogPostController extends Controller
{
    public function __construct(
        private ImageService $imageService,
        private FileService $fileService,
        private ActivityLogger $activityLogger
    ) {}

    /**
     * Display a listing of blog posts.
     */
    public function index(Request $request): View
    {
        $query = BlogPost::with(['category', 'author'])
            ->where('is_deleted', false);

        // Search filter
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhere('excerpt', 'like', "%{$search}%");
            });
        }

        // Category filter
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // Status filter
        if ($request->filled('status')) {
            switch ($request->status) {
                case 'published':
                    $query->where('is_published', true);
                    break;
                case 'draft':
                    $query->where('is_published', false);
                    break;
                case 'featured':
                    $query->where('is_featured', true);
                    break;
                case 'scheduled':
                    $query->whereNotNull('scheduled_at')
                          ->where('scheduled_at', '>', now());
                    break;
            }
        }

        // Author filter
        if ($request->filled('author')) {
            $query->where('author_id', $request->author);
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $posts = $query->paginate(20)->withQueryString();

        // Get filter options
        $categories = BlogCategory::where('is_active', true)
            ->where('is_deleted', false)
            ->orderBy('name')
            ->get();

        $authors = User::whereHas('role', function($q) {
            $q->whereIn('name', ['admin', 'staff']);
        })->where('is_active', true)
          ->where('is_deleted', false)
          ->orderBy('first_name')
          ->get();

        // Log activity
        $this->activityLogger->logActivity(
            'blog_posts_viewed',
            'Viewed blog posts list in admin panel',
            'success',
            null,
            ['filters' => $request->only(['search', 'category', 'status', 'author'])],
            ['total_posts' => $posts->total()]
        );

        return view('admin.blog.posts.index', compact('posts', 'categories', 'authors'));
    }

    /**
     * Show the form for creating a new blog post.
     */
    public function create(): View
    {
        $categories = BlogCategory::where('is_active', true)
            ->where('is_deleted', false)
            ->orderBy('name')
            ->get();

        $services = Service::where('is_active', true)
            ->where('is_deleted', false)
            ->orderBy('name')
            ->get();

        $authors = User::whereHas('role', function($q) {
            $q->whereIn('name', ['admin', 'staff']);
        })->where('is_active', true)
          ->where('is_deleted', false)
          ->orderBy('first_name')
          ->get();

        return view('admin.blog.posts.create', compact('categories', 'services', 'authors'));
    }

    /**
     * Store a newly created blog post in storage.
     */
    public function store(BlogPostStoreRequest $request): RedirectResponse
    {
        try {
            DB::beginTransaction();

            $data = $request->validated();

            // Generate UUID
            $data['uuid'] = Str::uuid();

            // Handle featured image upload
            if ($request->hasFile('featured_image')) {
                $imageResult = $this->imageService->processUploadedImage(
                    $request->file('featured_image'),
                    [
                        'subdirectory' => 'blog/featured',
                        'create_variants' => true,
                        'create_webp' => true,
                        'sizes' => [
                            'thumbnail' => ['width' => 300, 'height' => 200, 'crop' => true],
                            'medium' => ['width' => 600, 'height' => 400, 'crop' => true],
                            'large' => ['width' => 1200, 'height' => 800, 'crop' => false],
                        ],
                        'quality' => 85,
                        'strip_metadata' => true
                    ]
                );

                if ($imageResult['success']) {
                    $data['featured_image'] = $imageResult['path'];
                } else {
                    throw new \Exception('Failed to process featured image: ' . implode(', ', $imageResult['errors']));
                }
            }

            // Handle gallery images upload
            if ($request->hasFile('gallery_images')) {
                $galleryPaths = [];
                foreach ($request->file('gallery_images') as $image) {
                    $imageResult = $this->imageService->processUploadedImage(
                        $image,
                        [
                            'subdirectory' => 'blog/gallery',
                            'create_variants' => true,
                            'create_webp' => true,
                            'quality' => 85
                        ]
                    );

                    if ($imageResult['success']) {
                        $galleryPaths[] = $imageResult['path'];
                    }
                }
                $data['gallery_images'] = $galleryPaths;
            }

            // Create the blog post
            $post = BlogPost::create($data);

            DB::commit();

            // Log activity
            $this->activityLogger->logActivity(
                'blog_post_created',
                "Created blog post: {$post->title}",
                'success',
                null,
                ['post_id' => $post->id, 'title' => $post->title],
                ['post_uuid' => $post->uuid]
            );

            return redirect()
                ->route('admin.blog.posts.show', $post)
                ->with('success', 'Blog post created successfully!');

        } catch (\Exception $e) {
            DB::rollBack();

            // Log error
            $this->activityLogger->logActivity(
                'blog_post_create_failed',
                'Failed to create blog post',
                'failed',
                $e->getMessage(),
                ['request_data' => $request->except(['featured_image', 'gallery_images'])],
                []
            );

            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Failed to create blog post: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified blog post.
     */
    public function show(BlogPost $post): View
    {
        $post->load(['category', 'author', 'comments', 'pendingComments']);

        // Log activity
        $this->activityLogger->logActivity(
            'blog_post_viewed',
            "Viewed blog post: {$post->title}",
            'success',
            null,
            ['post_id' => $post->id],
            []
        );

        return view('admin.blog.posts.show', compact('post'));
    }

    /**
     * Show the form for editing the specified blog post.
     */
    public function edit(BlogPost $post): View
    {
        $categories = BlogCategory::where('is_active', true)
            ->where('is_deleted', false)
            ->orderBy('name')
            ->get();

        $services = Service::where('is_active', true)
            ->where('is_deleted', false)
            ->orderBy('name')
            ->get();

        $authors = User::whereHas('role', function($q) {
            $q->whereIn('name', ['admin', 'staff']);
        })->where('is_active', true)
          ->where('is_deleted', false)
          ->orderBy('first_name')
          ->get();

        return view('admin.blog.posts.edit', compact('post', 'categories', 'services', 'authors'));
    }

    /**
     * Update the specified blog post in storage.
     */
    public function update(BlogPostUpdateRequest $request, BlogPost $post): RedirectResponse
    {
        try {
            DB::beginTransaction();

            $data = $request->validated();

            // Handle featured image upload
            if ($request->hasFile('featured_image')) {
                // Delete old featured image
                if ($post->featured_image) {
                    $this->imageService->deleteImage($post->featured_image, true);
                }

                $imageResult = $this->imageService->processUploadedImage(
                    $request->file('featured_image'),
                    [
                        'subdirectory' => "blog/featured",
                        'create_variants' => true,
                        'create_webp' => true,
                        'sizes' => [
                            'thumbnail' => ['width' => 300, 'height' => 200, 'crop' => true],
                            'medium' => ['width' => 600, 'height' => 400, 'crop' => true],
                            'large' => ['width' => 1200, 'height' => 800, 'crop' => false],
                        ],
                        'quality' => 85,
                        'strip_metadata' => true
                    ]
                );

                if ($imageResult['success']) {
                    $data['featured_image'] = $imageResult['path'];
                } else {
                    throw new \Exception('Failed to process featured image: ' . implode(', ', $imageResult['errors']));
                }
            }

            // Handle gallery images
            $existingGallery = $post->gallery_images ?? [];

            // Remove images marked for deletion
            if ($request->filled('remove_gallery_images')) {
                foreach ($request->remove_gallery_images as $imagePath) {
                    $this->imageService->deleteImage($imagePath, true);
                    $existingGallery = array_filter($existingGallery, fn($img) => $img !== $imagePath);
                }
            }

            // Add new gallery images
            if ($request->hasFile('gallery_images')) {
                foreach ($request->file('gallery_images') as $image) {
                    $imageResult = $this->imageService->processUploadedImage(
                        $image,
                        [
                            'subdirectory' => 'blog/gallery',
                            'create_variants' => true,
                            'create_webp' => true,
                            'quality' => 85
                        ]
                    );

                    if ($imageResult['success']) {
                        $existingGallery[] = $imageResult['path'];
                    }
                }
            }

            $data['gallery_images'] = array_values($existingGallery);

            // Update the blog post
            $post->update($data);

            DB::commit();

            // Log activity
            $this->activityLogger->logActivity(
                'blog_post_updated',
                "Updated blog post: {$post->title}",
                'success',
                null,
                ['post_id' => $post->id, 'title' => $post->title],
                ['post_uuid' => $post->uuid]
            );

            return redirect()
                ->route('admin.blog.posts.show', $post)
                ->with('success', 'Blog post updated successfully!');

        } catch (\Exception $e) {
            DB::rollBack();

            // Log error
            $this->activityLogger->logActivity(
                'blog_post_update_failed',
                "Failed to update blog post: {$post->title}",
                'failed',
                $e->getMessage(),
                ['post_id' => $post->id],
                []
            );

            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Failed to update blog post: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified blog post from storage (soft delete).
     */
    public function destroy(BlogPost $post): RedirectResponse
    {
        try {
            // Soft delete by setting is_deleted flag
            $post->update([
                'is_deleted' => true,
                'is_published' => false, // Unpublish when deleting
            ]);

            // Log activity
            $this->activityLogger->logActivity(
                'blog_post_deleted',
                "Deleted blog post: {$post->title}",
                'success',
                null,
                ['post_id' => $post->id, 'title' => $post->title],
                ['post_uuid' => $post->uuid]
            );

            return redirect()
                ->route('admin.blog.posts.index')
                ->with('success', 'Blog post deleted successfully!');

        } catch (\Exception $e) {
            // Log error
            $this->activityLogger->logActivity(
                'blog_post_delete_failed',
                "Failed to delete blog post: {$post->title}",
                'failed',
                $e->getMessage(),
                ['post_id' => $post->id],
                []
            );

            return redirect()
                ->back()
                ->with('error', 'Failed to delete blog post: ' . $e->getMessage());
        }
    }

    /**
     * Toggle the published status of a blog post.
     */
    public function togglePublished(BlogPost $post): RedirectResponse
    {
        try {
            $newStatus = !$post->is_published;

            $post->update([
                'is_published' => $newStatus,
                'published_at' => $newStatus && !$post->published_at ? now() : $post->published_at,
            ]);

            $action = $newStatus ? 'published' : 'unpublished';

            // Log activity
            $this->activityLogger->logActivity(
                "blog_post_{$action}",
                ucfirst($action) . " blog post: {$post->title}",
                'success',
                null,
                ['post_id' => $post->id, 'new_status' => $newStatus],
                []
            );

            return redirect()
                ->back()
                ->with('success', "Blog post {$action} successfully!");

        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', 'Failed to update publish status: ' . $e->getMessage());
        }
    }

    /**
     * Toggle the featured status of a blog post.
     */
    public function toggleFeatured(BlogPost $post): RedirectResponse
    {
        try {
            $newStatus = !$post->is_featured;

            $post->update(['is_featured' => $newStatus]);

            $action = $newStatus ? 'featured' : 'unfeatured';

            // Log activity
            $this->activityLogger->logActivity(
                "blog_post_{$action}",
                ucfirst($action) . " blog post: {$post->title}",
                'success',
                null,
                ['post_id' => $post->id, 'new_status' => $newStatus],
                []
            );

            return redirect()
                ->back()
                ->with('success', "Blog post {$action} successfully!");

        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', 'Failed to update featured status: ' . $e->getMessage());
        }
    }

    /**
     * Upload image via AJAX for rich text editor.
     */
    public function uploadImage(Request $request): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'image' => 'required|image|mimes:jpeg,jpg,png,gif,webp|max:5120'
        ]);

        try {
            $imageResult = $this->imageService->processUploadedImage(
                $request->file('image'),
                [
                    'subdirectory' => 'blog/content',
                    'create_variants' => false,
                    'create_webp' => true,
                    'quality' => 85
                ]
            );

            if ($imageResult['success']) {
                return response()->json([
                    'success' => true,
                    'url' => asset('storage/' . $imageResult['path']),
                    'path' => $imageResult['path']
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => implode(', ', $imageResult['errors'])
            ], 400);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a gallery image.
     */
    public function deleteGalleryImage(BlogPost $post, Request $request): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'image_path' => 'required|string'
        ]);

        try {
            $imagePath = $request->image_path;
            $galleryImages = $post->gallery_images ?? [];

            if (!in_array($imagePath, $galleryImages)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Image not found in gallery'
                ], 404);
            }

            // Remove from array
            $galleryImages = array_filter($galleryImages, fn($img) => $img !== $imagePath);

            // Update post
            $post->update(['gallery_images' => array_values($galleryImages)]);

            // Delete physical file
            $this->imageService->deleteImage($imagePath, true);

            // Log activity
            $this->activityLogger->logActivity(
                'blog_gallery_image_deleted',
                "Deleted gallery image from blog post: {$post->title}",
                'success',
                null,
                ['post_id' => $post->id, 'image_path' => $imagePath],
                []
            );

            return response()->json([
                'success' => true,
                'message' => 'Image deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Restore a soft-deleted blog post.
     */
    public function restore(Request $request, $id): RedirectResponse
    {
        try {
            $post = BlogPost::where('id', $id)->where('is_deleted', true)->firstOrFail();

            $post->update(['is_deleted' => false]);

            // Log activity
            $this->activityLogger->logActivity(
                'blog_post_restored',
                "Restored blog post: {$post->title}",
                'success',
                null,
                ['post_id' => $post->id],
                []
            );

            return redirect()
                ->route('admin.blog.posts.show', $post)
                ->with('success', 'Blog post restored successfully!');

        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', 'Failed to restore blog post: ' . $e->getMessage());
        }
    }
}

