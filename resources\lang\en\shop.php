<?php

return [
    // Page Meta
    'page_title' => 'Shop - ChiSolution',
    'meta_description' => 'Browse our collection of digital products and services. Find the perfect solution for your business needs.',
    'meta_keywords' => 'shop, products, digital services, e-commerce, online store',

    // Navigation & Breadcrumbs
    'shop' => 'Shop',
    'home' => 'Home',
    'all_products' => 'All Products',
    'categories' => 'Categories',
    'brands' => 'Brands',

    // Product Listing
    'products_found' => ':count products found',
    'no_products_found' => 'No products found',
    'no_products_message' => 'Try adjusting your search criteria or browse all products.',
    'browse_all_products' => 'Browse All Products',
    'search_failed' => 'Search failed',
    'search_error_message' => 'There was an error loading products. Please try again.',
    'try_again' => 'Try Again',
    'loading' => 'Loading...',

    // Filters & Sorting
    'filter_by' => 'Filter by',
    'sort_by' => 'Sort by',
    'price_range' => 'Price Range',
    'min_price' => 'Min Price',
    'max_price' => 'Max Price',
    'apply_filters' => 'Apply Filters',
    'clear_filters' => 'Clear Filters',
    'clear_all_filters' => 'Clear All Filters',
    'search_products' => 'Search products...',
    'search' => 'Search',

    // Sorting Options
    'sort_newest' => 'Newest First',
    'sort_oldest' => 'Oldest First',
    'sort_price_low' => 'Price: Low to High',
    'sort_price_high' => 'Price: High to Low',
    'sort_name_asc' => 'Name: A to Z',
    'sort_name_desc' => 'Name: Z to A',
    'sort_popularity' => 'Most Popular',
    'sort_rating' => 'Highest Rated',

    // Product Cards
    'featured' => 'Featured',
    'sale' => 'Sale',
    'new' => 'New',
    'out_of_stock' => 'Out of Stock',
    'in_stock' => 'In Stock',
    'low_stock' => 'Low Stock',
    'add_to_cart' => 'Add to Cart',
    'view_product' => 'View Product',
    'quick_view' => 'Quick View',
    'compare' => 'Compare',
    'wishlist' => 'Add to Wishlist',

    // Product Details
    'product_details' => 'Product Details',
    'description' => 'Description',
    'specifications' => 'Specifications',
    'reviews' => 'Reviews',
    'related_products' => 'Related Products',
    'you_may_also_like' => 'You may also like',
    'recently_viewed' => 'Recently Viewed',

    // Pricing
    'price' => 'Price',
    'regular_price' => 'Regular Price',
    'sale_price' => 'Sale Price',
    'discount' => 'Discount',
    'save_amount' => 'Save :amount',
    'save_percentage' => 'Save :percentage%',
    'from_price' => 'From :price',
    'starting_at' => 'Starting at',

    // Stock Status
    'availability' => 'Availability',
    'stock_status' => 'Stock Status',
    'items_left' => ':count items left',
    'only_left' => 'Only :count left!',
    'back_in_stock' => 'Back in Stock',
    'notify_when_available' => 'Notify when available',

    // Product Variants
    'variants' => 'Variants',
    'options' => 'Options',
    'choose_option' => 'Choose an option',
    'size' => 'Size',
    'color' => 'Color',
    'style' => 'Style',
    'quantity' => 'Quantity',

    // Cart Actions
    'add_to_cart_success' => 'Product added to cart successfully!',
    'add_to_cart_error' => 'Failed to add product to cart.',
    'cart_updated' => 'Cart updated successfully!',
    'continue_shopping' => 'Continue Shopping',
    'view_cart' => 'View Cart',
    'checkout' => 'Checkout',

    // Categories
    'all_categories' => 'All Categories',
    'category' => 'Category',
    'subcategories' => 'Subcategories',
    'products_in_category' => 'Products in :category',

    // Pagination
    'showing_results' => 'Showing :from to :to of :total results',
    'previous' => 'Previous',
    'next' => 'Next',
    'page' => 'Page',
    'of' => 'of',
    'results_per_page' => 'Results per page',

    // Error Messages
    'product_not_found' => 'Product not found',
    'category_not_found' => 'Category not found',
    'search_too_short' => 'Search term must be at least 2 characters',
    'invalid_price_range' => 'Invalid price range',
    'no_results_for_search' => 'No results found for ":search"',

    // Success Messages
    'filter_applied' => 'Filters applied successfully',
    'search_completed' => 'Search completed',

    // Loading States
    'loading_products' => 'Loading products...',
    'loading_more' => 'Loading more...',
    'processing' => 'Processing...',

    // Accessibility
    'product_image_alt' => 'Image of :product',
    'filter_toggle' => 'Toggle filters',
    'sort_toggle' => 'Toggle sort options',
    'grid_view' => 'Grid view',
    'list_view' => 'List view',

    // Mobile
    'show_filters' => 'Show Filters',
    'hide_filters' => 'Hide Filters',
    'apply' => 'Apply',
    'cancel' => 'Cancel',
];
