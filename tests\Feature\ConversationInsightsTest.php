<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\ChatAssignment;
use App\Services\ConversationInsightsService;
use App\Services\ActivityLogger;
use App\Services\DashboardCacheService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use PHPUnit\Framework\Attributes\Test;
use Carbon\Carbon;

class ConversationInsightsTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected User $staff;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles with unique slugs
        $adminRole = Role::factory()->create([
            'name' => 'admin',
            'slug' => 'admin-insights'
        ]);
        $staffRole = Role::factory()->create([
            'name' => 'staff',
            'slug' => 'staff-insights'
        ]);
        $userRole = Role::factory()->create([
            'name' => 'user',
            'slug' => 'user-insights'
        ]);

        // Create users with explicit active status
        $this->admin = User::factory()->create([
            'role_id' => $adminRole->id,
            'is_active' => true,
            'is_deleted' => false,
        ]);
        $this->staff = User::factory()->create([
            'role_id' => $staffRole->id,
            'is_active' => true,
            'is_deleted' => false,
        ]);
        $this->user = User::factory()->create([
            'role_id' => $userRole->id,
            'is_active' => true,
            'is_deleted' => false,
        ]);

        // Mock dependencies
        $this->mock(ActivityLogger::class, function ($mock) {
            $mock->shouldReceive('log')->andReturn(new \App\Models\ActivityLog());
            $mock->shouldReceive('logActivity')->andReturn(new \App\Models\ActivityLog());
        });

        $this->mock(DashboardCacheService::class, function ($mock) {
            $mock->shouldReceive('remember')->andReturnUsing(function ($key, $callback, $ttl = null) {
                return $callback();
            });
        });

        // Clear cache
        Cache::flush();
    }

    #[Test]
    public function admin_can_access_insights_dashboard()
    {
        // Create some test data for the view
        $testInsights = [
            'overview' => [
                'total_conversations' => 0,
                'completed_conversations' => 0,
                'completion_rate' => 0,
                'average_messages_per_conversation' => 0,
                'average_duration_minutes' => 0,
                'active_conversations' => 0,
                'pending_conversations' => 0,
            ],
            'trending_topics' => [
                'top_keywords' => [],
            ],
            'peak_hours' => [
                'peak_hours' => collect([]),
                'low_activity_hours' => collect([]),
            ],
            'user_behavior' => [
                'top_users' => [],
            ],
            'staff_insights' => [
                'staff_performance' => [],
            ],
        ];

        // Mock the service to return test data
        $this->mock(ConversationInsightsService::class, function ($mock) use ($testInsights) {
            $mock->shouldReceive('getConversationInsights')->andReturn($testInsights);
        });

        $response = $this->actingAs($this->admin)
            ->get(route('admin.chat.insights.index'));

        $response->assertStatus(200);
        // Just check that we get a successful response
        $this->assertTrue($response->isSuccessful());
    }

    #[Test]
    public function staff_can_access_insights_dashboard()
    {
        // Create test data
        $testInsights = [
            'overview' => [
                'total_conversations' => 0,
                'completed_conversations' => 0,
                'completion_rate' => 0,
                'average_messages_per_conversation' => 0,
                'average_duration_minutes' => 0,
                'active_conversations' => 0,
                'pending_conversations' => 0,
            ],
            'trending_topics' => ['top_keywords' => []],
            'peak_hours' => ['peak_hours' => collect([]), 'low_activity_hours' => collect([])],
            'user_behavior' => ['top_users' => []],
            'staff_insights' => ['staff_performance' => []],
        ];

        $this->mock(ConversationInsightsService::class, function ($mock) use ($testInsights) {
            $mock->shouldReceive('getConversationInsights')->andReturn($testInsights);
        });

        $response = $this->actingAs($this->staff)
            ->get(route('admin.chat.insights.index'));

        $response->assertStatus(200);
    }

    #[Test]
    public function regular_user_cannot_access_insights_dashboard()
    {
        $response = $this->actingAs($this->user)
            ->get(route('admin.chat.insights.index'));

        $response->assertStatus(403);
    }

    #[Test]
    public function insights_service_provides_comprehensive_data()
    {
        // Create test data
        $room = ChatRoom::factory()->create();
        $assignment = ChatAssignment::factory()->create([
            'chat_room_id' => $room->id,
            'assigned_to' => $this->staff->id,
        ]);

        ChatMessage::factory()->count(5)->create([
            'chat_room_id' => $room->id,
            'user_id' => $this->user->id,
            'is_ai_generated' => false,
        ]);

        // Create the service instance directly
        $insightsService = new ConversationInsightsService(
            app(DashboardCacheService::class),
            app(ActivityLogger::class)
        );

        $insights = $insightsService->getConversationInsights();

        $this->assertArrayHasKey('overview', $insights);
        $this->assertArrayHasKey('conversation_patterns', $insights);
        $this->assertArrayHasKey('trending_topics', $insights);
        $this->assertArrayHasKey('usage_patterns', $insights);
        $this->assertArrayHasKey('conversation_flow', $insights);
        $this->assertArrayHasKey('peak_hours', $insights);
        $this->assertArrayHasKey('conversation_length', $insights);
        $this->assertArrayHasKey('resolution_insights', $insights);
        $this->assertArrayHasKey('user_behavior', $insights);
        $this->assertArrayHasKey('staff_insights', $insights);
    }

    #[Test]
    public function insights_api_returns_data()
    {
        $testInsights = [
            'overview' => ['total_conversations' => 0],
            'conversation_patterns' => [],
            'trending_topics' => [],
            'usage_patterns' => [],
            'conversation_flow' => [],
            'peak_hours' => [],
            'conversation_length' => [],
            'resolution_insights' => [],
            'user_behavior' => [],
            'staff_insights' => [],
        ];

        $this->mock(ConversationInsightsService::class, function ($mock) use ($testInsights) {
            $mock->shouldReceive('getConversationInsights')->andReturn($testInsights);
        });

        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.insights.api.v1.insights.index'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'overview',
                'conversation_patterns',
                'trending_topics',
                'usage_patterns',
                'conversation_flow',
                'peak_hours',
                'conversation_length',
                'resolution_insights',
                'user_behavior',
                'staff_insights',
            ],
            'filters',
        ]);
    }

    #[Test]
    public function insights_api_returns_overview_data()
    {
        $testInsights = [
            'overview' => [
                'total_conversations' => 0,
                'completed_conversations' => 0,
                'completion_rate' => 0,
                'average_messages_per_conversation' => 0,
                'average_duration_minutes' => 0,
                'active_conversations' => 0,
                'pending_conversations' => 0,
            ],
        ];

        $this->mock(ConversationInsightsService::class, function ($mock) use ($testInsights) {
            $mock->shouldReceive('getConversationInsights')->andReturn($testInsights);
        });

        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.insights.api.v1.overview'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'total_conversations',
                'completed_conversations',
                'completion_rate',
                'average_messages_per_conversation',
                'average_duration_minutes',
                'active_conversations',
                'pending_conversations',
            ],
        ]);
    }

    #[Test]
    public function insights_api_returns_patterns_data()
    {
        $testInsights = ['conversation_patterns' => []];
        $this->mock(ConversationInsightsService::class, function ($mock) use ($testInsights) {
            $mock->shouldReceive('getConversationInsights')->andReturn($testInsights);
        });

        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.insights.api.v1.patterns'));

        $response->assertStatus(200);
        $response->assertJsonStructure(['success', 'data']);
    }

    #[Test]
    public function insights_api_returns_trending_topics()
    {
        $testInsights = ['trending_topics' => []];
        $this->mock(ConversationInsightsService::class, function ($mock) use ($testInsights) {
            $mock->shouldReceive('getConversationInsights')->andReturn($testInsights);
        });

        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.insights.api.v1.trending.topics'));

        $response->assertStatus(200);
        $response->assertJsonStructure(['success', 'data']);
    }

    #[Test]
    public function insights_api_returns_usage_patterns()
    {
        $testInsights = ['usage_patterns' => []];
        $this->mock(ConversationInsightsService::class, function ($mock) use ($testInsights) {
            $mock->shouldReceive('getConversationInsights')->andReturn($testInsights);
        });

        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.insights.api.v1.usage.patterns'));

        $response->assertStatus(200);
        $response->assertJsonStructure(['success', 'data']);
    }

    #[Test]
    public function insights_api_returns_conversation_flow()
    {
        $testInsights = ['conversation_flow' => []];
        $this->mock(ConversationInsightsService::class, function ($mock) use ($testInsights) {
            $mock->shouldReceive('getConversationInsights')->andReturn($testInsights);
        });

        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.insights.api.v1.conversation.flow'));

        $response->assertStatus(200);
        $response->assertJsonStructure(['success', 'data']);
    }

    #[Test]
    public function insights_api_returns_peak_hours()
    {
        $testInsights = ['peak_hours' => []];
        $this->mock(ConversationInsightsService::class, function ($mock) use ($testInsights) {
            $mock->shouldReceive('getConversationInsights')->andReturn($testInsights);
        });

        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.insights.api.v1.peak.hours'));

        $response->assertStatus(200);
        $response->assertJsonStructure(['success', 'data']);
    }

    #[Test]
    public function insights_api_returns_user_behavior()
    {
        $testInsights = ['user_behavior' => []];
        $this->mock(ConversationInsightsService::class, function ($mock) use ($testInsights) {
            $mock->shouldReceive('getConversationInsights')->andReturn($testInsights);
        });

        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.insights.api.v1.user.behavior'));

        $response->assertStatus(200);
        $response->assertJsonStructure(['success', 'data']);
    }

    #[Test]
    public function insights_api_returns_staff_insights()
    {
        $testInsights = ['staff_insights' => []];
        $this->mock(ConversationInsightsService::class, function ($mock) use ($testInsights) {
            $mock->shouldReceive('getConversationInsights')->andReturn($testInsights);
        });

        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.insights.api.v1.staff.insights'));

        $response->assertStatus(200);
        $response->assertJsonStructure(['success', 'data']);
    }

    #[Test]
    public function insights_export_returns_report_data()
    {
        $testInsights = [
            'overview' => ['total_conversations' => 0],
            'conversation_patterns' => [],
            'trending_topics' => [],
            'usage_patterns' => [],
            'conversation_flow' => [],
            'peak_hours' => [],
            'conversation_length' => [],
            'resolution_insights' => [],
            'user_behavior' => [],
            'staff_insights' => [],
        ];

        $this->mock(ConversationInsightsService::class, function ($mock) use ($testInsights) {
            $mock->shouldReceive('getConversationInsights')->andReturn($testInsights);
        });

        $response = $this->actingAs($this->admin)
            ->postJson(route('admin.chat.insights.api.v1.export'), [
                'format' => 'json',
                'start_date' => now()->subDays(7)->format('Y-m-d'),
                'end_date' => now()->format('Y-m-d'),
            ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'report_generated_at',
                'filters_applied',
                'format',
                'data',
            ],
        ]);
    }

    #[Test]
    public function insights_service_calculates_overview_correctly()
    {
        // Create test data with known values
        $room1 = ChatRoom::factory()->create(['status' => 'closed']);
        $room2 = ChatRoom::factory()->create(['status' => 'active']);
        $room3 = ChatRoom::factory()->create(['status' => 'closed']);

        // Create assignments
        ChatAssignment::factory()->create(['chat_room_id' => $room1->id, 'assigned_to' => $this->staff->id]);
        ChatAssignment::factory()->create(['chat_room_id' => $room2->id, 'assigned_to' => $this->staff->id]);
        ChatAssignment::factory()->create(['chat_room_id' => $room3->id, 'assigned_to' => $this->staff->id]);

        // Create messages
        ChatMessage::factory()->count(3)->create(['chat_room_id' => $room1->id]);
        ChatMessage::factory()->count(5)->create(['chat_room_id' => $room2->id]);
        ChatMessage::factory()->count(2)->create(['chat_room_id' => $room3->id]);

        // Create the service instance directly
        $insightsService = new ConversationInsightsService(
            app(DashboardCacheService::class),
            app(ActivityLogger::class)
        );

        $insights = $insightsService->getConversationInsights();

        $this->assertEquals(3, $insights['overview']['total_conversations']);
        $this->assertEquals(2, $insights['overview']['completed_conversations']);
        $this->assertEquals(66.67, round($insights['overview']['completion_rate'], 2));
    }

    #[Test]
    public function insights_service_handles_filters_correctly()
    {
        // Create test data with different staff members
        $staff2 = User::factory()->create(['role_id' => $this->staff->role_id]);

        $room1 = ChatRoom::factory()->create();
        $room2 = ChatRoom::factory()->create();

        ChatAssignment::factory()->create(['chat_room_id' => $room1->id, 'assigned_to' => $this->staff->id]);
        ChatAssignment::factory()->create(['chat_room_id' => $room2->id, 'assigned_to' => $staff2->id]);

        ChatMessage::factory()->create(['chat_room_id' => $room1->id, 'user_id' => $this->user->id]);
        ChatMessage::factory()->create(['chat_room_id' => $room2->id, 'user_id' => $this->user->id]);

        // Create the service instance directly
        $insightsService = new ConversationInsightsService(
            app(DashboardCacheService::class),
            app(ActivityLogger::class)
        );

        // Test with staff filter
        $insightsWithStaffFilter = $insightsService->getConversationInsights([
            'staff_id' => $this->staff->id,
            'start_date' => now()->subDay(),
            'end_date' => now(),
        ]);

        $this->assertEquals(1, $insightsWithStaffFilter['overview']['total_conversations']);
    }

    #[Test]
    public function insights_service_extracts_keywords_correctly()
    {
        // Create test messages with known content
        $room = ChatRoom::factory()->create();
        ChatAssignment::factory()->create(['chat_room_id' => $room->id, 'assigned_to' => $this->staff->id]);

        ChatMessage::factory()->create([
            'chat_room_id' => $room->id,
            'user_id' => $this->user->id,
            'is_ai_generated' => false,
            'content' => 'I need help with my order payment issue'
        ]);
        ChatMessage::factory()->create([
            'chat_room_id' => $room->id,
            'user_id' => $this->user->id,
            'is_ai_generated' => false,
            'content' => 'The payment failed and I cannot complete my order'
        ]);

        // Create the service instance directly
        $insightsService = new ConversationInsightsService(
            app(DashboardCacheService::class),
            app(ActivityLogger::class)
        );

        $insights = $insightsService->getConversationInsights();

        // Check that trending topics are extracted
        $this->assertArrayHasKey('trending_topics', $insights);
        $this->assertArrayHasKey('top_keywords', $insights['trending_topics']);
    }

    #[Test]
    public function unauthorized_users_cannot_access_insights_api()
    {
        $response = $this->getJson(route('admin.chat.insights.api.v1.overview'));
        $response->assertStatus(401);

        $response = $this->actingAs($this->user)
            ->getJson(route('admin.chat.insights.api.v1.overview'));
        $response->assertStatus(403);
    }

    #[Test]
    public function insights_api_validates_filters()
    {
        // Mock the service to avoid actual processing
        $this->mock(ConversationInsightsService::class, function ($mock) {
            $mock->shouldReceive('getConversationInsights')->andReturn([]);
        });

        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.insights.api.v1.insights.index') . '?start_date=invalid-date');

        $response->assertStatus(422);
    }

    #[Test]
    public function insights_service_handles_empty_data_gracefully()
    {
        // Create the service instance directly
        $insightsService = new ConversationInsightsService(
            app(DashboardCacheService::class),
            app(ActivityLogger::class)
        );

        // Test with no data
        $insights = $insightsService->getConversationInsights();

        $this->assertEquals(0, $insights['overview']['total_conversations']);
        $this->assertEquals(0, $insights['overview']['completed_conversations']);
        $this->assertEquals(0, $insights['overview']['completion_rate']);
        $this->assertEquals(0, $insights['overview']['average_messages_per_conversation']);
        $this->assertEquals(0, $insights['overview']['average_duration_minutes']);
    }
}
