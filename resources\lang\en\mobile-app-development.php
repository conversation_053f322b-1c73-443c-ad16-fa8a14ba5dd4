<?php

return [
    // Page Meta
    'page_title' => 'Mobile App Development - ChiSolution',
    'meta_description' => 'Leading mobile app development company in South Africa specializing in cross-platform app development, React Native, Flutter, iOS and Android apps. Expert mobile app development services with competitive pricing.',
    'meta_keywords' => 'mobile app development South Africa, cross-platform app development, React Native development, Flutter app development, iOS app development, Android app development, mobile app development cost, app development company, native mobile apps, hybrid app development, mobile app UI/UX design, app development services',

    // Hero Section
    'hero_title' => 'Cross-Platform Mobile App Development Services',
    'hero_description' => 'Leading mobile app development company specializing in React Native, Flutter, and native iOS/Android apps. Transform your ideas into powerful mobile applications with competitive pricing and expert development services.',
    'get_quote' => 'Get Quote',
    'view_apps' => 'View Apps',

    // Cross-Platform Section
    'cross_platform_title' => 'Cross-Platform Mobile App <span class="text-blue-600">Development</span>',
    'cross_platform_description' => 'Build once, deploy everywhere. Our cross-platform mobile app development services help you reach both iOS and Android users with a single codebase, reducing development time and costs.',

    // Features
    'react_native_title' => 'React Native Development',
    'react_native_description' => 'Native performance with JavaScript. Build high-quality mobile apps using React Native for both iOS and Android platforms.',
    'flutter_title' => 'Flutter App Development',
    'flutter_description' => 'Google\'s UI toolkit for beautiful, natively compiled applications from a single codebase.',
    'native_ios_title' => 'Native iOS Development',
    'native_ios_description' => 'Swift and Objective-C development for optimal performance and platform-specific features.',
    'native_android_title' => 'Native Android Development',
    'native_android_description' => 'Kotlin and Java development leveraging the full power of the Android platform.',
    'ui_ux_design_title' => 'Mobile UI/UX Design',
    'ui_ux_design_description' => 'User-centered design approach creating intuitive and engaging mobile experiences.',
    'app_testing_title' => 'App Testing & QA',
    'app_testing_description' => 'Comprehensive testing across devices, platforms, and scenarios to ensure quality.',
    'educational_apps_title' => 'Educational Apps',
    'educational_apps_description' => 'Learning platforms with interactive content, progress tracking, and gamification features.',
    'healthcare_apps_title' => 'Healthcare Apps',
    'healthcare_apps_description' => 'Health and wellness applications with appointment booking, health tracking, and telemedicine features.',
    'entertainment_apps_title' => 'Entertainment Apps',
    'entertainment_apps_description' => 'Gaming, media streaming, and entertainment applications with engaging user experiences.',

    // Technologies Section
    'technologies_title' => '2025 Mobile App <span class="text-blue-600">Technologies</span>',
    'technologies_description' => 'Cutting-edge mobile app development technologies for cost-effective, high-performance applications with competitive pricing.',

    // Technology Items
    'react_native' => 'React Native',
    'flutter' => 'Flutter',
    'swift' => 'Swift',
    'kotlin' => 'Kotlin',
    'xamarin' => 'Xamarin',
    'ionic' => 'Ionic',
    'firebase' => 'Firebase',
    'aws_amplify' => 'AWS Amplify',
    'graphql' => 'GraphQL',
    'rest_api' => 'REST API',
    'push_notifications' => 'Push Notifications',
    'offline_storage' => 'Offline Storage',

    // Process Steps
    'discovery_title' => 'Discovery & Planning',
    'discovery_description' => 'We analyze your requirements, target audience, and business goals to create a comprehensive development strategy.',
    'design_title' => 'UI/UX Design',
    'design_description' => 'Our designers create intuitive and engaging user interfaces optimized for mobile devices.',
    'development_title' => 'Development',
    'development_description' => 'Our expert developers build your app using the latest technologies and best practices.',
    'testing_title' => 'Testing & Launch',
    'testing_description' => 'Rigorous testing across devices and platforms before launching to app stores.',

    // Service Descriptions
    'react_native_description' => 'Cross-platform mobile apps with React Native. Single codebase for iOS and Android with 60% cost savings and faster time-to-market.',
    'cross_platform_description' => 'Single codebase for both iOS and Android using React Native or Flutter. Faster development and cost-effective solution.',

    // Benefits
    'cost_effective_title' => 'Cost-Effective',
    'cost_effective_description' => 'Single codebase for multiple platforms',
    'faster_development_title' => 'Faster Development',
    'faster_development_description' => 'Reduced time to market',
    'single_codebase_title' => 'Single Codebase',
    'native_performance_title' => 'Native Performance',
    'native_performance_description' => 'Optimized for each platform',
    'easy_maintenance_title' => 'Easy Maintenance',
    'easy_maintenance_description' => 'Simplified updates and bug fixes',

    // CTA Section
    'cta_title' => 'Ready to Build Your Mobile App?',
    'cta_description' => 'Let\'s discuss your mobile app idea and create a solution that engages your users and grows your business.',
    'start_project' => 'Start Your Project',
    'get_consultation' => 'Get Free Consultation',
];
