<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductCategory;
use App\Services\ShopCacheService;
use App\Services\PerformanceOptimizer;
use Illuminate\Http\Request;
use Illuminate\View\View;

class ShopController extends Controller
{
    protected $shopCache;
    protected $performanceOptimizer;

    public function __construct(ShopCacheService $shopCache, PerformanceOptimizer $performanceOptimizer)
    {
        $this->shopCache = $shopCache;
        $this->performanceOptimizer = $performanceOptimizer;
    }

    /**
     * Display the shop index page.
     */
    public function index(string $locale, Request $request)
    {
        // Check performance limits early
        if (!$this->performanceOptimizer->checkLimits()) {
            return response()->json(['error' => 'Service temporarily unavailable'], 503);
        }

        // For AJAX requests, return JSON data
        if ($request->expectsJson()) {
            return $this->getProductsData($request);
        }

        try {
            // Get cached categories for filter sidebar with timeout protection
            $categories = $this->performanceOptimizer->optimizeQuery(
                'shop_categories_' . app()->getLocale(),
                fn() => $this->shopCache->getCategoriesWithCounts(),
                600 // 10 minutes cache
            );

            // Get cached price range for filter
            $priceRange = $this->performanceOptimizer->optimizeQuery(
                'shop_price_range',
                fn() => $this->shopCache->getPriceRange(),
                1800 // 30 minutes cache
            );

            // For initial page load, get cached products
            $products = $this->performanceOptimizer->optimizeQuery(
                'shop_products_' . md5(serialize($request->all())),
                fn() => $this->shopCache->getSearchResults($request, 12),
                300 // 5 minutes cache
            );

            return view('pages.shop.index', compact('products', 'categories', 'priceRange'));
        } finally {
            $this->performanceOptimizer->cleanup();
        }
    }

    /**
     * Get products data for AJAX requests.
     */
    private function getProductsData(Request $request)
    {
        // Use cached search results for AJAX requests too
        $products = $this->shopCache->getSearchResults($request, 12);

        return response()->json([
            'success' => true,
            'products' => $products->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'slug' => $product->slug,
                    'short_description' => $product->short_description,
                    'price' => $product->formatted_price,
                    'compare_price' => $product->formatted_compare_price,
                    'discount_percentage' => $product->discount_percentage,
                    'primary_image' => $product->primary_image,
                    'stock_status' => $product->stock_status,
                    'is_featured' => $product->is_featured,
                    'categories' => $product->categories->map(function ($category) {
                        return ['name' => $category->name];
                    }),
                    'url' => route('shop.product', $product->slug),
                ];
            }),
            'pagination' => [
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage(),
                'per_page' => $products->perPage(),
                'total' => $products->total(),
                'from' => $products->firstItem(),
                'to' => $products->lastItem(),
                'has_more_pages' => $products->hasMorePages(),
                'links' => $products->links()->render(),
            ],
            'filters' => $request->only(['search', 'category', 'min_price', 'max_price', 'sort']),
        ]);
    }

    /**
     * Build the product query with filters and sorting.
     */
    private function buildProductQuery(Request $request)
    {
        $query = Product::active()->with(['categories']);

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Category filter
        if ($request->filled('category')) {
            $category = ProductCategory::active()->where('slug', $request->category)->first();
            if ($category) {
                // Simple category filter without recursion for now
                $query->whereHas('categories', function ($q) use ($category) {
                    $q->where('product_categories.id', $category->id);
                });
            }
        }

        // Price filter
        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }

        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        // Sorting
        $sort = $request->get('sort', 'name');

        switch ($sort) {
            case 'name':
                $query->orderBy('name', 'asc');
                break;
            case 'name-desc':
                $query->orderBy('name', 'desc');
                break;
            case 'price':
                $query->orderBy('price', 'asc');
                break;
            case 'price-desc':
                $query->orderBy('price', 'desc');
                break;
            case 'created_at':
                $query->orderBy('created_at', 'desc');
                break;
            case 'created_at-asc':
                $query->orderBy('created_at', 'asc');
                break;
            case 'featured':
                $query->orderBy('is_featured', 'desc')->orderBy('name', 'asc');
                break;
            default:
                $query->orderBy('name', 'asc');
                break;
        }

        return $query;
    }
    
    /**
     * Display products in a specific category.
     */
    public function category(string $locale, Request $request, ProductCategory $category): View
    {
        if (!$category->is_active) {
            abort(404);
        }
        
        $categoryIds = $category->getAllCategoryIds();
        
        $query = Product::active()
            ->with(['categories'])
            ->whereHas('categories', function ($q) use ($categoryIds) {
                $q->whereIn('product_categories.id', $categoryIds);
            });
        
        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }
        
        // Price filter
        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }
        
        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }
        
        // Sorting
        $sort = $request->get('sort', 'name');

        switch ($sort) {
            case 'name':
                $query->orderBy('name', 'asc');
                break;
            case 'name-desc':
                $query->orderBy('name', 'desc');
                break;
            case 'price':
                $query->orderBy('price', 'asc');
                break;
            case 'price-desc':
                $query->orderBy('price', 'desc');
                break;
            case 'created_at':
                $query->orderBy('created_at', 'desc');
                break;
            case 'created_at-asc':
                $query->orderBy('created_at', 'asc');
                break;
            case 'featured':
                $query->orderBy('is_featured', 'desc')->orderBy('name', 'asc');
                break;
            default:
                $query->orderBy('name', 'asc');
                break;
        }
        
        $products = $query->paginate(12)->withQueryString();
        
        // Get subcategories
        $subcategories = $category->children()->active()->ordered()->withCount(['activeProducts as products_count'])->get();
        
        // Get cached price range for this category
        $priceRange = $this->shopCache->getCategoryPriceRange($category);
        
        return view('pages.shop.category', compact('category', 'products', 'subcategories', 'priceRange'));
    }
    
    /**
     * Display a specific product.
     */
    public function product(string $locale, Product $product): View
    {
        if (!$product->is_active) {
            abort(404);
        }

        $product->load(['categories', 'variants' => function ($query) {
            $query->active();
        }]);

        // Load reviews with user information
        $reviews = $product->approvedReviews()
            ->with(['user', 'order'])
            ->paginate(10);

        // Get user's eligibility to review if authenticated
        $userEligibility = null;
        $userReview = null;
        if (auth()->check()) {
            $userEligibility = \App\Models\ProductReview::validatePurchaseEligibility($product->id, auth()->id());
            $userReview = $product->getUserReview(auth()->id());
        }

        // Get related products from same categories
        $relatedProducts = Product::active()
            ->where('id', '!=', $product->id)
            ->whereHas('categories', function ($query) use ($product) {
                $query->whereIn('product_categories.id', $product->categories->pluck('id'));
            })
            ->inStock()
            ->limit(4)
            ->get();

        return view('pages.shop.product', compact('product', 'relatedProducts', 'reviews', 'userEligibility', 'userReview'));
    }
    
    /**
     * Search products via AJAX.
     */
    public function search(Request $request)
    {
        $request->validate([
            'q' => 'required|string|min:2|max:100',
        ]);
        
        $products = Product::active()
            ->search($request->q)
            ->inStock()
            ->limit(10)
            ->get(['id', 'name', 'slug', 'price', 'featured_image']);
        
        return response()->json([
            'products' => $products->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'slug' => $product->slug,
                    'price' => $product->formatted_price,
                    'image' => $product->primary_image,
                    'url' => route('shop.product', $product->slug),
                ];
            }),
        ]);
    }
    
    /**
     * Get product quick view data.
     */
    public function quickView(Product $product)
    {
        if (!$product->is_active) {
            abort(404);
        }
        
        $product->load(['variants' => function ($query) {
            $query->active();
        }]);
        
        return response()->json([
            'product' => [
                'id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
                'short_description' => $product->short_description,
                'price' => $product->formatted_price,
                'compare_price' => $product->formatted_compare_price,
                'discount_percentage' => $product->discount_percentage,
                'images' => $product->all_images,
                'stock_status' => $product->stock_status,
                'is_in_stock' => $product->isInStock(),
                'variants' => $product->variants->map(function ($variant) {
                    return [
                        'id' => $variant->id,
                        'name' => $variant->name,
                        'price' => $variant->formatted_price,
                        'stock_status' => $variant->stock_status,
                        'is_in_stock' => $variant->isInStock(),
                        'attributes' => $variant->formatted_attributes,
                    ];
                }),
                'url' => route('shop.product', $product->slug),
            ],
        ]);
    }
}
