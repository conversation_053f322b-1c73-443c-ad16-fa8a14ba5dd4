<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cache;

class ViewServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Share global cart count with all views
        View::composer('*', function ($view) {
            $globalCartCount = $this->getCartCount();
            $view->with('globalCartCount', $globalCartCount);
        });
    }

    /**
     * Get cart count for the current user/session.
     */
    private function getCartCount(): int
    {
        try {
            $cacheKey = Auth::check()
                ? 'cart_count_user_' . Auth::id()
                : 'cart_count_session_' . Session::getId();

            // Use shorter TTL for session-based caches to prevent memory leaks
            $ttl = Auth::check() ? 60 : 30; // 60 seconds for users, 30 for sessions
            
            return Cache::remember($cacheKey, $ttl, function () {
                // Use direct database query to avoid loading full cart model
                if (Auth::check()) {
                    return \DB::table('shopping_carts')
                        ->join('cart_items', 'shopping_carts.id', '=', 'cart_items.cart_id')
                        ->where('shopping_carts.user_id', Auth::id())
                        ->where('shopping_carts.expires_at', '>', now())
                        ->sum('cart_items.quantity') ?: 0;
                } else {
                    $sessionId = Session::getId();
                    return \DB::table('shopping_carts')
                        ->join('cart_items', 'shopping_carts.id', '=', 'cart_items.cart_id')
                        ->where('shopping_carts.session_id', $sessionId)
                        ->where('shopping_carts.expires_at', '>', now())
                        ->sum('cart_items.quantity') ?: 0;
                }
            });
        } catch (\Exception $e) {
            // Return 0 if there's any error (e.g., during testing without proper session)
            return 0;
        }
    }
}
