<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clients', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('company')->nullable();
            $table->text('description')->nullable();
            $table->string('logo_path')->nullable();
            $table->json('project_images')->nullable(); // Store array of image paths
            $table->string('website_url')->nullable();
            $table->string('industry')->nullable();
            $table->date('project_start_date')->nullable();
            $table->date('project_end_date')->nullable();
            $table->enum('project_status', ['completed', 'ongoing', 'paused'])->default('completed');
            $table->text('testimonial')->nullable();
            $table->integer('rating')->nullable()->comment('1-5 star rating');
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->boolean('is_deleted')->default(false);
            $table->timestamps();

            // Indexes for better performance
            $table->index(['is_active', 'is_deleted', 'sort_order']);
            $table->index(['is_featured', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clients');
    }
};
