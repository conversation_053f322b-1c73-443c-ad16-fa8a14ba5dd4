<?php

namespace App\Services;

use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\AiTrainingData;
use App\Models\AiConversationLog;
use App\Models\ChatSystemSetting;
use App\Models\User;
use App\Services\AI\AIProviderManager;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use OpenAI\Laravel\Facades\OpenAI;

class ChatAIService
{
    protected ?CircuitBreakerService $circuitBreaker = null;
    protected ActivityLogger $activityLogger;
    protected DashboardCacheService $cacheService;
    protected AIProviderManager $aiProviderManager;

    public function __construct(
        ActivityLogger $activityLogger,
        DashboardCacheService $cacheService
    ) {
        $this->activityLogger = $activityLogger;
        $this->cacheService = $cacheService;
        $this->aiProviderManager = app(AIProviderManager::class);
    }

    /**
     * Get or create circuit breaker instance
     */
    protected function getCircuitBreaker(): CircuitBreakerService
    {
        if (!$this->circuitBreaker) {
            $this->circuitBreaker = new CircuitBreakerService(
                'chat_ai_service',
                config('openai.circuit_breaker.failure_threshold', 5),
                config('openai.circuit_breaker.recovery_timeout', 300),
                config('openai.circuit_breaker.expected_exception_threshold', 10)
            );
        }
        return $this->circuitBreaker;
    }

    /**
     * Check if AI service is available.
     */
    public function isAIAvailable(): bool
    {
        // Check if AI is enabled in settings
        if (!ChatSystemSetting::isAiEnabled()) {
            return false;
        }

        // Check if API key is configured
        if (empty(config('services.openai.api_key'))) {
            return false;
        }

        // Check circuit breaker status
        return $this->getCircuitBreaker()->isAvailable();
    }

    /**
     * Generate automated greeting message.
     */
    public function generateGreeting(?User $user = null): array
    {
        if (!config('openai.chat.greeting_enabled', true)) {
            return [];
        }

        $greeting = config('openai.templates.greeting', 'Hello! How can I help you today?');

        // Personalize greeting if user is known
        if ($user && $user->first_name) {
            $greeting = "Hello {$user->first_name}! " . $greeting;
        }

        return [
            'content' => $greeting,
            'confidence' => 1.0,
            'is_greeting' => true,
        ];
    }

    /**
     * Generate AI response with progressive complexity and circuit breaker protection.
     */
    public function generateResponse(string $message, ChatRoom $room, array $context = []): array
    {
        // Check rate limiting first
        $userId = auth()->id() ?? null;
        if (!$this->checkRateLimit($userId)) {
            return $this->getFallbackResponse($message, 'Rate limit exceeded', 'en');
        }

        // Analyze sentiment and detect language for all messages (do this first)
        $sentiment = $this->analyzeSentiment($message);
        $detectedLanguage = $this->detectLanguage($message);

        // Use room language as primary, only use detected language if room language is not set
        $language = $room->language ?? $detectedLanguage;

        if (!ChatSystemSetting::isAiEnabled()) {
            $fallbackResponse = $this->getFallbackResponse($message, 'AI system disabled', $language);
            $fallbackResponse['sentiment'] = $sentiment;
            $fallbackResponse['detected_language'] = $detectedLanguage;
            return $fallbackResponse;
        }

        $startTime = microtime(true);

        // Extract user message ID from context if available
        $userMessageId = $context['user_message_id'] ?? null;

        try {
            // Level 1: Template/FAQ matching (instant)
            $templateResponse = $this->getTemplateResponse($message, $language);
            if ($templateResponse) {
                $templateResponse['sentiment'] = $sentiment;
                $templateResponse['detected_language'] = $detectedLanguage;
                $this->logConversation($room, $message, $templateResponse, 'template', 0.95, $startTime, false, null, $userMessageId);
                return $templateResponse;
            }

            // Level 2: Simple NLP (100ms)
            $simpleResponse = $this->getSimpleNLPResponse($message, $language);
            if ($simpleResponse) {
                $simpleResponse['sentiment'] = $sentiment;
                $simpleResponse['detected_language'] = $detectedLanguage;
                $this->logConversation($room, $message, $simpleResponse, 'simple_nlp', 0.75, $startTime, false, null, $userMessageId);
                return $simpleResponse;
            }

            // Level 3: Full AI with circuit breaker (2s)
            $fullResponse = $this->getFullAIResponse($message, $room, $context, $startTime, $language);
            $fullResponse['sentiment'] = $sentiment;
            $fullResponse['detected_language'] = $detectedLanguage;
            return $fullResponse;

        } catch (\Exception $e) {
            $this->activityLogger->log('ai_response_error', null, [
                'error' => $e->getMessage(),
                'message' => $message,
                'room_id' => $room->id,
            ]);

            $fallbackResponse = $this->getFallbackResponse($message, 'AI processing error', $language);
            $fallbackResponse['sentiment'] = $sentiment;
            $fallbackResponse['detected_language'] = $detectedLanguage;

            // Log the fallback response
            $this->logConversation($room, $message, $fallbackResponse, 'fallback', 0.5, $startTime, true, $e->getMessage(), $userMessageId);

            return $fallbackResponse;
        }
    }

    /**
     * Get template response for common queries.
     */
    protected function getTemplateResponse(string $message, string $language): ?array
    {
        $cacheKey = "ai_template_response_" . md5($message . $language);
        
        return $this->cacheService->remember(
            $cacheKey,
            function () use ($message, $language) {
                $trainingData = AiTrainingData::active()
                                             ->language($language)
                                             ->where('confidence_threshold', '>=', 0.9)
                                             ->get();

                foreach ($trainingData as $data) {
                    $similarity = $this->calculateSimilarity($message, $data->input_text);
                    
                    if ($similarity >= 0.9) {
                        return [
                            'response' => $data->expected_response,
                            'confidence' => $similarity,
                            'intent' => $data->intent,
                            'should_escalate' => false,
                            'response_type' => 'template',
                            'processing_time_ms' => 10,
                        ];
                    }
                }

                return null;
            },
            3600 // 1 hour cache for templates
        );
    }

    /**
     * Get simple NLP response.
     */
    protected function getSimpleNLPResponse(string $message, string $language): ?array
    {
        $cacheKey = "ai_simple_nlp_" . md5($message . $language);

        return $this->cacheService->remember(
            $cacheKey,
            function () use ($message, $language) {
                // Simple intent detection based on keywords
                $intent = $this->detectSimpleIntent($message, $language);

                if ($intent) {
                    $response = $this->getIntentResponse($intent, $language);

                    if ($response) {
                        return [
                            'response' => $response,
                            'confidence' => 0.75,
                            'intent' => $intent,
                            'should_escalate' => $intent === 'frustration', // Escalate frustration
                            'response_type' => 'simple_nlp',
                            'processing_time_ms' => 100,
                        ];
                    }
                }

                // Enhanced fallback for common patterns
                $enhancedResponse = $this->getEnhancedFallbackResponse($message, $language);
                if ($enhancedResponse) {
                    return $enhancedResponse;
                }

                return null;
            },
            1800 // 30 minutes cache
        );
    }

    /**
     * Get full AI response with circuit breaker protection.
     */
    protected function getFullAIResponse(string $message, ChatRoom $room, array $context, float $startTime, string $language = 'en'): array
    {
        $userMessageId = $context['user_message_id'] ?? null;

        return $this->getCircuitBreaker()->call(
            function () use ($message, $room, $context, $startTime, $userMessageId) {
                $response = $this->callOpenAI($message, $room, $context);
                $this->logConversation($room, $message, $response, 'openai', $response['confidence'], $startTime, false, null, $userMessageId);
                return $response;
            },
            function (\Exception $e = null) use ($message, $room, $startTime, $userMessageId) {
                $fallback = $this->getFallbackResponse($message, 'AI service unavailable');
                $this->logConversation($room, $message, $fallback, 'fallback', 0.5, $startTime, true, $e?->getMessage(), $userMessageId);
                return $fallback;
            }
        );
    }

    /**
     * Call AI provider API with multi-provider support.
     */
    protected function callOpenAI(string $message, ChatRoom $room, array $context): array
    {
        $config = config('chat.ai');
        $startTime = microtime(true);

        // Build conversation context for AI provider
        $aiContext = [
            'system_message' => $this->buildSystemPrompt($room->language, $context),
            'history' => $context['conversation_history'] ?? [],
            'temperature' => $config['temperature'] ?? 0.7,
            'max_tokens' => $config['max_tokens'] ?? 1000,
        ];

        // Get provider and model from config or use defaults
        $provider = $config['provider'] ?? config('ai-providers.default', 'openai');
        $model = $config['model'] ?? null; // Let provider choose default model

        try {
            // Use the new AI provider manager
            $response = $this->aiProviderManager->generateResponse(
                $message,
                $aiContext,
                $model,
                $provider
            );

            $aiResponse = $response['content'] ?? '';
            $tokensUsed = $response['usage']['total_tokens'] ?? 0;
            $modelUsed = $response['model'] ?? $model;
            $providerUsed = $response['provider'] ?? $provider;

        } catch (\Exception $e) {
            Log::error('AI Provider API Error', [
                'error' => $e->getMessage(),
                'provider' => $provider,
                'model' => $model,
                'room_id' => $room->id,
                'message_preview' => substr($message, 0, 100)
            ]);
            throw new \Exception('AI API request failed: ' . $e->getMessage());
        }

        $endTime = microtime(true);
        $processingTime = ($endTime - $startTime) * 1000; // Convert to milliseconds

        // Detect intent and confidence
        $intent = $this->detectIntent($message, $aiResponse);
        $confidence = $this->calculateConfidence($message, $aiResponse, $intent);

        return [
            'response' => $aiResponse,
            'confidence' => $confidence,
            'intent' => $intent,
            'should_escalate' => $confidence < config('chat.ai.escalation_threshold', 0.3),
            'response_type' => $providerUsed,
            'processing_time_ms' => round($processingTime, 2),
            'model_used' => $modelUsed,
            'provider_used' => $providerUsed,
            'tokens_used' => $tokensUsed,
            'raw_response' => $response,
        ];
    }

    /**
     * Generate AI response with specific provider and model.
     */
    public function generateResponseWithProvider(
        string $message,
        ChatRoom $room,
        array $context = [],
        string $provider = null,
        string $model = null
    ): array {
        $startTime = microtime(true);

        // Build conversation context
        $aiContext = [
            'system_message' => $this->buildSystemPrompt($room->language, $context),
            'history' => $context['conversation_history'] ?? [],
            'temperature' => $context['temperature'] ?? 0.7,
            'max_tokens' => $context['max_tokens'] ?? 1000,
        ];

        try {
            $response = $this->aiProviderManager->generateResponse(
                $message,
                $aiContext,
                $model,
                $provider
            );

            // Detect intent and confidence
            $intent = $this->detectIntent($message, $response['content']);
            $confidence = $this->calculateConfidence($message, $response['content'], $intent);

            $result = [
                'response' => $response['content'],
                'confidence' => $confidence,
                'intent' => $intent,
                'should_escalate' => $confidence < config('chat.ai.escalation_threshold', 0.3),
                'response_type' => $response['provider'],
                'processing_time_ms' => round((microtime(true) - $startTime) * 1000, 2),
                'model_used' => $response['model'],
                'provider_used' => $response['provider'],
                'tokens_used' => $response['usage']['total_tokens'] ?? 0,
                'raw_response' => $response,
            ];

            // Log the conversation
            $userMessageId = $context['user_message_id'] ?? null;
            $this->logConversation($room, $message, $result, $response['provider'], $confidence, $startTime, false, null, $userMessageId);

            return $result;

        } catch (\Exception $e) {
            Log::error('AI Provider Error', [
                'error' => $e->getMessage(),
                'provider' => $provider,
                'model' => $model,
                'room_id' => $room->id,
            ]);

            $fallback = $this->getFallbackResponse($message, 'AI service unavailable', $room->language);
            $userMessageId = $context['user_message_id'] ?? null;
            $this->logConversation($room, $message, $fallback, 'fallback', 0.5, $startTime, true, $e->getMessage(), $userMessageId);
            return $fallback;
        }
    }

    /**
     * Analyze sentiment using AI providers.
     */
    public function analyzeSentimentWithAI(string $text, string $provider = null, string $model = null): array
    {
        try {
            $aiProvider = $this->aiProviderManager->provider($provider);
            return $aiProvider->analyzeSentiment($text, $model);
        } catch (\Exception $e) {
            Log::error('AI Sentiment Analysis Error', [
                'error' => $e->getMessage(),
                'provider' => $provider,
                'model' => $model,
            ]);

            // Fallback to simple sentiment analysis
            return $this->analyzeSentiment($text);
        }
    }

    /**
     * Translate text using AI providers.
     */
    public function translateWithAI(
        string $text,
        string $targetLanguage,
        string $sourceLanguage = null,
        string $provider = null,
        string $model = null
    ): array {
        try {
            $aiProvider = $this->aiProviderManager->provider($provider);
            return $aiProvider->translateText($text, $targetLanguage, $sourceLanguage, $model);
        } catch (\Exception $e) {
            Log::error('AI Translation Error', [
                'error' => $e->getMessage(),
                'provider' => $provider,
                'model' => $model,
            ]);

            throw $e;
        }
    }

    /**
     * Summarize conversation using AI providers.
     */
    public function summarizeConversationWithAI(
        ChatRoom $room,
        int $maxLength = 150,
        string $provider = null,
        string $model = null
    ): array {
        try {
            // Get conversation history
            $messages = $room->messages()
                ->orderBy('created_at', 'asc')
                ->get()
                ->map(function ($message) {
                    return $message->user ?
                        "{$message->user->name}: {$message->content}" :
                        "System: {$message->content}";
                })
                ->implode("\n");

            $aiProvider = $this->aiProviderManager->provider($provider);
            return $aiProvider->summarizeText($messages, $maxLength, $model);
        } catch (\Exception $e) {
            Log::error('AI Conversation Summary Error', [
                'error' => $e->getMessage(),
                'provider' => $provider,
                'model' => $model,
                'room_id' => $room->id,
            ]);

            throw $e;
        }
    }

    /**
     * Extract topics from conversation using AI providers.
     */
    public function extractConversationTopics(
        ChatRoom $room,
        int $maxTopics = 5,
        string $provider = null,
        string $model = null
    ): array {
        try {
            // Get conversation history
            $messages = $room->messages()
                ->orderBy('created_at', 'asc')
                ->get()
                ->pluck('content')
                ->implode("\n");

            $aiProvider = $this->aiProviderManager->provider($provider);
            return $aiProvider->extractTopics($messages, $maxTopics, $model);
        } catch (\Exception $e) {
            Log::error('AI Topic Extraction Error', [
                'error' => $e->getMessage(),
                'provider' => $provider,
                'model' => $model,
                'room_id' => $room->id,
            ]);

            throw $e;
        }
    }

    /**
     * Get available AI providers and models.
     */
    public function getAvailableProvidersAndModels(): array
    {
        $providers = [];

        foreach ($this->aiProviderManager->getAvailableProviders() as $providerName) {
            $providerInfo = $this->aiProviderManager->getProviderInfo($providerName);
            $models = $this->aiProviderManager->getAvailableModels($providerName);

            $providers[$providerName] = [
                'name' => $providerInfo['name'] ?? $providerName,
                'models' => $models,
                'features' => $this->getProviderFeatures($providerName),
            ];
        }

        return $providers;
    }

    /**
     * Get provider features.
     */
    protected function getProviderFeatures(string $provider): array
    {
        $aiProvider = $this->aiProviderManager->provider($provider);

        return [
            'vision' => $aiProvider->supportsFeature('vision'),
            'function_calling' => $aiProvider->supportsFeature('function_calling'),
            'streaming' => $aiProvider->supportsFeature('streaming'),
            'search' => $aiProvider->supportsFeature('search'),
        ];
    }

    /**
     * Get AI usage statistics.
     */
    public function getAIUsageStats(): array
    {
        return $this->aiProviderManager->getUsageStats();
    }

    /**
     * Get fallback response when AI is unavailable.
     */
    protected function getFallbackResponse(string $message, string $reason, string $language = 'en'): array
    {
        $templates = $this->getLocalizedTemplates($language);

        return [
            'response' => $templates['fallback'],
            'confidence' => 0.0,
            'intent' => 'fallback',
            'should_escalate' => true,
            'response_type' => 'fallback',
            'processing_time_ms' => 10,
            'escalation_reason' => $reason,
            'is_fallback' => true,
            'language' => $language,
        ];
    }

    /**
     * Calculate text similarity.
     */
    protected function calculateSimilarity(string $text1, string $text2): float
    {
        // Simple word-based similarity
        $words1 = str_word_count(strtolower($text1), 1);
        $words2 = str_word_count(strtolower($text2), 1);
        
        $intersection = array_intersect($words1, $words2);
        $union = array_unique(array_merge($words1, $words2));
        
        return count($union) > 0 ? count($intersection) / count($union) : 0;
    }

    /**
     * Detect simple intent based on keywords with priority scoring.
     */
    protected function detectSimpleIntent(string $message, string $language): ?string
    {
        $keywords = [
            // Highest priority intents (emotional/urgent)
            'frustration' => ['frustrated', 'angry', 'upset', 'disappointed', 'terrible', 'awful', 'horrible', 'worst', 'hate', 'annoyed'],
            // Higher priority intents (more specific)
            'pricing' => ['price', 'cost', 'how much', 'expensive', 'pricing', 'prys', 'intengo', 'pricing'],
            'business_hours' => ['business hours', 'opening hours', 'open', 'closed', 'what time', 'when open', 'ure', 'amahora'],
            'contact' => ['contact', 'phone', 'email', 'address', 'kontak', 'thinta'],
            'support' => ['help', 'problem', 'issue', 'support', 'hulp', 'inkinga'],
            'services' => ['services', 'what do you do', 'what can you help', 'dienste', 'izinsiza'],
            'thanks' => ['thank', 'thanks', 'appreciate', 'dankie', 'ngiyabonga', 'enkosi'],
            // Lower priority intents (more general)
            'greeting' => ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'hallo', 'sawubona', 'molo'],
        ];

        $message = strtolower($message);
        $scores = [];

        // Score each intent based on keyword matches
        foreach ($keywords as $intent => $words) {
            $score = 0;
            foreach ($words as $word) {
                if (strpos($message, $word) !== false) {
                    // Give higher score for longer, more specific phrases
                    $score += strlen($word);
                }
            }
            if ($score > 0) {
                $scores[$intent] = $score;
            }
        }

        // Return the intent with the highest score
        if (!empty($scores)) {
            arsort($scores);
            return array_key_first($scores);
        }

        return null;
    }

    /**
     * Get enhanced fallback response for common patterns.
     */
    protected function getEnhancedFallbackResponse(string $message, string $language): ?array
    {
        $message = strtolower(trim($message));

        // Handle very short messages
        if (strlen($message) <= 3) {
            $templates = $this->getLocalizedTemplates($language);
            return [
                'response' => $templates['greeting'],
                'confidence' => 0.6,
                'intent' => 'greeting',
                'should_escalate' => false,
                'response_type' => 'enhanced_fallback',
                'processing_time_ms' => 50,
            ];
        }

        // Handle questions (contains ?)
        if (strpos($message, '?') !== false) {
            $response = $this->getQuestionResponse($message, $language);
            if ($response) {
                return [
                    'response' => $response,
                    'confidence' => 0.65,
                    'intent' => 'question',
                    'should_escalate' => false,
                    'response_type' => 'enhanced_fallback',
                    'processing_time_ms' => 75,
                ];
            }
        }

        // Handle expressions of frustration (check before other patterns)
        $frustrationWords = ['frustrated', 'angry', 'upset', 'disappointed', 'terrible', 'awful', 'horrible', 'worst', 'hate', 'annoyed'];
        foreach ($frustrationWords as $word) {
            if (strpos($message, $word) !== false) {
                return [
                    'response' => $this->getEmpathyResponse($language),
                    'confidence' => 0.7,
                    'intent' => 'frustration',
                    'should_escalate' => true,
                    'response_type' => 'enhanced_fallback',
                    'processing_time_ms' => 60,
                ];
            }
        }

        return null;
    }

    /**
     * Get response for detected intent.
     */
    protected function getIntentResponse(string $intent, string $language): ?string
    {
        $templates = $this->getLocalizedTemplates($language);

        // Handle special intents
        if ($intent === 'frustration') {
            return $this->getEmpathyResponse($language);
        }

        return $templates[$intent] ?? null;
    }

    /**
     * Get response for questions.
     */
    protected function getQuestionResponse(string $message, string $language): ?string
    {
        $templates = $this->getLocalizedTemplates($language);

        // Check for specific question patterns
        if (strpos($message, 'how') !== false || strpos($message, 'what') !== false) {
            return $templates['services'] ?? "I'd be happy to help you with information about our services. Could you be more specific about what you're looking for?";
        }

        if (strpos($message, 'when') !== false || strpos($message, 'time') !== false) {
            return $templates['business_hours'];
        }

        if (strpos($message, 'where') !== false || strpos($message, 'contact') !== false) {
            return $templates['contact'];
        }

        return "That's a great question! Let me connect you with someone who can provide you with detailed information.";
    }

    /**
     * Get empathy response for frustrated users.
     */
    protected function getEmpathyResponse(string $language): string
    {
        $responses = [
            'en' => "I understand your frustration, and I'm sorry you're experiencing this issue. Let me connect you with a human agent who can provide immediate assistance.",
            'af' => "Ek verstaan jou frustrasie, en ek is jammer jy ervaar hierdie probleem. Laat ek jou met 'n menslike agent verbind wat onmiddellike hulp kan bied.",
            'es' => "Entiendo tu frustración y lamento que estés experimentando este problema. Permíteme conectarte con un agente humano que puede brindarte asistencia inmediata.",
            'zu' => "Ngiyaqonda ukuphoxeka kwakho, futhi ngiyaxolisa ukuthi ubhekene nale nkinga. Ake ngikuxhumanise nomuntu ozokusiza ngokushesha.",
            'xh' => "Ndiyayiqonda into yokuba ukhathazekile, kwaye ndiyaxolisa ukuba ujongene nale ngxaki. Makhe ndikudibanise nomntu onokukunceda ngokukhawuleza.",
        ];

        return $responses[$language] ?? $responses['en'];
    }

    /**
     * Build system prompt for OpenAI.
     */
    protected function buildSystemPrompt(string $language, array $context): string
    {
        $basePrompt = "You are a helpful customer service assistant for ChiSolution. ";
        $basePrompt .= "Respond in a friendly, professional manner. ";
        $basePrompt .= "Keep responses concise and helpful. ";
        
        if ($language !== 'en') {
            $languageNames = [
                'af' => 'Afrikaans',
                'zu' => 'isiZulu',
                'xh' => 'isiXhosa',
            ];
            $basePrompt .= "Respond in " . ($languageNames[$language] ?? 'English') . ". ";
        }

        return $basePrompt;
    }

    /**
     * Detect intent from message and response.
     */
    protected function detectIntent(string $message, string $response): string
    {
        // Simple intent detection - in production, use more sophisticated NLP
        return $this->detectSimpleIntent($message, 'en') ?? 'general_inquiry';
    }

    /**
     * Calculate confidence score.
     */
    protected function calculateConfidence(string $message, string $response, string $intent): float
    {
        // Simple confidence calculation - in production, use ML models
        $baseConfidence = 0.7;
        
        // Adjust based on response length
        if (strlen($response) > 50) {
            $baseConfidence += 0.1;
        }
        
        // Adjust based on intent detection
        if ($intent !== 'general_inquiry') {
            $baseConfidence += 0.1;
        }
        
        return min(1.0, $baseConfidence);
    }

    /**
     * Log AI conversation.
     */
    protected function logConversation(
        ChatRoom $room,
        string $userMessage,
        array $aiResponse,
        string $model,
        float $confidence,
        float $startTime,
        bool $escalated = false,
        ?string $escalationReason = null,
        ?int $chatMessageId = null
    ): void {
        $processingTime = (microtime(true) - $startTime) * 1000;

        // If no chat_message_id provided, try to get the latest message from this room
        if (!$chatMessageId) {
            $latestMessage = $room->messages()->latest()->first();
            $chatMessageId = $latestMessage?->id;
        }

        // Only log if we have a valid chat_message_id
        if ($chatMessageId) {
            AiConversationLog::create([
                'chat_room_id' => $room->id,
                'chat_message_id' => $chatMessageId,
                'user_message' => $userMessage,
                'ai_response' => $aiResponse['response'],
                'intent_detected' => $aiResponse['intent'],
                'confidence_score' => $confidence,
                'processing_time_ms' => $processingTime,
                'model_used' => $model,
                'escalated_to_human' => $escalated,
                'escalation_reason' => $escalationReason,
            ]);
        }

        // Log activity
        $this->activityLogger->log('ai_response_generated', null, [
            'room_id' => $room->id,
            'intent' => $aiResponse['intent'],
            'confidence' => $confidence,
            'processing_time_ms' => $processingTime,
            'model' => $model,
            'escalated' => $escalated,
            'chat_message_id' => $chatMessageId,
        ]);
    }

    /**
     * Analyze sentiment of a message.
     */
    public function analyzeSentiment(string $message): array
    {
        $cacheKey = 'sentiment_' . md5($message);

        return Cache::remember($cacheKey, 3600, function () use ($message) {
            // Simple rule-based sentiment analysis
            $positive_words = [
                'good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic',
                'love', 'like', 'happy', 'pleased', 'satisfied', 'thank', 'thanks'
            ];

            $negative_words = [
                'bad', 'terrible', 'awful', 'horrible', 'hate', 'angry', 'frustrated',
                'problem', 'issue', 'wrong', 'error', 'broken', 'disappointed', 'upset'
            ];

            $neutral_words = [
                'okay', 'fine', 'normal', 'average', 'standard', 'regular'
            ];

            $message_lower = strtolower($message);
            $words = str_word_count($message_lower, 1);

            $positive_score = 0;
            $negative_score = 0;
            $neutral_score = 0;

            foreach ($words as $word) {
                if (in_array($word, $positive_words)) {
                    $positive_score++;
                } elseif (in_array($word, $negative_words)) {
                    $negative_score++;
                } elseif (in_array($word, $neutral_words)) {
                    $neutral_score++;
                }
            }

            $total_sentiment_words = $positive_score + $negative_score + $neutral_score;

            if ($total_sentiment_words === 0) {
                return [
                    'sentiment' => 'neutral',
                    'confidence' => 0.5,
                    'scores' => [
                        'positive' => 0.33,
                        'negative' => 0.33,
                        'neutral' => 0.34
                    ]
                ];
            }

            $positive_ratio = $positive_score / $total_sentiment_words;
            $negative_ratio = $negative_score / $total_sentiment_words;

            if ($positive_ratio > $negative_ratio && $positive_ratio > 0.3) {
                $sentiment = 'positive';
                $confidence = min(0.9, 0.5 + $positive_ratio);
            } elseif ($negative_ratio > $positive_ratio && $negative_ratio > 0.3) {
                $sentiment = 'negative';
                $confidence = min(0.9, 0.5 + $negative_ratio);
            } else {
                $sentiment = 'neutral';
                $confidence = 0.6;
            }

            return [
                'sentiment' => $sentiment,
                'confidence' => round($confidence, 2),
                'scores' => [
                    'positive' => round($positive_ratio, 2),
                    'negative' => round($negative_ratio, 2),
                    'neutral' => round(1 - $positive_ratio - $negative_ratio, 2)
                ]
            ];
        });
    }

    /**
     * Generate conversation summary for a chat room.
     */
    public function summarizeConversation(ChatRoom $room, int $messageLimit = 50): array
    {
        $cacheKey = "conversation_summary_{$room->id}_" . $room->updated_at->timestamp;

        return Cache::remember($cacheKey, 1800, function () use ($room, $messageLimit) {
            // Get recent messages
            $messages = $room->messages()
                ->with('user')
                ->orderBy('created_at', 'desc')
                ->limit($messageLimit)
                ->get()
                ->reverse();

            if ($messages->isEmpty()) {
                return [
                    'summary' => 'No messages in this conversation.',
                    'key_points' => [],
                    'sentiment_overview' => 'neutral',
                    'participant_count' => 0,
                    'message_count' => 0,
                ];
            }

            $messageTexts = [];
            $sentiments = [];
            $participants = [];

            foreach ($messages as $message) {
                $messageTexts[] = $message->content;

                // Analyze sentiment for each message
                $sentiment = $this->analyzeSentiment($message->content);
                $sentiments[] = $sentiment['sentiment'];

                // Track participants
                if ($message->user) {
                    $participants[$message->user->id] = $message->user->name;
                }
            }

            // Generate summary using simple extraction
            $summary = $this->extractConversationSummary($messageTexts);

            // Calculate overall sentiment
            $sentimentCounts = array_count_values($sentiments);
            $overallSentiment = array_keys($sentimentCounts, max($sentimentCounts))[0];

            // Extract key points
            $keyPoints = $this->extractKeyPoints($messageTexts);

            return [
                'summary' => $summary,
                'key_points' => $keyPoints,
                'sentiment_overview' => $overallSentiment,
                'sentiment_distribution' => $sentimentCounts,
                'participant_count' => count($participants),
                'participants' => array_values($participants),
                'message_count' => $messages->count(),
                'conversation_duration' => $messages->last()->created_at->diffInMinutes($messages->first()->created_at),
            ];
        });
    }

    /**
     * Extract conversation summary from message texts.
     */
    protected function extractConversationSummary(array $messageTexts): string
    {
        if (empty($messageTexts)) {
            return 'No conversation content available.';
        }

        $allText = implode(' ', $messageTexts);
        $sentences = preg_split('/[.!?]+/', $allText, -1, PREG_SPLIT_NO_EMPTY);

        // Simple extractive summarization - take first and last meaningful sentences
        $meaningfulSentences = array_filter($sentences, function($sentence) {
            return strlen(trim($sentence)) > 20; // Filter out very short sentences
        });

        if (empty($meaningfulSentences)) {
            return 'Conversation contains mostly short exchanges.';
        }

        $summary = '';
        if (count($meaningfulSentences) >= 2) {
            $summary = trim($meaningfulSentences[0]) . '. ' . trim(end($meaningfulSentences)) . '.';
        } else {
            $summary = trim($meaningfulSentences[0]) . '.';
        }

        return $summary;
    }

    /**
     * Extract key points from conversation.
     */
    protected function extractKeyPoints(array $messageTexts): array
    {
        $keyWords = [
            'problem', 'issue', 'help', 'question', 'price', 'cost', 'when', 'how',
            'contact', 'email', 'phone', 'address', 'order', 'delivery', 'payment'
        ];

        $keyPoints = [];
        $allText = strtolower(implode(' ', $messageTexts));

        foreach ($keyWords as $keyword) {
            if (strpos($allText, $keyword) !== false) {
                $keyPoints[] = ucfirst($keyword) . ' mentioned';
            }
        }

        return array_slice($keyPoints, 0, 5); // Limit to 5 key points
    }

    /**
     * Smart routing based on query type and sentiment.
     */
    public function routeQuery(string $message, ChatRoom $room): array
    {
        $sentiment = $this->analyzeSentiment($message);
        $intent = $this->detectSimpleIntent($message, $room->language ?? 'en');

        $routing = [
            'recommended_action' => 'ai_response',
            'priority' => 'normal',
            'suggested_department' => 'general',
            'escalate_to_human' => false,
            'reasoning' => 'Standard AI response appropriate',
        ];

        // High priority routing for negative sentiment
        if ($sentiment['sentiment'] === 'negative' && $sentiment['confidence'] > 0.7) {
            $routing['priority'] = 'high';
            $routing['escalate_to_human'] = true;
            $routing['reasoning'] = 'Negative sentiment detected - human intervention recommended';
        }

        // Route based on detected intent
        switch ($intent) {
            case 'support':
                $routing['suggested_department'] = 'technical_support';
                $routing['priority'] = 'high';
                if ($sentiment['sentiment'] === 'negative') {
                    $routing['escalate_to_human'] = true;
                }
                break;

            case 'pricing':
                $routing['suggested_department'] = 'sales';
                $routing['recommended_action'] = 'sales_qualified_lead';
                break;

            case 'contact':
                $routing['suggested_department'] = 'customer_service';
                $routing['recommended_action'] = 'provide_contact_info';
                break;

            case 'business_hours':
                $routing['recommended_action'] = 'provide_hours_info';
                break;

            case 'greeting':
                $routing['recommended_action'] = 'friendly_greeting';
                $routing['priority'] = 'low';
                break;
        }

        // Check for escalation keywords
        $escalationKeywords = ['urgent', 'emergency', 'asap', 'immediately', 'manager', 'supervisor'];
        $messageLower = strtolower($message);

        foreach ($escalationKeywords as $keyword) {
            if (strpos($messageLower, $keyword) !== false) {
                $routing['escalate_to_human'] = true;
                $routing['priority'] = 'urgent';
                $routing['reasoning'] = "Escalation keyword '{$keyword}' detected";
                break;
            }
        }

        return array_merge($routing, [
            'sentiment' => $sentiment,
            'detected_intent' => $intent,
            'message_analysis' => [
                'length' => strlen($message),
                'word_count' => str_word_count($message),
                'has_question_marks' => substr_count($message, '?') > 0,
                'has_exclamation' => substr_count($message, '!') > 0,
            ]
        ]);
    }

    /**
     * Detect language of a message.
     */
    public function detectLanguage(string $message): string
    {
        $cacheKey = 'language_detection_' . md5($message);

        return Cache::remember($cacheKey, 1800, function () use ($message) {
            // Simple language detection based on common words
            $languagePatterns = [
                'en' => [
                    'words' => ['the', 'and', 'you', 'that', 'was', 'for', 'are', 'with', 'his', 'they'],
                    'patterns' => ['/\b(hello|hi|help|please|thank|thanks)\b/i']
                ],
                'af' => [
                    'words' => ['die', 'en', 'van', 'is', 'in', 'op', 'met', 'vir', 'wat', 'ek'],
                    'patterns' => ['/\b(hallo|dankie|help|asseblief)\b/i']
                ],
                'es' => [
                    'words' => ['el', 'la', 'de', 'que', 'y', 'a', 'en', 'un', 'es', 'se'],
                    'patterns' => ['/\b(hola|ayuda|por favor|gracias|buenos días)\b/i']
                ],
                'fr' => [
                    'words' => ['le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir'],
                    'patterns' => ['/\b(bonjour|salut|aide|s\'il vous plaît|merci)\b/i']
                ],
                'zu' => [
                    'words' => ['ukuthi', 'ngoba', 'uma', 'noma', 'futhi', 'kodwa', 'lapho', 'khona'],
                    'patterns' => ['/\b(sawubona|ngiyabonga|ngicela|usizo)\b/i']
                ],
                'xh' => [
                    'words' => ['ukuba', 'kuba', 'xa', 'okanye', 'kwaye', 'kodwa', 'apho', 'khona'],
                    'patterns' => ['/\b(molo|enkosi|nceda|uncedo)\b/i']
                ],
            ];

            $messageLower = strtolower($message);
            $words = str_word_count($messageLower, 1);
            $scores = [];

            foreach ($languagePatterns as $lang => $data) {
                $score = 0;

                // Check word matches
                foreach ($data['words'] as $word) {
                    if (in_array($word, $words)) {
                        $score += 2;
                    }
                }

                // Check pattern matches
                foreach ($data['patterns'] as $pattern) {
                    if (preg_match($pattern, $message)) {
                        $score += 5;
                    }
                }

                $scores[$lang] = $score;
            }

            // Return language with highest score, default to English
            $detectedLang = array_keys($scores, max($scores))[0] ?? 'en';

            // If no clear winner (all scores are 0), default to English
            if (max($scores) === 0) {
                return 'en';
            }

            return $detectedLang;
        });
    }

    /**
     * Get localized response templates.
     */
    public function getLocalizedTemplates(string $language = 'en'): array
    {
        $cacheKey = "chat_templates_{$language}";

        return Cache::remember($cacheKey, 3600, function () use ($language) {
            // Default English templates
            $templates = [
                'greeting' => [
                    'en' => "Hello! I'm here to help you. How can I assist you today?",
                    'af' => "Hallo! Ek is hier om jou te help. Hoe kan ek jou vandag bystaan?",
                    'es' => "¡Hola! Estoy aquí para ayudarte. ¿Cómo puedo asistirte hoy?",
                    'fr' => "Bonjour! Je suis là pour vous aider. Comment puis-je vous assister aujourd'hui?",
                    'zu' => "Sawubona! Ngilapha ukukukusiza. Ngingakusiza kanjani namuhla?",
                    'xh' => "Molo! Ndikho ukukukunceda. Ndingakunceda njani namhlanje?",
                ],
                'business_hours' => [
                    'en' => "Our business hours are Monday to Friday, 8:00 AM to 5:00 PM.",
                    'af' => "Ons besigheidsure is Maandag tot Vrydag, 08:00 tot 17:00.",
                    'es' => "Nuestro horario de atención es de lunes a viernes, de 8:00 AM a 5:00 PM.",
                    'fr' => "Nos heures d'ouverture sont du lundi au vendredi, de 8h00 à 17h00.",
                    'zu' => "Amahora ethu ebhizinisi aMahlanu kuya kuLwesihlanu, 8:00 AM kuya ku-5:00 PM.",
                    'xh' => "Iiyure zethu zoshishino ziMvulo ukuya kuLwesihlanu, 8:00 AM ukuya ku-5:00 PM.",
                ],
                'contact' => [
                    'en' => "You can contact <NAME_EMAIL> or call us at +**************.",
                    'af' => "Jy kan ons <NAME_EMAIL> of bel ons by +**************.",
                    'es' => "Puedes <NAME_EMAIL> o llamarnos al +**************.",
                    'fr' => "Vous pouvez nous contacter à <EMAIL> ou nous appeler au +**************.",
                    'zu' => "Ungasithinta <EMAIL> noma usishayele ku-+**************.",
                    'xh' => "Ungasiqhagamshelana <EMAIL> okanye usifowunele ku-+**************.",
                ],
                'services' => [
                    'en' => "We offer web development, mobile apps, digital marketing, and IT consulting services. How can we help with your project?",
                    'af' => "Ons bied webontwikkeling, mobiele toepassings, digitale bemarking en IT-konsultasiedienste aan. Hoe kan ons help met jou projek?",
                    'es' => "Ofrecemos desarrollo web, aplicaciones móviles, marketing digital y servicios de consultoría IT. ¿Cómo podemos ayudar con tu proyecto?",
                    'zu' => "Sinikeza ukuthuthukiswa kwewebhu, izinhlelo zokusebenza zeselula, ukumaketha kwedijithali, nezinsiza zokweluleka ze-IT. Singakusiza kanjani ngephrojekthi yakho?",
                    'xh' => "Sinika uphuhliso lwewebhu, usetyenziso lwefowuni, ukuthengisa kwedijithali, kunye neenkonzo zokucebisa ze-IT. Singakunceda njani ngeprojekthi yakho?",
                ],
                'thanks' => [
                    'en' => "You're welcome! Is there anything else I can help you with today?",
                    'af' => "Dis 'n plesier! Is daar enigiets anders waarmee ek jou vandag kan help?",
                    'es' => "¡De nada! ¿Hay algo más en lo que pueda ayudarte hoy?",
                    'zu' => "Wamukelekile! Ingabe kukhona okunye engingakusiza ngakho namuhla?",
                    'xh' => "Wamkelekile! Ingaba ikho enye into endinokukunceda ngayo namhlanje?",
                ],
                'fallback' => [
                    'en' => "I'm sorry, I didn't understand that. A human agent will assist you shortly.",
                    'af' => "Jammer, ek het dit nie verstaan nie. 'n Menslike agent sal jou binnekort help.",
                    'es' => "Lo siento, no entendí eso. Un agente humano te asistirá en breve.",
                    'fr' => "Je suis désolé, je n'ai pas compris. Un agent humain vous assistera sous peu.",
                    'zu' => "Ngiyaxolisa, angikuqondile lokho. Umuntu ozokusiza uzokuthinta maduze.",
                    'xh' => "Ndiyaxolisa, andikuqondanga oko. Umntu ozokunceda uza kukufikelela kungekudala.",
                ],
            ];

            $result = [];
            foreach ($templates as $key => $translations) {
                $result[$key] = $translations[$language] ?? $translations['en'];
            }

            return $result;
        });
    }

    /**
     * Check if user has exceeded AI request rate limit.
     */
    public function checkRateLimit(string $userId = null): bool
    {
        if (!$userId) {
            $userId = 'anonymous_' . request()->ip();
        }

        $cacheKey = "ai_rate_limit_{$userId}";
        $maxRequests = config('openai.rate_limit.max_requests', 60); // per hour
        $windowSeconds = config('openai.rate_limit.window_seconds', 3600); // 1 hour

        $requests = Cache::get($cacheKey, 0);

        if ($requests >= $maxRequests) {
            return false; // Rate limit exceeded
        }

        // Increment counter
        Cache::put($cacheKey, $requests + 1, $windowSeconds);

        return true; // Within rate limit
    }

    /**
     * Get AI performance metrics.
     */
    public function getPerformanceMetrics(): array
    {
        return Cache::remember('ai_performance_metrics', 300, function () {
            // Get recent conversation logs for metrics
            $recentLogs = AiConversationLog::where('created_at', '>=', now()->subHours(24))
                ->get();

            if ($recentLogs->isEmpty()) {
                return [
                    'average_response_time' => 0,
                    'total_requests' => 0,
                    'success_rate' => 100,
                    'cache_hit_rate' => 0,
                    'escalation_rate' => 0,
                ];
            }

            $totalRequests = $recentLogs->count();
            $successfulRequests = $recentLogs->where('escalated', false)->count();
            $escalatedRequests = $recentLogs->where('escalated', true)->count();
            $averageResponseTime = $recentLogs->avg('processing_time_ms');

            // Calculate cache hit rate (approximate)
            $cacheHits = $recentLogs->where('response_type', 'template')->count() +
                        $recentLogs->where('response_type', 'simple_nlp')->count();
            $cacheHitRate = $totalRequests > 0 ? ($cacheHits / $totalRequests) * 100 : 0;

            return [
                'average_response_time' => round($averageResponseTime, 2),
                'total_requests' => $totalRequests,
                'success_rate' => round(($successfulRequests / $totalRequests) * 100, 2),
                'cache_hit_rate' => round($cacheHitRate, 2),
                'escalation_rate' => round(($escalatedRequests / $totalRequests) * 100, 2),
            ];
        });
    }
}
