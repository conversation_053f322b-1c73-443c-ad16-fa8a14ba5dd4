<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Services Hero Section</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fontfaceobserver/2.1.0/fontfaceobserver.standalone.js"></script>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
            font-family: 'Roboto', sans-serif;
        }

        #canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .hero-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 10;
            color: #fff;
        }

        .hero-title {
            font-size: 3rem;
            font-weight: bold;
            letter-spacing: 2px;
            text-transform: uppercase;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5), 0 0 20px rgba(255, 255, 255, 0.7);
        }

        .hero-description {
            font-size: 1.2rem;
            margin-top: 20px;
            font-family: 'Arial', sans-serif;
            opacity: 0.8;
            letter-spacing: 1px;
        }

        .cta-button {
            margin-top: 40px;
            padding: 15px 30px;
            background-color: #00d4ff;
            color: #000;
            font-size: 1.1rem;
            border-radius: 30px;
            text-decoration: none;
            font-weight: bold;
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.7);
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            background-color: #00b8d8;
            box-shadow: 0 0 20px rgba(0, 184, 216, 1);
        }

        /* Loading animation */
        .loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #00d4ff;
            font-size: 2rem;
            z-index: 20;
        }
    </style>
</head>
<body>
    <div class="loading" id="loadingText">Loading...</div>
    <canvas id="canvas"></canvas>
    <div class="hero-content">
        <h1 class="hero-title">AI Solutions for the Future</h1>
        <p class="hero-description">Agentic Coding, Automation, Voice Control, Chatbots, and Generative AI Services</p>
        <a href="#services" class="cta-button">Explore Our Services</a>
    </div>

    <script>
        // 1. Three.js Setup
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({ canvas: document.getElementById('canvas') });
        renderer.setSize(window.innerWidth, window.innerHeight);

        // 2. Background grid (for a futuristic feel)
        const gridHelper = new THREE.GridHelper(200, 50);
        scene.add(gridHelper);

        // 3. 3D Text for the title (AI Solutions)
        const loader = new THREE.FontLoader();
        loader.load('https://threejs.org/examples/fonts/helvetiker_regular.typeface.json', (font) => {
            const textGeometry = new THREE.TextGeometry('AI Solutions', {
                font: font,
                size: 10,
                height: 2,
                curveSegments: 12,
                bevelEnabled: true,
                bevelThickness: 0.5,
                bevelSize: 0.5,
                bevelOffset: 0,
                bevelSegments: 3
            });
            const material = new THREE.MeshBasicMaterial({ color: 0x00d4ff });
            const textMesh = new THREE.Mesh(textGeometry, material);
            textMesh.position.set(-30, 30, 0);
            scene.add(textMesh);
        });

        // 4. Add some floating points to represent data
        const particles = new THREE.BufferGeometry();
        const particleCount = 5000;
        const positions = new Float32Array(particleCount * 3);

        for (let i = 0; i < particleCount; i++) {
            positions[i * 3] = Math.random() * 500 - 250;
            positions[i * 3 + 1] = Math.random() * 500 - 250;
            positions[i * 3 + 2] = Math.random() * 500 - 250;
        }

        particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));

        const particleMaterial = new THREE.PointsMaterial({ color: 0x00d4ff, size: 0.5 });
        const particleSystem = new THREE.Points(particles, particleMaterial);
        scene.add(particleSystem);

        // 5. Animation loop
        camera.position.z = 100;

        function animate() {
            requestAnimationFrame(animate);

            // Rotate grid for dynamic feel
            scene.rotation.y += 0.001;

            // Move particles slowly
            particleSystem.rotation.x += 0.001;
            particleSystem.rotation.y += 0.001;

            renderer.render(scene, camera);
        }
        animate();

        // 6. GSAP Animation for smooth interactions
        gsap.from('.hero-title', {
            opacity: 0,
            y: -50,
            duration: 2,
            delay: 1,
            ease: 'power3.out'
        });
        gsap.from('.hero-description', {
            opacity: 0,
            y: 50,
            duration: 2,
            delay: 1.5,
            ease: 'power3.out'
        });
        gsap.from('.cta-button', {
            opacity: 0,
            y: 50,
            duration: 2,
            delay: 2,
            ease: 'power3.out'
        });

        // 7. Font loading (FontFaceObserver)
        const customFont = new FontFaceObserver('Roboto');
        customFont.load().then(() => {
            document.body.style.fontFamily = 'Roboto, sans-serif';
            document.getElementById('loadingText').style.display = 'none'; // Hide loading text
        });

        // Resize handling for canvas and camera
        window.addEventListener('resize', () => {
            renderer.setSize(window.innerWidth, window.innerHeight);
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
        });
    </script>
</body>
</html>
