<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>ChiSolution Chat Integration Example</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .demo-section h3 {
            margin-top: 0;
            color: #333;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            overflow-x: auto;
        }
        .code-block pre {
            margin: 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
        .output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            min-height: 100px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-comments"></i> ChiSolution Chat API Integration Example</h1>
        <p>This page demonstrates how to integrate the ChiSolution Chat API into your application.</p>

        <!-- Basic Integration -->
        <div class="demo-section">
            <h3>1. Basic Chat Integration</h3>
            <p>Create a chat room and send messages using the JavaScript SDK.</p>
            
            <div class="code-block">
                <pre>// Initialize the SDK
const chat = new ChiChatSDK({
    baseUrl: '/api/v1/chat',
    token: 'your-auth-token'
});

// Create a chat room
const room = await chat.createRoom({
    type: 'visitor',
    visitor_info: {
        name: 'Demo User',
        page: window.location.href
    }
});</pre>
            </div>

            <button class="btn" onclick="createChatRoom()">Create Chat Room</button>
            <button class="btn btn-success" onclick="sendTestMessage()" id="sendMessageBtn" disabled>Send Test Message</button>
            <button class="btn btn-warning" onclick="connectWebSocket()" id="connectWsBtn" disabled>Connect WebSocket</button>
            
            <div id="basicOutput" class="output">Click "Create Chat Room" to start...</div>
        </div>

        <!-- Widget Integration -->
        <div class="demo-section">
            <h3>2. Chat Widget Integration</h3>
            <p>Embed a complete chat widget in your website with just a few lines of code.</p>
            
            <div class="code-block">
                <pre>// Create and initialize chat widget
const chatWidget = chat.createWidget({
    container: 'body',
    theme: 'default',
    position: 'bottom-right',
    autoOpen: false
});

// The widget will appear in the bottom-right corner</pre>
            </div>

            <button class="btn" onclick="createChatWidget()">Create Chat Widget</button>
            <button class="btn btn-danger" onclick="removeChatWidget()">Remove Widget</button>
            
            <div id="widgetOutput" class="output">Widget will appear in bottom-right corner when created.</div>
        </div>

        <!-- File Upload -->
        <div class="demo-section">
            <h3>3. File Upload Integration</h3>
            <p>Upload files to chat rooms with progress tracking.</p>
            
            <div class="code-block">
                <pre>// Upload file to chat room
const fileInput = document.getElementById('fileInput');
const file = fileInput.files[0];

const result = await chat.uploadFile(roomUuid, file, 'Here is the file you requested');
console.log('File uploaded:', result);</pre>
            </div>

            <input type="file" id="fileInput" accept="image/*,.pdf,.doc,.docx">
            <button class="btn" onclick="uploadFile()" id="uploadBtn" disabled>Upload File</button>
            
            <div id="uploadOutput" class="output">Select a file and create a chat room first.</div>
        </div>

        <!-- Real-time Features -->
        <div class="demo-section">
            <h3>4. Real-time Features</h3>
            <p>Demonstrate typing indicators and real-time message updates.</p>
            
            <div class="code-block">
                <pre>// Listen for real-time events
chat.on('websocket:message', (data) => {
    console.log('New message received:', data.message);
});

chat.on('websocket:typing', (data) => {
    console.log('User typing:', data.user_name);
});

// Send typing indicator
await chat.sendTyping(roomUuid, true);</pre>
            </div>

            <button class="btn" onclick="sendTyping()" id="typingBtn" disabled>Send Typing Indicator</button>
            <button class="btn btn-success" onclick="markAsRead()" id="readBtn" disabled>Mark Last Message as Read</button>
            
            <div id="realtimeOutput" class="output">Connect WebSocket first to see real-time events.</div>
        </div>

        <!-- Analytics -->
        <div class="demo-section">
            <h3>5. Analytics Integration</h3>
            <p>Retrieve chat statistics and analytics data.</p>
            
            <div class="code-block">
                <pre>// Get chat statistics
const stats = await chat.getStatistics();
console.log('Chat statistics:', stats);

// Get analytics data
const analytics = await chat.getAnalytics({
    start_date: '2024-01-01',
    end_date: '2024-01-31',
    metric: 'response_time'
});</pre>
            </div>

            <button class="btn" onclick="getStatistics()">Get Statistics</button>
            
            <div id="analyticsOutput" class="output">Click "Get Statistics" to retrieve analytics data.</div>
        </div>

        <!-- Status Display -->
        <div class="demo-section">
            <h3>Current Status</h3>
            <div id="statusDisplay">
                <span class="status info">Ready to start</span>
            </div>
        </div>
    </div>

    <!-- Include the Chat SDK -->
    <script src="/js/chat-sdk.js"></script>
    
    <script>
        // Global variables
        let chat = null;
        let currentRoom = null;
        let chatWidget = null;
        let lastMessage = null;

        // Initialize SDK
        function initializeSDK() {
            chat = new ChiChatSDK({
                baseUrl: '/api/v1/chat',
                timeout: 30000
            });

            updateStatus('SDK initialized', 'success');
        }

        // Create chat room
        async function createChatRoom() {
            try {
                updateStatus('Creating chat room...', 'info');
                
                const response = await chat.createRoom({
                    type: 'visitor',
                    visitor_info: {
                        name: 'Demo User',
                        email: '<EMAIL>',
                        page: window.location.href
                    },
                    metadata: {
                        demo: true,
                        timestamp: new Date().toISOString()
                    }
                });

                currentRoom = response.data.room;
                document.getElementById('basicOutput').textContent = 
                    `Chat room created successfully!\n\nRoom UUID: ${currentRoom.uuid}\nType: ${currentRoom.type}\nStatus: ${currentRoom.status}`;

                // Enable other buttons
                document.getElementById('sendMessageBtn').disabled = false;
                document.getElementById('connectWsBtn').disabled = false;
                document.getElementById('uploadBtn').disabled = false;

                updateStatus(`Room created: ${currentRoom.uuid}`, 'success');
            } catch (error) {
                document.getElementById('basicOutput').textContent = `Error: ${error.message}`;
                updateStatus('Failed to create room', 'error');
            }
        }

        // Send test message
        async function sendTestMessage() {
            if (!currentRoom) return;

            try {
                const response = await chat.sendMessage(currentRoom.uuid, {
                    content: `Test message sent at ${new Date().toLocaleTimeString()}`,
                    message_type: 'text'
                });

                lastMessage = response.data.message;
                document.getElementById('basicOutput').textContent += 
                    `\n\nMessage sent successfully!\nMessage ID: ${lastMessage.uuid}\nContent: ${lastMessage.content}`;

                document.getElementById('readBtn').disabled = false;
                updateStatus('Message sent', 'success');
            } catch (error) {
                document.getElementById('basicOutput').textContent += `\n\nError sending message: ${error.message}`;
                updateStatus('Failed to send message', 'error');
            }
        }

        // Connect WebSocket
        function connectWebSocket() {
            if (!currentRoom) return;

            try {
                chat.connectWebSocket(currentRoom.uuid);

                // Listen for events
                chat.on('websocket:connected', () => {
                    document.getElementById('realtimeOutput').textContent = 'WebSocket connected successfully!';
                    document.getElementById('typingBtn').disabled = false;
                    updateStatus('WebSocket connected', 'success');
                });

                chat.on('websocket:message', (data) => {
                    document.getElementById('realtimeOutput').textContent += 
                        `\n\nNew message received:\n${JSON.stringify(data, null, 2)}`;
                });

                chat.on('websocket:typing', (data) => {
                    document.getElementById('realtimeOutput').textContent += 
                        `\n\nTyping indicator: ${data.user_name} is ${data.is_typing ? 'typing' : 'not typing'}`;
                });

                chat.on('websocket:error', (data) => {
                    document.getElementById('realtimeOutput').textContent += 
                        `\n\nWebSocket error: ${data.error}`;
                    updateStatus('WebSocket error', 'error');
                });

            } catch (error) {
                document.getElementById('realtimeOutput').textContent = `Error connecting WebSocket: ${error.message}`;
                updateStatus('WebSocket connection failed', 'error');
            }
        }

        // Create chat widget
        function createChatWidget() {
            if (!chat) {
                initializeSDK();
            }

            try {
                chatWidget = chat.createWidget({
                    container: 'body',
                    theme: 'default',
                    position: 'bottom-right',
                    autoOpen: false
                });

                document.getElementById('widgetOutput').textContent = 
                    'Chat widget created! Look for the chat icon in the bottom-right corner.';
                updateStatus('Widget created', 'success');
            } catch (error) {
                document.getElementById('widgetOutput').textContent = `Error creating widget: ${error.message}`;
                updateStatus('Widget creation failed', 'error');
            }
        }

        // Remove chat widget
        function removeChatWidget() {
            const widget = document.getElementById('chi-chat-widget');
            if (widget) {
                widget.remove();
                document.getElementById('widgetOutput').textContent = 'Chat widget removed.';
                updateStatus('Widget removed', 'info');
            }
        }

        // Upload file
        async function uploadFile() {
            if (!currentRoom) return;

            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];

            if (!file) {
                document.getElementById('uploadOutput').textContent = 'Please select a file first.';
                return;
            }

            try {
                updateStatus('Uploading file...', 'info');
                
                const response = await chat.uploadFile(currentRoom.uuid, file, 'Demo file upload');
                
                document.getElementById('uploadOutput').textContent = 
                    `File uploaded successfully!\n\nFile: ${file.name}\nSize: ${file.size} bytes\nResponse: ${JSON.stringify(response, null, 2)}`;
                
                updateStatus('File uploaded', 'success');
            } catch (error) {
                document.getElementById('uploadOutput').textContent = `Error uploading file: ${error.message}`;
                updateStatus('File upload failed', 'error');
            }
        }

        // Send typing indicator
        async function sendTyping() {
            if (!currentRoom) return;

            try {
                await chat.sendTyping(currentRoom.uuid, true);
                document.getElementById('realtimeOutput').textContent += '\n\nTyping indicator sent (typing: true)';
                
                // Send "not typing" after 3 seconds
                setTimeout(async () => {
                    await chat.sendTyping(currentRoom.uuid, false);
                    document.getElementById('realtimeOutput').textContent += '\nTyping indicator sent (typing: false)';
                }, 3000);

                updateStatus('Typing indicator sent', 'success');
            } catch (error) {
                document.getElementById('realtimeOutput').textContent += `\n\nError sending typing: ${error.message}`;
                updateStatus('Typing indicator failed', 'error');
            }
        }

        // Mark message as read
        async function markAsRead() {
            if (!currentRoom || !lastMessage) return;

            try {
                await chat.markAsRead(currentRoom.uuid, lastMessage.uuid);
                document.getElementById('realtimeOutput').textContent += 
                    `\n\nMessage marked as read: ${lastMessage.uuid}`;
                updateStatus('Message marked as read', 'success');
            } catch (error) {
                document.getElementById('realtimeOutput').textContent += 
                    `\n\nError marking as read: ${error.message}`;
                updateStatus('Mark as read failed', 'error');
            }
        }

        // Get statistics
        async function getStatistics() {
            try {
                updateStatus('Fetching statistics...', 'info');
                
                const stats = await chat.getStatistics();
                
                document.getElementById('analyticsOutput').textContent = 
                    `Statistics retrieved successfully!\n\n${JSON.stringify(stats, null, 2)}`;
                
                updateStatus('Statistics retrieved', 'success');
            } catch (error) {
                document.getElementById('analyticsOutput').textContent = `Error getting statistics: ${error.message}`;
                updateStatus('Statistics retrieval failed', 'error');
            }
        }

        // Update status display
        function updateStatus(message, type) {
            const statusDisplay = document.getElementById('statusDisplay');
            statusDisplay.innerHTML = `<span class="status ${type}">${message}</span>`;
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeSDK();
        });
    </script>
</body>
</html>
