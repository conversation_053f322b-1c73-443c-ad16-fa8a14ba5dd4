<?php

namespace Database\Factories;

use App\Models\AiConversationLog;
use App\Models\ChatRoom;
use App\Models\ChatMessage;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AiConversationLog>
 */
class AiConversationLogFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = AiConversationLog::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'chat_room_id' => 1, // We'll create this in tests
            'chat_message_id' => 1, // We'll create this in tests
            'user_message' => $this->faker->sentence(),
            'ai_response' => $this->faker->paragraph(),
            'intent_detected' => $this->faker->randomElement(['support', 'billing', 'technical', 'general', 'pricing']),
            'confidence_score' => $this->faker->randomFloat(2, 0.1, 1.0),
            'processing_time_ms' => $this->faker->numberBetween(100, 5000),
            'model_used' => $this->faker->randomElement(['gpt-4', 'gpt-3.5-turbo', 'claude-3']),
            'was_helpful' => $this->faker->boolean(70), // 70% chance of being helpful
            'escalated_to_human' => $this->faker->boolean(20), // 20% chance of escalation
            'escalation_reason' => $this->faker->optional(0.2)->randomElement([
                'low_confidence',
                'complex_query',
                'user_request',
                'technical_issue',
                'billing_dispute'
            ]),
        ];
    }

    /**
     * Indicate that the AI response was helpful.
     */
    public function helpful(): static
    {
        return $this->state(fn (array $attributes) => [
            'was_helpful' => true,
            'escalated_to_human' => false,
            'escalation_reason' => null,
        ]);
    }

    /**
     * Indicate that the AI response was unhelpful.
     */
    public function unhelpful(): static
    {
        return $this->state(fn (array $attributes) => [
            'was_helpful' => false,
        ]);
    }

    /**
     * Indicate that the conversation was escalated to human.
     */
    public function escalated(): static
    {
        return $this->state(fn (array $attributes) => [
            'escalated_to_human' => true,
            'escalation_reason' => $this->faker->randomElement([
                'low_confidence',
                'complex_query',
                'user_request',
                'technical_issue',
                'billing_dispute'
            ]),
        ]);
    }

    /**
     * Indicate high confidence response.
     */
    public function highConfidence(): static
    {
        return $this->state(fn (array $attributes) => [
            'confidence_score' => $this->faker->randomFloat(2, 0.8, 1.0),
            'escalated_to_human' => false,
        ]);
    }

    /**
     * Indicate low confidence response.
     */
    public function lowConfidence(): static
    {
        return $this->state(fn (array $attributes) => [
            'confidence_score' => $this->faker->randomFloat(2, 0.1, 0.5),
            'escalated_to_human' => $this->faker->boolean(60), // Higher chance of escalation
        ]);
    }

    /**
     * Indicate fast response.
     */
    public function fastResponse(): static
    {
        return $this->state(fn (array $attributes) => [
            'processing_time_ms' => $this->faker->numberBetween(100, 1000),
        ]);
    }

    /**
     * Indicate slow response.
     */
    public function slowResponse(): static
    {
        return $this->state(fn (array $attributes) => [
            'processing_time_ms' => $this->faker->numberBetween(3000, 10000),
        ]);
    }

    /**
     * Set specific model.
     */
    public function model(string $model): static
    {
        return $this->state(fn (array $attributes) => [
            'model_used' => $model,
        ]);
    }

    /**
     * Set specific intent.
     */
    public function intent(string $intent): static
    {
        return $this->state(fn (array $attributes) => [
            'intent_detected' => $intent,
        ]);
    }

    /**
     * Set specific confidence score.
     */
    public function confidence(float $score): static
    {
        return $this->state(fn (array $attributes) => [
            'confidence_score' => $score,
        ]);
    }

    /**
     * Set specific processing time.
     */
    public function processingTime(int $ms): static
    {
        return $this->state(fn (array $attributes) => [
            'processing_time_ms' => $ms,
        ]);
    }
}
