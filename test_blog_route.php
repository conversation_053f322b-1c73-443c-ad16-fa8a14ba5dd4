<?php

/**
 * Test script to verify blog post route model binding
 * 
 * Run with: php test_blog_route.php
 */

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// Test the route
$request = Illuminate\Http\Request::create('/en/blog/artificial-intelligence-in-web-development', 'GET');

try {
    $response = $kernel->handle($request);
    
    echo "✅ SUCCESS! Route handled without errors.\n";
    echo "Status Code: " . $response->getStatusCode() . "\n";
    
    if ($response->getStatusCode() === 200) {
        echo "✅ Page loaded successfully!\n";
    } else {
        echo "⚠️  Unexpected status code: " . $response->getStatusCode() . "\n";
    }
    
} catch (\TypeError $e) {
    echo "❌ FAILED! TypeError occurred:\n";
    echo $e->getMessage() . "\n";
    echo "\nFile: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
} catch (\Exception $e) {
    echo "❌ FAILED! Exception occurred:\n";
    echo $e->getMessage() . "\n";
    echo "\nFile: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

$kernel->terminate($request, $response ?? null);

