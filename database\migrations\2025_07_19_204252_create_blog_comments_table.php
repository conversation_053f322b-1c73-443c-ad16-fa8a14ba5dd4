<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('blog_comments', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();

            // Relationships
            $table->foreignId('blog_post_id')->constrained('blog_posts')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('parent_comment_id')->nullable()->constrained('blog_comments')->onDelete('cascade');

            // Comment content
            $table->text('content');
            $table->tinyInteger('rating')->nullable()->comment('1-5 star rating, null if no rating');

            // File attachments
            $table->json('attachments')->nullable()->comment('Array of uploaded file paths');

            // Moderation
            $table->boolean('is_approved')->default(false);
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->text('admin_notes')->nullable()->comment('Internal admin notes about the comment');

            // Soft delete
            $table->boolean('is_deleted')->default(false);
            $table->foreignId('deleted_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('deleted_at')->nullable();

            // Engagement tracking
            $table->unsignedInteger('helpful_count')->default(0)->comment('Number of users who found this helpful');
            $table->json('helpful_users')->nullable()->comment('Array of user IDs who marked as helpful');

            // Spam detection
            $table->boolean('is_flagged')->default(false);
            $table->unsignedInteger('flag_count')->default(0);
            $table->json('flagged_by')->nullable()->comment('Array of user IDs who flagged this comment');

            $table->timestamps();

            // Indexes for performance
            $table->index(['blog_post_id', 'is_approved', 'is_deleted']);
            $table->index(['user_id', 'created_at']);
            $table->index(['parent_comment_id']);
            $table->index(['is_approved', 'created_at']);
            $table->index(['rating']);
            $table->index(['is_flagged', 'flag_count']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('blog_comments');
    }
};
