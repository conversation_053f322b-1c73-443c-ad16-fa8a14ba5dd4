@extends('layouts.dashboard')

@section('title', 'Email Analytics')

@section('content')
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Email Analytics</h1>
            <p class="mt-1 text-sm text-gray-600">Comprehensive insights into your email marketing performance</p>
        </div>
        <div class="mt-4 sm:mt-0 flex items-center space-x-3">
            <div class="flex items-center space-x-2">
                <input type="date" 
                       id="date_from" 
                       value="{{ $dateRange[0]->format('Y-m-d') }}"
                       class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                <span class="text-gray-500">to</span>
                <input type="date" 
                       id="date_to" 
                       value="{{ $dateRange[1]->format('Y-m-d') }}"
                       class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                <button id="apply-date-filter" 
                        class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200">
                    Apply
                </button>
            </div>
            <div class="relative">
                <button id="export-dropdown-btn" 
                        class="inline-flex items-center px-4 py-2 bg-secondary-600 text-white rounded-lg hover:bg-secondary-700 transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    Export
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                    </svg>
                </button>
                <div id="export-dropdown" class="hidden absolute right-0 z-10 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200">
                    <div class="py-1">
                        <a href="#" class="export-link block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" data-type="campaigns">Campaign Data</a>
                        <a href="#" class="export-link block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" data-type="subscribers">Subscriber Data</a>
                        <a href="#" class="export-link block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" data-type="engagement">Engagement Data</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Overall Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Campaigns Sent -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Emails Sent</p>
                    <p class="text-2xl font-bold text-gray-900">{{ number_format($overallStats['campaigns']['sent']) }}</p>
                </div>
            </div>
        </div>

        <!-- Open Rate -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Open Rate</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $overallStats['rates']['open'] }}%</p>
                </div>
            </div>
        </div>

        <!-- Click Rate -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"/>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Click Rate</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $overallStats['rates']['click'] }}%</p>
                </div>
            </div>
        </div>

        <!-- Active Subscribers -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active Subscribers</p>
                    <p class="text-2xl font-bold text-gray-900">{{ number_format($overallStats['subscribers']['active']) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Campaign Performance Chart -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Campaign Performance</h3>
                <div class="flex items-center space-x-2">
                    <select id="performance-metric" class="text-sm border border-gray-300 rounded-lg px-3 py-1 focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="sent">Emails Sent</option>
                        <option value="opened">Emails Opened</option>
                        <option value="clicked">Emails Clicked</option>
                        <option value="bounced">Emails Bounced</option>
                    </select>
                </div>
            </div>
            <div class="h-80">
                <canvas id="performance-chart"></canvas>
            </div>
        </div>

        <!-- Engagement Metrics Chart -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Engagement Rates</h3>
                <div class="flex items-center space-x-2">
                    <div class="flex items-center space-x-1">
                        <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                        <span class="text-xs text-gray-600">Open Rate</span>
                    </div>
                    <div class="flex items-center space-x-1">
                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span class="text-xs text-gray-600">Click Rate</span>
                    </div>
                </div>
            </div>
            <div class="h-80">
                <canvas id="engagement-chart"></canvas>
            </div>
        </div>
    </div>

    <!-- Detailed Analytics Row -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Top Performing Campaigns -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Performing Campaigns</h3>
            <div class="space-y-4">
                @forelse($topCampaigns as $campaign)
                    <div class="flex items-center justify-between">
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 truncate">{{ $campaign->name }}</p>
                            <p class="text-xs text-gray-500">{{ $campaign->type_name }}</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-medium text-gray-900">{{ $campaign->getOpenRate() }}%</p>
                            <p class="text-xs text-gray-500">{{ number_format($campaign->emails_sent) }} sent</p>
                        </div>
                    </div>
                @empty
                    <p class="text-sm text-gray-500">No campaigns found for this period.</p>
                @endforelse
            </div>
        </div>

        <!-- Subscriber Growth -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Subscriber Growth</h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">New Subscribers</span>
                    <span class="text-sm font-medium text-green-600">+{{ number_format($overallStats['subscribers']['new']) }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Total Active</span>
                    <span class="text-sm font-medium text-gray-900">{{ number_format($overallStats['subscribers']['active']) }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Unsubscribes</span>
                    <span class="text-sm font-medium text-red-600">{{ number_format($overallStats['campaigns']['unsubscribes']) }}</span>
                </div>
                <div class="h-32 mt-4">
                    <canvas id="subscriber-growth-chart"></canvas>
                </div>
            </div>
        </div>

        <!-- Deliverability Stats -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Deliverability</h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Delivery Rate</span>
                    <span class="text-sm font-medium text-green-600">{{ $overallStats['rates']['delivery'] }}%</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Bounce Rate</span>
                    <span class="text-sm font-medium text-red-600">{{ $overallStats['rates']['bounce'] }}%</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Delivered</span>
                    <span class="text-sm font-medium text-gray-900">{{ number_format($overallStats['campaigns']['delivered']) }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Bounced</span>
                    <span class="text-sm font-medium text-gray-900">{{ number_format($overallStats['campaigns']['bounced']) }}</span>
                </div>
                
                <!-- Deliverability Donut Chart -->
                <div class="h-32 mt-4">
                    <canvas id="deliverability-chart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Campaigns Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Recent Campaigns</h3>
        </div>
        @if($recentCampaigns->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Campaign</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sent</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Open Rate</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Click Rate</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($recentCampaigns as $campaign)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4">
                                    <div class="text-sm font-medium text-gray-900">{{ $campaign->name }}</div>
                                    @if($campaign->emailTemplate)
                                        <div class="text-sm text-gray-500">{{ $campaign->emailTemplate->name }}</div>
                                    @endif
                                </td>
                                <td class="px-6 py-4">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ $campaign->type_name }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-900">
                                    {{ number_format($campaign->emails_sent) }}
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-900">
                                    {{ $campaign->getOpenRate() }}%
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-900">
                                    {{ $campaign->getClickRate() }}%
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500">
                                    {{ $campaign->created_at->format('M j, Y') }}
                                </td>
                                <td class="px-6 py-4">
                                    <a href="{{ route('admin.email-campaigns.show', $campaign) }}" 
                                       class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                                        View Details
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No campaigns found</h3>
                <p class="mt-1 text-sm text-gray-500">No campaigns were created in the selected date range.</p>
            </div>
        @endif
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Date filter functionality
    document.getElementById('apply-date-filter').addEventListener('click', function() {
        const dateFrom = document.getElementById('date_from').value;
        const dateTo = document.getElementById('date_to').value;
        
        const url = new URL(window.location);
        url.searchParams.set('date_from', dateFrom);
        url.searchParams.set('date_to', dateTo);
        window.location.href = url.toString();
    });

    // Export dropdown functionality
    const exportBtn = document.getElementById('export-dropdown-btn');
    const exportDropdown = document.getElementById('export-dropdown');
    
    exportBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        exportDropdown.classList.toggle('hidden');
    });

    document.addEventListener('click', function() {
        exportDropdown.classList.add('hidden');
    });

    // Export links
    document.querySelectorAll('.export-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const type = this.dataset.type;
            const dateFrom = document.getElementById('date_from').value;
            const dateTo = document.getElementById('date_to').value;
            
            const url = new URL('{{ route("admin.email-analytics.export") }}', window.location.origin);
            url.searchParams.set('type', type);
            url.searchParams.set('date_from', dateFrom);
            url.searchParams.set('date_to', dateTo);
            
            window.open(url.toString(), '_blank');
            exportDropdown.classList.add('hidden');
        });
    });

    // Initialize charts
    initializeCharts();
});

function initializeCharts() {
    // Campaign Performance Chart
    const performanceCtx = document.getElementById('performance-chart').getContext('2d');
    const performanceChart = new Chart(performanceCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Emails Sent',
                data: [],
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Engagement Chart
    const engagementCtx = document.getElementById('engagement-chart').getContext('2d');
    const engagementChart = new Chart(engagementCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'Open Rate %',
                    data: [],
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4
                },
                {
                    label: 'Click Rate %',
                    data: [],
                    borderColor: 'rgb(34, 197, 94)',
                    backgroundColor: 'rgba(34, 197, 94, 0.1)',
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });

    // Subscriber Growth Chart
    const subscriberCtx = document.getElementById('subscriber-growth-chart').getContext('2d');
    const subscriberChart = new Chart(subscriberCtx, {
        type: 'bar',
        data: {
            labels: @json(array_column($subscriberGrowth, 'date')),
            datasets: [{
                label: 'Net Growth',
                data: @json(array_column($subscriberGrowth, 'net_growth')),
                backgroundColor: 'rgba(34, 197, 94, 0.8)',
                borderColor: 'rgb(34, 197, 94)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Deliverability Chart
    const deliverabilityCtx = document.getElementById('deliverability-chart').getContext('2d');
    const deliverabilityChart = new Chart(deliverabilityCtx, {
        type: 'doughnut',
        data: {
            labels: ['Delivered', 'Bounced'],
            datasets: [{
                data: [
                    {{ $overallStats['campaigns']['delivered'] }},
                    {{ $overallStats['campaigns']['bounced'] }}
                ],
                backgroundColor: [
                    'rgba(34, 197, 94, 0.8)',
                    'rgba(239, 68, 68, 0.8)'
                ],
                borderColor: [
                    'rgb(34, 197, 94)',
                    'rgb(239, 68, 68)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Load chart data
    loadChartData();
}

function loadChartData() {
    const dateFrom = document.getElementById('date_from').value;
    const dateTo = document.getElementById('date_to').value;

    // Load engagement metrics
    fetch(`{{ route('admin.email-analytics.engagement-metrics') }}?date_from=${dateFrom}&date_to=${dateTo}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateEngagementChart(data.data);
            }
        })
        .catch(error => console.error('Error loading engagement data:', error));

    // Load campaign performance
    fetch(`{{ route('admin.email-analytics.campaign-performance') }}?date_from=${dateFrom}&date_to=${dateTo}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updatePerformanceChart(data.data);
            }
        })
        .catch(error => console.error('Error loading performance data:', error));
}

function updateEngagementChart(data) {
    const chart = Chart.getChart('engagement-chart');
    chart.data.labels = data.map(item => item.date);
    chart.data.datasets[0].data = data.map(item => item.open_rate);
    chart.data.datasets[1].data = data.map(item => item.click_rate);
    chart.update();
}

function updatePerformanceChart(data) {
    const chart = Chart.getChart('performance-chart');
    chart.data.labels = data.map(item => item.date);
    chart.data.datasets[0].data = data.map(item => item.sent);
    chart.update();
}
</script>
@endpush
@endsection
