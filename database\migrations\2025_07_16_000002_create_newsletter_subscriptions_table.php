<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('newsletter_subscriptions', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            
            // Subscriber Information
            $table->string('email')->index();
            $table->string('name')->nullable();
            
            // Subscription Status
            $table->boolean('is_active')->default(true);
            $table->timestamp('subscribed_at')->nullable();
            $table->timestamp('unsubscribed_at')->nullable();
            $table->timestamp('resubscribed_at')->nullable();
            
            // Email Verification
            $table->timestamp('email_verified_at')->nullable();
            $table->string('verification_token')->nullable();
            
            // Tracking Information
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->text('referrer')->nullable();
            
            // UTM Tracking
            $table->string('utm_source')->nullable();
            $table->string('utm_medium')->nullable();
            $table->string('utm_campaign')->nullable();
            
            // Unsubscribe Tracking
            $table->string('unsubscribe_ip', 45)->nullable();
            $table->text('unsubscribe_user_agent')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['email', 'is_active']);
            $table->index(['is_active', 'created_at']);
            $table->index(['subscribed_at']);
            $table->index(['unsubscribed_at']);
            $table->index('uuid');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('newsletter_subscriptions');
    }
};
