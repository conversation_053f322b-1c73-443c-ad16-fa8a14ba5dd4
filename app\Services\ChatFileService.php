<?php

namespace App\Services;

use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\ChatFile;
use App\Models\User;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ChatFileService
{
    protected FileService $fileService;
    protected ImageService $imageService;
    protected ActivityLogger $activityLogger;

    public function __construct(
        FileService $fileService,
        ImageService $imageService,
        ActivityLogger $activityLogger
    ) {
        $this->fileService = $fileService;
        $this->imageService = $imageService;
        $this->activityLogger = $activityLogger;
    }

    /**
     * Upload file to chat room.
     */
    public function uploadFile(
        ChatRoom $room,
        ChatMessage $message,
        UploadedFile $file,
        ?User $uploader = null
    ): ChatFile {
        return DB::transaction(function () use ($room, $message, $file, $uploader) {
            // Validate file using existing FileService
            $this->validateFile($file);

            // Generate unique filename
            $storedFilename = $this->generateUniqueFilename($file);
            $filePath = "chat/{$room->uuid}/{$storedFilename}";

            // Check if it's an image
            $isImage = $this->isImageFile($file);

            // Process file based on type
            if ($isImage) {
                $processedFile = $this->processImage($file, $filePath);
            } else {
                $processedFile = $this->processRegularFile($file, $filePath);
            }

            // Calculate file hash for deduplication
            $fileHash = hash_file('sha256', $file->getRealPath());

            // Create chat file record
            $chatFile = ChatFile::create([
                'chat_room_id' => $room->id,
                'chat_message_id' => $message->id,
                'uploaded_by' => $uploader?->id ?? auth()->id(),
                'original_filename' => $file->getClientOriginalName(),
                'stored_filename' => $storedFilename,
                'file_path' => $filePath,
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'file_hash' => $fileHash,
                'is_image' => $isImage,
                'expires_at' => $this->calculateExpiryDate(),
            ]);

            // Schedule virus scan using existing FileService
            $this->scheduleVirusScan($chatFile);

            // Log activity
            $this->activityLogger->log('chat_file_uploaded', $chatFile, [
                'room_id' => $room->id,
                'message_id' => $message->id,
                'filename' => $file->getClientOriginalName(),
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'is_image' => $isImage,
            ]);

            return $chatFile;
        });
    }

    /**
     * Process image file using ImageService.
     */
    protected function processImage(UploadedFile $file, string $filePath): array
    {
        // Use existing ImageService for processing
        $processedImage = $this->imageService->processUpload($file, [
            'path' => $filePath,
            'optimize' => true,
            'generate_thumbnails' => true,
            'max_width' => 1920,
            'max_height' => 1080,
            'quality' => 85,
        ]);

        return $processedImage;
    }

    /**
     * Process regular file using FileService.
     */
    protected function processRegularFile(UploadedFile $file, string $filePath): array
    {
        // Use existing FileService for processing
        $processedFile = $this->fileService->processUpload($file, [
            'path' => $filePath,
            'scan_virus' => true,
            'remove_metadata' => true,
        ]);

        return $processedFile;
    }

    /**
     * Validate file using existing FileService.
     */
    protected function validateFile(UploadedFile $file): void
    {
        $config = config('chat.files');
        
        // Check if file uploads are enabled
        if (!$config['enabled']) {
            throw new \Exception('File uploads are disabled');
        }

        // Check file size
        $maxSize = $this->parseFileSize($config['max_size']);
        if ($file->getSize() > $maxSize) {
            throw new \Exception('File size exceeds maximum allowed size');
        }

        // Check file type
        $extension = strtolower($file->getClientOriginalExtension());
        $allowedTypes = array_merge(
            $config['allowed_types']['images'] ?? [],
            $config['allowed_types']['documents'] ?? [],
            $config['allowed_types']['archives'] ?? []
        );

        if (!in_array($extension, $allowedTypes)) {
            throw new \Exception('File type not allowed');
        }

        // Use existing FileService validation
        $this->fileService->validateFile($file);
    }

    /**
     * Check if file is an image.
     */
    protected function isImageFile(UploadedFile $file): bool
    {
        $imageTypes = config('chat.files.allowed_types.images', []);
        $extension = strtolower($file->getClientOriginalExtension());
        
        return in_array($extension, $imageTypes);
    }

    /**
     * Generate unique filename.
     */
    protected function generateUniqueFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        return Str::uuid() . '.' . $extension;
    }

    /**
     * Calculate file expiry date.
     */
    protected function calculateExpiryDate(): ?\Carbon\Carbon
    {
        $retentionDays = config('chat.database.message_retention_days', 365);
        return now()->addDays($retentionDays);
    }

    /**
     * Schedule virus scan.
     */
    protected function scheduleVirusScan(ChatFile $chatFile): void
    {
        if (config('chat.files.virus_scan', true)) {
            // Use existing FileService virus scanning
            $this->fileService->scheduleVirusScan($chatFile->file_path, function ($result) use ($chatFile) {
                $chatFile->markAsScanned($result);
                
                if ($result === 'infected') {
                    // Log security incident
                    $this->activityLogger->log('chat_file_infected', $chatFile, [
                        'filename' => $chatFile->original_filename,
                        'room_id' => $chatFile->chat_room_id,
                    ]);
                    
                    // Delete infected file
                    $this->deleteFile($chatFile);
                }
            });
        }
    }

    /**
     * Generate temporary download token.
     */
    public function generateDownloadToken(ChatFile $file, ?User $user = null): string
    {
        $expiryHours = config('chat.files.token_expiry_hours', 1);
        return $file->generateDownloadToken($expiryHours * 60);
    }

    /**
     * Download file with token validation.
     */
    public function downloadFile(string $token): array
    {
        $file = ChatFile::validateDownloadToken($token);
        
        if (!$file) {
            throw new \Exception('Invalid or expired download token');
        }

        if (!$file->fileExists()) {
            throw new \Exception('File not found');
        }

        if ($file->isInfected()) {
            throw new \Exception('File is infected and cannot be downloaded');
        }

        // Increment download count
        $file->incrementDownloadCount();

        // Log download activity
        $this->activityLogger->log('chat_file_downloaded', $file, [
            'filename' => $file->original_filename,
            'room_id' => $file->chat_room_id,
            'download_count' => $file->download_count,
        ]);

        return [
            'content' => $file->getContent(),
            'filename' => $file->original_filename,
            'mime_type' => $file->mime_type,
            'size' => $file->file_size,
        ];
    }

    /**
     * Get file preview (for images).
     */
    public function getFilePreview(ChatFile $file, array $options = []): ?array
    {
        if (!$file->is_image) {
            return null;
        }

        if (!$file->fileExists() || $file->isInfected()) {
            return null;
        }

        // Use ImageService to generate preview
        return $this->imageService->generatePreview($file->file_path, array_merge([
            'width' => 300,
            'height' => 300,
            'quality' => 80,
        ], $options));
    }

    /**
     * Delete file.
     */
    public function deleteFile(ChatFile $file): bool
    {
        return DB::transaction(function () use ($file) {
            // Delete from storage
            if ($file->fileExists()) {
                Storage::delete($file->file_path);
            }

            // Log deletion
            $this->activityLogger->log('chat_file_deleted', $file, [
                'filename' => $file->original_filename,
                'room_id' => $file->chat_room_id,
                'reason' => 'manual_deletion',
            ]);

            // Delete record
            return $file->delete();
        });
    }

    /**
     * Clean up expired files.
     */
    public function cleanupExpiredFiles(): int
    {
        $expiredFiles = ChatFile::expired()->get();
        $deletedCount = 0;

        foreach ($expiredFiles as $file) {
            if ($this->deleteFile($file)) {
                $deletedCount++;
            }
        }

        if ($deletedCount > 0) {
            $this->activityLogger->log('chat_files_cleanup', null, [
                'deleted_count' => $deletedCount,
                'cleanup_date' => now(),
            ]);
        }

        return $deletedCount;
    }

    /**
     * Get file statistics.
     */
    public function getFileStatistics(array $filters = []): array
    {
        $query = ChatFile::query();

        if (isset($filters['room_id'])) {
            $query->where('chat_room_id', $filters['room_id']);
        }

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $total = $query->count();
        $images = $query->clone()->where('is_image', true)->count();
        $totalSize = $query->sum('file_size');
        $avgSize = $total > 0 ? $totalSize / $total : 0;

        return [
            'total_files' => $total,
            'image_files' => $images,
            'document_files' => $total - $images,
            'total_size_bytes' => $totalSize,
            'average_size_bytes' => $avgSize,
            'total_downloads' => $query->sum('download_count'),
        ];
    }

    /**
     * Parse file size string to bytes.
     */
    protected function parseFileSize(string $size): int
    {
        $units = ['B' => 1, 'KB' => 1024, 'MB' => 1048576, 'GB' => 1073741824];
        
        if (preg_match('/^(\d+(?:\.\d+)?)\s*([KMGT]?B)$/i', trim($size), $matches)) {
            $value = (float) $matches[1];
            $unit = strtoupper($matches[2]);
            
            return (int) ($value * ($units[$unit] ?? 1));
        }
        
        return (int) $size;
    }
}
