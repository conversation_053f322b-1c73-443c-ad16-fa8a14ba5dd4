<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\UserPreference;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class UserPreferencesController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display the user preferences form.
     */
    public function index(): View
    {
        $user = auth()->user();
        $preferences = $user->getPreferences();

        $availableTimezones = UserPreference::getAvailableTimezones();
        $availableCurrencies = UserPreference::getAvailableCurrencies();
        $availableLanguages = UserPreference::getAvailableLanguages();

        return view('admin.preferences.index', compact(
            'preferences',
            'availableTimezones',
            'availableCurrencies',
            'availableLanguages'
        ));
    }

    /**
     * Update user preferences.
     */
    public function update(Request $request): JsonResponse
    {
        $user = auth()->user();
        $preferences = $user->getPreferences();

        $validated = $request->validate([
            'language' => 'sometimes|string|in:' . implode(',', array_keys(UserPreference::getAvailableLanguages())),
            'timezone' => 'sometimes|string|in:' . implode(',', array_keys(UserPreference::getAvailableTimezones())),
            'currency' => 'sometimes|string|in:' . implode(',', array_keys(UserPreference::getAvailableCurrencies())),
            'date_format' => 'sometimes|string|in:Y-m-d,d/m/Y,m/d/Y,d-m-Y',
            'time_format' => 'sometimes|string|in:H:i,h:i A',
            'theme' => 'sometimes|string|in:light,dark,auto',
            'dashboard_layout' => 'sometimes|string|in:grid,list,compact',
            'show_welcome_message' => 'sometimes|boolean',
            'auto_refresh_dashboard' => 'sometimes|boolean',
            'auto_refresh_interval' => 'sometimes|integer|min:10|max:300',
            'email_notifications' => 'sometimes|boolean',
            'browser_notifications' => 'sometimes|boolean',
            'sms_notifications' => 'sometimes|boolean',
            'notification_types' => 'sometimes|array',
            'quiet_hours_start' => 'sometimes|nullable|date_format:H:i',
            'quiet_hours_end' => 'sometimes|nullable|date_format:H:i',
            'chat_sound_enabled' => 'sometimes|boolean',
            'chat_desktop_notifications' => 'sometimes|boolean',
            'chat_status' => 'sometimes|string|in:available,busy,away,offline',
            'max_concurrent_chats' => 'sometimes|integer|min:1|max:20',
            'auto_assign_chats' => 'sometimes|boolean',
            'items_per_page' => 'sometimes|integer|in:10,25,50,100',
            'show_tooltips' => 'sometimes|boolean',
            'compact_mode' => 'sometimes|boolean',
            'profile_visible' => 'sometimes|boolean',
            'activity_tracking' => 'sometimes|boolean',
            'analytics_tracking' => 'sometimes|boolean',
        ]);

        try {
            $preferences->update($validated);

            return response()->json([
                'success' => true,
                'message' => 'Preferences updated successfully',
                'preferences' => $preferences->fresh()->toPreferencesArray(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update preferences: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update a single preference.
     */
    public function updateSingle(Request $request): JsonResponse
    {
        $user = auth()->user();
        $preferences = $user->getPreferences();

        $request->validate([
            'key' => 'required|string',
            'value' => 'required',
        ]);

        $key = $request->input('key');
        $value = $request->input('value');

        if (!in_array($key, $preferences->getFillable())) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid preference key',
            ], 400);
        }

        try {
            $success = $preferences->updatePreference($key, $value);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Preference updated successfully',
                    'key' => $key,
                    'value' => $preferences->{$key},
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update preference',
                ], 500);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating preference: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Reset preferences to defaults.
     */
    public function reset(): JsonResponse
    {
        $user = auth()->user();

        try {
            // Delete existing preferences to trigger defaults
            $user->preferences()?->delete();

            // Create new preferences with defaults
            $preferences = $user->getPreferences();

            return response()->json([
                'success' => true,
                'message' => 'Preferences reset to defaults successfully',
                'preferences' => $preferences->toPreferencesArray(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reset preferences: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export preferences.
     */
    public function export(): JsonResponse
    {
        $user = auth()->user();
        $preferences = $user->getPreferences();

        return response()->json([
            'success' => true,
            'preferences' => $preferences->toPreferencesArray(),
            'exported_at' => now()->toISOString(),
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
            ],
        ]);
    }
}
