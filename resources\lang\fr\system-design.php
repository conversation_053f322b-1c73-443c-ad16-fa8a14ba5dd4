<?php

return [
    // Page Meta
    'page_title' => 'Services de Conception de Systèmes - ChiSolution',
    'meta_description' => 'Services professionnels de conception de systèmes incluant l\'architecture évolutive, la conception de microservices, l\'infrastructure cloud, la conception de bases de données, l\'architecture API et l\'optimisation des performances.',
    'meta_keywords' => 'conception de systèmes, architecture logicielle, systèmes évolutifs, microservices, architecture cloud, conception base de données, conception API, architecture système, systèmes distribués, optimisation performances',

    // Hero Section
    'hero_title' => 'Services Professionnels de Conception de Systèmes',
    'hero_description' => 'Construisez des systèmes évolutifs, fiables et performants qui grandissent avec votre entreprise. Architecture système experte pour applications modernes et systèmes distribués.',
    'get_started' => 'Commencer',
    'get_quote' => 'Obtenir un Devis',
    'view_portfolio' => 'Voir le Portfolio',

    // What is System Design Section
    'what_is_title' => 'Qu\'est-ce que la <span class="text-blue-600">Conception de Systèmes</span>?',
    'what_is_description' => 'La conception de systèmes est le processus de définition de l\'architecture, des composants, modules, interfaces et données d\'un système pour satisfaire des exigences spécifiées. Elle implique la création de systèmes évolutifs, fiables et maintenables.',
    'high_level_architecture_title' => 'Architecture de Haut Niveau',
    'high_level_architecture_description' => 'Définir la structure globale et les composants de votre système, y compris comment les différents services interagissent et communiquent entre eux.',
    'scalability_title' => 'Évolutivité',
    'scalability_description' => 'Concevoir des systèmes qui peuvent gérer une charge accrue en s\'étendant horizontalement ou verticalement sans compromettre les performances.',
    'reliability_title' => 'Fiabilité',
    'reliability_description' => 'Construire des systèmes tolérants aux pannes avec redondance, mécanismes de basculement et stratégies de récupération après sinistre.',
    'performance_title' => 'Performance',
    'performance_description' => 'Optimiser les performances du système grâce à des algorithmes efficaces, des stratégies de mise en cache et la gestion des ressources.',
    'security_title' => 'Sécurité',
    'security_description' => 'Implémenter des mesures de sécurité complètes incluant l\'authentification, l\'autorisation, le chiffrement et la protection contre les menaces.',
    'data_flow_title' => 'Flux de Données',
    'data_flow_description' => 'Concevoir des pipelines de données efficaces et des solutions de stockage qui garantissent l\'intégrité et l\'accessibilité des données.',

    // System Design Example Section
    'example_title' => 'Exemple de Conception de Système: <span class="text-blue-600">Plateforme E-commerce</span>',
    'example_description' => 'Voici comment nous concevons une plateforme e-commerce évolutive qui peut gérer des millions d\'utilisateurs et de transactions.',
    'load_balancer_title' => 'Équilibreur de Charge',
    'load_balancer_description' => 'Distribue les requêtes entrantes sur plusieurs serveurs',
    'api_gateway_title' => 'Passerelle API',
    'api_gateway_description' => 'Point d\'entrée unique pour toutes les requêtes client',
    'microservices_title' => 'Microservices',
    'microservices_description' => 'Services indépendants pour utilisateurs, produits, commandes, paiements',
    'databases_title' => 'Bases de Données',
    'databases_description' => 'Stockage de données optimisé pour différents besoins de service',
    'cache_layer_title' => 'Couche de Cache',
    'cache_layer_description' => 'Redis/Memcached pour une récupération rapide des données',
    'message_queue_title' => 'File de Messages',
    'message_queue_description' => 'Communication asynchrone entre services',
    'cdn_title' => 'CDN',
    'cdn_description' => 'Livraison de contenu global pour les ressources statiques',
    'monitoring_title' => 'Surveillance',
    'monitoring_description' => 'Suivi en temps réel de la santé et des performances du système',

    // System Design Tools Section
    'tools_title' => 'Outils de <span class="text-blue-600">Conception de Systèmes</span>',
    'tools_description' => 'Nous utilisons des outils et plateformes leaders de l\'industrie pour concevoir, implémenter et surveiller des systèmes évolutifs.',
    'architecture_modeling_title' => 'Modélisation d\'Architecture',
    'architecture_modeling_description' => 'Conception visuelle de systèmes utilisant des outils comme Lucidchart, Draw.io et Miro pour créer des diagrammes d\'architecture complets.',
    'cloud_platforms_title' => 'Plateformes Cloud',
    'cloud_platforms_description' => 'AWS, Azure et Google Cloud Platform pour l\'infrastructure évolutive et les services gérés.',
    'containerization_title' => 'Conteneurisation',
    'containerization_description' => 'Docker et Kubernetes pour la conteneurisation et l\'orchestration d\'applications.',
    'database_design_title' => 'Conception de Base de Données',
    'database_design_description' => 'Bases de données SQL et NoSQL incluant PostgreSQL, MongoDB, Redis et Elasticsearch.',
    'monitoring_tools_title' => 'Surveillance et Observabilité',
    'monitoring_tools_description' => 'Prometheus, Grafana, ELK Stack et New Relic pour une surveillance système complète.',
    'api_design_title' => 'Conception d\'API',
    'api_design_description' => 'APIs RESTful, GraphQL et gRPC pour une communication de service efficace.',

    // Technology Stack Section
    'tech_stack_title' => 'Stack <span class="text-blue-600">Technologique</span>',
    'tech_stack_description' => 'Nous utilisons des technologies et frameworks modernes pour construire des systèmes robustes et évolutifs.',
    'backend_title' => 'Technologies Backend',
    'backend_description' => 'Node.js, Python, Java, Go, .NET pour construire des services backend haute performance.',
    'databases_stack_title' => 'Bases de Données',
    'databases_stack_description' => 'PostgreSQL, MongoDB, Redis, Elasticsearch, InfluxDB pour divers besoins de stockage de données.',
    'cloud_devops_title' => 'Cloud et DevOps',
    'cloud_devops_description' => 'AWS, Azure, GCP, Docker, Kubernetes, Terraform pour l\'infrastructure cloud et le déploiement.',
    'message_queues_title' => 'Files de Messages',
    'message_queues_description' => 'RabbitMQ, Apache Kafka, Amazon SQS pour la communication asynchrone.',

    // System Design Process Section
    'process_title' => 'Notre Processus de <span class="text-blue-600">Conception de Systèmes</span>',
    'process_description' => 'Nous suivons une approche systématique pour concevoir des systèmes évolutifs qui répondent à vos exigences commerciales.',
    'step_requirements_title' => 'Analyse des Exigences',
    'step_requirements_description' => 'Comprendre les exigences fonctionnelles et non fonctionnelles, les contraintes et les objectifs commerciaux.',
    'step_high_level_title' => 'Conception de Haut Niveau',
    'step_high_level_description' => 'Créer un aperçu de l\'architecture système, identifier les composants majeurs et leurs interactions.',
    'step_detailed_title' => 'Conception Détaillée',
    'step_detailed_description' => 'Concevoir les composants individuels, APIs, modèles de données et définir les détails d\'implémentation.',
    'step_implementation_title' => 'Implémentation et Tests',
    'step_implementation_description' => 'Construire le système selon la conception, effectuer des tests et optimiser les performances.',

    // Why Choose Us Section
    'why_choose_title' => 'Pourquoi Choisir Nos Services de Conception de Systèmes?',
    'why_choose_description' => 'Nous combinons une expertise technique approfondie avec une expérience pratique pour livrer des systèmes qui s\'adaptent et performent.',
    'scalable_solutions_title' => 'Solutions Évolutives',
    'scalable_solutions_description' => 'Concevoir des systèmes qui grandissent avec votre entreprise et gèrent efficacement une charge croissante.',
    'proven_expertise_title' => 'Expertise Prouvée',
    'proven_expertise_description' => 'Des années d\'expérience dans la conception de systèmes pour des startups aux applications de niveau entreprise.',
    'modern_technologies_title' => 'Technologies Modernes',
    'modern_technologies_description' => 'Utiliser des technologies de pointe et les meilleures pratiques pour des performances optimales.',
    'cost_effective_title' => 'Rentable',
    'cost_effective_description' => 'Optimiser les coûts d\'infrastructure tout en maintenant haute performance et fiabilité.',
    'security_first_title' => 'Sécurité d\'Abord',
    'security_first_description' => 'Mesures de sécurité intégrées et conformité aux standards de l\'industrie.',
    'ongoing_optimization_title' => 'Optimisation Continue',
    'optimization_description' => 'Surveillance et optimisation continues pour assurer des performances de pointe.',

    // CTA Section
    'cta_title' => 'Prêt à Construire Votre Système Évolutif?',
    'cta_description' => 'Concevons une architecture système qui s\'adapte à votre entreprise et offre des performances exceptionnelles.',
    'start_project' => 'Commencer Votre Projet de Conception de Système',
    'free_consultation' => 'Consultation d\'Architecture Gratuite',
    'view_work' => 'Voir Notre Travail',

    // System Components
    'system_components' => [
        'Load Balancers' => 'Équilibreurs de Charge',
        'API Gateways' => 'Passerelles API',
        'Microservices' => 'Microservices',
        'Databases' => 'Bases de Données',
        'Caching' => 'Mise en Cache',
        'Message Queues' => 'Files de Messages',
        'CDN' => 'CDN',
        'Monitoring' => 'Surveillance',
    ],

    // Architecture Patterns
    'architecture_patterns' => [
        'Microservices Architecture' => 'Architecture Microservices',
        'Event-Driven Architecture' => 'Architecture Pilotée par Événements',
        'Serverless Architecture' => 'Architecture Sans Serveur',
        'Layered Architecture' => 'Architecture en Couches',
    ],

    // Scalability Features
    'scalability_features' => [
        'Horizontal Scaling' => 'Mise à l\'Échelle Horizontale',
        'Vertical Scaling' => 'Mise à l\'Échelle Verticale',
        'Auto-scaling' => 'Auto-scaling',
        'Load Distribution' => 'Distribution de Charge',
    ],

    // Performance Features
    'performance_features' => [
        'Caching Strategies' => 'Stratégies de Mise en Cache',
        'Database Optimization' => 'Optimisation de Base de Données',
        'CDN Integration' => 'Intégration CDN',
        'Performance Monitoring' => 'Surveillance des Performances',
    ],
];
