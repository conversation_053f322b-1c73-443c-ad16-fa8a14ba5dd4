<?php

namespace App\Http\Middleware;

use App\Models\LoginHistoryPermission;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckLoginHistoryPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string|null  $requiredPermission  Specific permission required
     * @param  string|null  $minimumLevel  Minimum access level required (partial, full)
     */
    public function handle(Request $request, Closure $next, ?string $requiredPermission = null, ?string $minimumLevel = null): Response
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        // Check if user is admin - admins have full access by default
        if ($user->hasRole('admin')) {
            return $next($request);
        }

        // Check if user has staff role (required for any login history permissions)
        if (!$user->hasRole(['admin', 'staff'])) {
            return response()->json([
                'error' => 'Forbidden',
                'message' => 'Only admin and staff users can access login history management.'
            ], 403);
        }

        // Get user's login history permission
        $permission = LoginHistoryPermission::where('user_id', $user->id)
            ->active()
            ->first();

        if (!$permission) {
            return response()->json([
                'error' => 'Forbidden',
                'message' => 'You do not have permission to access login history data.'
            ], 403);
        }

        // Check if permission is valid
        if (!$permission->isValid()) {
            return response()->json([
                'error' => 'Forbidden',
                'message' => 'Your login history access permission has expired or been revoked.'
            ], 403);
        }

        // Check minimum access level if specified
        if ($minimumLevel) {
            $levelHierarchy = ['none' => 0, 'partial' => 1, 'full' => 2];
            $userLevel = $levelHierarchy[$permission->access_level] ?? 0;
            $requiredLevelValue = $levelHierarchy[$minimumLevel] ?? 0;

            if ($userLevel < $requiredLevelValue) {
                return response()->json([
                    'error' => 'Forbidden',
                    'message' => "You need {$minimumLevel} access level to perform this action."
                ], 403);
            }
        }

        // Check specific permission if specified
        if ($requiredPermission && !$permission->hasSpecificPermission($requiredPermission)) {
            return response()->json([
                'error' => 'Forbidden',
                'message' => "You do not have the required permission: {$requiredPermission}"
            ], 403);
        }

        // Add permission info to request for use in controllers
        $request->merge([
            'login_history_permission' => $permission,
            'login_history_access_level' => $permission->access_level,
        ]);

        return $next($request);
    }
}
