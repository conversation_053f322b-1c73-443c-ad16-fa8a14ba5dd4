<?php

return [
    // Page titles and meta
    'page_title' => 'Careers - Join Our Team',
    'meta_description' => 'Join our dynamic team! Explore exciting career opportunities in web development, mobile apps, digital marketing, and more. Apply for jobs at our innovative tech company.',
    
    // Hero section
    'hero_title' => 'Join Our Amazing Team',
    'hero_subtitle' => 'Build the future of technology with us. We\'re looking for passionate, talented individuals to join our growing team.',
    'view_open_positions' => 'View Open Positions',
    'learn_about_culture' => 'Learn About Our Culture',
    
    // Job search and filters
    'search_jobs' => 'Search Jobs',
    'job_title_keywords' => 'Job title, keywords...',
    'employment_type' => 'Employment Type',
    'all_types' => 'All Types',
    'experience_level' => 'Experience Level',
    'all_levels' => 'All Levels',
    'location' => 'Location',
    'all_locations' => 'All Locations',
    'search_jobs_button' => 'Search Jobs',
    'clear_filters' => 'Clear Filters',
    
    // Open positions section
    'open_positions' => 'Open Positions',
    'open_positions_subtitle' => 'Discover exciting opportunities to grow your career with us. We offer competitive salaries, great benefits, and a collaborative work environment.',
    'featured' => 'Featured',
    'remote' => 'Remote',
    'view_details' => 'View Details',
    'apply_by' => 'Apply by',
    
    // No jobs message
    'no_jobs_title' => 'No open positions',
    'no_jobs_message' => 'We don\'t have any open positions matching your criteria at the moment. Check back soon!',
    'view_all_jobs' => 'View All Jobs',
    
    // Company culture section
    'company_culture' => 'Why Work With Us?',
    'culture_subtitle' => 'We believe in creating an environment where everyone can thrive and do their best work.',
    
    'culture_innovation_title' => 'Innovation First',
    'culture_innovation_desc' => 'Work with cutting-edge technologies and contribute to groundbreaking projects that make a real impact.',
    
    'culture_growth_title' => 'Career Growth',
    'culture_growth_desc' => 'We invest in your professional development with training, mentorship, and clear advancement paths.',
    
    'culture_balance_title' => 'Work-Life Balance',
    'culture_balance_desc' => 'Flexible working hours, remote options, and a supportive environment that values your well-being.',
    
    'culture_team_title' => 'Amazing Team',
    'culture_team_desc' => 'Join a diverse, collaborative team of talented professionals who are passionate about what they do.',
    
    'culture_benefits_title' => 'Great Benefits',
    'culture_benefits_desc' => 'Competitive salary, health insurance, retirement plans, and other perks that show we value you.',
    
    'culture_impact_title' => 'Make an Impact',
    'culture_impact_desc' => 'Your work will directly contribute to products and services that help businesses succeed.',
    
    // Application status section
    'check_status_title' => 'Check Your Application Status',
    'check_status_subtitle' => 'Enter your reference number and email to track your application progress.',
    'reference_number' => 'Reference Number',
    'email_address' => 'Email Address',
    'check_status_button' => 'Check Status',
    
    // Employment types
    'employment_types' => [
        'full-time' => 'Full Time',
        'part-time' => 'Part Time',
        'contract' => 'Contract',
        'internship' => 'Internship',
        'freelance' => 'Freelance',
    ],
    
    // Experience levels
    'experience_levels' => [
        'entry' => 'Entry Level',
        'junior' => 'Junior',
        'mid' => 'Mid Level',
        'senior' => 'Senior',
        'lead' => 'Lead',
        'manager' => 'Manager',
    ],
    
    // Job details page
    'job_details' => 'Job Details',
    'apply_now' => 'Apply Now',
    'back_to_jobs' => 'Back to Jobs',
    'job_description' => 'Job Description',
    'requirements' => 'Requirements',
    'responsibilities' => 'Responsibilities',
    'benefits' => 'Benefits',
    'salary_range' => 'Salary Range',
    'application_deadline' => 'Application Deadline',
    'ready_to_apply' => 'Ready to Apply?',
    'ready_to_apply_desc' => 'Join our team and help us build amazing products!',
    'apply_for_position' => 'Apply for This Position',
    'similar_positions' => 'Similar Positions',
    
    // Application form
    'application_form_title' => 'Apply for Position',
    'personal_information' => 'Personal Information',
    'first_name' => 'First Name',
    'last_name' => 'Last Name',
    'email' => 'Email',
    'phone' => 'Phone',
    'linkedin_profile' => 'LinkedIn Profile',
    'portfolio_website' => 'Portfolio Website',
    
    'professional_information' => 'Professional Information',
    'current_position' => 'Current Position',
    'years_experience' => 'Years of Experience',
    'expected_salary' => 'Expected Salary',
    'availability' => 'Availability',
    'cover_letter' => 'Cover Letter',
    'cover_letter_placeholder' => 'Tell us why you\'re interested in this position and what makes you a great fit...',
    
    'documents' => 'Documents',
    'resume_cv' => 'Resume/CV',
    'additional_documents' => 'Additional Documents',
    'additional_documents_desc' => 'Portfolio, certificates, or other relevant documents',
    
    'terms_agreement' => 'I agree to the Terms of Service and Privacy Policy',
    'submit_application' => 'Submit Application',
    
    // Application success
    'application_success_title' => 'Application Submitted Successfully!',
    'application_success_message' => 'Thank you for your interest in joining our team. We have received your application and will review it carefully.',
    'reference_number_is' => 'Your reference number is',
    'save_reference_number' => 'Please save this reference number for your records. You can use it to check your application status.',
    'what_happens_next' => 'What happens next?',
    'review_process' => 'Our hiring team will review your application within 3-5 business days.',
    'initial_screening' => 'If your profile matches our requirements, we\'ll contact you for an initial screening.',
    'interview_process' => 'Qualified candidates will be invited for interviews (phone/video or in-person).',
    'final_decision' => 'We\'ll make a final decision and notify all candidates of the outcome.',
    'check_status' => 'Check Status',
    'view_other_positions' => 'View Other Positions',
    'back_to_home' => 'Back to Home',
    
    // Application status
    'application_status' => 'Application Status',
    'status_submitted' => 'Submitted',
    'status_under_review' => 'Under Review',
    'status_interview_scheduled' => 'Interview Scheduled',
    'status_interview_completed' => 'Interview Completed',
    'status_offer_extended' => 'Offer Extended',
    'status_hired' => 'Hired',
    'status_rejected' => 'Not Selected',
    'status_withdrawn' => 'Withdrawn',
    
    'submitted_on' => 'Submitted on',
    'last_updated' => 'Last updated',
    'back_to_careers' => 'Back to Careers',
    'view_job_details' => 'View Job Details',
];
