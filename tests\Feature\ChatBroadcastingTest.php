<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\ChatParticipant;
use App\Events\Chat\MessageSent;
use App\Events\Chat\UserTyping;
use App\Events\Chat\UserOnlineStatus;
use App\Events\Chat\MessageRead;
use App\Events\Chat\RoomJoined;
use App\Events\Chat\RoomLeft;
use App\Services\ChatRealtimeService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\Facades\Cache;
use PHPUnit\Framework\Attributes\Test;

class ChatBroadcastingTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected ChatRoom $room;
    protected ChatRealtimeService $realtimeService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Fake only specific chat events for testing
        Event::fake([
            MessageSent::class,
            UserTyping::class,
            UserOnlineStatus::class,
            MessageRead::class,
            RoomJoined::class,
            RoomLeft::class,
        ]);
        
        $this->user = User::factory()->create();
        // Ensure user has a role to avoid hasRole() issues
        if (!$this->user->role_id) {
            $role = \App\Models\Role::firstOrCreate(['name' => 'customer']);
            $this->user->update(['role_id' => $role->id]);
        }
        $this->room = ChatRoom::factory()->active()->create();
        $this->realtimeService = app(ChatRealtimeService::class);
    }

    protected function tearDown(): void
    {
        // Clear any caches or static state that might leak between tests
        Cache::flush();

        parent::tearDown();
    }

    #[Test]
    public function message_sent_event_is_broadcasted_when_message_is_sent()
    {
        $this->actingAs($this->user, 'sanctum');

        $response = $this->postJson("/api/v1/chat/rooms/{$this->room->uuid}/messages", [
            'content' => 'Test message',
            'message_type' => 'text',
        ]);

        $response->assertStatus(201);

        // Assert that MessageSent event was dispatched
        Event::assertDispatched(MessageSent::class, function ($event) {
            return $event->room->id === $this->room->id &&
                   $event->message->content === 'Test message';
        });
    }

    #[Test]
    public function message_sent_event_broadcasts_on_correct_channel()
    {
        $message = ChatMessage::factory()->create([
            'chat_room_id' => $this->room->id,
            'content' => 'Test message',
        ]);

        $event = new MessageSent($message, $this->room);

        // Check that the event broadcasts on the correct private channel
        $channels = $event->broadcastOn();
        $this->assertCount(1, $channels);
        $this->assertEquals("private-chat.room.{$this->room->uuid}", $channels[0]->name);
    }

    #[Test]
    public function message_sent_event_includes_correct_data()
    {
        $message = ChatMessage::factory()->create([
            'chat_room_id' => $this->room->id,
            'content' => 'Test message',
            'user_id' => $this->user->id,
        ]);

        $event = new MessageSent($message, $this->room);
        $broadcastData = $event->broadcastWith();

        $this->assertArrayHasKey('message', $broadcastData);
        $this->assertArrayHasKey('room', $broadcastData);
        $this->assertArrayHasKey('timestamp', $broadcastData);
        
        $this->assertEquals($message->content, $broadcastData['message']['content']);
        $this->assertEquals($this->room->uuid, $broadcastData['room']['uuid']);
    }

    #[Test]
    public function typing_event_is_broadcasted_correctly()
    {
        $this->actingAs($this->user, 'sanctum');

        $response = $this->postJson("/api/v1/chat/rooms/{$this->room->uuid}/typing", [
            'is_typing' => true,
        ]);

        $response->assertStatus(200);

        // Assert that UserTyping event was dispatched
        Event::assertDispatched(UserTyping::class, function ($event) {
            return $event->room->id === $this->room->id &&
                   $event->isTyping === true &&
                   $event->userId === $this->user->id;
        });
    }

    #[Test]
    public function typing_event_broadcasts_with_correct_data()
    {
        $event = new UserTyping($this->room, get_class($this->user), $this->user->id, $this->user->name, true);
        $broadcastData = $event->broadcastWith();

        $this->assertArrayHasKey('user', $broadcastData);
        $this->assertArrayHasKey('is_typing', $broadcastData);
        $this->assertArrayHasKey('room_uuid', $broadcastData);
        $this->assertArrayHasKey('timestamp', $broadcastData);
        
        $this->assertEquals($this->user->id, $broadcastData['user']['id']);
        $this->assertEquals($this->user->name, $broadcastData['user']['name']);
        $this->assertTrue($broadcastData['is_typing']);
        $this->assertEquals($this->room->uuid, $broadcastData['room_uuid']);
    }

    #[Test]
    public function room_joined_event_is_broadcasted_when_user_joins()
    {
        $this->actingAs($this->user, 'sanctum');

        $response = $this->postJson("/api/v1/chat/rooms/{$this->room->uuid}/join");

        $response->assertStatus(200);

        // Assert that RoomJoined event was dispatched
        Event::assertDispatched(RoomJoined::class, function ($event) {
            return $event->room->id === $this->room->id &&
                   $event->participant->user_id === $this->user->id;
        });
    }

    #[Test]
    public function room_left_event_is_broadcasted_when_user_leaves()
    {
        // First join the room
        ChatParticipant::create([
            'chat_room_id' => $this->room->id,
            'user_id' => $this->user->id,
            'participant_type' => 'customer',
            'role' => 'participant',
            'display_name' => $this->user->name,
            'joined_at' => now(),
        ]);

        $this->actingAs($this->user, 'sanctum');

        $response = $this->postJson("/api/v1/chat/rooms/{$this->room->uuid}/leave");

        $response->assertStatus(200);

        // Assert that RoomLeft event was dispatched
        Event::assertDispatched(RoomLeft::class, function ($event) {
            return $event->room->id === $this->room->id &&
                   $event->userId === $this->user->id;
        });
    }

    #[Test]
    public function message_read_event_is_broadcasted_correctly()
    {
        $message = ChatMessage::factory()->create([
            'chat_room_id' => $this->room->id,
        ]);

        $this->actingAs($this->user, 'sanctum');

        $response = $this->postJson("/api/v1/chat/rooms/{$this->room->uuid}/messages/{$message->uuid}/read");

        $response->assertStatus(200);

        // Assert that MessageRead event was dispatched
        Event::assertDispatched(MessageRead::class, function ($event) use ($message) {
            return $event->message->id === $message->id &&
                   $event->room->id === $this->room->id &&
                   $event->readerId === $this->user->id;
        });
    }

    #[Test]
    public function online_status_event_broadcasts_with_throttling()
    {
        // Test that online status updates are throttled
        $this->realtimeService->updateOnlineStatus(
            $this->room,
            get_class($this->user),
            $this->user->id,
            $this->user->name,
            true
        );

        // First call should dispatch event
        Event::assertDispatched(UserOnlineStatus::class);

        // Reset event fake
        Event::fake();

        // Immediate second call should be throttled (not dispatch event)
        $this->realtimeService->updateOnlineStatus(
            $this->room,
            get_class($this->user),
            $this->user->id,
            $this->user->name,
            true
        );

        // Should not dispatch due to throttling
        Event::assertNotDispatched(UserOnlineStatus::class);
    }

    #[Test]
    public function events_have_correct_broadcast_names()
    {
        $message = ChatMessage::factory()->create(['chat_room_id' => $this->room->id]);
        $participant = ChatParticipant::factory()->create(['chat_room_id' => $this->room->id]);

        $messageSentEvent = new MessageSent($message, $this->room);
        $userTypingEvent = new UserTyping($this->room, 'User', 1, 'Test User', true);
        $userOnlineEvent = new UserOnlineStatus($this->room, 'User', 1, 'Test User', true);
        $messageReadEvent = new MessageRead($message, $this->room, 'User', 1, 'Test User');
        $roomJoinedEvent = new RoomJoined($this->room, $participant);
        $roomLeftEvent = new RoomLeft($this->room, 'User', 1, 'Test User');

        $this->assertEquals('message.sent', $messageSentEvent->broadcastAs());
        $this->assertEquals('user.typing', $userTypingEvent->broadcastAs());
        $this->assertEquals('user.online.status', $userOnlineEvent->broadcastAs());
        $this->assertEquals('message.read', $messageReadEvent->broadcastAs());
        $this->assertEquals('room.joined', $roomJoinedEvent->broadcastAs());
        $this->assertEquals('room.left', $roomLeftEvent->broadcastAs());
    }

    #[Test]
    public function realtime_service_handles_typing_with_debouncing()
    {
        // Test typing start
        $this->realtimeService->handleTyping(
            $this->room,
            get_class($this->user),
            $this->user->id,
            $this->user->name,
            true
        );

        Event::assertDispatched(UserTyping::class, function ($event) {
            return $event->isTyping === true;
        });

        // Reset event fake
        Event::fake();

        // Test typing stop
        $this->realtimeService->handleTyping(
            $this->room,
            get_class($this->user),
            $this->user->id,
            $this->user->name,
            false
        );

        Event::assertDispatched(UserTyping::class, function ($event) {
            return $event->isTyping === false;
        });
    }

    #[Test]
    public function realtime_service_broadcasts_message_correctly()
    {
        $message = ChatMessage::factory()->create([
            'chat_room_id' => $this->room->id,
            'content' => 'Test broadcast message',
        ]);

        $this->realtimeService->broadcastMessage($message, $this->room);

        Event::assertDispatched(MessageSent::class, function ($event) use ($message) {
            return $event->message->id === $message->id &&
                   $event->room->id === $this->room->id;
        });
    }
}
