<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\ChatSearchService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

class ChatSearchController extends Controller
{
    public function __construct(
        protected ChatSearchService $searchService
    ) {
        $this->middleware(['auth', 'admin']);
    }

    /**
     * Display the search interface.
     */
    public function index(): View
    {
        return view('admin.chat.search.index');
    }

    /**
     * Search chat messages.
     */
    public function searchMessages(Request $request): JsonResponse
    {
        $filters = $request->validate([
            'content' => 'nullable|string|max:255',
            'user_id' => 'nullable|integer',
            'room_id' => 'nullable|integer',
            'message_type' => 'nullable|string|in:text,file,image,system,ai,emoji',
            'is_ai_generated' => 'nullable|boolean',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
            'room_type' => 'nullable|string|in:visitor,support,sales',
            'room_status' => 'nullable|string|in:active,closed,transferred',
            'sort_by' => 'nullable|string|in:created_at,updated_at,content',
            'sort_order' => 'nullable|string|in:asc,desc',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        $perPage = $filters['per_page'] ?? 20;
        unset($filters['per_page']);

        $results = $this->searchService->searchMessages($filters, $perPage);

        return response()->json([
            'success' => true,
            'data' => $results,
            'message' => 'Messages search completed',
        ]);
    }

    /**
     * Search chat rooms.
     */
    public function searchRooms(Request $request): JsonResponse
    {
        $filters = $request->validate([
            'title' => 'nullable|string|max:255',
            'type' => 'nullable|string|in:visitor,support,sales',
            'status' => 'nullable|string|in:active,closed,transferred',
            'priority' => 'nullable|integer|min:1|max:5',
            'language' => 'nullable|string|max:10',
            'assigned_to' => 'nullable|integer',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
            'visitor_name' => 'nullable|string|max:255',
            'visitor_email' => 'nullable|email',
            'sort_by' => 'nullable|string|in:created_at,updated_at,title,priority',
            'sort_order' => 'nullable|string|in:asc,desc',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        $perPage = $filters['per_page'] ?? 20;
        unset($filters['per_page']);

        $results = $this->searchService->searchRooms($filters, $perPage);

        return response()->json([
            'success' => true,
            'data' => $results,
            'message' => 'Rooms search completed',
        ]);
    }

    /**
     * Global search across messages and rooms.
     */
    public function globalSearch(Request $request): JsonResponse
    {
        $request->validate([
            'query' => 'required|string|max:255',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        $query = $request->input('query');
        $filters = $request->only(['start_date', 'end_date']);
        $perPage = $request->input('per_page', 20);

        $results = $this->searchService->globalSearch($query, $filters, $perPage);

        return response()->json([
            'success' => true,
            'data' => $results,
            'message' => 'Global search completed',
        ]);
    }

    /**
     * Search conversations by participant.
     */
    public function searchByParticipant(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|integer',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
            'type' => 'nullable|string|in:visitor,support,sales',
            'status' => 'nullable|string|in:active,closed,transferred',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        $userId = $request->input('user_id');
        $filters = $request->only(['start_date', 'end_date', 'type', 'status']);
        $perPage = $request->input('per_page', 20);

        $results = $this->searchService->searchByParticipant($userId, $filters, $perPage);

        return response()->json([
            'success' => true,
            'data' => $results,
            'message' => 'Participant search completed',
        ]);
    }

    /**
     * Search AI-generated messages.
     */
    public function searchAIMessages(Request $request): JsonResponse
    {
        $filters = $request->validate([
            'min_confidence' => 'nullable|numeric|min:0|max:1',
            'max_confidence' => 'nullable|numeric|min:0|max:1',
            'ai_model' => 'nullable|string|max:100',
            'content' => 'nullable|string|max:255',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        $perPage = $filters['per_page'] ?? 20;
        unset($filters['per_page']);

        $results = $this->searchService->searchAIMessages($filters, $perPage);

        return response()->json([
            'success' => true,
            'data' => $results,
            'message' => 'AI messages search completed',
        ]);
    }

    /**
     * Get search suggestions.
     */
    public function getSuggestions(Request $request): JsonResponse
    {
        $request->validate([
            'query' => 'required|string|min:2|max:50',
            'limit' => 'nullable|integer|min:1|max:20',
        ]);

        $query = $request->input('query');
        $limit = $request->input('limit', 10);

        $suggestions = $this->searchService->getSearchSuggestions($query, $limit);

        return response()->json([
            'success' => true,
            'data' => $suggestions,
            'message' => 'Search suggestions retrieved',
        ]);
    }

    /**
     * Get search analytics.
     */
    public function getAnalytics(Request $request): JsonResponse
    {
        $filters = $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
        ]);

        $analytics = $this->searchService->getSearchAnalytics($filters);

        return response()->json([
            'success' => true,
            'data' => $analytics,
            'message' => 'Search analytics retrieved',
        ]);
    }

    /**
     * Export search results.
     */
    public function exportResults(Request $request): JsonResponse
    {
        $request->validate([
            'search_type' => 'required|string|in:messages,rooms,global',
            'format' => 'required|string|in:csv,json,xlsx',
            'filters' => 'required|array',
        ]);

        $searchType = $request->input('search_type');
        $format = $request->input('format');
        $filters = $request->input('filters');

        // Get search results
        $results = match ($searchType) {
            'messages' => $this->searchService->searchMessages($filters, 1000),
            'rooms' => $this->searchService->searchRooms($filters, 1000),
            'global' => $this->searchService->globalSearch($filters['query'] ?? '', $filters, 1000),
        };

        // Generate export file
        $filename = "chat_search_export_{$searchType}_" . now()->format('Y-m-d_H-i-s') . ".{$format}";
        
        // In a real implementation, you would generate the actual file here
        // For now, we'll return the data structure
        
        return response()->json([
            'success' => true,
            'data' => [
                'filename' => $filename,
                'download_url' => "/admin/chat/search/download/{$filename}",
                'results_count' => is_array($results) ? 
                    ($results['total_results'] ?? 0) : 
                    $results->total(),
            ],
            'message' => 'Export prepared successfully',
        ]);
    }

    /**
     * Advanced search with multiple criteria.
     */
    public function advancedSearch(Request $request): JsonResponse
    {
        $request->validate([
            'criteria' => 'required|array',
            'criteria.*.field' => 'required|string',
            'criteria.*.operator' => 'required|string|in:equals,contains,starts_with,ends_with,greater_than,less_than,between',
            'criteria.*.value' => 'required',
            'logic' => 'nullable|string|in:and,or',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        $criteria = $request->input('criteria');
        $logic = $request->input('logic', 'and');
        $perPage = $request->input('per_page', 20);

        // Convert advanced criteria to simple filters
        $filters = $this->convertAdvancedCriteria($criteria, $logic);

        // Determine search type based on criteria
        $hasMessageFields = collect($criteria)->pluck('field')->intersect(['content', 'message_type', 'is_ai_generated'])->isNotEmpty();
        $hasRoomFields = collect($criteria)->pluck('field')->intersect(['title', 'type', 'status', 'priority'])->isNotEmpty();

        if ($hasMessageFields && !$hasRoomFields) {
            $results = $this->searchService->searchMessages($filters, $perPage);
            $searchType = 'messages';
        } elseif ($hasRoomFields && !$hasMessageFields) {
            $results = $this->searchService->searchRooms($filters, $perPage);
            $searchType = 'rooms';
        } else {
            // Mixed criteria - use global search if there's a content query
            $query = $filters['content'] ?? $filters['title'] ?? '';
            $results = $this->searchService->globalSearch($query, $filters, $perPage);
            $searchType = 'global';
        }

        return response()->json([
            'success' => true,
            'data' => [
                'results' => $results,
                'search_type' => $searchType,
                'criteria_applied' => $criteria,
            ],
            'message' => 'Advanced search completed',
        ]);
    }

    /**
     * Convert advanced search criteria to simple filters.
     */
    protected function convertAdvancedCriteria(array $criteria, string $logic): array
    {
        $filters = [];

        foreach ($criteria as $criterion) {
            $field = $criterion['field'];
            $operator = $criterion['operator'];
            $value = $criterion['value'];

            switch ($operator) {
                case 'equals':
                    $filters[$field] = $value;
                    break;
                case 'contains':
                    $filters[$field] = $value; // Will be converted to LIKE in service
                    break;
                case 'starts_with':
                    $filters[$field] = $value . '%';
                    break;
                case 'ends_with':
                    $filters[$field] = '%' . $value;
                    break;
                case 'greater_than':
                    $filters["min_{$field}"] = $value;
                    break;
                case 'less_than':
                    $filters["max_{$field}"] = $value;
                    break;
                case 'between':
                    if (is_array($value) && count($value) === 2) {
                        $filters["min_{$field}"] = $value[0];
                        $filters["max_{$field}"] = $value[1];
                    }
                    break;
            }
        }

        return $filters;
    }
}
