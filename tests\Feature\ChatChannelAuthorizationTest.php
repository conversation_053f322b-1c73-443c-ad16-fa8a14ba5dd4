<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\ChatRoom;
use App\Models\ChatParticipant;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Broadcast;
use PHPUnit\Framework\Attributes\Test;

class ChatChannelAuthorizationTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected User $otherUser;
    protected ChatRoom $room;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->otherUser = User::factory()->create();
        $this->room = ChatRoom::factory()->create();
    }

    #[Test]
    public function user_can_access_chat_room_channel_when_participant()
    {
        // Add user as participant
        ChatParticipant::create([
            'chat_room_id' => $this->room->id,
            'participant_type' => 'customer', // Use valid enum value instead of class name
            'user_id' => $this->user->id,
            'role' => 'participant',
            'joined_at' => now(),
        ]);

        $this->actingAs($this->user);

        // Define the authorization callback
        $authCallback = function ($user, $roomUuid) {
            // Find the chat room
            $room = ChatRoom::where('uuid', $roomUuid)->first();

            if (!$room) {
                return false;
            }

            // Check if user is a participant in this room
            $participant = ChatParticipant::where('chat_room_id', $room->id)
                ->where('user_id', $user->id)
                ->first();

            if ($participant) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'type' => $participant->participant_type,
                    'role' => $participant->role ?? 'participant',
                ];
            }

            return false;
        };

        // Test channel authorization by calling the callback directly
        $authResult = $authCallback($this->user, $this->room->uuid);

        $this->assertIsArray($authResult);
        $this->assertEquals($this->user->id, $authResult['id']);
        $this->assertEquals($this->user->name, $authResult['name']);
        $this->assertEquals('participant', $authResult['role']);
    }

    #[Test]
    public function user_cannot_access_chat_room_channel_when_not_participant()
    {
        $this->actingAs($this->user);

        // Define the authorization callback
        $authCallback = function ($user, $roomUuid) {
            // Find the chat room
            $room = ChatRoom::where('uuid', $roomUuid)->first();

            if (!$room) {
                return false;
            }

            // Check if user is a participant in this room
            $participant = ChatParticipant::where('chat_room_id', $room->id)
                ->where('user_id', $user->id)
                ->first();

            if ($participant) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'type' => $participant->participant_type,
                    'role' => $participant->role ?? 'participant',
                ];
            }

            return false;
        };

        // Test channel authorization without being a participant
        $authResult = $authCallback($this->user, $this->room->uuid);

        $this->assertFalse($authResult);
    }

    #[Test]
    public function staff_user_can_access_any_chat_room_channel()
    {
        // Create a staff user with proper role
        $staffRole = \App\Models\Role::firstOrCreate(['name' => 'staff'], [
            'slug' => 'staff',
            'description' => 'Staff role',
            'is_active' => true,
        ]);

        $staffUser = User::factory()->create([
            'role_id' => $staffRole->id,
        ]);

        $this->actingAs($staffUser);

        // Define the authorization callback
        $authCallback = function ($user, $roomUuid) {
            // Find the chat room
            $room = ChatRoom::where('uuid', $roomUuid)->first();

            if (!$room) {
                return false;
            }

            // Check if user is a participant in this room
            $participant = ChatParticipant::where('chat_room_id', $room->id)
                ->where('user_id', $user->id)
                ->first();

            if ($participant) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'type' => $participant->participant_type,
                    'role' => $participant->role ?? 'participant',
                ];
            }

            // Allow staff/admin to join any room
            if ($user->hasRole(['staff', 'admin'])) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'type' => 'staff',
                    'role' => 'staff',
                ];
            }

            return false;
        };

        // Test channel authorization for staff
        $authResult = $authCallback($staffUser, $this->room->uuid);

        $this->assertIsArray($authResult);
        $this->assertEquals($staffUser->id, $authResult['id']);
        $this->assertEquals($staffUser->name, $authResult['name']);
        $this->assertEquals('staff', $authResult['role']);
    }

    #[Test]
    public function channel_authorization_fails_for_non_existent_room()
    {
        $this->actingAs($this->user);

        $nonExistentRoomUuid = 'non-existent-uuid';

        // Define the authorization callback
        $authCallback = function ($user, $roomUuid) {
            // Find the chat room
            $room = ChatRoom::where('uuid', $roomUuid)->first();

            if (!$room) {
                return false;
            }

            return true; // This shouldn't be reached
        };

        // Test channel authorization for non-existent room
        $authResult = $authCallback($this->user, $nonExistentRoomUuid);

        $this->assertFalse($authResult);
    }

    #[Test]
    public function presence_channel_authorization_works()
    {
        $this->actingAs($this->user);

        // Define the authorization callback
        $authCallback = function ($user) {
            return [
                'id' => $user->id,
                'name' => $user->name,
                'type' => 'customer',
            ];
        };

        // Test general chat presence channel
        $authResult = $authCallback($this->user);

        $this->assertIsArray($authResult);
        $this->assertEquals($this->user->id, $authResult['id']);
        $this->assertEquals($this->user->name, $authResult['name']);
        $this->assertEquals('customer', $authResult['type']);
    }

    #[Test]
    public function staff_channel_authorization_works_for_staff_only()
    {
        // Create a staff user with proper role
        $staffRole = \App\Models\Role::firstOrCreate(['name' => 'staff'], [
            'slug' => 'staff',
            'description' => 'Staff role',
            'is_active' => true,
        ]);

        $staffUser = User::factory()->create([
            'role_id' => $staffRole->id,
        ]);

        $this->actingAs($staffUser);

        // Define the authorization callback
        $authCallback = function ($user) {
            if ($user->hasRole(['staff', 'admin'])) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'type' => 'staff',
                    'role' => $user->hasRole('admin') ? 'admin' : 'staff',
                ];
            }

            return false;
        };

        // Test staff channel authorization
        $authResult = $authCallback($staffUser);

        $this->assertIsArray($authResult);
        $this->assertEquals($staffUser->id, $authResult['id']);
        $this->assertEquals('staff', $authResult['role']);
    }

    #[Test]
    public function regular_user_cannot_access_staff_channel()
    {
        $this->actingAs($this->user);

        // Define the authorization callback
        $authCallback = function ($user) {
            if ($user->hasRole(['staff', 'admin'])) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'type' => 'staff',
                    'role' => $user->hasRole('admin') ? 'admin' : 'staff',
                ];
            }

            return false;
        };

        // Test staff channel authorization for regular user
        $authResult = $authCallback($this->user);

        $this->assertFalse($authResult);
    }

    #[Test]
    public function admin_channel_authorization_works_for_admin_only()
    {
        // Create an admin user with proper role
        $adminRole = \App\Models\Role::firstOrCreate(['name' => 'admin'], [
            'slug' => 'admin',
            'description' => 'Admin role',
            'is_active' => true,
        ]);

        $adminUser = User::factory()->create([
            'role_id' => $adminRole->id,
        ]);

        $this->actingAs($adminUser);

        // Define the authorization callback
        $authCallback = function ($user) {
            if ($user->hasRole('admin')) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'type' => 'admin',
                    'role' => 'admin',
                ];
            }

            return false;
        };

        // Test admin channel authorization
        $authResult = $authCallback($adminUser);

        $this->assertIsArray($authResult);
        $this->assertEquals($adminUser->id, $authResult['id']);
        $this->assertEquals('admin', $authResult['role']);
    }
}
