<?php

namespace App\Http\Controllers;

use App\Models\UserAddress;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use App\Services\VisitorAnalytics;

class AddressController extends Controller
{
    protected VisitorAnalytics $visitorAnalytics;

    /**
     * Create a new controller instance.
     */
    public function __construct(VisitorAnalytics $visitorAnalytics)
    {
        $this->middleware('auth');
        $this->visitorAnalytics = $visitorAnalytics;
    }

    /**
     * Display a listing of the user's addresses.
     */
    public function index(): View
    {
        $user = auth()->user();
        $addresses = $user->addresses()
                         ->where('is_deleted', false)
                         ->orderBy('is_default', 'desc')
                         ->orderBy('created_at', 'desc')
                         ->get();

        // Track addresses page visit
        $this->visitorAnalytics->trackPageVisit(
            'My Addresses',
            [
                'user_id' => $user->id,
                'addresses_count' => $addresses->count(),
            ]
        );

        return view('addresses.index', compact('addresses'));
    }

    /**
     * Show the form for creating a new address.
     */
    public function create(): View
    {
        return view('addresses.create');
    }

    /**
     * Store a newly created address in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'type' => ['required', 'in:billing,shipping,default'],
            'first_name' => ['required', 'string', 'max:100'],
            'last_name' => ['required', 'string', 'max:100'],
            'company' => ['nullable', 'string', 'max:200'],
            'address_line_1' => ['required', 'string', 'max:255'],
            'address_line_2' => ['nullable', 'string', 'max:255'],
            'city' => ['required', 'string', 'max:100'],
            'state' => ['required', 'string', 'max:100'],
            'postal_code' => ['required', 'string', 'max:20'],
            'country' => ['required', 'string', 'size:2'],
            'phone' => ['nullable', 'string', 'max:20'],
            'is_default' => ['boolean'],
        ]);

        $validated['user_id'] = auth()->id();

        // If this is set as default, unset other default addresses
        if ($validated['is_default'] ?? false) {
            UserAddress::where('user_id', auth()->id())
                      ->where('is_deleted', false)
                      ->update(['is_default' => false]);
        }

        UserAddress::create($validated);

        return redirect()->route('addresses.index')
            ->with('success', 'Address added successfully!');
    }

    /**
     * Show the form for editing the specified address.
     */
    public function edit(UserAddress $address): View
    {
        // Return 404 if address is soft deleted
        if ($address->is_deleted) {
            abort(404);
        }

        // Ensure user can only edit their own addresses
        if ($address->user_id !== auth()->id()) {
            abort(403, 'Unauthorized access to address.');
        }

        return view('addresses.edit', compact('address'));
    }

    /**
     * Update the specified address in storage.
     */
    public function update(Request $request, UserAddress $address): RedirectResponse
    {
        // Return 404 if address is soft deleted
        if ($address->is_deleted) {
            abort(404);
        }

        // Ensure user can only update their own addresses
        if ($address->user_id !== auth()->id()) {
            abort(403, 'Unauthorized access to address.');
        }

        $validated = $request->validate([
            'type' => ['required', 'in:billing,shipping,default'],
            'first_name' => ['required', 'string', 'max:100'],
            'last_name' => ['required', 'string', 'max:100'],
            'company' => ['nullable', 'string', 'max:200'],
            'address_line_1' => ['required', 'string', 'max:255'],
            'address_line_2' => ['nullable', 'string', 'max:255'],
            'city' => ['required', 'string', 'max:100'],
            'state' => ['required', 'string', 'max:100'],
            'postal_code' => ['required', 'string', 'max:20'],
            'country' => ['required', 'string', 'size:2'],
            'phone' => ['nullable', 'string', 'max:20'],
            'is_default' => ['boolean'],
        ]);

        // If this is set as default, unset other default addresses
        if ($validated['is_default'] ?? false) {
            UserAddress::where('user_id', auth()->id())
                      ->where('id', '!=', $address->id)
                      ->where('is_deleted', false)
                      ->update(['is_default' => false]);
        }

        $address->update($validated);

        return redirect()->route('addresses.index')
            ->with('success', 'Address updated successfully!');
    }

    /**
     * Remove the specified address from storage (soft delete).
     */
    public function destroy(UserAddress $address): RedirectResponse
    {
        // Return 404 if address is already soft deleted
        if ($address->is_deleted) {
            abort(404);
        }

        // Ensure user can only delete their own addresses
        if ($address->user_id !== auth()->id()) {
            abort(403, 'Unauthorized access to address.');
        }

        // Soft delete the address
        $address->update(['is_deleted' => true, 'is_default' => false]);

        return redirect()->route('addresses.index')
            ->with('success', 'Address deleted successfully!');
    }

    /**
     * Set an address as the default address.
     */
    public function setDefault(UserAddress $address): RedirectResponse
    {
        // Return 404 if address is soft deleted
        if ($address->is_deleted) {
            abort(404);
        }

        // Ensure user can only modify their own addresses
        if ($address->user_id !== auth()->id()) {
            abort(403, 'Unauthorized access to address.');
        }

        // Unset all other default addresses for this user
        UserAddress::where('user_id', auth()->id())
                  ->where('is_deleted', false)
                  ->update(['is_default' => false]);

        // Set this address as default
        $address->update(['is_default' => true]);

        return redirect()->route('addresses.index')
            ->with('success', 'Default address updated successfully!');
    }
}
