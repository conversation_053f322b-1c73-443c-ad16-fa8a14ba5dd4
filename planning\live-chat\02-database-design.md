# 🗄️ Live Chat & AI Chatbots - Database Design

## 📋 Database Schema Overview

This document provides a comprehensive database design for the Live Chat & AI Chatbots feature. The schema is optimized for real-time performance, scalability, and data integrity while maintaining compatibility with the existing ChiSolution database structure.

## 🎯 Design Principles

### Core Principles
- **Performance First**: Optimized for real-time queries and high concurrency
- **Scalability**: Designed to handle thousands of concurrent users
- **Data Integrity**: Strong relationships and constraints
- **Flexibility**: Extensible schema for future enhancements
- **Compatibility**: Seamless integration with existing user and role systems

### Performance Considerations
- Strategic indexing for real-time queries
- Partitioning for large chat history tables
- Optimized JSON storage for metadata
- Efficient foreign key relationships
- Read-optimized denormalization where appropriate

## 📊 Entity Relationship Diagram

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     users       │    │   chat_rooms    │    │ chat_messages   │
│                 │    │                 │    │                 │
│ • id (PK)       │    │ • id (PK)       │    │ • id (PK)       │
│ • uuid          │    │ • uuid          │    │ • uuid          │
│ • first_name    │    │ • type          │    │ • chat_room_id  │
│ • last_name     │    │ • status        │    │ • user_id       │
│ • email         │    │ • title         │    │ • message_type  │
│ • role_id       │    │ • visitor_info  │    │ • content       │
│ • is_active     │    │ • metadata      │    │ • metadata      │
│                 │    │ • created_at    │    │ • is_ai_generated│
└─────────────────┘    │ • updated_at    │    │ • ai_confidence │
         │              │ • closed_at     │    │ • created_at    │
         │              └─────────────────┘    │ • updated_at    │
         │                       │             │ • deleted_at    │
         │                       │             └─────────────────┘
         │                       │                      │
         │              ┌─────────────────┐             │
         │              │chat_participants│             │
         │              │                 │             │
         │              │ • id (PK)       │             │
         │              │ • chat_room_id  │◄────────────┘
         │              │ • user_id       │
         │              │ • participant_type│
         │              │ • role          │
         │              │ • joined_at     │
         │              │ • left_at       │
         │              │ • is_active     │
         │              └─────────────────┘
         │                       │
         └───────────────────────┘
```

## 📋 Table Definitions

### 1. Chat Rooms Table

```sql
CREATE TABLE chat_rooms (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    type ENUM('visitor', 'customer', 'internal', 'support') NOT NULL DEFAULT 'visitor',
    status ENUM('active', 'waiting', 'closed', 'archived') NOT NULL DEFAULT 'active',
    title VARCHAR(255) NULL,
    visitor_info JSON NULL COMMENT 'Anonymous visitor information',
    metadata JSON NULL COMMENT 'Additional room metadata',
    priority TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '1=Low, 2=Medium, 3=High, 4=Urgent',
    language CHAR(2) NOT NULL DEFAULT 'en',
    created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    closed_at TIMESTAMP NULL,
    
    INDEX idx_status_updated (status, updated_at),
    INDEX idx_type_status (type, status),
    INDEX idx_priority_created (priority DESC, created_at),
    INDEX idx_language (language)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 2. Chat Messages Table

```sql
CREATE TABLE chat_messages (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    chat_room_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NULL COMMENT 'NULL for anonymous visitors',
    message_type ENUM('text', 'file', 'image', 'system', 'ai', 'emoji') NOT NULL DEFAULT 'text',
    content TEXT NOT NULL,
    metadata JSON NULL COMMENT 'File info, AI context, etc.',
    is_ai_generated BOOLEAN NOT NULL DEFAULT FALSE,
    ai_confidence DECIMAL(3,2) NULL COMMENT 'AI confidence score 0.00-1.00',
    ai_model VARCHAR(50) NULL COMMENT 'AI model used for generation',
    reply_to_message_id BIGINT UNSIGNED NULL COMMENT 'For threaded conversations',
    is_edited BOOLEAN NOT NULL DEFAULT FALSE,
    edit_count TINYINT UNSIGNED NOT NULL DEFAULT 0,
    created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    FOREIGN KEY (chat_room_id) REFERENCES chat_rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (reply_to_message_id) REFERENCES chat_messages(id) ON DELETE SET NULL,
    
    INDEX idx_room_created (chat_room_id, created_at),
    INDEX idx_user_created (user_id, created_at),
    INDEX idx_ai_generated (is_ai_generated, created_at),
    INDEX idx_message_type (message_type),
    INDEX idx_reply_to (reply_to_message_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 3. Chat Participants Table

```sql
CREATE TABLE chat_participants (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    chat_room_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NULL COMMENT 'NULL for anonymous visitors',
    participant_type ENUM('customer', 'staff', 'admin', 'visitor', 'bot') NOT NULL,
    role ENUM('participant', 'moderator', 'observer', 'owner') NOT NULL DEFAULT 'participant',
    display_name VARCHAR(100) NULL COMMENT 'Display name for anonymous users',
    permissions JSON NULL COMMENT 'Participant-specific permissions',
    joined_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    left_at TIMESTAMP NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    last_seen_at TIMESTAMP NULL,
    
    FOREIGN KEY (chat_room_id) REFERENCES chat_rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_room_user (chat_room_id, user_id),
    INDEX idx_room_active (chat_room_id, is_active),
    INDEX idx_user_active (user_id, is_active),
    INDEX idx_participant_type (participant_type),
    INDEX idx_last_seen (last_seen_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 4. Chat Assignments Table

```sql
CREATE TABLE chat_assignments (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    chat_room_id BIGINT UNSIGNED NOT NULL,
    assigned_to BIGINT UNSIGNED NOT NULL COMMENT 'Staff user ID',
    assigned_by BIGINT UNSIGNED NULL COMMENT 'Admin who made assignment',
    assignment_type ENUM('automatic', 'manual', 'transfer', 'escalation') NOT NULL,
    status ENUM('active', 'transferred', 'completed', 'cancelled') NOT NULL DEFAULT 'active',
    workload_score TINYINT UNSIGNED NULL COMMENT 'Staff workload at assignment time',
    notes TEXT NULL COMMENT 'Assignment notes or transfer reason',
    assigned_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    
    FOREIGN KEY (chat_room_id) REFERENCES chat_rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_assigned_to_status (assigned_to, status),
    INDEX idx_room_status (chat_room_id, status),
    INDEX idx_assignment_type (assignment_type),
    INDEX idx_assigned_at (assigned_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 5. Chat Ratings Table

```sql
CREATE TABLE chat_ratings (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    chat_room_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NULL COMMENT 'NULL for anonymous visitors',
    rating TINYINT UNSIGNED NOT NULL CHECK (rating BETWEEN 1 AND 5),
    feedback TEXT NULL,
    rating_categories JSON NULL COMMENT 'Detailed rating breakdown',
    staff_user_id BIGINT UNSIGNED NULL COMMENT 'Staff member being rated',
    is_anonymous BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (chat_room_id) REFERENCES chat_rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (staff_user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    UNIQUE KEY unique_room_rating (chat_room_id, user_id),
    INDEX idx_rating_created (rating, created_at),
    INDEX idx_staff_rating (staff_user_id, rating),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 6. Chat Sessions Table

```sql
CREATE TABLE chat_sessions (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    chat_room_id BIGINT UNSIGNED NOT NULL,
    session_start TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    session_end TIMESTAMP NULL,
    duration_seconds INT UNSIGNED NULL,
    message_count INT UNSIGNED NOT NULL DEFAULT 0,
    participant_count TINYINT UNSIGNED NOT NULL DEFAULT 0,
    ai_message_count INT UNSIGNED NOT NULL DEFAULT 0,
    staff_response_time_avg INT UNSIGNED NULL COMMENT 'Average response time in seconds',
    customer_satisfaction TINYINT UNSIGNED NULL COMMENT 'Rating 1-5',
    resolution_status ENUM('resolved', 'unresolved', 'escalated', 'abandoned') NULL,
    tags JSON NULL COMMENT 'Session tags for categorization',
    
    FOREIGN KEY (chat_room_id) REFERENCES chat_rooms(id) ON DELETE CASCADE,
    
    INDEX idx_session_start (session_start),
    INDEX idx_duration (duration_seconds),
    INDEX idx_resolution_status (resolution_status),
    INDEX idx_satisfaction (customer_satisfaction)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 7. Chat Files Table

```sql
CREATE TABLE chat_files (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    chat_room_id BIGINT UNSIGNED NOT NULL,
    chat_message_id BIGINT UNSIGNED NOT NULL,
    uploaded_by BIGINT UNSIGNED NULL,
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT UNSIGNED NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_hash VARCHAR(64) NOT NULL COMMENT 'SHA-256 hash for deduplication',
    is_image BOOLEAN NOT NULL DEFAULT FALSE,
    is_scanned BOOLEAN NOT NULL DEFAULT FALSE,
    scan_result ENUM('clean', 'infected', 'suspicious', 'error') NULL,
    download_count INT UNSIGNED NOT NULL DEFAULT 0,
    created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL COMMENT 'File retention policy',
    
    FOREIGN KEY (chat_room_id) REFERENCES chat_rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (chat_message_id) REFERENCES chat_messages(id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_room_created (chat_room_id, created_at),
    INDEX idx_message_id (chat_message_id),
    INDEX idx_file_hash (file_hash),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 8. AI Training Data Table

```sql
CREATE TABLE ai_training_data (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    intent VARCHAR(100) NOT NULL,
    input_text TEXT NOT NULL,
    expected_response TEXT NOT NULL,
    language CHAR(2) NOT NULL DEFAULT 'en',
    confidence_threshold DECIMAL(3,2) NOT NULL DEFAULT 0.80,
    category VARCHAR(50) NULL,
    tags JSON NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_intent_language (intent, language),
    INDEX idx_category (category),
    INDEX idx_active (is_active),
    FULLTEXT idx_input_text (input_text)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 9. AI Conversation Logs Table

```sql
CREATE TABLE ai_conversation_logs (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    chat_room_id BIGINT UNSIGNED NOT NULL,
    chat_message_id BIGINT UNSIGNED NOT NULL,
    user_message TEXT NOT NULL,
    ai_response TEXT NOT NULL,
    intent_detected VARCHAR(100) NULL,
    confidence_score DECIMAL(3,2) NOT NULL,
    processing_time_ms INT UNSIGNED NOT NULL,
    model_used VARCHAR(50) NOT NULL,
    was_helpful BOOLEAN NULL COMMENT 'User feedback on AI response',
    escalated_to_human BOOLEAN NOT NULL DEFAULT FALSE,
    escalation_reason VARCHAR(255) NULL,
    created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (chat_room_id) REFERENCES chat_rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (chat_message_id) REFERENCES chat_messages(id) ON DELETE CASCADE,
    
    INDEX idx_room_created (chat_room_id, created_at),
    INDEX idx_confidence (confidence_score),
    INDEX idx_escalated (escalated_to_human),
    INDEX idx_helpful (was_helpful)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 10. Chat Typing Indicators Table

```sql
CREATE TABLE chat_typing_indicators (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    chat_room_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NULL,
    display_name VARCHAR(100) NULL COMMENT 'For anonymous users',
    is_typing BOOLEAN NOT NULL DEFAULT TRUE,
    started_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,

    FOREIGN KEY (chat_room_id) REFERENCES chat_rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

    UNIQUE KEY unique_room_user_typing (chat_room_id, user_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_room_typing (chat_room_id, is_typing)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 11. Chat System Settings Table

```sql
CREATE TABLE chat_system_settings (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    setting_type ENUM('boolean', 'string', 'integer', 'json') NOT NULL DEFAULT 'string',
    description TEXT NULL,
    is_public BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Can be accessed by frontend',
    updated_by BIGINT UNSIGNED NULL,
    created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,

    INDEX idx_setting_key (setting_key),
    INDEX idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 12. Chat Activity Logs Integration

```sql
-- Extend existing activity_logs table for chat activities
-- Chat-specific activity types:
-- 'chat_room_created', 'chat_room_closed', 'chat_message_sent'
-- 'chat_file_uploaded', 'chat_participant_joined', 'chat_participant_left'
-- 'chat_assigned', 'chat_transferred', 'chat_rated'
-- 'chat_system_enabled', 'chat_system_disabled'
-- 'chat_ai_response_generated', 'chat_escalated_to_human'

-- Sample activity log entries for chat
INSERT INTO activity_logs (
    user_id, user_email, user_name, activity_type, activity_description,
    status, request_data, response_data, created_at
) VALUES (
    1, '<EMAIL>', 'Admin User', 'chat_system_toggle',
    'Chat system enabled by admin', 'success',
    '{"enabled": true, "previous_state": false}',
    '{"system_status": "active", "timestamp": "2024-01-15T10:30:00Z"}',
    NOW()
);
```

## 🔧 Database Optimization

### Partitioning Strategy

```sql
-- Partition chat_messages by month for better performance
ALTER TABLE chat_messages 
PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    PARTITION p202403 VALUES LESS THAN (202404),
    -- Add partitions as needed
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### Performance Indexes

```sql
-- Composite indexes for common queries
CREATE INDEX idx_chat_messages_room_user_created 
ON chat_messages(chat_room_id, user_id, created_at);

CREATE INDEX idx_chat_participants_room_type_active 
ON chat_participants(chat_room_id, participant_type, is_active);

CREATE INDEX idx_chat_assignments_staff_status_assigned 
ON chat_assignments(assigned_to, status, assigned_at);
```

### Data Retention Policies

```sql
-- Automated cleanup procedures
DELIMITER //
CREATE EVENT cleanup_old_typing_indicators
ON SCHEDULE EVERY 1 MINUTE
DO
BEGIN
    DELETE FROM chat_typing_indicators 
    WHERE expires_at < NOW();
END //

CREATE EVENT cleanup_old_chat_files
ON SCHEDULE EVERY 1 DAY
DO
BEGIN
    DELETE FROM chat_files 
    WHERE expires_at IS NOT NULL 
    AND expires_at < NOW();
END //
DELIMITER ;
```

## 📊 Sample Data & Relationships

### Example Chat Room with Messages

```sql
-- Insert sample chat room
INSERT INTO chat_rooms (uuid, type, status, title, language) 
VALUES ('550e8400-e29b-41d4-a716-************', 'visitor', 'active', 'Product Inquiry', 'en');

-- Insert participants
INSERT INTO chat_participants (chat_room_id, user_id, participant_type, display_name) 
VALUES (1, NULL, 'visitor', 'Anonymous User'), (1, 5, 'staff', NULL);

-- Insert messages
INSERT INTO chat_messages (uuid, chat_room_id, user_id, content, message_type) 
VALUES 
('msg-001', 1, NULL, 'Hello, I need help with your products', 'text'),
('msg-002', 1, 5, 'Hi! I\'d be happy to help. What product are you interested in?', 'text');
```

---

*This database design provides a robust foundation for the Live Chat & AI Chatbots feature with optimized performance, comprehensive relationships, and scalability for future growth.*
