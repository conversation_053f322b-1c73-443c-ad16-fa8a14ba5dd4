@echo off
REM =============================================================================
REM ChiSolution Digital Agency - Local Development Optimization Script
REM =============================================================================

echo 🚀 Optimizing Local Development Environment...

REM Check if we're in the correct directory
if not exist "artisan" (
    echo ❌ artisan file not found. Are you in the Laravel project root?
    pause
    exit /b 1
)

echo ✅ Installing/updating Composer dependencies...
call composer install

echo ✅ Installing/updating NPM dependencies...
call npm install

echo ✅ Building development assets...
call npm run dev

echo ✅ Generating application key (if needed)...
php artisan key:generate

echo ✅ Running database migrations...
php artisan migrate

echo ✅ Seeding database with sample data...
php artisan db:seed

echo ✅ Clearing all caches...
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

echo ✅ Creating storage symlink...
php artisan storage:link

echo ✅ Setting up queue tables...
php artisan queue:table
php artisan migrate

echo 🎉 Local development environment optimized!
echo.
echo Development server commands:
echo - Start server: php artisan serve
echo - Watch assets: npm run dev
echo - Run tests: php artisan test
echo - Queue worker: php artisan queue:work
echo.
pause
