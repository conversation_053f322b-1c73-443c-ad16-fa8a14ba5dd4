<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ChatApiUsageTracking
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $startTime = microtime(true);
        
        $response = $next($request);
        
        $endTime = microtime(true);
        $responseTime = ($endTime - $startTime) * 1000; // Convert to milliseconds
        
        $this->trackApiCall($request, $response, $responseTime);
        
        // Add performance headers
        $response->headers->set('X-Response-Time', round($responseTime, 2) . 'ms');
        $response->headers->set('X-API-Version', 'v1');
        
        return $response;
    }

    /**
     * Track API call for analytics.
     */
    protected function trackApiCall($request, $response, float $responseTime): void
    {
        if (!config('chat.api.usage_tracking', true)) {
            return;
        }

        $data = [
            'timestamp' => now()->toISOString(),
            'method' => $request->method(),
            'endpoint' => $request->path(),
            'status_code' => $response->getStatusCode(),
            'response_time_ms' => round($responseTime, 2),
            'user_id' => auth()->id(),
            'application' => $request->header('X-Application-Name', 'unknown'),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'request_size' => strlen($request->getContent()),
            'response_size' => strlen($response->getContent()),
        ];

        // Store in cache for batch processing
        $this->storeUsageData($data);
        
        // Log errors for monitoring
        if ($response->getStatusCode() >= 400) {
            $this->logApiError($request, $response, $data);
        }
        
        // Check for performance issues
        if ($responseTime > config('chat.monitoring.alert_thresholds.response_time_ms', 2000)) {
            $this->logSlowResponse($request, $responseTime);
        }
    }

    /**
     * Store usage data in cache for batch processing.
     */
    protected function storeUsageData(array $data): void
    {
        $cacheKey = 'chat_api_usage_' . date('Y-m-d-H');
        
        $usage = Cache::get($cacheKey, []);
        $usage[] = $data;
        
        // Limit cache size to prevent memory issues
        if (count($usage) > 1000) {
            $usage = array_slice($usage, -1000);
        }
        
        Cache::put($cacheKey, $usage, 3600); // 1 hour
    }

    /**
     * Log API errors for monitoring.
     */
    protected function logApiError($request, $response, array $data): void
    {
        Log::warning('Chat API Error', [
            'endpoint' => $data['endpoint'],
            'method' => $data['method'],
            'status_code' => $data['status_code'],
            'response_time_ms' => $data['response_time_ms'],
            'user_id' => $data['user_id'],
            'application' => $data['application'],
            'ip_address' => $data['ip_address'],
            'request_body' => $request->getContent(),
            'response_body' => $response->getContent(),
        ]);
    }

    /**
     * Log slow responses for performance monitoring.
     */
    protected function logSlowResponse($request, float $responseTime): void
    {
        Log::warning('Chat API Slow Response', [
            'endpoint' => $request->path(),
            'method' => $request->method(),
            'response_time_ms' => round($responseTime, 2),
            'user_id' => auth()->id(),
            'application' => $request->header('X-Application-Name', 'unknown'),
            'threshold_ms' => config('chat.monitoring.alert_thresholds.response_time_ms', 2000),
        ]);
    }

    /**
     * Get API usage statistics.
     */
    public static function getUsageStatistics(string $period = 'hour'): array
    {
        $cacheKey = match($period) {
            'hour' => 'chat_api_usage_' . date('Y-m-d-H'),
            'day' => 'chat_api_usage_' . date('Y-m-d') . '*',
            'week' => 'chat_api_usage_' . date('Y-W') . '*',
            default => 'chat_api_usage_' . date('Y-m-d-H'),
        };

        if ($period === 'hour') {
            $usage = Cache::get($cacheKey, []);
        } else {
            // For day/week, we'd need to aggregate multiple cache keys
            $usage = []; // Simplified for now
        }

        if (empty($usage)) {
            return [
                'total_requests' => 0,
                'average_response_time' => 0,
                'error_rate' => 0,
                'requests_by_endpoint' => [],
                'requests_by_application' => [],
            ];
        }

        $totalRequests = count($usage);
        $totalResponseTime = array_sum(array_column($usage, 'response_time_ms'));
        $errors = array_filter($usage, fn($item) => $item['status_code'] >= 400);
        
        $endpointCounts = [];
        $applicationCounts = [];
        
        foreach ($usage as $item) {
            $endpoint = $item['endpoint'];
            $application = $item['application'];
            
            $endpointCounts[$endpoint] = ($endpointCounts[$endpoint] ?? 0) + 1;
            $applicationCounts[$application] = ($applicationCounts[$application] ?? 0) + 1;
        }

        return [
            'total_requests' => $totalRequests,
            'average_response_time' => $totalRequests > 0 ? round($totalResponseTime / $totalRequests, 2) : 0,
            'error_rate' => $totalRequests > 0 ? round((count($errors) / $totalRequests) * 100, 2) : 0,
            'requests_by_endpoint' => $endpointCounts,
            'requests_by_application' => $applicationCounts,
            'period' => $period,
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Clear usage data (for cleanup jobs).
     */
    public static function clearOldUsageData(int $hoursOld = 24): int
    {
        $cleared = 0;
        $cutoffTime = now()->subHours($hoursOld);
        
        // Get all cache keys for API usage
        $pattern = 'chat_api_usage_*';
        
        // This is a simplified version - in production you'd use Redis SCAN
        for ($i = 0; $i < $hoursOld; $i++) {
            $time = $cutoffTime->copy()->addHours($i);
            $cacheKey = 'chat_api_usage_' . $time->format('Y-m-d-H');
            
            if (Cache::has($cacheKey)) {
                Cache::forget($cacheKey);
                $cleared++;
            }
        }
        
        return $cleared;
    }

    /**
     * Export usage data for analysis.
     */
    public static function exportUsageData(string $startDate, string $endDate): array
    {
        $start = \Carbon\Carbon::parse($startDate);
        $end = \Carbon\Carbon::parse($endDate);
        
        $allData = [];
        
        while ($start <= $end) {
            $cacheKey = 'chat_api_usage_' . $start->format('Y-m-d-H');
            $hourlyData = Cache::get($cacheKey, []);
            
            $allData = array_merge($allData, $hourlyData);
            $start->addHour();
        }
        
        return $allData;
    }
}
