<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\ShoppingCart;
use App\Services\ActivityLogger;
use App\Services\VisitorAnalytics;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cache;
use Illuminate\View\View;

class CartController extends Controller
{
    protected ActivityLogger $activityLogger;
    protected VisitorAnalytics $visitorAnalytics;

    public function __construct(ActivityLogger $activityLogger, VisitorAnalytics $visitorAnalytics)
    {
        $this->activityLogger = $activityLogger;
        $this->visitorAnalytics = $visitorAnalytics;
    }

    /**
     * Display the shopping cart.
     */
    public function index(string $locale): View
    {
        $cart = $this->getOrCreateCart();
        $cart->load(['items.product', 'items.productVariant']);
        
        return view('pages.cart.index', compact('cart'));
    }

    /**
     * Add a product to the cart.
     */
    public function add(string $locale, Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'variant_id' => 'nullable|exists:product_variants,id',
            'quantity' => 'required|integer|min:1|max:99',
        ]);

        $product = Product::findOrFail($request->product_id);
        $variant = $request->variant_id ? ProductVariant::findOrFail($request->variant_id) : null;

        // Check if product is active and in stock
        if (!$product->is_active) {
            $this->activityLogger->logCartActivity(
                'add_item',
                null,
                [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'variant_id' => $variant?->id,
                    'quantity' => $request->quantity,
                    'failure_reason' => 'product_inactive',
                ],
                false,
                'Product is no longer available'
            );

            return response()->json([
                'success' => false,
                'message' => 'This product is no longer available.',
            ], 400);
        }

        // Check inventory if tracking is enabled
        if ($product->track_inventory) {
            $availableQuantity = $variant ? $variant->inventory_quantity : $product->inventory_quantity;

            if ($availableQuantity < $request->quantity) {
                $this->activityLogger->logCartActivity(
                    'add_item',
                    null,
                    [
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'variant_id' => $variant?->id,
                        'quantity' => $request->quantity,
                        'available_quantity' => $availableQuantity,
                        'failure_reason' => 'insufficient_stock',
                    ],
                    false,
                    'Insufficient stock available'
                );

                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient stock available.',
                ], 400);
            }
        }

        try {
            $cart = $this->getOrCreateCart();
            $cartItem = $cart->addProduct($product, $request->quantity, $variant);

            // Log successful cart addition
            $this->activityLogger->logCartActivity(
                'add_item',
                $cart->id,
                [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'product_sku' => $product->sku,
                    'variant_id' => $variant?->id,
                    'variant_name' => $variant?->name,
                    'quantity' => $cartItem->quantity,
                    'unit_price' => $cartItem->price,
                    'total_price' => $cartItem->total,
                    'cart_total' => $cart->total,
                    'cart_item_count' => $cart->item_count,
                ]
            );

            // Track cart addition in visitor analytics
            $this->visitorAnalytics->trackFormInteraction(
                'add_to_cart',
                'submit',
                true,
                [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'variant_id' => $variant?->id,
                    'quantity' => $cartItem->quantity,
                    'cart_total' => $cart->total,
                    'cart_item_count' => $cart->item_count,
                ]
            );

            // Track journey step
            $this->visitorAnalytics->trackJourneyStep(
                'Product Added to Cart',
                'cart_addition',
                [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'product_category' => $product->categories->first()?->name,
                    'cart_value' => $cart->total,
                    'cart_items' => $cart->item_count,
                ]
            );

            // Update lead score for cart addition
            $this->visitorAnalytics->updateLeadScore(
                'cart_addition',
                ['cart_value' => $cart->total, 'product_category' => $product->categories->first()?->name]
            );

            // Clear cart count cache
            $this->clearCartCountCache();

            return response()->json([
                'success' => true,
                'message' => 'Product added to cart successfully!',
                'cart_count' => $cart->item_count,
                'cart' => [
                    'item_count' => $cart->item_count,
                    'formatted_total' => $cart->formatted_total,
                ],
                'item' => [
                    'id' => $cartItem->id,
                    'name' => $cartItem->name,
                    'quantity' => $cartItem->quantity,
                    'formatted_price' => $cartItem->formatted_price,
                    'formatted_total' => $cartItem->formatted_total,
                ],
            ]);
        } catch (\Exception $e) {
            \Log::error('Error adding product to cart', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while adding the product to cart.',
            ], 500);
        }
    }

    /**
     * Update cart item quantity.
     */
    public function update(string $locale, Request $request, $itemId)
    {
        $request->validate([
            'quantity' => 'required|integer|min:0|max:99',
        ]);

        $cart = $this->getOrCreateCart();
        $cartItem = $cart->items()->findOrFail($itemId);

        if ($request->quantity == 0) {
            $cartItem->delete();
            $cart->recalculateTotal();

            // Clear cart count cache
            $this->clearCartCountCache();

            // No need for fresh() - cart is already updated after recalculateTotal()
            return response()->json([
                'success' => true,
                'message' => 'Item removed from cart.',
                'cart' => [
                    'item_count' => $cart->item_count,
                    'formatted_total' => $cart->formatted_total,
                    'formatted_subtotal' => $cart->formatted_subtotal,
                    'formatted_tax_amount' => $cart->formatted_tax_amount,
                ],
            ]);
        }

        // Check inventory if tracking is enabled
        $product = $cartItem->product;
        $variant = $cartItem->productVariant;

        if ($product->track_inventory) {
            $availableQuantity = $variant ? $variant->inventory_quantity : $product->inventory_quantity;

            if ($availableQuantity < $request->quantity) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient stock available.',
                ], 400);
            }
        }

        $cartItem->updateQuantity($request->quantity);
        $cart->recalculateTotal();

        // No need for fresh() - cart is already updated after recalculateTotal()
        return response()->json([
            'success' => true,
            'message' => 'Cart updated successfully!',
            'cart' => [
                'item_count' => $cart->item_count,
                'formatted_total' => $cart->formatted_total,
                'formatted_subtotal' => $cart->formatted_subtotal,
                'formatted_tax_amount' => $cart->formatted_tax_amount,
            ],
            'item' => [
                'id' => $cartItem->id,
                'quantity' => $cartItem->quantity,
                'formatted_total' => $cartItem->formatted_total,
            ],
        ]);
    }

    /**
     * Remove an item from the cart.
     */
    public function remove(string $locale, $itemId)
    {
        $cart = $this->getOrCreateCart();
        $cartItem = $cart->items()->findOrFail($itemId);
        
        $cartItem->delete();
        $cart->recalculateTotal();

        return response()->json([
            'success' => true,
            'message' => 'Item removed from cart.',
            'cart' => [
                'item_count' => $cart->item_count,
                'formatted_total' => $cart->formatted_total,
            ],
        ]);
    }

    /**
     * Clear the entire cart.
     */
    public function clear(string $locale)
    {
        $cart = $this->getOrCreateCart();
        $cart->clear();

        return response()->json([
            'success' => true,
            'message' => 'Cart cleared successfully!',
            'cart' => [
                'item_count' => 0,
                'formatted_total' => 'R0.00',
            ],
        ]);
    }

    /**
     * Get cart count for header display.
     * Optimized with caching to reduce database load.
     */
    public function count(string $locale = 'en')
    {
        try {
            $cacheKey = Auth::check()
                ? 'cart_count_user_' . Auth::id()
                : 'cart_count_session_' . Session::getId();

            // Use shorter TTL for session-based caches to prevent memory leaks
            $ttl = Auth::check() ? 60 : 30; // 60 seconds for users, 30 for sessions
            $count = Cache::remember($cacheKey, $ttl, function () {
                // Use direct database query to avoid loading full cart model
                if (Auth::check()) {
                    return \DB::table('shopping_carts')
                        ->join('cart_items', 'shopping_carts.id', '=', 'cart_items.cart_id')
                        ->where('shopping_carts.user_id', Auth::id())
                        ->where('shopping_carts.expires_at', '>', now())
                        ->sum('cart_items.quantity') ?: 0;
                } else {
                    $sessionId = Session::getId();
                    return \DB::table('shopping_carts')
                        ->join('cart_items', 'shopping_carts.id', '=', 'cart_items.cart_id')
                        ->where('shopping_carts.session_id', $sessionId)
                        ->where('shopping_carts.expires_at', '>', now())
                        ->sum('cart_items.quantity') ?: 0;
                }
            });

            return response()->json([
                'count' => (int) $count,
                'cached' => true,
            ]);
        } catch (\Exception $e) {
            \Log::error('Cart count error: ' . $e->getMessage());
            return response()->json([
                'count' => 0,
                'error' => 'Unable to fetch cart count'
            ]);
        }
    }

    /**
     * Clear cart count cache.
     * Also cleans up orphaned session cache entries.
     */
    private function clearCartCountCache()
    {
        $cacheKey = Auth::check()
            ? 'cart_count_user_' . Auth::id()
            : 'cart_count_session_' . Session::getId();

        Cache::forget($cacheKey);

        // Clean up orphaned session cache entries periodically
        if (!Auth::check() && rand(1, 100) <= 5) { // 5% chance
            $this->cleanupOrphanedSessionCaches();
        }
    }

    /**
     * Clean up orphaned session cache entries.
     * This prevents memory leaks from regenerated sessions.
     */
    private function cleanupOrphanedSessionCaches()
    {
        try {
            // Get all active session IDs from shopping_carts table
            $activeSessions = \DB::table('shopping_carts')
                ->whereNotNull('session_id')
                ->where('expires_at', '>', now())
                ->pluck('session_id')
                ->unique()
                ->toArray();

            // Get all cache keys that match the session pattern
            $cachePrefix = 'cart_count_session_';

            // Note: This is a simplified approach. In production, you might want to use
            // a more sophisticated cache tagging system or store cache keys in database

            // For now, we'll just set a reasonable TTL on session caches
            // and let them expire naturally

        } catch (\Exception $e) {
            \Log::warning('Failed to cleanup orphaned session caches: ' . $e->getMessage());
        }
    }

    /**
     * Sync cart state between client and server.
     */
    public function sync(Request $request)
    {
        try {
            $localState = $request->input('local_state', []);
            $lastSync = $request->input('last_sync');

            // Get current server cart
            $cart = $this->getOrCreateCart();

            // Build server state
            $serverState = [
                'items' => $cart->items->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'product_id' => $item->product_id,
                        'variant_id' => $item->product_variant_id,
                        'quantity' => $item->quantity,
                        'unit_price' => $item->price,
                        'total_price' => $item->total,
                        'product' => [
                            'id' => $item->product->id,
                            'name' => $item->product->name,
                            'slug' => $item->product->slug,
                            'price' => $item->product->price,
                            'image' => $item->product->featured_image,
                        ],
                        'variant' => $item->productVariant ? [
                            'id' => $item->productVariant->id,
                            'name' => $item->productVariant->name,
                            'price' => $item->productVariant->price,
                        ] : null,
                    ];
                })->toArray(),
                'count' => $cart->item_count,
                'subtotal' => $cart->subtotal,
                'total' => $cart->total,
                'tax_amount' => $cart->tax_amount,
                'shipping_amount' => $cart->shipping_amount,
                'discount_amount' => $cart->discount_amount,
                'last_updated' => $cart->updated_at->timestamp * 1000, // Convert to JS timestamp
                'needs_sync' => false
            ];

            // If local state has newer changes, merge them
            if (!empty($localState) && isset($localState['last_updated'])) {
                $localTimestamp = $localState['last_updated'];
                $serverTimestamp = $cart->updated_at->timestamp * 1000;

                // If local state is newer, apply local changes to server
                if ($localTimestamp > $serverTimestamp) {
                    $this->applyLocalChangesToServer($cart, $localState);

                    // Rebuild server state after changes
                    $cart = $cart->fresh();
                    $serverState = [
                        'items' => $cart->items->map(function ($item) {
                            return [
                                'id' => $item->id,
                                'product_id' => $item->product_id,
                                'variant_id' => $item->product_variant_id,
                                'quantity' => $item->quantity,
                                'unit_price' => $item->price,
                                'total_price' => $item->total,
                                'product' => [
                                    'id' => $item->product->id,
                                    'name' => $item->product->name,
                                    'slug' => $item->product->slug,
                                    'price' => $item->product->price,
                                    'image' => $item->product->featured_image,
                                ],
                                'variant' => $item->productVariant ? [
                                    'id' => $item->productVariant->id,
                                    'name' => $item->productVariant->name,
                                    'price' => $item->productVariant->price,
                                ] : null,
                            ];
                        })->toArray(),
                        'count' => $cart->item_count,
                        'subtotal' => $cart->subtotal,
                        'total' => $cart->total,
                        'tax_amount' => $cart->tax_amount,
                        'shipping_amount' => $cart->shipping_amount,
                        'discount_amount' => $cart->discount_amount,
                        'last_updated' => $cart->updated_at->timestamp * 1000,
                        'needs_sync' => false
                    ];
                }
            }

            // Log successful sync only in specific conditions to avoid log spam
            $shouldLogSuccess = $this->shouldLogSyncSuccess($request, $serverState);
            if ($shouldLogSuccess) {
                $this->activityLogger->logActivity(
                    'api_cart_sync_recovered',
                    'Cart synchronization successful after previous failures',
                    'success',
                    null,
                    [
                        'items_count' => count($serverState['items'] ?? []),
                        'total_amount' => $serverState['total'] ?? 0,
                        'user_type' => Auth::check() ? 'authenticated' : 'guest',
                        'recovery_sync' => true,
                    ],
                    [
                        'sync_successful' => true,
                        'system_recovered' => true,
                    ],
                    10 // Low risk for successful recovery
                );
            }

            return response()->json($serverState);

        } catch (\Exception $e) {
            \Log::error('Cart sync error: ' . $e->getMessage(), [
                'request' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            // Track failure count for recovery detection
            $cacheKey = 'cart_sync_failures_' . (Auth::id() ?? Session::getId());
            $failureCount = Cache::increment($cacheKey, 1);
            Cache::put($cacheKey, $failureCount, 3600); // Expire in 1 hour

            // Log cart sync failure in activity logs for monitoring
            $this->activityLogger->logActivity(
                'api_cart_sync_failed',
                'Cart synchronization failed: ' . $e->getMessage(),
                'failed',
                $e->getMessage(),
                [
                    'local_state_items_count' => count($localState['items'] ?? []),
                    'local_state_total' => $localState['total'] ?? 0,
                    'last_sync' => $lastSync,
                    'error_type' => get_class($e),
                    'user_type' => Auth::check() ? 'authenticated' : 'guest',
                    'consecutive_failures' => $failureCount,
                ],
                [
                    'sync_successful' => false,
                    'error_logged' => true,
                    'requires_manual_intervention' => $failureCount >= 3,
                    'system_degraded' => $failureCount >= 5,
                ],
                min(75 + ($failureCount * 5), 100) // Increase risk with consecutive failures
            );

            return response()->json([
                'error' => 'Sync failed',
                'message' => 'Unable to sync cart. Please refresh the page.'
            ], 500);
        }
    }

    /**
     * Determine if we should log this successful sync.
     * Only log in specific conditions to avoid spam.
     */
    protected function shouldLogSyncSuccess(Request $request, array $serverState): bool
    {
        $cacheKey = 'cart_sync_failures_' . (Auth::id() ?? Session::getId());

        // Check if there were recent failures for this user/session
        $recentFailures = Cache::get($cacheKey, 0);

        // Log success if:
        // 1. There were recent failures (recovery scenario)
        // 2. This is the first sync of the session (session start)
        // 3. Cart has high value (business critical)
        $isRecovery = $recentFailures > 0;
        $isFirstSync = !Cache::has('cart_sync_success_' . (Auth::id() ?? Session::getId()));
        $isHighValue = ($serverState['total'] ?? 0) > 1000; // High value cart

        if ($isRecovery || $isFirstSync || $isHighValue) {
            // Clear failure count on successful sync
            Cache::forget($cacheKey);

            // Mark that we've had a successful sync (expires in 1 hour)
            Cache::put('cart_sync_success_' . (Auth::id() ?? Session::getId()), true, 3600);

            return true;
        }

        return false;
    }

    /**
     * Apply local changes to server cart.
     */
    private function applyLocalChangesToServer($cart, $localState)
    {
        // This is a simplified merge strategy
        // In a production app, you might want more sophisticated conflict resolution

        if (isset($localState['items']) && is_array($localState['items'])) {
            // Clear existing items and rebuild from local state
            $cart->items()->delete();

            foreach ($localState['items'] as $localItem) {
                if (isset($localItem['product_id']) && isset($localItem['quantity'])) {
                    $product = \App\Models\Product::find($localItem['product_id']);
                    $variant = isset($localItem['variant_id']) ?
                        \App\Models\ProductVariant::find($localItem['variant_id']) : null;

                    if ($product) {
                        $cart->addProduct($product, $localItem['quantity'], $variant);
                    }
                }
            }
        }
    }

    /**
     * Apply a coupon code.
     */
    public function applyCoupon(string $locale, Request $request)
    {
        $request->validate([
            'coupon_code' => 'required|string|max:50',
        ]);

        $cart = $this->getOrCreateCart();

        if ($cart->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'Your cart is empty.',
            ]);
        }

        $couponCode = strtoupper(trim($request->coupon_code));

        // Find the coupon (don't use active scope to allow checking expired coupons)
        $coupon = \App\Models\Coupon::where('code', $couponCode)
            ->where('is_deleted', false)
            ->first();

        if (!$coupon) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid coupon code.',
            ]);
        }

        // Check if coupon is valid
        if (!$coupon->isValid()) {
            return response()->json([
                'success' => false,
                'message' => 'This coupon is no longer valid.',
            ]);
        }

        // Check user-specific restrictions
        if (Auth::check() && !$coupon->canBeUsedBy(Auth::user())) {
            return response()->json([
                'success' => false,
                'message' => 'You have already used this coupon the maximum number of times.',
            ]);
        }

        // Prepare cart items for coupon validation
        $cartItems = $cart->items->map(function ($item) {
            return [
                'product_id' => $item->product_id,
                'category_ids' => $item->product->categories->pluck('id')->toArray(),
                'total' => $item->total,
            ];
        })->toArray();

        // Calculate discount
        $discountAmount = $coupon->calculateDiscount($cart->subtotal, $cartItems);

        if ($discountAmount <= 0) {
            return response()->json([
                'success' => false,
                'message' => 'This coupon is not applicable to items in your cart.',
            ]);
        }

        // Apply coupon to cart
        $cart->coupon_id = $coupon->uuid;
        $cart->coupon_code = $coupon->code;
        $cart->discount_amount = $discountAmount;
        $cart->recalculateTotal();

        return response()->json([
            'success' => true,
            'message' => 'Coupon applied successfully!',
            'cart' => [
                'formatted_subtotal' => $cart->formatted_subtotal,
                'formatted_discount_amount' => $cart->formatted_discount_amount,
                'formatted_total' => $cart->formatted_total,
                'coupon_code' => $coupon->code,
                'discount_amount' => $discountAmount,
            ],
        ]);
    }

    /**
     * Remove applied coupon.
     */
    public function removeCoupon(string $locale, Request $request)
    {
        $cart = $this->getOrCreateCart();

        if (!$cart->coupon_id) {
            return response()->json([
                'success' => false,
                'message' => 'No coupon is currently applied.',
            ]);
        }

        $cart->coupon_id = null;
        $cart->coupon_code = null;
        $cart->discount_amount = 0;
        $cart->recalculateTotal();

        return response()->json([
            'success' => true,
            'message' => 'Coupon removed successfully.',
            'cart' => [
                'formatted_subtotal' => $cart->formatted_subtotal,
                'formatted_discount_amount' => $cart->formatted_discount_amount,
                'formatted_total' => $cart->formatted_total,
            ],
        ]);
    }

    /**
     * Get or create a shopping cart for the current user/session.
     */
    protected function getOrCreateCart(): ShoppingCart
    {
        if (Auth::check()) {
            // For authenticated users, find or create cart by user_id
            $cart = ShoppingCart::active()
                ->where('user_id', Auth::id())
                ->first();
                
            if (!$cart) {
                $cart = ShoppingCart::create([
                    'user_id' => Auth::id(),
                    'currency' => 'ZAR',
                ]);
            }
        } else {
            // For guest users, find or create cart by session_id
            $sessionId = Session::getId();
            $cart = ShoppingCart::active()
                ->where('session_id', $sessionId)
                ->first();
                
            if (!$cart) {
                $cart = ShoppingCart::create([
                    'session_id' => $sessionId,
                    'currency' => 'ZAR',
                ]);
            }
        }

        // Extend cart expiration
        $cart->extendExpiration();
        
        return $cart;
    }

    /**
     * Merge guest cart with user cart after login.
     */
    public function mergeGuestCart()
    {
        if (!Auth::check()) {
            return;
        }

        $sessionId = Session::getId();
        $guestCart = ShoppingCart::active()
            ->where('session_id', $sessionId)
            ->whereNull('user_id')
            ->first();

        if (!$guestCart || $guestCart->isEmpty()) {
            return;
        }

        $userCart = $this->getOrCreateCart();

        // Merge guest cart items into user cart
        foreach ($guestCart->items as $guestItem) {
            $userCart->addProduct(
                $guestItem->product,
                $guestItem->quantity,
                $guestItem->productVariant
            );
        }

        // Delete guest cart
        $guestCart->delete();
    }
}
