<?php

namespace App\Http\Middleware;

use App\Services\ActivityLogger;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response as BaseResponse;

class ActivityLoggingMiddleware
{
    protected ActivityLogger $activityLogger;

    public function __construct(ActivityLogger $activityLogger)
    {
        $this->activityLogger = $activityLogger;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): BaseResponse
    {
        // Skip if already logged or should be skipped
        if ($request->attributes->get('activity_logged', false) || $this->shouldSkipLogging($request)) {
            return $next($request);
        }

        $startTime = microtime(true);

        try {
            $response = $next($request);
            $endTime = microtime(true);

            // Update last seen timestamp for authenticated users
            $this->updateUserLastSeen();

            // Queue activity logging for background processing
            $this->queueActivityLogging($request, $response, $endTime - $startTime);

            // Mark as logged to prevent double-logging
            $request->attributes->set('activity_logged', true);

            return $response;
        } catch (\Throwable $exception) {
            $endTime = microtime(true);

            // Create a response object for the exception
            $statusCode = $this->getExceptionStatusCode($exception);
            $response = response('', $statusCode);

            // Queue failed activity logging
            $this->queueActivityLogging($request, $response, $endTime - $startTime);

            // Mark as logged to prevent double-logging
            $request->attributes->set('activity_logged', true);

            // Re-throw the exception so Laravel can handle it normally
            throw $exception;
        }
    }

    /**
     * Check if logging should be skipped for this request.
     */
    private function shouldSkipLogging(Request $request): bool
    {
        $path = $request->path();

        // Skip high-frequency, low-value routes
        $skipPatterns = [
            'api/cart/count', 'api/cart/sync', '*.css', '*.js', '*.png',
            '*.jpg', '*.jpeg', '*.gif', '*.svg', '*.ico', '*.woff*',
            'livewire/*', '_debugbar/*', 'telescope/*', 'horizon/*'
        ];

        foreach ($skipPatterns as $pattern) {
            if (fnmatch($pattern, $path)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Queue activity logging for background processing.
     */
    private function queueActivityLogging(Request $request, BaseResponse $response, float $processingTime): void
    {
        try {
            // Use shutdown function for async processing
            if (function_exists('fastcgi_finish_request')) {
                register_shutdown_function(function() use ($request, $response, $processingTime) {
                    $this->logActivity($request, $response, $processingTime);
                });
            } else {
                // Fallback: log with timeout protection
                $this->logActivityWithTimeout($request, $response, $processingTime);
            }
        } catch (\Exception $e) {
            // Never let logging break the request
            \Log::error('Activity logging queue failed: ' . $e->getMessage());
        }
    }

    /**
     * Log activity with timeout protection.
     */
    private function logActivityWithTimeout(Request $request, BaseResponse $response, float $processingTime): void
    {
        $loggingStart = microtime(true);
        $maxLoggingTime = 0.3; // 300ms max for logging

        try {
            // Check timeout before processing
            if ((microtime(true) - $loggingStart) > $maxLoggingTime) {
                return;
            }

            $this->logActivity($request, $response, $processingTime);
        } catch (\Exception $e) {
            \Log::error('Activity logging with timeout failed: ' . $e->getMessage());
        }
    }

    /**
     * Log the customer dashboard activity.
     */
    protected function logActivity(Request $request, BaseResponse $response, float $processingTime): void
    {
        try {
            $routeName = $request->route()?->getName();
            $method = $request->method();
            $uri = $request->getRequestUri();
            $statusCode = $response->getStatusCode();

            // Determine activity type and description based on route and method
            $activityData = $this->determineActivityType($request, $response);

            if (!$activityData) {
                return; // Skip logging for certain routes
            }

            $success = $statusCode >= 200 && $statusCode < 400;
            $failureReason = $success ? null : $this->getFailureReason($statusCode, $response);

            // Log the activity
            $this->activityLogger->logActivity(
                $activityData['type'],
                $activityData['description'],
                $success ? 'success' : 'failed',
                $failureReason,
                [
                    'http_method' => $method,
                    'status_code' => $statusCode,
                    'processing_time_ms' => round($processingTime * 1000, 2),
                    'route_name' => $routeName,
                    'uri' => $uri,
                    'request_size' => strlen($request->getContent()),
                    'user_role' => Auth::user()?->role?->name,
                ],
                [
                    'response_successful' => $success,
                    'response_size' => $this->getResponseSize($response),
                    'redirect_location' => $response instanceof \Illuminate\Http\RedirectResponse
                        ? $response->getTargetUrl() : null,
                ],
                null, // Let the service calculate risk score
                Auth::user() // Pass the current user (can be null)
            );

        } catch (\Exception $e) {
            // Don't let logging errors break the application
            \Log::warning('Activity logging failed', [
                'error' => $e->getMessage(),
                'route' => $request->route()?->getName(),
                'user_id' => Auth::id(),
            ]);
        }
    }

    /**
     * Determine activity type and description based on request.
     */
    protected function determineActivityType(Request $request, BaseResponse $response): ?array
    {
        $routeName = $request->route()?->getName();
        $method = $request->method();
        $uri = $request->getRequestUri();

        // Skip certain routes that shouldn't be logged (very high frequency, low value)
        $skipRoutes = [
            'api.cart.sync', // Too frequent - only log failures manually in controller
        ];

        if (in_array($routeName, $skipRoutes)) {
            return null;
        }

        // Dashboard access
        if ($routeName === 'dashboard') {
            return [
                'type' => 'customer_dashboard_access',
                'description' => 'Accessed customer dashboard'
            ];
        }

        // Profile management
        if (str_starts_with($routeName, 'profile.')) {
            return $this->getProfileActivityData($routeName, $method);
        }

        // Project applications
        if (str_starts_with($routeName, 'project-applications.')) {
            return $this->getProjectApplicationActivityData($routeName, $method, $request);
        }

        // Orders
        if (str_starts_with($routeName, 'orders.')) {
            return $this->getOrderActivityData($routeName, $method, $request);
        }

        // Projects (for clients)
        if (str_starts_with($routeName, 'projects.')) {
            return $this->getProjectActivityData($routeName, $method, $request);
        }

        // Cart activities (both web and API)
        if (str_starts_with($routeName, 'cart.') || str_starts_with($routeName, 'api.cart.')) {
            return $this->getCartActivityData($routeName, $method);
        }

        // Admin dashboard activities
        if (str_starts_with($routeName, 'admin.')) {
            return $this->getAdminActivityData($routeName, $method, $request);
        }

        // Dashboard search
        if ($routeName === 'dashboard.search') {
            return [
                'type' => 'dashboard_search',
                'description' => 'Dashboard search performed: ' . ($request->get('q') ?? 'empty query')
            ];
        }

        // Generic customer activity for other authenticated routes
        return [
            'type' => 'customer_activity',
            'description' => "Customer accessed: {$uri}"
        ];
    }

    /**
     * Get profile activity data.
     */
    protected function getProfileActivityData(string $routeName, string $method): array
    {
        $actions = [
            'profile.edit' => ['type' => 'profile_view', 'description' => 'Profile edit page accessed'],
            'profile.update' => ['type' => 'profile_update', 'description' => 'Profile information updated'],
            'profile.password.update' => ['type' => 'profile_password_update', 'description' => 'Profile password updated'],
            'profile.avatar.delete' => ['type' => 'profile_avatar_delete', 'description' => 'Profile avatar deleted'],
            'profile.delete-account' => ['type' => 'profile_delete_view', 'description' => 'Account deletion page accessed'],
            'profile.destroy' => ['type' => 'profile_delete', 'description' => 'Account deletion attempted'],
        ];

        return $actions[$routeName] ?? [
            'type' => 'profile_activity',
            'description' => "Profile action: {$routeName}"
        ];
    }

    /**
     * Get project application activity data.
     */
    protected function getProjectApplicationActivityData(string $routeName, string $method, Request $request): array
    {
        $applicationId = $request->route('project_application')?->id ?? $request->route('project-application')?->id;
        
        $actions = [
            'project-applications.index' => ['type' => 'project_application_list', 'description' => 'Project applications list accessed'],
            'project-applications.create' => ['type' => 'project_application_create_form', 'description' => 'Project application creation form accessed'],
            'project-applications.store' => ['type' => 'project_application_create', 'description' => 'Project application submitted'],
            'project-applications.show' => ['type' => 'project_application_view', 'description' => "Project application viewed (ID: {$applicationId})"],
            'project-applications.edit' => ['type' => 'project_application_edit_form', 'description' => "Project application edit form accessed (ID: {$applicationId})"],
            'project-applications.update' => ['type' => 'project_application_update', 'description' => "Project application updated (ID: {$applicationId})"],
            'project-applications.destroy' => ['type' => 'project_application_delete', 'description' => "Project application deleted (ID: {$applicationId})"],
        ];

        return $actions[$routeName] ?? [
            'type' => 'project_application_activity',
            'description' => "Project application action: {$routeName}"
        ];
    }

    /**
     * Get order activity data.
     */
    protected function getOrderActivityData(string $routeName, string $method, Request $request): array
    {
        $orderId = $request->route('order')?->id;
        
        $actions = [
            'orders.index' => ['type' => 'order_list', 'description' => 'Orders list accessed'],
            'orders.show' => ['type' => 'order_view', 'description' => "Order viewed (ID: {$orderId})"],
            'orders.destroy' => ['type' => 'order_cancel', 'description' => "Order cancelled (ID: {$orderId})"],
        ];

        return $actions[$routeName] ?? [
            'type' => 'order_activity',
            'description' => "Order action: {$routeName}"
        ];
    }

    /**
     * Get project activity data.
     */
    protected function getProjectActivityData(string $routeName, string $method, Request $request): array
    {
        $projectId = $request->route('project')?->id;
        
        $actions = [
            'projects.index' => ['type' => 'project_list', 'description' => 'Projects list accessed'],
            'projects.show' => ['type' => 'project_view', 'description' => "Project viewed (ID: {$projectId})"],
            'projects.destroy' => ['type' => 'project_delete', 'description' => "Project deleted (ID: {$projectId})"],
        ];

        return $actions[$routeName] ?? [
            'type' => 'project_activity',
            'description' => "Project action: {$routeName}"
        ];
    }

    /**
     * Get cart activity data.
     */
    protected function getCartActivityData(string $routeName, string $method): array
    {
        $actions = [
            'cart.index' => ['type' => 'cart_view', 'description' => 'Shopping cart accessed'],
            'cart.add' => ['type' => 'cart_add_item', 'description' => 'Item added to cart'],
            'cart.update' => ['type' => 'cart_update_item', 'description' => 'Cart item updated'],
            'cart.remove' => ['type' => 'cart_remove_item', 'description' => 'Item removed from cart'],
            'cart.clear' => ['type' => 'cart_clear', 'description' => 'Cart cleared'],
            'cart.count' => ['type' => 'cart_count_check', 'description' => 'Cart count checked'],
            'cart.coupon' => ['type' => 'cart_apply_coupon', 'description' => 'Coupon applied to cart'],
            // API cart routes
            'api.cart.index' => ['type' => 'api_cart_view', 'description' => 'Cart viewed via API'],
            'api.cart.add' => ['type' => 'api_cart_add_item', 'description' => 'Item added to cart via API'],
            'api.cart.count' => ['type' => 'api_cart_count_check', 'description' => 'Cart count checked via API'],
            // Note: api.cart.sync is handled manually in controller (failures only)
        ];

        return $actions[$routeName] ?? [
            'type' => 'cart_activity',
            'description' => "Cart action: {$routeName}"
        ];
    }

    /**
     * Get admin activity data.
     */
    protected function getAdminActivityData(string $routeName, string $method, Request $request): array
    {
        $actions = [
            'admin.dashboard' => ['type' => 'admin_dashboard_access', 'description' => 'Admin dashboard accessed'],
            'admin.dashboard.alt' => ['type' => 'admin_dashboard_access', 'description' => 'Admin dashboard accessed'],
            'admin.dashboard.visitor-chart-data' => ['type' => 'admin_dashboard_chart_data', 'description' => 'Admin dashboard chart data requested'],
            'admin.search' => ['type' => 'admin_search', 'description' => 'Admin search performed: ' . ($request->get('q') ?? 'empty query')],
            'admin.search.advanced' => ['type' => 'admin_search_advanced', 'description' => 'Admin advanced search accessed'],
            'admin.search.suggestions' => ['type' => 'admin_search_suggestions', 'description' => 'Admin search suggestions requested'],
            'admin.search.filters' => ['type' => 'admin_search_filters', 'description' => 'Admin search filters requested'],
        ];

        return $actions[$routeName] ?? [
            'type' => 'admin_activity',
            'description' => "Admin action: {$routeName}"
        ];
    }

    /**
     * Get failure reason based on status code.
     */
    protected function getFailureReason(int $statusCode, BaseResponse $response): string
    {
        return match ($statusCode) {
            400 => 'Bad Request',
            401 => 'Unauthorized',
            403 => 'Forbidden',
            404 => 'Not Found',
            422 => 'Validation Failed',
            429 => 'Too Many Requests',
            500 => 'Internal Server Error',
            503 => 'Service Unavailable',
            default => "HTTP {$statusCode}"
        };
    }

    /**
     * Get response size.
     */
    protected function getResponseSize(BaseResponse $response): ?int
    {
        $content = $response->getContent();
        return $content ? strlen($content) : null;
    }

    /**
     * Get HTTP status code for exception.
     */
    protected function getExceptionStatusCode(\Throwable $exception): int
    {
        if ($exception instanceof \Symfony\Component\HttpKernel\Exception\HttpException) {
            return $exception->getStatusCode();
        }

        if ($exception instanceof \Illuminate\Database\Eloquent\ModelNotFoundException) {
            return 404;
        }

        if ($exception instanceof \Illuminate\Auth\AuthenticationException) {
            return 401;
        }

        if ($exception instanceof \Illuminate\Auth\Access\AuthorizationException) {
            return 403;
        }

        if ($exception instanceof \Illuminate\Validation\ValidationException) {
            return 422;
        }

        // Default to 500 for other exceptions
        return 500;
    }

    /**
     * Update the last seen timestamp for authenticated users.
     */
    protected function updateUserLastSeen(): void
    {
        $user = Auth::user();

        if (!$user) {
            return;
        }

        // Only update if last seen is older than 1 minute to avoid excessive database writes
        if (!$user->last_seen_at || $user->last_seen_at < now()->subMinute()) {
            try {
                $user->updateLastSeen();
            } catch (\Exception $e) {
                // Log error but don't fail the request
                Log::warning('Failed to update user last seen timestamp', [
                    'user_id' => $user->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }
}
