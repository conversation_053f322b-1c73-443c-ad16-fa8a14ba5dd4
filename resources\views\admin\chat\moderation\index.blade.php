@extends('layouts.dashboard')

@section('title', 'Chat Moderation Dashboard')

@section('content')
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Chat Moderation Dashboard</h1>
            <p class="text-gray-600 mt-2">Monitor and moderate live chat conversations</p>
        </div>
        
        <!-- Real-time Status -->
        <div class="flex items-center space-x-4">
            <div class="flex items-center">
                <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse mr-2"></div>
                <span class="text-sm text-gray-600">Live Monitoring</span>
            </div>
            <button onclick="refreshDashboard()" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refresh
            </button>
        </div>
    </div>

    <!-- Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active Chats</p>
                    <p class="text-2xl font-bold text-gray-900" id="active-chats">{{ $stats['active_chats'] }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Waiting</p>
                    <p class="text-2xl font-bold text-gray-900" id="waiting-chats">{{ $stats['waiting_chats'] }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Messages Today</p>
                    <p class="text-2xl font-bold text-gray-900" id="messages-today">{{ $stats['total_messages_today'] }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Online Staff</p>
                    <p class="text-2xl font-bold text-gray-900" id="online-staff">{{ $stats['online_staff'] }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Avg Response</p>
                    <p class="text-2xl font-bold text-gray-900" id="avg-response">{{ $stats['avg_response_time'] }}m</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-orange-100 rounded-lg">
                    <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Satisfaction</p>
                    <p class="text-2xl font-bold text-gray-900" id="satisfaction">{{ $stats['satisfaction_rating'] }}/5</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Active Chats -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100">
                <div class="px-6 py-4 border-b border-neutral-200">
                    <h3 class="text-lg font-semibold text-gray-900">Active Chats</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4" id="active-chats-list">
                        @forelse($activeChats as $chat)
                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-3 h-3 rounded-full {{ $chat->status === 'active' ? 'bg-green-500' : 'bg-yellow-500' }}"></div>
                                        <div>
                                            <h4 class="font-medium text-gray-900">{{ $chat->title ?? 'Chat #' . $chat->id }}</h4>
                                            <p class="text-sm text-gray-600">
                                                Priority: {{ $chat->priority }}/5 | 
                                                {{ $chat->participants->count() }} participants
                                            </p>
                                            @if($chat->currentAssignment)
                                                <p class="text-xs text-blue-600">
                                                    Assigned to: {{ $chat->currentAssignment->assignedStaff->name }}
                                                </p>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button onclick="escalateChat({{ $chat->id }})" class="btn-sm btn-warning">
                                        Escalate
                                    </button>
                                    <a href="{{ route('admin.chat.rooms.show', $chat) }}" class="btn-sm btn-primary">
                                        View
                                    </a>
                                </div>
                            </div>
                        @empty
                            <div class="text-center py-8">
                                <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                                <p class="text-gray-500">No active chats at the moment</p>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>

        <!-- Staff Workload -->
        <div>
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100">
                <div class="px-6 py-4 border-b border-neutral-200">
                    <h3 class="text-lg font-semibold text-gray-900">Staff Workload</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        @foreach($staffWorkload as $staff)
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                                        <span class="text-xs font-medium text-gray-600">
                                            {{ substr($staff['staff']->first_name, 0, 1) }}{{ substr($staff['staff']->last_name, 0, 1) }}
                                        </span>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900">{{ $staff['staff']->name }}</p>
                                        <p class="text-xs text-gray-600">
                                            {{ $staff['active_assignments'] }}/{{ $staff['max_assignments'] }} chats
                                        </p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="w-16 bg-gray-200 rounded-full h-2">
                                        <div class="h-2 rounded-full {{ $staff['workload_percentage'] > 80 ? 'bg-red-500' : ($staff['workload_percentage'] > 60 ? 'bg-yellow-500' : 'bg-green-500') }}" 
                                             style="width: {{ min(100, $staff['workload_percentage']) }}%"></div>
                                    </div>
                                    <p class="text-xs text-gray-600 mt-1">{{ round($staff['workload_percentage']) }}%</p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Escalation Modal -->
<div id="escalationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Escalate Chat</h3>
            </div>
            <form id="escalationForm">
                <div class="px-6 py-4 space-y-4">
                    <input type="hidden" id="escalate-room-id">
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Priority Level</label>
                        <select id="escalate-priority" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            <option value="3">Medium (3)</option>
                            <option value="4">High (4)</option>
                            <option value="5">Critical (5)</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Reason</label>
                        <textarea id="escalate-reason" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2" 
                                  placeholder="Explain why this chat needs escalation..."></textarea>
                    </div>
                </div>
                <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                    <button type="button" onclick="closeEscalationModal()" class="btn-secondary">Cancel</button>
                    <button type="submit" class="btn-primary">Escalate</button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
// Auto-refresh dashboard every 30 seconds
setInterval(refreshDashboard, 30000);

function refreshDashboard() {
    fetch('{{ route("admin.chat.moderation.stats") }}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStats(data.stats);
            }
        })
        .catch(error => console.error('Error refreshing dashboard:', error));
}

function updateStats(stats) {
    document.getElementById('active-chats').textContent = stats.active_chats;
    document.getElementById('waiting-chats').textContent = stats.waiting_chats;
    document.getElementById('messages-today').textContent = stats.total_messages_today;
    document.getElementById('online-staff').textContent = stats.online_staff;
    document.getElementById('avg-response').textContent = stats.avg_response_time + 'm';
    document.getElementById('satisfaction').textContent = stats.satisfaction_rating + '/5';
}

function escalateChat(roomId) {
    document.getElementById('escalate-room-id').value = roomId;
    document.getElementById('escalationModal').classList.remove('hidden');
}

function closeEscalationModal() {
    document.getElementById('escalationModal').classList.add('hidden');
    document.getElementById('escalationForm').reset();
}

document.getElementById('escalationForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = {
        room_id: document.getElementById('escalate-room-id').value,
        priority: document.getElementById('escalate-priority').value,
        reason: document.getElementById('escalate-reason').value,
        _token: '{{ csrf_token() }}'
    };
    
    fetch('{{ route("admin.chat.moderation.escalate") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeEscalationModal();
            refreshDashboard();
            alert('Chat escalated successfully');
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while escalating the chat');
    });
});
</script>
@endpush
