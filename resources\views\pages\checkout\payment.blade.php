@extends('layouts.app')

@section('title', 'Payment - ' . __('common.company_name'))
@section('meta_description', 'Complete your payment securely')

@section('content')
<section class="py-20 bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <!-- Progress Steps -->
            <div class="mb-8">
                <div class="flex items-center justify-center space-x-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                            ✓
                        </div>
                        <span class="ml-2 text-sm text-green-600 font-medium">Cart</span>
                    </div>
                    <div class="w-16 h-0.5 bg-green-500"></div>
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                            ✓
                        </div>
                        <span class="ml-2 text-sm text-green-600 font-medium">Checkout</span>
                    </div>
                    <div class="w-16 h-0.5 bg-blue-500"></div>
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                            3
                        </div>
                        <span class="ml-2 text-sm text-blue-600 font-medium">Payment</span>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Payment Form -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6">Payment Information</h2>

                        <!-- Payment Method Selection -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Payment Method</h3>
                            <div class="space-y-3">
                                <label class="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                    <input type="radio" name="payment_method" value="stripe" checked class="text-blue-600 focus:ring-blue-500">
                                    <div class="ml-3 flex items-center">
                                        <svg class="w-8 h-5 mr-3" viewBox="0 0 40 24" fill="none">
                                            <rect width="40" height="24" rx="4" fill="#635BFF"/>
                                            <path d="M15.27 7.93c0-.69-.57-1.26-1.27-1.26s-1.27.57-1.27 1.26v8.14c0 .69.57 1.26 1.27 1.26s1.27-.57 1.27-1.26V7.93z" fill="white"/>
                                            <path d="M19.73 7.93c0-.69-.57-1.26-1.27-1.26s-1.27.57-1.27 1.26v8.14c0 .69.57 1.26 1.27 1.26s1.27-.57 1.27-1.26V7.93z" fill="white"/>
                                            <path d="M24.19 7.93c0-.69-.57-1.26-1.27-1.26s-1.27.57-1.27 1.26v8.14c0 .69.57 1.26 1.27 1.26s1.27-.57 1.27-1.26V7.93z" fill="white"/>
                                            <path d="M28.65 7.93c0-.69-.57-1.26-1.27-1.26s-1.27.57-1.27 1.26v8.14c0 .69.57 1.26 1.27 1.26s1.27-.57 1.27-1.26V7.93z" fill="white"/>
                                        </svg>
                                        <span class="font-medium text-gray-900">Credit/Debit Card</span>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <!-- Stripe Payment Form -->
                        <div id="stripe-payment-form">
                            <form id="payment-form" class="space-y-6">
                                @csrf
                                <input type="hidden" name="order_id" value="{{ $order->uuid }}">

                                <!-- Card Element -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        Card Information
                                    </label>
                                    <div id="card-element" class="p-3 border border-gray-300 rounded-lg bg-white">
                                        <!-- Stripe Elements will create form elements here -->
                                    </div>
                                    <div id="card-errors" role="alert" class="mt-2 text-sm text-red-600"></div>
                                </div>

                                <!-- Billing Address -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            Billing Name
                                        </label>
                                        <input type="text" name="billing_name" value="{{ $order->billing_first_name }} {{ $order->billing_last_name }}" 
                                               class="form-input" required>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            Email
                                        </label>
                                        <input type="email" name="billing_email" value="{{ $order->email }}" 
                                               class="form-input" required>
                                    </div>
                                </div>

                                <!-- Security Notice -->
                                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                    <div class="flex items-start">
                                        <svg class="w-5 h-5 text-green-600 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        <div>
                                            <h4 class="text-sm font-medium text-green-800">Secure Payment</h4>
                                            <p class="text-sm text-green-700 mt-1">
                                                Your payment information is encrypted and secure. We use Stripe for payment processing.
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Submit Button -->
                                <button type="submit" id="submit-payment" class="w-full btn-primary">
                                    <span id="button-text">
                                        <svg class="w-5 h-5 mr-2 inline" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        Pay {{ $order->formatted_total }}
                                    </span>
                                    <div id="spinner" class="hidden">
                                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Processing...
                                    </div>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-lg shadow-sm p-6 sticky top-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
                        
                        <!-- Order Items -->
                        <div class="space-y-3 mb-6">
                            @foreach($order->items as $item)
                            <div class="flex items-center space-x-3">
                                <img src="{{ $item->product->primary_image }}" alt="{{ $item->product->name }}" 
                                     class="w-12 h-12 object-cover rounded">
                                <div class="flex-1 min-w-0">
                                    <h4 class="text-sm font-medium text-gray-900 truncate">{{ $item->product->name }}</h4>
                                    @if($item->productVariant)
                                    <p class="text-xs text-gray-500">{{ $item->productVariant->name }}</p>
                                    @endif
                                    <p class="text-xs text-gray-500">Qty: {{ $item->quantity }}</p>
                                </div>
                                <span class="text-sm font-medium text-gray-900">{{ $item->formatted_total }}</span>
                            </div>
                            @endforeach
                        </div>

                        <!-- Order Totals -->
                        <div class="border-t border-gray-200 pt-4 space-y-3">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Subtotal</span>
                                <span class="text-gray-900">{{ $order->formatted_subtotal }}</span>
                            </div>

                            @if($order->discount_amount > 0)
                            <div class="flex justify-between text-sm text-green-600">
                                <span>Discount @if($order->coupon_code)({{ $order->coupon_code }})@endif</span>
                                <span>-{{ $order->formatted_discount_amount }}</span>
                            </div>
                            @endif

                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Shipping</span>
                                <span class="text-gray-900">{{ $order->formatted_shipping_amount }}</span>
                            </div>

                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Tax</span>
                                <span class="text-gray-900">{{ $order->formatted_tax_amount }}</span>
                            </div>

                            <div class="border-t border-gray-200 pt-3">
                                <div class="flex justify-between">
                                    <span class="text-base font-semibold text-gray-900">Total</span>
                                    <span class="text-lg font-bold text-gray-900">{{ $order->formatted_total }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Order Info -->
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <div class="text-sm text-gray-600 space-y-1">
                                <div><strong>Order #:</strong> {{ $order->order_number }}</div>
                                <div><strong>Email:</strong> {{ $order->email }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script src="https://js.stripe.com/v3/"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Stripe
    const stripe = Stripe('{{ config('services.stripe.key') }}');
    const elements = stripe.elements();

    // Create card element
    const cardElement = elements.create('card', {
        style: {
            base: {
                fontSize: '16px',
                color: '#424770',
                '::placeholder': {
                    color: '#aab7c4',
                },
            },
        },
    });

    cardElement.mount('#card-element');

    // Handle real-time validation errors from the card Element
    cardElement.on('change', function(event) {
        const displayError = document.getElementById('card-errors');
        if (event.error) {
            displayError.textContent = event.error.message;
        } else {
            displayError.textContent = '';
        }
    });

    // Handle form submission
    const form = document.getElementById('payment-form');
    form.addEventListener('submit', async function(event) {
        event.preventDefault();

        const submitButton = document.getElementById('submit-payment');
        const buttonText = document.getElementById('button-text');
        const spinner = document.getElementById('spinner');

        // Disable submit button and show spinner
        submitButton.disabled = true;
        buttonText.classList.add('hidden');
        spinner.classList.remove('hidden');

        // Create payment method
        const {token, error} = await stripe.createToken(cardElement, {
            name: document.querySelector('input[name="billing_name"]').value,
            email: document.querySelector('input[name="billing_email"]').value,
        });

        if (error) {
            // Show error to customer
            const errorElement = document.getElementById('card-errors');
            errorElement.textContent = error.message;

            // Re-enable submit button
            submitButton.disabled = false;
            buttonText.classList.remove('hidden');
            spinner.classList.add('hidden');
        } else {
            // Submit payment to server
            submitPayment(token.id);
        }
    });

    async function submitPayment(tokenId) {
        try {
            const response = await fetch('{{ route('checkout.payment.process', $order->uuid) }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    token: tokenId,
                    order_id: '{{ $order->uuid }}'
                })
            });

            const result = await response.json();

            if (result.success) {
                // Redirect to success page
                window.location.href = result.redirect_url || '{{ route('checkout.success', $order->uuid) }}';
            } else {
                // Show error
                const errorElement = document.getElementById('card-errors');
                errorElement.textContent = result.message || 'Payment failed. Please try again.';

                // Re-enable submit button
                const submitButton = document.getElementById('submit-payment');
                const buttonText = document.getElementById('button-text');
                const spinner = document.getElementById('spinner');
                
                submitButton.disabled = false;
                buttonText.classList.remove('hidden');
                spinner.classList.add('hidden');
            }
        } catch (error) {
            console.error('Payment error:', error);

            // Log payment processing error
            paymentLogger.logError('payment_processing_error', {
                error_message: error.message,
                error_stack: error.stack,
                order_id: '{{ $order->uuid }}',
                payment_method: '{{ $order->payment_method }}',
                timestamp: Date.now()
            });

            const errorElement = document.getElementById('card-errors');
            errorElement.textContent = 'An error occurred. Please try again.';

            // Re-enable submit button
            const submitButton = document.getElementById('submit-payment');
            const buttonText = document.getElementById('button-text');
            const spinner = document.getElementById('spinner');

            submitButton.disabled = false;
            buttonText.classList.remove('hidden');
            spinner.classList.add('hidden');
        }
    }

    // Payment Logger Class
    class PaymentLogger {
        constructor() {
            this.apiEndpoint = '/api/log-checkout-activity';
            this.sessionId = this.getSessionId();
            this.orderId = '{{ $order->uuid }}';
            this.paymentMethod = '{{ $order->payment_method }}';
            this.userAuthenticated = {{ $order->user_id ? 'true' : 'false' }};
            this.customerType = '{{ $order->user_id ? "registered" : "guest" }}';
            this.cartType = '{{ $order->user_id ? "user" : "session" }}';
            this.init();
        }

        getSessionId() {
            let sessionId = sessionStorage.getItem('checkout_session_id');
            if (!sessionId) {
                sessionId = 'payment_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                sessionStorage.setItem('checkout_session_id', sessionId);
            }
            return sessionId;
        }

        init() {
            this.setupErrorHandling();
            this.setupPaymentFormLogging();
            this.setupStripeErrorLogging();
            this.logPageLoad();
        }

        setupErrorHandling() {
            window.addEventListener('error', (event) => {
                this.logError('javascript_error', {
                    message: event.message,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    stack: event.error?.stack,
                    timestamp: Date.now()
                });
            });

            window.addEventListener('unhandledrejection', (event) => {
                this.logError('promise_rejection', {
                    reason: event.reason?.toString(),
                    stack: event.reason?.stack,
                    timestamp: Date.now()
                });
            });
        }

        setupPaymentFormLogging() {
            const form = document.getElementById('payment-form');
            if (!form) return;

            form.addEventListener('submit', (event) => {
                this.logInteraction('payment_form_submit', {
                    order_id: this.orderId,
                    payment_method: this.paymentMethod,
                    timestamp: Date.now()
                });
            });

            // Log card element interactions
            if (window.cardElement) {
                cardElement.on('change', (event) => {
                    if (event.error) {
                        this.logError('stripe_card_error', {
                            error_type: event.error.type,
                            error_code: event.error.code,
                            error_message: event.error.message,
                            timestamp: Date.now()
                        });
                    }
                });

                cardElement.on('focus', () => {
                    this.logInteraction('card_field_focus', {
                        timestamp: Date.now()
                    });
                });

                cardElement.on('blur', () => {
                    this.logInteraction('card_field_blur', {
                        timestamp: Date.now()
                    });
                });
            }
        }

        setupStripeErrorLogging() {
            // Override Stripe createToken to log errors
            if (window.stripe) {
                const originalCreateToken = stripe.createToken;
                stripe.createToken = (...args) => {
                    return originalCreateToken.apply(stripe, args).then(result => {
                        if (result.error) {
                            this.logError('stripe_token_error', {
                                error_type: result.error.type,
                                error_code: result.error.code,
                                error_message: result.error.message,
                                order_id: this.orderId,
                                timestamp: Date.now()
                            });
                        } else {
                            this.logInteraction('stripe_token_created', {
                                order_id: this.orderId,
                                timestamp: Date.now()
                            });
                        }
                        return result;
                    });
                };
            }
        }

        logPageLoad() {
            this.logInteraction('payment_page_load', {
                order_id: this.orderId,
                payment_method: this.paymentMethod,
                page_url: window.location.href,
                user_authenticated: this.userAuthenticated,
                customer_type: this.customerType,
                cart_type: this.cartType,
                timestamp: Date.now()
            });
        }

        logError(type, data) {
            this.sendLog('error', {
                type: type,
                data: Object.assign(data, {
                    order_id: this.orderId,
                    payment_method: this.paymentMethod
                }),
                session_id: this.sessionId,
                timestamp: Date.now(),
                page_url: window.location.href
            });
        }

        logInteraction(type, data) {
            this.sendLog('interaction', {
                type: type,
                data: Object.assign(data, {
                    order_id: this.orderId,
                    payment_method: this.paymentMethod
                }),
                session_id: this.sessionId,
                timestamp: Date.now()
            });
        }

        async sendLog(logType, logData) {
            try {
                await fetch(this.apiEndpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                    },
                    body: JSON.stringify({
                        log_type: logType,
                        log_data: logData
                    })
                });
            } catch (error) {
                console.error('Failed to send payment log:', error);
            }
        }
    }

    // Initialize payment logger
    const paymentLogger = new PaymentLogger();
});
</script>
@endpush
@endsection
