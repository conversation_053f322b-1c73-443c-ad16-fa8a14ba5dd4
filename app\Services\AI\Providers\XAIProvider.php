<?php

namespace App\Services\AI\Providers;

use Exception;

class <PERSON><PERSON><PERSON>rovider extends BaseAIProvider
{
    /**
     * Validate the provider configuration.
     */
    public function validateConfiguration(): bool
    {
        if (empty($this->config['api_key'])) {
            throw new \Exception('xAI API key is not configured. Please set XAI_API_KEY in your environment variables.');
        }

        if (empty($this->config['base_url'])) {
            throw new \Exception('xAI base URL is not configured. Please set XAI_BASE_URL in your environment variables.');
        }

        if (!filter_var($this->config['base_url'], FILTER_VALIDATE_URL)) {
            throw new \Exception('xAI base URL is not a valid URL.');
        }

        return true;
    }

    /**
     * Generate a response from xAI Grok.
     */
    public function generateResponse(string $message, array $context = [], string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        $this->validateModel($model);

        $messages = $this->buildMessages($message, $context);
        
        $data = [
            'model' => $model,
            'messages' => $messages,
            'max_tokens' => $context['max_tokens'] ?? 1000,
            'temperature' => $context['temperature'] ?? 0.7,
            'top_p' => $context['top_p'] ?? 1.0,
            'frequency_penalty' => $context['frequency_penalty'] ?? 0.0,
            'presence_penalty' => $context['presence_penalty'] ?? 0.0,
        ];

        // Add search capability if supported by model
        if ($this->supportsFeature('search', $model) && ($context['enable_search'] ?? true)) {
            $data['stream'] = false; // Search requires non-streaming
        }

        // Add function calling if supported and provided
        if (isset($context['functions']) && $this->supportsFeature('function_calling', $model)) {
            $data['functions'] = $context['functions'];
            if (isset($context['function_call'])) {
                $data['function_call'] = $context['function_call'];
            }
        }

        // Add tools if supported
        if (isset($context['tools']) && $this->supportsFeature('function_calling', $model)) {
            $data['tools'] = $context['tools'];
            if (isset($context['tool_choice'])) {
                $data['tool_choice'] = $context['tool_choice'];
            }
        }

        $response = $this->makeRequest('chat/completions', $data);

        return [
            'content' => $this->extractContent($response),
            'model' => $model,
            'provider' => $this->name,
            'usage' => $response['usage'] ?? [],
            'finish_reason' => $response['choices'][0]['finish_reason'] ?? null,
            'function_call' => $response['choices'][0]['message']['function_call'] ?? null,
            'tool_calls' => $response['choices'][0]['message']['tool_calls'] ?? null,
            'search_results' => $this->extractSearchResults($response),
            'raw_response' => $response,
        ];
    }

    /**
     * Analyze sentiment using Grok.
     */
    public function analyzeSentiment(string $text, string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        
        $prompt = "Analyze the sentiment of the following text with Grok's unique perspective. Provide a sentiment score from -1 (very negative) to 1 (very positive), the dominant emotion, and your confidence level. Be witty but accurate. Format as JSON with keys: score, emotion, confidence, explanation.\n\nText: {$text}";

        $response = $this->generateResponse($prompt, [
            'temperature' => 0.3,
            'max_tokens' => 200,
        ], $model);

        try {
            $analysis = json_decode($response['content'], true);
            return [
                'sentiment_score' => $analysis['score'] ?? 0,
                'emotion' => $analysis['emotion'] ?? 'neutral',
                'confidence' => $analysis['confidence'] ?? 0.5,
                'explanation' => $analysis['explanation'] ?? '',
                'provider' => $this->name,
                'model' => $model,
                'search_enhanced' => !empty($response['search_results']),
            ];
        } catch (Exception $e) {
            return [
                'sentiment_score' => 0,
                'emotion' => 'neutral',
                'confidence' => 0.5,
                'explanation' => 'Unable to parse sentiment analysis',
                'provider' => $this->name,
                'model' => $model,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Translate text using Grok.
     */
    public function translateText(string $text, string $targetLanguage, string $sourceLanguage = null, string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        
        $sourceText = $sourceLanguage ? "from {$sourceLanguage} " : '';
        $prompt = "Translate the following text {$sourceText}to {$targetLanguage}. Provide only the translation with Grok's accuracy:\n\n{$text}";

        $response = $this->generateResponse($prompt, [
            'temperature' => 0.3,
            'max_tokens' => strlen($text) * 2,
        ], $model);

        return [
            'translated_text' => trim($response['content']),
            'source_language' => $sourceLanguage,
            'target_language' => $targetLanguage,
            'provider' => $this->name,
            'model' => $model,
            'confidence' => 0.9,
            'search_enhanced' => !empty($response['search_results']),
        ];
    }

    /**
     * Summarize text using Grok.
     */
    public function summarizeText(string $text, int $maxLength = 150, string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        
        $prompt = "Summarize the following text in approximately {$maxLength} words. Use Grok's analytical capabilities to focus on the most important points:\n\n{$text}";

        $response = $this->generateResponse($prompt, [
            'temperature' => 0.5,
            'max_tokens' => $maxLength * 2,
        ], $model);

        return [
            'summary' => trim($response['content']),
            'original_length' => strlen($text),
            'summary_length' => strlen($response['content']),
            'compression_ratio' => strlen($response['content']) / strlen($text),
            'provider' => $this->name,
            'model' => $model,
            'search_enhanced' => !empty($response['search_results']),
        ];
    }

    /**
     * Extract topics from text using Grok.
     */
    public function extractTopics(string $text, int $maxTopics = 5, string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        
        $prompt = "Extract the top {$maxTopics} topics from the following text using Grok's analytical capabilities. For each topic, provide a title and description. Format as JSON array with objects containing 'title' and 'description':\n\n{$text}";

        $response = $this->generateResponse($prompt, [
            'temperature' => 0.4,
            'max_tokens' => 500,
        ], $model);

        try {
            $topics = json_decode($response['content'], true);
            return [
                'topics' => $topics ?: [],
                'provider' => $this->name,
                'model' => $model,
                'search_enhanced' => !empty($response['search_results']),
            ];
        } catch (Exception $e) {
            return [
                'topics' => [],
                'provider' => $this->name,
                'model' => $model,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Generate function calls using Grok.
     */
    public function generateFunctionCalls(string $message, array $availableFunctions = [], array $context = [], string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        
        if (!$this->supportsFeature('function_calling', $model)) {
            throw new Exception("Function calling not supported by model {$model}");
        }

        $messages = $this->buildMessages($message, $context);
        
        $data = [
            'model' => $model,
            'messages' => $messages,
            'tools' => $availableFunctions,
            'tool_choice' => 'auto',
        ];

        $response = $this->makeRequest('chat/completions', $data);

        return [
            'content' => $this->extractContent($response),
            'tool_calls' => $response['choices'][0]['message']['tool_calls'] ?? [],
            'model' => $model,
            'provider' => $this->name,
            'usage' => $response['usage'] ?? [],
            'search_results' => $this->extractSearchResults($response),
            'raw_response' => $response,
        ];
    }

    /**
     * Analyze image using Grok Vision.
     */
    public function analyzeImage(string $imageUrl, string $prompt = null, string $model = null): array
    {
        $model = $model ?: 'grok-4'; // Default to vision-capable model
        
        if (!$this->supportsFeature('vision', $model)) {
            throw new Exception("Vision not supported by model {$model}");
        }

        $prompt = $prompt ?: "Describe what you see in this image with Grok's unique analytical perspective.";

        $messages = [
            [
                'role' => 'user',
                'content' => [
                    [
                        'type' => 'text',
                        'text' => $prompt,
                    ],
                    [
                        'type' => 'image_url',
                        'image_url' => [
                            'url' => $imageUrl,
                        ],
                    ],
                ],
            ],
        ];

        $data = [
            'model' => $model,
            'messages' => $messages,
            'max_tokens' => 1000,
        ];

        $response = $this->makeRequest('chat/completions', $data);

        return [
            'description' => $this->extractContent($response),
            'model' => $model,
            'provider' => $this->name,
            'usage' => $response['usage'] ?? [],
            'search_enhanced' => !empty($this->extractSearchResults($response)),
            'raw_response' => $response,
        ];
    }

    /**
     * Perform real-time search using Grok's search capabilities.
     */
    public function searchWithGrok(string $query, array $context = [], string $model = null): array
    {
        $model = $model ?: $this->getDefaultModel();
        
        if (!$this->supportsFeature('search', $model)) {
            throw new Exception("Search not supported by model {$model}");
        }

        $prompt = "Search for and provide comprehensive information about: {$query}. Use your real-time search capabilities to provide the most current and accurate information.";

        $response = $this->generateResponse($prompt, array_merge($context, [
            'enable_search' => true,
            'temperature' => 0.3,
        ]), $model);

        return [
            'search_query' => $query,
            'results' => $response['content'],
            'search_results' => $response['search_results'] ?? [],
            'model' => $model,
            'provider' => $this->name,
            'usage' => $response['usage'] ?? [],
        ];
    }

    /**
     * Extract search results from Grok response.
     */
    protected function extractSearchResults(array $response): array
    {
        // Grok may include search results in the response
        // This is a placeholder for the actual implementation
        return $response['search_results'] ?? [];
    }

    /**
     * Get default headers for xAI API.
     */
    protected function getDefaultHeaders(): array
    {
        $headers = parent::getDefaultHeaders();
        $headers['Authorization'] = 'Bearer ' . $this->config['api_key'];
        
        return $headers;
    }

    /**
     * Extract content from xAI response.
     */
    protected function extractContent(array $response): string
    {
        return $response['choices'][0]['message']['content'] ?? '';
    }

    /**
     * Check if the provider supports a specific feature.
     */
    public function supportsFeature(string $feature, string $model = null): bool
    {
        // Grok has unique search capabilities
        if ($feature === 'search') {
            return true; // All Grok models support search
        }

        return parent::supportsFeature($feature, $model);
    }
}
