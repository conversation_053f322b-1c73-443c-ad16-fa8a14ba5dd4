<?php

namespace App\Services;

use App\Models\ChatWebhook;
use App\Models\ChatWebhookDelivery;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use App\Jobs\DeliverChatWebhook;

class ChatWebhookService
{
    /**
     * Trigger webhook for a specific event.
     */
    public function triggerEvent(string $eventType, array $data): void
    {
        $webhooks = ChatWebhook::where('is_active', true)
            ->get()
            ->filter(fn($webhook) => $webhook->shouldReceiveEvent($eventType));

        foreach ($webhooks as $webhook) {
            $this->queueWebhookDelivery($webhook, $eventType, $data);
        }
    }

    /**
     * Queue webhook delivery for processing.
     */
    protected function queueWebhookDelivery(ChatWebhook $webhook, string $eventType, array $data): void
    {
        $payload = $this->buildPayload($eventType, $data);
        
        $delivery = ChatWebhookDelivery::create([
            'chat_webhook_id' => $webhook->id,
            'event_type' => $eventType,
            'payload' => $payload,
            'status' => 'pending',
        ]);

        // Queue the delivery job
        Queue::push(new DeliverChatWebhook($delivery));
    }

    /**
     * Build webhook payload.
     */
    protected function buildPayload(string $eventType, array $data): array
    {
        return [
            'event' => $eventType,
            'timestamp' => now()->toISOString(),
            'data' => $data,
            'version' => '1.0',
        ];
    }

    /**
     * Deliver webhook synchronously (used by job).
     */
    public function deliverWebhook(ChatWebhookDelivery $delivery): bool
    {
        $webhook = $delivery->webhook;
        $payload = json_encode($delivery->payload);

        try {
            $headers = array_merge(
                [
                    'Content-Type' => $webhook->content_type,
                    'User-Agent' => 'ChiSolution-Webhook/1.0',
                    'X-Webhook-Event' => $delivery->event_type,
                    'X-Webhook-Delivery' => $delivery->uuid,
                ],
                $webhook->headers ?? []
            );

            // Add HMAC signature if secret is configured
            if ($webhook->secret) {
                $headers['X-Webhook-Signature'] = $webhook->generateSignature($payload);
            }

            $response = Http::timeout($webhook->timeout_seconds)
                ->withHeaders($headers)
                ->post($webhook->url, $delivery->payload);

            if ($response->successful()) {
                $delivery->markAsDelivered(
                    $response->status(),
                    $response->body()
                );
                return true;
            } else {
                $delivery->markAsFailed(
                    "HTTP {$response->status()}: {$response->body()}",
                    $response->status(),
                    $response->body()
                );
                return false;
            }

        } catch (\Exception $e) {
            $delivery->markAsFailed($e->getMessage());
            
            Log::error('Webhook delivery failed', [
                'webhook_id' => $webhook->id,
                'delivery_id' => $delivery->id,
                'error' => $e->getMessage(),
                'url' => $webhook->url,
            ]);

            return false;
        }
    }

    /**
     * Test webhook endpoint.
     */
    public function testWebhook(ChatWebhook $webhook): array
    {
        $testPayload = [
            'event' => 'webhook.test',
            'timestamp' => now()->toISOString(),
            'data' => [
                'message' => 'This is a test webhook delivery',
                'webhook_id' => $webhook->uuid,
            ],
            'version' => '1.0',
        ];

        try {
            $headers = array_merge(
                [
                    'Content-Type' => $webhook->content_type,
                    'User-Agent' => 'ChiSolution-Webhook/1.0',
                    'X-Webhook-Event' => 'webhook.test',
                ],
                $webhook->headers ?? []
            );

            if ($webhook->secret) {
                $headers['X-Webhook-Signature'] = $webhook->generateSignature(json_encode($testPayload));
            }

            $response = Http::timeout($webhook->timeout_seconds)
                ->withHeaders($headers)
                ->post($webhook->url, $testPayload);

            return [
                'success' => $response->successful(),
                'status_code' => $response->status(),
                'response_body' => $response->body(),
                'response_time' => $response->transferStats?->getTransferTime() ?? 0,
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'status_code' => null,
                'response_body' => null,
                'response_time' => null,
            ];
        }
    }

    /**
     * Retry failed webhook deliveries.
     */
    public function retryFailedDeliveries(): int
    {
        $deliveries = ChatWebhookDelivery::readyForRetry()->get();
        $retried = 0;

        foreach ($deliveries as $delivery) {
            Queue::push(new DeliverChatWebhook($delivery));
            $retried++;
        }

        return $retried;
    }

    /**
     * Get webhook statistics.
     */
    public function getWebhookStats(int $days = 7): array
    {
        $startDate = now()->subDays($days);

        $totalDeliveries = ChatWebhookDelivery::where('created_at', '>=', $startDate)->count();
        $successfulDeliveries = ChatWebhookDelivery::where('created_at', '>=', $startDate)
            ->where('status', 'delivered')->count();
        $failedDeliveries = ChatWebhookDelivery::where('created_at', '>=', $startDate)
            ->where('status', 'failed')->count();

        $successRate = $totalDeliveries > 0 ? round(($successfulDeliveries / $totalDeliveries) * 100, 2) : 0;

        return [
            'total_deliveries' => $totalDeliveries,
            'successful_deliveries' => $successfulDeliveries,
            'failed_deliveries' => $failedDeliveries,
            'success_rate' => $successRate,
            'period_days' => $days,
        ];
    }
}
