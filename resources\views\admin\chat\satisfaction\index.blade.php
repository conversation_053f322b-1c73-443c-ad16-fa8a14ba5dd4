@extends('layouts.dashboard')

@section('title', 'Customer Satisfaction Dashboard')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Customer Satisfaction Dashboard</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.chat.analytics.index') }}">Chat Analytics</a></li>
                        <li class="breadcrumb-item active">Customer Satisfaction</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Row -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form id="satisfaction-filters" class="row g-3">
                        <div class="col-md-3">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                   value="{{ $defaultFilters['start_date']->format('Y-m-d') }}">
                        </div>
                        <div class="col-md-3">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" 
                                   value="{{ $defaultFilters['end_date']->format('Y-m-d') }}">
                        </div>
                        <div class="col-md-2">
                            <label for="staff_id" class="form-label">Staff Member</label>
                            <select class="form-select" id="staff_id" name="staff_id">
                                <option value="all">All Staff</option>
                                <!-- Staff options would be populated dynamically -->
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="department" class="form-label">Department</label>
                            <select class="form-select" id="department" name="department">
                                <option value="all">All Departments</option>
                                <option value="support">Support</option>
                                <option value="sales">Sales</option>
                                <option value="technical">Technical</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block">Apply Filters</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Overview Metrics Row -->
    <div class="row">
        <div class="col-xl-2 col-md-4">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate">Average Rating</h5>
                            <h3 class="my-2 py-1" id="average-rating">{{ number_format($metrics['overview']['average_rating'] ?? 0, 1) }}</h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-star text-warning" style="font-size: 24px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate">Satisfaction Rate</h5>
                            <h3 class="my-2 py-1" id="satisfaction-rate">{{ number_format($metrics['overview']['satisfaction_rate'] ?? 0, 1) }}%</h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-thumb-up text-success" style="font-size: 24px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate">Total Ratings</h5>
                            <h3 class="my-2 py-1" id="total-ratings">{{ $metrics['overview']['total_ratings'] ?? 0 }}</h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-chart-bar text-info" style="font-size: 24px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate">Response Rate</h5>
                            <h3 class="my-2 py-1" id="response-rate">{{ number_format($metrics['overview']['response_rate'] ?? 0, 1) }}%</h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-reply text-primary" style="font-size: 24px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate">NPS Score</h5>
                            <h3 class="my-2 py-1" id="nps-score">{{ number_format($metrics['nps_metrics']['nps_score'] ?? 0, 1) }}</h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-trending-up text-success" style="font-size: 24px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row">
        <div class="col-xl-6">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Ratings Distribution</h4>
                    <div id="ratings-distribution-chart" style="height: 300px;"></div>
                    
                    <div class="mt-3">
                        <div class="row text-center">
                            @if(isset($metrics['ratings_breakdown']['distribution']))
                                @for($i = 5; $i >= 1; $i--)
                                <div class="col">
                                    <h5 class="fw-normal text-muted">{{ $i }} Star{{ $i > 1 ? 's' : '' }}</h5>
                                    <h4>{{ $metrics['ratings_breakdown']['distribution'][$i] ?? 0 }}</h4>
                                    <p class="text-muted mb-0">{{ $metrics['ratings_breakdown']['percentage_distribution'][$i] ?? 0 }}%</p>
                                </div>
                                @endfor
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-6">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Satisfaction Trends</h4>
                    <div id="satisfaction-trends-chart" style="height: 300px;"></div>
                    
                    <div class="mt-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Trend Direction:</span>
                            <span class="badge bg-{{ ($metrics['satisfaction_trends']['trend_direction'] ?? 'stable') === 'improving' ? 'success' : (($metrics['satisfaction_trends']['trend_direction'] ?? 'stable') === 'declining' ? 'danger' : 'secondary') }}">
                                {{ ucfirst($metrics['satisfaction_trends']['trend_direction'] ?? 'stable') }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- NPS and Staff Performance Row -->
    <div class="row">
        <div class="col-xl-4">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Net Promoter Score (NPS)</h4>
                    
                    <div class="text-center mb-4">
                        <h2 class="text-{{ ($metrics['nps_metrics']['nps_score'] ?? 0) >= 50 ? 'success' : (($metrics['nps_metrics']['nps_score'] ?? 0) >= 0 ? 'warning' : 'danger') }}">
                            {{ number_format($metrics['nps_metrics']['nps_score'] ?? 0, 1) }}
                        </h2>
                        <p class="text-muted">NPS Score</p>
                    </div>

                    <div class="row text-center">
                        <div class="col-4">
                            <h5 class="text-success">{{ $metrics['nps_metrics']['promoters'] ?? 0 }}</h5>
                            <p class="text-muted mb-0">Promoters</p>
                        </div>
                        <div class="col-4">
                            <h5 class="text-warning">{{ $metrics['nps_metrics']['passives'] ?? 0 }}</h5>
                            <p class="text-muted mb-0">Passives</p>
                        </div>
                        <div class="col-4">
                            <h5 class="text-danger">{{ $metrics['nps_metrics']['detractors'] ?? 0 }}</h5>
                            <p class="text-muted mb-0">Detractors</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-8">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Staff Performance</h4>
                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap mb-0" id="staff-performance-table">
                            <thead class="table-light">
                                <tr>
                                    <th>Staff Member</th>
                                    <th>Avg Rating</th>
                                    <th>Total Ratings</th>
                                    <th>Satisfaction Rate</th>
                                    <th>Performance</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($metrics['staff_performance'] ?? [] as $staff)
                                <tr>
                                    <td><strong>{{ $staff['staff_name'] }}</strong></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="me-2">{{ number_format($staff['avg_rating'], 1) }}</span>
                                            @for($i = 1; $i <= 5; $i++)
                                                <i class="mdi mdi-star{{ $i <= $staff['avg_rating'] ? '' : '-outline' }} text-warning"></i>
                                            @endfor
                                        </div>
                                    </td>
                                    <td>{{ $staff['total_ratings'] }}</td>
                                    <td>
                                        <span class="badge bg-{{ $staff['satisfaction_rate'] >= 80 ? 'success' : ($staff['satisfaction_rate'] >= 60 ? 'warning' : 'danger') }}">
                                            {{ number_format($staff['satisfaction_rate'], 1) }}%
                                        </span>
                                    </td>
                                    <td>
                                        @php
                                            $performance = $staff['avg_rating'] >= 4.5 ? 'excellent' : ($staff['avg_rating'] >= 4.0 ? 'good' : ($staff['avg_rating'] >= 3.5 ? 'average' : 'needs_improvement'));
                                        @endphp
                                        <span class="badge bg-{{ $performance === 'excellent' ? 'success' : ($performance === 'good' ? 'info' : ($performance === 'average' ? 'warning' : 'danger')) }}">
                                            {{ ucfirst(str_replace('_', ' ', $performance)) }}
                                        </span>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="text-center text-muted">No staff performance data available</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Feedback Analysis Row -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Recent Customer Feedback</h4>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <h6 class="text-success mb-3">Positive Feedback (4-5 Stars)</h6>
                            <div class="feedback-list" id="positive-feedback">
                                @forelse($metrics['feedback_analysis']['feedback_by_rating']['positive'] ?? [] as $feedback)
                                <div class="mb-3 p-2 border-start border-success border-3">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            @for($i = 1; $i <= $feedback['rating']; $i++)
                                                <i class="mdi mdi-star text-warning"></i>
                                            @endfor
                                        </div>
                                        <small class="text-muted">{{ \Carbon\Carbon::parse($feedback['created_at'])->diffForHumans() }}</small>
                                    </div>
                                    <p class="mb-0 mt-1">{{ Str::limit($feedback['comment'], 100) }}</p>
                                </div>
                                @empty
                                <p class="text-muted">No positive feedback available</p>
                                @endforelse
                            </div>
                        </div>

                        <div class="col-md-4">
                            <h6 class="text-warning mb-3">Neutral Feedback (3 Stars)</h6>
                            <div class="feedback-list" id="neutral-feedback">
                                @forelse($metrics['feedback_analysis']['feedback_by_rating']['neutral'] ?? [] as $feedback)
                                <div class="mb-3 p-2 border-start border-warning border-3">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            @for($i = 1; $i <= $feedback['rating']; $i++)
                                                <i class="mdi mdi-star text-warning"></i>
                                            @endfor
                                        </div>
                                        <small class="text-muted">{{ \Carbon\Carbon::parse($feedback['created_at'])->diffForHumans() }}</small>
                                    </div>
                                    <p class="mb-0 mt-1">{{ Str::limit($feedback['comment'], 100) }}</p>
                                </div>
                                @empty
                                <p class="text-muted">No neutral feedback available</p>
                                @endforelse
                            </div>
                        </div>

                        <div class="col-md-4">
                            <h6 class="text-danger mb-3">Negative Feedback (1-2 Stars)</h6>
                            <div class="feedback-list" id="negative-feedback">
                                @forelse($metrics['feedback_analysis']['feedback_by_rating']['negative'] ?? [] as $feedback)
                                <div class="mb-3 p-2 border-start border-danger border-3">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            @for($i = 1; $i <= $feedback['rating']; $i++)
                                                <i class="mdi mdi-star text-warning"></i>
                                            @endfor
                                        </div>
                                        <small class="text-muted">{{ \Carbon\Carbon::parse($feedback['created_at'])->diffForHumans() }}</small>
                                    </div>
                                    <p class="mb-0 mt-1">{{ Str::limit($feedback['comment'], 100) }}</p>
                                </div>
                                @empty
                                <p class="text-muted">No negative feedback available</p>
                                @endforelse
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Row -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Export & Actions</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-primary me-2" onclick="exportReport('json')">
                                <i class="mdi mdi-download"></i> Export JSON
                            </button>
                            <button type="button" class="btn btn-success me-2" onclick="exportReport('csv')">
                                <i class="mdi mdi-file-excel"></i> Export CSV
                            </button>
                            <button type="button" class="btn btn-danger" onclick="exportReport('pdf')">
                                <i class="mdi mdi-file-pdf"></i> Export PDF
                            </button>
                        </div>
                        <div class="col-md-6 text-end">
                            <button type="button" class="btn btn-info" onclick="refreshMetrics()">
                                <i class="mdi mdi-refresh"></i> Refresh Metrics
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh metrics every 5 minutes
setInterval(function() {
    refreshMetrics();
}, 300000);

// Handle filter form submission
document.getElementById('satisfaction-filters').addEventListener('submit', function(e) {
    e.preventDefault();
    refreshMetrics();
});

function refreshMetrics() {
    const formData = new FormData(document.getElementById('satisfaction-filters'));
    const params = new URLSearchParams(formData);
    
    fetch(`{{ route('admin.chat.satisfaction.api.v1.metrics.index') }}?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateDashboard(data.data);
            }
        })
        .catch(error => console.error('Error refreshing metrics:', error));
}

function updateDashboard(metrics) {
    // Update overview metrics
    document.getElementById('average-rating').textContent = metrics.overview.average_rating.toFixed(1);
    document.getElementById('satisfaction-rate').textContent = metrics.overview.satisfaction_rate.toFixed(1) + '%';
    document.getElementById('total-ratings').textContent = metrics.overview.total_ratings;
    document.getElementById('response-rate').textContent = metrics.overview.response_rate.toFixed(1) + '%';
    document.getElementById('nps-score').textContent = metrics.nps_metrics.nps_score.toFixed(1);
    
    // Update charts and tables would go here
    console.log('Dashboard updated with new metrics:', metrics);
}

function exportReport(format) {
    const formData = new FormData(document.getElementById('satisfaction-filters'));
    formData.append('format', format);
    
    fetch('{{ route("admin.chat.satisfaction.api.v1.reports.export") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.download_url) {
                window.open(data.download_url, '_blank');
            } else {
                // Handle JSON export
                const blob = new Blob([JSON.stringify(data.data, null, 2)], {type: 'application/json'});
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `satisfaction-report-${format}-${new Date().toISOString().split('T')[0]}.${format}`;
                a.click();
                window.URL.revokeObjectURL(url);
            }
        }
    })
    .catch(error => console.error('Error exporting report:', error));
}
</script>
@endsection
