<?php

namespace App\SDK;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\MultipartStream;

/**
 * ChiSolution Chat SDK for PHP
 * 
 * A comprehensive PHP SDK for integrating with the ChiSolution Chat API
 * 
 * @version 1.0.0
 * <AUTHOR> Team
 */
class ChiChatSDK
{
    protected Client $httpClient;
    protected string $baseUrl;
    protected ?string $apiKey;
    protected ?string $token;
    protected int $timeout;
    protected int $retryAttempts;
    protected int $retryDelay;

    public function __construct(array $config = [])
    {
        $this->baseUrl = $config['base_url'] ?? 'http://localhost/api/v1/chat';
        $this->apiKey = $config['api_key'] ?? null;
        $this->token = $config['token'] ?? null;
        $this->timeout = $config['timeout'] ?? 30;
        $this->retryAttempts = $config['retry_attempts'] ?? 3;
        $this->retryDelay = $config['retry_delay'] ?? 1;

        $this->httpClient = new Client([
            'base_uri' => $this->baseUrl,
            'timeout' => $this->timeout,
            'headers' => $this->getDefaultHeaders(),
        ]);
    }

    /**
     * Get default headers for requests
     */
    protected function getDefaultHeaders(): array
    {
        $headers = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'User-Agent' => 'ChiSolution-Chat-SDK-PHP/1.0.0',
        ];

        if ($this->apiKey) {
            $headers['X-API-Key'] = $this->apiKey;
        }

        if ($this->token) {
            $headers['Authorization'] = "Bearer {$this->token}";
        }

        return $headers;
    }

    /**
     * Make HTTP request with retry logic
     */
    protected function request(string $method, string $endpoint, array $data = null, array $options = []): array
    {
        $url = ltrim($endpoint, '/');
        
        $requestOptions = array_merge([
            'headers' => $this->getDefaultHeaders(),
        ], $options);

        if ($data && in_array(strtoupper($method), ['POST', 'PUT', 'PATCH'])) {
            $requestOptions['json'] = $data;
        }

        for ($attempt = 1; $attempt <= $this->retryAttempts; $attempt++) {
            try {
                $response = $this->httpClient->request($method, $url, $requestOptions);
                return json_decode($response->getBody()->getContents(), true);
            } catch (RequestException $e) {
                if ($attempt === $this->retryAttempts) {
                    throw new ChiChatSDKException(
                        "Request failed after {$this->retryAttempts} attempts: " . $e->getMessage(),
                        $e->getCode(),
                        $e
                    );
                }
                
                sleep($this->retryDelay * $attempt);
            }
        }
    }

    /**
     * Create a new chat room
     */
    public function createRoom(array $roomData): array
    {
        return $this->request('POST', '/rooms', $roomData);
    }

    /**
     * Get chat room details
     */
    public function getRoom(string $roomUuid): array
    {
        return $this->request('GET', "/rooms/{$roomUuid}");
    }

    /**
     * Update chat room
     */
    public function updateRoom(string $roomUuid, array $data): array
    {
        return $this->request('PUT', "/rooms/{$roomUuid}", $data);
    }

    /**
     * Delete chat room
     */
    public function deleteRoom(string $roomUuid): array
    {
        return $this->request('DELETE', "/rooms/{$roomUuid}");
    }

    /**
     * Send a message to a chat room
     */
    public function sendMessage(string $roomUuid, array $messageData): array
    {
        return $this->request('POST', "/rooms/{$roomUuid}/messages", $messageData);
    }

    /**
     * Get messages from a chat room
     */
    public function getMessages(string $roomUuid, array $options = []): array
    {
        $query = http_build_query($options);
        $endpoint = "/rooms/{$roomUuid}/messages" . ($query ? "?{$query}" : '');
        return $this->request('GET', $endpoint);
    }

    /**
     * Join a chat room
     */
    public function joinRoom(string $roomUuid): array
    {
        return $this->request('POST', "/rooms/{$roomUuid}/join");
    }

    /**
     * Leave a chat room
     */
    public function leaveRoom(string $roomUuid): array
    {
        return $this->request('POST', "/rooms/{$roomUuid}/leave");
    }

    /**
     * Send typing indicator
     */
    public function sendTyping(string $roomUuid, bool $isTyping = true): array
    {
        return $this->request('POST', "/rooms/{$roomUuid}/typing", ['is_typing' => $isTyping]);
    }

    /**
     * Mark message as read
     */
    public function markAsRead(string $roomUuid, string $messageUuid): array
    {
        return $this->request('POST', "/rooms/{$roomUuid}/messages/{$messageUuid}/read");
    }

    /**
     * Upload file to chat room
     */
    public function uploadFile(string $roomUuid, string $filePath, string $messageContent = ''): array
    {
        if (!file_exists($filePath)) {
            throw new ChiChatSDKException("File not found: {$filePath}");
        }

        $multipart = [
            [
                'name' => 'file',
                'contents' => fopen($filePath, 'r'),
                'filename' => basename($filePath),
            ],
            [
                'name' => 'message_content',
                'contents' => $messageContent,
            ],
        ];

        return $this->request('POST', "/rooms/{$roomUuid}/files", null, [
            'multipart' => $multipart,
            'headers' => array_diff_key($this->getDefaultHeaders(), ['Content-Type' => '']),
        ]);
    }

    /**
     * Get chat statistics
     */
    public function getStatistics(): array
    {
        return $this->request('GET', '/statistics');
    }

    /**
     * Get room status
     */
    public function getRoomStatus(string $roomUuid): array
    {
        return $this->request('GET', "/rooms/{$roomUuid}/status");
    }

    /**
     * Get list of chat rooms
     */
    public function getRooms(array $filters = []): array
    {
        $query = http_build_query($filters);
        $endpoint = "/rooms" . ($query ? "?{$query}" : '');
        return $this->request('GET', $endpoint);
    }

    /**
     * Assign staff to chat room
     */
    public function assignStaff(string $roomUuid, int $staffId): array
    {
        return $this->request('POST', "/rooms/{$roomUuid}/assign", ['staff_id' => $staffId]);
    }

    /**
     * Transfer chat room to another staff
     */
    public function transferRoom(string $roomUuid, int $newStaffId, string $reason = ''): array
    {
        return $this->request('POST', "/rooms/{$roomUuid}/transfer", [
            'new_staff_id' => $newStaffId,
            'reason' => $reason,
        ]);
    }

    /**
     * Close chat room
     */
    public function closeRoom(string $roomUuid, string $reason = ''): array
    {
        return $this->request('POST', "/rooms/{$roomUuid}/close", ['reason' => $reason]);
    }

    /**
     * Submit chat rating
     */
    public function submitRating(string $roomUuid, int $rating, string $feedback = '', array $categories = []): array
    {
        return $this->request('POST', "/rooms/{$roomUuid}/rating", [
            'rating' => $rating,
            'feedback' => $feedback,
            'rating_categories' => $categories,
        ]);
    }

    /**
     * Get available staff
     */
    public function getAvailableStaff(): array
    {
        return $this->request('GET', '/staff/availability');
    }

    /**
     * Generate AI response (if AI is enabled)
     */
    public function generateAIResponse(string $roomUuid, string $message, array $context = []): array
    {
        return $this->request('POST', '/ai/generate-response', [
            'chat_room_id' => $roomUuid,
            'user_message' => $message,
            'context' => $context,
        ]);
    }

    /**
     * Batch operations
     */
    public function batchSendMessages(array $messages): array
    {
        return $this->request('POST', '/messages/batch', ['messages' => $messages]);
    }

    /**
     * Get chat analytics
     */
    public function getAnalytics(array $filters = []): array
    {
        $query = http_build_query($filters);
        $endpoint = "/analytics" . ($query ? "?{$query}" : '');
        return $this->request('GET', $endpoint);
    }

    /**
     * Health check
     */
    public function healthCheck(): array
    {
        return $this->request('GET', '/health');
    }

    /**
     * Set authentication token
     */
    public function setToken(string $token): self
    {
        $this->token = $token;
        return $this;
    }

    /**
     * Set API key
     */
    public function setApiKey(string $apiKey): self
    {
        $this->apiKey = $apiKey;
        return $this;
    }
}

/**
 * Custom exception for SDK errors
 */
class ChiChatSDKException extends \Exception
{
    //
}
