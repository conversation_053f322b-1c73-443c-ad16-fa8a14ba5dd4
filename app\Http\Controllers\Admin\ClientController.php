<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ClientController extends Controller
{
    public function index(Request $request)
    {
        $query = Client::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('company', 'like', "%{$search}%")
                  ->orWhere('industry', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->get('status') === 'active') {
                $query->where('is_active', true);
            } elseif ($request->get('status') === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Filter by featured
        if ($request->filled('featured')) {
            $query->where('is_featured', $request->get('featured') === '1');
        }

        // Exclude soft deleted
        $query->where('is_deleted', false);

        $clients = $query->ordered()->paginate(15);

        return view('admin.clients.index', compact('clients'));
    }

    public function create()
    {
        return view('admin.clients.create');
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'company' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'project_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'website_url' => 'nullable|url',
            'industry' => 'nullable|string|max:255',
            'project_start_date' => 'nullable|date',
            'project_end_date' => 'nullable|date|after_or_equal:project_start_date',
            'project_status' => 'required|in:completed,ongoing,paused',
            'testimonial' => 'nullable|string',
            'rating' => 'nullable|integer|min:1|max:5',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->except(['logo', 'project_images']);

        // Handle logo upload
        if ($request->hasFile('logo')) {
            $logoPath = $request->file('logo')->store('clients/logos', 'public');
            $data['logo_path'] = $logoPath;
        }

        // Handle project images upload
        if ($request->hasFile('project_images')) {
            $imagePaths = [];
            foreach ($request->file('project_images') as $image) {
                $imagePaths[] = $image->store('clients/projects', 'public');
            }
            $data['project_images'] = $imagePaths;
        }

        Client::create($data);

        return redirect()->route('admin.clients.index')
            ->with('success', 'Client created successfully.');
    }

    public function show(Client $client)
    {
        return view('admin.clients.show', compact('client'));
    }

    public function edit(Client $client)
    {
        return view('admin.clients.edit', compact('client'));
    }

    public function update(Request $request, Client $client)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'company' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'project_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'website_url' => 'nullable|url',
            'industry' => 'nullable|string|max:255',
            'project_start_date' => 'nullable|date',
            'project_end_date' => 'nullable|date|after_or_equal:project_start_date',
            'project_status' => 'required|in:completed,ongoing,paused',
            'testimonial' => 'nullable|string',
            'rating' => 'nullable|integer|min:1|max:5',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->except(['logo', 'project_images']);

        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo
            if ($client->logo_path) {
                Storage::disk('public')->delete($client->logo_path);
            }
            $logoPath = $request->file('logo')->store('clients/logos', 'public');
            $data['logo_path'] = $logoPath;
        }

        // Handle project images upload
        if ($request->hasFile('project_images')) {
            // Delete old images
            if ($client->project_images) {
                foreach ($client->project_images as $imagePath) {
                    Storage::disk('public')->delete($imagePath);
                }
            }
            $imagePaths = [];
            foreach ($request->file('project_images') as $image) {
                $imagePaths[] = $image->store('clients/projects', 'public');
            }
            $data['project_images'] = $imagePaths;
        }

        $client->update($data);

        return redirect()->route('admin.clients.index')
            ->with('success', 'Client updated successfully.');
    }

    public function destroy(Client $client)
    {
        $client->softDelete();

        return redirect()->route('admin.clients.index')
            ->with('success', 'Client deleted successfully.');
    }

    public function toggleFeatured(Client $client)
    {
        $client->toggleFeatured();

        return response()->json([
            'success' => true,
            'is_featured' => $client->is_featured,
            'message' => $client->is_featured ? 'Client featured successfully.' : 'Client unfeatured successfully.'
        ]);
    }

    public function toggleActive(Client $client)
    {
        $client->toggleActive();

        return response()->json([
            'success' => true,
            'is_active' => $client->is_active,
            'message' => $client->is_active ? 'Client activated successfully.' : 'Client deactivated successfully.'
        ]);
    }

    public function bulkAction(Request $request)
    {
        $action = $request->get('action');
        $clientIds = $request->get('client_ids', []);

        if (empty($clientIds)) {
            return redirect()->back()->with('error', 'No clients selected.');
        }

        $clients = Client::whereIn('id', $clientIds)->get();

        switch ($action) {
            case 'activate':
                $clients->each->toggleActive();
                $message = 'Selected clients activated successfully.';
                break;
            case 'deactivate':
                $clients->each(function ($client) {
                    if ($client->is_active) {
                        $client->toggleActive();
                    }
                });
                $message = 'Selected clients deactivated successfully.';
                break;
            case 'feature':
                $clients->each(function ($client) {
                    if (!$client->is_featured) {
                        $client->toggleFeatured();
                    }
                });
                $message = 'Selected clients featured successfully.';
                break;
            case 'unfeature':
                $clients->each(function ($client) {
                    if ($client->is_featured) {
                        $client->toggleFeatured();
                    }
                });
                $message = 'Selected clients unfeatured successfully.';
                break;
            case 'delete':
                $clients->each->softDelete();
                $message = 'Selected clients deleted successfully.';
                break;
            default:
                return redirect()->back()->with('error', 'Invalid action selected.');
        }

        return redirect()->back()->with('success', $message);
    }
}
