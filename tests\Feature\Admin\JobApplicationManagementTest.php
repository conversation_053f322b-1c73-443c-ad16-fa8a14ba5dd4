<?php

namespace Tests\Feature\Admin;

use App\Models\Job;
use App\Models\JobApplication;
use App\Models\Role;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

use PHPUnit\Framework\Attributes\Test;
class JobApplicationManagementTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin role using firstOrCreate to avoid conflicts
        $adminRole = Role::firstOrCreate(
            ['name' => 'admin'],
            [
                'slug' => 'admin',
                'description' => 'Administrator role with full permissions',
                'permissions' => [
                    'users' => ['create', 'read', 'update', 'delete'],
                    'roles' => ['create', 'read', 'update', 'delete'],
                    'projects' => ['create', 'read', 'update', 'delete'],
                    'orders' => ['create', 'read', 'update', 'delete'],
                    'services' => ['create', 'read', 'update', 'delete'],
                    'activity_logs' => ['read', 'delete'],
                    'dashboard' => ['access'],
                ],
                'is_active' => true,
            ]
        );

        $this->admin = User::factory()->create([
            'role_id' => $adminRole->id,
        ]);
    }

    /**
     * Test admin can view job applications index.
     */
    public function test_admin_can_view_job_applications_index(): void
    {
        $job = Job::create([
            'title' => 'Test Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Cape Town',
            'employment_type' => 'full-time',
            'experience_level' => 'mid',
            'department' => 'Engineering',
            'salary_period' => 'monthly',
        ]);

        JobApplication::create([
            'job_id' => $job->id,
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'phone' => '123456789',
            'country' => 'South Africa',
            'cover_letter' => 'Test cover letter',
            'experience_summary' => 'Test experience',
            'highest_qualification' => 'Bachelor Degree',
        ]);

        $response = $this->actingAs($this->admin)->get('/admin/job-applications');

        $response->assertStatus(200);
        $response->assertSee('Job Applications');
        $response->assertSee('John Doe');
        $response->assertSee('<EMAIL>');
        $response->assertSee('Test Job');
    }

    /**
     * Test admin can view job application details.
     */
    public function test_admin_can_view_job_application_details(): void
    {
        $job = Job::create([
            'title' => 'Software Developer',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Cape Town',
            'employment_type' => 'full-time',
            'experience_level' => 'mid',
            'department' => 'Engineering',
            'salary_period' => 'monthly',
        ]);

        $application = JobApplication::create([
            'job_id' => $job->id,
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'email' => '<EMAIL>',
            'phone' => '*********',
            'country' => 'South Africa',
            'cover_letter' => 'I am very interested in this position and believe I would be a great fit.',
            'experience_summary' => 'I have 5 years of experience in software development.',
            'highest_qualification' => 'Master Degree in Computer Science',
            'current_position' => 'Senior Developer',
            'current_company' => 'Tech Corp',
            'expected_salary' => 55000,
        ]);

        $response = $this->actingAs($this->admin)->get("/admin/job-applications/{$application->id}");

        $response->assertStatus(200);
        $response->assertSee('Jane Smith');
        $response->assertSee('<EMAIL>');
        $response->assertSee('Software Developer');
        $response->assertSee('I am very interested in this position');
        $response->assertSee('5 years of experience');
        $response->assertSee('Senior Developer');
        $response->assertSee('55,000');
    }

    /**
     * Test admin can update application status.
     */
    public function test_admin_can_update_application_status(): void
    {
        $job = Job::create([
            'title' => 'Test Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Cape Town',
            'employment_type' => 'full-time',
            'experience_level' => 'mid',
            'department' => 'Engineering',
            'salary_period' => 'monthly',
        ]);

        $application = JobApplication::create([
            'job_id' => $job->id,
            'first_name' => 'Status',
            'last_name' => 'Update',
            'email' => '<EMAIL>',
            'phone' => '123456789',
            'country' => 'South Africa',
            'cover_letter' => 'Test cover letter',
            'experience_summary' => 'Test experience',
            'highest_qualification' => 'Bachelor Degree',
            'status' => 'pending',
        ]);

        $updateData = [
            'status' => 'shortlisted',
            'admin_notes' => 'Candidate looks promising, moving to next stage.',
        ];

        $response = $this->actingAs($this->admin)->put("/admin/job-applications/{$application->id}", $updateData);

        $this->assertDatabaseHas('job_applications', [
            'id' => $application->id,
            'status' => 'shortlisted',
            'admin_notes' => 'Candidate looks promising, moving to next stage.',
        ]);

        $response->assertRedirect("/admin/job-applications/{$application->id}");
    }

    /**
     * Test admin can update application status via AJAX.
     */
    public function test_admin_can_update_application_status_via_ajax(): void
    {
        $job = Job::create([
            'title' => 'Test Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Cape Town',
            'employment_type' => 'full-time',
            'experience_level' => 'mid',
            'department' => 'Engineering',
            'salary_period' => 'monthly',
        ]);

        $application = JobApplication::create([
            'job_id' => $job->id,
            'first_name' => 'Ajax',
            'last_name' => 'Update',
            'email' => '<EMAIL>',
            'phone' => '123456789',
            'country' => 'South Africa',
            'cover_letter' => 'Test cover letter',
            'experience_summary' => 'Test experience',
            'highest_qualification' => 'Bachelor Degree',
            'status' => 'pending',
        ]);

        $response = $this->actingAs($this->admin)
            ->patchJson("/admin/job-applications/{$application->id}/status", [
                'status' => 'reviewing'
            ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $this->assertDatabaseHas('job_applications', [
            'id' => $application->id,
            'status' => 'reviewing',
        ]);
    }

    /**
     * Test admin can filter applications by status.
     */
    public function test_admin_can_filter_applications_by_status(): void
    {
        $job = Job::create([
            'title' => 'Test Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Cape Town',
            'employment_type' => 'full-time',
            'experience_level' => 'mid',
            'department' => 'Engineering',
            'salary_period' => 'monthly',
        ]);

        // Create applications with different statuses
        JobApplication::create([
            'job_id' => $job->id,
            'first_name' => 'Pending',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'phone' => '111111111',
            'country' => 'South Africa',
            'cover_letter' => 'Test cover letter',
            'experience_summary' => 'Test experience',
            'highest_qualification' => 'Bachelor Degree',
            'status' => 'pending',
        ]);

        JobApplication::create([
            'job_id' => $job->id,
            'first_name' => 'Shortlisted',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'phone' => '222222222',
            'country' => 'South Africa',
            'cover_letter' => 'Test cover letter',
            'experience_summary' => 'Test experience',
            'highest_qualification' => 'Bachelor Degree',
            'status' => 'shortlisted',
        ]);

        // Test filtering by pending status
        $response = $this->actingAs($this->admin)->get('/admin/job-applications?status=pending');
        $response->assertStatus(200);
        $response->assertSee('<EMAIL>');
        $response->assertDontSee('<EMAIL>');

        // Test filtering by shortlisted status
        $response = $this->actingAs($this->admin)->get('/admin/job-applications?status=shortlisted');
        $response->assertStatus(200);
        $response->assertSee('<EMAIL>');
        $response->assertDontSee('<EMAIL>');
    }

    /**
     * Test admin can filter applications by job.
     */
    public function test_admin_can_filter_applications_by_job(): void
    {
        $job1 = Job::create([
            'title' => 'Developer Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Cape Town',
            'employment_type' => 'full-time',
            'experience_level' => 'mid',
            'department' => 'Engineering',
            'salary_period' => 'monthly',
        ]);

        $job2 = Job::create([
            'title' => 'Designer Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Johannesburg',
            'employment_type' => 'full-time',
            'experience_level' => 'mid',
            'department' => 'Design',
            'salary_period' => 'monthly',
        ]);

        JobApplication::create([
            'job_id' => $job1->id,
            'first_name' => 'Developer',
            'last_name' => 'Applicant',
            'email' => '<EMAIL>',
            'phone' => '111111111',
            'country' => 'South Africa',
            'cover_letter' => 'Test cover letter',
            'experience_summary' => 'Test experience',
            'highest_qualification' => 'Bachelor Degree',
        ]);

        JobApplication::create([
            'job_id' => $job2->id,
            'first_name' => 'Designer',
            'last_name' => 'Applicant',
            'email' => '<EMAIL>',
            'phone' => '222222222',
            'country' => 'South Africa',
            'cover_letter' => 'Test cover letter',
            'experience_summary' => 'Test experience',
            'highest_qualification' => 'Bachelor Degree',
        ]);

        $response = $this->actingAs($this->admin)->get("/admin/job-applications?job_id={$job1->id}");
        $response->assertStatus(200);
        $response->assertSee('<EMAIL>');
        $response->assertDontSee('<EMAIL>');
    }

    /**
     * Test admin can delete job application.
     */
    public function test_admin_can_delete_job_application(): void
    {
        $job = Job::create([
            'title' => 'Test Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Cape Town',
            'employment_type' => 'full-time',
            'experience_level' => 'mid',
            'department' => 'Engineering',
            'salary_period' => 'monthly',
        ]);

        $application = JobApplication::create([
            'job_id' => $job->id,
            'first_name' => 'Delete',
            'last_name' => 'Me',
            'email' => '<EMAIL>',
            'phone' => '123456789',
            'country' => 'South Africa',
            'cover_letter' => 'Test cover letter',
            'experience_summary' => 'Test experience',
            'highest_qualification' => 'Bachelor Degree',
        ]);

        $response = $this->actingAs($this->admin)->delete("/admin/job-applications/{$application->id}");

        $this->assertSoftDeleted('job_applications', [
            'id' => $application->id,
        ]);

        $response->assertRedirect('/admin/job-applications');
    }

    /**
     * Test non-admin cannot access job application management.
     */
    public function test_non_admin_cannot_access_job_application_management(): void
    {
        $customerRole = Role::firstOrCreate(
            ['name' => 'customer'],
            [
                'slug' => 'customer',
                'description' => 'Customer role with basic permissions',
                'permissions' => [
                    'profile' => ['read', 'update'],
                    'projects' => ['create', 'read'],
                    'orders' => ['create', 'read'],
                    'dashboard' => ['access'],
                ],
                'is_active' => true,
            ]
        );
        $user = User::factory()->create(['role_id' => $customerRole->id]);

        $response = $this->actingAs($user)->get('/admin/job-applications');
        $response->assertStatus(403);
    }

    /**
     * Test guest cannot access job application management.
     */
    public function test_guest_cannot_access_job_application_management(): void
    {
        $response = $this->get('/admin/job-applications');
        $response->assertRedirect('/login');
    }
}
