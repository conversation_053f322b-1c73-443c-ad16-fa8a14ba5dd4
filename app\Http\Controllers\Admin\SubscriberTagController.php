<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SubscriberTag;
use App\Models\NewsletterSubscription;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;

class SubscriberTagController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): View|JsonResponse
    {
        $query = SubscriberTag::withCount(['subscribers']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $tags = $query->paginate(15)->withQueryString();

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'data' => $tags,
            ]);
        }

        return view('admin.subscriber-tags.index', compact('tags'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        return view('admin.subscriber-tags.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse|RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:subscriber_tags,name',
            'description' => 'nullable|string|max:1000',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_active' => 'boolean',
        ]);

        $validated['created_by'] = auth()->id();
        $validated['updated_by'] = auth()->id();

        $tag = SubscriberTag::create($validated);

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Subscriber tag created successfully.',
                'data' => $tag->load(['creator', 'updater']),
            ]);
        }

        return redirect()->route('admin.subscriber-tags.index')
                        ->with('success', 'Subscriber tag created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(SubscriberTag $subscriberTag): View
    {
        $subscriberTag->load(['creator', 'updater']);

        // Get subscribers with this tag
        $subscribers = $subscriberTag->subscribers()
            ->with(['tags'])
            ->paginate(20);

        // Get tag statistics
        $statistics = [
            'total_subscribers' => $subscriberTag->subscribers()->count(),
            'active_subscribers' => $subscriberTag->subscribers()->where('is_active', true)->count(),
            'engagement_stats' => $subscriberTag->subscribers()
                ->selectRaw('
                    AVG(engagement_score) as avg_engagement,
                    AVG(total_emails_opened) as avg_opens,
                    AVG(total_emails_clicked) as avg_clicks
                ')
                ->first(),
            'lifecycle_distribution' => $subscriberTag->subscribers()
                ->select('lifecycle_stage', DB::raw('count(*) as count'))
                ->groupBy('lifecycle_stage')
                ->pluck('count', 'lifecycle_stage')
                ->toArray(),
        ];

        return view('admin.subscriber-tags.show', compact('subscriberTag', 'subscribers', 'statistics'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(SubscriberTag $subscriberTag): View
    {
        return view('admin.subscriber-tags.edit', compact('subscriberTag'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, SubscriberTag $subscriberTag): JsonResponse|RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:subscriber_tags,name,' . $subscriberTag->id,
            'description' => 'nullable|string|max:1000',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_active' => 'boolean',
        ]);

        $validated['updated_by'] = auth()->id();

        $subscriberTag->update($validated);

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Subscriber tag updated successfully.',
                'data' => $subscriberTag->fresh(['creator', 'updater']),
            ]);
        }

        return redirect()->route('admin.subscriber-tags.show', $subscriberTag)
                        ->with('success', 'Subscriber tag updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SubscriberTag $subscriberTag): JsonResponse|RedirectResponse
    {
        // Check if tag is being used
        $subscriberCount = $subscriberTag->subscribers()->count();

        if ($subscriberCount > 0) {
            if (request()->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => "Cannot delete tag. It is currently assigned to {$subscriberCount} subscribers.",
                ], 422);
            }

            return redirect()->back()
                            ->with('error', "Cannot delete tag. It is currently assigned to {$subscriberCount} subscribers.");
        }

        $subscriberTag->delete();

        if (request()->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Subscriber tag deleted successfully.',
            ]);
        }

        return redirect()->route('admin.subscriber-tags.index')
                        ->with('success', 'Subscriber tag deleted successfully.');
    }

    /**
     * Toggle tag status.
     */
    public function toggleStatus(SubscriberTag $subscriberTag): JsonResponse
    {
        $subscriberTag->update(['is_active' => !$subscriberTag->is_active]);

        return response()->json([
            'success' => true,
            'message' => 'Tag status updated successfully.',
            'is_active' => $subscriberTag->is_active,
        ]);
    }

    /**
     * Assign tag to subscribers.
     */
    public function assignToSubscribers(SubscriberTag $subscriberTag, Request $request): JsonResponse
    {
        $request->validate([
            'subscriber_ids' => 'required|array|min:1',
            'subscriber_ids.*' => 'exists:newsletter_subscriptions,id',
        ]);

        $subscriberIds = $request->get('subscriber_ids');

        // Get subscribers that don't already have this tag
        $existingSubscriberIds = $subscriberTag->subscribers()->pluck('newsletter_subscriptions.id')->toArray();
        $newSubscriberIds = array_diff($subscriberIds, $existingSubscriberIds);

        if (empty($newSubscriberIds)) {
            return response()->json([
                'success' => false,
                'message' => 'All selected subscribers already have this tag.',
            ], 422);
        }

        // Attach tag to new subscribers
        $subscriberTag->subscribers()->attach($newSubscriberIds, [
            'tagged_at' => now(),
            'tagged_by' => auth()->id(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $assignedCount = count($newSubscriberIds);

        return response()->json([
            'success' => true,
            'message' => "Tag assigned to {$assignedCount} subscribers successfully.",
            'assigned_count' => $assignedCount,
        ]);
    }

    /**
     * Remove tag from subscribers.
     */
    public function removeFromSubscribers(SubscriberTag $subscriberTag, Request $request): JsonResponse
    {
        $request->validate([
            'subscriber_ids' => 'required|array|min:1',
            'subscriber_ids.*' => 'exists:newsletter_subscriptions,id',
        ]);

        $subscriberIds = $request->get('subscriber_ids');

        $subscriberTag->subscribers()->detach($subscriberIds);

        $removedCount = count($subscriberIds);

        return response()->json([
            'success' => true,
            'message' => "Tag removed from {$removedCount} subscribers successfully.",
            'removed_count' => $removedCount,
        ]);
    }

    /**
     * Get subscribers for tag assignment.
     */
    public function getAvailableSubscribers(SubscriberTag $subscriberTag, Request $request): JsonResponse
    {
        $search = $request->get('search', '');
        $excludeExisting = $request->get('exclude_existing', true);

        $query = NewsletterSubscription::select(['id', 'email', 'name', 'lifecycle_stage', 'engagement_score'])
            ->where('is_active', true);

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('email', 'like', "%{$search}%")
                  ->orWhere('name', 'like', "%{$search}%");
            });
        }

        if ($excludeExisting) {
            $existingSubscriberIds = $subscriberTag->subscribers()->pluck('newsletter_subscriptions.id');
            $query->whereNotIn('id', $existingSubscriberIds);
        }

        $subscribers = $query->orderBy('email')
                            ->limit(50)
                            ->get();

        return response()->json([
            'success' => true,
            'subscribers' => $subscribers,
        ]);
    }

    /**
     * Bulk actions for tags.
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $request->validate([
            'action' => 'required|string|in:activate,deactivate,delete',
            'tag_ids' => 'required|array|min:1',
            'tag_ids.*' => 'exists:subscriber_tags,id',
        ]);

        $tagIds = $request->get('tag_ids');
        $action = $request->get('action');

        $tags = SubscriberTag::whereIn('id', $tagIds);

        switch ($action) {
            case 'activate':
                $tags->update(['is_active' => true]);
                $message = 'Tags activated successfully.';
                break;
            case 'deactivate':
                $tags->update(['is_active' => false]);
                $message = 'Tags deactivated successfully.';
                break;
            case 'delete':
                $deletableTags = $tags->get()->filter(function ($tag) {
                    return $tag->subscribers()->count() === 0;
                });
                $deletableTags->each(fn($tag) => $tag->delete());
                $deletedCount = $deletableTags->count();
                $totalCount = count($tagIds);

                if ($deletedCount === $totalCount) {
                    $message = 'All selected tags deleted successfully.';
                } else {
                    $skippedCount = $totalCount - $deletedCount;
                    $message = "{$deletedCount} tags deleted successfully. {$skippedCount} tags could not be deleted because they have subscribers.";
                }
                break;
        }

        return response()->json([
            'success' => true,
            'message' => $message,
        ]);
    }

    /**
     * Get tag statistics.
     */
    public function getStatistics(SubscriberTag $subscriberTag): JsonResponse
    {
        $statistics = [
            'total_subscribers' => $subscriberTag->subscribers()->count(),
            'active_subscribers' => $subscriberTag->subscribers()->where('is_active', true)->count(),
            'engagement_stats' => $subscriberTag->subscribers()
                ->selectRaw('
                    AVG(engagement_score) as avg_engagement,
                    AVG(total_emails_opened) as avg_opens,
                    AVG(total_emails_clicked) as avg_clicks
                ')
                ->first(),
            'lifecycle_distribution' => $subscriberTag->subscribers()
                ->select('lifecycle_stage', DB::raw('count(*) as count'))
                ->groupBy('lifecycle_stage')
                ->pluck('count', 'lifecycle_stage')
                ->toArray(),
            'recent_activity' => $subscriberTag->subscribers()
                ->where('last_activity_at', '>=', now()->subDays(30))
                ->count(),
        ];

        return response()->json([
            'success' => true,
            'statistics' => $statistics,
        ]);
    }
}
