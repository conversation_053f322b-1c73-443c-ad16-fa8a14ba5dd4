#!/bin/bash

# =============================================================================
# ChiSolution Digital Agency - Production Deployment Script
# =============================================================================

set -e  # Exit on any error

echo "🚀 Starting Production Deployment..."

# Configuration
APP_DIR="/var/www/chisolution"
BACKUP_DIR="/var/backups/chisolution"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Pre-deployment checks
log_info "Running pre-deployment checks..."

# Check if we're in the correct directory
if [ ! -f "artisan" ]; then
    log_error "artisan file not found. Are you in the Laravel project root?"
    exit 1
fi

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
    log_error ".env.production file not found!"
    exit 1
fi

# Backup current deployment
log_info "Creating backup..."
mkdir -p $BACKUP_DIR
if [ -d "$APP_DIR" ]; then
    tar -czf "$BACKUP_DIR/backup_$TIMESTAMP.tar.gz" -C "$APP_DIR" .
    log_info "Backup created: $BACKUP_DIR/backup_$TIMESTAMP.tar.gz"
fi

# Put application in maintenance mode
log_info "Enabling maintenance mode..."
php artisan down --message="Deploying new version. We'll be back shortly!" --retry=60

# Git operations
log_info "Pulling latest code from repository..."
git fetch origin
git reset --hard origin/main

# Copy production environment file
log_info "Setting up production environment..."
cp .env.production .env

# Install/update dependencies
log_info "Installing Composer dependencies..."
composer install --no-dev --optimize-autoloader --no-interaction

log_info "Installing NPM dependencies..."
npm ci --production

# Build assets
log_info "Building production assets..."
npm run build

# Database operations
log_info "Running database migrations..."
php artisan migrate --force

# Clear and optimize caches
log_info "Optimizing application..."
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache
php artisan optimize

# Set proper permissions
log_info "Setting file permissions..."
chown -R www-data:www-data storage bootstrap/cache
chmod -R 775 storage bootstrap/cache

# Queue restart (if using queue workers)
log_info "Restarting queue workers..."
php artisan queue:restart

# Clear application cache
log_info "Clearing application cache..."
php artisan cache:clear

# Disable maintenance mode
log_info "Disabling maintenance mode..."
php artisan up

# Health check
log_info "Running health check..."
if curl -f -s http://localhost/up > /dev/null; then
    log_info "✅ Health check passed!"
else
    log_error "❌ Health check failed!"
    log_warning "Consider rolling back the deployment"
fi

# Cleanup old backups (keep last 5)
log_info "Cleaning up old backups..."
cd $BACKUP_DIR
ls -t backup_*.tar.gz | tail -n +6 | xargs -r rm

log_info "🎉 Production deployment completed successfully!"
log_info "Backup location: $BACKUP_DIR/backup_$TIMESTAMP.tar.gz"

echo ""
echo "Post-deployment checklist:"
echo "- [ ] Verify website is accessible"
echo "- [ ] Check error logs: tail -f storage/logs/laravel.log"
echo "- [ ] Monitor application performance"
echo "- [ ] Test critical user flows"
echo "- [ ] Verify payment processing"
