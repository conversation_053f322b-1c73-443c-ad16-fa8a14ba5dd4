<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Project;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Support\Facades\Cache;
use App\Services\DashboardCacheService;
use App\Services\VisitorAnalytics;
use App\Services\ActivityLogger;

class DashboardController extends Controller
{
    protected DashboardCacheService $cacheService;
    protected VisitorAnalytics $visitorAnalytics;
    protected ActivityLogger $activityLogger;

    /**
     * Create a new controller instance.
     */
    public function __construct(DashboardCacheService $cacheService, VisitorAnalytics $visitorAnalytics, ActivityLogger $activityLogger)
    {
        $this->middleware('auth');
        $this->cacheService = $cacheService;
        $this->visitorAnalytics = $visitorAnalytics;
        $this->activityLogger = $activityLogger;
    }

    /**
     * Show the customer/client dashboard.
     */
    public function index()
    {
        $user = auth()->user();

        // Redirect admin/staff users to admin dashboard
        if ($user->isAdminOrStaff()) {
            return redirect()->route('admin.dashboard');
        }

        // Track dashboard visit in visitor analytics
        $this->visitorAnalytics->trackPageVisit(
            'Customer Dashboard',
            [
                'user_id' => $user->id,
                'user_role' => $user->role,
                'dashboard_type' => 'customer',
            ]
        );

        // Get dashboard data based on user role
        $dashboardData = $this->getDashboardData($user);

        return view('dashboard.index', $dashboardData);
    }

    /**
     * Get dashboard data based on user role.
     */
    private function getDashboardData(User $user): array
    {
        $data = [
            'user' => $user,
            'recent_activity' => [],
            'quick_stats' => [],
            'user_type' => 'customer', // Default to customer
        ];

        if ($user->isCustomer()) {
            $data = array_merge($data, $this->getCustomerDashboardData($user));
        } elseif ($user->isClient()) {
            $data = array_merge($data, $this->getClientDashboardData($user));
        } else {
            // For admin/staff users who access the regular dashboard
            $data['user_type'] = $user->role->name ?? 'customer';
            $data['quick_stats'] = [
                'total_projects' => 0,
                'active_projects' => 0,
                'total_project_value' => 0,
                'account_status' => $user->is_active ? 'active' : 'inactive',
            ];
        }

        return $data;
    }

    /**
     * Get customer-specific dashboard data.
     */
    private function getCustomerDashboardData(User $user): array
    {
        $data = $this->cacheService->getCustomerDashboardData($user);

        return array_merge($data, [
            'user_type' => 'customer',
            'recent_activity' => $this->getCustomerRecentActivity($user),
        ]);
    }

    /**
     * Get client-specific dashboard data.
     */
    private function getClientDashboardData(User $user): array
    {
        $data = $this->cacheService->getClientDashboardData($user);

        return array_merge($data, [
            'user_type' => 'client',
            'recent_activity' => $this->getClientRecentActivity($user),
        ]);
    }

    /**
     * Get recent activity for customers.
     */
    private function getCustomerRecentActivity(User $user): array
    {
        $activities = [];

        // Recent orders
        $recentOrders = Order::where('user_id', $user->id)
                            ->where('is_deleted', false)
                            ->orderBy('created_at', 'desc')
                            ->limit(3)
                            ->get();

        foreach ($recentOrders as $order) {
            $activities[] = [
                'type' => 'order',
                'title' => "Order #{$order->order_number}",
                'description' => "Order {$order->status}",
                'date' => $order->created_at,
                'url' => route('orders.show', $order),
                'icon' => 'shopping-bag',
                'status' => $order->status,
            ];
        }

        // Sort by date
        usort($activities, function ($a, $b) {
            return $b['date'] <=> $a['date'];
        });

        return array_slice($activities, 0, 5);
    }

    /**
     * Get recent activity for clients.
     */
    private function getClientRecentActivity(User $user): array
    {
        $activities = [];

        // Recent projects
        $recentProjects = Project::where('client_id', $user->id)
                                ->where('is_deleted', false)
                                ->orderBy('updated_at', 'desc')
                                ->limit(3)
                                ->get();

        foreach ($recentProjects as $project) {
            $activities[] = [
                'type' => 'project',
                'title' => $project->title,
                'description' => "Project {$project->status}",
                'date' => $project->updated_at,
                'url' => route('projects.show', $project),
                'icon' => 'briefcase',
                'status' => $project->status,
            ];
        }

        // Recent orders
        $recentOrders = Order::where('user_id', $user->id)
                            ->where('is_deleted', false)
                            ->orderBy('created_at', 'desc')
                            ->limit(2)
                            ->get();

        foreach ($recentOrders as $order) {
            $activities[] = [
                'type' => 'order',
                'title' => "Order #{$order->order_number}",
                'description' => "Order {$order->status}",
                'date' => $order->created_at,
                'url' => route('orders.show', $order),
                'icon' => 'shopping-bag',
                'status' => $order->status,
            ];
        }

        // Sort by date
        usort($activities, function ($a, $b) {
            return $b['date'] <=> $a['date'];
        });

        return array_slice($activities, 0, 5);
    }

    /**
     * Get dashboard search results.
     */
    public function search(Request $request)
    {
        $query = $request->get('q');
        $user = auth()->user();

        if (empty($query)) {
            // Log empty search attempt
            $this->activityLogger->logCustomerActivity(
                'dashboard_search_empty',
                'Dashboard search attempted with empty query',
                'success',
                null,
                ['search_query' => '', 'user_type' => $user->role->name ?? 'unknown'],
                ['results_count' => 0]
            );
            return response()->json(['results' => []]);
        }

        $results = [];

        if ($user->isCustomer()) {
            $results = $this->searchCustomerContent($user, $query);
        } elseif ($user->isClient()) {
            $results = $this->searchClientContent($user, $query);
        }

        // Log search activity with results
        $this->activityLogger->logCustomerActivity(
            'dashboard_search',
            "Dashboard search performed: {$query}",
            'success',
            null,
            [
                'search_query' => $query,
                'user_type' => $user->role->name ?? 'unknown',
                'search_length' => strlen($query),
            ],
            [
                'results_count' => count($results),
                'search_successful' => count($results) > 0,
            ]
        );

        return response()->json(['results' => $results]);
    }

    /**
     * Search customer content.
     */
    private function searchCustomerContent(User $user, string $query): array
    {
        $results = [];

        // Search orders
        $orders = Order::where('user_id', $user->id)
                      ->where('is_deleted', false)
                      ->where(function ($q) use ($query) {
                          $q->where('order_number', 'like', "%{$query}%")
                            ->orWhere('status', 'like', "%{$query}%");
                      })
                      ->limit(5)
                      ->get();

        foreach ($orders as $order) {
            $results[] = [
                'title' => "Order #{$order->order_number}",
                'description' => "Status: {$order->status}",
                'url' => route('orders.show', $order),
                'type' => 'order',
            ];
        }

        return $results;
    }

    /**
     * Search client content.
     */
    private function searchClientContent(User $user, string $query): array
    {
        $results = [];

        // Search projects
        $projects = Project::where('client_id', $user->id)
                          ->where('is_deleted', false)
                          ->where(function ($q) use ($query) {
                              $q->where('title', 'like', "%{$query}%")
                                ->orWhere('description', 'like', "%{$query}%")
                                ->orWhere('status', 'like', "%{$query}%");
                          })
                          ->limit(5)
                          ->get();

        foreach ($projects as $project) {
            $results[] = [
                'title' => $project->title,
                'description' => "Status: {$project->status}",
                'url' => route('my-projects.show', $project),
                'type' => 'project',
            ];
        }

        // Search orders
        $orders = Order::where('user_id', $user->id)
                      ->where('is_deleted', false)
                      ->where(function ($q) use ($query) {
                          $q->where('order_number', 'like', "%{$query}%")
                            ->orWhere('status', 'like', "%{$query}%");
                      })
                      ->limit(3)
                      ->get();

        foreach ($orders as $order) {
            $results[] = [
                'title' => "Order #{$order->order_number}",
                'description' => "Status: {$order->status}",
                'url' => route('orders.show', $order),
                'type' => 'order',
            ];
        }

        return $results;
    }
}
