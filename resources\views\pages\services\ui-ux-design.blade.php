@extends('layouts.app')

@section('title', __('ui-ux-design.page_title'))
@section('meta_description', __('ui-ux-design.meta_description'))
@section('meta_keywords', __('ui-ux-design.meta_keywords'))

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-to-br from-blue-700 via-blue-800 to-indigo-900 text-white overflow-hidden">
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
    </div>
    
    <div class="container mx-auto px-4 py-20 lg:py-32 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            
            <div class="text-center lg:text-left">
                <h1 class="text-5xl lg:text-6xl font-extrabold text-white mb-6 leading-tight">
                    Professional UI/UX<br>Design Services
                </h1>
                <p class="text-xl text-blue-200 mb-10 max-w-lg mx-auto lg:mx-0">
                    Create intuitive, engaging user experiences with our expert UI/UX design services. From mobile apps to websites, we design interfaces that users love and businesses trust.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <a href="{{ route('contact', ['locale' => app()->getLocale()]) }}" 
                       class="px-8 py-3 text-lg rounded-full font-semibold transition-all duration-300
                              bg-white text-blue-700 shadow-xl hover:bg-gray-100 hover:scale-[1.02] inline-flex items-center justify-center">
                        Get Quote 
                        <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path></svg>
                    </a>
                    <a href="{{ route('projects.index', ['locale' => app()->getLocale()]) }}" 
                       class="px-8 py-3 text-lg rounded-full font-semibold transition-all duration-300
                              border-2 border-white text-white hover:bg-white hover:text-blue-700 inline-flex items-center justify-center">
                        View Portfolio
                    </a>
                </div>
            </div>
            
            <div class="flex justify-center lg:justify-end">
                <div class="w-full max-w-lg p-6 sm:p-10 rounded-2xl bg-white shadow-2xl">
                    <div class="grid grid-cols-2 gap-6 text-gray-800">
                        
                        <div class="flex flex-col items-center p-4 rounded-xl border border-gray-100 transition-transform duration-300 hover:scale-105 hover:shadow-lg bg-gray-50">
                            <div class="w-16 h-16 mb-2 flex items-center justify-center rounded-xl bg-gray-900 shadow-md">
                                <span class="!text-white text-xl font-bold">Fg</span>
                            </div>
                            <span class="text-base font-medium text-gray-800">Figma</span>
                        </div>
                        
                        <div class="flex flex-col items-center p-4 rounded-xl border border-pink-100 transition-transform duration-300 hover:scale-105 hover:shadow-lg bg-pink-50">
                            <div class="w-16 h-16 mb-2 flex items-center justify-center rounded-xl bg-pink-500 shadow-md">
                                <span class="text-white text-xl font-bold">Xd</span>
                            </div>
                            <span class="text-base font-medium text-gray-800">Adobe XD</span>
                        </div>
                        
                        <div class="flex flex-col items-center p-4 rounded-xl border border-yellow-100 transition-transform duration-300 hover:scale-105 hover:shadow-lg bg-yellow-50">
                            <div class="w-16 h-16 mb-2 flex items-center justify-center rounded-xl bg-yellow-500 shadow-md">
                                <span class="text-white text-xl font-bold">S</span>
                            </div>
                            <span class="text-base font-medium text-gray-800">Sketch</span>
                        </div>
                        
                        <div class="flex flex-col items-center p-4 rounded-xl border border-blue-100 transition-transform duration-300 hover:scale-105 hover:shadow-lg bg-blue-50">
                            <div class="w-16 h-16 mb-2 flex items-center justify-center rounded-xl bg-blue-500 shadow-md">
                                <span class="text-white text-xl font-bold">Ps</span>
                            </div>
                            <span class="text-base font-medium text-gray-800">Photoshop</span>
                        </div>
                        
                    </div>
                </div>
            </div>           
        </div>
    </div>
    
    <div class="absolute bottom-0 left-0 w-full">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block w-full h-16 fill-white">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
        </svg>
    </div>
</section>

<!-- UI/UX Design Overview -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                What is <span class="text-blue-600">UI/UX Design?</span>
            </h2>
            <p class="text-lead max-w-4xl mx-auto">
                UI/UX design focuses on creating intuitive, engaging, and seamless user experiences. While UI (User Interface) design deals with the visual elements users interact with, UX (User Experience) design ensures the overall experience is pleasant and meets user goals.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-12 mb-16">
            <!-- UI Design -->
            <div class="card-hover">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 mb-4">UI Design (User Interface)</h3>
                <p class="text-gray-600 mb-4">
                    UI design focuses on the visual elements that users interact with on a website or app. This includes:
                </p>
                <ul class="text-gray-600 space-y-2">
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Layout and structure of elements (buttons, navigation, forms)
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Color schemes, fonts, icons, and graphical elements
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Clean, aesthetically pleasing, and easy-to-navigate interfaces
                    </li>
                </ul>
            </div>
            
            <!-- UX Design -->
            <div class="card-hover">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 mb-4">UX Design (User Experience)</h3>
                <p class="text-gray-600 mb-4">
                    UX design focuses on the overall experience of a user as they interact with the product. This involves:
                </p>
                <ul class="text-gray-600 space-y-2">
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Understanding user needs and pain points
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Mapping out user flows and ensuring intuitive navigation
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Creating wireframes, prototypes, and conducting user testing
                    </li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Design Tools & Platforms -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                Our <span class="text-blue-600">Design Tools</span> & Platforms
            </h2>
            <p class="text-lead max-w-3xl mx-auto">
                We use industry-leading design tools and platforms to create stunning, functional designs that meet your business objectives.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Web Design with WordPress -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Web Design with WordPress</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    Custom WordPress themes and designs that are responsive, SEO-friendly, and easy to manage. Perfect for businesses looking for scalable web solutions.
                </p>
            </div>
            
            <!-- Design with Figma -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Design with Figma</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    Collaborative design using Figma for real-time feedback, prototyping, and design systems. Perfect for team collaboration and iterative design processes.
                </p>
            </div>
            
            <!-- Design with Photoshop -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Design with Photoshop</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    Professional image editing and graphic design using Adobe Photoshop for high-quality visual assets, mockups, and detailed design work.
                </p>
            </div>
            
            <!-- Design with Sketch -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Design with Sketch</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    Vector-based design using Sketch for creating scalable UI elements, icons, and interface designs with precision and flexibility.
                </p>
            </div>
            
            <!-- Design with Adobe XD -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 6.707 6.293a1 1 0 00-1.414 1.414L8.586 11l-3.293 3.293a1 1 0 001.414 1.414L10 12.414l3.293 3.293a1 1 0 001.414-1.414L11.414 11l3.293-3.293z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Design with Adobe XD</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    Interactive prototyping and user experience design using Adobe XD for creating dynamic, clickable prototypes and user journey mapping.
                </p>
            </div>
            
            <!-- Mobile App Design -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7 2a2 2 0 00-2 2v12a2 2 0 002 2h6a2 2 0 002-2V4a2 2 0 00-2-2H7zM8 5a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zm1 10a1 1 0 100 2h2a1 1 0 100-2H9z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Mobile App Design</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    Native and cross-platform mobile app UI/UX design focusing on touch interactions, mobile-first design principles, and platform-specific guidelines.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Our Design Capabilities -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                Our <span class="text-blue-600">Design Capabilities</span>
            </h2>
            <p class="text-lead max-w-3xl mx-auto">
                From concept to completion, we provide comprehensive UI/UX design services that drive user engagement and business success.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Website Design -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Website Design</h3>
                <p class="text-gray-600 text-sm leading-relaxed mb-4">
                    Responsive, modern website designs that work seamlessly across all devices and browsers.
                </p>
                <ul class="text-gray-600 text-sm space-y-1">
                    <li>• Responsive design for all screen sizes</li>
                    <li>• Modern, clean aesthetics</li>
                    <li>• Fast loading and optimized performance</li>
                    <li>• SEO-friendly structure</li>
                </ul>
            </div>

            <!-- Mobile App Design -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7 2a2 2 0 00-2 2v12a2 2 0 002 2h6a2 2 0 002-2V4a2 2 0 00-2-2H7zM8 5a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zm1 10a1 1 0 100 2h2a1 1 0 100-2H9z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Mobile App Design</h3>
                <p class="text-gray-600 text-sm leading-relaxed mb-4">
                    Native iOS and Android app designs that follow platform guidelines and best practices.
                </p>
                <ul class="text-gray-600 text-sm space-y-1">
                    <li>• iOS and Android design guidelines</li>
                    <li>• Touch-friendly interface elements</li>
                    <li>• Intuitive navigation patterns</li>
                    <li>• Optimized for mobile performance</li>
                </ul>
            </div>

            <!-- User Interface Design -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">User Interface Design</h3>
                <p class="text-gray-600 text-sm leading-relaxed mb-4">
                    Clean, intuitive interfaces that make complex interactions simple and enjoyable.
                </p>
                <ul class="text-gray-600 text-sm space-y-1">
                    <li>• Consistent design systems</li>
                    <li>• Accessible design principles</li>
                    <li>• Interactive prototypes</li>
                    <li>• Component libraries</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Our Design Process -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                Our <span class="text-blue-600">Design Process</span>
            </h2>
            <p class="text-lead max-w-3xl mx-auto">
                We follow a proven design methodology that ensures your project meets user needs and business objectives.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Step 1: Research & Discovery -->
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                    1
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Research & Discovery</h3>
                <p class="text-gray-600 text-sm">
                    Understanding your users, business goals, and market landscape through comprehensive research and analysis.
                </p>
            </div>

            <!-- Step 2: Wireframing & Planning -->
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                    2
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Wireframing & Planning</h3>
                <p class="text-gray-600 text-sm">
                    Creating wireframes and user flows to map out the structure and functionality of your digital product.
                </p>
            </div>

            <!-- Step 3: Visual Design -->
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                    3
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Visual Design</h3>
                <p class="text-gray-600 text-sm">
                    Bringing your brand to life with beautiful, functional designs that resonate with your target audience.
                </p>
            </div>

            <!-- Step 4: Testing & Iteration -->
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                    4
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Testing & Iteration</h3>
                <p class="text-gray-600 text-sm">
                    User testing and feedback collection to refine and optimize the design for maximum effectiveness.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Why Choose Our UI/UX Design Services -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                Why Choose Our <span class="text-blue-600">UI/UX Design</span> Services?
            </h2>
            <p class="text-lead max-w-3xl mx-auto">
                The importance of good UI/UX design cannot be overstated. It directly impacts user satisfaction, conversion rates, and business success.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div>
                <h3 class="text-2xl font-bold text-gray-900 mb-6">The Importance of UI/UX Design</h3>
                <div class="space-y-4">
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <svg class="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-1">Increased User Engagement</h4>
                            <p class="text-gray-600 text-sm">Well-designed interfaces keep users engaged and encourage them to explore your product further.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <svg class="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-1">Higher Conversion Rates</h4>
                            <p class="text-gray-600 text-sm">Intuitive design guides users toward desired actions, improving conversion rates and ROI.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <svg class="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-1">Reduced Development Costs</h4>
                            <p class="text-gray-600 text-sm">Proper planning and design reduce the need for costly revisions during development.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <svg class="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-1">Brand Differentiation</h4>
                            <p class="text-gray-600 text-sm">Unique, memorable designs help your brand stand out in a competitive marketplace.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div>
                <h3 class="text-2xl font-bold text-gray-900 mb-6">Our Implementation Style</h3>
                <div class="space-y-4">
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <svg class="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-1">User-Centered Approach</h4>
                            <p class="text-gray-600 text-sm">Every design decision is made with the end user in mind, ensuring optimal usability and satisfaction.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <svg class="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-1">Data-Driven Design</h4>
                            <p class="text-gray-600 text-sm">We use analytics, user feedback, and testing data to inform our design decisions.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <svg class="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-1">Collaborative Process</h4>
                            <p class="text-gray-600 text-sm">We work closely with your team throughout the design process, ensuring alignment with business goals.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <svg class="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-1">Future-Proof Solutions</h4>
                            <p class="text-gray-600 text-sm">Our designs are scalable and adaptable, growing with your business and evolving user needs.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section class="py-20 bg-gradient-blue text-white">
    <div class="container mx-auto px-4 text-center">
        <h2 class="heading-2 text-white mb-6">
            Ready to Transform Your <span class="text-blue-300">User Experience?</span>
        </h2>
        <p class="text-lead text-blue-100 mb-8 max-w-3xl mx-auto">
            Let's create designs that not only look beautiful but also drive results. Contact us today to discuss your UI/UX design project.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('contact', ['locale' => app()->getLocale()]) }}" class="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-50 transition-colors inline-flex items-center justify-center">
                Start Your Design Project
                <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </a>
            <a href="{{ route('projects.index', ['locale' => app()->getLocale()]) }}" class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors inline-flex items-center justify-center">
                View Our Design Work
            </a>
        </div>
    </div>
</section>

@include('partials.seo.structure-data', [
    'serviceName' => __('ui-ux-design.page_title'),
    'serviceDescription' => __('ui-ux-design.meta_description'),
    'serviceType' => 'UI/UX Design',
    'serviceCategory' => 'Design & User Experience',
    'serviceFeatures' => [
        __('ui-ux-design.ui_design_title'),
        __('ui-ux-design.ux_design_title'),
        __('ui-ux-design.mobile_app_design_title'),
        __('ui-ux-design.website_design_title'),
        __('ui-ux-design.figma_design_title'),
        __('ui-ux-design.adobe_xd_title')
    ],
    'priceRange' => 'Contact for custom quote'
])

@endsection
