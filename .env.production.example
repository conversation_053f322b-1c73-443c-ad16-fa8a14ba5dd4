# Live Chat AI System - Production Environment Configuration
# Copy this file to .env and update with your production values

# Application Configuration
APP_NAME="ChiSolution Inc."
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=https://your-domain.com
APP_TIMEZONE=SAST

# Logging Configuration
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error
LOG_STDERR_FORMATTER=Monolog\Formatter\JsonFormatter

# Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=
DB_USERNAME=
DB_PASSWORD=

# Redis Configuration
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=REPLACE_WITH_REDIS_PASSWORD
REDIS_PORT=6379
REDIS_DB=0

# Cache Configuration
CACHE_DRIVER=redis
CACHE_PREFIX=chat_prod

# Session Configuration
SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=true
SESSION_PATH=/
SESSION_DOMAIN=your-domain.com
SESSION_SECURE_COOKIES=true

# Queue Configuration
QUEUE_CONNECTION=redis
QUEUE_FAILED_DRIVER=database-uuids

# Broadcasting Configuration
BROADCAST_DRIVER=pusher

# Filesystem Configuration
FILESYSTEM_DISK=s3
AWS_ACCESS_KEY_ID=REPLACE_WITH_AWS_KEY
AWS_SECRET_ACCESS_KEY=REPLACE_WITH_AWS_SECRET
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=your-chat-system-bucket
AWS_USE_PATH_STYLE_ENDPOINT=false

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.your-provider.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=REPLACE_WITH_EMAIL_PASSWORD
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# Pusher Configuration (Real-time Features)
PUSHER_APP_ID=REPLACE_WITH_PUSHER_APP_ID
PUSHER_APP_KEY=REPLACE_WITH_PUSHER_KEY
PUSHER_APP_SECRET=REPLACE_WITH_PUSHER_SECRET
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

# OpenAI Configuration
OPENAI_API_KEY=REPLACE_WITH_OPENAI_API_KEY
OPENAI_ORGANIZATION=REPLACE_WITH_OPENAI_ORG_ID

# Chat Performance Configuration
CHAT_CACHE_ENABLED=true
CHAT_CACHE_TTL=3600
CHAT_ANALYTICS_CACHE_TTL=1800
CHAT_AI_CACHE_TTL=7200
CHAT_SESSION_CACHE_TTL=900
CHAT_ASSIGNMENTS_CACHE_TTL=600

# Rate Limiting Configuration
CHAT_RATE_LIMITING_ENABLED=true
CHAT_MESSAGE_RATE_LIMIT=60
CHAT_MESSAGE_RATE_DECAY=1
CHAT_ROOM_RATE_LIMIT=5
CHAT_ROOM_RATE_DECAY=60
CHAT_AI_RATE_LIMIT=30
CHAT_AI_RATE_DECAY=1
CHAT_FILE_RATE_LIMIT=10
CHAT_FILE_RATE_DECAY=5

# Database Optimization
CHAT_QUERY_OPTIMIZATION=true
CHAT_QUERY_CHUNK_SIZE=100
CHAT_AUTO_INDEX=true

# Database Cleanup
CHAT_CLEANUP_ENABLED=true
CHAT_CLEANUP_MESSAGES_DAYS=365
CHAT_CLEANUP_SESSIONS_DAYS=30
CHAT_CLEANUP_LOGS_DAYS=90

# Broadcasting Configuration
CHAT_BROADCASTING_ENABLED=true
CHAT_BROADCAST_DRIVER=pusher
CHAT_BROADCAST_BATCH_SIZE=100
CHAT_BROADCAST_RETRIES=3

# WebSocket Configuration
CHAT_WEBSOCKETS_ENABLED=true
CHAT_MAX_CONNECTIONS=1000
CHAT_HEARTBEAT_INTERVAL=30

# AI Circuit Breaker
CHAT_AI_CIRCUIT_BREAKER=true
CHAT_AI_FAILURE_THRESHOLD=5
CHAT_AI_RECOVERY_TIMEOUT=60
CHAT_AI_MAX_RESPONSE_TIME=10
CHAT_AI_STREAMING=false
CHAT_AI_PARALLEL=false

# AI Model Configuration
CHAT_AI_DEFAULT_MODEL=gpt-3.5-turbo
CHAT_AI_FALLBACK_MODEL=gpt-3.5-turbo
CHAT_AI_MAX_TOKENS=150
CHAT_AI_TEMPERATURE=0.7

# Performance Monitoring
CHAT_MONITORING_ENABLED=true
CHAT_RESPONSE_TIME_THRESHOLD=2.0
CHAT_MEMORY_THRESHOLD=128
CHAT_CPU_THRESHOLD=80
CHAT_ALERTS_ENABLED=true
CHAT_ALERT_EMAIL=true
CHAT_ALERT_SLACK=false
CHAT_PERFORMANCE_LOGS=true
CHAT_SLOW_QUERY_THRESHOLD=1.0
CHAT_LOG_LEVEL=info

# Optimization Settings
CHAT_LAZY_LOADING=true
CHAT_MESSAGE_PAGINATION=50
CHAT_USER_PAGINATION=20
CHAT_COMPRESSION=true
CHAT_GZIP_LEVEL=6
CHAT_MINIFY=true

# CDN Configuration
CHAT_CDN_ENABLED=true
CHAT_CDN_URL=https://cdn.your-domain.com
CHAT_CDN_STATIC=true

# Load Balancing
CHAT_LOAD_BALANCING=false
CHAT_LB_STRATEGY=round_robin
CHAT_HEALTH_CHECKS=true
CHAT_FAILOVER=true

# Security Configuration
CHAT_MESSAGE_ENCRYPTION=false
CHAT_FILE_ENCRYPTION=false
CHAT_ENCRYPTION_ALGO=AES-256-CBC
CHAT_STRICT_VALIDATION=true
CHAT_SANITIZATION_LEVEL=medium
CHAT_XSS_PROTECTION=true

# Third-party Services
SENTRY_LARAVEL_DSN=REPLACE_WITH_SENTRY_DSN
SENTRY_TRACES_SAMPLE_RATE=0.1

# Analytics
GOOGLE_ANALYTICS_ID=REPLACE_WITH_GA_ID
MIXPANEL_TOKEN=REPLACE_WITH_MIXPANEL_TOKEN

# Social Login (Optional)
GOOGLE_CLIENT_ID=REPLACE_WITH_GOOGLE_CLIENT_ID
GOOGLE_CLIENT_SECRET=REPLACE_WITH_GOOGLE_CLIENT_SECRET
FACEBOOK_CLIENT_ID=REPLACE_WITH_FACEBOOK_CLIENT_ID
FACEBOOK_CLIENT_SECRET=REPLACE_WITH_FACEBOOK_CLIENT_SECRET

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_RETENTION_DAYS=30

# Monitoring Services
NEW_RELIC_LICENSE_KEY=REPLACE_WITH_NEW_RELIC_KEY
DATADOG_API_KEY=REPLACE_WITH_DATADOG_KEY

# SSL Configuration
FORCE_HTTPS=true
HSTS_MAX_AGE=31536000

# CORS Configuration
CORS_ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With,X-CSRF-TOKEN

# API Configuration
API_RATE_LIMIT=1000
API_RATE_LIMIT_WINDOW=60

# File Upload Configuration
MAX_FILE_SIZE=10240
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,txt
UPLOAD_PATH=uploads/chat

# Notification Configuration
NOTIFICATION_CHANNELS=mail,database,broadcast
SLACK_WEBHOOK_URL=REPLACE_WITH_SLACK_WEBHOOK
DISCORD_WEBHOOK_URL=REPLACE_WITH_DISCORD_WEBHOOK

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_SECRET=REPLACE_WITH_MAINTENANCE_SECRET

# Feature Flags
FEATURE_AI_CHATBOT=true
FEATURE_FILE_SHARING=true
FEATURE_SCREEN_SHARING=false
FEATURE_VIDEO_CHAT=false
FEATURE_VOICE_MESSAGES=false
FEATURE_TRANSLATION=true
FEATURE_SENTIMENT_ANALYSIS=true

# Localization
DEFAULT_LOCALE=en
FALLBACK_LOCALE=en
SUPPORTED_LOCALES=en,es,fr,de,it,pt,zh,ja,ko

# Time Zone
DEFAULT_TIMEZONE=SAST
BUSINESS_TIMEZONE=Pretoria/South_Africa

# Business Hours
BUSINESS_HOURS_START=09:00
BUSINESS_HOURS_END=17:00
BUSINESS_DAYS=1,2,3,4,5,6

# Auto-assignment
AUTO_ASSIGNMENT_ENABLED=true
AUTO_ASSIGNMENT_ALGORITHM=round_robin
MAX_CONCURRENT_CHATS=5

# Escalation Rules
ESCALATION_ENABLED=true
ESCALATION_TIMEOUT=300
ESCALATION_CONFIDENCE_THRESHOLD=0.7

# Customer Satisfaction
SATISFACTION_SURVEY_ENABLED=true
SATISFACTION_SURVEY_DELAY=300
NPS_SURVEY_ENABLED=true
NPS_SURVEY_FREQUENCY=30

# Compliance
GDPR_COMPLIANCE=true
CCPA_COMPLIANCE=true
DATA_RETENTION_POLICY=true
AUDIT_LOGGING=true

# Development/Debug (Set to false in production)
TELESCOPE_ENABLED=false
DEBUGBAR_ENABLED=false
QUERY_LOGGING=false
