<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\ShoppingCart;
use App\Models\User;
use App\Services\EmailNotificationService;
use App\Services\PaymentService;
use App\Services\ActivityLogger;
use App\Services\VisitorAnalytics;
use App\Http\Requests\CheckoutRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use App\Models\Currency;

class CheckoutController extends Controller
{
    protected ActivityLogger $activityLogger;
    protected VisitorAnalytics $visitorAnalytics;

    public function __construct(ActivityLogger $activityLogger, VisitorAnalytics $visitorAnalytics)
    {
        $this->activityLogger = $activityLogger;
        $this->visitorAnalytics = $visitorAnalytics;
    }

    /**
     * Display the checkout page.
     */
    public function index(string $locale): View|RedirectResponse
    {
        $activityLogger = app(ActivityLogger::class);
        $visitorAnalytics = app(VisitorAnalytics::class);

        // Log checkout initiation
        $activityLogger->logCheckoutActivity(
            'initiated',
            ['page' => 'checkout_index'],
            true,
            null,
            null,
            []
        );

        // Track visitor analytics and checkout funnel
        $visitorAnalytics->trackPageVisit('Checkout Started', [
            'page_type' => 'checkout_index',
            'user_authenticated' => Auth::check(),
            'user_type' => Auth::check() ? 'registered' : 'guest',
            'customer_type' => Auth::check() ? 'registered' : 'guest',
            'cart_type' => Auth::check() ? 'user' : 'session'
        ]);
        $visitorAnalytics->trackCheckoutFunnelStep('initiated', [
            'page' => 'checkout_index',
            'user_authenticated' => Auth::check(),
            'user_type' => Auth::check() ? 'registered' : 'guest',
            'customer_type' => Auth::check() ? 'registered' : 'guest',
            'cart_type' => Auth::check() ? 'user' : 'session'
        ], true);

        \Log::info('Checkout index accessed', [
            'session_id' => Session::getId(),
            'user_id' => Auth::id(),
            'is_authenticated' => Auth::check(),
        ]);

        $cart = $this->getCart();

        \Log::info('Cart retrieved for checkout', [
            'cart_found' => $cart !== null,
            'cart_id' => $cart?->id,
            'cart_session_id' => $cart?->session_id,
            'cart_user_id' => $cart?->user_id,
            'cart_items_count' => $cart?->items->count(),
            'cart_is_empty' => $cart?->isEmpty(),
        ]);

        if (!$cart || $cart->isEmpty()) {
            \Log::warning('Checkout redirected due to empty cart', [
                'cart_exists' => $cart !== null,
                'cart_is_empty' => $cart?->isEmpty(),
            ]);

            // Log failed checkout initiation
            $activityLogger->logCheckoutActivity(
                'initiated',
                ['page' => 'checkout_index', 'cart_status' => 'empty'],
                false,
                'Cart is empty',
                null,
                []
            );

            // Track checkout abandonment
            $visitorAnalytics->trackCheckoutAbandonment('initiated', [
                'reason' => 'empty_cart',
                'cart_status' => 'empty'
            ]);

            return redirect()->route('shop.index')->with('error', 'Your cart is empty.');
        }

        // Check if all items are still available
        if (!$cart->allItemsAvailable()) {
            $unavailableItems = $cart->getUnavailableItems();

            // Log cart review failure
            $activityLogger->logCheckoutActivity(
                'cart_review',
                [
                    'cart_id' => $cart->id,
                    'unavailable_items' => collect($unavailableItems)->pluck('id')->toArray(),
                    'total_items' => $cart->items->count(),
                    'unavailable_count' => count($unavailableItems)
                ],
                false,
                'Some items are no longer available',
                null,
                []
            );

            return redirect()->route('cart.index')
                ->with('error', 'Some items in your cart are no longer available.')
                ->with('unavailable_items', $unavailableItems);
        }

        // Update prices to current prices
        $cart->updatePrices();

        // Log successful cart review
        $activityLogger->logCheckoutActivity(
            'cart_review',
            [
                'cart_id' => $cart->id,
                'items_count' => $cart->items->count(),
                'total_amount' => $cart->total,
                'currency' => $cart->currency,
                'all_items_available' => true
            ],
            true,
            null,
            null,
            []
        );

        // Track checkout funnel progress
        $visitorAnalytics->trackCheckoutFunnelStep('cart_review', [
            'cart_id' => $cart->id,
            'items_count' => $cart->items->count(),
            'total_amount' => $cart->total,
            'currency' => $cart->currency,
            'user_authenticated' => Auth::check(),
            'customer_type' => Auth::check() ? 'registered' : 'guest',
            'cart_type' => Auth::check() ? 'user' : 'session'
        ], true);

        $user = Auth::user();

        return view('pages.checkout.index', compact('cart', 'user'));
    }

    /**
     * Process the checkout and create an order.
     */
    public function process(string $locale, CheckoutRequest $request)
    {
        $activityLogger = app(ActivityLogger::class);
        $visitorAnalytics = app(VisitorAnalytics::class);

        $cart = $this->getCart();

        if (!$cart || $cart->isEmpty()) {
            // Log failed checkout process due to empty cart
            $activityLogger->logCheckoutActivity(
                'form_validation',
                ['error' => 'empty_cart'],
                false,
                'Cart is empty during checkout process',
                null,
                []
            );

            return redirect()->route('shop.index')->with('error', 'Your cart is empty.');
        }

        // Get validated data from CheckoutRequest (validation and logging handled automatically)
        $validated = $request->validated();

        // Check stock availability one more time
        if (!$cart->allItemsAvailable()) {
            $unavailableItems = $cart->getUnavailableItems();

            // Log stock availability failure during checkout
            $activityLogger->logCheckoutActivity(
                'billing_processed',
                [
                    'cart_id' => $cart->id,
                    'unavailable_items' => collect($unavailableItems)->pluck('id')->toArray(),
                    'check_point' => 'pre_order_creation'
                ],
                false,
                'Items became unavailable during checkout process',
                null,
                []
            );

            return redirect()->route('cart.index')
                ->with('error', 'Some items in your cart are no longer available.');
        }

        DB::beginTransaction();

        try {
            $user = null;

            // Handle user creation or login
            if ($request->create_account && !Auth::check()) {
                // Check if user already exists
                $existingUser = User::where('email', $validated['email'])->first();
                if ($existingUser) {
                    // Log account creation failure
                    $activityLogger->logCheckoutActivity(
                        'billing_processed',
                        [
                            'cart_id' => $cart->id,
                            'account_creation_attempted' => true,
                            'customer_email' => $validated['email'],
                            'failure_reason' => 'email_already_exists'
                        ],
                        false,
                        'Account creation failed - email already exists',
                        null,
                        ['email' => ['An account with this email already exists.']]
                    );

                    return back()->withErrors(['email' => 'An account with this email already exists.']);
                }

                // Log account creation attempt
                $activityLogger->logCheckoutActivity(
                    'billing_processed',
                    [
                        'cart_id' => $cart->id,
                        'account_creation' => true,
                        'customer_email' => $validated['email']
                    ],
                    true,
                    null,
                    null,
                    []
                );

                // Create new user
                $user = User::create([
                    'first_name' => $validated['first_name'],
                    'last_name' => $validated['last_name'],
                    'email' => $validated['email'],
                    'phone' => $validated['phone'],
                    'password' => Hash::make($validated['password']),
                    'email_verified_at' => now(),
                ]);

                Auth::login($user);

                // Log successful account creation
                $activityLogger->logActivity(
                    'account_created_during_checkout',
                    "Account created during checkout for {$validated['email']}",
                    'success',
                    null,
                    [
                        'created_during_checkout' => true,
                        'cart_id' => $cart->id,
                        'user_id' => $user->id
                    ],
                    ['account_created' => true, 'auto_login' => true]
                );
            } elseif (Auth::check()) {
                $user = Auth::user();

                // Log billing processing for existing user
                $activityLogger->logCheckoutActivity(
                    'billing_processed',
                    [
                        'cart_id' => $cart->id,
                        'customer_type' => 'existing_user',
                        'customer_email' => $validated['email'],
                        'user_id' => $user->id
                    ],
                    true,
                    null,
                    null,
                    []
                );
            } else {
                // Log billing processing for guest
                $activityLogger->logCheckoutActivity(
                    'billing_processed',
                    [
                        'cart_id' => $cart->id,
                        'customer_type' => 'guest',
                        'customer_email' => $validated['email']
                    ],
                    true,
                    null,
                    null,
                    []
                );
            }

            // Prepare shipping address
            $shippingAddress = !$request->same_as_billing ? [
                'address_line_1' => $validated['shipping_address_line_1'],
                'address_line_2' => $validated['shipping_address_line_2'],
                'city' => $validated['shipping_city'],
                'state' => $validated['shipping_state'],
                'postal_code' => $validated['shipping_postal_code'],
                'country' => $validated['shipping_country'],
            ] : [
                'address_line_1' => $validated['billing_address_line_1'],
                'address_line_2' => $validated['billing_address_line_2'],
                'city' => $validated['billing_city'],
                'state' => $validated['billing_state'],
                'postal_code' => $validated['billing_postal_code'],
                'country' => $validated['billing_country'],
            ];

            // Log shipping calculation
            $activityLogger->logCheckoutActivity(
                'shipping_calculated',
                [
                    'cart_id' => $cart->id,
                    'same_as_billing' => $request->same_as_billing,
                    'shipping_country' => $shippingAddress['country'],
                    'billing_country' => $validated['billing_country']
                ],
                true,
                null,
                null,
                []
            );

            // Determine order and payment status based on payment method
            $orderStatus = 'pending';
            $paymentStatus = 'pending';

            switch ($validated['payment_method']) {
                case 'cash_on_delivery':
                    $orderStatus = 'confirmed';
                    $paymentStatus = 'pending_cod';
                    break;
                case 'pay_offline':
                    $orderStatus = 'pending_payment';
                    $paymentStatus = 'pending_offline';
                    break;
                case 'stripe':
                case 'paypal':
                    $orderStatus = 'pending';
                    $paymentStatus = 'pending';
                    break;
            }

            // Log order creation attempt
            $orderData = [
                'cart_id' => $cart->id,
                'customer_email' => $validated['email'],
                'payment_method' => $validated['payment_method'],
                'order_status' => $orderStatus,
                'payment_status' => $paymentStatus,
                'customer_type' => $user ? 'registered' : 'guest',
                'items_count' => $cart->items->count(),
                'total_amount' => $cart->total,
                'currency' => $cart->currency
            ];

            // Get coupon information if applied
            $coupon = null;
            if ($cart->coupon_id) {
                $coupon = \App\Models\Coupon::where('uuid', $cart->coupon_id)->first();
            }

            // Create order
            $order = Order::create([
                'uuid' => Str::uuid(),
                'user_id' => $user?->id,
                'session_id' => $user ? null : Session::getId(), // Store session ID for guest orders
                'order_number' => $this->generateOrderNumber(),
                'status' => $orderStatus,
                'payment_status' => $paymentStatus,
                'payment_method' => $validated['payment_method'],
                'currency_id' => Currency::where('is_default', true)->first()?->id ?? Currency::first()->id,

                // Customer Information
                'email' => $validated['email'],
                'phone' => $validated['phone'],

                // Billing Address
                'billing_address' => [
                    'address_line_1' => $validated['billing_address_line_1'],
                    'address_line_2' => $validated['billing_address_line_2'],
                    'city' => $validated['billing_city'],
                    'state' => $validated['billing_state'],
                    'postal_code' => $validated['billing_postal_code'],
                    'country' => $validated['billing_country'],
                ],

                // Shipping Address
                'shipping_address' => $shippingAddress,

                // Totals
                'subtotal' => $cart->subtotal,
                'tax_amount' => $cart->tax_amount,
                'shipping_amount' => $cart->shipping_amount,
                'discount_amount' => $cart->discount_amount,
                'total_amount' => $cart->total,

                // Coupon Information
                'coupon_id' => $coupon?->id,
                'coupon_code' => $cart->coupon_code,
            ]);

            // Create order items
            foreach ($cart->items as $cartItem) {
                $order->items()->create([
                    'product_id' => $cartItem->product_id,
                    'variant_id' => $cartItem->product_variant_id,
                    'quantity' => $cartItem->quantity,
                    'unit_price' => $cartItem->price,
                    'total_price' => $cartItem->total,
                    'product_snapshot' => [
                        'product_name' => $cartItem->product->name,
                        'product_sku' => $cartItem->sku,
                        'variant_name' => $cartItem->productVariant?->name,
                    ],
                ]);

                // Decrease inventory
                if ($cartItem->productVariant) {
                    $cartItem->productVariant->decreaseInventory($cartItem->quantity);
                } else {
                    $cartItem->product->decreaseInventory($cartItem->quantity);
                }
            }

            // Track coupon usage if a coupon was applied
            if ($coupon) {
                $coupon->markAsUsed();

                // Log coupon usage in order history
                $historyService = app(\App\Services\OrderHistoryService::class);
                $historyService->logNoteAdded($order, "Coupon '{$coupon->code}' applied with {$coupon->formatted_value} discount");
            }

            // Clear the cart
            $cart->delete();

            DB::commit();

            // Log successful order creation
            $activityLogger->logCheckoutActivity(
                'order_created',
                array_merge($orderData, [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'order_uuid' => $order->uuid,
                    'items_processed' => $order->items->count(),
                    'inventory_updated' => true,
                    'cart_cleared' => true
                ]),
                true,
                null,
                $order->id,
                []
            );

            // Log order creation activity (existing pattern)
            $activityLogger->logOrderActivity(
                'create',
                $order->id,
                [
                    'order_number' => $order->order_number,
                    'total_amount' => $order->total_amount,
                    'payment_method' => $order->payment_method,
                    'items_count' => $order->items->count(),
                    'customer_type' => auth()->check() ? 'registered' : 'guest',
                    'billing_country' => $order->billing_address['country'] ?? null,
                ],
                true
            );

            // Track checkout completion in visitor analytics
            $this->visitorAnalytics->trackFormInteraction(
                'checkout_form',
                'submit',
                true,
                [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'total_amount' => $order->total_amount,
                    'payment_method' => $order->payment_method,
                ]
            );

            // Track purchase conversion
            $this->visitorAnalytics->trackConversion(
                'purchase',
                [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'total_amount' => $order->total_amount,
                    'payment_method' => $order->payment_method,
                    'items_count' => $order->items->count(),
                    'customer_type' => auth()->check() ? 'registered' : 'guest',
                ]
            );

            // Track journey step
            $this->visitorAnalytics->trackJourneyStep(
                'Purchase Completed',
                'conversion',
                [
                    'order_value' => $order->total_amount,
                    'payment_method' => $order->payment_method,
                    'items_purchased' => $order->items->count(),
                ]
            );

            // Update lead score for purchase
            $this->visitorAnalytics->updateLeadScore(
                'purchase',
                ['order_value' => $order->total_amount]
            );

            // Send order confirmation email
            try {
                $emailService = new EmailNotificationService();
                $emailService->sendOrderConfirmation($order);

                // Log successful confirmation email
                $activityLogger->logCheckoutActivity(
                    'confirmation_sent',
                    [
                        'order_id' => $order->id,
                        'order_number' => $order->order_number,
                        'customer_email' => $order->email,
                        'email_type' => 'order_confirmation'
                    ],
                    true,
                    null,
                    $order->id,
                    []
                );
            } catch (\Exception $emailException) {
                // Log email failure but don't fail the entire checkout
                $activityLogger->logCheckoutActivity(
                    'confirmation_sent',
                    [
                        'order_id' => $order->id,
                        'order_number' => $order->order_number,
                        'customer_email' => $order->email,
                        'email_type' => 'order_confirmation'
                    ],
                    false,
                    'Failed to send confirmation email: ' . $emailException->getMessage(),
                    $order->id,
                    []
                );

                \Log::warning('Order confirmation email failed', [
                    'order_id' => $order->id,
                    'error' => $emailException->getMessage()
                ]);
            }

            // Handle different payment methods
            switch ($validated['payment_method']) {
                case 'cash_on_delivery':
                    // Log COD completion
                    $activityLogger->logCheckoutActivity(
                        'completed',
                        [
                            'order_id' => $order->id,
                            'payment_method' => 'cash_on_delivery',
                            'requires_payment' => false,
                            'completion_type' => 'immediate'
                        ],
                        true,
                        null,
                        $order->id,
                        []
                    );

                    return redirect()->route('checkout.success', $order->uuid)
                        ->with('success', 'Order placed successfully! You will pay cash on delivery.');

                case 'pay_offline':
                    // Log offline payment setup
                    $activityLogger->logCheckoutActivity(
                        'payment_initiated',
                        [
                            'order_id' => $order->id,
                            'payment_method' => 'pay_offline',
                            'requires_manual_payment' => true,
                            'payment_instructions_sent' => true
                        ],
                        true,
                        null,
                        $order->id,
                        []
                    );

                    return redirect()->route('checkout.offline-payment', $order->uuid)
                        ->with('success', 'Order created successfully! Payment instructions have been sent to your email.');

                case 'stripe':
                case 'paypal':
                default:
                    // Log payment initiation
                    $activityLogger->logCheckoutActivity(
                        'payment_initiated',
                        [
                            'order_id' => $order->id,
                            'payment_method' => $validated['payment_method'],
                            'requires_online_payment' => true,
                            'redirect_to_payment' => true
                        ],
                        true,
                        null,
                        $order->id,
                        []
                    );

                    // Redirect to payment processing
                    return redirect()->route('checkout.payment', $order->uuid)
                        ->with('success', 'Order created successfully! Please complete your payment.');
            }

        } catch (\Exception $e) {
            DB::rollback();

            // Log comprehensive checkout failure
            $activityLogger->logCheckoutActivity(
                'order_created',
                [
                    'cart_id' => $cart?->id,
                    'customer_email' => $validated['email'] ?? null,
                    'payment_method' => $validated['payment_method'] ?? null,
                    'exception_type' => get_class($e),
                    'exception_file' => $e->getFile(),
                    'exception_line' => $e->getLine()
                ],
                false,
                'Checkout process failed: ' . $e->getMessage(),
                null,
                []
            );

            \Log::error('Checkout process failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => Auth::id(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'cart_id' => $cart?->id,
                'customer_email' => $validated['email'] ?? null,
            ]);

            // For testing, show the actual error
            if (app()->environment('testing')) {
                throw $e;
            }

            return back()->withErrors(['error' => 'An error occurred while processing your order. Please try again.']);
        }
    }

    /**
     * Display the payment page.
     */
    public function payment(string $locale, Order $order): View|RedirectResponse
    {
        $activityLogger = app(ActivityLogger::class);
        $visitorAnalytics = app(VisitorAnalytics::class);

        if ($order->payment_status !== 'pending') {
            // Log attempt to access payment page for non-pending order
            $activityLogger->logCheckoutActivity(
                'payment_initiated',
                [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'current_payment_status' => $order->payment_status,
                    'access_denied' => true
                ],
                false,
                'Attempted to access payment page for order with non-pending payment status',
                $order->id,
                []
            );

            return redirect()->route('orders.show', $order->uuid);
        }

        // Log payment page access
        $activityLogger->logCheckoutActivity(
            'payment_initiated',
            [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'payment_method' => $order->payment_method,
                'total_amount' => $order->total_amount,
                'payment_page_loaded' => true
            ],
            true,
            null,
            $order->id,
            []
        );

        // Track payment page view
        $visitorAnalytics->trackPageVisit('Payment Page Loaded', [
            'page_type' => 'checkout_payment',
            'order_id' => $order->id,
            'payment_method' => $order->payment_method,
            'order_value' => $order->total_amount,
            'customer_type' => $order->user_id ? 'registered' : 'guest',
            'cart_type' => $order->user_id ? 'user' : 'session'
        ]);

        return view('pages.checkout.payment', compact('order'));
    }

    /**
     * Process payment via AJAX.
     */
    public function processPayment(string $locale, Request $request, Order $order)
    {
        $activityLogger = app(ActivityLogger::class);
        $visitorAnalytics = app(VisitorAnalytics::class);

        try {
            $request->validate([
                'token' => 'required|string',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Log payment validation failure
            $activityLogger->logCheckoutActivity(
                'payment_completed',
                [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'payment_method' => $order->payment_method,
                    'validation_failed' => true
                ],
                false,
                'Payment token validation failed',
                $order->id,
                $e->errors()
            );

            return response()->json([
                'success' => false,
                'message' => 'Invalid payment token provided.',
            ]);
        }

        if ($order->payment_status !== 'pending') {
            // Log attempt to process already processed payment
            $activityLogger->logCheckoutActivity(
                'payment_completed',
                [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'current_payment_status' => $order->payment_status,
                    'duplicate_attempt' => true
                ],
                false,
                'Attempted to process payment for order that is not pending',
                $order->id,
                []
            );

            return response()->json([
                'success' => false,
                'message' => 'This order has already been processed.',
            ]);
        }

        $paymentService = new PaymentService();

        // Log payment processing attempt
        $activityLogger->logCheckoutActivity(
            'payment_completed',
            [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'payment_method' => $order->payment_method,
                'total_amount' => $order->total_amount,
                'processing_started' => true,
                'customer_ip' => $request->ip()
            ],
            true,
            null,
            $order->id,
            []
        );

        // Process Stripe payment
        $result = $paymentService->processStripePayment($order, $request->token, [
            'customer_ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        if ($result['success']) {
            // Log successful payment
            $activityLogger->logCheckoutActivity(
                'payment_completed',
                [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'payment_method' => $order->payment_method,
                    'total_amount' => $order->total_amount,
                    'payment_successful' => true,
                    'transaction_id' => $result['transaction_id'] ?? null
                ],
                true,
                null,
                $order->id,
                []
            );

            // Log final checkout completion
            $activityLogger->logCheckoutActivity(
                'completed',
                [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'payment_method' => $order->payment_method,
                    'total_amount' => $order->total_amount,
                    'checkout_completed' => true,
                    'completion_type' => 'payment_success'
                ],
                true,
                null,
                $order->id,
                []
            );

            // Track successful payment conversion
            $visitorAnalytics->trackConversion('payment_completed', [
                'order_id' => $order->id,
                'payment_method' => $order->payment_method,
                'order_value' => $order->total_amount
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment processed successfully!',
                'redirect_url' => route('checkout.success', $order->uuid),
            ]);
        } else {
            // Log payment failure
            $activityLogger->logCheckoutActivity(
                'payment_completed',
                [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'payment_method' => $order->payment_method,
                    'total_amount' => $order->total_amount,
                    'payment_failed' => true,
                    'error_message' => $result['error'] ?? 'Unknown error'
                ],
                false,
                'Payment processing failed: ' . ($result['error'] ?? 'Unknown error'),
                $order->id,
                []
            );

            // Track failed payment
            $visitorAnalytics->trackFormInteraction('payment_form', 'submit', false, [
                'order_id' => $order->id,
                'error' => $result['error'] ?? 'Unknown error'
            ]);

            return response()->json([
                'success' => false,
                'message' => $result['error'],
            ]);
        }
    }

    /**
     * Process payment completion.
     */
    public function paymentComplete(string $locale, Request $request, Order $order)
    {
        $paymentService = new PaymentService();

        try {
            $paymentMethod = $request->input('payment_method', $order->payment_method);

            switch ($paymentMethod) {
                case 'stripe':
                    $stripeToken = $request->input('stripe_token');
                    if (!$stripeToken) {
                        return back()->withErrors(['payment' => 'Payment token is required.']);
                    }

                    $result = $paymentService->processStripePayment($order, $stripeToken);

                    if ($result['success']) {
                        return redirect()->route('checkout.success', $order->uuid)
                            ->with('success', 'Payment completed successfully!');
                    } else {
                        return back()->withErrors(['payment' => $result['error']]);
                    }
                    break;

                case 'paypal':
                    // TODO: Implement PayPal processing
                    return back()->withErrors(['payment' => 'PayPal payment not yet implemented.']);
                    break;

                default:
                    return back()->withErrors(['payment' => 'Invalid payment method.']);
            }

        } catch (\Exception $e) {
            \Log::error('Payment processing error', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
            ]);

            return back()->withErrors(['payment' => 'An error occurred while processing your payment. Please try again.']);
        }
    }

    /**
     * Display offline payment instructions.
     */
    public function offlinePayment(string $locale, Order $order)
    {
        // Ensure this is an offline payment order
        if ($order->payment_method !== 'pay_offline') {
            return redirect()->route('checkout.success', $order->uuid);
        }

        // Ensure order belongs to current user or session
        if (Auth::check()) {
            if ($order->user_id !== Auth::id()) {
                abort(403, 'Unauthorized access to order.');
            }
        } else {
            // For guest orders, check session
            $sessionId = Session::getId();
            if ($order->session_id !== $sessionId) {
                abort(403, 'Unauthorized access to order.');
            }
        }

        return view('pages.checkout.offline-payment', compact('order'));
    }

    /**
     * Display the order success page.
     */
    public function success(string $locale, Order $order): View
    {
        // Ensure order belongs to current user or session
        if (Auth::check()) {
            if ($order->user_id !== Auth::id()) {
                abort(403, 'Unauthorized access to order.');
            }
        } else {
            // For guest orders, check session ID
            $sessionId = Session::getId();
            if ($order->session_id !== $sessionId) {
                abort(403, 'Unauthorized access to order.');
            }
        }

        $order->load(['items.product', 'items.variant']);

        return view('pages.checkout.success', compact('order'));
    }

    /**
     * Get the current cart.
     */
    protected function getCart(): ?ShoppingCart
    {
        if (Auth::check()) {
            return ShoppingCart::where('user_id', Auth::id())
                ->with(['items.product', 'items.productVariant'])
                ->first();
        } else {
            $sessionId = Session::getId();
            return ShoppingCart::where('session_id', $sessionId)
                ->with(['items.product', 'items.productVariant'])
                ->first();
        }
    }

    /**
     * Generate a unique order number.
     */
    protected function generateOrderNumber(): string
    {
        // Simplified order number generation for testing
        return 'ORD-' . date('Y') . '-' . time() . '-' . mt_rand(1000, 9999);
    }
}
