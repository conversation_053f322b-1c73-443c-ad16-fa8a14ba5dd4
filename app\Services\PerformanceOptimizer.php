<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PerformanceOptimizer
{
    private $startTime;
    private $memoryStart;
    private $maxExecutionTime;
    private $maxMemoryUsage;

    public function __construct()
    {
        $this->startTime = microtime(true);
        $this->memoryStart = memory_get_usage(true);
        $this->maxExecutionTime = 25; // 25 seconds max (safe for most hosting)
        $this->maxMemoryUsage = 128 * 1024 * 1024; // 128MB
    }

    /**
     * Check if we're approaching execution limits
     */
    public function checkLimits(): bool
    {
        $currentTime = microtime(true);
        $currentMemory = memory_get_usage(true);
        
        $executionTime = $currentTime - $this->startTime;
        $memoryUsage = $currentMemory - $this->memoryStart;
        
        if ($executionTime > $this->maxExecutionTime) {
            Log::warning('Execution time limit approaching', [
                'execution_time' => $executionTime,
                'max_time' => $this->maxExecutionTime
            ]);
            return false;
        }
        
        if ($memoryUsage > $this->maxMemoryUsage) {
            Log::warning('Memory usage limit approaching', [
                'memory_usage' => $memoryUsage,
                'max_memory' => $this->maxMemoryUsage
            ]);
            return false;
        }
        
        return true;
    }

    /**
     * Optimize database queries with caching
     */
    public function optimizeQuery(string $cacheKey, callable $query, int $ttl = 300)
    {
        if (!$this->checkLimits()) {
            // Return cached result if available, otherwise minimal data
            return Cache::get($cacheKey, collect());
        }

        return Cache::remember($cacheKey, $ttl, function() use ($query) {
            $startTime = microtime(true);
            $result = $query();
            $queryTime = microtime(true) - $startTime;
            
            if ($queryTime > 1.0) { // Log slow queries
                Log::warning('Slow query detected in PerformanceOptimizer', [
                    'execution_time' => $queryTime
                ]);
            }
            
            return $result;
        });
    }

    /**
     * Batch process items with timeout protection
     */
    public function batchProcess(array $items, callable $processor, int $batchSize = 50): array
    {
        $results = [];
        $batches = array_chunk($items, $batchSize);
        
        foreach ($batches as $batch) {
            if (!$this->checkLimits()) {
                Log::warning('Batch processing stopped due to limits');
                break;
            }
            
            foreach ($batch as $item) {
                try {
                    $results[] = $processor($item);
                } catch (\Exception $e) {
                    Log::error('Batch processing error', [
                        'error' => $e->getMessage(),
                        'item' => $item
                    ]);
                }
            }
            
            // Small delay to prevent overwhelming the system
            usleep(1000); // 1ms
        }
        
        return $results;
    }

    /**
     * Optimize image processing with size limits
     */
    public function optimizeImageProcessing(string $imagePath, array $options = []): bool
    {
        if (!$this->checkLimits()) {
            return false;
        }

        $fileSize = filesize($imagePath);
        $maxSize = $options['max_size'] ?? 10 * 1024 * 1024; // 10MB default
        
        if ($fileSize > $maxSize) {
            Log::warning('Image too large for processing', [
                'file_size' => $fileSize,
                'max_size' => $maxSize,
                'path' => $imagePath
            ]);
            return false;
        }

        return true;
    }

    /**
     * Clean up memory and optimize garbage collection
     */
    public function cleanup(): void
    {
        // Force garbage collection
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
        
        // Clear query log to free memory
        DB::flushQueryLog();
        
        // Log final stats
        $finalTime = microtime(true) - $this->startTime;
        $finalMemory = memory_get_usage(true) - $this->memoryStart;
        
        Log::info('Performance optimization completed', [
            'execution_time' => $finalTime,
            'memory_used' => $finalMemory,
            'peak_memory' => memory_get_peak_usage(true)
        ]);
    }

    /**
     * Get performance metrics
     */
    public function getMetrics(): array
    {
        return [
            'execution_time' => microtime(true) - $this->startTime,
            'memory_usage' => memory_get_usage(true) - $this->memoryStart,
            'peak_memory' => memory_get_peak_usage(true),
            'within_limits' => $this->checkLimits()
        ];
    }

    /**
     * Set custom limits for specific operations
     */
    public function setLimits(int $maxTime = null, int $maxMemory = null): void
    {
        if ($maxTime !== null) {
            $this->maxExecutionTime = $maxTime;
        }
        
        if ($maxMemory !== null) {
            $this->maxMemoryUsage = $maxMemory;
        }
    }

    /**
     * Emergency timeout handler
     */
    public function handleTimeout(): void
    {
        Log::emergency('Emergency timeout handler triggered', [
            'execution_time' => microtime(true) - $this->startTime,
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ]);
        
        // Clear all caches to free memory
        Cache::flush();
        
        // Force cleanup
        $this->cleanup();
        
        // Terminate gracefully
        exit(1);
    }
}
