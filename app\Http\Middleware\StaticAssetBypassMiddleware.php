<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class StaticAssetBypassMiddleware
{
    /**
     * Handle an incoming request.
     * This middleware should be placed FIRST in the middleware stack
     * to bypass all other middleware for static assets.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Ultra-fast static asset detection
        if ($this->isStaticAsset($request)) {
            // Skip all other middleware for static assets
            return $this->handleStaticAsset($request);
        }

        return $next($request);
    }

    /**
     * Check if this is a static asset request.
     */
    private function isStaticAsset(Request $request): bool
    {
        $path = $request->path();
        
        // Check for common static asset paths
        if (str_starts_with($path, 'build/') || 
            str_starts_with($path, 'images/') || 
            str_starts_with($path, 'js/') || 
            str_starts_with($path, 'css/') ||
            str_starts_with($path, 'fonts/') ||
            str_starts_with($path, 'storage/')) {
            return true;
        }

        // Check file extension
        $extension = pathinfo($path, PATHINFO_EXTENSION);
        
        $staticExtensions = [
            'css', 'js', 'png', 'jpg', 'jpeg', 'gif', 'svg', 'ico', 
            'woff', 'woff2', 'ttf', 'eot', 'otf', 'webp', 'avif',
            'mp4', 'webm', 'pdf', 'zip', 'txt', 'xml', 'json', 'map'
        ];

        return in_array(strtolower($extension), $staticExtensions);
    }

    /**
     * Handle static asset request directly.
     */
    private function handleStaticAsset(Request $request): Response
    {
        $path = $request->path();
        
        // Handle favicon specifically
        if ($path === 'favicon.ico') {
            $faviconPath = public_path('favicon.ico');
            if (file_exists($faviconPath)) {
                return response()->file($faviconPath, [
                    'Content-Type' => 'image/x-icon',
                    'Cache-Control' => 'public, max-age=86400', // 1 day
                ]);
            }
            return response('', 404);
        }

        // For other static assets, let Laravel handle them normally
        // but with optimized headers
        $fullPath = public_path($path);
        
        if (!file_exists($fullPath)) {
            return response('', 404);
        }

        // Set appropriate cache headers for static assets
        $mimeType = $this->getMimeType($path);
        $maxAge = $this->getCacheMaxAge($path);

        return response()->file($fullPath, [
            'Content-Type' => $mimeType,
            'Cache-Control' => "public, max-age={$maxAge}",
            'Expires' => gmdate('D, d M Y H:i:s', time() + $maxAge) . ' GMT',
        ]);
    }

    /**
     * Get MIME type for file.
     */
    private function getMimeType(string $path): string
    {
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
        
        $mimeTypes = [
            'css' => 'text/css',
            'js' => 'application/javascript',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'ico' => 'image/x-icon',
            'woff' => 'font/woff',
            'woff2' => 'font/woff2',
            'ttf' => 'font/ttf',
            'eot' => 'application/vnd.ms-fontobject',
            'otf' => 'font/otf',
            'webp' => 'image/webp',
            'avif' => 'image/avif',
            'mp4' => 'video/mp4',
            'webm' => 'video/webm',
            'pdf' => 'application/pdf',
            'zip' => 'application/zip',
            'txt' => 'text/plain',
            'xml' => 'application/xml',
            'json' => 'application/json',
            'map' => 'application/json',
        ];

        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }

    /**
     * Get cache max age for different file types.
     */
    private function getCacheMaxAge(string $path): int
    {
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
        
        // Different cache durations for different asset types
        $cacheDurations = [
            'css' => 86400 * 7,    // 1 week
            'js' => 86400 * 7,     // 1 week
            'png' => 86400 * 30,   // 1 month
            'jpg' => 86400 * 30,   // 1 month
            'jpeg' => 86400 * 30,  // 1 month
            'gif' => 86400 * 30,   // 1 month
            'svg' => 86400 * 30,   // 1 month
            'ico' => 86400 * 30,   // 1 month
            'woff' => 86400 * 365, // 1 year
            'woff2' => 86400 * 365, // 1 year
            'ttf' => 86400 * 365,  // 1 year
            'eot' => 86400 * 365,  // 1 year
            'otf' => 86400 * 365,  // 1 year
            'webp' => 86400 * 30,  // 1 month
            'avif' => 86400 * 30,  // 1 month
        ];

        return $cacheDurations[$extension] ?? 86400; // Default 1 day
    }
}
