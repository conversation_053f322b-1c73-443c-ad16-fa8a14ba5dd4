<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use App\Services\VisitorAnalytics;
use App\Services\LoginHistoryService;
use App\Models\LoginHistory;

class ProfileController extends Controller
{
    protected VisitorAnalytics $visitorAnalytics;
    protected LoginHistoryService $loginHistoryService;

    /**
     * Create a new controller instance.
     */
    public function __construct(VisitorAnalytics $visitorAnalytics, LoginHistoryService $loginHistoryService)
    {
        $this->middleware('auth');
        $this->visitorAnalytics = $visitorAnalytics;
        $this->loginHistoryService = $loginHistoryService;
    }

    /**
     * Show the profile edit form.
     */
    public function edit(): View
    {
        $user = auth()->user();

        // Track profile edit page visit
        $this->visitorAnalytics->trackPageVisit(
            'Profile Edit',
            [
                'user_id' => $user->id,
                'action' => 'view_profile_edit',
            ]
        );

        return view('profile.edit', compact('user'));
    }

    /**
     * Update the user's profile information.
     */
    public function update(Request $request): RedirectResponse
    {
        $user = auth()->user();

        $validated = $request->validate([
            'first_name' => ['required', 'string', 'max:100'],
            'last_name' => ['required', 'string', 'max:100'],
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'phone' => ['nullable', 'string', 'max:20'],
            'avatar' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
        ]);

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Delete old avatar if exists
            if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
                Storage::disk('public')->delete($user->avatar);
            }

            // Store new avatar
            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $validated['avatar'] = $avatarPath;
        }

        // Check if email changed (requires re-verification)
        $emailChanged = $user->email !== $validated['email'];

        // Update user
        $user->update($validated);

        // Track profile update in visitor analytics
        $this->visitorAnalytics->trackFormInteraction(
            'profile_update_form',
            'submit',
            true,
            [
                'user_id' => $user->id,
                'fields_updated' => array_keys($validated),
                'email_changed' => $emailChanged,
                'avatar_updated' => $request->hasFile('avatar'),
            ]
        );

        // If email changed, mark as unverified
        if ($emailChanged) {
            $user->email_verified_at = null;
            $user->save();

            // Send verification email
            $user->sendEmailVerificationNotification();

            return redirect()->route('profile.edit')
                ->with('success', 'Profile updated successfully! Please check your email to verify your new email address.');
        }

        return redirect()->route('profile.edit')
            ->with('success', 'Profile updated successfully!');
    }

    /**
     * Update the user's password.
     */
    public function updatePassword(Request $request): RedirectResponse
    {
        $user = auth()->user();

        $validated = $request->validate([
            'current_password' => ['required', 'string'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
        ]);

        // Verify current password
        if (!Hash::check($validated['current_password'], $user->password)) {
            return back()->withErrors(['current_password' => 'The current password is incorrect.']);
        }

        // Update password
        $user->update([
            'password' => Hash::make($validated['password']),
        ]);

        return redirect()->route('profile.edit')
            ->with('success', 'Password updated successfully!');
    }

    /**
     * Delete the user's avatar.
     */
    public function deleteAvatar(): RedirectResponse
    {
        $user = auth()->user();

        if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
            Storage::disk('public')->delete($user->avatar);
        }

        $user->update(['avatar' => null]);

        return redirect()->route('profile.edit')
            ->with('success', 'Avatar deleted successfully!');
    }

    /**
     * Show the account deletion confirmation page.
     */
    public function deleteAccount(): View
    {
        return view('profile.delete-account');
    }

    /**
     * Delete the user's account.
     */
    public function destroyAccount(Request $request): RedirectResponse
    {
        $user = auth()->user();

        $request->validate([
            'password' => ['required', 'string'],
            'confirmation' => ['required', 'string', 'in:DELETE'],
        ]);

        // Verify password
        if (!Hash::check($request->password, $user->password)) {
            return back()->withErrors(['password' => 'The password is incorrect.']);
        }

        // Soft delete the user
        $user->update([
            'is_deleted' => true,
            'is_active' => false,
        ]);

        // Logout the user
        auth()->logout();

        return redirect()->route('home')
            ->with('success', 'Your account has been deleted successfully.');
    }

    /**
     * Show the login history page.
     */
    public function loginHistory(): View
    {
        $user = auth()->user();

        // Track login history page visit
        $this->visitorAnalytics->trackPageVisit(
            'Profile Login History',
            [
                'user_id' => $user->id,
                'action' => 'view_login_history',
            ]
        );

        // Get basic statistics
        $stats = $this->loginHistoryService->getLoginStats($user, 30);

        return view('profile.login-history', compact('user', 'stats'));
    }

    /**
     * Get login history data for AJAX requests.
     */
    public function loginHistoryData(Request $request): JsonResponse
    {
        $user = auth()->user();

        $query = LoginHistory::where('user_id', $user->id)
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->has('status') && $request->status !== 'all') {
            if ($request->status === 'success') {
                $query->where('login_successful', true);
            } elseif ($request->status === 'failed') {
                $query->where('login_successful', false);
            }
        }

        if ($request->has('device_type') && $request->device_type !== 'all') {
            $query->where('device_type', $request->device_type);
        }

        if ($request->has('ip_address') && !empty($request->ip_address)) {
            $query->where('ip_address', 'like', '%' . $request->ip_address . '%');
        }

        // Date range filter
        if ($request->has('date_range')) {
            switch ($request->date_range) {
                case 'today':
                    $query->whereDate('created_at', today());
                    break;
                case 'this_week':
                    $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
                    break;
                case '7_days':
                    $query->where('created_at', '>=', now()->subDays(7));
                    break;
                case 'custom':
                    if ($request->has('start_date') && $request->has('end_date')) {
                        $query->whereBetween('created_at', [
                            $request->start_date . ' 00:00:00',
                            $request->end_date . ' 23:59:59'
                        ]);
                    }
                    break;
            }
        }

        $loginHistories = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $loginHistories->items(),
            'pagination' => [
                'current_page' => $loginHistories->currentPage(),
                'last_page' => $loginHistories->lastPage(),
                'per_page' => $loginHistories->perPage(),
                'total' => $loginHistories->total(),
                'from' => $loginHistories->firstItem(),
                'to' => $loginHistories->lastItem(),
            ]
        ]);
    }
}
