<?php

namespace Database\Factories;

use App\Models\LoginHistoryPermission;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LoginHistoryPermission>
 */
class LoginHistoryPermissionFactory extends Factory
{
    protected $model = LoginHistoryPermission::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'uuid' => Str::uuid(),
            'user_id' => User::factory(),
            'granted_by_user_id' => User::factory(),
            'access_level' => $this->faker->randomElement(['none', 'partial', 'full']),
            'specific_permissions' => $this->getRandomPermissions(),
            'reason' => $this->faker->sentence(),
            'granted_at' => now(),
            'expires_at' => $this->faker->optional(0.7)->dateTimeBetween('now', '+1 year'),
            'is_active' => true,
            'admin_notes' => $this->faker->optional(0.3)->paragraph(),
        ];
    }

    /**
     * Indicate that the permission has no access.
     */
    public function noAccess(): static
    {
        return $this->state(fn (array $attributes) => [
            'access_level' => 'none',
            'specific_permissions' => [],
        ]);
    }

    /**
     * Indicate that the permission has partial access.
     */
    public function partialAccess(): static
    {
        return $this->state(fn (array $attributes) => [
            'access_level' => 'partial',
            'specific_permissions' => LoginHistoryPermission::getDefaultPartialPermissions(),
        ]);
    }

    /**
     * Indicate that the permission has full access.
     */
    public function fullAccess(): static
    {
        return $this->state(fn (array $attributes) => [
            'access_level' => 'full',
            'specific_permissions' => array_keys(LoginHistoryPermission::getAvailableSpecificPermissions()),
        ]);
    }

    /**
     * Indicate that the permission is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the permission is expired.
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'expires_at' => $this->faker->dateTimeBetween('-1 year', '-1 day'),
        ]);
    }

    /**
     * Indicate that the permission is expiring soon.
     */
    public function expiringSoon(): static
    {
        return $this->state(fn (array $attributes) => [
            'expires_at' => $this->faker->dateTimeBetween('now', '+7 days'),
        ]);
    }

    /**
     * Indicate that the permission never expires.
     */
    public function neverExpires(): static
    {
        return $this->state(fn (array $attributes) => [
            'expires_at' => null,
        ]);
    }

    /**
     * Set specific user for the permission.
     */
    public function forUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
        ]);
    }

    /**
     * Set specific granter for the permission.
     */
    public function grantedBy(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'granted_by_user_id' => $user->id,
        ]);
    }

    /**
     * Set custom specific permissions.
     */
    public function withPermissions(array $permissions): static
    {
        return $this->state(fn (array $attributes) => [
            'specific_permissions' => $permissions,
        ]);
    }

    /**
     * Set custom reason.
     */
    public function withReason(string $reason): static
    {
        return $this->state(fn (array $attributes) => [
            'reason' => $reason,
        ]);
    }

    /**
     * Set custom expiration date.
     */
    public function expiresAt(\DateTime $date): static
    {
        return $this->state(fn (array $attributes) => [
            'expires_at' => $date,
        ]);
    }

    /**
     * Add admin notes.
     */
    public function withNotes(string $notes): static
    {
        return $this->state(fn (array $attributes) => [
            'admin_notes' => $notes,
        ]);
    }

    /**
     * Get random permissions based on access level.
     */
    protected function getRandomPermissions(): array
    {
        $allPermissions = array_keys(LoginHistoryPermission::getAvailableSpecificPermissions());
        $accessLevel = $this->faker->randomElement(['none', 'partial', 'full']);

        return match ($accessLevel) {
            'none' => [],
            'partial' => $this->faker->randomElements($allPermissions, $this->faker->numberBetween(1, 4)),
            'full' => $allPermissions,
            default => [],
        };
    }
}
