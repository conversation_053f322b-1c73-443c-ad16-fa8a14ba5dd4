<?php

namespace Tests\Feature\Services;

use App\Services\FileService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

use PHPUnit\Framework\Attributes\Test;
class FileServiceFeatureTest extends TestCase
{
    private FileService $fileService;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->fileService = app(FileService::class);
        
        // Set up test configuration
        Config::set('file.max_file_size', 50 * 1024 * 1024);
        Config::set('file.allowed_mimes', [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'text/csv',
            'application/zip',
        ]);
        Config::set('file.allowed_extensions', ['pdf', 'doc', 'docx', 'txt', 'csv', 'zip']);
        Config::set('file.virus_scan.enabled', false);
        Config::set('file.storage.disk', 'testing');
        Config::set('file.storage.path', 'files');
        Config::set('file.storage.organize_by_date', true);
        Config::set('file.storage.organize_by_type', true);
        Config::set('file.logging.enabled', false);
        Config::set('file.security.remove_metadata', false);
        Config::set('file.content_analysis.extract_text', false);
        Config::set('file.archive_handling.enabled', false);
        
        Config::set('filesystems.disks.testing', [
            'driver' => 'local',
            'root' => storage_path('app/testing'),
        ]);
        
        Storage::fake('testing');
    }
    
    protected function tearDown(): void
    {
        Storage::disk('testing')->deleteDirectory('');
        parent::tearDown();
    }
    #[Test]
    public function it_can_process_valid_pdf_file()
    {
        // Create a fake PDF file with proper header
        $tempPath = tempnam(sys_get_temp_dir(), 'test_pdf');
        file_put_contents($tempPath, '%PDF-1.4' . str_repeat('a', 1000));
        
        $file = new UploadedFile($tempPath, 'document.pdf', 'application/pdf', null, true);
        
        $result = $this->fileService->processUploadedFile($file);
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('file_path', $result);
        $this->assertArrayHasKey('file_info', $result);
        $this->assertArrayHasKey('virus_scan', $result);
        $this->assertArrayHasKey('processing_time_ms', $result);
        
        $this->assertEquals('document.pdf', $result['file_info']['original_name']);
        $this->assertTrue($result['virus_scan']['clean']);
        
        // Verify file was stored
        $this->assertTrue(Storage::disk('testing')->exists($result['file_path']));
        
        unlink($tempPath);
    }
    #[Test]
    public function it_organizes_files_by_date_and_type()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_pdf');
        file_put_contents($tempPath, '%PDF-1.4' . str_repeat('a', 1000));
        
        $file = new UploadedFile($tempPath, 'test.pdf', 'application/pdf', null, true);
        
        $result = $this->fileService->processUploadedFile($file);
        
        $this->assertTrue($result['success']);
        
        // Check that file path includes date and type organization
        $expectedPath = 'files/' . date('Y/m/d') . '/document/';
        $this->assertStringContainsString($expectedPath, $result['file_path']);
        
        unlink($tempPath);
    }
    #[Test]
    public function it_can_quick_upload_file()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt');
        file_put_contents($tempPath, 'This is a test document');
        
        $file = new UploadedFile($tempPath, 'quick.txt', 'text/plain', null, true);
        
        $result = $this->fileService->quickUpload($file, 'uploads');
        
        $this->assertTrue($result['success']);
        $this->assertStringContainsString('uploads', $result['file_path']);
        $this->assertTrue(Storage::disk('testing')->exists($result['file_path']));
        
        unlink($tempPath);
    }
    #[Test]
    public function it_can_secure_upload_file()
    {
        Config::set('file.security.remove_metadata', true);
        Config::set('file.content_analysis.extract_text', true);
        
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt');
        file_put_contents($tempPath, 'This is a secure document');
        
        $file = new UploadedFile($tempPath, 'secure.txt', 'text/plain', null, true);
        
        $result = $this->fileService->secureUpload($file, 'secure');
        
        $this->assertTrue($result['success']);
        $this->assertStringContainsString('secure', $result['file_path']);
        $this->assertTrue(Storage::disk('testing')->exists($result['file_path']));
        
        unlink($tempPath);
    }
    #[Test]
    public function it_rejects_invalid_files()
    {
        $file = UploadedFile::fake()->create('malware.exe', 1024, 'application/octet-stream');
        
        $result = $this->fileService->processUploadedFile($file);
        
        $this->assertFalse($result['success']);
        $this->assertArrayHasKey('errors', $result);
        $this->assertNotEmpty($result['errors']);
    }
    #[Test]
    public function it_can_get_file_url()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt');
        file_put_contents($tempPath, 'Test content');
        
        $file = new UploadedFile($tempPath, 'test.txt', 'text/plain', null, true);
        
        $result = $this->fileService->processUploadedFile($file);
        $this->assertTrue($result['success']);
        
        $url = $this->fileService->getFileUrl($result['file_path']);
        
        $this->assertIsString($url);
        $this->assertStringContainsString($result['file_path'], $url);
        
        unlink($tempPath);
    }
    #[Test]
    public function it_can_get_file_info()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt');
        file_put_contents($tempPath, 'Test content for info');
        
        $file = new UploadedFile($tempPath, 'info.txt', 'text/plain', null, true);
        
        $result = $this->fileService->processUploadedFile($file);
        $this->assertTrue($result['success']);
        
        $info = $this->fileService->getFileInfo($result['file_path']);
        
        $this->assertTrue($info['exists']);
        $this->assertArrayHasKey('size', $info);
        $this->assertArrayHasKey('last_modified', $info);
        $this->assertArrayHasKey('mime_type', $info);
        $this->assertArrayHasKey('url', $info);
        
        unlink($tempPath);
    }
    #[Test]
    public function it_can_delete_file()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt');
        file_put_contents($tempPath, 'Test content to delete');
        
        $file = new UploadedFile($tempPath, 'delete.txt', 'text/plain', null, true);
        
        $result = $this->fileService->processUploadedFile($file);
        $this->assertTrue($result['success']);
        
        // Verify file exists
        $this->assertTrue(Storage::disk('testing')->exists($result['file_path']));
        
        // Delete file
        $deleted = $this->fileService->deleteFile($result['file_path']);
        
        $this->assertTrue($deleted);
        $this->assertFalse(Storage::disk('testing')->exists($result['file_path']));
        
        unlink($tempPath);
    }
    #[Test]
    public function it_handles_file_not_found_gracefully()
    {
        $info = $this->fileService->getFileInfo('non-existent-file.txt');
        
        $this->assertFalse($info['exists']);
        $this->assertEquals('File not found', $info['message']);
    }
    #[Test]
    public function it_processes_csv_files()
    {
        $csvContent = "Name,Email,Phone\nJohn Doe,<EMAIL>,************\nJane Smith,<EMAIL>,************";
        $tempPath = tempnam(sys_get_temp_dir(), 'test_csv');
        file_put_contents($tempPath, $csvContent);
        
        $file = new UploadedFile($tempPath, 'data.csv', 'text/csv', null, true);
        
        $result = $this->fileService->processUploadedFile($file);
        
        $this->assertTrue($result['success']);
        $this->assertEquals('data.csv', $result['file_info']['original_name']);
        $this->assertEquals('spreadsheet', $result['file_info']['type_category']);
        
        unlink($tempPath);
    }
    #[Test]
    public function it_processes_word_documents()
    {
        // Create a minimal DOCX file structure (ZIP with proper signature)
        $tempPath = tempnam(sys_get_temp_dir(), 'test_docx');
        file_put_contents($tempPath, 'PK' . str_repeat('a', 1000)); // ZIP signature
        
        $file = new UploadedFile($tempPath, 'document.docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', null, true);
        
        $result = $this->fileService->processUploadedFile($file);
        
        $this->assertTrue($result['success']);
        $this->assertEquals('document.docx', $result['file_info']['original_name']);
        $this->assertEquals('document', $result['file_info']['type_category']);
        
        unlink($tempPath);
    }
    #[Test]
    public function it_handles_archive_processing_when_enabled()
    {
        Config::set('file.archive_handling.enabled', true);
        
        // Create a minimal ZIP file
        $tempPath = tempnam(sys_get_temp_dir(), 'test_zip');
        file_put_contents($tempPath, 'PK' . str_repeat('a', 1000));
        
        $file = new UploadedFile($tempPath, 'archive.zip', 'application/zip', null, true);
        
        $result = $this->fileService->processUploadedFile($file);
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('archive_analysis', $result);
        
        unlink($tempPath);
    }
    #[Test]
    public function it_handles_text_extraction_when_enabled()
    {
        Config::set('file.content_analysis.extract_text', true);
        
        $textContent = 'This is a test document with extractable text content.';
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt');
        file_put_contents($tempPath, $textContent);
        
        $file = new UploadedFile($tempPath, 'extract.txt', 'text/plain', null, true);
        
        $result = $this->fileService->processUploadedFile($file);
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('text_content', $result);
        $this->assertTrue($result['text_content']['success']);
        $this->assertStringContainsString('extractable text', $result['text_content']['text']);
        
        unlink($tempPath);
    }
    #[Test]
    public function it_measures_processing_time()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt');
        file_put_contents($tempPath, 'Performance test content');
        
        $file = new UploadedFile($tempPath, 'performance.txt', 'text/plain', null, true);
        
        $result = $this->fileService->processUploadedFile($file);
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('processing_time_ms', $result);
        $this->assertIsFloat($result['processing_time_ms']);
        $this->assertGreaterThan(0, $result['processing_time_ms']);
        
        unlink($tempPath);
    }
    #[Test]
    public function it_handles_exceptions_gracefully()
    {
        // Create a file that will cause issues during processing
        $file = UploadedFile::fake()->create('test.pdf', 0, 'application/pdf'); // Empty file
        
        $result = $this->fileService->processUploadedFile($file);
        
        // Should handle gracefully even if processing fails
        $this->assertArrayHasKey('success', $result);
        $this->assertArrayHasKey('errors', $result);
    }
}
