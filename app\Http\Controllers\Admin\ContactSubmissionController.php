<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ContactSubmission;
use App\Services\ActivityLogger;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class ContactSubmissionController extends Controller
{
    protected ActivityLogger $activityLogger;

    public function __construct(ActivityLogger $activityLogger)
    {
        $this->middleware('auth');
        $this->middleware('role:admin,staff'); // Ensure only admins/staff can access
        $this->activityLogger = $activityLogger;
    }

    public function index(Request $request): View
    {
        $query = ContactSubmission::query()
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('status')) {
            switch ($request->status) {
                case 'unread':
                    $query->where('is_read', false);
                    break;
                case 'read':
                    $query->where('is_read', true);
                    break;
                case 'spam':
                    $query->where('is_spam', true);
                    break;
                case 'not_spam':
                    $query->where('is_spam', false);
                    break;
            }
        }

        if ($request->filled('service')) {
            $query->where('service', $request->service);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('company', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%")
                  ->orWhere('message', 'like', "%{$search}%");
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $submissions = $query->paginate(20)->withQueryString();

        // Get statistics
        $stats = [
            'total' => ContactSubmission::count(),
            'unread' => ContactSubmission::where('is_read', false)->count(),
            'spam' => ContactSubmission::where('is_spam', true)->count(),
            'today' => ContactSubmission::whereDate('created_at', today())->count(),
        ];

        // Get unique services for filter
        $services = ContactSubmission::whereNotNull('service')
            ->distinct()
            ->pluck('service')
            ->filter()
            ->sort()
            ->values();

        return view('admin.contact-submissions.index', compact('submissions', 'stats', 'services'));
    }

    public function show(ContactSubmission $contactSubmission): View
    {
        // Mark as read if not already read
        if (!$contactSubmission->is_read) {
            $contactSubmission->markAsRead();

            // Log activity
            $this->activityLogger->logCustomerActivity(
                'admin_contact_submission_viewed',
                "Admin viewed contact submission from {$contactSubmission->name}",
                'info',
                null,
                [
                    'submission_id' => $contactSubmission->id,
                    'submission_uuid' => $contactSubmission->uuid,
                    'sender_email' => $contactSubmission->email,
                    'admin_user' => Auth::user()->email,
                ]
            );
        }

        return view('admin.contact-submissions.show', compact('contactSubmission'));
    }

    public function markAsRead(ContactSubmission $contactSubmission): JsonResponse
    {
        $contactSubmission->markAsRead();

        $this->activityLogger->logCustomerActivity(
            'admin_contact_submission_marked_read',
            "Admin marked contact submission from {$contactSubmission->name} as read",
            'success',
            null,
            [
                'submission_id' => $contactSubmission->id,
                'submission_uuid' => $contactSubmission->uuid,
                'sender_email' => $contactSubmission->email,
                'admin_user' => Auth::user()->email,
            ]
        );

        return response()->json([
            'success' => true,
            'message' => 'Submission marked as read.',
        ]);
    }

    public function markAsSpam(ContactSubmission $contactSubmission): JsonResponse
    {
        $newStatus = !$contactSubmission->is_spam;
        $contactSubmission->update(['is_spam' => $newStatus]);

        $this->activityLogger->logCustomerActivity(
            'admin_contact_submission_spam_toggle',
            "Admin " . ($newStatus ? 'marked as spam' : 'unmarked as spam') . " contact submission from {$contactSubmission->name}",
            'warning',
            null,
            [
                'submission_id' => $contactSubmission->id,
                'submission_uuid' => $contactSubmission->uuid,
                'sender_email' => $contactSubmission->email,
                'new_spam_status' => $newStatus,
                'admin_user' => Auth::user()->email,
            ]
        );

        return response()->json([
            'success' => true,
            'is_spam' => $newStatus,
            'message' => $newStatus ? 'Submission marked as spam.' : 'Submission unmarked as spam.',
        ]);
    }

    public function reply(Request $request, ContactSubmission $contactSubmission): JsonResponse
    {
        $request->validate([
            'reply_message' => 'required|string|max:5000',
        ]);

        // Mark as replied
        $contactSubmission->update([
            'replied_at' => now(),
            'replied_by' => Auth::id(),
        ]);

        // TODO: Implement email sending logic here
        // Mail::to($contactSubmission->email)->send(new ContactReply($request->reply_message));

        $this->activityLogger->logCustomerActivity(
            'admin_contact_submission_replied',
            "Admin replied to contact submission from {$contactSubmission->name}",
            'success',
            null,
            [
                'submission_id' => $contactSubmission->id,
                'submission_uuid' => $contactSubmission->uuid,
                'sender_email' => $contactSubmission->email,
                'reply_length' => strlen($request->reply_message),
                'admin_user' => Auth::user()->email,
            ]
        );

        return response()->json([
            'success' => true,
            'message' => 'Reply sent successfully.',
        ]);
    }

    public function destroy(ContactSubmission $contactSubmission): RedirectResponse
    {
        $senderName = $contactSubmission->name;
        $senderEmail = $contactSubmission->email;

        $contactSubmission->delete();

        $this->activityLogger->logCustomerActivity(
            'admin_contact_submission_deleted',
            "Admin deleted contact submission from {$senderName}",
            'warning',
            null,
            [
                'sender_name' => $senderName,
                'sender_email' => $senderEmail,
                'admin_user' => Auth::user()->email,
            ]
        );

        return redirect()->route('admin.contact-submissions.index')
            ->with('success', 'Contact submission deleted successfully.');
    }
}
