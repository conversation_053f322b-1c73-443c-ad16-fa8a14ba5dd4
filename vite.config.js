import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/css/ai-animations.css',
                'resources/js/app.js',
                'resources/js/ai-services-lite.js'
            ],
            refresh: true,
        }),
        tailwindcss(),
    ],
    build: {
        rollupOptions: {
            output: {
                manualChunks(id) {
                    if (id.includes('node_modules')) {
                        if (id.includes('three')) return 'vendor-3d';
                        if (id.includes('gsap')) return 'vendor-animations';
                        return 'vendor';
                    }
                    if (id.includes('ai-services-3d')) return 'ai-3d';
                    if (id.includes('bootstrap')) return 'core';
                }
            }
        },
        chunkSizeWarningLimit: 500
    }
});
