<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ChatFile extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'chat_room_id',
        'chat_message_id',
        'uploaded_by',
        'original_filename',
        'stored_filename',
        'file_path',
        'file_size',
        'mime_type',
        'file_hash',
        'is_image',
        'is_scanned',
        'scan_result',
        'download_count',
        'expires_at',
    ];

    protected $casts = [
        'file_size' => 'integer',
        'is_image' => 'boolean',
        'is_scanned' => 'boolean',
        'download_count' => 'integer',
        'expires_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $attributes = [
        'is_image' => false,
        'is_scanned' => false,
        'download_count' => 0,
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Scope for images.
     */
    public function scopeImages($query)
    {
        return $query->where('is_image', true);
    }

    /**
     * Scope for documents.
     */
    public function scopeDocuments($query)
    {
        return $query->where('is_image', false);
    }

    /**
     * Scope for scanned files.
     */
    public function scopeScanned($query)
    {
        return $query->where('is_scanned', true);
    }

    /**
     * Scope for clean files.
     */
    public function scopeClean($query)
    {
        return $query->where('scan_result', 'clean');
    }

    /**
     * Scope for expired files.
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now());
    }

    /**
     * Get the chat room this file belongs to.
     */
    public function chatRoom(): BelongsTo
    {
        return $this->belongsTo(ChatRoom::class);
    }

    /**
     * Get the message this file is attached to.
     */
    public function chatMessage(): BelongsTo
    {
        return $this->belongsTo(ChatMessage::class);
    }

    /**
     * Get the user who uploaded this file.
     */
    public function uploader(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Check if file is an image.
     */
    public function isImage(): bool
    {
        return $this->is_image;
    }

    /**
     * Check if file is scanned.
     */
    public function isScanned(): bool
    {
        return $this->is_scanned;
    }

    /**
     * Check if file is clean (virus scan).
     */
    public function isVirusClean(): bool
    {
        return $this->scan_result === 'clean';
    }

    /**
     * Check if file is infected.
     */
    public function isInfected(): bool
    {
        return $this->scan_result === 'infected';
    }

    /**
     * Check if file is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at < now();
    }

    /**
     * Get file size in human readable format.
     */
    public function getFileSizeHumanAttribute(): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($this->file_size, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Get file extension.
     */
    public function getFileExtensionAttribute(): string
    {
        return pathinfo($this->original_filename, PATHINFO_EXTENSION);
    }

    /**
     * Get file type category.
     */
    public function getFileTypeCategoryAttribute(): string
    {
        if ($this->is_image) {
            return 'image';
        }

        $extension = strtolower($this->file_extension);

        if (in_array($extension, ['pdf', 'doc', 'docx', 'txt', 'rtf'])) {
            return 'document';
        }

        if (in_array($extension, ['zip', 'rar', '7z', 'tar', 'gz'])) {
            return 'archive';
        }

        if (in_array($extension, ['mp3', 'wav', 'ogg', 'flac'])) {
            return 'audio';
        }

        if (in_array($extension, ['mp4', 'avi', 'mov', 'wmv'])) {
            return 'video';
        }

        return 'other';
    }

    /**
     * Get file icon based on type.
     */
    public function getFileIconAttribute(): string
    {
        return match($this->file_type_category) {
            'image' => '🖼️',
            'document' => '📄',
            'archive' => '📦',
            'audio' => '🎵',
            'video' => '🎬',
            default => '📎'
        };
    }

    /**
     * Get download URL.
     */
    public function getDownloadUrlAttribute(): string
    {
        return route('chat.files.download', $this->uuid);
    }

    /**
     * Get preview URL for images.
     */
    public function getPreviewUrlAttribute(): ?string
    {
        if (!$this->is_image) {
            return null;
        }

        return route('chat.files.preview', $this->uuid);
    }

    /**
     * Get scan result label.
     */
    public function getScanResultLabelAttribute(): string
    {
        return match($this->scan_result) {
            'clean' => 'Clean',
            'infected' => 'Infected',
            'suspicious' => 'Suspicious',
            'error' => 'Scan Error',
            default => 'Not Scanned'
        };
    }

    /**
     * Get scan result color for UI.
     */
    public function getScanResultColorAttribute(): string
    {
        return match($this->scan_result) {
            'clean' => 'green',
            'infected' => 'red',
            'suspicious' => 'orange',
            'error' => 'yellow',
            default => 'gray'
        };
    }

    /**
     * Increment download count.
     */
    public function incrementDownloadCount(): bool
    {
        $this->increment('download_count');
        return true;
    }

    /**
     * Mark file as scanned.
     */
    public function markAsScanned(string $result): bool
    {
        $this->is_scanned = true;
        $this->scan_result = $result;
        return $this->save();
    }

    /**
     * Get file content.
     */
    public function getContent(): ?string
    {
        if (!Storage::exists($this->file_path)) {
            return null;
        }

        return Storage::get($this->file_path);
    }

    /**
     * Delete file from storage.
     */
    public function deleteFile(): bool
    {
        if (Storage::exists($this->file_path)) {
            Storage::delete($this->file_path);
        }

        return $this->delete();
    }

    /**
     * Check if file exists in storage.
     */
    public function fileExists(): bool
    {
        return Storage::exists($this->file_path);
    }

    /**
     * Generate temporary download token.
     */
    public function generateDownloadToken(int $expiryMinutes = 60): string
    {
        $token = Str::random(32);
        
        cache()->put("file_download_token_{$token}", [
            'file_id' => $this->id,
            'expires_at' => now()->addMinutes($expiryMinutes)
        ], $expiryMinutes * 60);

        return $token;
    }

    /**
     * Validate download token.
     */
    public static function validateDownloadToken(string $token): ?self
    {
        $data = cache()->get("file_download_token_{$token}");
        
        if (!$data || now() > $data['expires_at']) {
            return null;
        }

        return static::find($data['file_id']);
    }
}
