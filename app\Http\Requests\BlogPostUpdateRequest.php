<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BlogPostUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Authorization is handled by middleware
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $postId = $this->route('post')->id ?? $this->route('post');

        return [
            // Basic fields
            'title' => ['required', 'string', 'max:300'],
            'slug' => [
                'nullable',
                'string',
                'max:400',
                Rule::unique('blog_posts', 'slug')->ignore($postId),
                'regex:/^[a-z0-9]+(?:-[a-z0-9]+)*$/'
            ],
            'excerpt' => ['nullable', 'string', 'max:500'],
            'content' => ['required', 'string'],
            
            // Relationships
            'category_id' => ['nullable', 'exists:blog_categories,id'],
            'author_id' => ['required', 'exists:users,id'],
            'service_ids' => ['nullable', 'array'],
            'service_ids.*' => ['exists:services,id'],
            
            // Media
            'featured_image' => ['nullable', 'image', 'mimes:jpeg,jpg,png,gif,webp', 'max:5120'], // 5MB
            'featured_image_url' => ['nullable', 'string', 'max:500'], // For existing image
            'gallery_images' => ['nullable', 'array', 'max:10'],
            'gallery_images.*' => ['image', 'mimes:jpeg,jpg,png,gif,webp', 'max:5120'],
            'existing_gallery_images' => ['nullable', 'array'], // Keep track of existing images
            'remove_gallery_images' => ['nullable', 'array'], // Images to remove
            
            // Videos
            'videos' => ['nullable', 'array', 'max:5'],
            'videos.*.type' => ['required_with:videos', 'in:uploaded,youtube,vimeo'],
            'videos.*.url' => ['required_with:videos', 'string', 'max:500'],
            'videos.*.title' => ['nullable', 'string', 'max:200'],
            'videos.*.thumbnail' => ['nullable', 'string', 'max:500'],
            
            // Social embeds
            'social_embeds' => ['nullable', 'array', 'max:10'],
            'social_embeds.*.platform' => ['required_with:social_embeds', 'in:twitter,instagram,facebook,linkedin'],
            'social_embeds.*.url' => ['required_with:social_embeds', 'string', 'url', 'max:500'],
            'social_embeds.*.embed_code' => ['nullable', 'string'],
            
            // Status
            'is_published' => ['boolean'],
            'is_featured' => ['boolean'],
            'is_deleted' => ['boolean'],
            'published_at' => ['nullable', 'date'],
            'scheduled_at' => ['nullable', 'date', 'after:now'],
            
            // SEO - Basic
            'meta_title' => ['nullable', 'string', 'max:255'],
            'meta_description' => ['nullable', 'string', 'max:500'],
            'meta_keywords' => ['nullable', 'string', 'max:500'],
            'focus_keyword' => ['nullable', 'string', 'max:100'],
            'canonical_url' => ['nullable', 'url', 'max:500'],
            
            // SEO - Open Graph
            'og_title' => ['nullable', 'string', 'max:255'],
            'og_description' => ['nullable', 'string', 'max:500'],
            'og_image' => ['nullable', 'string', 'max:500'],
            'og_type' => ['nullable', 'string', 'max:50'],
            
            // SEO - Twitter Card
            'twitter_card' => ['nullable', 'in:summary,summary_large_image,app,player'],
            'twitter_title' => ['nullable', 'string', 'max:255'],
            'twitter_description' => ['nullable', 'string', 'max:500'],
            'twitter_image' => ['nullable', 'string', 'max:500'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'title.required' => 'The blog post title is required.',
            'title.max' => 'The title cannot exceed 300 characters.',
            'slug.unique' => 'This slug is already in use. Please choose a different one.',
            'slug.regex' => 'The slug must contain only lowercase letters, numbers, and hyphens.',
            'content.required' => 'The blog post content is required.',
            'author_id.required' => 'An author must be assigned to this post.',
            'author_id.exists' => 'The selected author does not exist.',
            'category_id.exists' => 'The selected category does not exist.',
            'featured_image.image' => 'The featured image must be a valid image file.',
            'featured_image.max' => 'The featured image cannot exceed 5MB.',
            'gallery_images.max' => 'You cannot upload more than 10 gallery images.',
            'videos.max' => 'You cannot add more than 5 videos.',
            'social_embeds.max' => 'You cannot add more than 10 social media embeds.',
            'scheduled_at.after' => 'The scheduled publish date must be in the future.',
            'meta_title.max' => 'The meta title cannot exceed 255 characters.',
            'meta_description.max' => 'The meta description cannot exceed 500 characters.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Auto-generate slug from title if not provided
        if (!$this->slug && $this->title) {
            $this->merge([
                'slug' => \Illuminate\Support\Str::slug($this->title),
            ]);
        }

        // Convert boolean strings to actual booleans
        $this->merge([
            'is_published' => filter_var($this->is_published ?? false, FILTER_VALIDATE_BOOLEAN),
            'is_featured' => filter_var($this->is_featured ?? false, FILTER_VALIDATE_BOOLEAN),
            'is_deleted' => filter_var($this->is_deleted ?? false, FILTER_VALIDATE_BOOLEAN),
        ]);

        // Set published_at to now if publishing for the first time
        $post = $this->route('post');
        if ($this->is_published && !$this->published_at && (!$post || !$post->published_at)) {
            $this->merge([
                'published_at' => now(),
            ]);
        }
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'title' => 'post title',
            'slug' => 'URL slug',
            'content' => 'post content',
            'excerpt' => 'post excerpt',
            'category_id' => 'category',
            'author_id' => 'author',
            'service_ids' => 'related services',
            'featured_image' => 'featured image',
            'gallery_images' => 'gallery images',
            'videos' => 'videos',
            'social_embeds' => 'social media embeds',
            'meta_title' => 'SEO title',
            'meta_description' => 'SEO description',
            'meta_keywords' => 'SEO keywords',
            'focus_keyword' => 'focus keyword',
            'og_title' => 'Open Graph title',
            'og_description' => 'Open Graph description',
            'twitter_title' => 'Twitter card title',
            'twitter_description' => 'Twitter card description',
        ];
    }
}

