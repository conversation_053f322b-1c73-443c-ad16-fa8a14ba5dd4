<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\ChatSystemSetting;
use App\Services\ChatAIService;
use App\Services\ActivityLogger;
use App\Services\DashboardCacheService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use OpenAI\Laravel\Facades\OpenAI;
use PHPUnit\Framework\Attributes\Test;

class ChatAIAdvancedFeaturesTest extends TestCase
{
    use RefreshDatabase;

    protected ChatAIService $aiService;
    protected User $user;
    protected ChatRoom $room;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles
        $userRole = Role::factory()->create(['name' => 'user']);

        // Create user
        $this->user = User::factory()->create(['role_id' => $userRole->id]);

        // Create chat room
        $this->room = ChatRoom::factory()->create([
            'status' => 'active',
            'language' => 'en',
        ]);

        // Enable AI
        ChatSystemSetting::updateOrCreate(
            ['setting_key' => 'ai_enabled'],
            [
                'setting_value' => 'true',
                'setting_type' => 'boolean',
                'description' => 'Enable or disable AI chatbot',
                'is_public' => true,
            ]
        );

        // Mock dependencies
        $this->mock(ActivityLogger::class, function ($mock) {
            $mock->shouldReceive('log')->andReturn(new \App\Models\ActivityLog());
        });

        $this->mock(DashboardCacheService::class, function ($mock) {
            $mock->shouldReceive('invalidatePattern')->andReturn(true);
        });

        // Create AI service
        $this->aiService = app(ChatAIService::class);

        // Clear cache
        Cache::flush();
    }

    #[Test]
    public function ai_service_analyzes_sentiment_correctly()
    {
        // Test positive sentiment
        $positiveSentiment = $this->aiService->analyzeSentiment('I love this service! It is amazing and wonderful.');
        
        $this->assertEquals('positive', $positiveSentiment['sentiment']);
        $this->assertGreaterThan(0.7, $positiveSentiment['confidence']);
        $this->assertArrayHasKey('scores', $positiveSentiment);

        // Test negative sentiment
        $negativeSentiment = $this->aiService->analyzeSentiment('This is terrible! I hate this broken service.');
        
        $this->assertEquals('negative', $negativeSentiment['sentiment']);
        $this->assertGreaterThan(0.7, $negativeSentiment['confidence']);

        // Test neutral sentiment
        $neutralSentiment = $this->aiService->analyzeSentiment('Hello, can you help me with information?');
        
        $this->assertEquals('neutral', $neutralSentiment['sentiment']);
    }

    #[Test]
    public function ai_service_includes_sentiment_in_responses()
    {
        // Mock OpenAI response
        OpenAI::fake([
            \OpenAI\Responses\Chat\CreateResponse::fake([
                'choices' => [
                    [
                        'message' => [
                            'content' => 'I understand you are frustrated. Let me help you with that.'
                        ]
                    ]
                ],
                'usage' => [
                    'total_tokens' => 25
                ]
            ])
        ]);

        $response = $this->aiService->generateResponse(
            'I am really angry about this problem!',
            $this->room,
            []
        );

        $this->assertArrayHasKey('sentiment', $response);
        $this->assertEquals('negative', $response['sentiment']['sentiment']);
        $this->assertArrayHasKey('confidence', $response['sentiment']);
    }

    #[Test]
    public function ai_service_summarizes_conversations()
    {
        // Create some messages in the room
        ChatMessage::factory()->create([
            'chat_room_id' => $this->room->id,
            'user_id' => $this->user->id,
            'content' => 'Hello, I need help with my order.',
            'created_at' => now()->subMinutes(10),
        ]);

        ChatMessage::factory()->create([
            'chat_room_id' => $this->room->id,
            'user_id' => null, // AI response
            'content' => 'I can help you with your order. What is the issue?',
            'created_at' => now()->subMinutes(9),
        ]);

        ChatMessage::factory()->create([
            'chat_room_id' => $this->room->id,
            'user_id' => $this->user->id,
            'content' => 'The delivery was delayed and I am not happy about it.',
            'created_at' => now()->subMinutes(8),
        ]);

        $summary = $this->aiService->summarizeConversation($this->room);

        $this->assertArrayHasKey('summary', $summary);
        $this->assertArrayHasKey('key_points', $summary);
        $this->assertArrayHasKey('sentiment_overview', $summary);
        $this->assertArrayHasKey('participant_count', $summary);
        $this->assertArrayHasKey('message_count', $summary);
        
        $this->assertEquals(3, $summary['message_count']);
        $this->assertGreaterThan(0, $summary['participant_count']);
        $this->assertIsArray($summary['key_points']);
    }

    #[Test]
    public function ai_service_routes_queries_intelligently()
    {
        // Test support query routing (without escalation keywords)
        $supportRouting = $this->aiService->routeQuery(
            'I have a problem with my account and need help.',
            $this->room
        );

        $this->assertEquals('technical_support', $supportRouting['suggested_department']);
        $this->assertEquals('high', $supportRouting['priority']);
        $this->assertTrue($supportRouting['escalate_to_human']);

        // Test sales query routing
        $salesRouting = $this->aiService->routeQuery(
            'How much does your premium plan cost?',
            $this->room
        );

        $this->assertEquals('sales', $salesRouting['suggested_department']);
        $this->assertEquals('sales_qualified_lead', $salesRouting['recommended_action']);

        // Test greeting routing
        $greetingRouting = $this->aiService->routeQuery(
            'Hello there!',
            $this->room
        );

        $this->assertEquals('friendly_greeting', $greetingRouting['recommended_action']);
        $this->assertEquals('low', $greetingRouting['priority']);
        $this->assertFalse($greetingRouting['escalate_to_human']);
    }

    #[Test]
    public function ai_service_escalates_based_on_keywords()
    {
        $escalationRouting = $this->aiService->routeQuery(
            'This is urgent! I need to speak to a manager immediately!',
            $this->room
        );

        $this->assertTrue($escalationRouting['escalate_to_human']);
        $this->assertEquals('urgent', $escalationRouting['priority']);
        $this->assertStringContainsString('urgent', $escalationRouting['reasoning']);
    }

    #[Test]
    public function sentiment_analysis_is_cached()
    {
        $message = 'This is a test message for caching.';
        
        // First call
        $result1 = $this->aiService->analyzeSentiment($message);
        
        // Second call should use cache
        $result2 = $this->aiService->analyzeSentiment($message);
        
        $this->assertEquals($result1, $result2);
        
        // Verify cache key exists
        $cacheKey = 'sentiment_' . md5($message);
        $this->assertTrue(Cache::has($cacheKey));
    }

    #[Test]
    public function conversation_summary_is_cached()
    {
        // Create a message
        ChatMessage::factory()->create([
            'chat_room_id' => $this->room->id,
            'user_id' => $this->user->id,
            'content' => 'Test message for summary caching.',
        ]);

        // First call
        $summary1 = $this->aiService->summarizeConversation($this->room);
        
        // Second call should use cache
        $summary2 = $this->aiService->summarizeConversation($this->room);
        
        $this->assertEquals($summary1, $summary2);
    }
}
