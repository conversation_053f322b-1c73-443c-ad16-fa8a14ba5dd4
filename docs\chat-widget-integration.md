# Chat Widget Integration Guide

## Overview

The Modern Chat Widget is a floating, responsive chat interface that integrates seamlessly with the ChiSolution Chat API. It provides real-time messaging, file uploads, typing indicators, and a modern user experience.

## Features

### Core Features
- **Floating Design**: Positioned in bottom-right corner by default
- **Real-time Messaging**: WebSocket integration for instant communication
- **Responsive Layout**: Adapts to mobile screens (full-screen on mobile)
- **File Upload**: Support for images, PDFs, and text files
- **Emoji Picker**: Quick access to common emojis
- **Typing Indicators**: Shows when support agents are typing
- **Message Status**: Sent (✓), Delivered (✓✓), Read (✓✓ blue)
- **Unread Badge**: Shows count of unread messages
- **Online Status**: Indicates when support is available

### User Experience
- **Keyboard Shortcuts**: Ctrl/Cmd+K to toggle, Escape to close
- **Auto-save State**: Preserves chat session across page reloads
- **Sound Notifications**: Optional audio alerts for new messages
- **Browser Notifications**: Desktop notifications when chat is closed
- **Accessibility**: Screen reader support and keyboard navigation

## Installation

The chat widget is automatically included in all layouts and will initialize on page load.

### Files Included
- `resources/js/chat-widget.js` - Main widget functionality
- `resources/css/chat-widget.css` - Widget styles
- Integration in `resources/views/layouts/app.blade.php`
- Integration in `resources/views/layouts/dashboard.blade.php`
- Integration in `resources/views/layouts/auth.blade.php`

## Configuration

### Default Configuration
```javascript
{
    apiBaseUrl: '/api/v1/chat',
    position: 'bottom-right',
    theme: 'modern',
    autoOpen: false,
    enableSound: true,
    enableNotifications: true
}
```

### Customization Options

#### Position
- `bottom-right` (default)
- `bottom-left`
- `top-right`
- `top-left`

#### Theme
- `modern` (default) - Gradient design with rounded corners
- `dark` - Dark mode support (auto-detects system preference)

## API Integration

The widget integrates with the existing Chat API endpoints:

### Core Endpoints
- `POST /api/v1/chat/rooms` - Create chat room
- `GET /api/v1/chat/rooms/{uuid}` - Get room details
- `POST /api/v1/chat/rooms/{uuid}/messages` - Send message
- `GET /api/v1/chat/rooms/{uuid}/messages` - Get messages
- `POST /api/v1/chat/rooms/{uuid}/typing` - Send typing indicator

### WebSocket Events
- `message.sent` - New message received
- `user.typing` - Typing indicator
- `user.online.status` - Online status changes
- `message.read` - Message read receipts

## Usage

### Automatic Initialization
The widget automatically initializes on all pages except admin areas:

```javascript
// Automatic initialization
document.addEventListener('DOMContentLoaded', function() {
    if (!window.location.pathname.includes('/admin')) {
        window.chatWidget = new ModernChatWidget({
            apiBaseUrl: '/api/v1/chat',
            enableNotifications: true,
            enableSound: true
        });
    }
});
```

### Manual Control
```javascript
// Open chat programmatically
window.openChat();

// Close chat
window.closeChat();

// Toggle chat
window.toggleChat();

// Access widget instance
window.chatWidget.open();
window.chatWidget.close();
window.chatWidget.toggle();
```

### Keyboard Shortcuts
- **Ctrl/Cmd + K**: Toggle chat window
- **Escape**: Close chat window
- **Enter**: Send message (Shift+Enter for new line)

## Customization

### Disabling the Widget
Add a meta tag to disable the widget on specific pages:
```html
<meta name="chat-disabled" content="true">
```

### Custom Styling
Override CSS variables for theme customization:
```css
.modern-chat-widget {
    --chat-primary-color: #667eea;
    --chat-secondary-color: #764ba2;
    --chat-border-radius: 16px;
    --chat-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}
```

### Event Listeners
```javascript
// Listen for chat events
window.chatWidget.on('message:sent', function(message) {
    console.log('Message sent:', message);
});

window.chatWidget.on('room:created', function(room) {
    console.log('Room created:', room);
});
```

## Mobile Responsiveness

The widget automatically adapts to mobile devices:
- **Desktop**: 380px × 600px floating window
- **Mobile**: Full-screen overlay with safe area support
- **Tablet**: Responsive sizing based on screen width

## Browser Support

- **Modern Browsers**: Chrome 60+, Firefox 60+, Safari 12+, Edge 79+
- **Features**: WebSocket, Notifications API, File API
- **Fallbacks**: Graceful degradation for older browsers

## Testing

### Test Page
A test page is available at `/test-chat.html` for development testing.

### Manual Testing Checklist
- [ ] Widget appears in bottom-right corner
- [ ] Click to open/close works
- [ ] Keyboard shortcuts work (Ctrl+K, Escape)
- [ ] Responsive design on mobile
- [ ] Message input and display
- [ ] File upload interface
- [ ] Emoji picker functionality
- [ ] Typing indicators
- [ ] Unread badge updates

## Troubleshooting

### Common Issues

1. **Widget not appearing**
   - Check if `chat-disabled` meta tag is present
   - Verify JavaScript console for errors
   - Ensure CSS files are loaded

2. **API connection fails**
   - Check CSRF token configuration
   - Verify API endpoints are accessible
   - Check network tab for failed requests

3. **WebSocket not connecting**
   - Ensure Laravel Echo is configured
   - Check WebSocket server status
   - Verify authentication tokens

### Debug Mode
Enable debug logging:
```javascript
window.chatWidget.debug = true;
```

## Performance Considerations

- **Lazy Loading**: Widget only loads when needed
- **Message Caching**: Recent messages cached in localStorage
- **Debounced Typing**: Typing indicators are debounced to reduce API calls
- **Optimized Animations**: CSS transforms for smooth performance

## Security

- **CSRF Protection**: All API requests include CSRF tokens
- **Input Sanitization**: Messages are escaped to prevent XSS
- **File Validation**: Uploaded files are validated for type and size
- **Rate Limiting**: API endpoints include rate limiting

## Future Enhancements

- Voice message support
- Video chat integration
- Advanced emoji picker
- Message reactions
- Chat history export
- Multi-language support
- Custom themes
- Widget positioning options
