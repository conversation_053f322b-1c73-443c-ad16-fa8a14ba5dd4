# Login History & Security Tracking System Documentation

## Overview

This comprehensive login history and security tracking system provides robust monitoring and analysis of all login attempts across the Laravel application. It tracks both successful and failed login attempts with detailed security metadata for threat analysis.

## Features

### Core Tracking Capabilities
- **Comprehensive Login Tracking**: Records every login attempt (successful and failed)
- **Device Fingerprinting**: Unique device identification across sessions
- **Geolocation Tracking**: IP-based location detection and analysis
- **Security Risk Scoring**: Automated risk assessment for each login attempt
- **Brute Force Detection**: Tracks failed attempts and suspicious patterns
- **Session Management**: Complete session lifecycle tracking

### Security Features
- **VPN/Tor Detection**: Identifies proxy and anonymization services
- **Device Recognition**: Tracks known vs unknown devices
- **Location Analysis**: Monitors login patterns by geographic location
- **Suspicious Activity Alerts**: Automated flagging of high-risk logins
- **IP Blacklist/Whitelist**: Support for IP-based access control

## Database Schema

The system uses a comprehensive `login_histories` table with 40+ fields including:

### Core Fields
- `user_id`: Associated user (nullable for failed attempts with unknown emails)
- `login_status`: 'success' or 'failed'
- `ip_address`: Client IP address
- `device_fingerprint`: Unique device identifier
- `user_agent`: Full browser user agent string

### Location Data
- `location`: Formatted location string
- `latitude`/`longitude`: GPS coordinates
- `geolocation_data`: Full geolocation API response (JSON)

### Security Metadata
- `risk_score`: Calculated risk score (0-100)
- `is_suspicious`: Boolean flag for suspicious activities
- `security_alert`: High-priority security flag
- `is_vpn`/`is_tor`: Proxy detection flags
- `is_device_known`/`is_location_known`: Recognition flags

### Session Tracking
- `session_id`: Laravel session identifier
- `session_started_at`/`session_ended_at`: Session lifecycle
- `session_duration`: Total session time in seconds

## API Endpoints

### Authentication Required Endpoints

#### GET `/api/v1/login-history`
Retrieve paginated login history for authenticated user.

**Query Parameters:**
- `status`: Filter by 'success' or 'failed'
- `days`: Limit to recent days (max 365)
- `suspicious_only`: Show only suspicious activities
- `per_page`: Results per page (default: 15)

**Response:**
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "uuid",
        "status": "success",
        "timestamp": "2025-01-22T10:30:00Z",
        "location": "San Francisco, CA, US",
        "device_type": "desktop",
        "browser": "Chrome 120.0",
        "os": "Windows 11",
        "ip_address": "192.168.***.100",
        "risk_level": "Low",
        "risk_color": "green",
        "is_new_device": false,
        "is_new_location": false,
        "session_duration": "2h 15m 30s",
        "failure_reason": null
      }
    ],
    "current_page": 1,
    "per_page": 15,
    "total": 25
  }
}
```

#### GET `/api/v1/login-history/statistics`
Get comprehensive login statistics.

**Response:**
```json
{
  "success": true,
  "data": {
    "total_logins": 150,
    "successful_logins": 145,
    "failed_logins": 5,
    "success_rate": 96.67,
    "unique_devices": 3,
    "unique_locations": 2,
    "recent_logins": [...],
    "suspicious_activities": 2,
    "period_days": 30
  }
}
```

#### GET `/api/v1/login-history/alerts`
Get security alerts for the user.

#### GET `/api/v1/login-history/{uuid}`
Get detailed information about a specific login attempt.

### Public Endpoints

#### POST `/api/v1/track-login`
Track login attempts from external systems.

**Request Body:**
```json
{
  "user_id": 123,
  "email": "<EMAIL>",
  "status": "success",
  "failure_reason": "Invalid credentials",
  "login_method": "standard",
  "twofa_used": false,
  "twofa_verified": false
}
```

## Service Classes

### LoginHistoryService

Main service class for tracking and analyzing login attempts.

**Key Methods:**
- `trackLoginAttempt()`: Record a login attempt with full metadata
- `recordLogout()`: Track session end and calculate duration
- `getLoginStats()`: Generate comprehensive statistics
- `calculateRiskScore()`: Assess security risk of login attempt

### Integration with Existing Controllers

The system automatically integrates with:
- `LoginController`: Tracks web-based logins
- `Api\AuthController`: Tracks API-based logins
- Activity logging system for additional audit trails

## Risk Scoring Algorithm

The system calculates risk scores (0-100) based on:

- **Failed Login**: +30 points
- **Unknown Device**: +20 points
- **Unknown Location**: +15 points
- **VPN Usage**: +25 points
- **Tor Usage**: +40 points
- **Recent Failed Attempts**: +10 points per attempt (max +30)
- **Suspicious User Agent**: +15 points
- **Unusual Login Time**: +10 points

**Risk Levels:**
- 0-19: Minimal (Green)
- 20-39: Low (Blue)
- 40-59: Medium (Yellow)
- 60-79: High (Orange)
- 80-100: Critical (Red)

## Security Features

### Privacy Protection
- IP addresses are masked in API responses (192.168.***.100)
- Users can only access their own login history
- Sensitive data is sanitized for client consumption

### Brute Force Protection
- Tracks failed attempts per IP address
- Integrates with existing rate limiting
- Escalates risk scores for repeated failures

### Device Recognition
- Generates consistent device fingerprints
- Tracks device usage patterns
- Flags new device logins for review

## Testing

Comprehensive test suite includes:

### Unit Tests (`tests/Feature/LoginHistoryTest.php`)
- Login attempt tracking
- Risk score calculation
- Device fingerprinting
- Session management
- Statistics generation

### API Tests (`tests/Feature/Api/LoginHistoryApiTest.php`)
- Authentication requirements
- Data filtering and pagination
- Privacy protection
- Response formatting

## Installation & Setup

1. **Run Migration:**
   ```bash
   php artisan migrate
   ```

2. **Update Controllers:**
   The system automatically integrates with existing authentication controllers.

3. **Configure Services:**
   The `LoginHistoryService` is automatically registered and injected.

## Configuration

### Geolocation Service
Currently uses ip-api.com for location data. Can be replaced with:
- MaxMind GeoIP2
- IPStack
- IPGeolocation

### Security Detection
Implement custom logic for:
- VPN/Proxy detection services
- IP blacklist/whitelist management
- Custom risk scoring rules

## Usage Examples

### Track Manual Login
```php
$loginHistoryService = app(LoginHistoryService::class);

$loginHistory = $loginHistoryService->trackLoginAttempt(
    $request,
    $user,
    'success'
);
```

### Get User Statistics
```php
$stats = $loginHistoryService->getLoginStats($user, 30);
echo "Success rate: {$stats['success_rate']}%";
```

### Check for Suspicious Activity
```php
$suspiciousLogins = LoginHistory::where('user_id', $user->id)
    ->where('is_suspicious', true)
    ->recent(24)
    ->get();
```

## Security Considerations

1. **Data Retention**: Consider implementing data retention policies
2. **Performance**: Index frequently queried fields
3. **Privacy**: Ensure compliance with data protection regulations
4. **Monitoring**: Set up alerts for high-risk activities
5. **Backup**: Regular backups of security logs

## Future Enhancements

- Machine learning-based anomaly detection
- Real-time security notifications
- Advanced geofencing capabilities
- Integration with SIEM systems
- Mobile app push notifications for security alerts
- Behavioral analysis patterns
- Advanced device fingerprinting techniques

## Support

For questions or issues with the login history system, please refer to the test files for usage examples or contact the development team.
