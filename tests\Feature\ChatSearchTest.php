<?php

namespace Tests\Feature;

use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\ChatParticipant;
use App\Models\User;
use App\Models\Role;
use App\Services\ChatSearchService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ChatSearchTest extends TestCase
{
    use RefreshDatabase;

    protected ChatSearchService $searchService;
    protected User $admin;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->searchService = app(ChatSearchService::class);

        // Create users
        $adminRole = Role::factory()->create(['name' => 'admin', 'slug' => 'admin']);
        $userRole = Role::factory()->create(['name' => 'user', 'slug' => 'user']);
        
        $this->admin = User::factory()->create(['role_id' => $adminRole->id]);
        $this->user = User::factory()->create(['role_id' => $userRole->id]);
    }

    /** @test */
    public function it_can_search_messages_by_content()
    {
        // Create test data
        $room = ChatRoom::factory()->create();
        
        $message1 = ChatMessage::factory()->create([
            'chat_room_id' => $room->id,
            'user_id' => $this->user->id,
            'content' => 'Hello, I need help with my order',
            'message_type' => 'text',
        ]);

        $message2 = ChatMessage::factory()->create([
            'chat_room_id' => $room->id,
            'user_id' => $this->user->id,
            'content' => 'Thank you for your assistance',
            'message_type' => 'text',
        ]);

        $message3 = ChatMessage::factory()->create([
            'chat_room_id' => $room->id,
            'user_id' => $this->user->id,
            'content' => 'Can you help me with billing?',
            'message_type' => 'text',
        ]);

        // Search for messages containing "help"
        $results = $this->searchService->searchMessages(['content' => 'help']);

        $this->assertEquals(2, $results->total());
        $this->assertTrue($results->contains('id', $message1->id));
        $this->assertTrue($results->contains('id', $message3->id));
        $this->assertFalse($results->contains('id', $message2->id));
    }

    /** @test */
    public function it_can_search_rooms_by_type_and_status()
    {
        // Create test data
        $room1 = ChatRoom::factory()->create([
            'type' => 'visitor',
            'status' => 'active',
            'title' => 'Customer Support Chat',
        ]);

        $room2 = ChatRoom::factory()->create([
            'type' => 'support',
            'status' => 'closed',
            'title' => 'Technical Support',
        ]);

        $room3 = ChatRoom::factory()->create([
            'type' => 'visitor',
            'status' => 'active',
            'title' => 'Sales Inquiry',
        ]);

        // Search for active visitor rooms
        $results = $this->searchService->searchRooms([
            'type' => 'visitor',
            'status' => 'active',
        ]);

        $this->assertEquals(2, $results->total());
        $this->assertTrue($results->contains('id', $room1->id));
        $this->assertTrue($results->contains('id', $room3->id));
        $this->assertFalse($results->contains('id', $room2->id));
    }

    /** @test */
    public function it_can_perform_global_search()
    {
        // Create test data
        $room = ChatRoom::factory()->create([
            'title' => 'Support Chat for Order Issues',
        ]);

        $message = ChatMessage::factory()->create([
            'chat_room_id' => $room->id,
            'content' => 'I have an issue with my order',
        ]);

        // Global search for "order"
        $results = $this->searchService->globalSearch('order');

        $this->assertArrayHasKey('messages', $results);
        $this->assertArrayHasKey('rooms', $results);
        $this->assertArrayHasKey('total_results', $results);

        $this->assertEquals(1, $results['messages']->total());
        $this->assertEquals(1, $results['rooms']->total());
        $this->assertEquals(2, $results['total_results']);
    }

    /** @test */
    public function it_can_search_by_participant()
    {
        // Create test data
        $room1 = ChatRoom::factory()->create();
        $room2 = ChatRoom::factory()->create();
        $room3 = ChatRoom::factory()->create();

        // Add user as participant to room1 and room2
        ChatParticipant::factory()->create([
            'chat_room_id' => $room1->id,
            'user_id' => $this->user->id,
        ]);

        ChatParticipant::factory()->create([
            'chat_room_id' => $room2->id,
            'user_id' => $this->user->id,
        ]);

        // Search for rooms where user is a participant
        $results = $this->searchService->searchByParticipant($this->user->id);

        $this->assertEquals(2, $results->total());
        $this->assertTrue($results->contains('id', $room1->id));
        $this->assertTrue($results->contains('id', $room2->id));
        $this->assertFalse($results->contains('id', $room3->id));
    }

    /** @test */
    public function it_can_search_ai_messages()
    {
        // Create test data
        $room = ChatRoom::factory()->create();

        $aiMessage1 = ChatMessage::factory()->create([
            'chat_room_id' => $room->id,
            'content' => 'AI generated response 1',
            'is_ai_generated' => true,
            'ai_confidence' => 0.95,
            'ai_model' => 'gpt-4',
        ]);

        $aiMessage2 = ChatMessage::factory()->create([
            'chat_room_id' => $room->id,
            'content' => 'AI generated response 2',
            'is_ai_generated' => true,
            'ai_confidence' => 0.75,
            'ai_model' => 'gpt-3.5',
        ]);

        $humanMessage = ChatMessage::factory()->create([
            'chat_room_id' => $room->id,
            'content' => 'Human message',
            'is_ai_generated' => false,
        ]);

        // Search for AI messages with high confidence
        $results = $this->searchService->searchAIMessages([
            'min_confidence' => 0.8,
        ]);

        $this->assertEquals(1, $results->total());
        $this->assertTrue($results->contains('id', $aiMessage1->id));
        $this->assertFalse($results->contains('id', $aiMessage2->id));
        $this->assertFalse($results->contains('id', $humanMessage->id));
    }

    /** @test */
    public function it_can_get_search_suggestions()
    {
        // Create test data with common words
        $room = ChatRoom::factory()->create();

        ChatMessage::factory()->create([
            'chat_room_id' => $room->id,
            'content' => 'Hello, I need help with my order',
            'created_at' => now()->subDays(1),
        ]);

        ChatMessage::factory()->create([
            'chat_room_id' => $room->id,
            'content' => 'Can you help me with billing?',
            'created_at' => now()->subDays(2),
        ]);

        ChatMessage::factory()->create([
            'chat_room_id' => $room->id,
            'content' => 'I have a question about my account',
            'created_at' => now()->subDays(3),
        ]);

        // Get suggestions for "hel"
        $suggestions = $this->searchService->getSearchSuggestions('hel');

        $this->assertIsArray($suggestions);
        $this->assertContains('help', $suggestions);
        $this->assertContains('hello', $suggestions);
    }

    /** @test */
    public function it_can_get_search_analytics()
    {
        // Create test data
        $room = ChatRoom::factory()->create(['created_at' => now()->subDays(5)]);

        ChatMessage::factory()->create([
            'chat_room_id' => $room->id,
            'user_id' => $this->user->id,
            'content' => 'Regular message',
            'is_ai_generated' => false,
            'created_at' => now()->subDays(3),
        ]);

        ChatMessage::factory()->create([
            'chat_room_id' => $room->id,
            'user_id' => $this->user->id,
            'content' => 'AI response',
            'is_ai_generated' => true,
            'created_at' => now()->subDays(2),
        ]);

        // Get analytics
        $analytics = $this->searchService->getSearchAnalytics([
            'start_date' => now()->subDays(7),
            'end_date' => now(),
        ]);

        $this->assertArrayHasKey('total_messages', $analytics);
        $this->assertArrayHasKey('total_rooms', $analytics);
        $this->assertArrayHasKey('ai_messages', $analytics);
        $this->assertArrayHasKey('ai_percentage', $analytics);
        $this->assertArrayHasKey('active_users', $analytics);
        $this->assertArrayHasKey('common_words', $analytics);
        $this->assertArrayHasKey('room_types', $analytics);

        $this->assertEquals(2, $analytics['total_messages']);
        $this->assertEquals(1, $analytics['total_rooms']);
        $this->assertEquals(1, $analytics['ai_messages']);
        $this->assertEquals(50.0, $analytics['ai_percentage']);
    }

    /** @test */
    public function admin_can_access_search_endpoints()
    {
        $this->actingAs($this->admin);

        // Create test data
        $room = ChatRoom::factory()->create();
        ChatMessage::factory()->create([
            'chat_room_id' => $room->id,
            'content' => 'Test message for search',
        ]);

        // Test message search endpoint
        $response = $this->postJson(route('admin.chat.search.messages'), [
            'content' => 'test',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'data',
                    'current_page',
                    'total',
                ],
                'message',
            ]);

        // Test room search endpoint
        $response = $this->postJson(route('admin.chat.search.rooms'), [
            'type' => 'visitor',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data',
                'message',
            ]);

        // Test global search endpoint
        $response = $this->postJson(route('admin.chat.search.global'), [
            'query' => 'test',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'messages',
                    'rooms',
                    'total_results',
                ],
                'message',
            ]);
    }

    /** @test */
    public function it_validates_search_parameters()
    {
        $this->actingAs($this->admin);

        // Test invalid message search parameters
        $response = $this->postJson(route('admin.chat.search.messages'), [
            'per_page' => 150, // Exceeds maximum
            'sort_order' => 'invalid', // Invalid sort order
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['per_page', 'sort_order']);

        // Test missing required global search parameter
        $response = $this->postJson(route('admin.chat.search.global'), [
            // Missing 'query' parameter
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['query']);
    }

    /** @test */
    public function it_can_filter_messages_by_date_range()
    {
        // Create test data with different dates
        $room = ChatRoom::factory()->create();

        $oldMessage = ChatMessage::factory()->create([
            'chat_room_id' => $room->id,
            'content' => 'Old message',
            'created_at' => now()->subDays(10),
        ]);

        $recentMessage = ChatMessage::factory()->create([
            'chat_room_id' => $room->id,
            'content' => 'Recent message',
            'created_at' => now()->subDays(2),
        ]);

        // Search for messages in the last 5 days
        $results = $this->searchService->searchMessages([
            'start_date' => now()->subDays(5)->toDateString(),
            'end_date' => now()->toDateString(),
        ]);

        $this->assertEquals(1, $results->total());
        $this->assertTrue($results->contains('id', $recentMessage->id));
        $this->assertFalse($results->contains('id', $oldMessage->id));
    }
}
