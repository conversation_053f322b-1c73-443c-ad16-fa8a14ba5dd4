<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_typing_indicators', function (Blueprint $table) {
            $table->id();
            $table->foreignId('chat_room_id')->constrained('chat_rooms')->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->string('display_name', 100)->nullable()->comment('For anonymous users');
            $table->boolean('is_typing')->default(true);
            $table->timestamp('started_at')->useCurrent();
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();
            
            // Unique constraint to prevent duplicate typing indicators
            $table->unique(['chat_room_id', 'user_id'], 'unique_room_user_typing');
            
            // Performance indexes
            $table->index('expires_at', 'idx_expires_at');
            $table->index(['chat_room_id', 'is_typing'], 'idx_room_typing');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_typing_indicators');
    }
};
