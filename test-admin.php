<?php
// Test admin dashboard
$url = 'http://localhost:8000/admin';
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_NOBODY, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "cURL Error: " . $error . "\n";
} else {
    echo "HTTP Status Code: " . $httpCode . "\n";
    if ($httpCode == 200) {
        echo "✅ Admin dashboard is working correctly!\n";
    } elseif ($httpCode == 302) {
        echo "🔄 Admin dashboard redirected (likely to login page)\n";
    } else {
        echo "❌ Admin dashboard returned error code: " . $httpCode . "\n";
    }
}
?>
