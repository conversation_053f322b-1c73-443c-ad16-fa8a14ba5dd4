@extends('layouts.app')

@section('title', __('errors.400_title', ['default' => 'Bad Request']) . ' - ' . __('common.company_name'))
@section('meta_description', __('errors.400_description', ['default' => 'The request could not be understood by the server.']))

@section('content')
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
        <!-- Error Icon -->
        <div class="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-red-100">
            <svg class="h-16 w-16 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-1.964-1.333-2.732 0L3.732 16c-.77 1.333.192 3 1.732 3z"/>
            </svg>
        </div>

        <!-- Error Code -->
        <div>
            <h1 class="text-6xl font-bold text-gray-900">400</h1>
            <h2 class="mt-2 text-3xl font-extrabold text-gray-900">
                {{ __('errors.400_title', ['default' => 'Bad Request']) }}
            </h2>
            <p class="mt-2 text-base text-gray-600">
                {{ __('errors.400_description', ['default' => 'The request could not be understood by the server due to malformed syntax.']) }}
            </p>
        </div>

        <!-- Actions -->
        <div class="mt-8 space-y-4">
            <a href="{{ route('home', ['locale' => app()->getLocale()]) }}" class="btn-primary inline-flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                </svg>
                {{ __('errors.go_home', ['default' => 'Go to Homepage']) }}
            </a>
            <div>
                <button onclick="history.back()" class="text-primary-600 hover:text-primary-800 font-medium">
                    {{ __('errors.go_back', ['default' => 'Go Back']) }}
                </button>
            </div>
        </div>

        <!-- Contact Support -->
        <div class="mt-8 pt-8 border-t border-gray-200">
            <p class="text-sm text-gray-600">
                {{ __('errors.need_help', ['default' => 'Need help?']) }}
                <a href="{{ route('contact', ['locale' => app()->getLocale()]) }}" class="text-primary-600 hover:text-primary-800 font-medium">
                    {{ __('errors.contact_support', ['default' => 'Contact Support']) }}
                </a>
            </p>
        </div>
    </div>
</div>
@endsection

