<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class UserPreference extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'user_id',
        'language',
        'timezone',
        'currency',
        'date_format',
        'time_format',
        'theme',
        'dashboard_widgets',
        'dashboard_layout',
        'show_welcome_message',
        'auto_refresh_dashboard',
        'auto_refresh_interval',
        'email_notifications',
        'browser_notifications',
        'sms_notifications',
        'notification_types',
        'quiet_hours_start',
        'quiet_hours_end',
        'chat_sound_enabled',
        'chat_desktop_notifications',
        'chat_status',
        'max_concurrent_chats',
        'auto_assign_chats',
        'items_per_page',
        'show_tooltips',
        'compact_mode',
        'table_columns',
        'profile_visible',
        'activity_tracking',
        'analytics_tracking',
        'custom_settings',
    ];

    protected $casts = [
        'dashboard_widgets' => 'array',
        'notification_types' => 'array',
        'table_columns' => 'array',
        'custom_settings' => 'array',
        'show_welcome_message' => 'boolean',
        'auto_refresh_dashboard' => 'boolean',
        'email_notifications' => 'boolean',
        'browser_notifications' => 'boolean',
        'sms_notifications' => 'boolean',
        'chat_sound_enabled' => 'boolean',
        'chat_desktop_notifications' => 'boolean',
        'auto_assign_chats' => 'boolean',
        'show_tooltips' => 'boolean',
        'compact_mode' => 'boolean',
        'profile_visible' => 'boolean',
        'activity_tracking' => 'boolean',
        'analytics_tracking' => 'boolean',
        'auto_refresh_interval' => 'integer',
        'max_concurrent_chats' => 'integer',
        'items_per_page' => 'integer',
        'quiet_hours_start' => 'datetime:H:i',
        'quiet_hours_end' => 'datetime:H:i',
    ];

    protected $attributes = [
        'language' => 'en',
        'timezone' => 'UTC',
        'currency' => 'USD',
        'date_format' => 'Y-m-d',
        'time_format' => 'H:i',
        'theme' => 'light',
        'dashboard_layout' => 'grid',
        'show_welcome_message' => true,
        'auto_refresh_dashboard' => true,
        'auto_refresh_interval' => 30,
        'email_notifications' => true,
        'browser_notifications' => true,
        'sms_notifications' => false,
        'chat_sound_enabled' => true,
        'chat_desktop_notifications' => true,
        'chat_status' => 'available',
        'max_concurrent_chats' => 5,
        'auto_assign_chats' => true,
        'items_per_page' => 25,
        'show_tooltips' => true,
        'compact_mode' => false,
        'profile_visible' => true,
        'activity_tracking' => true,
        'analytics_tracking' => true,
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Get the user that owns the preferences.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get or create preferences for a user.
     */
    public static function getForUser(User $user): self
    {
        return static::firstOrCreate(
            ['user_id' => $user->id],
            ['uuid' => (string) Str::uuid()]
        );
    }

    /**
     * Update a specific preference.
     */
    public function updatePreference(string $key, $value): bool
    {
        if (!in_array($key, $this->fillable)) {
            return false;
        }

        $this->{$key} = $value;
        return $this->save();
    }

    /**
     * Get all preferences as array.
     */
    public function toPreferencesArray(): array
    {
        return $this->only($this->fillable);
    }

    /**
     * Check if user is in quiet hours.
     */
    public function isInQuietHours(): bool
    {
        if (!$this->quiet_hours_start || !$this->quiet_hours_end) {
            return false;
        }

        $now = now($this->timezone)->format('H:i');
        $start = $this->quiet_hours_start->format('H:i');
        $end = $this->quiet_hours_end->format('H:i');

        if ($start <= $end) {
            return $now >= $start && $now <= $end;
        } else {
            // Quiet hours span midnight
            return $now >= $start || $now <= $end;
        }
    }

    /**
     * Get available timezones.
     */
    public static function getAvailableTimezones(): array
    {
        return [
            'UTC' => 'UTC',
            'America/New_York' => 'Eastern Time (US & Canada)',
            'America/Chicago' => 'Central Time (US & Canada)',
            'America/Denver' => 'Mountain Time (US & Canada)',
            'America/Los_Angeles' => 'Pacific Time (US & Canada)',
            'Europe/London' => 'London',
            'Europe/Paris' => 'Paris',
            'Europe/Berlin' => 'Berlin',
            'Africa/Johannesburg' => 'Johannesburg',
            'Africa/Cairo' => 'Cairo',
            'Asia/Tokyo' => 'Tokyo',
            'Asia/Shanghai' => 'Shanghai',
            'Asia/Kolkata' => 'Mumbai',
            'Australia/Sydney' => 'Sydney',
        ];
    }

    /**
     * Get available currencies.
     */
    public static function getAvailableCurrencies(): array
    {
        return [
            'USD' => 'US Dollar ($)',
            'EUR' => 'Euro (€)',
            'GBP' => 'British Pound (£)',
            'ZAR' => 'South African Rand (R)',
            'JPY' => 'Japanese Yen (¥)',
            'CNY' => 'Chinese Yuan (¥)',
            'INR' => 'Indian Rupee (₹)',
            'AUD' => 'Australian Dollar (A$)',
            'CAD' => 'Canadian Dollar (C$)',
        ];
    }

    /**
     * Get available languages.
     */
    public static function getAvailableLanguages(): array
    {
        return [
            'en' => 'English',
            'af' => 'Afrikaans',
            'zu' => 'Zulu',
            'xh' => 'Xhosa',
            'es' => 'Spanish',
            'fr' => 'French',
            'de' => 'German',
            'pt' => 'Portuguese',
            'zh' => 'Chinese',
            'ja' => 'Japanese',
        ];
    }
}
