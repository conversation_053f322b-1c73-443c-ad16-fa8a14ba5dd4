<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_system_settings', function (Blueprint $table) {
            $table->id();
            $table->string('setting_key', 100)->unique();
            $table->text('setting_value');
            $table->enum('setting_type', ['boolean', 'string', 'integer', 'json'])->default('string');
            $table->text('description')->nullable();
            $table->boolean('is_public')->default(false)->comment('Can be accessed by frontend');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            
            // Performance indexes
            $table->index('setting_key', 'idx_setting_key');
            $table->index('is_public', 'idx_is_public');
        });
        
        // Insert default settings
        DB::table('chat_system_settings')->insert([
            [
                'setting_key' => 'chat_enabled',
                'setting_value' => '1',
                'setting_type' => 'boolean',
                'description' => 'Enable or disable the chat system',
                'is_public' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'setting_key' => 'ai_enabled',
                'setting_value' => '1',
                'setting_type' => 'boolean',
                'description' => 'Enable or disable AI chatbot',
                'is_public' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'setting_key' => 'max_concurrent_users',
                'setting_value' => '1000',
                'setting_type' => 'integer',
                'description' => 'Maximum concurrent users allowed',
                'is_public' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_system_settings');
    }
};
