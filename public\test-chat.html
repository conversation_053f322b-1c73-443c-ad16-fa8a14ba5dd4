<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="test-token">
    <title>Chat Widget Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Include chat widget styles inline for testing */
        .modern-chat-widget {
            position: fixed;
            z-index: 9999;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            font-size: 14px;
            line-height: 1.5;
            color: #374151;
        }

        .modern-chat-widget--bottom-right {
            bottom: 20px;
            right: 20px;
        }

        .chat-toggle {
            position: relative;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        .chat-toggle:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }

        .chat-toggle.active {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .chat-toggle-icon {
            position: relative;
            width: 24px;
            height: 24px;
            color: white;
        }

        .chat-toggle-icon svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            transition: all 0.3s ease;
        }

        .chat-toggle .close-icon {
            opacity: 0;
            transform: rotate(90deg);
        }

        .chat-toggle.active .chat-icon {
            opacity: 0;
            transform: rotate(-90deg);
        }

        .chat-toggle.active .close-icon {
            opacity: 1;
            transform: rotate(0deg);
        }

        .chat-window {
            position: absolute;
            bottom: 80px;
            right: 0;
            width: 380px;
            height: 600px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            animation: slideInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            transform-origin: bottom right;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-radius: 16px 16px 0 0;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            scroll-behavior: smooth;
        }

        .welcome-message {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 20px;
            padding: 16px;
            background: #f8fafc;
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }

        .chat-input-area {
            border-top: 1px solid #e5e7eb;
            background: white;
            border-radius: 0 0 16px 16px;
            padding: 16px 20px;
        }

        .chat-input {
            width: 100%;
            border: 1px solid #d1d5db;
            border-radius: 20px;
            padding: 10px 16px;
            font-size: 14px;
            outline: none;
            resize: none;
        }

        .unread-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            min-width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: 600;
            border: 2px solid white;
        }

        .online-indicator {
            position: absolute;
            bottom: 5px;
            right: 5px;
            width: 12px;
            height: 12px;
            background: #10b981;
            border-radius: 50%;
            border: 2px solid white;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .online-indicator.online {
            opacity: 1;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">Chat Widget Test Page</h1>
        
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">Test Instructions</h2>
            <ul class="list-disc list-inside space-y-2 text-gray-600">
                <li>The chat widget should appear in the bottom-right corner</li>
                <li>Click the chat button to open/close the widget</li>
                <li>Try typing a message (it will show an error since API is not connected)</li>
                <li>Use keyboard shortcut Ctrl+K (or Cmd+K) to toggle chat</li>
                <li>Press Escape to close the chat when open</li>
            </ul>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">Widget Features</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-2">
                    <h3 class="font-medium">Visual Features:</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Modern gradient design</li>
                        <li>• Smooth animations</li>
                        <li>• Responsive layout</li>
                        <li>• Unread message badge</li>
                        <li>• Online status indicator</li>
                    </ul>
                </div>
                <div class="space-y-2">
                    <h3 class="font-medium">Functional Features:</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Real-time messaging</li>
                        <li>• File upload support</li>
                        <li>• Emoji picker</li>
                        <li>• Typing indicators</li>
                        <li>• Keyboard shortcuts</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="mt-8 text-center">
            <button onclick="window.openChat && window.openChat()" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg mr-4">
                Open Chat
            </button>
            <button onclick="window.closeChat && window.closeChat()" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg mr-4">
                Close Chat
            </button>
            <button onclick="window.toggleChat && window.toggleChat()" class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg">
                Toggle Chat
            </button>
        </div>
    </div>

    <!-- Mock Laravel configuration -->
    <script>
        window.Laravel = {
            csrfToken: 'test-token'
        };
        
        window.appConfig = {
            routes: {
                chatApi: '/api/v1/chat'
            }
        };
    </script>

    <!-- Include the chat widget script (simplified version for testing) -->
    <script>
        // Simplified version of the chat widget for testing
        class TestChatWidget {
            constructor() {
                this.isOpen = false;
                this.createWidget();
                this.attachEventListeners();
            }

            createWidget() {
                const widgetHTML = `
                    <div id="modern-chat-widget" class="modern-chat-widget modern-chat-widget--bottom-right">
                        <div class="chat-toggle" id="chat-toggle">
                            <div class="chat-toggle-icon">
                                <svg class="chat-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                                </svg>
                                <svg class="close-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="18" y1="6" x2="6" y2="18"></line>
                                    <line x1="6" y1="6" x2="18" y2="18"></line>
                                </svg>
                            </div>
                            <div class="unread-badge" style="display: none;">
                                <span>3</span>
                            </div>
                            <div class="online-indicator online"></div>
                        </div>

                        <div class="chat-window" id="chat-window" style="display: none;">
                            <div class="chat-header">
                                <div>
                                    <div class="font-semibold">Live Support</div>
                                    <div class="text-sm opacity-90">We're here to help!</div>
                                </div>
                            </div>

                            <div class="chat-messages">
                                <div class="welcome-message">
                                    <div class="text-center">
                                        <h4 class="font-semibold mb-2">Welcome to Live Support!</h4>
                                        <p class="text-gray-600">How can we help you today?</p>
                                    </div>
                                </div>
                            </div>

                            <div class="chat-input-area">
                                <textarea class="chat-input" placeholder="Type your message..." rows="1"></textarea>
                            </div>
                        </div>
                    </div>
                `;
                document.body.insertAdjacentHTML('beforeend', widgetHTML);
            }

            attachEventListeners() {
                document.getElementById('chat-toggle').addEventListener('click', () => {
                    this.toggle();
                });

                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape' && this.isOpen) {
                        this.close();
                    }
                    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                        e.preventDefault();
                        this.toggle();
                    }
                });
            }

            toggle() {
                if (this.isOpen) {
                    this.close();
                } else {
                    this.open();
                }
            }

            open() {
                document.getElementById('chat-window').style.display = 'flex';
                document.getElementById('chat-toggle').classList.add('active');
                this.isOpen = true;
            }

            close() {
                document.getElementById('chat-window').style.display = 'none';
                document.getElementById('chat-toggle').classList.remove('active');
                this.isOpen = false;
            }
        }

        // Initialize test widget
        document.addEventListener('DOMContentLoaded', function() {
            window.chatWidget = new TestChatWidget();
            
            window.openChat = () => window.chatWidget.open();
            window.closeChat = () => window.chatWidget.close();
            window.toggleChat = () => window.chatWidget.toggle();
        });
    </script>
</body>
</html>
