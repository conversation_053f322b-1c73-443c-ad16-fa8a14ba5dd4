<?php

namespace App\Console\Commands;

use App\Models\ChatWebhook;
use App\Services\ChatWebhookService;
use App\Events\Chat\WebhookEvent;
use Illuminate\Console\Command;

class TestWebhookSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'chat:test-webhooks';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the chat webhook system';

    /**
     * Execute the console command.
     */
    public function handle(ChatWebhookService $webhookService): int
    {
        $this->info('Testing Chat Webhook System...');

        // Create a test webhook
        $webhook = ChatWebhook::create([
            'name' => 'Test Webhook',
            'url' => 'https://httpbin.org/post', // Test endpoint that accepts POST requests
            'events' => ['message.sent', 'room.created'],
            'is_active' => true,
            'max_retries' => 3,
            'timeout_seconds' => 30,
        ]);

        $this->info("Created test webhook: {$webhook->name} ({$webhook->uuid})");

        // Test webhook endpoint
        $this->info('Testing webhook endpoint...');
        $testResult = $webhookService->testWebhook($webhook);

        if ($testResult['success']) {
            $this->info("✅ Webhook test successful! Status: {$testResult['status_code']}");
        } else {
            $this->error("❌ Webhook test failed: {$testResult['error']}");
        }

        // Trigger a test event
        $this->info('Triggering test event...');
        event(new WebhookEvent('message.sent', [
            'message_id' => 123,
            'room_id' => 456,
            'content' => 'Test message from webhook system',
            'created_at' => now()->toISOString(),
        ]));

        $this->info('✅ Test event triggered');

        // Check delivery statistics
        $stats = $webhookService->getWebhookStats();
        $this->info("Webhook Statistics:");
        $this->info("- Total deliveries: {$stats['total_deliveries']}");
        $this->info("- Successful: {$stats['successful_deliveries']}");
        $this->info("- Failed: {$stats['failed_deliveries']}");
        $this->info("- Success rate: {$stats['success_rate']}%");

        // Clean up test webhook
        $webhook->delete();
        $this->info('Test webhook cleaned up');

        $this->info('✅ Webhook system test completed!');

        return Command::SUCCESS;
    }
}
