<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Order;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use App\Services\DashboardCacheService;
use App\Services\VisitorAnalyticsDashboard;
use App\Services\ActivityLogger;
use App\Services\RecentActivityService;

class DashboardController extends Controller
{
    protected DashboardCacheService $cacheService;
    protected VisitorAnalyticsDashboard $visitorAnalytics;
    protected ActivityLogger $activityLogger;
    protected RecentActivityService $recentActivityService;

    /**
     * Create a new controller instance.
     */
    public function __construct(DashboardCacheService $cacheService, VisitorAnalyticsDashboard $visitorAnalytics, ActivityLogger $activityLogger, RecentActivityService $recentActivityService)
    {
        $this->middleware('auth');
        $this->middleware('role:admin,staff');
        $this->cacheService = $cacheService;
        $this->visitorAnalytics = $visitorAnalytics;
        $this->activityLogger = $activityLogger;
        $this->recentActivityService = $recentActivityService;
    }

    /**
     * Show the admin dashboard.
     */
    public function index(): View
    {
        // Use cache service for optimized data retrieval
        $stats = $this->cacheService->getAdminStats();
        $recent_orders = $this->cacheService->getRecentOrders();
        $low_stock_products = $this->cacheService->getLowStockProducts();
        $recent_activities = $this->recentActivityService->getRecentActivities(10);

        // Get visitor analytics data
        $visitor_stats = $this->visitorAnalytics->getVisitorStats();
        $visitor_chart_data = $this->visitorAnalytics->getVisitorChartData(30);
        $top_pages = $this->visitorAnalytics->getTopPages(10);
        $recent_visitors = $this->visitorAnalytics->getRecentVisitors(10);

        // Get journey and conversion analytics
        $conversion_stats = $this->visitorAnalytics->getConversionStats();
        $lead_quality_stats = $this->visitorAnalytics->getLeadQualityStats();
        $journey_analytics = $this->visitorAnalytics->getJourneyAnalytics();
        $conversion_funnel = $this->visitorAnalytics->getConversionFunnel();
        $high_value_leads = $this->visitorAnalytics->getHighValueLeads(10);

        return view('admin.dashboard.index', compact(
            'stats',
            'recent_orders',
            'low_stock_products',
            'recent_activities',
            'visitor_stats',
            'visitor_chart_data',
            'top_pages',
            'recent_visitors',
            'conversion_stats',
            'lead_quality_stats',
            'journey_analytics',
            'conversion_funnel',
            'high_value_leads'
        ));
    }

    /**
     * Get visitor chart data via AJAX.
     */
    public function getVisitorChartData(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'period' => 'required|in:1,3,5,7,14,30,custom',
                'start_date' => 'required_if:period,custom|date|before_or_equal:end_date',
                'end_date' => 'required_if:period,custom|date|after_or_equal:start_date',
            ]);

            $period = $request->input('period');

            if ($period === 'custom') {
                $startDate = $request->input('start_date');
                $endDate = $request->input('end_date');
                $chartData = $this->visitorAnalytics->getVisitorChartDataByDateRange($startDate, $endDate);
                $title = "Visitor Trends (" . date('M j', strtotime($startDate)) . " - " . date('M j, Y', strtotime($endDate)) . ")";
            } else {
                $days = (int) $period;
                $chartData = $this->visitorAnalytics->getVisitorChartData($days);
                $title = "Visitor Trends (Last {$days} " . ($days === 1 ? 'Day' : 'Days') . ")";
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error loading chart data',
                'error' => $e->getMessage()
            ], 500);
        }

        // Log admin chart data access
        $this->activityLogger->logCustomerActivity(
            'admin_dashboard_chart_data',
            "Admin requested visitor chart data for period: {$period}",
            'success',
            null,
            [
                'period' => $period,
                'start_date' => $period === 'custom' ? ($startDate ?? null) : null,
                'end_date' => $period === 'custom' ? ($endDate ?? null) : null,
                'days' => $period !== 'custom' ? (int) $period : null,
            ],
            [
                'chart_title' => $title,
                'data_points' => count($chartData),
            ]
        );

        return response()->json([
            'success' => true,
            'data' => $chartData,
            'title' => $title,
            'period' => $period,
        ]);
    }

    /**
     * Get top pages data for dashboard filtering.
     */
    public function getTopPages(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'days' => 'sometimes|integer|min:1|max:365',
                'start_date' => 'sometimes|date',
                'end_date' => 'sometimes|date|after_or_equal:start_date',
            ]);

            if ($request->has('start_date') && $request->has('end_date')) {
                $topPages = $this->visitorAnalytics->getTopPagesByDateRange(
                    $request->start_date,
                    $request->end_date,
                    5
                );
                $period = "(" . date('M j', strtotime($request->start_date)) . " - " . date('M j, Y', strtotime($request->end_date)) . ")";
            } else {
                $days = $request->input('days', 7);
                $topPages = $this->visitorAnalytics->getTopPages(5, $days);
                $period = "(Last {$days} " . ($days === 1 ? 'Day' : 'Days') . ")";
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error loading top pages data',
                'error' => $e->getMessage()
            ], 500);
        }

        $html = '';
        if ($topPages && count($topPages) > 0) {
            foreach ($topPages as $page) {
                // Handle both array and object formats
                $pageTitle = is_array($page) ? ($page['page_title'] ?? '') : ($page->page_title ?? '');
                $routeName = is_array($page) ? ($page['route_name'] ?? '') : ($page->route_name ?? '');
                $visits = is_array($page) ? ($page['visits'] ?? 0) : ($page->visits ?? 0);
                $uniqueVisitors = is_array($page) ? ($page['unique_visitors'] ?? 0) : ($page->unique_visitors ?? 0);

                $displayTitle = $pageTitle ?: $routeName;

                $html .= '<div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">';
                $html .= '<div class="flex-1">';
                $html .= '<h4 class="text-sm font-medium text-gray-900">' . e($displayTitle) . '</h4>';
                $html .= '<p class="text-xs text-gray-500">' . e($routeName) . '</p>';
                $html .= '</div>';
                $html .= '<div class="text-right">';
                $html .= '<p class="text-sm font-semibold text-gray-900">' . number_format($visits) . '</p>';
                $html .= '<p class="text-xs text-gray-500">' . number_format($uniqueVisitors) . ' unique</p>';
                $html .= '</div>';
                $html .= '</div>';
            }
        } else {
            $html = '<div class="text-center py-4">';
            $html .= '<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">';
            $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>';
            $html .= '</svg>';
            $html .= '<p class="mt-2 text-sm text-gray-500">No page data available ' . e($period) . '</p>';
            $html .= '</div>';
        }

        return response()->json([
            'success' => true,
            'html' => $html,
            'period' => $period
        ]);
    }
}
