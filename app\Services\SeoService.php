<?php

namespace App\Services;

use Artesaos\SEOTools\Facades\SEOMeta;
use Artesaos\SEOTools\Facades\OpenGraph;
use Artesaos\SEOTools\Facades\TwitterCard;
use Artesaos\SEOTools\Facades\JsonLd;
use Spatie\SchemaOrg\Schema;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;

class SeoService
{
    protected array $jsonLdSchemas = [];
    protected array $breadcrumbs = [];
    protected ?string $canonicalUrl = null;
    protected array $hreflangUrls = [];
    
    public function __construct()
    {
        // Set default values from config
        $this->setDefaults();
    }

    /**
     * Set default SEO values from configuration
     */
    protected function setDefaults(): void
    {
        SEOMeta::setTitle(config('app.name'));
        SEOMeta::setDescription(__('common.company_description'));
        SEOMeta::addKeyword(explode(',', 'digital agency, web development, mobile apps, e-commerce'));
        
        OpenGraph::setTitle(config('app.name'));
        OpenGraph::setDescription(__('common.company_description'));
        OpenGraph::setType('website');
        OpenGraph::setSiteName(config('app.name'));
        OpenGraph::addImage(asset('images/og-image.jpg'));
        
        TwitterCard::setType('summary_large_image');
        TwitterCard::setTitle(config('app.name'));
        TwitterCard::setDescription(__('common.company_description'));
        TwitterCard::setImage(asset('images/twitter-image.jpg'));
    }

    /**
     * Set page title with automatic suffix
     */
    public function setTitle(string $title, bool $appendSiteName = true): self
    {
        $fullTitle = $appendSiteName ? $title . ' - ' . config('app.name') : $title;
        
        SEOMeta::setTitle($fullTitle);
        OpenGraph::setTitle($title);
        TwitterCard::setTitle($title);
        
        return $this;
    }

    /**
     * Set meta description
     */
    public function setDescription(string $description): self
    {
        SEOMeta::setDescription($description);
        OpenGraph::setDescription($description);
        TwitterCard::setDescription($description);
        
        return $this;
    }

    /**
     * Set meta keywords
     */
    public function setKeywords(array|string $keywords): self
    {
        if (is_string($keywords)) {
            $keywords = explode(',', $keywords);
        }
        
        SEOMeta::setKeywords($keywords);
        
        return $this;
    }

    /**
     * Set canonical URL
     */
    public function setCanonical(string $url): self
    {
        $this->canonicalUrl = $url;
        SEOMeta::setCanonical($url);
        OpenGraph::setUrl($url);
        
        return $this;
    }

    /**
     * Add hreflang URLs for multilingual SEO
     */
    public function addHreflang(string $locale, string $url): self
    {
        $this->hreflangUrls[$locale] = $url;
        
        return $this;
    }

    /**
     * Set Open Graph image
     */
    public function setImage(string $imageUrl, array $properties = []): self
    {
        OpenGraph::addImage($imageUrl, $properties);
        TwitterCard::setImage($imageUrl);
        
        return $this;
    }

    /**
     * Add custom meta tag
     */
    public function addMeta(string $name, string $content, string $type = 'name'): self
    {
        SEOMeta::addMeta($name, $content, $type);

        return $this;
    }

    /**
     * Add 2025 SEO best practices meta tags
     */
    public function add2025SeoTags(): self
    {
        // Core Web Vitals and Performance hints
        $this->addMeta('theme-color', '#1e40af'); // Brand color for mobile browsers
        $this->addMeta('color-scheme', 'light dark'); // Support for dark mode

        // Mobile-first indexing optimization
        $this->addMeta('viewport', 'width=device-width, initial-scale=1, viewport-fit=cover');
        $this->addMeta('mobile-web-app-capable', 'yes');
        $this->addMeta('apple-mobile-web-app-capable', 'yes');
        $this->addMeta('apple-mobile-web-app-status-bar-style', 'default');

        // Security and privacy
        $this->addMeta('referrer', 'strict-origin-when-cross-origin');

        // AI and search engine optimization
        $this->addMeta('robots', 'index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1');

        return $this;
    }

    /**
     * Add E-A-T (Expertise, Authoritativeness, Trustworthiness) signals
     */
    public function addEATSignals(array $data = []): self
    {
        // Add author information for E-A-T
        if (isset($data['author'])) {
            $this->addMeta('author', $data['author']);
            $this->addMeta('article:author', $data['author']);
        }

        // Add publication date for freshness signals
        if (isset($data['published_time'])) {
            $this->addMeta('article:published_time', $data['published_time']);
            OpenGraph::addProperty('article:published_time', $data['published_time']);
        }

        // Add modified date for content freshness
        if (isset($data['modified_time'])) {
            $this->addMeta('article:modified_time', $data['modified_time']);
            OpenGraph::addProperty('article:modified_time', $data['modified_time']);
        }

        // Add expertise indicators
        if (isset($data['expertise_level'])) {
            $this->addMeta('expertise-level', $data['expertise_level']);
        }

        return $this;
    }

    /**
     * Add accessibility and inclusive design meta tags
     */
    public function addAccessibilityTags(): self
    {
        // Screen reader optimization
        $this->addMeta('screen-reader-optimized', 'true');

        // Language and locale information
        $this->addMeta('content-language', app()->getLocale());

        return $this;
    }

    /**
     * Add JSON-LD schema
     */
    public function addSchema($schema): self
    {
        if (is_object($schema) && method_exists($schema, 'toArray')) {
            $this->jsonLdSchemas[] = $schema->toArray();
        } elseif (is_array($schema)) {
            $this->jsonLdSchemas[] = $schema;
        }
        
        return $this;
    }

    /**
     * Add organization schema
     */
    public function addOrganizationSchema(array $data = []): self
    {
        $defaultData = [
            'name' => config('app.name'),
            'url' => url('/'),
            'logo' => asset('images/logo.png'),
            'description' => __('common.company_description'),
            'address' => [
                '@type' => 'PostalAddress',
                'addressCountry' => 'ZA',
                'addressRegion' => 'South Africa'
            ],
            'sameAs' => [
                'https://facebook.com/chisolution',
                'https://twitter.com/chisolution',
                'https://linkedin.com/company/chisolution'
            ]
        ];

        $schema = Schema::organization()
            ->name($data['name'] ?? $defaultData['name'])
            ->url($data['url'] ?? $defaultData['url'])
            ->logo($data['logo'] ?? $defaultData['logo'])
            ->description($data['description'] ?? $defaultData['description']);

        if (isset($data['address']) || isset($defaultData['address'])) {
            $address = $data['address'] ?? $defaultData['address'];
            $schema->address(
                Schema::postalAddress()
                    ->addressCountry($address['addressCountry'] ?? 'ZA')
                    ->addressRegion($address['addressRegion'] ?? 'South Africa')
            );
        }

        if (isset($data['sameAs']) || isset($defaultData['sameAs'])) {
            $schema->sameAs($data['sameAs'] ?? $defaultData['sameAs']);
        }

        $this->addSchema($schema);

        return $this;
    }

    /**
     * Add LocalBusiness schema for local SEO
     */
    public function addLocalBusinessSchema(array $data = []): self
    {
        $defaultData = [
            'name' => config('app.name'),
            'description' => __('common.company_description'),
            'url' => url('/'),
            'telephone' => '+27-11-123-4567', // Default phone
            'email' => '<EMAIL>',
            'address' => [
                'streetAddress' => 'Cape Town',
                'addressLocality' => 'Cape Town',
                'addressRegion' => 'Western Cape',
                'postalCode' => '8000',
                'addressCountry' => 'ZA'
            ],
            'geo' => [
                'latitude' => -33.9249,
                'longitude' => 18.4241
            ],
            'openingHours' => ['Mo-Fr 09:00-17:00'],
            'priceRange' => '$$',
            'areaServed' => 'South Africa'
        ];

        $mergedData = array_merge($defaultData, $data);

        $schema = Schema::localBusiness()
            ->name($mergedData['name'])
            ->description($mergedData['description'])
            ->url($mergedData['url'])
            ->telephone($mergedData['telephone'])
            ->email($mergedData['email'])
            ->address(
                Schema::postalAddress()
                    ->streetAddress($mergedData['address']['streetAddress'])
                    ->addressLocality($mergedData['address']['addressLocality'])
                    ->addressRegion($mergedData['address']['addressRegion'])
                    ->postalCode($mergedData['address']['postalCode'])
                    ->addressCountry($mergedData['address']['addressCountry'])
            )
            ->geo(
                Schema::geoCoordinates()
                    ->latitude($mergedData['geo']['latitude'])
                    ->longitude($mergedData['geo']['longitude'])
            )
            ->openingHours($mergedData['openingHours'])
            ->priceRange($mergedData['priceRange'])
            ->areaServed($mergedData['areaServed']);

        $this->addSchema($schema);

        return $this;
    }

    /**
     * Add Service schema for service pages
     */
    public function addServiceSchema(array $data = []): self
    {
        $defaultData = [
            'name' => 'Digital Services',
            'description' => 'Professional digital services including web development, mobile apps, and digital marketing',
            'provider' => config('app.name'),
            'areaServed' => 'South Africa',
            'serviceType' => 'Digital Services'
        ];

        $mergedData = array_merge($defaultData, $data);

        $schema = Schema::service()
            ->name($mergedData['name'])
            ->description($mergedData['description'])
            ->provider(
                Schema::organization()->name($mergedData['provider'])
            )
            ->areaServed($mergedData['areaServed'])
            ->serviceType($mergedData['serviceType']);

        $this->addSchema($schema);

        return $this;
    }

    /**
     * Add FAQ schema for better search visibility
     */
    public function addFAQSchema(array $faqs = []): self
    {
        if (empty($faqs)) {
            return $this;
        }

        $faqItems = [];
        foreach ($faqs as $faq) {
            $faqItems[] = Schema::question()
                ->name($faq['question'])
                ->acceptedAnswer(
                    Schema::answer()->text($faq['answer'])
                );
        }

        $schema = Schema::faqPage()->mainEntity($faqItems);
        $this->addSchema($schema);

        return $this;
    }

    /**
     * Add BreadcrumbList schema
     */
    public function addBreadcrumbSchema(array $breadcrumbs = []): self
    {
        if (empty($breadcrumbs)) {
            return $this;
        }

        $listItems = [];
        foreach ($breadcrumbs as $index => $breadcrumb) {
            $listItems[] = Schema::listItem()
                ->position($index + 1)
                ->name($breadcrumb['name'])
                ->item($breadcrumb['url']);
        }

        $schema = Schema::breadcrumbList()->itemListElement($listItems);
        $this->addSchema($schema);

        return $this;
    }

    /**
     * Add breadcrumb schema
     */
    public function addBreadcrumbs(array $breadcrumbs): self
    {
        $this->breadcrumbs = $breadcrumbs;
        
        $listItems = [];
        foreach ($breadcrumbs as $index => $breadcrumb) {
            $listItems[] = Schema::listItem()
                ->position($index + 1)
                ->name($breadcrumb['name'])
                ->item($breadcrumb['url'] ?? null);
        }

        $schema = Schema::breadcrumbList()->itemListElement($listItems);
        $this->addSchema($schema);
        
        return $this;
    }

    /**
     * Generate all SEO tags
     */
    public function render(): string
    {
        $output = [];
        
        // Basic meta tags
        $output[] = SEOMeta::generate();
        $output[] = OpenGraph::generate();
        $output[] = TwitterCard::generate();
        
        // Canonical URL
        if ($this->canonicalUrl) {
            $output[] = '<link rel="canonical" href="' . $this->canonicalUrl . '">';
        }
        
        // Hreflang tags
        foreach ($this->hreflangUrls as $locale => $url) {
            $output[] = '<link rel="alternate" hreflang="' . $locale . '" href="' . $url . '">';
        }
        
        // JSON-LD schemas
        if (!empty($this->jsonLdSchemas)) {
            foreach ($this->jsonLdSchemas as $schema) {
                $output[] = '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . '</script>';
            }
        }
        
        return implode("\n", $output);
    }

    /**
     * Get structured data for validation
     */
    public function getStructuredData(): array
    {
        return $this->jsonLdSchemas;
    }

    /**
     * Cache SEO data for performance
     */
    public function cache(string $key, int $minutes = 60): self
    {
        Cache::put("seo.{$key}", $this->jsonLdSchemas, now()->addMinutes($minutes));
        
        return $this;
    }

    /**
     * Load cached SEO data
     */
    public function loadFromCache(string $key): self
    {
        $cached = Cache::get("seo.{$key}");
        if ($cached) {
            $this->jsonLdSchemas = array_merge($this->jsonLdSchemas, $cached);
        }
        
        return $this;
    }
}
