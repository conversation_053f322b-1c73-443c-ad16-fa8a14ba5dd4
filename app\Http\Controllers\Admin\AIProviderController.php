<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\AI\AIProviderManager;
use App\Services\ChatAIService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

class AIProviderController extends Controller
{
    public function __construct(
        protected AIProviderManager $aiProviderManager,
        protected ChatAIService $chatAIService
    ) {
        $this->middleware(['auth', 'role:admin']);
    }

    /**
     * Display AI provider management interface.
     */
    public function index(): View
    {
        $providers = $this->chatAIService->getAvailableProvidersAndModels();
        $usageStats = $this->chatAIService->getAIUsageStats();
        $currentProvider = config('ai-providers.default');

        return view('admin.ai-providers.index', compact('providers', 'usageStats', 'currentProvider'));
    }

    /**
     * Get available providers and models.
     */
    public function getProviders(): JsonResponse
    {
        try {
            $providers = $this->chatAIService->getAvailableProvidersAndModels();
            
            return response()->json([
                'success' => true,
                'data' => $providers,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch providers: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get models for a specific provider.
     */
    public function getModels(string $provider): JsonResponse
    {
        try {
            $models = $this->aiProviderManager->getAvailableModels($provider);
            
            return response()->json([
                'success' => true,
                'data' => $models,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch models: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get models by tier.
     */
    public function getModelsByTier(string $tier): JsonResponse
    {
        try {
            $models = $this->aiProviderManager->getModelsByTier($tier);
            
            return response()->json([
                'success' => true,
                'data' => $models,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch models by tier: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Test AI provider connection.
     */
    public function testProvider(Request $request): JsonResponse
    {
        $request->validate([
            'provider' => 'required|string',
            'model' => 'nullable|string',
            'message' => 'string|max:500',
        ]);

        $provider = $request->input('provider');
        $model = $request->input('model');
        $message = $request->input('message', 'Hello, this is a test message.');

        try {
            $startTime = microtime(true);
            
            $response = $this->aiProviderManager->generateResponse(
                $message,
                ['temperature' => 0.7, 'max_tokens' => 100],
                $model,
                $provider
            );

            $responseTime = round((microtime(true) - $startTime) * 1000, 2);

            return response()->json([
                'success' => true,
                'data' => [
                    'response' => $response['content'],
                    'provider' => $response['provider'],
                    'model' => $response['model'],
                    'response_time_ms' => $responseTime,
                    'tokens_used' => $response['usage']['total_tokens'] ?? 0,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Provider test failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get cost estimate for a request.
     */
    public function getCostEstimate(Request $request): JsonResponse
    {
        $request->validate([
            'provider' => 'required|string',
            'model' => 'nullable|string',
            'message' => 'required|string|max:10000',
        ]);

        try {
            $provider = $this->aiProviderManager->provider($request->input('provider'));
            $estimate = $provider->estimateCost(
                $request->input('message'),
                [],
                $request->input('model')
            );

            return response()->json([
                'success' => true,
                'data' => $estimate,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to estimate cost: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get usage statistics.
     */
    public function getUsageStats(): JsonResponse
    {
        try {
            $stats = $this->chatAIService->getAIUsageStats();
            
            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch usage stats: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Reset usage statistics.
     */
    public function resetUsageStats(Request $request): JsonResponse
    {
        $request->validate([
            'provider' => 'nullable|string',
        ]);

        try {
            $this->aiProviderManager->resetUsageStats($request->input('provider'));
            
            return response()->json([
                'success' => true,
                'message' => 'Usage statistics reset successfully.',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reset usage stats: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Test sentiment analysis.
     */
    public function testSentiment(Request $request): JsonResponse
    {
        $request->validate([
            'text' => 'required|string|max:1000',
            'provider' => 'nullable|string',
            'model' => 'nullable|string',
        ]);

        try {
            $result = $this->chatAIService->analyzeSentimentWithAI(
                $request->input('text'),
                $request->input('provider'),
                $request->input('model')
            );

            return response()->json([
                'success' => true,
                'data' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Sentiment analysis failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Test translation.
     */
    public function testTranslation(Request $request): JsonResponse
    {
        $request->validate([
            'text' => 'required|string|max:1000',
            'target_language' => 'required|string|max:10',
            'source_language' => 'nullable|string|max:10',
            'provider' => 'nullable|string',
            'model' => 'nullable|string',
        ]);

        try {
            $result = $this->chatAIService->translateWithAI(
                $request->input('text'),
                $request->input('target_language'),
                $request->input('source_language'),
                $request->input('provider'),
                $request->input('model')
            );

            return response()->json([
                'success' => true,
                'data' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Translation failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Compare providers performance.
     */
    public function compareProviders(Request $request): JsonResponse
    {
        $request->validate([
            'message' => 'required|string|max:500',
            'providers' => 'required|array|min:2|max:4',
            'providers.*' => 'string',
        ]);

        $message = $request->input('message');
        $providers = $request->input('providers');
        $results = [];

        foreach ($providers as $provider) {
            try {
                $startTime = microtime(true);
                
                $response = $this->aiProviderManager->generateResponse(
                    $message,
                    ['temperature' => 0.7, 'max_tokens' => 200],
                    null,
                    $provider
                );

                $responseTime = round((microtime(true) - $startTime) * 1000, 2);

                $results[$provider] = [
                    'success' => true,
                    'response' => $response['content'],
                    'model' => $response['model'],
                    'response_time_ms' => $responseTime,
                    'tokens_used' => $response['usage']['total_tokens'] ?? 0,
                ];
            } catch (\Exception $e) {
                $results[$provider] = [
                    'success' => false,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return response()->json([
            'success' => true,
            'data' => $results,
        ]);
    }
}
