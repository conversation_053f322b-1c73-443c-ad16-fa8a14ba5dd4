<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Services\SeoService;

class SeoTest extends TestCase
{
    /**
     * Test that homepage has proper SEO meta tags
     */
    public function test_homepage_has_seo_meta_tags(): void
    {
        $response = $this->get('/');

        $response->assertStatus(200);

        // Check for essential meta tags
        $response->assertSee('<title>', false);
        $response->assertSee('<meta name="description"', false);
        $response->assertSee('<meta property="og:title"', false);
        $response->assertSee('<meta property="og:description"', false);
        $response->assertSee('<meta name="twitter:card"', false);
    }

    /**
     * Test that about page has proper schema markup
     */
    public function test_about_page_has_organization_schema(): void
    {
        $response = $this->get('/about');

        $response->assertStatus(200);

        // Check for JSON-LD schema
        $response->assertSee('application/ld+json', false);
        $response->assertSee('"@type": "Organization"', false);
        $response->assertSee('"@context": "https://schema.org"', false);
    }

    /**
     * Test that service pages have proper schema markup
     */
    public function test_service_pages_have_service_schema(): void
    {
        $response = $this->get('/services/web-development');

        $response->assertStatus(200);

        // Check for Service schema
        $response->assertSee('application/ld+json', false);
        $response->assertSee('"@type": "Service"', false);
    }

    /**
     * Test that FAQ schema is present on service pages
     */
    public function test_service_pages_have_faq_schema(): void
    {
        $response = $this->get('/services/web-development');

        $response->assertStatus(200);

        // Check for FAQ schema
        $response->assertSee('"@type": "FAQPage"', false);
        $response->assertSee('"@type": "Question"', false);
        $response->assertSee('"acceptedAnswer"', false);
    }

    /**
     * Test sitemap generation
     */
    public function test_sitemap_generation(): void
    {
        // Generate sitemap
        $this->artisan('seo:generate-sitemap')
            ->expectsOutput('🗺️ Generating comprehensive sitemap...')
            ->assertExitCode(0);

        // Check if sitemap file exists
        $this->assertFileExists(public_path('sitemap.xml'));

        // Check sitemap content
        $sitemapContent = file_get_contents(public_path('sitemap.xml'));
        $this->assertStringContainsString('<?xml version="1.0" encoding="UTF-8"?>', $sitemapContent);
        $this->assertStringContainsString('<urlset', $sitemapContent);
        $this->assertStringContainsString('<url>', $sitemapContent);
    }

    /**
     * Test robots.txt accessibility
     */
    public function test_robots_txt_exists_and_valid(): void
    {
        $response = $this->get('/robots.txt');

        $response->assertStatus(200);
        $response->assertSee('User-agent: *');
        $response->assertSee('Sitemap:');
    }

    /**
     * Test SEO service functionality
     */
    public function test_seo_service_functionality(): void
    {
        $seoService = new SeoService();

        $seoService->setTitle('Test Title')
                   ->setDescription('Test Description')
                   ->setKeywords(['test', 'seo', 'keywords']);

        $output = $seoService->render();

        $this->assertStringContainsString('Test Title', $output);
        $this->assertStringContainsString('Test Description', $output);
        $this->assertStringContainsString('<meta', $output);
    }

    /**
     * Test schema markup generation
     */
    public function test_schema_markup_generation(): void
    {
        $seoService = new SeoService();

        $seoService->addOrganizationSchema([
            'name' => 'Test Company',
            'url' => 'https://example.com'
        ]);

        $schemas = $seoService->getStructuredData();

        $this->assertNotEmpty($schemas);
        $this->assertArrayHasKey(0, $schemas);
        $this->assertEquals('Organization', $schemas[0]['@type']);
    }

    /**
     * Test breadcrumb schema
     */
    public function test_breadcrumb_schema_generation(): void
    {
        $seoService = new SeoService();

        $breadcrumbs = [
            ['name' => 'Home', 'url' => '/'],
            ['name' => 'Services', 'url' => '/services'],
            ['name' => 'Web Development', 'url' => '/services/web-development']
        ];

        $seoService->addBreadcrumbs($breadcrumbs);

        $schemas = $seoService->getStructuredData();

        $this->assertNotEmpty($schemas);
        $this->assertEquals('BreadcrumbList', $schemas[0]['@type']);
    }

    /**
     * Test canonical URL setting
     */
    public function test_canonical_url_setting(): void
    {
        $seoService = new SeoService();

        $seoService->setCanonical('https://example.com/test');

        $output = $seoService->render();

        $this->assertStringContainsString('<link rel="canonical" href="https://example.com/test">', $output);
    }
}
