@extends('layouts.dashboard')

@section('title', 'Chat Rooms Management')

@section('content')
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Chat Rooms Management</h1>
            <p class="text-gray-600 mt-2">Manage and monitor all chat conversations</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('admin.chat.moderation.index') }}" class="btn btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                Live Dashboard
            </a>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Rooms</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $stats['total'] }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $stats['active'] }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Waiting</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $stats['waiting'] }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-gray-100 rounded-lg">
                    <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Closed</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $stats['closed'] }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6 mb-8">
        <form method="GET" action="{{ route('admin.chat.rooms.index') }}" class="flex flex-wrap gap-4">
            <div class="flex-1 min-w-48">
                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select name="status" id="status" class="form-select w-full">
                    <option value="">All Statuses</option>
                    <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                    <option value="waiting" {{ request('status') === 'waiting' ? 'selected' : '' }}>Waiting</option>
                    <option value="closed" {{ request('status') === 'closed' ? 'selected' : '' }}>Closed</option>
                    <option value="resolved" {{ request('status') === 'resolved' ? 'selected' : '' }}>Resolved</option>
                </select>
            </div>

            <div class="flex-1 min-w-48">
                <label for="assigned_to" class="block text-sm font-medium text-gray-700 mb-2">Assignment</label>
                <select name="assigned_to" id="assigned_to" class="form-select w-full">
                    <option value="">All Assignments</option>
                    <option value="me" {{ request('assigned_to') === 'me' ? 'selected' : '' }}>Assigned to Me</option>
                    <option value="unassigned" {{ request('assigned_to') === 'unassigned' ? 'selected' : '' }}>Unassigned</option>
                </select>
            </div>

            <div class="flex-1 min-w-48">
                <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">Min Priority</label>
                <select name="priority" id="priority" class="form-select w-full">
                    <option value="">All Priorities</option>
                    <option value="1" {{ request('priority') === '1' ? 'selected' : '' }}>1 (Low)</option>
                    <option value="2" {{ request('priority') === '2' ? 'selected' : '' }}>2</option>
                    <option value="3" {{ request('priority') === '3' ? 'selected' : '' }}>3 (Medium)</option>
                    <option value="4" {{ request('priority') === '4' ? 'selected' : '' }}>4</option>
                    <option value="5" {{ request('priority') === '5' ? 'selected' : '' }}>5 (High)</option>
                </select>
            </div>

            <div class="flex items-end">
                <button type="submit" class="btn btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Filter
                </button>
            </div>
        </form>
    </div>

    <!-- Chat Rooms List -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100">
        <div class="px-6 py-4 border-b border-neutral-200">
            <h3 class="text-lg font-semibold text-gray-900">Chat Rooms</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Room</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Participants</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned To</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Message</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($chatRooms as $room)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ $room->title ?? 'Chat #' . $room->id }}
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        Created {{ $room->created_at->diffForHumans() }}
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                    {{ $room->status === 'active' ? 'bg-green-100 text-green-800' : '' }}
                                    {{ $room->status === 'waiting' ? 'bg-yellow-100 text-yellow-800' : '' }}
                                    {{ $room->status === 'closed' ? 'bg-gray-100 text-gray-800' : '' }}
                                    {{ $room->status === 'resolved' ? 'bg-blue-100 text-blue-800' : '' }}">
                                    {{ ucfirst($room->status) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    @for($i = 1; $i <= 5; $i++)
                                        <svg class="w-4 h-4 {{ $i <= $room->priority ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                    @endfor
                                    <span class="ml-2 text-sm text-gray-600">{{ $room->priority }}/5</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $room->participants->count() }} participants
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                @if($room->currentAssignment)
                                    {{ $room->currentAssignment->assignedStaff->name }}
                                @else
                                    <span class="text-gray-500">Unassigned</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                @if($room->messages->isNotEmpty())
                                    {{ $room->messages->first()->created_at->diffForHumans() }}
                                @else
                                    No messages
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="{{ route('admin.chat.rooms.show', $room) }}" class="text-blue-600 hover:text-blue-900 mr-3">
                                    View
                                </a>
                                @if($room->status !== 'closed')
                                    <button onclick="assignRoom({{ $room->id }})" class="text-green-600 hover:text-green-900 mr-3">
                                        Assign
                                    </button>
                                    <button onclick="closeRoom({{ $room->id }})" class="text-red-600 hover:text-red-900">
                                        Close
                                    </button>
                                @endif
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                    </svg>
                                    <p class="text-lg font-medium">No chat rooms found</p>
                                    <p class="text-sm">Try adjusting your filters or check back later.</p>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        @if($chatRooms->hasPages())
            <div class="px-6 py-4 border-t border-neutral-200">
                {{ $chatRooms->links() }}
            </div>
        @endif
    </div>
</div>

<!-- Assignment Modal -->
<div id="assignmentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Assign Chat Room</h3>
            </div>
            <form id="assignmentForm">
                <div class="px-6 py-4">
                    <input type="hidden" id="assign-room-id" name="room_id">
                    <label for="staff_id" class="block text-sm font-medium text-gray-700 mb-2">Assign to Staff Member</label>
                    <select name="staff_id" id="staff_id" class="form-select w-full" required>
                        <option value="">Select staff member...</option>
                        <!-- Staff options will be loaded dynamically -->
                    </select>
                </div>
                <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                    <button type="button" onclick="closeAssignmentModal()" class="btn btn-secondary">Cancel</button>
                    <button type="submit" class="btn btn-primary">Assign</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function assignRoom(roomId) {
    document.getElementById('assign-room-id').value = roomId;
    document.getElementById('assignmentModal').classList.remove('hidden');
    
    // Load staff members
    loadStaffMembers();
}

function closeAssignmentModal() {
    document.getElementById('assignmentModal').classList.add('hidden');
}

function closeRoom(roomId) {
    if (confirm('Are you sure you want to close this chat room?')) {
        fetch(`/admin/chat/rooms/${roomId}/close`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to close chat room: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while closing the chat room.');
        });
    }
}

function loadStaffMembers() {
    // This would typically load from an API endpoint
    // For now, we'll use a placeholder
    const staffSelect = document.getElementById('staff_id');
    staffSelect.innerHTML = '<option value="">Loading...</option>';
    
    // You can implement an API endpoint to load staff members
    // fetch('/admin/staff/list')...
}

document.getElementById('assignmentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const roomId = formData.get('room_id');
    
    fetch(`/admin/chat/rooms/${roomId}/assign`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            staff_id: formData.get('staff_id')
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeAssignmentModal();
            location.reload();
        } else {
            alert('Failed to assign chat room: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while assigning the chat room.');
    });
});
</script>
@endsection
