@extends('layouts.app')

@section('title', __('errors.401_title', ['default' => 'Unauthorized']) . ' - ' . __('common.company_name'))
@section('meta_description', __('errors.401_description', ['default' => 'Authentication is required to access this resource.']))

@section('content')
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
        <!-- Error Icon -->
        <div class="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-yellow-100">
            <svg class="h-16 w-16 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
            </svg>
        </div>

        <!-- Error Code -->
        <div>
            <h1 class="text-6xl font-bold text-gray-900">401</h1>
            <h2 class="mt-2 text-3xl font-extrabold text-gray-900">
                {{ __('errors.401_title', ['default' => 'Unauthorized']) }}
            </h2>
            <p class="mt-2 text-base text-gray-600">
                {{ __('errors.401_description', ['default' => 'You need to be logged in to access this page.']) }}
            </p>
        </div>

        <!-- Actions -->
        <div class="mt-8 space-y-4">
            <a href="{{ route('login', ['locale' => app()->getLocale()]) }}" class="btn-primary inline-flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"/>
                </svg>
                {{ __('errors.login', ['default' => 'Login']) }}
            </a>
            <div>
                <a href="{{ route('home', ['locale' => app()->getLocale()]) }}" class="text-primary-600 hover:text-primary-800 font-medium">
                    {{ __('errors.go_home', ['default' => 'Go to Homepage']) }}
                </a>
            </div>
        </div>

        <!-- Contact Support -->
        <div class="mt-8 pt-8 border-t border-gray-200">
            <p class="text-sm text-gray-600">
                {{ __('errors.need_help', ['default' => 'Need help?']) }}
                <a href="{{ route('contact', ['locale' => app()->getLocale()]) }}" class="text-primary-600 hover:text-primary-800 font-medium">
                    {{ __('errors.contact_support', ['default' => 'Contact Support']) }}
                </a>
            </p>
        </div>
    </div>
</div>
@endsection

