<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscriber_tags', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->string('color')->default('#3B82F6'); // Hex color for UI
            $table->boolean('is_active')->default(true);
            $table->unsignedInteger('subscriber_count')->default(0);
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();

            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->index(['is_active', 'name']);
        });

        // Pivot table for many-to-many relationship
        Schema::create('newsletter_subscription_tags', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('newsletter_subscription_id');
            $table->unsignedBigInteger('subscriber_tag_id');
            $table->timestamp('tagged_at')->useCurrent();
            $table->unsignedBigInteger('tagged_by')->nullable(); // Admin who added the tag

            $table->foreign('newsletter_subscription_id', 'ns_tag_subscription_fk')
                  ->references('id')->on('newsletter_subscriptions')->onDelete('cascade');
            $table->foreign('subscriber_tag_id', 'ns_tag_tag_fk')
                  ->references('id')->on('subscriber_tags')->onDelete('cascade');
            $table->foreign('tagged_by')->references('id')->on('users')->onDelete('set null');

            $table->unique(['newsletter_subscription_id', 'subscriber_tag_id'], 'subscription_tag_unique');
            $table->index('tagged_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('newsletter_subscription_tags');
        Schema::dropIfExists('subscriber_tags');
    }
};
