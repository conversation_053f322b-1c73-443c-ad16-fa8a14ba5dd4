<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_campaigns', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->string('type')->default('newsletter'); // newsletter, promotional, drip, automated
            $table->string('status')->default('draft'); // draft, scheduled, sending, sent, paused, cancelled

            // Template and content
            $table->unsignedBigInteger('email_template_id')->nullable();
            $table->string('subject');
            $table->string('from_name')->nullable();
            $table->string('from_email')->nullable();
            $table->string('reply_to')->nullable();

            // Scheduling
            $table->timestamp('scheduled_at')->nullable();
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->boolean('is_recurring')->default(false);
            $table->string('recurring_frequency')->nullable(); // daily, weekly, monthly
            $table->json('recurring_settings')->nullable();

            // Targeting and segmentation
            $table->json('target_segments')->nullable(); // Subscriber segments/tags
            $table->json('target_criteria')->nullable(); // Additional targeting criteria
            $table->boolean('send_to_all')->default(false);

            // Analytics and tracking
            $table->boolean('track_opens')->default(true);
            $table->boolean('track_clicks')->default(true);
            $table->unsignedInteger('total_recipients')->default(0);
            $table->unsignedInteger('emails_sent')->default(0);
            $table->unsignedInteger('emails_delivered')->default(0);
            $table->unsignedInteger('emails_bounced')->default(0);
            $table->unsignedInteger('emails_opened')->default(0);
            $table->unsignedInteger('emails_clicked')->default(0);
            $table->unsignedInteger('unsubscribes')->default(0);

            // Drip campaign settings
            $table->unsignedBigInteger('parent_campaign_id')->nullable(); // For drip sequences
            $table->unsignedInteger('sequence_order')->nullable();
            $table->unsignedInteger('delay_days')->nullable(); // Days after previous email
            $table->json('trigger_conditions')->nullable(); // Conditions for automated sending

            // User tracking
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();

            $table->foreign('email_template_id')->references('id')->on('email_templates')->onDelete('set null');
            $table->foreign('parent_campaign_id')->references('id')->on('email_campaigns')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');

            $table->index(['type', 'status']);
            $table->index(['status', 'scheduled_at']);
            $table->index(['parent_campaign_id', 'sequence_order']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_campaigns');
    }
};
