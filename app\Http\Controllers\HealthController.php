<?php

namespace App\Http\Controllers;

use App\Services\HealthCheckService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class HealthController extends Controller
{
    public function __construct(
        protected HealthCheckService $healthCheckService
    ) {
        //
    }

    /**
     * Basic health check endpoint.
     */
    public function health(): JsonResponse
    {
        return response()->json([
            'status' => 'healthy',
            'timestamp' => now()->toISOString(),
            'service' => 'ChiSolution Chat API',
            'version' => config('app.version', '1.0.0'),
        ]);
    }

    /**
     * Comprehensive health check with all system components.
     */
    public function detailed(): JsonResponse
    {
        $healthCheck = $this->healthCheckService->performHealthCheck();
        
        $statusCode = $healthCheck['status'] === 'healthy' ? 200 : 503;
        
        return response()->json($healthCheck, $statusCode);
    }

    /**
     * System metrics for monitoring.
     */
    public function metrics(): JsonResponse
    {
        $metrics = $this->healthCheckService->getSystemMetrics();
        
        return response()->json([
            'success' => true,
            'data' => $metrics,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Readiness probe for Kubernetes/Docker.
     */
    public function ready(): JsonResponse
    {
        $healthCheck = $this->healthCheckService->performHealthCheck();
        
        // Check critical components for readiness
        $criticalComponents = ['database', 'redis', 'cache'];
        $isReady = collect($criticalComponents)->every(function ($component) use ($healthCheck) {
            return $healthCheck['checks'][$component]['status'] === 'healthy';
        });
        
        $statusCode = $isReady ? 200 : 503;
        
        return response()->json([
            'status' => $isReady ? 'ready' : 'not_ready',
            'timestamp' => now()->toISOString(),
            'critical_components' => collect($criticalComponents)->mapWithKeys(function ($component) use ($healthCheck) {
                return [$component => $healthCheck['checks'][$component]['status']];
            }),
        ], $statusCode);
    }

    /**
     * Liveness probe for Kubernetes/Docker.
     */
    public function live(): JsonResponse
    {
        // Simple liveness check - just verify the application is running
        return response()->json([
            'status' => 'alive',
            'timestamp' => now()->toISOString(),
            'uptime' => $this->getApplicationUptime(),
        ]);
    }

    /**
     * Prometheus metrics endpoint.
     */
    public function prometheus(Request $request): string
    {
        $healthCheck = $this->healthCheckService->performHealthCheck();
        $systemMetrics = $this->healthCheckService->getSystemMetrics();
        
        $metrics = [];
        
        // Health status metrics
        foreach ($healthCheck['checks'] as $component => $check) {
            $status = $check['status'] === 'healthy' ? 1 : 0;
            $metrics[] = "chisolution_health_status{component=\"{$component}\"} {$status}";
            
            if (isset($check['response_time_ms'])) {
                $metrics[] = "chisolution_response_time_ms{component=\"{$component}\"} {$check['response_time_ms']}";
            }
        }
        
        // System metrics
        $metrics[] = "chisolution_memory_usage_bytes " . $systemMetrics['memory_usage']['current'];
        $metrics[] = "chisolution_memory_peak_bytes " . $systemMetrics['memory_usage']['peak'];
        $metrics[] = "chisolution_memory_limit_bytes " . $systemMetrics['memory_usage']['limit'];
        
        if ($systemMetrics['cpu_load']) {
            $metrics[] = "chisolution_cpu_load_1m " . $systemMetrics['cpu_load'][0];
            $metrics[] = "chisolution_cpu_load_5m " . $systemMetrics['cpu_load'][1];
            $metrics[] = "chisolution_cpu_load_15m " . $systemMetrics['cpu_load'][2];
        }
        
        // Chat-specific metrics
        if (isset($healthCheck['checks']['chat_system']['details'])) {
            $chatDetails = $healthCheck['checks']['chat_system']['details'];
            $metrics[] = "chisolution_active_rooms " . ($chatDetails['active_rooms'] ?? 0);
            $metrics[] = "chisolution_recent_rooms_1h " . ($chatDetails['recent_rooms_1h'] ?? 0);
            $metrics[] = "chisolution_recent_messages_1h " . ($chatDetails['recent_messages_1h'] ?? 0);
        }
        
        // Add timestamp
        $timestamp = now()->timestamp * 1000; // Prometheus expects milliseconds
        $metricsWithTimestamp = array_map(function ($metric) use ($timestamp) {
            return $metric . " " . $timestamp;
        }, $metrics);
        
        return response(implode("\n", $metricsWithTimestamp) . "\n")
            ->header('Content-Type', 'text/plain; version=0.0.4; charset=utf-8');
    }

    /**
     * Get application uptime.
     */
    protected function getApplicationUptime(): ?float
    {
        $startFile = storage_path('framework/down');
        if (file_exists($startFile)) {
            return null; // Application is in maintenance mode
        }
        
        // Try to get uptime from cache or calculate from server start
        return cache()->remember('app_uptime_start', 3600, function () {
            return time();
        });
    }
}
