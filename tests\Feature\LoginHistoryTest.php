<?php

namespace Tests\Feature;

use App\Models\LoginHistory;
use App\Models\User;
use App\Models\Role;
use App\Services\LoginHistoryService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;

class LoginHistoryTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected LoginHistoryService $loginHistoryService;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a customer role
        $customerRole = Role::factory()->create([
            'name' => 'customer',
            'slug' => 'customer',
            'is_active' => true,
        ]);

        $this->user = User::factory()->create([
            'role_id' => $customerRole->id,
            'email_verified_at' => now(),
        ]);

        $this->loginHistoryService = app(LoginHistoryService::class);
    }

    /**
     * Create a request with session for testing
     */
    private function createRequestWithSession(array $server = []): Request
    {
        $defaultServer = [
            'REMOTE_ADDR' => '***********',
            'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ];

        $request = Request::create('/login', 'POST', [], [], [], array_merge($defaultServer, $server));
        $request->setLaravelSession($this->app['session.store']);

        return $request;
    }

    #[Test]
    public function it_tracks_successful_login_attempt()
    {
        $request = $this->createRequestWithSession();

        $loginHistory = $this->loginHistoryService->trackLoginAttempt(
            $request,
            $this->user,
            'success'
        );

        $this->assertInstanceOf(LoginHistory::class, $loginHistory);
        $this->assertEquals($this->user->id, $loginHistory->user_id);
        $this->assertEquals('success', $loginHistory->login_status);
        $this->assertEquals('***********', $loginHistory->ip_address);
        $this->assertNotNull($loginHistory->device_fingerprint);
        $this->assertNotNull($loginHistory->session_id);
    }

    #[Test]
    public function it_tracks_failed_login_attempt()
    {
        $request = $this->createRequestWithSession();

        $loginHistory = $this->loginHistoryService->trackLoginAttempt(
            $request,
            $this->user,
            'failed',
            'Invalid credentials'
        );

        $this->assertEquals('failed', $loginHistory->login_status);
        $this->assertEquals('Invalid credentials', $loginHistory->failure_reason);
        $this->assertGreaterThan(0, $loginHistory->risk_score);
    }

    #[Test]
    public function it_tracks_failed_login_without_user()
    {
        $request = $this->createRequestWithSession();

        $loginHistory = $this->loginHistoryService->trackLoginAttempt(
            $request,
            null, // No user found
            'failed',
            'Invalid credentials'
        );

        $this->assertNull($loginHistory->user_id);
        $this->assertEquals('failed', $loginHistory->login_status);
        $this->assertEquals('Invalid credentials', $loginHistory->failure_reason);
    }

    #[Test]
    public function it_calculates_higher_risk_score_for_unknown_device()
    {
        $request = $this->createRequestWithSession();

        // First login (new device)
        $firstLogin = $this->loginHistoryService->trackLoginAttempt(
            $request,
            $this->user,
            'success'
        );

        // Second login from same device
        $secondLogin = $this->loginHistoryService->trackLoginAttempt(
            $request,
            $this->user,
            'success'
        );

        // First login should have higher risk score due to unknown device
        $this->assertGreaterThan($secondLogin->risk_score, $firstLogin->risk_score);
        $this->assertFalse($firstLogin->is_device_known);
        $this->assertTrue($secondLogin->is_device_known);
    }

    #[Test]
    public function it_records_logout_and_session_duration()
    {
        $request = $this->createRequestWithSession();

        // Track login
        $loginHistory = $this->loginHistoryService->trackLoginAttempt(
            $request,
            $this->user,
            'success'
        );

        // Simulate some time passing
        sleep(1);

        // Track logout
        $this->loginHistoryService->recordLogout($this->user, $request);

        $loginHistory->refresh();

        $this->assertNotNull($loginHistory->session_ended_at);
        $this->assertGreaterThan(0, $loginHistory->session_duration);
    }

    #[Test]
    public function it_detects_suspicious_activity()
    {
        $request = $this->createRequestWithSession([
            'HTTP_USER_AGENT' => 'curl/7.68.0', // Suspicious user agent
        ]);

        $loginHistory = $this->loginHistoryService->trackLoginAttempt(
            $request,
            $this->user,
            'failed',
            'Invalid credentials'
        );

        // Should be marked as suspicious due to failed login + suspicious user agent
        $this->assertTrue($loginHistory->is_suspicious);
        $this->assertGreaterThan(50, $loginHistory->risk_score);
    }

    #[Test]
    public function it_generates_consistent_device_fingerprint()
    {
        $request1 = $this->createRequestWithSession([
            'HTTP_ACCEPT_LANGUAGE' => 'en-US,en;q=0.9',
        ]);

        $request2 = $this->createRequestWithSession([
            'REMOTE_ADDR' => '***********', // Different IP
            'HTTP_ACCEPT_LANGUAGE' => 'en-US,en;q=0.9',
        ]);

        $login1 = $this->loginHistoryService->trackLoginAttempt($request1, $this->user, 'success');
        $login2 = $this->loginHistoryService->trackLoginAttempt($request2, $this->user, 'success');

        // Same device fingerprint despite different IP
        $this->assertEquals($login1->device_fingerprint, $login2->device_fingerprint);
    }

    #[Test]
    public function it_provides_login_statistics()
    {
        // Create some login history
        $request = $this->createRequestWithSession();

        // Successful logins
        $this->loginHistoryService->trackLoginAttempt($request, $this->user, 'success');
        $this->loginHistoryService->trackLoginAttempt($request, $this->user, 'success');

        // Failed login
        $this->loginHistoryService->trackLoginAttempt($request, $this->user, 'failed', 'Invalid credentials');

        $stats = $this->loginHistoryService->getLoginStats($this->user, 30);

        $this->assertEquals(3, $stats['total_logins']);
        $this->assertEquals(2, $stats['successful_logins']);
        $this->assertEquals(1, $stats['failed_logins']);
        $this->assertEquals(66.67, $stats['success_rate']);
    }

    #[Test]
    public function it_handles_location_data_gracefully()
    {
        $request = $this->createRequestWithSession([
            'REMOTE_ADDR' => '127.0.0.1', // Local IP that won't have location data
        ]);

        $loginHistory = $this->loginHistoryService->trackLoginAttempt(
            $request,
            $this->user,
            'success'
        );

        // Should handle missing location data gracefully
        $this->assertNotNull($loginHistory);
        $this->assertEquals('127.0.0.1', $loginHistory->ip_address);
        $this->assertEquals('Unknown Location', $loginHistory->location);
    }

    #[Test]
    public function it_tracks_multiple_failed_attempts_from_same_ip()
    {
        $request = $this->createRequestWithSession();

        // Multiple failed attempts
        $login1 = $this->loginHistoryService->trackLoginAttempt($request, $this->user, 'failed', 'Invalid credentials');
        $login2 = $this->loginHistoryService->trackLoginAttempt($request, $this->user, 'failed', 'Invalid credentials');
        $login3 = $this->loginHistoryService->trackLoginAttempt($request, $this->user, 'failed', 'Invalid credentials');

        // Risk score should increase with each failed attempt
        $this->assertGreaterThan($login1->risk_score, $login2->risk_score);
        $this->assertGreaterThan($login2->risk_score, $login3->risk_score);
    }
}
