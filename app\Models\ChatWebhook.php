<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class ChatWebhook extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'name',
        'url',
        'events',
        'secret',
        'headers',
        'is_active',
        'max_retries',
        'timeout_seconds',
        'ip_whitelist',
        'content_type',
        'description',
    ];

    protected $casts = [
        'events' => 'array',
        'headers' => 'array',
        'ip_whitelist' => 'array',
        'is_active' => 'boolean',
        'max_retries' => 'integer',
        'timeout_seconds' => 'integer',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the webhook deliveries for this webhook.
     */
    public function deliveries(): HasMany
    {
        return $this->hasMany(ChatWebhookDelivery::class);
    }

    /**
     * Get recent deliveries for this webhook.
     */
    public function recentDeliveries(int $limit = 50): HasMany
    {
        return $this->deliveries()
            ->orderBy('created_at', 'desc')
            ->limit($limit);
    }

    /**
     * Get delivery statistics for this webhook.
     */
    public function getDeliveryStats(int $days = 7): array
    {
        $startDate = now()->subDays($days);
        
        $total = $this->deliveries()
            ->where('created_at', '>=', $startDate)
            ->count();

        $delivered = $this->deliveries()
            ->where('created_at', '>=', $startDate)
            ->where('status', 'delivered')
            ->count();

        $failed = $this->deliveries()
            ->where('created_at', '>=', $startDate)
            ->where('status', 'failed')
            ->count();

        $successRate = $total > 0 ? round(($delivered / $total) * 100, 2) : 0;

        return [
            'total' => $total,
            'delivered' => $delivered,
            'failed' => $failed,
            'success_rate' => $successRate,
            'period_days' => $days,
        ];
    }

    /**
     * Check if this webhook should receive a specific event.
     */
    public function shouldReceiveEvent(string $eventType): bool
    {
        if (!$this->is_active) {
            return false;
        }

        return in_array($eventType, $this->events ?? []);
    }

    /**
     * Generate HMAC signature for payload.
     */
    public function generateSignature(string $payload): ?string
    {
        if (empty($this->secret)) {
            return null;
        }

        return 'sha256=' . hash_hmac('sha256', $payload, $this->secret);
    }

    /**
     * Get available event types.
     */
    public static function getAvailableEvents(): array
    {
        return [
            'message.sent' => 'Message sent in chat room',
            'message.received' => 'Message received in chat room',
            'room.created' => 'Chat room created',
            'room.closed' => 'Chat room closed',
            'room.assigned' => 'Chat room assigned to staff',
            'room.transferred' => 'Chat room transferred to another staff',
            'user.joined' => 'User joined chat room',
            'user.left' => 'User left chat room',
            'file.uploaded' => 'File uploaded to chat room',
            'rating.submitted' => 'Chat rating submitted',
            'ai.response' => 'AI response generated',
            'escalation.triggered' => 'Chat escalated to human agent',
        ];
    }
}
