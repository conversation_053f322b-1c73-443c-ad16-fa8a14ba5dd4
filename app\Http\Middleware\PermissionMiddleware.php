<?php

namespace App\Http\Middleware;

use App\Services\PermissionService;
use App\Services\ActivityLogger;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class PermissionMiddleware
{
    protected PermissionService $permissionService;
    protected ActivityLogger $activityLogger;

    public function __construct(PermissionService $permissionService, ActivityLogger $activityLogger)
    {
        $this->permissionService = $permissionService;
        $this->activityLogger = $activityLogger;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  $resource
     * @param  string  $action
     * @param  string|null  $adminOnly - If 'admin', only admins can access regardless of permissions
     */
    public function handle(Request $request, Closure $next, string $resource, string $action, ?string $adminOnly = null): Response
    {
        if (!$request->user()) {
            $this->logUnauthorizedAccess($request, $resource, $action, 'UNAUTHENTICATED');

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'UNAUTHENTICATED',
                        'message' => 'Authentication required.',
                        'timestamp' => now()->toISOString()
                    ]
                ], 401);
            }

            return redirect()->route('login');
        }

        $user = $request->user();

        // Check if user is active and not deleted
        if (!$user->is_active || $user->is_deleted) {
            $this->logUnauthorizedAccess($request, $resource, $action, 'ACCOUNT_INACTIVE', $user);

            $message = 'Your account is inactive.';
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'ACCOUNT_INACTIVE',
                        'message' => $message,
                        'timestamp' => now()->toISOString()
                    ]
                ], 403);
            }

            abort(403, $message);
        }

        // Admin-only check
        if ($adminOnly === 'admin' && !$user->isAdmin()) {
            $this->logUnauthorizedAccess($request, $resource, $action, 'ADMIN_REQUIRED', $user);

            $message = 'Administrator access required.';
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'ADMIN_REQUIRED',
                        'message' => $message,
                        'timestamp' => now()->toISOString()
                    ]
                ], 403);
            }

            abort(403, $message);
        }

        // Check if user has required permission (using enhanced service)
        if (!$this->permissionService->userHasPermission($user, $resource, $action)) {
            $this->logUnauthorizedAccess($request, $resource, $action, 'INSUFFICIENT_PERMISSIONS', $user);

            $message = "You do not have permission to {$action} {$resource}.";
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'INSUFFICIENT_PERMISSIONS',
                        'message' => $message,
                        'timestamp' => now()->toISOString(),
                        'required_permission' => [
                            'resource' => $resource,
                            'action' => $action
                        ]
                    ]
                ], 403);
            }

            abort(403, $message);
        }

        return $next($request);
    }

    /**
     * Log unauthorized access attempts.
     */
    private function logUnauthorizedAccess(Request $request, string $resource, string $action, string $reason, $user = null): void
    {
        $this->activityLogger->logActivity(
            'unauthorized_access_attempt',
            "Unauthorized access attempt to {$action} {$resource}. Reason: {$reason}",
            'failed',
            $reason,
            [
                'resource' => $resource,
                'action' => $action,
                'reason' => $reason,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'route' => $request->route()?->getName(),
                'url' => $request->fullUrl()
            ],
            [],
            75, // High risk score for unauthorized access
            $user
        );
    }
}
