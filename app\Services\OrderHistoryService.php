<?php

namespace App\Services;

use App\Models\Order;
use App\Models\OrderHistory;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OrderHistoryService
{
    protected $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * Log order creation.
     */
    public function logOrderCreated(Order $order, ?User $user = null): OrderHistory
    {
        $user = $user ?? Auth::user();
        
        return $this->createHistoryEntry($order, [
            'event_type' => OrderHistory::EVENT_ORDER_CREATED,
            'description' => "Order #{$order->order_number} was created",
            'triggered_by' => $user ? 'customer' : 'guest',
            'user_id' => $user?->id,
            'user_name' => $user ? "{$user->first_name} {$user->last_name}" : null,
            'user_email' => $user?->email ?? $order->email,
            'metadata' => [
                'order_number' => $order->order_number,
                'total_amount' => $order->total_amount,
                'payment_method' => $order->payment_method,
                'items_count' => $order->items->count(),
                'customer_type' => $user ? 'registered' : 'guest',
            ],
        ]);
    }

    /**
     * Log order status change.
     */
    public function logStatusChange(Order $order, string $oldStatus, string $newStatus, ?User $changedBy = null): OrderHistory
    {
        $changedBy = $changedBy ?? Auth::user();
        
        $description = "Order status changed from " . ucfirst($oldStatus) . " to " . ucfirst($newStatus);
        
        return $this->createHistoryEntry($order, [
            'event_type' => OrderHistory::EVENT_STATUS_CHANGED,
            'field_name' => 'status',
            'old_value' => $oldStatus,
            'new_value' => $newStatus,
            'description' => $description,
            'triggered_by' => $changedBy ? 'admin' : 'system',
            'user_id' => $changedBy?->id,
            'user_name' => $changedBy ? "{$changedBy->first_name} {$changedBy->last_name}" : null,
            'user_email' => $changedBy?->email,
            'metadata' => [
                'status_from' => $oldStatus,
                'status_to' => $newStatus,
                'order_number' => $order->order_number,
            ],
        ]);
    }

    /**
     * Log payment status change.
     */
    public function logPaymentStatusChange(Order $order, string $oldStatus, string $newStatus, ?User $changedBy = null): OrderHistory
    {
        $changedBy = $changedBy ?? Auth::user();
        
        $description = "Payment status changed from " . ucfirst($oldStatus) . " to " . ucfirst($newStatus);
        
        return $this->createHistoryEntry($order, [
            'event_type' => OrderHistory::EVENT_PAYMENT_STATUS_CHANGED,
            'field_name' => 'payment_status',
            'old_value' => $oldStatus,
            'new_value' => $newStatus,
            'description' => $description,
            'triggered_by' => $changedBy ? 'admin' : 'system',
            'user_id' => $changedBy?->id,
            'user_name' => $changedBy ? "{$changedBy->first_name} {$changedBy->last_name}" : null,
            'user_email' => $changedBy?->email,
            'metadata' => [
                'payment_status_from' => $oldStatus,
                'payment_status_to' => $newStatus,
                'order_number' => $order->order_number,
            ],
        ]);
    }

    /**
     * Log order shipped.
     */
    public function logOrderShipped(Order $order, ?User $shippedBy = null): OrderHistory
    {
        $shippedBy = $shippedBy ?? Auth::user();
        
        return $this->createHistoryEntry($order, [
            'event_type' => OrderHistory::EVENT_SHIPPED,
            'description' => "Order was marked as shipped",
            'triggered_by' => $shippedBy ? 'admin' : 'system',
            'user_id' => $shippedBy?->id,
            'user_name' => $shippedBy ? "{$shippedBy->first_name} {$shippedBy->last_name}" : null,
            'user_email' => $shippedBy?->email,
            'metadata' => [
                'shipped_at' => $order->shipped_at?->toISOString(),
                'order_number' => $order->order_number,
            ],
        ]);
    }

    /**
     * Log order delivered.
     */
    public function logOrderDelivered(Order $order, ?User $deliveredBy = null): OrderHistory
    {
        $deliveredBy = $deliveredBy ?? Auth::user();
        
        return $this->createHistoryEntry($order, [
            'event_type' => OrderHistory::EVENT_DELIVERED,
            'description' => "Order was marked as delivered",
            'triggered_by' => $deliveredBy ? 'admin' : 'system',
            'user_id' => $deliveredBy?->id,
            'user_name' => $deliveredBy ? "{$deliveredBy->first_name} {$deliveredBy->last_name}" : null,
            'user_email' => $deliveredBy?->email,
            'metadata' => [
                'delivered_at' => $order->delivered_at?->toISOString(),
                'order_number' => $order->order_number,
            ],
        ]);
    }

    /**
     * Log order cancelled.
     */
    public function logOrderCancelled(Order $order, ?User $cancelledBy = null, ?string $reason = null): OrderHistory
    {
        $cancelledBy = $cancelledBy ?? Auth::user();
        
        $description = "Order was cancelled";
        if ($reason) {
            $description .= " - Reason: {$reason}";
        }
        
        return $this->createHistoryEntry($order, [
            'event_type' => OrderHistory::EVENT_CANCELLED,
            'description' => $description,
            'triggered_by' => $cancelledBy ? 'admin' : 'system',
            'user_id' => $cancelledBy?->id,
            'user_name' => $cancelledBy ? "{$cancelledBy->first_name} {$cancelledBy->last_name}" : null,
            'user_email' => $cancelledBy?->email,
            'metadata' => [
                'cancellation_reason' => $reason,
                'order_number' => $order->order_number,
            ],
        ]);
    }

    /**
     * Log note added to order.
     */
    public function logNoteAdded(Order $order, string $note, ?User $addedBy = null): OrderHistory
    {
        $addedBy = $addedBy ?? Auth::user();
        
        return $this->createHistoryEntry($order, [
            'event_type' => OrderHistory::EVENT_NOTE_ADDED,
            'description' => "Note added to order",
            'new_value' => $note,
            'triggered_by' => $addedBy ? 'admin' : 'system',
            'user_id' => $addedBy?->id,
            'user_name' => $addedBy ? "{$addedBy->first_name} {$addedBy->last_name}" : null,
            'user_email' => $addedBy?->email,
            'metadata' => [
                'note_content' => $note,
                'order_number' => $order->order_number,
            ],
        ]);
    }

    /**
     * Create a history entry with common data.
     */
    protected function createHistoryEntry(Order $order, array $data): OrderHistory
    {
        $commonData = [
            'order_id' => $order->id,
            'ip_address' => $this->request->ip(),
            'user_agent' => $this->request->userAgent(),
        ];

        return OrderHistory::create(array_merge($commonData, $data));
    }

    /**
     * Get order history for display.
     */
    public function getOrderHistory(Order $order): \Illuminate\Database\Eloquent\Collection
    {
        return $order->history()
            ->orderBy('created_at', 'desc')
            ->get();
    }
}
