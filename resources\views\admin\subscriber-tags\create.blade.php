@extends('layouts.dashboard')

@section('title', 'Create Subscriber Tag')

@section('content')
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Create Subscriber Tag</h1>
            <p class="mt-1 text-sm text-gray-600">Create a new tag to organize and segment your subscribers</p>
        </div>
        <a href="{{ route('admin.subscriber-tags.index') }}" 
           class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
            </svg>
            Back to Tags
        </a>
    </div>

    <!-- Messages Container -->
    <div id="messages-container" class="hidden"></div>

    <!-- Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <form id="tag-form" method="POST" action="{{ route('admin.subscriber-tags.store') }}" class="space-y-6">
            @csrf

            <!-- Tag Name -->
            <div>
                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
                    Tag Name <span class="text-red-500">*</span>
                </label>
                <input type="text" 
                       id="name" 
                       name="name" 
                       value="{{ old('name') }}"
                       required
                       placeholder="Enter tag name..."
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('name') border-red-500 @enderror">
                @error('name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea id="description" 
                          name="description" 
                          rows="3"
                          placeholder="Brief description of this tag..."
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('description') border-red-500 @enderror">{{ old('description') }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Color -->
            <div>
                <label for="color" class="block text-sm font-medium text-gray-700 mb-1">
                    Tag Color <span class="text-red-500">*</span>
                </label>
                <div class="flex items-center space-x-3">
                    <input type="color" 
                           id="color" 
                           name="color" 
                           value="{{ old('color', '#3B82F6') }}"
                           required
                           class="h-10 w-20 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('color') border-red-500 @enderror">
                    <div class="flex-1">
                        <input type="text" 
                               id="color-hex" 
                               value="{{ old('color', '#3B82F6') }}"
                               placeholder="#3B82F6"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    </div>
                </div>
                <p class="mt-1 text-sm text-gray-500">Choose a color to help identify this tag in the interface</p>
                @error('color')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Predefined Colors -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Quick Colors</label>
                <div class="flex flex-wrap gap-2">
                    @php
                        $predefinedColors = [
                            '#3B82F6', // Blue
                            '#10B981', // Green
                            '#F59E0B', // Yellow
                            '#EF4444', // Red
                            '#8B5CF6', // Purple
                            '#06B6D4', // Cyan
                            '#F97316', // Orange
                            '#84CC16', // Lime
                            '#EC4899', // Pink
                            '#6B7280', // Gray
                        ];
                    @endphp
                    @foreach($predefinedColors as $presetColor)
                        <button type="button" 
                                class="color-preset w-8 h-8 rounded-full border-2 border-gray-300 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"
                                style="background-color: {{ $presetColor }}"
                                data-color="{{ $presetColor }}"
                                title="{{ $presetColor }}">
                        </button>
                    @endforeach
                </div>
            </div>

            <!-- Status -->
            <div class="flex items-center">
                <input type="checkbox" 
                       id="is_active" 
                       name="is_active" 
                       value="1"
                       {{ old('is_active', true) ? 'checked' : '' }}
                       class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                <label for="is_active" class="ml-2 text-sm text-gray-700">Active</label>
                <p class="ml-2 text-sm text-gray-500">(Inactive tags won't be available for assignment)</p>
            </div>

            <!-- Preview -->
            <div class="bg-gray-50 rounded-lg p-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Preview</label>
                <div class="flex items-center space-x-2">
                    <span id="tag-preview" 
                          class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium text-white"
                          style="background-color: {{ old('color', '#3B82F6') }}">
                        <span id="preview-text">{{ old('name', 'Tag Name') }}</span>
                    </span>
                    <span class="text-sm text-gray-500">This is how your tag will appear</span>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{{ route('admin.subscriber-tags.index') }}" 
                   class="inline-flex items-center px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors duration-200">
                    Cancel
                </a>
                <button type="submit" 
                        id="submit-btn"
                        class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                    <span class="submit-text">Create Tag</span>
                    <span class="loading-text hidden">Creating...</span>
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('name');
    const colorInput = document.getElementById('color');
    const colorHexInput = document.getElementById('color-hex');
    const tagPreview = document.getElementById('tag-preview');
    const previewText = document.getElementById('preview-text');
    const colorPresets = document.querySelectorAll('.color-preset');

    // Update preview when name changes
    nameInput.addEventListener('input', function() {
        previewText.textContent = this.value || 'Tag Name';
    });

    // Update preview when color changes
    function updateColorPreview(color) {
        tagPreview.style.backgroundColor = color;
        colorInput.value = color;
        colorHexInput.value = color;
    }

    colorInput.addEventListener('change', function() {
        updateColorPreview(this.value);
    });

    colorHexInput.addEventListener('input', function() {
        const color = this.value;
        if (/^#[0-9A-Fa-f]{6}$/.test(color)) {
            updateColorPreview(color);
        }
    });

    // Color preset functionality
    colorPresets.forEach(preset => {
        preset.addEventListener('click', function() {
            const color = this.dataset.color;
            updateColorPreview(color);
        });
    });

    // Form submission
    const form = document.getElementById('tag-form');
    const submitBtn = document.getElementById('submit-btn');
    const messagesContainer = document.getElementById('messages-container');

    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        // Show loading state
        submitBtn.disabled = true;
        document.querySelector('.submit-text').classList.add('hidden');
        document.querySelector('.loading-text').classList.remove('hidden');

        // Clear previous messages
        messagesContainer.innerHTML = '';
        messagesContainer.classList.add('hidden');

        try {
            const formData = new FormData(form);

            const response = await fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (data.success) {
                // Redirect to tags index
                window.location.href = '{{ route("admin.subscriber-tags.index") }}';
            } else {
                // Show error message
                messagesContainer.innerHTML = `
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex">
                            <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">Error</h3>
                                <p class="mt-1 text-sm text-red-700">${data.message || 'An error occurred while creating the tag.'}</p>
                            </div>
                        </div>
                    </div>
                `;
                messagesContainer.classList.remove('hidden');
            }
        } catch (error) {
            console.error('Error:', error);
            messagesContainer.innerHTML = `
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex">
                        <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">Error</h3>
                            <p class="mt-1 text-sm text-red-700">An unexpected error occurred. Please try again.</p>
                        </div>
                    </div>
                </div>
            `;
            messagesContainer.classList.remove('hidden');
        } finally {
            // Reset loading state
            submitBtn.disabled = false;
            document.querySelector('.submit-text').classList.remove('hidden');
            document.querySelector('.loading-text').classList.add('hidden');
        }
    });
});
</script>
@endpush
@endsection
