/**
 * Global Cart Utilities
 * Optimized cart utilities using local storage and smart server synchronization
 */

// Global cart count update function
function updateCartCount(count) {
    // Update all cart count elements
    const cartCountElements = document.querySelectorAll('.cart-count');
    cartCountElements.forEach(element => {
        element.textContent = count;

        // Show/hide badge based on count
        if (count > 0) {
            element.style.display = '';
            element.classList.remove('hidden');

            // Add animation for visual feedback
            element.classList.add('animate-pulse');
            setTimeout(() => {
                element.classList.remove('animate-pulse');
            }, 1000);
        } else {
            element.style.display = 'none';
            element.classList.add('hidden');
        }
    });

    // Update global cart count variable
    window.globalCartCount = count;
}

// Enhanced add to cart function using state manager
async function addToCart(productId, quantity = 1, variantId = null) {
    try {
        // Show loading state
        showNotification('Adding to cart...', 'info');

        // Get product data (from page or fetch if needed)
        const product = await getProductData(productId);
        const variant = variantId ? await getVariantData(variantId) : null;

        // Add to local state immediately for instant feedback
        if (window.cartStateManager) {
            window.cartStateManager.addItem(product, quantity, variant);
            showNotification('Item added to cart!', 'success');
        } else {
            // Fallback to server request
            return await addToCartServer(productId, quantity, variantId);
        }

    } catch (error) {
        console.error('❌ Error adding to cart:', error);
        showNotification('Failed to add item to cart. Please try again.', 'error');
    }
}

// Get product data (from page data or API)
async function getProductData(productId) {
    // Try to get from page data first
    const pageProduct = window.pageData?.product;
    if (pageProduct && pageProduct.id === productId) {
        return pageProduct;
    }

    // Fallback to API
    const response = await fetch(`/api/products/${productId}`, {
        headers: { 'X-Requested-With': 'XMLHttpRequest' }
    });

    if (response.ok) {
        return await response.json();
    }

    throw new Error('Failed to fetch product data');
}

// Get variant data
async function getVariantData(variantId) {
    const response = await fetch(`/api/product-variants/${variantId}`, {
        headers: { 'X-Requested-With': 'XMLHttpRequest' }
    });

    if (response.ok) {
        return await response.json();
    }

    return null;
}

// Fallback server add to cart
async function addToCartServer(productId, quantity, variantId) {
    // Get cart add URL from global config or fallback
    const cartAddUrl = window.appConfig?.routes?.cartAdd || '/cart/add';
    const response = await fetch(cartAddUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            product_id: productId,
            quantity: quantity,
            variant_id: variantId
        })
    });

    if (response.ok) {
        const data = await response.json();
        if (data.success) {
            updateCartCount(data.cart.item_count);
            showNotification(data.message, 'success');
        } else {
            throw new Error(data.message);
        }
    } else {
        throw new Error('Server request failed');
    }
}

// Optimized cart count fetching - uses local storage first
async function fetchCartCount() {
    // Use local state if available
    if (window.cartStateManager) {
        const count = window.cartStateManager.getCount();
        updateCartCount(count);
        return count;
    }

    // Fallback to server
    try {
        // Get cart count URL from global config or fallback
        const cartCountUrl = window.appConfig?.routes?.cartCount || '/cart/count';
        const response = await fetch(cartCountUrl, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        if (response.ok) {
            const data = await response.json();
            updateCartCount(data.count);
            return data.count;
        }
    } catch (error) {
        console.error('❌ Error fetching cart count:', error);
    }
    return 0;
}

// Debounce function to prevent rapid successive calls
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Debounced version of fetchCartCount (wait 1 second between calls)
const debouncedFetchCartCount = debounce(fetchCartCount, 1000);

// Initialize cart system on page load
document.addEventListener('DOMContentLoaded', function() {
    // Initialize cart count from local storage or server
    if (window.cartStateManager) {
        // Use local state
        const count = window.cartStateManager.getCount();
        updateCartCount(count);

        // Listen for cart state changes
        window.addEventListener('cartStateChanged', function(event) {
            updateCartCount(event.detail.count);
        });
    } else {
        // Fallback to server fetch
        debouncedFetchCartCount();
    }
});

// Smart cart count refresh - only when necessary
let isIdle = false;
let idleTimer;

function resetIdleTimer() {
    clearTimeout(idleTimer);
    if (isIdle) {
        isIdle = false;
        // Force sync when user becomes active again
        if (window.cartStateManager) {
            window.cartStateManager.forceSync();
        } else {
            debouncedFetchCartCount();
        }
    }

    // Set user as idle after 5 minutes of inactivity
    idleTimer = setTimeout(() => {
        isIdle = true;
    }, 300000); // 5 minutes
}

// Track user activity
['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
    document.addEventListener(event, resetIdleTimer, true);
});

// Initialize idle timer
resetIdleTimer();

// Global notification function
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 transition-all duration-300 transform translate-x-full`;
    
    // Set background color based on type
    switch(type) {
        case 'success':
            notification.classList.add('bg-green-500');
            break;
        case 'error':
            notification.classList.add('bg-red-500');
            break;
        case 'warning':
            notification.classList.add('bg-yellow-500');
            break;
        default:
            notification.classList.add('bg-blue-500');
    }
    
    notification.innerHTML = `
        <div class="flex items-center">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-3 text-white hover:text-gray-200">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 300);
    }, 5000);
}

// Export functions for use in other scripts
window.updateCartCount = updateCartCount;
window.fetchCartCount = fetchCartCount;
window.debouncedFetchCartCount = debouncedFetchCartCount;
window.showNotification = showNotification;
window.addToCart = addToCart;
