<?php

namespace App\Console\Commands;

use App\Services\ChatWebhookService;
use Illuminate\Console\Command;

class ProcessWebhookRetries extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'chat:process-webhook-retries';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process failed webhook deliveries that are ready for retry';

    /**
     * Execute the console command.
     */
    public function handle(ChatWebhookService $webhookService): int
    {
        $this->info('Processing webhook retries...');

        $retriedCount = $webhookService->retryFailedDeliveries();

        if ($retriedCount > 0) {
            $this->info("✅ Queued {$retriedCount} webhook deliveries for retry");
        } else {
            $this->info('ℹ️  No webhook deliveries ready for retry');
        }

        return Command::SUCCESS;
    }
}
