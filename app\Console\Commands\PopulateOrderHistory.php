<?php

namespace App\Console\Commands;

use App\Models\Order;
use App\Models\OrderHistory;
use App\Services\OrderHistoryService;
use Illuminate\Console\Command;
use Illuminate\Http\Request;

class PopulateOrderHistory extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'orders:populate-history {--force : Force populate even if history exists}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Populate order history for existing orders that don\'t have history entries';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to populate order history...');

        // Create a mock request for the service
        $request = Request::create('/', 'GET');
        $historyService = new OrderHistoryService($request);

        $orders = Order::where('is_deleted', false)->get();
        $processed = 0;
        $skipped = 0;

        foreach ($orders as $order) {
            // Check if order already has history
            if (!$this->option('force') && $order->history()->exists()) {
                $skipped++;
                continue;
            }

            // Clear existing history if force option is used
            if ($this->option('force')) {
                $order->history()->delete();
            }

            // Create order creation history
            $historyService->logOrderCreated($order, $order->user);

            // Add status-specific history based on current order state
            if ($order->status !== 'pending') {
                // Log status progression
                $this->createStatusProgression($order, $historyService);
            }

            // Add payment status history if paid
            if ($order->payment_status === 'paid' || $order->payment_status === 'completed') {
                $historyService->logPaymentStatusChange($order, 'pending', $order->payment_status);
            }

            $processed++;
            $this->line("Processed order #{$order->order_number}");
        }

        $this->info("Completed! Processed: {$processed}, Skipped: {$skipped}");
    }

    /**
     * Create status progression history based on current order status.
     */
    private function createStatusProgression(Order $order, OrderHistoryService $historyService)
    {
        $statusProgression = [
            'pending' => [],
            'confirmed' => ['pending'],
            'processing' => ['pending', 'confirmed'],
            'shipped' => ['pending', 'confirmed', 'processing'],
            'delivered' => ['pending', 'confirmed', 'processing', 'shipped'],
            'cancelled' => ['pending'], // Could be cancelled from any status
            'refunded' => ['pending', 'confirmed', 'processing', 'shipped', 'delivered'],
        ];

        $currentStatus = $order->status;
        $progression = $statusProgression[$currentStatus] ?? [];

        $previousStatus = 'pending';
        foreach ($progression as $status) {
            if ($status !== 'pending') {
                $historyService->logStatusChange($order, $previousStatus, $status);
            }
            $previousStatus = $status;
        }

        // Log final status if different from last in progression
        if (!empty($progression) && end($progression) !== $currentStatus) {
            $historyService->logStatusChange($order, end($progression), $currentStatus);
        }

        // Log specific events
        if ($currentStatus === 'shipped' && $order->shipped_at) {
            $historyService->logOrderShipped($order);
        }

        if ($currentStatus === 'delivered' && $order->delivered_at) {
            $historyService->logOrderDelivered($order);
        }

        if ($currentStatus === 'cancelled') {
            $historyService->logOrderCancelled($order);
        }
    }
}
