@extends('layouts.app')

@section('title', __('common.projects') . ' - ' . __('common.company_name'))
@section('meta_description', 'Explore our portfolio of successful digital projects including websites, mobile apps, and e-commerce solutions.')
@section('meta_keywords', 'portfolio, projects, web development projects, mobile apps, case studies, client work')

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-blue text-white overflow-hidden">
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
    </div>
    
    <div class="container mx-auto px-4 py-20 lg:py-32 relative z-10">
        <div class="text-center max-w-4xl mx-auto">
            <h1 class="heading-1 text-white mb-6">
                Our <span class="text-blue-300">Portfolio</span>
            </h1>
            <p class="text-lead text-blue-100 mb-8">
                Discover the innovative digital solutions we've created for our clients. Each project represents our commitment to excellence and innovation.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('contact', ['locale' => app()->getLocale()]) }}" class="btn-primary bg-white text-blue-600 hover:bg-blue-50">
                    Start Your Project
                    <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </a>
            </div>
        </div>
    </div>
    
    <div class="absolute bottom-0 left-0 w-full">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block w-full h-16 fill-white">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
        </svg>
    </div>
</section>

<!-- Filter Section -->
<section class="py-12 bg-white border-b border-gray-200">
    <div class="container mx-auto px-4">
        <div class="flex flex-wrap justify-center gap-4">
            <button class="filter-btn active" data-filter="all">All Projects</button>
            <button class="filter-btn" data-filter="web-development">Web Development</button>
            <button class="filter-btn" data-filter="mobile-apps">Mobile Apps</button>
            <button class="filter-btn" data-filter="ecommerce">E-commerce</button>
            <button class="filter-btn" data-filter="branding">Branding</button>
        </div>
    </div>
</section>

<!-- Projects Grid -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="projects-grid">
            <!-- Project 1 - E-commerce Platform -->
            <div class="project-card card-hover" data-category="ecommerce web-development">
                <div class="relative overflow-hidden rounded-lg mb-6">
                    <img src="{{ asset('images/projects/ecommerce-platform.jpg') }}" alt="E-commerce Platform" class="w-full h-48 object-cover hover-lift">
                    <div class="absolute inset-0 bg-blue-600 bg-opacity-0 hover:bg-opacity-90 transition-all duration-300 flex items-center justify-center">
                        <a href="#" class="btn-primary opacity-0 hover:opacity-100 transition-opacity duration-300">
                            View Project
                        </a>
                    </div>
                </div>
                <div class="space-y-2">
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded">E-commerce</span>
                        <span class="px-2 py-1 bg-green-100 text-green-600 text-xs rounded">Web Development</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900">TechStore Online</h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Complete e-commerce solution with inventory management, payment processing, and customer accounts.
                    </p>
                    <div class="flex items-center text-sm text-gray-500">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                        </svg>
                        Completed: March 2024
                    </div>
                </div>
            </div>
            
            <!-- Project 2 - Mobile App -->
            <div class="project-card card-hover" data-category="mobile-apps">
                <div class="relative overflow-hidden rounded-lg mb-6">
                    <img src="{{ asset('images/projects/fitness-app.jpg') }}" alt="Fitness Mobile App" class="w-full h-48 object-cover hover-lift">
                    <div class="absolute inset-0 bg-blue-600 bg-opacity-0 hover:bg-opacity-90 transition-all duration-300 flex items-center justify-center">
                        <a href="#" class="btn-primary opacity-0 hover:opacity-100 transition-opacity duration-300">
                            View Project
                        </a>
                    </div>
                </div>
                <div class="space-y-2">
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 bg-purple-100 text-purple-600 text-xs rounded">Mobile App</span>
                        <span class="px-2 py-1 bg-orange-100 text-orange-600 text-xs rounded">Health & Fitness</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900">FitTracker Pro</h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Cross-platform fitness app with workout tracking, nutrition planning, and social features.
                    </p>
                    <div class="flex items-center text-sm text-gray-500">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                        </svg>
                        Completed: February 2024
                    </div>
                </div>
            </div>
            
            <!-- Project 3 - Corporate Website -->
            <div class="project-card card-hover" data-category="web-development branding">
                <div class="relative overflow-hidden rounded-lg mb-6">
                    <img src="{{ asset('images/projects/corporate-website.jpg') }}" alt="Corporate Website" class="w-full h-48 object-cover hover-lift">
                    <div class="absolute inset-0 bg-blue-600 bg-opacity-0 hover:bg-opacity-90 transition-all duration-300 flex items-center justify-center">
                        <a href="#" class="btn-primary opacity-0 hover:opacity-100 transition-opacity duration-300">
                            View Project
                        </a>
                    </div>
                </div>
                <div class="space-y-2">
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 bg-green-100 text-green-600 text-xs rounded">Web Development</span>
                        <span class="px-2 py-1 bg-pink-100 text-pink-600 text-xs rounded">Branding</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900">InnovateCorp</h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Modern corporate website with custom CMS, team portal, and integrated blog system.
                    </p>
                    <div class="flex items-center text-sm text-gray-500">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                        </svg>
                        Completed: January 2024
                    </div>
                </div>
            </div>
            
            <!-- Project 4 - Restaurant App -->
            <div class="project-card card-hover" data-category="mobile-apps ecommerce">
                <div class="relative overflow-hidden rounded-lg mb-6">
                    <img src="{{ asset('images/projects/restaurant-app.jpg') }}" alt="Restaurant Mobile App" class="w-full h-48 object-cover hover-lift">
                    <div class="absolute inset-0 bg-blue-600 bg-opacity-0 hover:bg-opacity-90 transition-all duration-300 flex items-center justify-center">
                        <a href="#" class="btn-primary opacity-0 hover:opacity-100 transition-opacity duration-300">
                            View Project
                        </a>
                    </div>
                </div>
                <div class="space-y-2">
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 bg-purple-100 text-purple-600 text-xs rounded">Mobile App</span>
                        <span class="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded">E-commerce</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900">Bistro Delivery</h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Food delivery app with real-time tracking, payment integration, and loyalty rewards system.
                    </p>
                    <div class="flex items-center text-sm text-gray-500">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                        </svg>
                        Completed: December 2023
                    </div>
                </div>
            </div>
            
            <!-- Project 5 - Portfolio Website -->
            <div class="project-card card-hover" data-category="web-development branding">
                <div class="relative overflow-hidden rounded-lg mb-6">
                    <img src="{{ asset('images/projects/portfolio-website.jpg') }}" alt="Portfolio Website" class="w-full h-48 object-cover hover-lift">
                    <div class="absolute inset-0 bg-blue-600 bg-opacity-0 hover:bg-opacity-90 transition-all duration-300 flex items-center justify-center">
                        <a href="#" class="btn-primary opacity-0 hover:opacity-100 transition-opacity duration-300">
                            View Project
                        </a>
                    </div>
                </div>
                <div class="space-y-2">
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 bg-green-100 text-green-600 text-xs rounded">Web Development</span>
                        <span class="px-2 py-1 bg-pink-100 text-pink-600 text-xs rounded">Branding</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900">Creative Studio</h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Interactive portfolio website for a creative agency with stunning animations and case studies.
                    </p>
                    <div class="flex items-center text-sm text-gray-500">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                        </svg>
                        Completed: November 2023
                    </div>
                </div>
            </div>
            
            <!-- Project 6 - Learning Platform -->
            <div class="project-card card-hover" data-category="web-development mobile-apps">
                <div class="relative overflow-hidden rounded-lg mb-6">
                    <img src="{{ asset('images/projects/learning-platform.jpg') }}" alt="Learning Platform" class="w-full h-48 object-cover hover-lift">
                    <div class="absolute inset-0 bg-blue-600 bg-opacity-0 hover:bg-opacity-90 transition-all duration-300 flex items-center justify-center">
                        <a href="#" class="btn-primary opacity-0 hover:opacity-100 transition-opacity duration-300">
                            View Project
                        </a>
                    </div>
                </div>
                <div class="space-y-2">
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 bg-green-100 text-green-600 text-xs rounded">Web Development</span>
                        <span class="px-2 py-1 bg-purple-100 text-purple-600 text-xs rounded">Mobile App</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900">EduPlatform</h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Comprehensive learning management system with video streaming, assessments, and progress tracking.
                    </p>
                    <div class="flex items-center text-sm text-gray-500">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                        </svg>
                        Completed: October 2023
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Load More Button -->
        <div class="text-center mt-12">
            <button class="btn-outline">
                Load More Projects
                <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-blue-600 text-white">
    <div class="container mx-auto px-4 text-center">
        <h2 class="heading-2 text-white mb-4">
            Ready to Create Something Amazing?
        </h2>
        <p class="text-lead text-blue-100 mb-8 max-w-2xl mx-auto">
            Let's discuss your project and create a digital solution that stands out from the competition.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('contact', ['locale' => app()->getLocale()]) }}" class="btn-primary bg-white text-blue-600 hover:bg-blue-50">
                Start Your Project
                <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </a>
            <a href="{{ route('services.index', ['locale' => app()->getLocale()]) }}" class="btn-outline border-white text-white hover:bg-white hover:text-blue-600">
                Our Services
            </a>
        </div>
    </div>
</section>

@push('styles')
<style>
.filter-btn {
    @apply px-4 py-2 rounded-lg text-sm font-medium transition-colors;
    @apply bg-gray-100 text-gray-600 hover:bg-gray-200;
}

.filter-btn.active {
    @apply bg-blue-600 text-white;
}

.project-card {
    transition: transform 0.3s ease;
}

.project-card:hover {
    transform: translateY(-4px);
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const projectCards = document.querySelectorAll('.project-card');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter projects
            projectCards.forEach(card => {
                const categories = card.getAttribute('data-category');
                
                if (filter === 'all' || categories.includes(filter)) {
                    card.style.display = 'block';
                    card.classList.add('animate-fade-in');
                } else {
                    card.style.display = 'none';
                }
            });
        });
    });
});
</script>
@endpush
@endsection
