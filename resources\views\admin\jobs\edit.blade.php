@extends('layouts.dashboard')

@section('title', 'Edit Job: ' . $job->title . ' - ' . __('common.company_name'))
@section('page_title', 'Edit Job')

@section('content')
<div class="p-6 space-y-6">
    <!-- Header Section -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Edit Job: {{ $job->title }}</h1>
            <p class="mt-1 text-sm text-gray-600">Update job posting details and requirements</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('admin.jobs.show', $job) }}" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                </svg>
                View Job
            </a>
            <a href="{{ route('admin.jobs.index') }}" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                Back to Jobs
            </a>
        </div>
    </div>

    <!-- Form Messages -->
    <div id="form-messages" class="hidden"></div>

    <!-- Form -->
    <form id="job-form" method="POST" action="{{ route('admin.jobs.update', $job) }}" class="space-y-6">
        @csrf
        @method('PUT')

        <!-- Basic Information -->
        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-6">Basic Information</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Job Title -->
                <div class="md:col-span-2">
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Job Title *</label>
                    <input type="text" name="title" id="title" required
                           value="{{ old('title', $job->title) }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 @error('title') border-red-500 @enderror">
                    @error('title')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Department -->
                <div>
                    <label for="department" class="block text-sm font-medium text-gray-700 mb-1">Department *</label>
                    <input type="text" name="department" id="department" required
                           value="{{ old('department', $job->department) }}"
                           placeholder="e.g., Engineering, Marketing, Design"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 @error('department') border-red-500 @enderror">
                    @error('department')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Location -->
                <div>
                    <label for="location" class="block text-sm font-medium text-gray-700 mb-1">Location *</label>
                    <input type="text" name="location" id="location" required
                           value="{{ old('location', $job->location) }}"
                           placeholder="e.g., Cape Town, Johannesburg"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 @error('location') border-red-500 @enderror">
                    @error('location')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Employment Type -->
                <div>
                    <label for="employment_type" class="block text-sm font-medium text-gray-700 mb-1">Employment Type *</label>
                    <select name="employment_type" id="employment_type" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 @error('employment_type') border-red-500 @enderror">
                        <option value="">Select Type</option>
                        @foreach($employmentTypes as $key => $label)
                            <option value="{{ $key }}" {{ old('employment_type', $job->employment_type) === $key ? 'selected' : '' }}>{{ $label }}</option>
                        @endforeach
                    </select>
                    @error('employment_type')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Experience Level -->
                <div>
                    <label for="experience_level" class="block text-sm font-medium text-gray-700 mb-1">Experience Level *</label>
                    <select name="experience_level" id="experience_level" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 @error('experience_level') border-red-500 @enderror">
                        <option value="">Select Level</option>
                        @foreach($experienceLevels as $key => $label)
                            <option value="{{ $key }}" {{ old('experience_level', $job->experience_level) === $key ? 'selected' : '' }}>{{ $label }}</option>
                        @endforeach
                    </select>
                    @error('experience_level')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Job Description -->
        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-6">Job Description</h2>
            
            <!-- Description -->
            <div class="mb-6">
                <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Job Description *</label>
                <textarea name="description" id="description" rows="6" required
                          placeholder="Provide a detailed description of the role, what the candidate will be doing, and what makes this opportunity exciting..."
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 @error('description') border-red-500 @enderror">{{ old('description', $job->description) }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Responsibilities -->
            <div class="mb-6">
                <label for="responsibilities" class="block text-sm font-medium text-gray-700 mb-1">Key Responsibilities *</label>
                <textarea name="responsibilities" id="responsibilities" rows="5" required
                          placeholder="List the main responsibilities and duties for this role..."
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 @error('responsibilities') border-red-500 @enderror">{{ old('responsibilities', $job->responsibilities) }}</textarea>
                @error('responsibilities')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Requirements -->
            <div class="mb-6">
                <label for="requirements" class="block text-sm font-medium text-gray-700 mb-1">Requirements *</label>
                <textarea name="requirements" id="requirements" rows="5" required
                          placeholder="List the required qualifications, skills, and experience..."
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 @error('requirements') border-red-500 @enderror">{{ old('requirements', $job->requirements) }}</textarea>
                @error('requirements')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Benefits -->
            <div>
                <label for="benefits" class="block text-sm font-medium text-gray-700 mb-1">Benefits & Perks</label>
                <textarea name="benefits" id="benefits" rows="4"
                          placeholder="Describe the benefits, perks, and what makes working here great..."
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 @error('benefits') border-red-500 @enderror">{{ old('benefits', $job->benefits) }}</textarea>
                @error('benefits')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        </div>

        <!-- Compensation & Details -->
        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-6">Compensation & Details</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Salary Min -->
                <div>
                    <label for="salary_min" class="block text-sm font-medium text-gray-700 mb-1">Minimum Salary (ZAR)</label>
                    <input type="number" name="salary_min" id="salary_min" min="0"
                           value="{{ old('salary_min', $job->salary_min) }}"
                           placeholder="25000"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 @error('salary_min') border-red-500 @enderror">
                    @error('salary_min')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Salary Max -->
                <div>
                    <label for="salary_max" class="block text-sm font-medium text-gray-700 mb-1">Maximum Salary (ZAR)</label>
                    <input type="number" name="salary_max" id="salary_max" min="0"
                           value="{{ old('salary_max', $job->salary_max) }}"
                           placeholder="45000"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 @error('salary_max') border-red-500 @enderror">
                    @error('salary_max')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Salary Period -->
                <div>
                    <label for="salary_period" class="block text-sm font-medium text-gray-700 mb-1">Salary Period *</label>
                    <select name="salary_period" id="salary_period" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 @error('salary_period') border-red-500 @enderror">
                        @foreach($salaryPeriods as $key => $label)
                            <option value="{{ $key }}" {{ old('salary_period', $job->salary_period) === $key ? 'selected' : '' }}>{{ $label }}</option>
                        @endforeach
                    </select>
                    @error('salary_period')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Application Deadline -->
                <div>
                    <label for="application_deadline" class="block text-sm font-medium text-gray-700 mb-1">Application Deadline</label>
                    <input type="date" name="application_deadline" id="application_deadline"
                           value="{{ old('application_deadline', $job->application_deadline?->format('Y-m-d')) }}"
                           min="{{ date('Y-m-d', strtotime('+1 day')) }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 @error('application_deadline') border-red-500 @enderror">
                    @error('application_deadline')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Sort Order -->
                <div>
                    <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-1">Sort Order</label>
                    <input type="number" name="sort_order" id="sort_order" min="0"
                           value="{{ old('sort_order', $job->sort_order) }}"
                           placeholder="0"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 @error('sort_order') border-red-500 @enderror">
                    <p class="mt-1 text-xs text-gray-500">Lower numbers appear first</p>
                    @error('sort_order')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Settings -->
        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-6">Job Settings</h2>
            
            <div class="space-y-4">
                <!-- Remote Work -->
                <label class="flex items-center">
                    <input type="checkbox" name="is_remote" value="1" {{ old('is_remote', $job->is_remote) ? 'checked' : '' }}
                           class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                    <span class="ml-2 text-sm text-gray-700">Remote work available</span>
                </label>

                <!-- Featured -->
                <label class="flex items-center">
                    <input type="checkbox" name="is_featured" value="1" {{ old('is_featured', $job->is_featured) ? 'checked' : '' }}
                           class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                    <span class="ml-2 text-sm text-gray-700">Mark as featured job</span>
                </label>

                <!-- Active -->
                <label class="flex items-center">
                    <input type="checkbox" name="is_active" value="1" {{ old('is_active', $job->is_active) ? 'checked' : '' }}
                           class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                    <span class="ml-2 text-sm text-gray-700">Job is active and accepting applications</span>
                </label>
            </div>
        </div>

        <!-- SEO Settings -->
        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-6">SEO Settings (Optional)</h2>
            
            <div class="space-y-4">
                <!-- Meta Title -->
                <div>
                    <label for="meta_title" class="block text-sm font-medium text-gray-700 mb-1">Meta Title</label>
                    <input type="text" name="meta_title" id="meta_title"
                           value="{{ old('meta_title', $job->meta_title) }}"
                           placeholder="Custom page title for search engines"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 @error('meta_title') border-red-500 @enderror">
                    @error('meta_title')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Meta Description -->
                <div>
                    <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-1">Meta Description</label>
                    <textarea name="meta_description" id="meta_description" rows="3"
                              placeholder="Brief description for search engine results"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 @error('meta_description') border-red-500 @enderror">{{ old('meta_description', $job->meta_description) }}</textarea>
                    @error('meta_description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-end space-x-4">
            <a href="{{ route('admin.jobs.show', $job) }}" 
               class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors">
                Cancel
            </a>
            <button type="submit"
                    id="submit-btn"
                    class="px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                <span class="submit-text">Update Job</span>
                <span class="loading-text hidden">Updating...</span>
            </button>
        </div>
    </form>
</div>

@push('scripts')
<script>
// Salary validation
document.getElementById('salary_max').addEventListener('input', function() {
    const minSalary = parseFloat(document.getElementById('salary_min').value) || 0;
    const maxSalary = parseFloat(this.value) || 0;
    
    if (maxSalary > 0 && minSalary > 0 && maxSalary < minSalary) {
        this.setCustomValidity('Maximum salary must be greater than minimum salary');
    } else {
        this.setCustomValidity('');
    }
});

// AJAX Form Submission
const form = document.getElementById('job-form');
const submitBtn = document.getElementById('submit-btn');
const submitText = submitBtn.querySelector('.submit-text');
const loadingText = submitBtn.querySelector('.loading-text');
const messagesContainer = document.getElementById('form-messages');

form.addEventListener('submit', async function(e) {
    e.preventDefault();

    // Show loading state
    submitBtn.disabled = true;
    submitText.classList.add('hidden');
    loadingText.classList.remove('hidden');

    // Clear previous messages
    messagesContainer.innerHTML = '';
    messagesContainer.classList.add('hidden');

    // Clear previous error states
    document.querySelectorAll('.border-red-500').forEach(el => {
        el.classList.remove('border-red-500');
    });
    document.querySelectorAll('.text-red-600').forEach(el => {
        if (el.classList.contains('error-message')) {
            el.remove();
        }
    });

    try {
        const formData = new FormData(form);

        const response = await fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (data.success) {
            // Show success message
            messagesContainer.innerHTML = `
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-green-800">${data.message}</p>
                        </div>
                    </div>
                </div>
            `;
            messagesContainer.classList.remove('hidden');

            // Redirect after a short delay
            setTimeout(() => {
                window.location.href = data.redirect;
            }, 1500);
        } else {
            // Show error message
            messagesContainer.innerHTML = `
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-red-800">${data.message}</p>
                        </div>
                    </div>
                </div>
            `;
            messagesContainer.classList.remove('hidden');

            // Show field errors
            if (data.errors) {
                Object.keys(data.errors).forEach(field => {
                    const input = document.querySelector(`[name="${field}"]`);
                    if (input) {
                        input.classList.add('border-red-500');

                        // Add error message
                        const errorDiv = document.createElement('p');
                        errorDiv.className = 'mt-1 text-sm text-red-600 error-message';
                        errorDiv.textContent = data.errors[field][0];
                        input.parentNode.appendChild(errorDiv);
                    }
                });
            }
        }
    } catch (error) {
        console.error('Error:', error);
        messagesContainer.innerHTML = `
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-red-800">An error occurred. Please try again.</p>
                    </div>
                </div>
            </div>
        `;
        messagesContainer.classList.remove('hidden');
    } finally {
        // Reset button state
        submitBtn.disabled = false;
        submitText.classList.remove('hidden');
        loadingText.classList.add('hidden');
    }
});
</script>
@endpush
@endsection
