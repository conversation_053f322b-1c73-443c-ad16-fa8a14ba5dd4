<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'name',
        'slug',
        'short_description',
        'description',
        'sku',
        'barcode',
        'brand',
        'model_number',
        'price',
        'compare_price',
        'cost_price',
        'track_inventory',
        'inventory_quantity',
        'low_stock_threshold',
        'weight',
        'dimensions',
        'featured_image',
        'gallery',
        'is_active',
        'is_featured',
        'is_deleted',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'compare_price' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'weight' => 'decimal:2',
        'dimensions' => 'array',
        'gallery' => 'array',
        'track_inventory' => 'boolean',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'is_deleted' => 'boolean',
    ];

    protected $appends = [
        'formatted_price',
        'formatted_compare_price',
        'discount_percentage',
        'featured_image_url',
        'primary_image',
        'all_images',
        'stock_status',
        'review_count',
        'average_rating',
        'formatted_average_rating',
        'star_rating',
        'rating_distribution',
        'five_star_percentage',
        'recent_reviews',
        'top_helpful_reviews',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            if (empty($product->uuid)) {
                $product->uuid = Str::uuid();
            }
            if (empty($product->slug)) {
                $product->slug = Str::slug($product->name);
            }
        });

        static::updating(function ($product) {
            if ($product->isDirty('name') && empty($product->slug)) {
                $product->slug = Str::slug($product->name);
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Retrieve the model for a bound value.
     */
    public function resolveRouteBinding($value, $field = null)
    {
        return $this->where($field ?? $this->getRouteKeyName(), $value)
                    ->where('is_active', true)
                    ->where('is_deleted', false)
                    ->first();
    }

    /**
     * Scope a query to only include active products.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('is_deleted', false);
    }

    /**
     * Scope a query to only include featured products.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to only include products in stock.
     */
    public function scopeInStock($query)
    {
        return $query->where(function ($q) {
            $q->where('track_inventory', false)
              ->orWhere('inventory_quantity', '>', 0);
        });
    }

    /**
     * Scope a query to search products by name or description.
     * Properly escapes LIKE wildcards to prevent SQL injection.
     */
    public function scopeSearch($query, $term)
    {
        // Escape LIKE wildcards to prevent SQL injection
        $escapedTerm = str_replace(['%', '_'], ['\\%', '\\_'], $term);
        $searchTerm = "%{$escapedTerm}%";

        return $query->where(function ($q) use ($searchTerm) {
            $q->where('name', 'like', $searchTerm)
              ->orWhere('description', 'like', $searchTerm)
              ->orWhere('sku', 'like', $searchTerm);
        });
    }

    /**
     * Get the categories for the product.
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(ProductCategory::class, 'product_category_relations', 'product_id', 'category_id');
    }

    /**
     * Get the product variants.
     */
    public function variants(): HasMany
    {
        return $this->hasMany(ProductVariant::class);
    }

    /**
     * Get the cart items for this product.
     */
    public function cartItems(): HasMany
    {
        return $this->hasMany(CartItem::class);
    }

    /**
     * Check if product is in stock.
     */
    public function isInStock(): bool
    {
        if (!$this->track_inventory) {
            return true;
        }

        return $this->inventory_quantity > 0;
    }

    /**
     * Check if product is low in stock.
     */
    public function isLowStock(): bool
    {
        if (!$this->track_inventory) {
            return false;
        }

        return $this->inventory_quantity <= $this->low_stock_threshold;
    }

    /**
     * Get the formatted price.
     */
    public function getFormattedPriceAttribute(): string
    {
        return 'R' . number_format($this->price, 2);
    }

    /**
     * Get the formatted compare price.
     */
    public function getFormattedComparePriceAttribute(): string
    {
        return $this->compare_price ? 'R' . number_format($this->compare_price, 2) : '';
    }

    /**
     * Get the discount percentage.
     */
    public function getDiscountPercentageAttribute(): int
    {
        if (!$this->compare_price || $this->compare_price <= $this->price) {
            return 0;
        }

        return round((($this->compare_price - $this->price) / $this->compare_price) * 100);
    }

    /**
     * Get the featured image URL.
     * Note: This method only reads data and does not modify the database.
     * Use fixImagePaths() method separately to clean up legacy paths.
     */
    public function getFeaturedImageUrlAttribute(): string
    {
        if (!$this->featured_image) {
            return asset('images/products/placeholder.jpg');
        }

        $imagePath = $this->normalizeImagePath($this->featured_image);
        return app(\App\Services\ImageService::class)->getImageUrl($imagePath);
    }

    /**
     * Normalize image path without modifying the database.
     */
    public function normalizeImagePath(string $imagePath): string
    {
        // Handle various legacy path formats
        if (str_contains($imagePath, storage_path())) {
            // Convert full system path to relative path
            $imagePath = str_replace(storage_path('app/public/'), '', $imagePath);
            $imagePath = str_replace('\\', '/', $imagePath); // Normalize path separators
        } elseif (str_starts_with($imagePath, 'C:') || str_starts_with($imagePath, '/')) {
            // Handle other absolute paths - extract just the relative part
            $pathParts = explode('/', str_replace('\\', '/', $imagePath));
            $storageIndex = array_search('storage', $pathParts);
            if ($storageIndex !== false && isset($pathParts[$storageIndex + 1])) {
                $imagePath = implode('/', array_slice($pathParts, $storageIndex + 1));
            }
        }

        // Clean up any double slashes and ensure proper format
        return ltrim(str_replace('//', '/', $imagePath), '/');
    }

    /**
     * Fix legacy image paths in the database.
     * This should be called explicitly when needed, not during attribute access.
     */
    public function fixImagePaths(): bool
    {
        $needsUpdate = false;
        $updates = [];

        if ($this->featured_image) {
            $normalizedPath = $this->normalizeImagePath($this->featured_image);
            if ($normalizedPath !== $this->featured_image) {
                $updates['featured_image'] = $normalizedPath;
                $needsUpdate = true;
            }
        }

        if ($this->gallery && is_array($this->gallery)) {
            $normalizedGallery = array_map([$this, 'normalizeImagePath'], $this->gallery);
            if ($normalizedGallery !== $this->gallery) {
                $updates['gallery'] = $normalizedGallery;
                $needsUpdate = true;
            }
        }

        if ($needsUpdate) {
            return $this->update($updates);
        }

        return true;
    }

    /**
     * Get the primary image URL (alias for featured_image_url).
     */
    public function getPrimaryImageAttribute(): string
    {
        return $this->featured_image_url;
    }

    /**
     * Get all product images with URLs.
     */
    public function getAllImagesAttribute(): array
    {
        $images = [];

        if ($this->featured_image) {
            $images[] = $this->featured_image_url;
        }

        if ($this->gallery && is_array($this->gallery)) {
            $imageService = app(\App\Services\ImageService::class);
            foreach ($this->gallery as $imagePath) {
                $images[] = $imageService->getImageUrl($imagePath);
            }
        }

        return array_unique($images);
    }

    /**
     * Get the stock status.
     */
    public function getStockStatusAttribute(): string
    {
        if (!$this->track_inventory) {
            return 'In Stock';
        }

        if ($this->inventory_quantity <= 0) {
            return 'Out of Stock';
        }

        if ($this->isLowStock()) {
            return 'Low Stock';
        }

        return 'In Stock';
    }

    /**
     * Get the SEO title.
     */
    public function getSeoTitleAttribute(): string
    {
        return $this->meta_title ?: $this->name;
    }

    /**
     * Get the SEO description.
     */
    public function getSeoDescriptionAttribute(): string
    {
        return $this->meta_description ?: $this->short_description ?: Str::limit(strip_tags($this->description), 160);
    }

    /**
     * Decrease inventory quantity.
     * Uses atomic database operations to prevent race conditions.
     */
    public function decreaseInventory(int $quantity): bool
    {
        if (!$this->track_inventory) {
            return true;
        }

        $originalQuantity = $this->inventory_quantity;

        return \DB::transaction(function () use ($quantity, $originalQuantity) {
            // Use atomic decrement with condition to prevent negative inventory
            $affected = \DB::table('products')
                ->where('id', $this->id)
                ->where('inventory_quantity', '>=', $quantity)
                ->decrement('inventory_quantity', $quantity);

            if ($affected > 0) {
                // Refresh the model to reflect the change
                $this->refresh();

                // Log inventory change
                app(\App\Services\ActivityLogger::class)->logInventoryActivity(
                    'decrease',
                    $this->id,
                    [
                        'product_name' => $this->name,
                        'product_sku' => $this->sku,
                        'quantity_changed' => -$quantity,
                        'original_quantity' => $originalQuantity,
                        'new_quantity' => $this->inventory_quantity,
                        'low_stock_threshold' => $this->low_stock_threshold,
                        'requires_reorder' => $this->inventory_quantity <= ($this->low_stock_threshold ?? 0),
                    ]
                );

                // Check for low stock alert
                if ($this->inventory_quantity <= ($this->low_stock_threshold ?? 0) && $this->inventory_quantity > 0) {
                    app(\App\Services\ActivityLogger::class)->logInventoryActivity(
                        'low_stock_alert',
                        $this->id,
                        [
                            'product_name' => $this->name,
                            'product_sku' => $this->sku,
                            'current_quantity' => $this->inventory_quantity,
                            'low_stock_threshold' => $this->low_stock_threshold,
                        ]
                    );
                }

                // Check for out of stock
                if ($this->inventory_quantity <= 0) {
                    app(\App\Services\ActivityLogger::class)->logInventoryActivity(
                        'out_of_stock',
                        $this->id,
                        [
                            'product_name' => $this->name,
                            'product_sku' => $this->sku,
                            'last_quantity_change' => -$quantity,
                        ]
                    );
                }

                return true;
            }

            // Log failed inventory decrease
            app(\App\Services\ActivityLogger::class)->logInventoryActivity(
                'decrease',
                $this->id,
                [
                    'product_name' => $this->name,
                    'product_sku' => $this->sku,
                    'requested_quantity' => $quantity,
                    'available_quantity' => $originalQuantity,
                    'failure_reason' => 'insufficient_stock',
                ],
                false,
                'Insufficient inventory to decrease by requested amount'
            );

            return false;
        });
    }

    /**
     * Increase inventory quantity.
     */
    public function increaseInventory(int $quantity): void
    {
        if ($this->track_inventory) {
            $this->increment('inventory_quantity', $quantity);
        }
    }

    // ==================== REVIEW RELATIONSHIPS ====================

    /**
     * Get all reviews for this product.
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(ProductReview::class);
    }

    /**
     * Get approved reviews for this product.
     */
    public function approvedReviews(): HasMany
    {
        return $this->hasMany(ProductReview::class)
                    ->approved()
                    ->active()
                    ->orderBy('created_at', 'desc');
    }

    /**
     * Get featured reviews for this product.
     */
    public function featuredReviews(): HasMany
    {
        return $this->hasMany(ProductReview::class)
                    ->approved()
                    ->active()
                    ->featured()
                    ->orderBy('helpful_count', 'desc');
    }

    // ==================== REVIEW AGGREGATION METHODS ====================

    /**
     * Get the total number of approved reviews.
     */
    public function getReviewCountAttribute(): int
    {
        return $this->reviews()->approved()->active()->count();
    }

    /**
     * Get the average rating for this product.
     */
    public function getAverageRatingAttribute(): float
    {
        return $this->reviews()
                    ->approved()
                    ->active()
                    ->avg('rating') ?? 0.0;
    }

    /**
     * Get the formatted average rating (1 decimal place).
     */
    public function getFormattedAverageRatingAttribute(): string
    {
        return number_format($this->average_rating, 1);
    }

    /**
     * Get the star rating display for average rating.
     */
    public function getStarRatingAttribute(): string
    {
        $rating = round($this->average_rating);
        $stars = '';
        for ($i = 1; $i <= 5; $i++) {
            $stars .= $i <= $rating ? '★' : '☆';
        }
        return $stars;
    }

    /**
     * Get rating distribution (count per star level).
     */
    public function getRatingDistributionAttribute(): array
    {
        $distribution = [1 => 0, 2 => 0, 3 => 0, 4 => 0, 5 => 0];

        $ratings = $this->reviews()
                        ->approved()
                        ->active()
                        ->selectRaw('rating, COUNT(*) as count')
                        ->groupBy('rating')
                        ->pluck('count', 'rating')
                        ->toArray();

        return array_merge($distribution, $ratings);
    }

    /**
     * Get the percentage of 5-star reviews.
     */
    public function getFiveStarPercentageAttribute(): float
    {
        $totalReviews = $this->review_count;
        if ($totalReviews === 0) return 0.0;

        $fiveStarCount = $this->reviews()
                              ->approved()
                              ->active()
                              ->where('rating', 5)
                              ->count();

        return ($fiveStarCount / $totalReviews) * 100;
    }

    /**
     * Check if user has purchased and can review this product.
     */
    public function canBeReviewedBy(int $userId): bool
    {
        $eligibility = ProductReview::validatePurchaseEligibility($this->id, $userId);
        return $eligibility['eligible'];
    }

    /**
     * Get user's existing review for this product.
     */
    public function getUserReview(int $userId): ?ProductReview
    {
        return $this->reviews()
                    ->where('user_id', $userId)
                    ->active()
                    ->first();
    }

    /**
     * Check if user has already reviewed this product.
     */
    public function hasUserReviewed(int $userId): bool
    {
        return $this->getUserReview($userId) !== null;
    }

    /**
     * Get recent reviews (last 30 days).
     */
    public function getRecentReviewsAttribute()
    {
        return $this->reviews()
                    ->approved()
                    ->active()
                    ->where('created_at', '>=', now()->subDays(30))
                    ->orderBy('created_at', 'desc')
                    ->limit(5)
                    ->get();
    }

    /**
     * Get top helpful reviews.
     */
    public function getTopHelpfulReviewsAttribute()
    {
        return $this->reviews()
                    ->approved()
                    ->active()
                    ->where('helpful_count', '>', 0)
                    ->orderBy('helpful_count', 'desc')
                    ->limit(3)
                    ->get();
    }
}
