<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class EmailCampaign extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'name',
        'slug',
        'description',
        'type',
        'status',
        'email_template_id',
        'subject',
        'from_name',
        'from_email',
        'reply_to',
        'scheduled_at',
        'started_at',
        'completed_at',
        'is_recurring',
        'recurring_frequency',
        'recurring_settings',
        'target_segments',
        'target_criteria',
        'send_to_all',
        'track_opens',
        'track_clicks',
        'total_recipients',
        'emails_sent',
        'emails_delivered',
        'emails_bounced',
        'emails_opened',
        'emails_clicked',
        'unsubscribes',
        'parent_campaign_id',
        'sequence_order',
        'delay_days',
        'trigger_conditions',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'scheduled_at' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'is_recurring' => 'boolean',
        'recurring_settings' => 'array',
        'target_segments' => 'array',
        'target_criteria' => 'array',
        'send_to_all' => 'boolean',
        'track_opens' => 'boolean',
        'track_clicks' => 'boolean',
        'trigger_conditions' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($campaign) {
            if (empty($campaign->uuid)) {
                $campaign->uuid = Str::uuid();
            }
            if (empty($campaign->slug)) {
                $campaign->slug = Str::slug($campaign->name);
            }
        });

        static::updating(function ($campaign) {
            if ($campaign->isDirty('name') && empty($campaign->slug)) {
                $campaign->slug = Str::slug($campaign->name);
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Scope a query by status.
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query by type.
     */
    public function scopeType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope scheduled campaigns.
     */
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled')
                    ->whereNotNull('scheduled_at');
    }

    /**
     * Scope active campaigns.
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['scheduled', 'sending']);
    }

    /**
     * Get the email template.
     */
    public function emailTemplate(): BelongsTo
    {
        return $this->belongsTo(EmailTemplate::class);
    }

    /**
     * Get the parent campaign (for drip sequences).
     */
    public function parentCampaign(): BelongsTo
    {
        return $this->belongsTo(EmailCampaign::class, 'parent_campaign_id');
    }

    /**
     * Get child campaigns (drip sequence emails).
     */
    public function childCampaigns(): HasMany
    {
        return $this->hasMany(EmailCampaign::class, 'parent_campaign_id')
                   ->orderBy('sequence_order');
    }

    /**
     * Get campaign sends.
     */
    public function sends(): HasMany
    {
        return $this->hasMany(EmailCampaignSend::class);
    }

    /**
     * Get the user who created this campaign.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this campaign.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get campaign statistics.
     */
    public function getStatistics(): array
    {
        return [
            'total_recipients' => $this->total_recipients,
            'emails_sent' => $this->emails_sent,
            'emails_delivered' => $this->emails_delivered,
            'emails_bounced' => $this->emails_bounced,
            'emails_opened' => $this->emails_opened,
            'emails_clicked' => $this->emails_clicked,
            'unsubscribes' => $this->unsubscribes,
            'delivery_rate' => $this->getDeliveryRate(),
            'open_rate' => $this->getOpenRate(),
            'click_rate' => $this->getClickRate(),
            'unsubscribe_rate' => $this->getUnsubscribeRate(),
        ];
    }

    /**
     * Get delivery rate percentage.
     */
    public function getDeliveryRate(): float
    {
        if ($this->emails_sent === 0) {
            return 0;
        }
        return round(($this->emails_delivered / $this->emails_sent) * 100, 2);
    }

    /**
     * Get open rate percentage.
     */
    public function getOpenRate(): float
    {
        if ($this->emails_delivered === 0) {
            return 0;
        }
        return round(($this->emails_opened / $this->emails_delivered) * 100, 2);
    }

    /**
     * Get click rate percentage.
     */
    public function getClickRate(): float
    {
        if ($this->emails_delivered === 0) {
            return 0;
        }
        return round(($this->emails_clicked / $this->emails_delivered) * 100, 2);
    }

    /**
     * Get unsubscribe rate percentage.
     */
    public function getUnsubscribeRate(): float
    {
        if ($this->emails_delivered === 0) {
            return 0;
        }
        return round(($this->unsubscribes / $this->emails_delivered) * 100, 2);
    }

    /**
     * Check if campaign can be edited.
     */
    public function canBeEdited(): bool
    {
        return in_array($this->status, ['draft', 'scheduled']);
    }

    /**
     * Check if campaign can be deleted.
     */
    public function canBeDeleted(): bool
    {
        return in_array($this->status, ['draft', 'cancelled']);
    }

    /**
     * Check if campaign can be sent.
     */
    public function canBeSent(): bool
    {
        return $this->status === 'draft' && !empty($this->subject);
    }

    /**
     * Get campaign types.
     */
    public static function getTypes(): array
    {
        return [
            'newsletter' => 'Newsletter',
            'promotional' => 'Promotional',
            'drip' => 'Drip Campaign',
            'automated' => 'Automated',
            'welcome' => 'Welcome Series',
            'abandoned_cart' => 'Abandoned Cart',
        ];
    }

    /**
     * Get campaign statuses.
     */
    public static function getStatuses(): array
    {
        return [
            'draft' => 'Draft',
            'scheduled' => 'Scheduled',
            'sending' => 'Sending',
            'sent' => 'Sent',
            'paused' => 'Paused',
            'cancelled' => 'Cancelled',
        ];
    }

    /**
     * Get formatted type name.
     */
    public function getTypeNameAttribute(): string
    {
        $types = self::getTypes();
        return $types[$this->type] ?? ucfirst($this->type);
    }

    /**
     * Get formatted status name.
     */
    public function getStatusNameAttribute(): string
    {
        $statuses = self::getStatuses();
        return $statuses[$this->status] ?? ucfirst($this->status);
    }

    /**
     * Get status badge color.
     */
    public function getStatusBadgeColorAttribute(): string
    {
        return match($this->status) {
            'draft' => 'bg-gray-100 text-gray-800',
            'scheduled' => 'bg-blue-100 text-blue-800',
            'sending' => 'bg-yellow-100 text-yellow-800',
            'sent' => 'bg-green-100 text-green-800',
            'paused' => 'bg-orange-100 text-orange-800',
            'cancelled' => 'bg-red-100 text-red-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Mark campaign as started.
     */
    public function markAsStarted(): void
    {
        $this->update([
            'status' => 'sending',
            'started_at' => now(),
        ]);
    }

    /**
     * Mark campaign as completed.
     */
    public function markAsCompleted(): void
    {
        $this->update([
            'status' => 'sent',
            'completed_at' => now(),
        ]);
    }

    /**
     * Pause campaign.
     */
    public function pause(): void
    {
        if ($this->status === 'sending') {
            $this->update(['status' => 'paused']);
        }
    }

    /**
     * Resume campaign.
     */
    public function resume(): void
    {
        if ($this->status === 'paused') {
            $this->update(['status' => 'sending']);
        }
    }

    /**
     * Cancel campaign.
     */
    public function cancel(): void
    {
        if (in_array($this->status, ['draft', 'scheduled', 'paused'])) {
            $this->update(['status' => 'cancelled']);
        }
    }
}
