@extends('layouts.dashboard')

@section('title', 'Visitor Analytics - Admin')

@section('content')
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Visitor Analytics</h1>
            <p class="text-gray-600 mt-1">Monitor and analyze website visitor behavior</p>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.visitor-analytics.export', request()->query()) }}" 
               class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Export CSV
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Visitors</p>
                    <p class="text-2xl font-bold text-gray-900">{{ number_format($stats['total_visitors']) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Unique Visitors</p>
                    <p class="text-2xl font-bold text-green-600">{{ number_format($stats['unique_visitors']) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-orange-100 rounded-lg">
                    <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Today</p>
                    <p class="text-2xl font-bold text-orange-600">{{ number_format($stats['today_visitors']) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-red-100 rounded-lg">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Suspicious</p>
                    <p class="text-2xl font-bold text-red-600">{{ number_format($stats['suspicious_visitors']) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
        <form method="GET" action="{{ route('admin.visitor-analytics.index') }}" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label for="period" class="block text-sm font-medium text-gray-700 mb-1">Period</label>
                    <select name="period" id="period" class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500" onchange="toggleCustomDates()">
                        <option value="">All Time</option>
                        <option value="today" {{ request('period') === 'today' ? 'selected' : '' }}>Today</option>
                        <option value="yesterday" {{ request('period') === 'yesterday' ? 'selected' : '' }}>Yesterday</option>
                        <option value="week" {{ request('period') === 'week' ? 'selected' : '' }}>Last 7 Days</option>
                        <option value="month" {{ request('period') === 'month' ? 'selected' : '' }}>Last 30 Days</option>
                        <option value="custom" {{ request('period') === 'custom' ? 'selected' : '' }}>Custom Range</option>
                    </select>
                </div>

                <div id="startDateDiv" class="{{ request('period') === 'custom' ? '' : 'hidden' }}">
                    <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                    <input type="date" name="start_date" id="start_date" value="{{ request('start_date') }}" 
                           class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                </div>

                <div id="endDateDiv" class="{{ request('period') === 'custom' ? '' : 'hidden' }}">
                    <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                    <input type="date" name="end_date" id="end_date" value="{{ request('end_date') }}" 
                           class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                </div>

                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    <input type="text" name="search" id="search" value="{{ request('search') }}" 
                           placeholder="IP, location, page, referrer..."
                           class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                </div>
            </div>

            <div class="flex items-center gap-2">
                <button type="submit" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Filter
                </button>
                <a href="{{ route('admin.visitor-analytics.index') }}" class="btn-secondary">
                    Clear
                </a>
            </div>
        </form>
    </div>

    <!-- Visitor Analytics Chart -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
            <div>
                <h3 id="chartTitle" class="text-lg font-semibold text-gray-900 mb-1">Visitor Analytics Trends (Last 30 Days)</h3>
                <p class="text-sm text-gray-600">Track visitor behavior, page views, and user engagement over time</p>
            </div>

            <!-- Chart Controls -->
            <div class="flex flex-wrap items-center gap-3 mt-4 sm:mt-0">
                <!-- Chart Type Selector -->
                <div class="flex bg-gray-100 rounded-lg p-1 mr-2">
                    <button type="button" class="chart-type-btn px-3 py-1.5 text-xs font-medium rounded-md transition-colors bg-primary-600 text-white" data-type="visitors">Complete View</button>
                    <button type="button" class="chart-type-btn px-3 py-1.5 text-xs font-medium rounded-md transition-colors text-gray-600 hover:bg-gray-200" data-type="devices">Device Focus</button>
                    <button type="button" class="chart-type-btn px-3 py-1.5 text-xs font-medium rounded-md transition-colors text-gray-600 hover:bg-gray-200" data-type="engagement">Engagement</button>
                </div>

                <!-- Quick Period Buttons -->
                <div class="flex bg-gray-100 rounded-lg p-1">
                    <button type="button" class="chart-period-btn px-3 py-1.5 text-xs font-medium rounded-md transition-colors text-gray-600 hover:bg-gray-200" data-period="7">7D</button>
                    <button type="button" class="chart-period-btn px-3 py-1.5 text-xs font-medium rounded-md transition-colors text-gray-600 hover:bg-gray-200" data-period="14">14D</button>
                    <button type="button" class="chart-period-btn px-3 py-1.5 text-xs font-medium rounded-md transition-colors bg-secondary-600 text-white" data-period="30">30D</button>
                    <button type="button" class="chart-period-btn px-3 py-1.5 text-xs font-medium rounded-md transition-colors text-gray-600 hover:bg-gray-200" data-period="90">90D</button>
                </div>

                <!-- Custom Range Button -->
                <button type="button" id="customChartRangeBtn" class="px-3 py-1.5 text-xs font-medium text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    Custom
                </button>
            </div>
        </div>

        <!-- Custom Date Range (Hidden by default) -->
        <div id="customChartDateRange" class="hidden mb-4 p-4 bg-gray-50 rounded-lg">
            <div class="flex flex-wrap items-end gap-4">
                <div>
                    <label for="chartStartDate" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                    <input type="date" id="chartStartDate" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 text-sm">
                </div>
                <div>
                    <label for="chartEndDate" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                    <input type="date" id="chartEndDate" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 text-sm">
                </div>
                <div class="flex gap-2">
                    <button type="button" id="applyCustomChartRange" class="px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700 transition-colors">
                        Apply
                    </button>
                    <button type="button" id="cancelCustomChartRange" class="px-4 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50 transition-colors">
                        Cancel
                    </button>
                </div>
            </div>
        </div>

        <!-- Chart Container -->
        <div class="relative">
            <!-- Loading Overlay -->
            <div id="chartLoading" class="hidden absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg z-10">
                <div class="flex items-center space-x-3">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
                    <span class="text-sm text-gray-600">Loading chart data...</span>
                </div>
            </div>

            <!-- Chart Canvas -->
            <div class="bg-gray-50 rounded-lg p-4">
                <canvas id="visitorAnalyticsChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Chart Legend -->
        <div class="mt-6 chart-legend rounded-lg p-4 border border-gray-200">
            <div class="text-center mb-3">
                <span class="text-xs text-gray-500 font-medium">Click legend items to show/hide data series</span>
            </div>
            <div id="chartLegend" class="flex flex-wrap items-center justify-center gap-2 text-sm">
                <!-- Dynamic legend will be populated by JavaScript -->
            </div>
        </div>

        <!-- Analytics Summary -->
        <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Top Locations -->
            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
                <h4 class="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                    <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    Top Visitor Locations
                </h4>
                <div id="topLocations" class="space-y-2">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>

            <!-- Device Breakdown -->
            <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-4 border border-green-200">
                <h4 class="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                    <svg class="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                    Device Usage
                </h4>
                <div id="deviceBreakdown" class="space-y-2">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Visitors Table -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Visitor</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Page</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Device</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Visit Time</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($visitors as $visitor)
                        <tr class="hover:bg-gray-50 {{ $visitor->is_suspicious ? 'bg-red-50' : '' }}">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full {{ $visitor->is_suspicious ? 'bg-red-100' : 'bg-gray-300' }} flex items-center justify-center">
                                            @if($visitor->is_suspicious)
                                                <svg class="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                                </svg>
                                            @else
                                                <svg class="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                                </svg>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ $visitor->ip_address }}
                                            @if($visitor->is_suspicious)
                                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    Suspicious
                                                </span>
                                            @endif
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            {{ $visitor->user_agent ? Str::limit($visitor->user_agent, 50) : 'Unknown User Agent' }}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    {{ $visitor->city ?: 'Unknown' }}, {{ $visitor->country ?: 'Unknown' }}
                                </div>
                                <div class="text-sm text-gray-500">
                                    {{ $visitor->region ?: 'Unknown Region' }}
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900 truncate max-w-xs">
                                    {{ $visitor->page_url ?: 'Unknown Page' }}
                                </div>
                                @if($visitor->referrer)
                                    <div class="text-sm text-gray-500 truncate max-w-xs">
                                        From: {{ $visitor->referrer }}
                                    </div>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    {{ $visitor->device_type ?: 'Unknown' }}
                                </div>
                                <div class="text-sm text-gray-500">
                                    {{ $visitor->browser ?: 'Unknown' }} • {{ $visitor->platform ?: 'Unknown' }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <div>{{ $visitor->visited_at->format('M j, Y') }}</div>
                                <div class="text-xs">{{ $visitor->visited_at->format('g:i A') }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="{{ route('admin.visitor-analytics.show', $visitor) }}" 
                                   class="text-blue-600 hover:text-blue-900" title="View Details">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </a>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                    <h3 class="mt-2 text-sm font-medium text-gray-900">No visitors found</h3>
                                    <p class="mt-1 text-sm text-gray-500">No visitors match your current filters.</p>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if($visitors->hasPages())
            <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                {{ $visitors->links() }}
            </div>
        @endif
    </div>
</div>
@endsection

@push('styles')
<style>
/* Chart container styling */
#visitorAnalyticsChart {
    height: 300px !important;
}

/* Custom button styling for chart controls */
.chart-period-btn {
    transition: all 0.2s ease-in-out;
}

.chart-period-btn:hover {
    transform: translateY(-1px);
}

/* Loading animation */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Chart legend styling */
.chart-legend {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
function toggleCustomDates() {
    const period = document.getElementById('period').value;
    const startDateDiv = document.getElementById('startDateDiv');
    const endDateDiv = document.getElementById('endDateDiv');

    if (period === 'custom') {
        startDateDiv.classList.remove('hidden');
        endDateDiv.classList.remove('hidden');
    } else {
        startDateDiv.classList.add('hidden');
        endDateDiv.classList.add('hidden');
    }
}

// Visitor Analytics Chart Implementation
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('visitorAnalyticsChart').getContext('2d');
    let visitorChart;
    let currentChartType = 'visitors';
    let currentData = null;

    // Initialize chart with default data (30 days)
    initChart(30);

    // Initialize chart function
    function initChart(period, startDate = null, endDate = null) {
        if (visitorChart) {
            visitorChart.destroy();
        }

        // Show loading
        showChartLoading();

        // Fetch chart data
        fetchChartData(period, startDate, endDate);
    }

    // Chart type configurations
    const chartConfigs = {
        visitors: {
            title: 'Comprehensive Visitor Analytics',
            datasets: [
                { key: 'unique_visitors', label: 'Unique Visitors', color: 'rgb(59, 130, 246)', fill: true },
                { key: 'page_views', label: 'Page Views', color: 'rgb(16, 185, 129)', fill: false },
                { key: 'new_visitors', label: 'New Visitors', color: 'rgb(147, 51, 234)', fill: false },
                { key: 'returning_visitors', label: 'Returning Visitors', color: 'rgb(245, 158, 11)', fill: false },
                { key: 'mobile_visitors', label: 'Mobile Users', color: 'rgb(34, 197, 94)', fill: false },
                { key: 'desktop_visitors', label: 'Desktop Users', color: 'rgb(99, 102, 241)', fill: false },
                { key: 'tablet_visitors', label: 'Tablet Users', color: 'rgb(168, 85, 247)', fill: false }
            ]
        },
        devices: {
            title: 'Device Usage Analytics',
            datasets: [
                { key: 'mobile_visitors', label: 'Mobile Visitors', color: 'rgb(34, 197, 94)', fill: true },
                { key: 'desktop_visitors', label: 'Desktop Visitors', color: 'rgb(59, 130, 246)', fill: false },
                { key: 'tablet_visitors', label: 'Tablet Visitors', color: 'rgb(168, 85, 247)', fill: false },
                { key: 'unique_visitors', label: 'Total Unique Visitors', color: 'rgb(156, 163, 175)', fill: false }
            ]
        },
        engagement: {
            title: 'Engagement & Performance Analytics',
            datasets: [
                { key: 'form_submissions', label: 'Form Submissions', color: 'rgb(34, 197, 94)', fill: true },
                { key: 'conversions', label: 'Conversions', color: 'rgb(245, 158, 11)', fill: false },
                { key: 'bounce_visitors', label: 'Bounce Visitors', color: 'rgb(239, 68, 68)', fill: false },
                { key: 'suspicious_visitors', label: 'Suspicious Visitors', color: 'rgb(156, 163, 175)', fill: false }
            ]
        }
    };

    // Create chart with data
    function createChart(data) {
        currentData = data;
        const config = chartConfigs[currentChartType];

        // Destroy existing chart if it exists
        if (visitorChart) {
            visitorChart.destroy();
            visitorChart = null;
        }

        const datasets = config.datasets.map(dataset => ({
            label: dataset.label,
            data: data.map(item => item[dataset.key] || 0),
            borderColor: dataset.color,
            backgroundColor: dataset.fill ? dataset.color.replace('rgb', 'rgba').replace(')', ', 0.1)') : 'transparent',
            tension: 0.1,
            fill: dataset.fill,
            pointBackgroundColor: dataset.color,
            pointBorderColor: '#fff',
            pointBorderWidth: 2,
            pointRadius: 4,
            pointHoverRadius: 6
        }));

        visitorChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.map(item => {
                    const date = new Date(item.date);
                    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                }),
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                plugins: {
                    legend: {
                        display: false // We have custom legend
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: 'rgba(255, 255, 255, 0.1)',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: true,
                        callbacks: {
                            title: function(context) {
                                const date = new Date(data[context[0].dataIndex].date);
                                return date.toLocaleDateString('en-US', {
                                    weekday: 'long',
                                    year: 'numeric',
                                    month: 'long',
                                    day: 'numeric'
                                });
                            },
                            label: function(context) {
                                return context.dataset.label + ': ' + context.parsed.y.toLocaleString();
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        display: true,
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6B7280',
                            font: {
                                size: 12
                            }
                        }
                    },
                    y: {
                        display: true,
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(107, 114, 128, 0.1)'
                        },
                        ticks: {
                            color: '#6B7280',
                            font: {
                                size: 12
                            },
                            callback: function(value) {
                                return value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });

        // Update legend
        updateChartLegend(config);

        hideChartLoading();
    }

    // Update chart legend
    function updateChartLegend(config) {
        const legendContainer = document.getElementById('chartLegend');
        legendContainer.innerHTML = '';

        config.datasets.forEach((dataset, index) => {
            const legendItem = document.createElement('div');
            legendItem.className = 'flex items-center cursor-pointer hover:bg-gray-100 rounded-lg p-2 transition-colors';
            legendItem.dataset.datasetIndex = index;
            legendItem.innerHTML = `
                <div class="w-3 h-3 rounded-full mr-2 shadow-sm legend-color" style="background-color: ${dataset.color}"></div>
                <span class="text-gray-700 font-medium legend-label">${dataset.label}</span>
            `;

            // Add click handler for toggling dataset visibility
            legendItem.addEventListener('click', function() {
                const datasetIndex = parseInt(this.dataset.datasetIndex);
                const meta = visitorChart.getDatasetMeta(datasetIndex);

                // Toggle visibility
                meta.hidden = meta.hidden === null ? !visitorChart.data.datasets[datasetIndex].hidden : null;

                // Update legend appearance
                const colorElement = this.querySelector('.legend-color');
                const labelElement = this.querySelector('.legend-label');

                if (meta.hidden) {
                    colorElement.style.backgroundColor = '#d1d5db'; // Gray color when hidden
                    labelElement.classList.add('text-gray-400');
                    labelElement.classList.remove('text-gray-700');
                    this.classList.add('opacity-50');
                } else {
                    colorElement.style.backgroundColor = dataset.color;
                    labelElement.classList.remove('text-gray-400');
                    labelElement.classList.add('text-gray-700');
                    this.classList.remove('opacity-50');
                }

                // Update chart
                visitorChart.update();
            });

            legendContainer.appendChild(legendItem);
        });
    }

    // Switch chart type
    function switchChartType(type) {
        currentChartType = type;

        // Destroy existing chart before creating new one
        if (visitorChart) {
            visitorChart.destroy();
            visitorChart = null;
        }

        if (currentData) {
            createChart(currentData);
        }

        // Update chart type buttons
        document.querySelectorAll('.chart-type-btn').forEach(btn => {
            btn.classList.remove('bg-primary-600', 'text-white');
            btn.classList.add('text-gray-600', 'hover:bg-gray-200');
        });

        const activeBtn = document.querySelector(`[data-type="${type}"]`);
        if (activeBtn) {
            activeBtn.classList.remove('text-gray-600', 'hover:bg-gray-200');
            activeBtn.classList.add('bg-primary-600', 'text-white');
        }

        // Update chart title
        const config = chartConfigs[type];
        updateChartTitle(config.title + ' Trends');
    }

    // Update location summary
    function updateLocationSummary(locationData) {
        const container = document.getElementById('topLocations');
        container.innerHTML = '';

        if (locationData && locationData.length > 0) {
            locationData.slice(0, 5).forEach((location, index) => {
                const item = document.createElement('div');
                item.className = 'flex items-center justify-between text-sm';
                item.innerHTML = `
                    <div class="flex items-center">
                        <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mr-3">${index + 1}</span>
                        <span class="text-gray-700 font-medium">${location.location}</span>
                    </div>
                    <div class="text-right">
                        <div class="text-gray-900 font-semibold">${location.unique_visitors.toLocaleString()}</div>
                        <div class="text-gray-500 text-xs">${location.page_views.toLocaleString()} views</div>
                    </div>
                `;
                container.appendChild(item);
            });
        } else {
            container.innerHTML = '<div class="text-gray-500 text-sm">No location data available</div>';
        }
    }

    // Update device summary
    function updateDeviceSummary(deviceData) {
        const container = document.getElementById('deviceBreakdown');
        container.innerHTML = '';

        if (deviceData && deviceData.length > 0) {
            const deviceIcons = {
                'mobile': '📱',
                'desktop': '🖥️',
                'tablet': '📱',
                'unknown': '❓'
            };

            deviceData.forEach(device => {
                const deviceType = device.device_type || 'unknown';
                const icon = deviceIcons[deviceType.toLowerCase()] || deviceIcons['unknown'];

                const item = document.createElement('div');
                item.className = 'flex items-center justify-between text-sm';
                item.innerHTML = `
                    <div class="flex items-center">
                        <span class="text-lg mr-3">${icon}</span>
                        <span class="text-gray-700 font-medium capitalize">${deviceType}</span>
                    </div>
                    <div class="text-right">
                        <div class="text-gray-900 font-semibold">${device.unique_visitors.toLocaleString()}</div>
                        <div class="text-gray-500 text-xs">${device.page_views.toLocaleString()} views</div>
                    </div>
                `;
                container.appendChild(item);
            });
        } else {
            container.innerHTML = '<div class="text-gray-500 text-sm">No device data available</div>';
        }
    }

    // Show/hide loading overlay
    function showChartLoading() {
        document.getElementById('chartLoading').classList.remove('hidden');
    }

    function hideChartLoading() {
        document.getElementById('chartLoading').classList.add('hidden');
    }

    // Update chart title
    function updateChartTitle(title) {
        document.getElementById('chartTitle').textContent = title;
    }

    // Update period button states
    function updatePeriodButtons(activePeriod) {
        document.querySelectorAll('.chart-period-btn').forEach(btn => {
            btn.classList.remove('bg-primary-600', 'text-white');
            btn.classList.add('text-gray-600', 'hover:bg-gray-200');
        });

        if (activePeriod !== 'custom') {
            const activeBtn = document.querySelector(`[data-period="${activePeriod}"]`);
            if (activeBtn) {
                activeBtn.classList.remove('text-gray-600', 'hover:bg-gray-200');
                activeBtn.classList.add('bg-secondary-600', 'text-white');
            }
        }
    }

    // Fetch chart data via AJAX
    function fetchChartData(period, startDate = null, endDate = null) {
        const params = new URLSearchParams({ period });
        if (period === 'custom' && startDate && endDate) {
            params.append('start_date', startDate);
            params.append('end_date', endDate);
        }

        const url = `{{ route('admin.visitor-analytics.chart-data') }}?${params}`;

        fetch(url, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                createChart(data.data);
                updateChartTitle(data.title || `Visitor Analytics Trends (${period} days)`);
                updatePeriodButtons(data.period || period);

                // Update summary sections
                if (data.location_data) {
                    updateLocationSummary(data.location_data);
                }
                if (data.device_data) {
                    updateDeviceSummary(data.device_data);
                }
            } else {
                console.error('Failed to fetch chart data:', data);
                alert('Failed to load chart data. Please try again.');
                hideChartLoading();
            }
        })
        .catch(error => {
            console.error('Error fetching chart data:', error);
            alert('An error occurred while loading chart data. Please try again.');
            hideChartLoading();
        });
    }

    // Chart type button event listeners
    document.querySelectorAll('.chart-type-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const type = this.dataset.type;
            switchChartType(type);
        });
    });

    // Period button event listeners
    document.querySelectorAll('.chart-period-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const period = this.dataset.period;
            initChart(period);

            // Hide custom date range if visible
            document.getElementById('customChartDateRange').classList.add('hidden');
        });
    });

    // Custom range button event listener
    document.getElementById('customChartRangeBtn').addEventListener('click', function() {
        const customRange = document.getElementById('customChartDateRange');
        customRange.classList.toggle('hidden');

        // Set default dates
        if (!customRange.classList.contains('hidden')) {
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 30);

            document.getElementById('chartStartDate').value = startDate.toISOString().split('T')[0];
            document.getElementById('chartEndDate').value = endDate.toISOString().split('T')[0];
        }
    });

    // Apply custom range
    document.getElementById('applyCustomChartRange').addEventListener('click', function() {
        const startDate = document.getElementById('chartStartDate').value;
        const endDate = document.getElementById('chartEndDate').value;

        if (!startDate || !endDate) {
            alert('Please select both start and end dates.');
            return;
        }

        if (new Date(startDate) > new Date(endDate)) {
            alert('Start date must be before or equal to end date.');
            return;
        }

        initChart('custom', startDate, endDate);
        document.getElementById('customChartDateRange').classList.add('hidden');
    });

    // Cancel custom range
    document.getElementById('cancelCustomChartRange').addEventListener('click', function() {
        document.getElementById('customChartDateRange').classList.add('hidden');
    });
});
</script>
@endpush
