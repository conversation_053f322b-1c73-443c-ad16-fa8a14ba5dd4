<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\ChatAnalyticsService;
use App\Services\ChatAIService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

class ChatAnalyticsController extends Controller
{
    protected ChatAnalyticsService $analyticsService;
    protected ChatAIService $aiService;

    public function __construct(ChatAnalyticsService $analyticsService, ChatAIService $aiService)
    {
        $this->analyticsService = $analyticsService;
        $this->aiService = $aiService;

        // Simplified middleware for testing
        $this->middleware('auth');
    }

    /**
     * Display the analytics dashboard.
     */
    public function index(): View
    {
        $metrics = $this->analyticsService->getDashboardMetrics();
        $staffPerformance = $this->analyticsService->getStaffPerformance();
        $customerSatisfaction = $this->analyticsService->getCustomerSatisfaction();

        return view('admin.chat.analytics.index', compact(
            'metrics',
            'staffPerformance',
            'customerSatisfaction'
        ));
    }

    /**
     * Get real-time metrics via AJAX.
     */
    public function getRealTimeMetrics(): JsonResponse
    {
        $metrics = $this->analyticsService->getDashboardMetrics();
        
        return response()->json([
            'success' => true,
            'data' => $metrics['real_time'],
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get AI performance metrics.
     */
    public function getAIMetrics(): JsonResponse
    {
        try {
            $metrics = $this->aiService->getPerformanceMetrics();
            
            return response()->json([
                'success' => true,
                'data' => $metrics,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Unable to fetch AI metrics',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get conversation trends data.
     */
    public function getTrends(Request $request): JsonResponse
    {
        $period = $request->get('period', 'week'); // day, week, month
        $metrics = $this->analyticsService->getDashboardMetrics();
        
        $trendsData = match($period) {
            'day' => $metrics['trends']['hourly_activity'],
            'week' => $metrics['week']['daily_breakdown'],
            'month' => $metrics['month']['weekly_breakdown'],
            default => $metrics['trends']['daily_conversations'],
        };

        return response()->json([
            'success' => true,
            'data' => $trendsData,
            'period' => $period,
        ]);
    }

    /**
     * Get staff performance data.
     */
    public function getStaffPerformance(): JsonResponse
    {
        $performance = $this->analyticsService->getStaffPerformance();
        
        return response()->json([
            'success' => true,
            'data' => $performance,
        ]);
    }

    /**
     * Get customer satisfaction data.
     */
    public function getCustomerSatisfaction(): JsonResponse
    {
        $satisfaction = $this->analyticsService->getCustomerSatisfaction();
        
        return response()->json([
            'success' => true,
            'data' => $satisfaction,
        ]);
    }

    /**
     * Export analytics report.
     */
    public function exportReport(Request $request): JsonResponse
    {
        $format = $request->get('format', 'json'); // json, csv, pdf
        $period = $request->get('period', 'month');
        
        try {
            $metrics = $this->analyticsService->getDashboardMetrics();
            $staffPerformance = $this->analyticsService->getStaffPerformance();
            $customerSatisfaction = $this->analyticsService->getCustomerSatisfaction();
            
            $reportData = [
                'generated_at' => now()->toISOString(),
                'period' => $period,
                'metrics' => $metrics,
                'staff_performance' => $staffPerformance,
                'customer_satisfaction' => $customerSatisfaction,
            ];

            switch ($format) {
                case 'csv':
                    return $this->exportToCsv($reportData);
                case 'pdf':
                    return $this->exportToPdf($reportData);
                default:
                    return response()->json([
                        'success' => true,
                        'data' => $reportData,
                        'download_url' => null,
                    ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Export failed',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get analytics summary for dashboard widgets.
     */
    public function getSummary(): JsonResponse
    {
        $metrics = $this->analyticsService->getDashboardMetrics();
        
        $summary = [
            'active_conversations' => $metrics['real_time']['active_conversations'],
            'waiting_customers' => $metrics['real_time']['waiting_customers'],
            'online_staff' => $metrics['real_time']['online_staff'],
            'avg_response_time' => $metrics['real_time']['avg_response_time'],
            'today_conversations' => $metrics['today']['total_conversations'],
            'today_messages' => $metrics['today']['total_messages'],
            'escalation_rate' => $metrics['today']['escalation_rate'],
            'growth_rate' => $metrics['month']['growth_rate'] ?? 0,
        ];

        return response()->json([
            'success' => true,
            'data' => $summary,
        ]);
    }

    /**
     * Export data to CSV format.
     */
    protected function exportToCsv(array $data): JsonResponse
    {
        // For now, return JSON with CSV structure
        // In a real implementation, this would generate and return a CSV file
        return response()->json([
            'success' => true,
            'format' => 'csv',
            'data' => $data,
            'download_url' => '/admin/chat/analytics/download/csv/' . time(),
        ]);
    }

    /**
     * Export data to PDF format.
     */
    protected function exportToPdf(array $data): JsonResponse
    {
        // For now, return JSON with PDF structure
        // In a real implementation, this would generate and return a PDF file
        return response()->json([
            'success' => true,
            'format' => 'pdf',
            'data' => $data,
            'download_url' => '/admin/chat/analytics/download/pdf/' . time(),
        ]);
    }

    /**
     * Get historical data for charts.
     */
    public function getHistoricalData(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $metric = $request->get('metric', 'conversations'); // conversations, messages, ai_interactions
        
        $metrics = $this->analyticsService->getDashboardMetrics();
        
        // For now, return sample data
        // In a real implementation, this would query historical data
        $historicalData = [];
        for ($i = $days; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $historicalData[] = [
                'date' => $date,
                'value' => rand(10, 100), // Sample data
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $historicalData,
            'metric' => $metric,
            'period' => $days . ' days',
        ]);
    }

    /**
     * Get peak usage analysis.
     */
    public function getPeakUsage(): JsonResponse
    {
        $metrics = $this->analyticsService->getDashboardMetrics();
        
        return response()->json([
            'success' => true,
            'data' => [
                'peak_hours' => $metrics['trends']['peak_hours'],
                'busiest_days' => $metrics['trends']['busiest_days'],
                'hourly_activity' => $metrics['trends']['hourly_activity'],
            ],
        ]);
    }
}
