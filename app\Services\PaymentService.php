<?php

namespace App\Services;

use App\Models\Order;
use App\Models\Payment;
use App\Services\EmailNotificationService;
use App\Services\ActivityLogger;
use App\Services\VisitorAnalytics;
use Stripe\Stripe;
use Stripe\Charge;
use Stripe\PaymentIntent;
use Stripe\Exception\CardException;
use Stripe\Exception\ApiErrorException;
use Illuminate\Support\Facades\Log;

class PaymentService
{
    protected ActivityLogger $activityLogger;
    protected VisitorAnalytics $visitorAnalytics;

    public function __construct(ActivityLogger $activityLogger = null, VisitorAnalytics $visitorAnalytics = null)
    {
        Stripe::setApiKey(config('services.stripe.secret'));
        $this->activityLogger = $activityLogger ?? app(ActivityLogger::class);
        $this->visitorAnalytics = $visitorAnalytics ?? app(VisitorAnalytics::class);
    }

    /**
     * Process a Stripe payment
     */
    public function processStripePayment(Order $order, string $token, array $metadata = []): array
    {
        // Log payment initiation
        $this->activityLogger->logPaymentActivity(
            'initiate',
            null,
            [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'payment_method' => 'stripe',
                'amount' => $order->total_amount,
                'currency' => $order->currency?->code,
                'customer_email' => $order->email,
                'customer_type' => $order->user_id ? 'registered' : 'guest',
                'cart_type' => $order->user_id ? 'user' : 'session',
                'user_authenticated' => $order->user_id !== null,
                'metadata' => $metadata
            ],
            true
        );

        // Log checkout activity for payment processing
        $this->activityLogger->logCheckoutActivity(
            'payment_completed',
            [
                'order_id' => $order->id,
                'payment_method' => 'stripe',
                'amount' => $order->total_amount,
                'processing_started' => true,
                'customer_type' => $order->user_id ? 'registered' : 'guest',
                'cart_type' => $order->user_id ? 'user' : 'session',
                'user_authenticated' => $order->user_id !== null
            ],
            true,
            null,
            $order->id,
            []
        );

        try {
            // Create the charge
            $charge = Charge::create([
                'amount' => $this->convertToStripeAmount($order->total_amount),
                'currency' => strtolower($order->currency?->code ?? 'usd'),
                'source' => $token,
                'description' => "Order #{$order->order_number}",
                'metadata' => array_merge([
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'customer_email' => $order->email,
                ], $metadata),
            ]);

            // Create payment record
            $payment = Payment::create([
                'order_id' => $order->id,
                'payment_method' => 'stripe',
                'payment_gateway' => 'stripe',
                'transaction_id' => $charge->id,
                'amount' => $order->total_amount,
                'currency' => $order->currency?->code,
                'status' => 'completed',
                'gateway_response' => json_encode($charge->toArray()),
                'processed_at' => now(),
            ]);

            // Update order status
            $order->update([
                'payment_status' => 'paid',
                'status' => 'processing',
            ]);

            // Log successful payment processing
            $this->activityLogger->logPaymentActivity(
                'complete',
                $payment->id,
                [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'payment_method' => 'stripe',
                    'transaction_id' => $charge->id,
                    'amount' => $order->total_amount,
                    'currency' => $order->currency?->code,
                    'stripe_charge_id' => $charge->id,
                    'stripe_status' => $charge->status,
                    'customer_email' => $order->email,
                    'gateway_response_summary' => [
                        'id' => $charge->id,
                        'status' => $charge->status,
                        'paid' => $charge->paid,
                        'amount' => $charge->amount,
                        'currency' => $charge->currency,
                        'created' => $charge->created
                    ]
                ],
                true
            );

            // Log successful checkout completion
            $this->activityLogger->logCheckoutActivity(
                'payment_completed',
                [
                    'order_id' => $order->id,
                    'payment_id' => $payment->id,
                    'transaction_id' => $charge->id,
                    'payment_successful' => true,
                    'order_status_updated' => true,
                    'final_order_status' => 'processing',
                    'final_payment_status' => 'paid'
                ],
                true,
                null,
                $order->id,
                []
            );

            // Track successful payment in visitor analytics
            $this->visitorAnalytics->trackConversion('payment_completed', [
                'order_id' => $order->id,
                'payment_method' => 'stripe',
                'transaction_id' => $charge->id,
                'amount' => $order->total_amount,
                'currency' => $order->currency?->code
            ]);

            // Send payment received email
            try {
                $emailService = new EmailNotificationService();
                $emailService->sendPaymentReceived($order, $payment);

                // Log successful email notification
                $this->activityLogger->logCheckoutActivity(
                    'confirmation_sent',
                    [
                        'order_id' => $order->id,
                        'payment_id' => $payment->id,
                        'email_type' => 'payment_received',
                        'recipient' => $order->email
                    ],
                    true,
                    null,
                    $order->id,
                    []
                );
            } catch (\Exception $emailException) {
                // Log email failure but don't fail the payment
                $this->activityLogger->logCheckoutActivity(
                    'confirmation_sent',
                    [
                        'order_id' => $order->id,
                        'payment_id' => $payment->id,
                        'email_type' => 'payment_received',
                        'recipient' => $order->email
                    ],
                    false,
                    'Failed to send payment confirmation email: ' . $emailException->getMessage(),
                    $order->id,
                    []
                );

                Log::warning('Payment confirmation email failed', [
                    'order_id' => $order->id,
                    'payment_id' => $payment->id,
                    'error' => $emailException->getMessage()
                ]);
            }

            return [
                'success' => true,
                'payment' => $payment,
                'charge' => $charge,
                'transaction_id' => $charge->id,
                'message' => 'Payment processed successfully',
            ];

        } catch (CardException $e) {
            // Card was declined - log comprehensive error details
            $errorData = [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'payment_method' => 'stripe',
                'amount' => $order->total_amount,
                'currency' => $order->currency?->code,
                'error_type' => 'card_declined',
                'error_message' => $e->getMessage(),
                'stripe_code' => $e->getStripeCode(),
                'decline_code' => $e->getDeclineCode(),
                'customer_email' => $order->email,
                'metadata' => $metadata
            ];

            // Log payment failure
            $this->activityLogger->logPaymentActivity(
                'process',
                null,
                $errorData,
                false,
                'Card declined: ' . $e->getMessage()
            );

            // Log checkout payment failure
            $this->activityLogger->logCheckoutActivity(
                'payment_completed',
                array_merge($errorData, [
                    'payment_failed' => true,
                    'failure_type' => 'card_declined'
                ]),
                false,
                'Payment failed - card declined: ' . $e->getMessage(),
                $order->id,
                []
            );

            // Track failed payment in visitor analytics
            $this->visitorAnalytics->trackFormInteraction('payment_form', 'submit', false, [
                'order_id' => $order->id,
                'error_type' => 'card_declined',
                'error_code' => $e->getStripeCode(),
                'decline_code' => $e->getDeclineCode()
            ]);

            // Log security event for potential fraud
            if (in_array($e->getDeclineCode(), ['stolen_card', 'lost_card', 'fraudulent'])) {
                $this->activityLogger->logSecurityEvent(
                    'payment_fraud_attempt',
                    [
                        'order_id' => $order->id,
                        'decline_code' => $e->getDeclineCode(),
                        'customer_email' => $order->email,
                        'amount' => $order->total_amount
                    ],
                    'critical'
                );
            }

            Log::error('Stripe Card Error', $errorData);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'type' => 'card_error',
                'decline_code' => $e->getDeclineCode(),
                'stripe_code' => $e->getStripeCode(),
            ];

        } catch (ApiErrorException $e) {
            // Stripe API error - log comprehensive error details
            $errorData = [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'payment_method' => 'stripe',
                'amount' => $order->total_amount,
                'currency' => $order->currency?->code,
                'error_type' => 'api_error',
                'error_message' => $e->getMessage(),
                'stripe_code' => $e->getStripeCode(),
                'http_status' => $e->getHttpStatus(),
                'customer_email' => $order->email,
                'metadata' => $metadata
            ];

            // Log payment API failure
            $this->activityLogger->logPaymentActivity(
                'process',
                null,
                $errorData,
                false,
                'Stripe API error: ' . $e->getMessage()
            );

            // Log checkout payment failure
            $this->activityLogger->logCheckoutActivity(
                'payment_completed',
                array_merge($errorData, [
                    'payment_failed' => true,
                    'failure_type' => 'api_error'
                ]),
                false,
                'Payment failed - Stripe API error: ' . $e->getMessage(),
                $order->id,
                []
            );

            // Track API error in visitor analytics
            $this->visitorAnalytics->trackError('stripe_api_error', [
                'order_id' => $order->id,
                'error_code' => $e->getStripeCode(),
                'http_status' => $e->getHttpStatus()
            ]);

            Log::error('Stripe API Error', $errorData);

            return [
                'success' => false,
                'error' => 'Payment processing failed. Please try again.',
                'type' => 'api_error',
                'stripe_code' => $e->getStripeCode(),
                'http_status' => $e->getHttpStatus(),
            ];

        } catch (\Exception $e) {
            // General error - log comprehensive error details
            $errorData = [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'payment_method' => 'stripe',
                'amount' => $order->total_amount,
                'currency' => $order->currency?->code,
                'error_type' => 'general_error',
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'customer_email' => $order->email,
                'metadata' => $metadata
            ];

            // Log payment processing failure
            $this->activityLogger->logPaymentActivity(
                'process',
                null,
                $errorData,
                false,
                'Payment processing failed: ' . $e->getMessage()
            );

            // Log checkout payment failure
            $this->activityLogger->logCheckoutActivity(
                'payment_completed',
                array_merge($errorData, [
                    'payment_failed' => true,
                    'failure_type' => 'general_error',
                    'exception_class' => get_class($e)
                ]),
                false,
                'Payment failed - unexpected error: ' . $e->getMessage(),
                $order->id,
                []
            );

            // Track general error in visitor analytics
            $this->visitorAnalytics->trackError('payment_processing_error', [
                'order_id' => $order->id,
                'error_type' => 'general_error',
                'exception_class' => get_class($e)
            ]);

            Log::error('Payment Processing Error', $errorData);

            return [
                'success' => false,
                'error' => 'An unexpected error occurred. Please try again.',
                'type' => 'general_error',
            ];
        }
    }

    /**
     * Create a PaymentIntent for Stripe (for SCA compliance)
     */
    public function createPaymentIntent(Order $order, array $metadata = []): array
    {
        try {
            $paymentIntent = PaymentIntent::create([
                'amount' => $this->convertToStripeAmount($order->total),
                'currency' => strtolower($order->currency),
                'description' => "Order #{$order->order_number}",
                'metadata' => array_merge([
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'customer_email' => $order->email,
                ], $metadata),
                'automatic_payment_methods' => [
                    'enabled' => true,
                ],
            ]);

            return [
                'success' => true,
                'payment_intent' => $paymentIntent,
                'client_secret' => $paymentIntent->client_secret,
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe PaymentIntent Error', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => 'Failed to initialize payment. Please try again.',
            ];
        }
    }

    /**
     * Process PayPal payment (placeholder for future implementation)
     */
    public function processPayPalPayment(Order $order, array $paypalData): array
    {
        // TODO: Implement PayPal payment processing
        return [
            'success' => false,
            'error' => 'PayPal payment not yet implemented',
        ];
    }

    /**
     * Refund a payment
     */
    public function refundPayment(Payment $payment, float $amount = null): array
    {
        try {
            if ($payment->payment_gateway !== 'stripe') {
                return [
                    'success' => false,
                    'error' => 'Refunds only supported for Stripe payments',
                ];
            }

            $refundAmount = $amount ? $this->convertToStripeAmount($amount) : null;

            $refund = \Stripe\Refund::create([
                'charge' => $payment->transaction_id,
                'amount' => $refundAmount,
            ]);

            // Update payment record
            $payment->update([
                'status' => $refundAmount ? 'partially_refunded' : 'refunded',
                'refunded_amount' => ($payment->refunded_amount ?? 0) + ($amount ?? $payment->amount),
                'gateway_response' => json_encode(array_merge(
                    json_decode($payment->gateway_response, true) ?? [],
                    ['refund' => $refund->toArray()]
                )),
            ]);

            return [
                'success' => true,
                'refund' => $refund,
                'message' => 'Refund processed successfully',
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe Refund Error', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => 'Refund processing failed. Please try again.',
            ];
        }
    }

    /**
     * Convert amount to Stripe format (cents)
     */
    private function convertToStripeAmount(float $amount): int
    {
        return (int) round($amount * 100);
    }

    /**
     * Convert amount from Stripe format (cents) to decimal
     */
    private function convertFromStripeAmount(int $amount): float
    {
        return $amount / 100;
    }

    /**
     * Verify webhook signature
     */
    public function verifyWebhookSignature(string $payload, string $signature): bool
    {
        try {
            \Stripe\Webhook::constructEvent(
                $payload,
                $signature,
                config('services.stripe.webhook_secret')
            );
            return true;
        } catch (\Exception $e) {
            Log::error('Stripe Webhook Signature Verification Failed', [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Handle webhook events
     */
    public function handleWebhookEvent(array $event): void
    {
        switch ($event['type']) {
            case 'payment_intent.succeeded':
                $this->handlePaymentIntentSucceeded($event['data']['object']);
                break;

            case 'payment_intent.payment_failed':
                $this->handlePaymentIntentFailed($event['data']['object']);
                break;

            case 'charge.dispute.created':
                $this->handleChargeDispute($event['data']['object']);
                break;

            default:
                Log::info('Unhandled Stripe webhook event', ['type' => $event['type']]);
        }
    }

    /**
     * Handle successful payment intent
     */
    private function handlePaymentIntentSucceeded(array $paymentIntent): void
    {
        $orderId = $paymentIntent['metadata']['order_id'] ?? null;
        
        if ($orderId) {
            $order = Order::find($orderId);
            if ($order && $order->payment_status !== 'paid') {
                $order->update([
                    'payment_status' => 'paid',
                    'status' => 'processing',
                ]);

                // Create payment record if it doesn't exist
                Payment::firstOrCreate(
                    ['transaction_id' => $paymentIntent['id']],
                    [
                        'order_id' => $order->id,
                        'payment_method' => 'stripe',
                        'payment_gateway' => 'stripe',
                        'amount' => $this->convertFromStripeAmount($paymentIntent['amount']),
                        'currency' => strtoupper($paymentIntent['currency']),
                        'status' => 'completed',
                        'gateway_response' => json_encode($paymentIntent),
                        'processed_at' => now(),
                    ]
                );
            }
        }
    }

    /**
     * Handle failed payment intent
     */
    private function handlePaymentIntentFailed(array $paymentIntent): void
    {
        $orderId = $paymentIntent['metadata']['order_id'] ?? null;
        
        if ($orderId) {
            $order = Order::find($orderId);
            if ($order) {
                $order->update([
                    'payment_status' => 'failed',
                    'status' => 'cancelled',
                ]);

                // Create failed payment record
                Payment::create([
                    'order_id' => $order->id,
                    'payment_method' => 'stripe',
                    'payment_gateway' => 'stripe',
                    'transaction_id' => $paymentIntent['id'],
                    'amount' => $this->convertFromStripeAmount($paymentIntent['amount']),
                    'currency' => strtoupper($paymentIntent['currency']),
                    'status' => 'failed',
                    'gateway_response' => json_encode($paymentIntent),
                    'processed_at' => now(),
                ]);
            }
        }
    }

    /**
     * Handle charge dispute
     */
    private function handleChargeDispute(array $dispute): void
    {
        $chargeId = $dispute['charge'];
        
        $payment = Payment::where('transaction_id', $chargeId)->first();
        if ($payment) {
            $payment->update([
                'status' => 'disputed',
                'gateway_response' => json_encode(array_merge(
                    json_decode($payment->gateway_response, true) ?? [],
                    ['dispute' => $dispute]
                )),
            ]);

            // Update order status
            $payment->order->update([
                'payment_status' => 'disputed',
            ]);
        }
    }
}
