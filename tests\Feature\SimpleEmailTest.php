<?php

namespace Tests\Feature;

use App\Mail\OrderConfirmation;
use App\Models\Order;
use App\Models\User;
use App\Services\EmailNotificationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;

class SimpleEmailTest extends TestCase
{
    use RefreshDatabase;

    public function test_email_notification_service_exists()
    {
        $service = new EmailNotificationService();
        $this->assertInstanceOf(EmailNotificationService::class, $service);
    }

    public function test_order_confirmation_mail_can_be_created()
    {
        $user = User::factory()->create();
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'email' => '<EMAIL>',
        ]);

        $mail = new OrderConfirmation($order);
        $this->assertInstanceOf(OrderConfirmation::class, $mail);
        $this->assertEquals($order->id, $mail->order->id);
    }

    public function test_email_service_sends_order_confirmation()
    {
        Mail::fake();
        
        $user = User::factory()->create();
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'email' => '<EMAIL>',
        ]);

        $emailService = new EmailNotificationService();
        $result = $emailService->sendOrderConfirmation($order);

        $this->assertTrue($result);
        Mail::assertQueued(OrderConfirmation::class);
    }
}
