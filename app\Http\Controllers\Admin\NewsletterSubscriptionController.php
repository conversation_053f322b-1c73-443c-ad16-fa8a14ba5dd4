<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\NewsletterSubscription;
use App\Services\ActivityLogger;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class NewsletterSubscriptionController extends Controller
{
    protected ActivityLogger $activityLogger;

    public function __construct(ActivityLogger $activityLogger)
    {
        $this->middleware('auth');
        $this->middleware('role:admin,staff'); // Basic role check
        $this->activityLogger = $activityLogger;
        
        // Additional permission check for newsletter access
        $this->middleware(function ($request, $next) {
            $user = $request->user();

            // Admins always have access
            if ($user->isAdmin()) {
                return $next($request);
            }

            // Staff need specific newsletter permission
            if ($user->isStaff()) {
                if ($user->hasPermission('newsletter', 'manage')) {
                    return $next($request);
                }

                abort(403, 'Access denied. Newsletter management permission required.');
            }

            abort(403, 'Access denied. Admin or staff role with newsletter permission required.');
        });
    }

    /**
     * Display a listing of newsletter subscriptions.
     */
    public function index(Request $request): View
    {
        $query = NewsletterSubscription::query()
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('status')) {
            switch ($request->status) {
                case 'active':
                    $query->where('is_active', true);
                    break;
                case 'inactive':
                    $query->where('is_active', false);
                    break;
                case 'verified':
                    $query->whereNotNull('email_verified_at');
                    break;
                case 'unverified':
                    $query->whereNull('email_verified_at');
                    break;
            }
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('email', 'like', "%{$search}%")
                  ->orWhere('name', 'like', "%{$search}%");
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $subscriptions = $query->paginate(20)->withQueryString();

        // Get statistics
        $stats = [
            'total' => NewsletterSubscription::count(),
            'active' => NewsletterSubscription::where('is_active', true)->count(),
            'verified' => NewsletterSubscription::whereNotNull('email_verified_at')->count(),
            'today' => NewsletterSubscription::whereDate('created_at', today())->count(),
        ];

        return view('admin.newsletter-subscriptions.index', compact('subscriptions', 'stats'));
    }

    /**
     * Display the specified newsletter subscription.
     */
    public function show(NewsletterSubscription $newsletterSubscription): View
    {
        // Log activity
        $this->activityLogger->logCustomerActivity(
            'admin_newsletter_subscription_viewed',
            "Admin viewed newsletter subscription for {$newsletterSubscription->email}",
            'info',
            null,
            [
                'subscription_id' => $newsletterSubscription->id,
                'subscription_uuid' => $newsletterSubscription->uuid,
                'subscriber_email' => $newsletterSubscription->email,
                'admin_user' => Auth::user()->email,
            ]
        );

        // Load subscription history
        $newsletterSubscription->load(['history.adminUser']);

        return view('admin.newsletter-subscriptions.show', compact('newsletterSubscription'));
    }

    /**
     * Toggle subscription status.
     */
    public function toggleStatus(NewsletterSubscription $newsletterSubscription): JsonResponse
    {
        $oldStatus = $newsletterSubscription->is_active;
        $newStatus = !$newsletterSubscription->is_active;
        $statusFrom = $oldStatus ? 'active' : 'inactive';
        $statusTo = $newStatus ? 'active' : 'inactive';
        $action = $newStatus ? 'activated' : 'deactivated';

        $newsletterSubscription->update(['is_active' => $newStatus]);

        // Track history
        $newsletterSubscription->trackHistory(
            action: $action,
            statusFrom: $statusFrom,
            statusTo: $statusTo,
            description: $action === 'activated'
                ? 'Subscription activated by admin'
                : 'Subscription deactivated by admin',
            metadata: [
                'admin_action' => true,
                'previous_status' => $oldStatus,
                'new_status' => $newStatus,
            ],
            triggeredBy: 'admin',
            adminUserId: auth()->id(),
            ipAddress: request()->ip(),
            userAgent: request()->userAgent()
        );

        $this->activityLogger->logCustomerActivity(
            'admin_newsletter_subscription_status_toggle',
            "Admin " . ($newStatus ? 'activated' : 'deactivated') . " newsletter subscription for {$newsletterSubscription->email}",
            'success',
            null,
            [
                'subscription_id' => $newsletterSubscription->id,
                'subscription_uuid' => $newsletterSubscription->uuid,
                'subscriber_email' => $newsletterSubscription->email,
                'new_status' => $newStatus,
                'admin_user' => Auth::user()->email,
            ]
        );

        return response()->json([
            'success' => true,
            'is_active' => $newStatus,
            'message' => $newStatus ? 'Subscription activated.' : 'Subscription deactivated.',
        ]);
    }

    /**
     * Export subscriptions to CSV.
     */
    public function export(Request $request): \Symfony\Component\HttpFoundation\StreamedResponse
    {
        $query = NewsletterSubscription::query();

        // Apply same filters as index
        if ($request->filled('status')) {
            switch ($request->status) {
                case 'active':
                    $query->where('is_active', true);
                    break;
                case 'inactive':
                    $query->where('is_active', false);
                    break;
                case 'verified':
                    $query->whereNotNull('email_verified_at');
                    break;
                case 'unverified':
                    $query->whereNull('email_verified_at');
                    break;
            }
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('email', 'like', "%{$search}%")
                  ->orWhere('name', 'like', "%{$search}%");
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $filename = 'newsletter-subscriptions-' . now()->format('Y-m-d-H-i-s') . '.csv';

        $this->activityLogger->logCustomerActivity(
            'admin_newsletter_subscriptions_exported',
            "Admin exported newsletter subscriptions to CSV",
            'success',
            null,
            [
                'filename' => $filename,
                'filters' => $request->only(['status', 'search', 'date_from', 'date_to']),
                'admin_user' => Auth::user()->email,
            ]
        );

        return response()->streamDownload(function () use ($query) {
            $handle = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($handle, [
                'Email',
                'Name',
                'Status',
                'Verified',
                'Subscribed Date',
                'Verified Date',
                'IP Address',
                'User Agent'
            ]);

            // Stream data
            $query->chunk(1000, function ($subscriptions) use ($handle) {
                foreach ($subscriptions as $subscription) {
                    fputcsv($handle, [
                        $subscription->email,
                        $subscription->name,
                        $subscription->is_active ? 'Active' : 'Inactive',
                        $subscription->email_verified_at ? 'Yes' : 'No',
                        $subscription->created_at->format('Y-m-d H:i:s'),
                        $subscription->email_verified_at?->format('Y-m-d H:i:s'),
                        $subscription->ip_address,
                        $subscription->user_agent
                    ]);
                }
            });

            fclose($handle);
        }, $filename, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }

    /**
     * Remove the specified newsletter subscription.
     */
    public function destroy(NewsletterSubscription $newsletterSubscription): RedirectResponse
    {
        $subscriberEmail = $newsletterSubscription->email;
        $subscriberName = $newsletterSubscription->name;

        $newsletterSubscription->delete();

        $this->activityLogger->logCustomerActivity(
            'admin_newsletter_subscription_deleted',
            "Admin deleted newsletter subscription for {$subscriberEmail}",
            'warning',
            null,
            [
                'subscriber_email' => $subscriberEmail,
                'subscriber_name' => $subscriberName,
                'admin_user' => Auth::user()->email,
            ]
        );

        return redirect()->route('admin.newsletter-subscriptions.index')
            ->with('success', 'Newsletter subscription deleted successfully.');
    }
}
