<?php

namespace Database\Factories;

use App\Models\Order;
use App\Models\User;
use App\Models\Currency;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Order>
 */
class OrderFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'uuid' => $this->faker->uuid(),
            'user_id' => User::factory(),
            'order_number' => 'ORD-' . strtoupper($this->faker->bothify('??##??##')),
            'email' => $this->faker->email(),
            'phone' => $this->faker->optional()->phoneNumber(),
            'session_id' => null, // Will be set for guest orders
            'status' => $this->faker->randomElement(['pending', 'confirmed', 'pending_payment', 'processing', 'shipped', 'delivered', 'cancelled']),
            'payment_status' => $this->faker->randomElement(['pending', 'paid', 'failed', 'refunded', 'pending_cod', 'pending_offline']),
            'payment_method' => $this->faker->randomElement(['stripe', 'paypal', 'cash_on_delivery', 'pay_offline']),
            'currency_id' => function () {
                // Try to get default currency, fallback to first currency, or create one
                return Currency::where('is_default', true)->first()?->id
                    ?? Currency::first()?->id
                    ?? Currency::create([
                        'code' => 'ZAR',
                        'name' => 'South African Rand',
                        'symbol' => 'R',
                        'exchange_rate' => 1.0000,
                        'is_default' => true,
                        'is_active' => true,
                    ])->id;
            },
            'subtotal' => $this->faker->randomFloat(2, 10, 1000),
            'tax_amount' => $this->faker->randomFloat(2, 1, 100),
            'shipping_amount' => $this->faker->randomFloat(2, 5, 50),
            'discount_amount' => $this->faker->randomFloat(2, 0, 50),
            'total_amount' => function (array $attributes) {
                return $attributes['subtotal'] + $attributes['tax_amount'] + $attributes['shipping_amount'] - $attributes['discount_amount'];
            },
            'shipping_address' => [
                'first_name' => $this->faker->firstName(),
                'last_name' => $this->faker->lastName(),
                'address_line_1' => $this->faker->streetAddress(),
                'address_line_2' => $this->faker->optional()->secondaryAddress(),
                'city' => $this->faker->city(),
                'state' => $this->faker->state(),
                'postal_code' => $this->faker->postcode(),
                'country' => $this->faker->country(),
            ],
            'billing_address' => [
                'first_name' => $this->faker->firstName(),
                'last_name' => $this->faker->lastName(),
                'address_line_1' => $this->faker->streetAddress(),
                'address_line_2' => $this->faker->optional()->secondaryAddress(),
                'city' => $this->faker->city(),
                'state' => $this->faker->state(),
                'postal_code' => $this->faker->postcode(),
                'country' => $this->faker->country(),
            ],
            'notes' => $this->faker->optional()->sentence(),
            'shipped_at' => $this->faker->optional()->dateTimeBetween('-1 month', 'now'),
            'delivered_at' => $this->faker->optional()->dateTimeBetween('-1 month', 'now'),
            'is_deleted' => false,
            'created_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'updated_at' => function (array $attributes) {
                return $this->faker->dateTimeBetween($attributes['created_at'], 'now');
            },
        ];
    }

    /**
     * Indicate that the order is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'payment_status' => 'pending',
        ]);
    }

    /**
     * Indicate that the order is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'delivered',
            'payment_status' => 'paid',
            'shipped_at' => $this->faker->dateTimeBetween('-1 month', '-1 week'),
            'delivered_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    /**
     * Indicate that the order is cancelled.
     */
    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'cancelled',
            'payment_status' => 'failed',
        ]);
    }
}
