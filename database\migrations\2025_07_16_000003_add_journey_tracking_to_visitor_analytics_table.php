<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('visitor_analytics', function (Blueprint $table) {
            // User Journey Tracking
            $table->json('user_journey')->nullable()->after('form_interactions');
            $table->integer('journey_length')->default(0)->after('user_journey');
            
            // Conversion Tracking
            $table->boolean('converted')->default(false)->after('journey_length');
            $table->string('conversion_type')->nullable()->after('converted');
            $table->json('conversions')->nullable()->after('conversion_type');
            
            // Exit Intent Tracking
            $table->boolean('exit_intent')->default(false)->after('conversions');
            $table->json('exit_data')->nullable()->after('exit_intent');
            $table->timestamp('exit_timestamp')->nullable()->after('exit_data');
            
            // Enhanced Engagement Metrics
            $table->integer('time_on_page')->nullable()->after('exit_timestamp'); // Seconds spent on page
            $table->integer('pages_visited_in_session')->default(1)->after('time_on_page');
            $table->json('pages_visited')->nullable()->after('pages_visited_in_session'); // Array of pages visited
            
            // Lead Scoring
            $table->integer('lead_score')->default(0)->after('pages_visited'); // 0-100 lead quality score
            $table->string('lead_status')->default('cold')->after('lead_score'); // cold, warm, hot, converted
            
            // Campaign Attribution
            $table->string('campaign_source')->nullable()->after('lead_status');
            $table->string('campaign_medium')->nullable()->after('campaign_source');
            $table->string('campaign_name')->nullable()->after('campaign_medium');
            $table->string('campaign_term')->nullable()->after('campaign_name');
            $table->string('campaign_content')->nullable()->after('campaign_term');
            
            // Add indexes for new fields
            $table->index(['converted', 'visited_at']);
            $table->index(['conversion_type', 'visited_at']);
            $table->index(['lead_score', 'visited_at']);
            $table->index(['lead_status', 'visited_at']);
            $table->index(['campaign_source', 'visited_at']);
            $table->index(['exit_intent', 'visited_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('visitor_analytics', function (Blueprint $table) {
            $table->dropIndex(['converted', 'visited_at']);
            $table->dropIndex(['conversion_type', 'visited_at']);
            $table->dropIndex(['lead_score', 'visited_at']);
            $table->dropIndex(['lead_status', 'visited_at']);
            $table->dropIndex(['campaign_source', 'visited_at']);
            $table->dropIndex(['exit_intent', 'visited_at']);
            
            $table->dropColumn([
                'user_journey',
                'journey_length',
                'converted',
                'conversion_type',
                'conversions',
                'exit_intent',
                'exit_data',
                'exit_timestamp',
                'time_on_page',
                'pages_visited_in_session',
                'pages_visited',
                'lead_score',
                'lead_status',
                'campaign_source',
                'campaign_medium',
                'campaign_name',
                'campaign_term',
                'campaign_content',
            ]);
        });
    }
};
