<?php

namespace App\Events\Chat;

use App\Models\ChatRoom;
use App\Models\ChatParticipant;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class RoomJoined implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ChatRoom $room;
    public ChatParticipant $participant;

    /**
     * Create a new event instance.
     */
    public function __construct(ChatRoom $room, ChatParticipant $participant)
    {
        $this->room = $room;
        $this->participant = $participant;
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('chat.room.' . $this->room->uuid),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'participant' => [
                'id' => $this->participant->id,
                'user_type' => $this->participant->user_type,
                'user_id' => $this->participant->user_id,
                'user_name' => $this->participant->user_name,
                'role' => $this->participant->role,
                'joined_at' => $this->participant->joined_at->toISOString(),
            ],
            'room' => [
                'uuid' => $this->room->uuid,
                'status' => $this->room->status,
                'participant_count' => $this->room->participants()->count(),
            ],
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'room.joined';
    }
}
