# 🔌 Live Chat & AI Chatbots - RESTful API Specification

## 📋 API Overview

This document provides comprehensive API specifications for the Live Chat & AI Chatbots feature. The API is designed as a **standalone, reusable service** following RESTful principles with real-time WebSocket extensions. Any application or system can integrate chat functionality through these APIs without developing from scratch.

## 🎯 API-First Design Philosophy

### Reusable Chat Service
- **Standalone API**: Complete chat functionality accessible via REST endpoints
- **Multi-Application Support**: Same API serves web apps, mobile apps, and third-party systems
- **No Rebuild Required**: Integrate chat into any project with simple API calls
- **Microservice Ready**: Designed for easy extraction to separate service

## 🎯 API Design Principles

### Core Principles
- **RESTful Design**: Standard HTTP methods and status codes
- **API-First Architecture**: Complete functionality accessible via REST endpoints
- **Cross-Application Compatibility**: Works with any system that can make HTTP requests
- **Real-time Support**: WebSocket integration for live features
- **Consistent Response Format**: Standardized JSON responses
- **Multiple Authentication Methods**: Bearer tokens, API keys, session-based
- **Rate Limiting**: Configurable rate limits for different endpoints
- **Versioning**: API versioning for backward compatibility
- **Circuit Breaker Integration**: Resilient external service calls

### Integration Examples
```bash
# E-commerce website integration
curl -X POST "https://api.chisolution.com/chat/v1/rooms" \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"type": "customer_support", "customer_id": 123}'

# Mobile app integration
curl -X POST "https://api.chisolution.com/chat/v1/rooms/uuid-123/messages" \
  -H "X-API-Key: YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"content": "Hello, I need help", "type": "text"}'

# CRM system integration
curl -X GET "https://api.chisolution.com/chat/v1/analytics" \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -G -d "start_date=2024-01-01&end_date=2024-01-31"
```

### Response Format

```json
{
    "success": true,
    "data": {},
    "message": "Operation completed successfully",
    "meta": {
        "timestamp": "2024-01-15T10:30:00Z",
        "version": "1.0",
        "request_id": "req_123456789"
    },
    "errors": []
}
```

## 🔐 Authentication

### Authentication Methods

```http
# Bearer Token Authentication
Authorization: Bearer {access_token}

# API Key Authentication (for webhooks)
X-API-Key: {api_key}
```

### Required Permissions

```php
// Chat permissions
'chat.participate'     // Can participate in chats
'chat.moderate'        // Can moderate chat content
'chat.assign'          // Can assign chats to staff
'chat.view_all'        // Can view all chat sessions
'chat.export'          // Can export chat data
'chat.admin'           // Full chat administration
```

## 📡 REST API Endpoints

### 1. Chat Rooms Management

#### Create Chat Room
```http
POST /api/v1/chat/rooms
Content-Type: application/json
Authorization: Bearer {token}

{
    "type": "visitor",
    "title": "Product Inquiry",
    "language": "en",
    "visitor_info": {
        "name": "John Doe",
        "email": "<EMAIL>",
        "page_url": "https://example.com/products"
    },
    "priority": 1
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "id": 123,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "type": "visitor",
        "status": "active",
        "title": "Product Inquiry",
        "language": "en",
        "priority": 1,
        "created_at": "2024-01-15T10:30:00Z",
        "participants": [],
        "last_message": null
    },
    "message": "Chat room created successfully"
}
```

#### Get Chat Room Details
```http
GET /api/v1/chat/rooms/{room_uuid}
Authorization: Bearer {token}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "id": 123,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "type": "visitor",
        "status": "active",
        "title": "Product Inquiry",
        "language": "en",
        "priority": 1,
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:35:00Z",
        "participants": [
            {
                "id": 1,
                "user_id": null,
                "participant_type": "visitor",
                "display_name": "John Doe",
                "joined_at": "2024-01-15T10:30:00Z",
                "is_active": true
            }
        ],
        "last_message": {
            "id": 456,
            "content": "Hello, I need help",
            "created_at": "2024-01-15T10:32:00Z"
        },
        "assignment": {
            "assigned_to": 5,
            "staff_name": "Jane Smith",
            "assigned_at": "2024-01-15T10:31:00Z"
        }
    }
}
```

#### List Chat Rooms
```http
GET /api/v1/chat/rooms?status=active&type=visitor&page=1&per_page=20
Authorization: Bearer {token}
```

**Query Parameters:**
- `status`: active, waiting, closed, archived
- `type`: visitor, customer, internal, support
- `assigned_to`: staff user ID
- `language`: en, af, zu, xh
- `priority`: 1-4
- `page`: page number
- `per_page`: items per page (max 100)

#### Update Chat Room
```http
PUT /api/v1/chat/rooms/{room_uuid}
Content-Type: application/json
Authorization: Bearer {token}

{
    "status": "closed",
    "title": "Product Inquiry - Resolved"
}
```

### 2. Messages Management

#### Send Message
```http
POST /api/v1/chat/rooms/{room_uuid}/messages
Content-Type: application/json
Authorization: Bearer {token}

{
    "content": "Hello, how can I help you today?",
    "message_type": "text",
    "reply_to_message_id": null
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "id": 789,
        "uuid": "msg-550e8400-e29b-41d4-a716-************",
        "chat_room_id": 123,
        "user_id": 5,
        "content": "Hello, how can I help you today?",
        "message_type": "text",
        "is_ai_generated": false,
        "created_at": "2024-01-15T10:33:00Z",
        "sender": {
            "id": 5,
            "name": "Jane Smith",
            "role": "staff"
        }
    },
    "message": "Message sent successfully"
}
```

#### Get Chat History
```http
GET /api/v1/chat/rooms/{room_uuid}/messages?page=1&per_page=50&before=2024-01-15T10:30:00Z
Authorization: Bearer {token}
```

**Query Parameters:**
- `page`: page number
- `per_page`: messages per page (max 100)
- `before`: get messages before this timestamp
- `after`: get messages after this timestamp
- `message_type`: text, file, image, system, ai

#### Update Message
```http
PUT /api/v1/chat/messages/{message_uuid}
Content-Type: application/json
Authorization: Bearer {token}

{
    "content": "Updated message content"
}
```

#### Delete Message
```http
DELETE /api/v1/chat/messages/{message_uuid}
Authorization: Bearer {token}
```

### 3. Participants Management

#### Join Chat Room
```http
POST /api/v1/chat/rooms/{room_uuid}/participants
Content-Type: application/json
Authorization: Bearer {token}

{
    "participant_type": "staff",
    "role": "participant"
}
```

#### Leave Chat Room
```http
DELETE /api/v1/chat/rooms/{room_uuid}/participants/{user_id}
Authorization: Bearer {token}
```

#### Update Participant Role
```http
PUT /api/v1/chat/rooms/{room_uuid}/participants/{user_id}
Content-Type: application/json
Authorization: Bearer {token}

{
    "role": "moderator"
}
```

### 4. Staff Assignment

#### Assign Staff to Chat
```http
POST /api/v1/chat/rooms/{room_uuid}/assignments
Content-Type: application/json
Authorization: Bearer {token}

{
    "assigned_to": 5,
    "assignment_type": "manual",
    "notes": "Customer needs technical support"
}
```

#### Transfer Chat Assignment
```http
PUT /api/v1/chat/assignments/{assignment_id}/transfer
Content-Type: application/json
Authorization: Bearer {token}

{
    "new_assignee": 7,
    "reason": "Specialist required for this issue"
}
```

#### Get Staff Availability
```http
GET /api/v1/chat/staff/availability
Authorization: Bearer {token}
```

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "user_id": 5,
            "name": "Jane Smith",
            "is_online": true,
            "current_chats": 3,
            "max_chats": 5,
            "availability_status": "available",
            "last_seen": "2024-01-15T10:35:00Z"
        }
    ]
}
```

### 5. AI Chatbot Integration

#### Generate AI Response
```http
POST /api/v1/chat/ai/generate-response
Content-Type: application/json
Authorization: Bearer {token}

{
    "chat_room_id": 123,
    "user_message": "What are your business hours?",
    "context": {
        "conversation_history": [],
        "user_language": "en",
        "intent": "business_hours"
    }
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "response": "Our business hours are Monday to Friday, 8:00 AM to 6:00 PM SAST.",
        "confidence": 0.95,
        "intent": "business_hours",
        "should_escalate": false,
        "suggested_actions": [
            "provide_contact_info",
            "offer_callback"
        ]
    }
}
```

#### Analyze Message Intent
```http
POST /api/v1/chat/ai/analyze-intent
Content-Type: application/json
Authorization: Bearer {token}

{
    "message": "I want to return a product",
    "language": "en"
}
```

### 6. File Upload

#### Upload File to Chat
```http
POST /api/v1/chat/rooms/{room_uuid}/files
Content-Type: multipart/form-data
Authorization: Bearer {token}

file: [binary file data]
message_content: "Here's the screenshot of the issue"
```

**Response:**
```json
{
    "success": true,
    "data": {
        "file_id": 456,
        "uuid": "file-550e8400-e29b-41d4-a716-************",
        "original_filename": "screenshot.png",
        "file_size": 1024000,
        "mime_type": "image/png",
        "download_url": "/api/v1/chat/files/file-550e8400-e29b-41d4-a716-************/download",
        "message": {
            "id": 790,
            "content": "Here's the screenshot of the issue",
            "message_type": "file"
        }
    }
}
```

### 7. Chat Ratings

#### Submit Chat Rating
```http
POST /api/v1/chat/rooms/{room_uuid}/rating
Content-Type: application/json
Authorization: Bearer {token}

{
    "rating": 5,
    "feedback": "Excellent support, very helpful!",
    "rating_categories": {
        "response_time": 5,
        "helpfulness": 5,
        "professionalism": 5
    }
}
```

### 8. System Administration

#### Toggle Chat System
```http
POST /api/v1/admin/chat/toggle
Content-Type: application/json
Authorization: Bearer {token}

{
    "enabled": true
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "enabled": true,
        "previous_state": false,
        "toggled_by": "Admin User",
        "toggled_at": "2024-01-15T10:30:00Z"
    },
    "message": "Chat system enabled successfully"
}
```

#### Get Chat System Status
```http
GET /api/v1/chat/system/status
Authorization: Bearer {token}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "enabled": true,
        "online_agents": 5,
        "active_chats": 12,
        "queue_length": 3,
        "system_health": "healthy"
    }
}
```

### 9. Analytics & Reporting

#### Get Chat Analytics
```http
GET /api/v1/chat/analytics?start_date=2024-01-01&end_date=2024-01-31&group_by=day
Authorization: Bearer {token}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "summary": {
            "total_chats": 1250,
            "average_rating": 4.7,
            "average_response_time": 45,
            "resolution_rate": 0.92
        },
        "daily_stats": [
            {
                "date": "2024-01-15",
                "chat_count": 45,
                "average_rating": 4.8,
                "ai_responses": 120,
                "human_responses": 89
            }
        ]
    }
}
```

## 🔄 WebSocket Events

### Connection & Authentication

```javascript
// Connect to WebSocket
const echo = new Echo({
    broadcaster: 'pusher',
    key: 'your-pusher-key',
    cluster: 'your-cluster',
    forceTLS: true,
    auth: {
        headers: {
            Authorization: 'Bearer ' + token
        }
    }
});
```

### Chat Room Events

```javascript
// Join chat room channel
echo.join(`chat.room.${roomId}`)
    .here((users) => {
        console.log('Users currently in room:', users);
    })
    .joining((user) => {
        console.log('User joined:', user);
    })
    .leaving((user) => {
        console.log('User left:', user);
    })
    .listen('MessageSent', (e) => {
        console.log('New message:', e.message);
    })
    .listen('TypingStarted', (e) => {
        console.log('User typing:', e.user);
    })
    .listen('TypingStopped', (e) => {
        console.log('User stopped typing:', e.user);
    });
```

### Staff Notification Events

```javascript
// Listen for staff notifications
echo.private(`chat.staff.${userId}`)
    .listen('ChatAssigned', (e) => {
        console.log('New chat assigned:', e.assignment);
    })
    .listen('ChatTransferred', (e) => {
        console.log('Chat transferred:', e.transfer);
    })
    .listen('UrgentChatAlert', (e) => {
        console.log('Urgent chat needs attention:', e.chat);
    });
```

## 📊 Rate Limiting

### Rate Limit Configuration

```php
// Rate limits per endpoint
'chat.send_message' => '60 per minute',
'chat.create_room' => '10 per hour',
'chat.upload_file' => '20 per hour',
'ai.generate_response' => '100 per hour',
'chat.join_room' => '30 per hour'
```

### Rate Limit Headers

```http
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 45
X-RateLimit-Reset: 1642248000
```

## 🚨 Error Handling

### Standard Error Response

```json
{
    "success": false,
    "data": null,
    "message": "Validation failed",
    "errors": [
        {
            "field": "content",
            "code": "required",
            "message": "The content field is required."
        }
    ],
    "meta": {
        "timestamp": "2024-01-15T10:30:00Z",
        "request_id": "req_123456789"
    }
}
```

### HTTP Status Codes

- `200 OK`: Successful request
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `422 Unprocessable Entity`: Validation errors
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

## 📝 API Usage Examples

### Complete Chat Flow Example

```javascript
// 1. Create chat room
const room = await fetch('/api/v1/chat/rooms', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
    },
    body: JSON.stringify({
        type: 'visitor',
        title: 'Product Support',
        language: 'en'
    })
});

// 2. Join WebSocket channel
echo.join(`chat.room.${room.data.id}`)
    .listen('MessageSent', handleNewMessage);

// 3. Send message
const message = await fetch(`/api/v1/chat/rooms/${room.data.uuid}/messages`, {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
    },
    body: JSON.stringify({
        content: 'Hello, I need help with my order',
        message_type: 'text'
    })
});
```

## 🛠️ SDK & Integration Examples

### PHP SDK Example
```php
// Install via Composer: composer require chisolution/chat-sdk
use ChiSolution\ChatSDK\ChatClient;

$chat = new ChatClient('https://api.chisolution.com/chat/v1', 'YOUR_API_KEY');

// Start a chat session
$room = $chat->createRoom([
    'type' => 'customer_support',
    'customer_data' => [
        'name' => 'John Doe',
        'email' => '<EMAIL>'
    ]
]);

// Send message
$message = $chat->sendMessage($room->uuid, 'I need help with my order');

// Listen for real-time updates
$chat->onMessage($room->uuid, function($message) {
    echo "New message: " . $message->content;
});
```

### JavaScript SDK Example
```javascript
// Install via NPM: npm install @chisolution/chat-sdk
import ChatSDK from '@chisolution/chat-sdk';

const chat = new ChatSDK('https://api.chisolution.com/chat/v1', 'YOUR_API_KEY');

// Create chat room
const room = await chat.createRoom({
    type: 'customer_support',
    customer_data: { name: 'John Doe', email: '<EMAIL>' }
});

// Send message
const message = await chat.sendMessage(room.uuid, 'Hello, I need help');

// Real-time updates
chat.subscribe(room.uuid, (event) => {
    if (event.type === 'message') {
        console.log('New message:', event.data);
    }
});
```

### Python SDK Example
```python
# Install via pip: pip install chisolution-chat-sdk
from chisolution_chat import ChatClient

chat = ChatClient('https://api.chisolution.com/chat/v1', 'YOUR_API_KEY')

# Create chat room
room = chat.create_room({
    'type': 'customer_support',
    'customer_data': {
        'name': 'John Doe',
        'email': '<EMAIL>'
    }
})

# Send message
message = chat.send_message(room['uuid'], 'I need assistance')

# Listen for updates
def on_message(message):
    print(f"New message: {message['content']}")

chat.subscribe(room['uuid'], on_message)
```

### WordPress Plugin Integration
```php
// WordPress plugin: wp-chisolution-chat
function add_chat_widget() {
    if (is_user_logged_in()) {
        $user = wp_get_current_user();
        $chat_config = [
            'api_url' => 'https://api.chisolution.com/chat/v1',
            'api_key' => get_option('chisolution_api_key'),
            'user_data' => [
                'name' => $user->display_name,
                'email' => $user->user_email,
                'user_id' => $user->ID
            ]
        ];

        wp_enqueue_script('chisolution-chat', 'path/to/chat-widget.js');
        wp_localize_script('chisolution-chat', 'chatConfig', $chat_config);
    }
}
add_action('wp_footer', 'add_chat_widget');
```

### React Component Integration
```jsx
// React component for easy integration
import { ChatWidget } from '@chisolution/react-chat';

function App() {
    return (
        <div>
            <h1>My E-commerce Store</h1>

            <ChatWidget
                apiUrl="https://api.chisolution.com/chat/v1"
                apiKey="YOUR_API_KEY"
                customerData={{
                    name: "John Doe",
                    email: "<EMAIL>",
                    page: window.location.pathname
                }}
                theme="modern"
                position="bottom-right"
            />
        </div>
    );
}
```

## 🔄 Webhook Integration

### Webhook Configuration
```http
POST /api/v1/webhooks/configure
Content-Type: application/json
Authorization: Bearer {token}

{
    "url": "https://your-system.com/chat-webhook",
    "events": ["message.sent", "room.created", "room.closed"],
    "secret": "your_webhook_secret"
}
```

### Webhook Payload Example
```json
{
    "event": "message.sent",
    "timestamp": "2024-01-15T10:30:00Z",
    "data": {
        "room_id": "550e8400-e29b-41d4-a716-************",
        "message": {
            "id": 123,
            "content": "Hello, I need help",
            "sender": {
                "type": "customer",
                "name": "John Doe"
            }
        }
    },
    "signature": "sha256=abc123..."
}
```

## 📊 Multi-Application Usage Analytics

### Application Registration
```http
POST /api/v1/applications/register
Content-Type: application/json
Authorization: Bearer {admin_token}

{
    "name": "E-commerce Store",
    "domain": "shop.example.com",
    "type": "web_application",
    "features": ["customer_support", "lead_generation"]
}
```

### Usage Tracking
```http
GET /api/v1/applications/{app_id}/analytics
Authorization: Bearer {token}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "application": "E-commerce Store",
        "period": "last_30_days",
        "metrics": {
            "total_chats": 1250,
            "total_messages": 8500,
            "average_response_time": 45,
            "customer_satisfaction": 4.7,
            "api_calls": 25000,
            "bandwidth_used": "2.5GB"
        }
    }
}
```

---

*This comprehensive API specification enables any application or system to integrate professional-grade live chat functionality without developing from scratch, providing maximum reusability and efficiency.*
