<?php

namespace Database\Factories;

use App\Models\ChatParticipant;
use App\Models\ChatRoom;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ChatParticipant>
 */
class ChatParticipantFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ChatParticipant::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'chat_room_id' => ChatRoom::factory(),
            'user_id' => User::factory(),
            'participant_type' => $this->faker->randomElement(['customer', 'staff', 'visitor']),
            'role' => $this->faker->randomElement(['participant', 'moderator', 'observer', 'owner']),
            'display_name' => $this->faker->name(),
            'permissions' => [
                'can_send_messages' => true,
                'can_upload_files' => $this->faker->boolean(80),
                'can_moderate' => $this->faker->boolean(20),
            ],
            'joined_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'left_at' => $this->faker->optional(0.3)->dateTimeBetween('now', '+1 hour'), // 30% chance of having left
            'is_active' => $this->faker->boolean(80), // 80% chance of being active
            'last_seen_at' => $this->faker->dateTimeBetween('-1 hour', 'now'),
        ];
    }

    /**
     * Indicate that the participant is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
            'left_at' => null,
            'last_seen_at' => now(),
        ]);
    }

    /**
     * Indicate that the participant has left.
     */
    public function left(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
            'left_at' => $this->faker->dateTimeBetween('-1 hour', 'now'),
        ]);
    }

    /**
     * Indicate that the participant is a customer.
     */
    public function customer(): static
    {
        return $this->state(fn (array $attributes) => [
            'participant_type' => 'customer',
            'role' => 'participant',
            'permissions' => [
                'can_send_messages' => true,
                'can_upload_files' => true,
                'can_moderate' => false,
            ],
        ]);
    }

    /**
     * Indicate that the participant is staff.
     */
    public function staff(): static
    {
        return $this->state(fn (array $attributes) => [
            'participant_type' => 'staff',
            'role' => $this->faker->randomElement(['moderator', 'owner']),
            'permissions' => [
                'can_send_messages' => true,
                'can_upload_files' => true,
                'can_moderate' => true,
            ],
        ]);
    }

    /**
     * Indicate that the participant is a visitor.
     */
    public function visitor(): static
    {
        return $this->state(fn (array $attributes) => [
            'participant_type' => 'visitor',
            'role' => 'participant',
            'user_id' => null, // Visitors might not have user accounts
            'display_name' => 'Anonymous Visitor',
            'permissions' => [
                'can_send_messages' => true,
                'can_upload_files' => false,
                'can_moderate' => false,
            ],
        ]);
    }

    /**
     * Set the chat room for this participant.
     */
    public function forRoom(ChatRoom $room): static
    {
        return $this->state(fn (array $attributes) => [
            'chat_room_id' => $room->id,
        ]);
    }

    /**
     * Set the user for this participant.
     */
    public function forUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
            'display_name' => $user->name,
        ]);
    }
}
