<?php

namespace App\Events\Chat;

use App\Models\ChatMessage;
use App\Models\ChatRoom;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MessageRead implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ChatMessage $message;
    public ChatRoom $room;
    public string $readerType;
    public int $readerId;
    public string $readerName;

    /**
     * Create a new event instance.
     */
    public function __construct(ChatMessage $message, ChatRoom $room, string $readerType, int $readerId, string $readerName)
    {
        $this->message = $message;
        $this->room = $room;
        $this->readerType = $readerType;
        $this->readerId = $readerId;
        $this->readerName = $readerName;
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('chat.room.' . $this->room->uuid),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'message_uuid' => $this->message->uuid,
            'reader' => [
                'type' => $this->readerType,
                'id' => $this->readerId,
                'name' => $this->readerName,
            ],
            'room_uuid' => $this->room->uuid,
            'read_at' => now()->toISOString(),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'message.read';
    }
}
