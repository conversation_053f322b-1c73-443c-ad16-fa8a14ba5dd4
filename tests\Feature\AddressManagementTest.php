<?php

namespace Tests\Feature;

use App\Models\Role;
use App\Models\User;
use App\Models\UserAddress;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class AddressManagementTest extends TestCase
{
    use RefreshDatabase;

    protected $seed = true;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a customer user for testing
        $customerRole = Role::where('name', 'customer')->first();
        $this->user = User::factory()->create(['role_id' => $customerRole->id]);
    }

    #[Test]
    public function authenticated_user_can_view_address_index()
    {
        $response = $this->actingAs($this->user)->get('/addresses');

        $response->assertStatus(200);
        $response->assertViewIs('addresses.index');
        $response->assertSee('Address Book');
    }

    #[Test]
    public function unauthenticated_user_cannot_access_addresses()
    {
        $response = $this->get('/addresses');

        $response->assertRedirect('/login');
    }

    #[Test]
    public function user_can_view_create_address_form()
    {
        $response = $this->actingAs($this->user)->get('/addresses/create');

        $response->assertStatus(200);
        $response->assertViewIs('addresses.create');
        $response->assertSee('Add New Address');
    }

    #[Test]
    public function user_can_create_new_address()
    {
        $addressData = [
            'type' => 'billing',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'company' => 'Test Company',
            'address_line_1' => '123 Test Street',
            'address_line_2' => 'Apt 4B',
            'city' => 'Cape Town',
            'state' => 'Western Cape',
            'postal_code' => '8001',
            'country' => 'ZA',
            'phone' => '+27123456789',
            'is_default' => true,
        ];

        $response = $this->actingAs($this->user)->post('/addresses', $addressData);

        $response->assertRedirect('/addresses');
        $response->assertSessionHas('success', 'Address added successfully!');

        $this->assertDatabaseHas('user_addresses', [
            'user_id' => $this->user->id,
            'type' => 'billing',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'city' => 'Cape Town',
            'is_default' => true,
            'is_deleted' => false,
        ]);
    }

    #[Test]
    public function creating_default_address_unsets_previous_default()
    {
        // Create first default address
        $firstAddress = UserAddress::factory()->create([
            'user_id' => $this->user->id,
            'is_default' => true,
            'is_deleted' => false,
        ]);

        // Create second address as default
        $addressData = [
            'type' => 'shipping',
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'address_line_1' => '456 New Street',
            'city' => 'Johannesburg',
            'state' => 'Gauteng',
            'postal_code' => '2000',
            'country' => 'ZA',
            'is_default' => true,
        ];

        $response = $this->actingAs($this->user)->post('/addresses', $addressData);

        $response->assertRedirect('/addresses');

        // Check that first address is no longer default
        $this->assertDatabaseHas('user_addresses', [
            'id' => $firstAddress->id,
            'is_default' => false,
        ]);

        // Check that new address is default
        $this->assertDatabaseHas('user_addresses', [
            'user_id' => $this->user->id,
            'first_name' => 'Jane',
            'is_default' => true,
        ]);
    }

    #[Test]
    public function user_can_view_edit_form_for_own_address()
    {
        $address = UserAddress::factory()->create([
            'user_id' => $this->user->id,
            'is_deleted' => false,
        ]);

        $response = $this->actingAs($this->user)->get("/addresses/{$address->id}/edit");

        $response->assertStatus(200);
        $response->assertViewIs('addresses.edit');
        $response->assertSee('Edit Address');
    }

    #[Test]
    public function user_cannot_edit_other_users_address()
    {
        $otherUser = User::factory()->create();
        $address = UserAddress::factory()->create([
            'user_id' => $otherUser->id,
            'is_deleted' => false,
        ]);

        $response = $this->actingAs($this->user)->get("/addresses/{$address->id}/edit");

        $response->assertStatus(403);
    }

    #[Test]
    public function user_can_update_own_address()
    {
        $address = UserAddress::factory()->create([
            'user_id' => $this->user->id,
            'first_name' => 'Old Name',
            'is_deleted' => false,
        ]);

        $updateData = [
            'type' => 'shipping',
            'first_name' => 'Updated Name',
            'last_name' => 'Updated Last',
            'address_line_1' => 'Updated Street',
            'city' => 'Updated City',
            'state' => 'Updated State',
            'postal_code' => '9999',
            'country' => 'ZA',
        ];

        $response = $this->actingAs($this->user)
            ->patch("/addresses/{$address->id}", $updateData);

        $response->assertRedirect('/addresses');
        $response->assertSessionHas('success', 'Address updated successfully!');

        $this->assertDatabaseHas('user_addresses', [
            'id' => $address->id,
            'first_name' => 'Updated Name',
            'city' => 'Updated City',
        ]);
    }

    #[Test]
    public function user_cannot_update_other_users_address()
    {
        $otherUser = User::factory()->create();
        $address = UserAddress::factory()->create([
            'user_id' => $otherUser->id,
            'is_deleted' => false,
        ]);

        $updateData = [
            'type' => 'billing',
            'first_name' => 'Hacker',
            'last_name' => 'Attempt',
            'address_line_1' => 'Hack Street',
            'city' => 'Hack City',
            'state' => 'Hack State',
            'postal_code' => '0000',
            'country' => 'ZA',
        ];

        $response = $this->actingAs($this->user)
            ->patch("/addresses/{$address->id}", $updateData);

        $response->assertStatus(403);

        // Ensure address wasn't updated
        $this->assertDatabaseMissing('user_addresses', [
            'id' => $address->id,
            'first_name' => 'Hacker',
        ]);
    }

    #[Test]
    public function user_can_soft_delete_own_address()
    {
        $address = UserAddress::factory()->create([
            'user_id' => $this->user->id,
            'is_deleted' => false,
        ]);

        $response = $this->actingAs($this->user)
            ->delete("/addresses/{$address->id}");

        $response->assertRedirect('/addresses');
        $response->assertSessionHas('success', 'Address deleted successfully!');

        // Check that address is soft deleted
        $this->assertDatabaseHas('user_addresses', [
            'id' => $address->id,
            'is_deleted' => true,
            'is_default' => false, // Should also unset default
        ]);
    }

    #[Test]
    public function user_cannot_delete_other_users_address()
    {
        $otherUser = User::factory()->create();
        $address = UserAddress::factory()->create([
            'user_id' => $otherUser->id,
            'is_deleted' => false,
        ]);

        $response = $this->actingAs($this->user)
            ->delete("/addresses/{$address->id}");

        $response->assertStatus(403);

        // Ensure address wasn't deleted
        $this->assertDatabaseHas('user_addresses', [
            'id' => $address->id,
            'is_deleted' => false,
        ]);
    }

    #[Test]
    public function user_can_set_address_as_default()
    {
        $address = UserAddress::factory()->create([
            'user_id' => $this->user->id,
            'is_default' => false,
            'is_deleted' => false,
        ]);

        $response = $this->actingAs($this->user)
            ->patch("/addresses/{$address->id}/set-default");

        $response->assertRedirect('/addresses');
        $response->assertSessionHas('success', 'Default address updated successfully!');

        $this->assertDatabaseHas('user_addresses', [
            'id' => $address->id,
            'is_default' => true,
        ]);
    }

    #[Test]
    public function setting_default_address_unsets_other_defaults()
    {
        // Create two addresses
        $firstAddress = UserAddress::factory()->create([
            'user_id' => $this->user->id,
            'is_default' => true,
            'is_deleted' => false,
        ]);

        $secondAddress = UserAddress::factory()->create([
            'user_id' => $this->user->id,
            'is_default' => false,
            'is_deleted' => false,
        ]);

        // Set second address as default
        $response = $this->actingAs($this->user)
            ->patch("/addresses/{$secondAddress->id}/set-default");

        $response->assertRedirect('/addresses');

        // Check that first address is no longer default
        $this->assertDatabaseHas('user_addresses', [
            'id' => $firstAddress->id,
            'is_default' => false,
        ]);

        // Check that second address is now default
        $this->assertDatabaseHas('user_addresses', [
            'id' => $secondAddress->id,
            'is_default' => true,
        ]);
    }

    #[Test]
    public function user_cannot_set_other_users_address_as_default()
    {
        $otherUser = User::factory()->create();
        $address = UserAddress::factory()->create([
            'user_id' => $otherUser->id,
            'is_default' => false,
            'is_deleted' => false,
        ]);

        $response = $this->actingAs($this->user)
            ->patch("/addresses/{$address->id}/set-default");

        $response->assertStatus(403);

        $this->assertDatabaseHas('user_addresses', [
            'id' => $address->id,
            'is_default' => false,
        ]);
    }

    #[Test]
    public function deleted_addresses_are_not_shown_in_index()
    {
        // Create active address
        $activeAddress = UserAddress::factory()->create([
            'user_id' => $this->user->id,
            'first_name' => 'ActiveUser',
            'last_name' => 'Test',
            'is_deleted' => false,
        ]);

        // Create deleted address
        $deletedAddress = UserAddress::factory()->create([
            'user_id' => $this->user->id,
            'first_name' => 'DeletedUser',
            'last_name' => 'Test',
            'is_deleted' => true,
        ]);

        $response = $this->actingAs($this->user)->get('/addresses');

        $response->assertStatus(200);
        $response->assertSee('ActiveUser Test');
        $response->assertDontSee('DeletedUser Test');
    }

    #[Test]
    public function address_validation_requires_mandatory_fields()
    {
        $response = $this->actingAs($this->user)->post('/addresses', []);

        $response->assertSessionHasErrors([
            'type',
            'first_name',
            'last_name',
            'address_line_1',
            'city',
            'state',
            'postal_code',
            'country',
        ]);
    }

    #[Test]
    public function address_validation_accepts_valid_data()
    {
        $validData = [
            'type' => 'billing',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'address_line_1' => '123 Test Street',
            'city' => 'Cape Town',
            'state' => 'Western Cape',
            'postal_code' => '8001',
            'country' => 'ZA',
        ];

        $response = $this->actingAs($this->user)->post('/addresses', $validData);

        $response->assertRedirect('/addresses');
        $response->assertSessionHasNoErrors();
    }

    #[Test]
    public function accessing_deleted_address_edit_returns_404()
    {
        $address = UserAddress::factory()->create([
            'user_id' => $this->user->id,
            'is_deleted' => true,
        ]);

        $response = $this->actingAs($this->user)->get("/addresses/{$address->id}/edit");

        $response->assertStatus(404);
    }

    #[Test]
    public function updating_deleted_address_returns_404()
    {
        $address = UserAddress::factory()->create([
            'user_id' => $this->user->id,
            'is_deleted' => true,
        ]);

        $updateData = [
            'type' => 'billing',
            'first_name' => 'Updated',
            'last_name' => 'Name',
            'address_line_1' => 'Updated Street',
            'city' => 'Updated City',
            'state' => 'Updated State',
            'postal_code' => '9999',
            'country' => 'ZA',
        ];

        $response = $this->actingAs($this->user)
            ->patch("/addresses/{$address->id}", $updateData);

        $response->assertStatus(404);
    }

    #[Test]
    public function deleting_already_deleted_address_returns_404()
    {
        $address = UserAddress::factory()->create([
            'user_id' => $this->user->id,
            'is_deleted' => true,
        ]);

        $response = $this->actingAs($this->user)
            ->delete("/addresses/{$address->id}");

        $response->assertStatus(404);
    }

    #[Test]
    public function setting_deleted_address_as_default_returns_404()
    {
        $address = UserAddress::factory()->create([
            'user_id' => $this->user->id,
            'is_deleted' => true,
        ]);

        $response = $this->actingAs($this->user)
            ->patch("/addresses/{$address->id}/set-default");

        $response->assertStatus(404);
    }
}
