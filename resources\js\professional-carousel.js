class ProfessionalCarousel {
    constructor(container) {
        this.container = container;
        this.track = container.querySelector('.carousel-track');
        this.slides = container.querySelectorAll('.carousel-slide');
        this.prevBtn = container.querySelector('.carousel-prev');
        this.nextBtn = container.querySelector('.carousel-next');
        this.indicators = container.querySelectorAll('.carousel-indicator');
        
        this.currentIndex = 0;
        this.itemsPerView = this.getItemsPerView();
        this.totalSlides = Math.ceil(this.slides.length / this.itemsPerView);
        this.autoplay = container.dataset.autoplay === 'true';
        this.interval = parseInt(container.dataset.interval) || 5000;
        this.autoplayTimer = null;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupExpandButtons();
        this.updateCarousel();
        this.updateIndicators();
        
        if (this.autoplay) {
            this.startAutoplay();
        }
        
        // Handle window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }
    
    getItemsPerView() {
        const width = window.innerWidth;
        if (width >= 1280) return 4; // xl: 4 items
        if (width >= 1024) return 3;  // lg: 3 items
        if (width >= 768) return 2;   // md: 2 items
        return 1;                     // sm: 1 item
    }
    
    handleResize() {
        const newItemsPerView = this.getItemsPerView();
        if (newItemsPerView !== this.itemsPerView) {
            this.itemsPerView = newItemsPerView;
            this.totalSlides = Math.ceil(this.slides.length / this.itemsPerView);
            this.currentIndex = Math.min(this.currentIndex, this.totalSlides - 1);
            this.updateCarousel();
            this.updateIndicators();
        }
    }
    
    setupEventListeners() {
        if (this.prevBtn) {
            this.prevBtn.addEventListener('click', () => this.prev());
        }
        
        if (this.nextBtn) {
            this.nextBtn.addEventListener('click', () => this.next());
        }
        
        this.indicators.forEach((indicator, index) => {
            indicator.addEventListener('click', () => this.goToSlide(index));
        });
        
        // Pause autoplay on hover
        this.container.addEventListener('mouseenter', () => this.pauseAutoplay());
        this.container.addEventListener('mouseleave', () => this.resumeAutoplay());
        
        // Touch/swipe support
        this.setupTouchEvents();
    }
    
    setupExpandButtons() {
        const expandBtns = this.container.querySelectorAll('.expand-btn');
        expandBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleCardExpansion(btn);
            });
        });
    }
    
    toggleCardExpansion(btn) {
        const card = btn.closest('.client-card');
        const details = card.querySelector('.client-details');
        const expandText = btn.querySelector('.expand-text');
        const expandIcon = btn.querySelector('.expand-icon');
        
        if (details.classList.contains('expanded')) {
            // Collapse
            details.classList.remove('expanded');
            card.classList.remove('expanded');
            btn.classList.remove('expanded');
            expandText.textContent = 'View Details';
        } else {
            // Expand
            details.classList.add('expanded');
            card.classList.add('expanded');
            btn.classList.add('expanded');
            expandText.textContent = 'Hide Details';
        }
    }
    
    setupTouchEvents() {
        let startX = 0;
        let currentX = 0;
        let isDragging = false;
        
        this.track.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            isDragging = true;
            this.pauseAutoplay();
        });
        
        this.track.addEventListener('touchmove', (e) => {
            if (!isDragging) return;
            currentX = e.touches[0].clientX;
        });
        
        this.track.addEventListener('touchend', () => {
            if (!isDragging) return;
            isDragging = false;
            
            const diff = startX - currentX;
            const threshold = 50;
            
            if (Math.abs(diff) > threshold) {
                if (diff > 0) {
                    this.next();
                } else {
                    this.prev();
                }
            }
            
            this.resumeAutoplay();
        });
    }
    
    prev() {
        this.currentIndex = this.currentIndex > 0 ? this.currentIndex - 1 : this.totalSlides - 1;
        this.updateCarousel();
        this.updateIndicators();
    }
    
    next() {
        this.currentIndex = this.currentIndex < this.totalSlides - 1 ? this.currentIndex + 1 : 0;
        this.updateCarousel();
        this.updateIndicators();
    }
    
    goToSlide(index) {
        this.currentIndex = index;
        this.updateCarousel();
        this.updateIndicators();
    }
    
    updateCarousel() {
        const slideWidth = 100 / this.itemsPerView;
        const translateX = -this.currentIndex * 100;
        this.track.style.transform = `translateX(${translateX}%)`;
        
        // Update navigation buttons
        if (this.prevBtn) {
            this.prevBtn.disabled = false;
        }
        if (this.nextBtn) {
            this.nextBtn.disabled = false;
        }
    }
    
    updateIndicators() {
        this.indicators.forEach((indicator, index) => {
            indicator.classList.toggle('active', index === this.currentIndex);
        });
    }
    
    startAutoplay() {
        if (!this.autoplay) return;
        this.autoplayTimer = setInterval(() => {
            this.next();
        }, this.interval);
    }
    
    pauseAutoplay() {
        if (this.autoplayTimer) {
            clearInterval(this.autoplayTimer);
            this.autoplayTimer = null;
        }
    }
    
    resumeAutoplay() {
        if (this.autoplay && !this.autoplayTimer) {
            this.startAutoplay();
        }
    }
    
    destroy() {
        this.pauseAutoplay();
        // Remove event listeners if needed
    }
}

// Initialize carousels when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const carouselContainers = document.querySelectorAll('.client-carousel-container');
    carouselContainers.forEach(container => {
        new ProfessionalCarousel(container);
    });
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProfessionalCarousel;
}
