<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AiTrainingData extends Model
{
    use HasFactory;

    protected $fillable = [
        'intent',
        'input_text',
        'expected_response',
        'language',
        'confidence_threshold',
        'category',
        'tags',
        'is_active',
    ];

    protected $casts = [
        'confidence_threshold' => 'decimal:2',
        'tags' => 'array',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $attributes = [
        'language' => 'en',
        'confidence_threshold' => 0.80,
        'is_active' => true,
    ];

    /**
     * Scope for active training data.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for specific intent.
     */
    public function scopeIntent($query, string $intent)
    {
        return $query->where('intent', $intent);
    }

    /**
     * Scope for specific language.
     */
    public function scopeLanguage($query, string $language)
    {
        return $query->where('language', $language);
    }

    /**
     * Scope for specific category.
     */
    public function scopeCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope for high confidence training data.
     */
    public function scopeHighConfidence($query)
    {
        return $query->where('confidence_threshold', '>=', 0.8);
    }

    /**
     * Search training data by input text.
     */
    public function scopeSearchInput($query, string $search)
    {
        return $query->whereFullText('input_text', $search);
    }

    /**
     * Check if training data has specific tag.
     */
    public function hasTag(string $tag): bool
    {
        $tags = $this->tags ?? [];
        return in_array($tag, $tags);
    }

    /**
     * Add tag to training data.
     */
    public function addTag(string $tag): bool
    {
        $tags = $this->tags ?? [];
        
        if (!in_array($tag, $tags)) {
            $tags[] = $tag;
            $this->tags = $tags;
            return $this->save();
        }

        return true;
    }

    /**
     * Remove tag from training data.
     */
    public function removeTag(string $tag): bool
    {
        $tags = $this->tags ?? [];
        
        if (($key = array_search($tag, $tags)) !== false) {
            unset($tags[$key]);
            $this->tags = array_values($tags);
            return $this->save();
        }

        return true;
    }

    /**
     * Get intent label.
     */
    public function getIntentLabelAttribute(): string
    {
        return ucfirst(str_replace('_', ' ', $this->intent));
    }

    /**
     * Get category label.
     */
    public function getCategoryLabelAttribute(): string
    {
        return ucfirst(str_replace('_', ' ', $this->category ?? 'general'));
    }

    /**
     * Get language name.
     */
    public function getLanguageNameAttribute(): string
    {
        return match($this->language) {
            'en' => 'English',
            'af' => 'Afrikaans',
            'zu' => 'isiZulu',
            'xh' => 'isiXhosa',
            default => 'Unknown'
        };
    }

    /**
     * Get confidence percentage.
     */
    public function getConfidencePercentageAttribute(): int
    {
        return (int) ($this->confidence_threshold * 100);
    }

    /**
     * Get input text summary.
     */
    public function getInputSummaryAttribute(): string
    {
        return strlen($this->input_text) > 100 
            ? substr($this->input_text, 0, 97) . '...'
            : $this->input_text;
    }

    /**
     * Get response summary.
     */
    public function getResponseSummaryAttribute(): string
    {
        return strlen($this->expected_response) > 100 
            ? substr($this->expected_response, 0, 97) . '...'
            : $this->expected_response;
    }

    /**
     * Activate training data.
     */
    public function activate(): bool
    {
        $this->is_active = true;
        return $this->save();
    }

    /**
     * Deactivate training data.
     */
    public function deactivate(): bool
    {
        $this->is_active = false;
        return $this->save();
    }

    /**
     * Find similar training data by input text.
     */
    public static function findSimilar(string $inputText, string $language = 'en', float $threshold = 0.7): array
    {
        // This is a simplified similarity check
        // In production, we will use more sophisticated NLP techniques
        $words = str_word_count(strtolower($inputText), 1);
        
        $similar = static::active()
                        ->language($language)
                        ->get()
                        ->filter(function ($item) use ($words, $threshold) {
                            $itemWords = str_word_count(strtolower($item->input_text), 1);
                            $intersection = array_intersect($words, $itemWords);
                            $similarity = count($intersection) / max(count($words), count($itemWords));
                            
                            return $similarity >= $threshold;
                        })
                        ->sortByDesc(function ($item) use ($words) {
                            $itemWords = str_word_count(strtolower($item->input_text), 1);
                            $intersection = array_intersect($words, $itemWords);
                            return count($intersection) / max(count($words), count($itemWords));
                        });

        return $similar->values()->toArray();
    }

    /**
     * Get training data statistics.
     */
    public static function getStatistics(): array
    {
        $total = static::count();
        $active = static::active()->count();
        $byLanguage = static::selectRaw('language, COUNT(*) as count')
                           ->groupBy('language')
                           ->pluck('count', 'language')
                           ->toArray();
        $byIntent = static::selectRaw('intent, COUNT(*) as count')
                         ->groupBy('intent')
                         ->orderByDesc('count')
                         ->limit(10)
                         ->pluck('count', 'intent')
                         ->toArray();

        return [
            'total' => $total,
            'active' => $active,
            'inactive' => $total - $active,
            'by_language' => $byLanguage,
            'top_intents' => $byIntent,
        ];
    }

    /**
     * Import training data from array.
     */
    public static function importData(array $data): int
    {
        $imported = 0;

        foreach ($data as $item) {
            if (isset($item['intent'], $item['input_text'], $item['expected_response'])) {
                static::create([
                    'intent' => $item['intent'],
                    'input_text' => $item['input_text'],
                    'expected_response' => $item['expected_response'],
                    'language' => $item['language'] ?? 'en',
                    'confidence_threshold' => $item['confidence_threshold'] ?? 0.80,
                    'category' => $item['category'] ?? null,
                    'tags' => $item['tags'] ?? [],
                ]);
                $imported++;
            }
        }

        return $imported;
    }

    /**
     * Export training data to array.
     */
    public static function exportData(array $filters = []): array
    {
        $query = static::query();

        if (isset($filters['language'])) {
            $query->language($filters['language']);
        }

        if (isset($filters['intent'])) {
            $query->intent($filters['intent']);
        }

        if (isset($filters['category'])) {
            $query->category($filters['category']);
        }

        if (isset($filters['active'])) {
            $query->where('is_active', $filters['active']);
        }

        return $query->get()->map(function ($item) {
            return [
                'intent' => $item->intent,
                'input_text' => $item->input_text,
                'expected_response' => $item->expected_response,
                'language' => $item->language,
                'confidence_threshold' => $item->confidence_threshold,
                'category' => $item->category,
                'tags' => $item->tags,
            ];
        })->toArray();
    }
}
