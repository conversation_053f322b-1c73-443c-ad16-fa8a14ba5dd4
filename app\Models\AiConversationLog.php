<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AiConversationLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'chat_room_id',
        'chat_message_id',
        'user_message',
        'ai_response',
        'intent_detected',
        'confidence_score',
        'processing_time_ms',
        'model_used',
        'was_helpful',
        'escalated_to_human',
        'escalation_reason',
    ];

    protected $casts = [
        'confidence_score' => 'decimal:2',
        'processing_time_ms' => 'integer',
        'was_helpful' => 'boolean',
        'escalated_to_human' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Scope for high confidence responses.
     */
    public function scopeHighConfidence($query)
    {
        return $query->where('confidence_score', '>=', 0.8);
    }

    /**
     * Scope for low confidence responses.
     */
    public function scopeLowConfidence($query)
    {
        return $query->where('confidence_score', '<', 0.6);
    }

    /**
     * Scope for helpful responses.
     */
    public function scopeHelpful($query)
    {
        return $query->where('was_helpful', true);
    }

    /**
     * Scope for unhelpful responses.
     */
    public function scopeUnhelpful($query)
    {
        return $query->where('was_helpful', false);
    }

    /**
     * Scope for escalated conversations.
     */
    public function scopeEscalated($query)
    {
        return $query->where('escalated_to_human', true);
    }

    /**
     * Scope for specific intent.
     */
    public function scopeIntent($query, string $intent)
    {
        return $query->where('intent_detected', $intent);
    }

    /**
     * Scope for specific model.
     */
    public function scopeModel($query, string $model)
    {
        return $query->where('model_used', $model);
    }

    /**
     * Scope for fast responses.
     */
    public function scopeFastResponse($query, int $maxMs = 1000)
    {
        return $query->where('processing_time_ms', '<=', $maxMs);
    }

    /**
     * Get the chat room this log belongs to.
     */
    public function chatRoom(): BelongsTo
    {
        return $this->belongsTo(ChatRoom::class);
    }

    /**
     * Get the chat message this log belongs to.
     */
    public function chatMessage(): BelongsTo
    {
        return $this->belongsTo(ChatMessage::class);
    }

    /**
     * Check if response was helpful.
     */
    public function wasHelpful(): ?bool
    {
        return $this->was_helpful;
    }

    /**
     * Check if conversation was escalated.
     */
    public function wasEscalated(): bool
    {
        return $this->escalated_to_human;
    }

    /**
     * Get confidence percentage.
     */
    public function getConfidencePercentageAttribute(): int
    {
        return (int) ($this->confidence_score * 100);
    }

    /**
     * Get confidence level label.
     */
    public function getConfidenceLevelAttribute(): string
    {
        if ($this->confidence_score >= 0.9) {
            return 'Very High';
        } elseif ($this->confidence_score >= 0.8) {
            return 'High';
        } elseif ($this->confidence_score >= 0.6) {
            return 'Medium';
        } elseif ($this->confidence_score >= 0.4) {
            return 'Low';
        } else {
            return 'Very Low';
        }
    }

    /**
     * Get processing time in human readable format.
     */
    public function getProcessingTimeHumanAttribute(): string
    {
        if ($this->processing_time_ms < 1000) {
            return $this->processing_time_ms . 'ms';
        } else {
            return round($this->processing_time_ms / 1000, 2) . 's';
        }
    }

    /**
     * Get intent label.
     */
    public function getIntentLabelAttribute(): string
    {
        return ucfirst(str_replace('_', ' ', $this->intent_detected ?? 'unknown'));
    }

    /**
     * Get user message summary.
     */
    public function getUserMessageSummaryAttribute(): string
    {
        return strlen($this->user_message) > 100 
            ? substr($this->user_message, 0, 97) . '...'
            : $this->user_message;
    }

    /**
     * Get AI response summary.
     */
    public function getAiResponseSummaryAttribute(): string
    {
        return strlen($this->ai_response) > 100 
            ? substr($this->ai_response, 0, 97) . '...'
            : $this->ai_response;
    }

    /**
     * Mark response as helpful.
     */
    public function markAsHelpful(): bool
    {
        $this->was_helpful = true;
        return $this->save();
    }

    /**
     * Mark response as unhelpful.
     */
    public function markAsUnhelpful(): bool
    {
        $this->was_helpful = false;
        return $this->save();
    }

    /**
     * Escalate to human.
     */
    public function escalateToHuman(string $reason): bool
    {
        $this->escalated_to_human = true;
        $this->escalation_reason = $reason;
        return $this->save();
    }

    /**
     * Get performance metrics for AI responses.
     */
    public static function getPerformanceMetrics(array $filters = []): array
    {
        $query = static::query();

        // Apply filters
        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        if (isset($filters['model'])) {
            $query->model($filters['model']);
        }

        $total = $query->count();
        
        if ($total === 0) {
            return [
                'total_responses' => 0,
                'average_confidence' => 0,
                'average_processing_time' => 0,
                'helpfulness_rate' => 0,
                'escalation_rate' => 0,
                'high_confidence_rate' => 0,
            ];
        }

        $helpful = $query->clone()->helpful()->count();
        $escalated = $query->clone()->escalated()->count();
        $highConfidence = $query->clone()->highConfidence()->count();
        $avgConfidence = $query->clone()->avg('confidence_score') ?? 0;
        $avgProcessingTime = $query->clone()->avg('processing_time_ms') ?? 0;

        return [
            'total_responses' => $total,
            'average_confidence' => round($avgConfidence, 2),
            'average_processing_time' => round($avgProcessingTime, 2),
            'helpfulness_rate' => round(($helpful / $total) * 100, 2),
            'escalation_rate' => round(($escalated / $total) * 100, 2),
            'high_confidence_rate' => round(($highConfidence / $total) * 100, 2),
        ];
    }

    /**
     * Get intent distribution.
     */
    public static function getIntentDistribution(array $filters = []): array
    {
        $query = static::query();

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        return $query->selectRaw('intent_detected, COUNT(*) as count')
                    ->whereNotNull('intent_detected')
                    ->groupBy('intent_detected')
                    ->orderByDesc('count')
                    ->pluck('count', 'intent_detected')
                    ->toArray();
    }

    /**
     * Get model performance comparison.
     */
    public static function getModelComparison(): array
    {
        $models = static::selectRaw('model_used')
                       ->distinct()
                       ->pluck('model_used')
                       ->toArray();

        $comparison = [];

        foreach ($models as $model) {
            $metrics = static::getPerformanceMetrics(['model' => $model]);
            $comparison[$model] = $metrics;
        }

        return $comparison;
    }

    /**
     * Get trending intents.
     */
    public static function getTrendingIntents(int $days = 7, int $limit = 10): array
    {
        return static::where('created_at', '>=', now()->subDays($days))
                    ->selectRaw('intent_detected, COUNT(*) as count')
                    ->whereNotNull('intent_detected')
                    ->groupBy('intent_detected')
                    ->orderByDesc('count')
                    ->limit($limit)
                    ->pluck('count', 'intent_detected')
                    ->toArray();
    }

    /**
     * Get escalation reasons.
     */
    public static function getEscalationReasons(): array
    {
        return static::escalated()
                    ->selectRaw('escalation_reason, COUNT(*) as count')
                    ->whereNotNull('escalation_reason')
                    ->groupBy('escalation_reason')
                    ->orderByDesc('count')
                    ->pluck('count', 'escalation_reason')
                    ->toArray();
    }
}
