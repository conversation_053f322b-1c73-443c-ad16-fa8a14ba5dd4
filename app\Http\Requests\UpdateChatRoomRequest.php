<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdateChatRoomRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Allow all for now, can add authorization logic later
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'status' => 'sometimes|string|in:active,closed,pending,resolved',
            'priority' => 'sometimes|integer|min:1|max:5',
            'title' => 'sometimes|string|max:255',
            'description' => 'sometimes|string|max:1000',
            'language' => 'sometimes|string|max:10',
            'metadata' => 'sometimes|array',
            
            // Assignment
            'assigned_to' => 'sometimes|integer|exists:users,id',
            'department_id' => 'sometimes|integer|exists:departments,id',
            
            // Visitor info updates
            'visitor_info' => 'sometimes|array',
            'visitor_info.name' => 'sometimes|string|max:255',
            'visitor_info.email' => 'sometimes|email|max:255',
            'visitor_info.phone' => 'sometimes|string|max:50',
            'visitor_info.company' => 'sometimes|string|max:255',
            
            // Tags
            'tags' => 'sometimes|array',
            'tags.*' => 'string|max:50',
            
            // Resolution info
            'resolution_notes' => 'sometimes|string|max:2000',
            'satisfaction_rating' => 'sometimes|integer|min:1|max:5',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'status.in' => 'Status must be one of: active, closed, pending, resolved.',
            'priority.min' => 'Priority must be at least 1.',
            'priority.max' => 'Priority cannot be greater than 5.',
            'visitor_info.email.email' => 'Please provide a valid email address.',
            'satisfaction_rating.min' => 'Satisfaction rating must be at least 1.',
            'satisfaction_rating.max' => 'Satisfaction rating cannot be greater than 5.',
        ];
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422)
        );
    }
}
