<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('login_history_permissions', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('granted_by_user_id')->constrained('users')->onDelete('cascade');
            $table->enum('access_level', ['none', 'partial', 'full'])->default('none');
            $table->json('specific_permissions')->nullable(); // Additional granular permissions
            $table->text('reason')->nullable(); // Reason for granting permission
            $table->timestamp('granted_at');
            $table->timestamp('expires_at')->nullable(); // Optional expiration
            $table->boolean('is_active')->default(true);
            $table->text('admin_notes')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['user_id', 'access_level']);
            $table->index(['granted_by_user_id']);
            $table->index(['is_active', 'expires_at']);

            // Unique constraint to prevent duplicate permissions for same user
            $table->unique(['user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('login_history_permissions');
    }
};
