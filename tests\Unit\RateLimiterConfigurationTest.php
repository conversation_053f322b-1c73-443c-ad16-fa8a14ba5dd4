<?php

namespace Tests\Unit;

use Tests\TestCase;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Http\Request;
use Illuminate\Cache\RateLimiting\Limit;
use PHPUnit\Framework\Attributes\Test;

class RateLimiterConfigurationTest extends TestCase
{
    #[Test]
    public function chat_room_creation_rate_limiter_is_configured()
    {
        $limiter = RateLimiter::limiter('chat-room-creation');
        
        $this->assertNotNull($limiter, 'chat-room-creation rate limiter should be configured');
        
        // Test the limiter with a mock request
        $request = Request::create('/test', 'POST');
        $request->setUserResolver(function () {
            return (object) ['id' => 1];
        });
        
        $limit = $limiter($request);
        
        $this->assertInstanceOf(Limit::class, $limit);
        $this->assertEquals(10, $limit->maxAttempts);
        $this->assertEquals(3600, $limit->decaySeconds); // 1 hour = 3600 seconds
    }

    #[Test]
    public function chat_message_sending_rate_limiter_is_configured()
    {
        $limiter = RateLimiter::limiter('chat-message-sending');
        
        $this->assertNotNull($limiter, 'chat-message-sending rate limiter should be configured');
        
        // Test the limiter with a mock request
        $request = Request::create('/test', 'POST');
        $request->setUserResolver(function () {
            return (object) ['id' => 1];
        });
        
        $limit = $limiter($request);
        
        $this->assertInstanceOf(Limit::class, $limit);
        $this->assertEquals(60, $limit->maxAttempts);
        $this->assertEquals(60, $limit->decaySeconds); // 1 minute = 60 seconds
    }

    #[Test]
    public function chat_realtime_rate_limiter_is_configured()
    {
        $limiter = RateLimiter::limiter('chat-realtime');
        
        $this->assertNotNull($limiter, 'chat-realtime rate limiter should be configured');
        
        // Test the limiter with a mock request
        $request = Request::create('/test', 'POST');
        $request->setUserResolver(function () {
            return (object) ['id' => 1];
        });
        
        $limit = $limiter($request);
        
        $this->assertInstanceOf(Limit::class, $limit);
        $this->assertEquals(30, $limit->maxAttempts);
        $this->assertEquals(60, $limit->decaySeconds); // 1 minute = 60 seconds
    }

    #[Test]
    public function chat_file_upload_rate_limiter_is_configured()
    {
        $limiter = RateLimiter::limiter('chat-file-upload');
        
        $this->assertNotNull($limiter, 'chat-file-upload rate limiter should be configured');
        
        // Test the limiter with a mock request
        $request = Request::create('/test', 'POST');
        $request->setUserResolver(function () {
            return (object) ['id' => 1];
        });
        
        $limit = $limiter($request);
        
        $this->assertInstanceOf(Limit::class, $limit);
        $this->assertEquals(10, $limit->maxAttempts);
        $this->assertEquals(60, $limit->decaySeconds); // 1 minute = 60 seconds
    }

    #[Test]
    public function chat_usage_rate_limiter_is_configured()
    {
        $limiter = RateLimiter::limiter('chat-usage');
        
        $this->assertNotNull($limiter, 'chat-usage rate limiter should be configured');
        
        // Test the limiter with a mock request
        $request = Request::create('/test', 'POST');
        $request->setUserResolver(function () {
            return null; // No user, should use IP
        });
        
        $limit = $limiter($request);
        
        $this->assertInstanceOf(Limit::class, $limit);
        $this->assertEquals(100, $limit->maxAttempts);
        $this->assertEquals(60, $limit->decaySeconds); // 1 minute = 60 seconds
    }

    #[Test]
    public function api_rate_limiter_is_configured()
    {
        $limiter = RateLimiter::limiter('api');
        
        $this->assertNotNull($limiter, 'api rate limiter should be configured');
        
        // Test the limiter with a mock request
        $request = Request::create('/test', 'GET');
        $request->setUserResolver(function () {
            return (object) ['id' => 1];
        });
        
        $limit = $limiter($request);
        
        $this->assertInstanceOf(Limit::class, $limit);
        $this->assertEquals(60, $limit->maxAttempts);
        $this->assertEquals(60, $limit->decaySeconds); // 1 minute = 60 seconds
    }

    #[Test]
    public function rate_limiters_use_user_id_when_authenticated()
    {
        $limiter = RateLimiter::limiter('chat-message-sending');
        
        // Test with authenticated user
        $request = Request::create('/test', 'POST');
        $request->setUserResolver(function () {
            return (object) ['id' => 123];
        });
        
        $limit = $limiter($request);
        
        // The key should include the user ID
        $this->assertStringContainsString('123', $limit->key);
    }

    #[Test]
    public function rate_limiters_use_ip_when_not_authenticated()
    {
        $limiter = RateLimiter::limiter('chat-usage');
        
        // Test with no user (guest)
        $request = Request::create('/test', 'POST');
        $request->setUserResolver(function () {
            return null;
        });
        
        // Set a fake IP
        $request->server->set('REMOTE_ADDR', '***********');
        
        $limit = $limiter($request);
        
        // The key should include the IP address
        $this->assertStringContainsString('***********', $limit->key);
    }

    #[Test]
    public function all_required_rate_limiters_are_defined()
    {
        $requiredLimiters = [
            'chat-room-creation',
            'chat-message-sending',
            'chat-realtime',
            'chat-file-upload',
            'chat-usage',
            'api',
        ];

        foreach ($requiredLimiters as $limiterName) {
            $limiter = RateLimiter::limiter($limiterName);
            $this->assertNotNull($limiter, "Rate limiter '{$limiterName}' should be configured");
        }
    }

    #[Test]
    public function rate_limiter_keys_are_properly_scoped()
    {
        $request = Request::create('/test', 'POST');
        $request->setUserResolver(function () {
            return (object) ['id' => 123];
        });

        // Test different rate limiters have different keys
        $roomCreationLimiter = RateLimiter::limiter('chat-room-creation');
        $messageSendingLimiter = RateLimiter::limiter('chat-message-sending');

        $roomCreationLimit = $roomCreationLimiter($request);
        $messageSendingLimit = $messageSendingLimiter($request);

        // Both keys should contain the user ID (Laravel handles limiter scoping internally)
        $this->assertStringContainsString('123', $roomCreationLimit->key);
        $this->assertStringContainsString('123', $messageSendingLimit->key);

        // Keys should be the same format since they use the same key resolver
        // Laravel automatically scopes them by limiter name internally
        $this->assertEquals($roomCreationLimit->key, $messageSendingLimit->key);
    }
}
