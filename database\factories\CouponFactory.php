<?php

namespace Database\Factories;

use App\Models\Coupon;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Coupon>
 */
class CouponFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Coupon::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'uuid' => Str::uuid(),
            'code' => strtoupper($this->faker->bothify('????##')),
            'name' => $this->faker->words(3, true),
            'description' => $this->faker->sentence(),
            'type' => $this->faker->randomElement(['percentage', 'fixed_amount']),
            'value' => $this->faker->randomFloat(2, 5, 50),
            'minimum_amount' => null,
            'maximum_discount' => null,
            'usage_limit' => null,
            'usage_limit_per_customer' => null,
            'used_count' => 0,
            'is_active' => true,
            'starts_at' => null,
            'expires_at' => null,
            'applicable_products' => null,
            'applicable_categories' => null,
            'exclude_products' => null,
            'exclude_categories' => null,
            'is_deleted' => false,
        ];
    }

    /**
     * Create a percentage coupon.
     */
    public function percentage(float $value = 10): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'percentage',
            'value' => $value,
        ]);
    }

    /**
     * Create a fixed amount coupon.
     */
    public function fixedAmount(float $value = 20): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'fixed_amount',
            'value' => $value,
        ]);
    }

    /**
     * Create an inactive coupon.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create an expired coupon.
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'expires_at' => now()->subDay(),
        ]);
    }

    /**
     * Create a coupon with minimum amount.
     */
    public function withMinimumAmount(float $amount): static
    {
        return $this->state(fn (array $attributes) => [
            'minimum_amount' => $amount,
        ]);
    }

    /**
     * Create a coupon with maximum discount.
     */
    public function withMaximumDiscount(float $amount): static
    {
        return $this->state(fn (array $attributes) => [
            'maximum_discount' => $amount,
        ]);
    }

    /**
     * Create a coupon with usage limit.
     */
    public function withUsageLimit(int $limit): static
    {
        return $this->state(fn (array $attributes) => [
            'usage_limit' => $limit,
        ]);
    }

    /**
     * Create a coupon with per-customer usage limit.
     */
    public function withPerCustomerLimit(int $limit): static
    {
        return $this->state(fn (array $attributes) => [
            'usage_limit_per_customer' => $limit,
        ]);
    }
}
