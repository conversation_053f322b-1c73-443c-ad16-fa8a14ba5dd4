<?php

return [
    /*
    |--------------------------------------------------------------------------
    | File Upload Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for general file uploads including documents, spreadsheets,
    | presentations, and other file types.
    |
    */

    // Maximum file size in bytes (50MB default)
    'max_file_size' => env('FILE_MAX_SIZE', 50 * 1024 * 1024),

    // Allowed MIME types for file uploads
    'allowed_mimes' => [
        // Documents
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain',
        'text/csv',
        'application/rtf',
        
        // Archives (with restrictions)
        'application/zip',
        'application/x-rar-compressed',
        'application/x-7z-compressed',
        
        // Other formats
        'application/json',
        'application/xml',
        'text/xml',
    ],

    // Allowed file extensions
    'allowed_extensions' => [
        // Documents
        'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
        'txt', 'csv', 'rtf',
        
        // Archives
        'zip', 'rar', '7z',
        
        // Other
        'json', 'xml',
    ],

    // Forbidden file extensions (never allowed)
    'forbidden_extensions' => [
        'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 'jar',
        'app', 'deb', 'pkg', 'rpm', 'dmg', 'iso', 'msi', 'run',
        'sh', 'bash', 'zsh', 'fish', 'ps1', 'psm1', 'psd1',
        'php', 'asp', 'aspx', 'jsp', 'py', 'rb', 'pl', 'cgi',
    ],

    // File type categories with specific rules
    'file_types' => [
        'document' => [
            'extensions' => ['pdf', 'doc', 'docx', 'txt', 'rtf'],
            'mimes' => [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'text/plain',
                'application/rtf',
            ],
            'max_size' => 25 * 1024 * 1024, // 25MB
            'scan_content' => true,
        ],
        'spreadsheet' => [
            'extensions' => ['xls', 'xlsx', 'csv'],
            'mimes' => [
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'text/csv',
            ],
            'max_size' => 15 * 1024 * 1024, // 15MB
            'scan_content' => true,
        ],
        'presentation' => [
            'extensions' => ['ppt', 'pptx'],
            'mimes' => [
                'application/vnd.ms-powerpoint',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            ],
            'max_size' => 30 * 1024 * 1024, // 30MB
            'scan_content' => true,
        ],
        'archive' => [
            'extensions' => ['zip', 'rar', '7z'],
            'mimes' => [
                'application/zip',
                'application/x-rar-compressed',
                'application/x-7z-compressed',
            ],
            'max_size' => 100 * 1024 * 1024, // 100MB
            'scan_content' => true,
            'extract_and_scan' => true, // Extract and scan contents
        ],
        'data' => [
            'extensions' => ['json', 'xml'],
            'mimes' => [
                'application/json',
                'application/xml',
                'text/xml',
            ],
            'max_size' => 5 * 1024 * 1024, // 5MB
            'scan_content' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    */
    'security' => [
        // Enable comprehensive file validation
        'validate_file_content' => env('FILE_VALIDATE_CONTENT', true),
        
        // Check file signatures (magic bytes)
        'check_file_signature' => env('FILE_CHECK_SIGNATURE', true),
        
        // Sanitize filenames
        'sanitize_filename' => env('FILE_SANITIZE_FILENAME', true),
        
        // Remove metadata from files
        'remove_metadata' => env('FILE_REMOVE_METADATA', true),
        
        // Scan file contents for suspicious patterns
        'scan_content_patterns' => env('FILE_SCAN_PATTERNS', true),
        
        // Maximum filename length
        'max_filename_length' => env('FILE_MAX_FILENAME_LENGTH', 255),
    ],

    /*
    |--------------------------------------------------------------------------
    | Virus Scanning Configuration
    |--------------------------------------------------------------------------
    */
    'virus_scan' => [
        // Enable virus scanning
        'enabled' => env('FILE_VIRUS_SCAN_ENABLED', true),
        
        // Scanning method: 'clamav', 'windows_defender', 'custom', 'basic'
        'method' => env('FILE_VIRUS_SCAN_METHOD', 'basic'),
        
        // Custom command for virus scanning (use {file} placeholder)
        'custom_command' => env('FILE_VIRUS_SCAN_COMMAND', ''),
        
        // Quarantine path for infected files
        'quarantine_path' => env('FILE_QUARANTINE_PATH', storage_path('quarantine')),
        
        // Delete infected files instead of quarantine
        'delete_infected' => env('FILE_DELETE_INFECTED', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Storage Configuration
    |--------------------------------------------------------------------------
    */
    'storage' => [
        // Storage disk to use
        'disk' => env('FILE_STORAGE_DISK', 'public'),
        
        // Base path for file storage
        'path' => env('FILE_STORAGE_PATH', 'files'),
        
        // Temporary storage path
        'temp_path' => env('FILE_TEMP_PATH', 'temp/files'),
        
        // Organize files by date (Y/m/d structure)
        'organize_by_date' => env('FILE_ORGANIZE_BY_DATE', true),
        
        // Organize files by type
        'organize_by_type' => env('FILE_ORGANIZE_BY_TYPE', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Content Analysis Configuration
    |--------------------------------------------------------------------------
    */
    'content_analysis' => [
        // Enable content extraction and analysis
        'enabled' => env('FILE_CONTENT_ANALYSIS', true),
        
        // Extract text content from documents
        'extract_text' => env('FILE_EXTRACT_TEXT', true),
        
        // Analyze document metadata
        'analyze_metadata' => env('FILE_ANALYZE_METADATA', true), 
        
        // Check for embedded files/objects
        'check_embedded_objects' => env('FILE_CHECK_EMBEDDED', true),
        
        // Maximum text extraction size (in characters)
        'max_text_extraction' => env('FILE_MAX_TEXT_EXTRACTION', 100000),
    ],

    /*
    |--------------------------------------------------------------------------
    | Archive Handling Configuration
    |--------------------------------------------------------------------------
    */
    'archive_handling' => [
        // Enable archive extraction and scanning
        'enabled' => env('FILE_ARCHIVE_HANDLING', true),
        
        // Maximum extraction depth
        'max_extraction_depth' => env('FILE_MAX_EXTRACTION_DEPTH', 3),
        
        // Maximum number of files to extract
        'max_extracted_files' => env('FILE_MAX_EXTRACTED_FILES', 100),
        
        // Maximum total size of extracted files
        'max_extracted_size' => env('FILE_MAX_EXTRACTED_SIZE', 500 * 1024 * 1024), // 500MB
        
        // Temporary extraction path
        'extraction_path' => env('FILE_EXTRACTION_PATH', storage_path('temp/extraction')),
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Configuration
    |--------------------------------------------------------------------------
    */
    'performance' => [
        // Memory limit for file processing
        'memory_limit' => env('FILE_MEMORY_LIMIT', '512M'),
        
        // Maximum execution time for file processing
        'max_execution_time' => env('FILE_MAX_EXECUTION_TIME', 300), // 5 minutes
        
        // Enable file processing queue
        'use_queue' => env('FILE_USE_QUEUE', false),
        
        // Queue name for file processing
        'queue_name' => env('FILE_QUEUE_NAME', 'file-processing'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    */
    'logging' => [
        // Enable file processing logging
        'enabled' => env('FILE_LOGGING_ENABLED', true),
        
        // Log channel to use
        'log_channel' => env('FILE_LOG_CHANNEL', 'daily'),
        
        // Log level
        'log_level' => env('FILE_LOG_LEVEL', 'info'),
        
        // Log file details
        'log_file_details' => env('FILE_LOG_DETAILS', true),
        
        // Log processing time
        'log_processing_time' => env('FILE_LOG_PROCESSING_TIME', true),
        
        // Log security events
        'log_security_events' => env('FILE_LOG_SECURITY', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Suspicious Content Patterns
    |--------------------------------------------------------------------------
    */
    'suspicious_patterns' => [
        // JavaScript patterns
        'javascript' => [
            '/javascript:/i',
            '/<script/i',
            '/eval\s*\(/i',
            '/document\.write/i',
            '/window\.location/i',
        ],
        
        // VBScript patterns
        'vbscript' => [
            '/vbscript:/i',
            '/CreateObject/i',
            '/WScript\.Shell/i',
        ],
        
        // PowerShell patterns
        'powershell' => [
            '/powershell/i',
            '/Invoke-Expression/i',
            '/IEX\s/i',
            '/DownloadString/i',
        ],
        
        // Command injection patterns
        'command_injection' => [
            '/cmd\.exe/i',
            '/system\(/i',
            '/exec\(/i',
            '/shell_exec/i',
        ],
        
        // SQL injection patterns
        'sql_injection' => [
            '/union\s+select/i',
            '/drop\s+table/i',
            '/insert\s+into/i',
            '/delete\s+from/i',
        ],
    ],
];
