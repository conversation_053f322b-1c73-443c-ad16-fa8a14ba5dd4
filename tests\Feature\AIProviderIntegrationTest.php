<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\ChatRoom;
use App\Models\ChatSystemSetting;
use App\Services\AI\AIProviderManager;
use App\Services\ChatAIService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;

class AIProviderIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected ChatRoom $room;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = User::factory()->create();
        $this->admin->assignRole('admin');
        
        $this->room = ChatRoom::factory()->create([
            'language' => 'en',
        ]);

        // Mock AI provider configurations
        Config::set('ai-providers.default', 'openai');
        Config::set('ai-providers.providers.openai.api_key', 'test-key');
    }

    /** @test */
    public function admin_can_access_ai_configuration_page()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.chat.ai.index'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.chat.ai.index');
        $response->assertViewHas(['settings', 'templates', 'trainingData', 'performance', 'providers', 'usageStats', 'currentProvider']);
    }

    /** @test */
    public function admin_can_get_available_providers()
    {
        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.ai.providers'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'openai' => [
                    'name',
                    'models',
                    'features'
                ]
            ]
        ]);
    }

    /** @test */
    public function admin_can_test_ai_provider()
    {
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'content' => 'Hello! This is a test response from OpenAI.'
                        ],
                        'finish_reason' => 'stop'
                    ]
                ],
                'usage' => [
                    'total_tokens' => 25,
                    'prompt_tokens' => 15,
                    'completion_tokens' => 10
                ]
            ], 200)
        ]);

        $response = $this->actingAs($this->admin)
            ->postJson(route('admin.chat.ai.test-provider'), [
                'provider' => 'openai',
                'model' => 'gpt-4.1-mini',
                'message' => 'Hello, this is a test message.'
            ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'response',
                'provider',
                'model',
                'processing_time_ms',
                'tokens_used',
                'confidence'
            ]
        ]);
    }

    /** @test */
    public function admin_can_update_provider_settings()
    {
        $response = $this->actingAs($this->admin)
            ->postJson(route('admin.chat.ai.provider-settings.update'), [
                'default_provider' => 'anthropic',
                'fallback_enabled' => true,
                'rate_limiting_enabled' => true,
                'caching_enabled' => false,
                'openai_model' => 'gpt-4.1-mini',
                'anthropic_model' => 'claude-sonnet-4-20250514'
            ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify settings were saved
        $this->assertEquals('anthropic', ChatSystemSetting::get('ai_default_provider'));
        $this->assertEquals('true', ChatSystemSetting::get('ai_fallback_enabled'));
        $this->assertEquals('false', ChatSystemSetting::get('ai_caching_enabled'));
    }

    /** @test */
    public function admin_can_get_usage_statistics()
    {
        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.chat.ai.usage-stats'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'openai' => [
                    'requests_today',
                    'tokens_used_today',
                    'cost_today'
                ]
            ]
        ]);
    }

    /** @test */
    public function ai_provider_manager_can_generate_response()
    {
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'content' => 'Hello! How can I help you today?'
                        ],
                        'finish_reason' => 'stop'
                    ]
                ],
                'usage' => [
                    'total_tokens' => 25,
                    'prompt_tokens' => 15,
                    'completion_tokens' => 10
                ]
            ], 200)
        ]);

        $aiManager = app(AIProviderManager::class);
        $response = $aiManager->generateResponse(
            'Hello',
            ['system_message' => 'You are a helpful assistant'],
            'gpt-4.1-mini',
            'openai'
        );

        $this->assertEquals('Hello! How can I help you today?', $response['content']);
        $this->assertEquals('openai', $response['provider']);
        $this->assertEquals('gpt-4.1-mini', $response['model']);
        $this->assertArrayHasKey('usage', $response);
    }

    /** @test */
    public function chat_ai_service_integrates_with_provider_manager()
    {
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'content' => 'Hello! I can help you with your inquiry.'
                        ],
                        'finish_reason' => 'stop'
                    ]
                ],
                'usage' => [
                    'total_tokens' => 30,
                    'prompt_tokens' => 20,
                    'completion_tokens' => 10
                ]
            ], 200)
        ]);

        $chatAI = app(ChatAIService::class);
        $response = $chatAI->generateResponseWithProvider(
            'I need help with my order',
            $this->room,
            ['temperature' => 0.7, 'max_tokens' => 100],
            'openai',
            'gpt-4.1-mini'
        );

        $this->assertEquals('Hello! I can help you with your inquiry.', $response['response']);
        $this->assertEquals('openai', $response['provider_used']);
        $this->assertEquals('gpt-4.1-mini', $response['model_used']);
        $this->assertArrayHasKey('confidence', $response);
        $this->assertArrayHasKey('processing_time_ms', $response);
    }

    /** @test */
    public function system_handles_provider_failure_gracefully()
    {
        Http::fake([
            'api.openai.com/*' => Http::response([], 500)
        ]);

        $response = $this->actingAs($this->admin)
            ->postJson(route('admin.chat.ai.test-provider'), [
                'provider' => 'openai',
                'message' => 'Test message'
            ]);

        $response->assertStatus(500);
        $response->assertJson(['success' => false]);
        $response->assertJsonStructure(['message']);
    }

    /** @test */
    public function ai_provider_settings_are_validated()
    {
        $response = $this->actingAs($this->admin)
            ->postJson(route('admin.chat.ai.provider-settings.update'), [
                'default_provider' => 'invalid_provider',
                'fallback_enabled' => 'not_boolean'
            ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['default_provider']);
    }

    /** @test */
    public function non_admin_cannot_access_ai_provider_management()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->get(route('admin.chat.ai.index'));

        $response->assertStatus(403);
    }

    /** @test */
    public function ai_provider_configuration_is_loaded_correctly()
    {
        $providers = config('ai-providers.providers');
        
        $this->assertArrayHasKey('openai', $providers);
        $this->assertArrayHasKey('anthropic', $providers);
        $this->assertArrayHasKey('google', $providers);
        $this->assertArrayHasKey('xai', $providers);

        foreach ($providers as $provider) {
            $this->assertArrayHasKey('name', $provider);
            $this->assertArrayHasKey('models', $provider);
        }
    }
}
