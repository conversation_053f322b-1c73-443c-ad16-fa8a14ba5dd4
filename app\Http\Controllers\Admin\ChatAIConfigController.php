<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ChatSystemSetting;
use App\Models\AiTrainingData;
use App\Services\ChatAIService;
use App\Services\AI\AIProviderManager;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;

class ChatAIConfigController extends Controller
{
    protected ChatAIService $aiService;
    protected AIProviderManager $aiProviderManager;

    public function __construct(ChatAIService $aiService, AIProviderManager $aiProviderManager)
    {
        $this->aiService = $aiService;
        $this->aiProviderManager = $aiProviderManager;
    }

    /**
     * Display the AI configuration dashboard.
     */
    public function index(): View
    {
        $settings = $this->getAISettings();
        $templates = $this->getResponseTemplates();
        $trainingData = $this->getTrainingDataStats();
        $performance = $this->getPerformanceMetrics();
        $providers = $this->aiService->getAvailableProvidersAndModels();
        $usageStats = $this->aiService->getAIUsageStats();
        $currentProvider = config('ai-providers.default', 'openai');

        return view('admin.chat.ai.index', compact(
            'settings',
            'templates',
            'trainingData',
            'performance',
            'providers',
            'usageStats',
            'currentProvider'
        ));
    }

    /**
     * Get current AI settings.
     */
    protected function getAISettings(): array
    {
        return [
            'enabled' => ChatSystemSetting::isAiEnabled(),
            'model' => config('openai.model', 'gpt-3.5-turbo'),
            'temperature' => config('openai.temperature', 0.7),
            'max_tokens' => config('openai.max_tokens', 150),
            'confidence_threshold' => config('openai.confidence_threshold', 0.6),
            'escalation_threshold' => config('openai.escalation_threshold', 0.3),
            'greeting_enabled' => config('openai.chat.greeting_enabled', true),
            'fallback_enabled' => config('openai.chat.enable_fallback', true),
        ];
    }

    /**
     * Get response templates.
     */
    protected function getResponseTemplates(): array
    {
        return config('openai.templates', []);
    }

    /**
     * Get training data statistics.
     */
    protected function getTrainingDataStats(): array
    {
        return [
            'total_entries' => AiTrainingData::count(),
            'active_entries' => AiTrainingData::active()->count(),
            'languages' => AiTrainingData::distinct('language')->pluck('language')->toArray(),
            'intents' => AiTrainingData::distinct('intent')->pluck('intent')->toArray(),
        ];
    }

    /**
     * Get AI performance metrics.
     */
    protected function getPerformanceMetrics(): array
    {
        // Get cached metrics or calculate them
        return Cache::remember('ai_performance_metrics', 300, function () {
            return [
                'avg_response_time' => 1.2, // seconds
                'success_rate' => 95.5, // percentage
                'escalation_rate' => 12.3, // percentage
                'user_satisfaction' => 4.2, // out of 5
                'total_interactions' => 1250,
                'ai_availability' => $this->aiService->isAIAvailable() ? 100 : 0,
            ];
        });
    }

    /**
     * Update AI settings.
     */
    public function updateSettings(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'enabled' => 'required|boolean',
            'model' => 'required|string|in:gpt-3.5-turbo,gpt-4,gpt-4-turbo',
            'temperature' => 'required|numeric|min:0|max:2',
            'max_tokens' => 'required|integer|min:50|max:1000',
            'confidence_threshold' => 'required|numeric|min:0|max:1',
            'escalation_threshold' => 'required|numeric|min:0|max:1',
            'greeting_enabled' => 'required|boolean',
            'fallback_enabled' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            // Update AI enabled status
            ChatSystemSetting::updateOrCreate(
                ['setting_key' => 'ai_enabled'],
                [
                    'setting_value' => $request->enabled ? 'true' : 'false',
                    'setting_type' => 'boolean',
                    'description' => 'Enable or disable AI chatbot',
                    'is_public' => true,
                ]
            );

            // Update other settings in config cache
            $settings = $request->only([
                'model', 'temperature', 'max_tokens', 
                'confidence_threshold', 'escalation_threshold',
                'greeting_enabled', 'fallback_enabled'
            ]);

            // Store settings in cache for runtime use
            Cache::put('chat_ai_runtime_settings', $settings, 3600);

            // Clear performance metrics cache to refresh
            Cache::forget('ai_performance_metrics');

            return response()->json([
                'success' => true,
                'message' => 'AI settings updated successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update settings: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update response templates.
     */
    public function updateTemplates(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'templates' => 'required|array',
            'templates.greeting' => 'required|string|max:500',
            'templates.fallback' => 'required|string|max:500',
            'templates.error' => 'required|string|max:500',
            'templates.offline' => 'required|string|max:500',
            'templates.goodbye' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            // Store templates in cache
            Cache::put('chat_ai_templates', $request->templates, 3600);

            return response()->json([
                'success' => true,
                'message' => 'Response templates updated successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update templates: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Test AI connection and response.
     */
    public function testConnection(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'test_message' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            // Create a test room for AI testing
            $testRoom = new \App\Models\ChatRoom([
                'uuid' => 'test-' . uniqid(),
                'title' => 'AI Test Room',
                'language' => 'en',
                'status' => 'active',
            ]);

            $startTime = microtime(true);
            $response = $this->aiService->generateResponse(
                $request->test_message,
                $testRoom,
                []
            );
            $endTime = microtime(true);

            $responseTime = round(($endTime - $startTime) * 1000, 2);

            return response()->json([
                'success' => true,
                'message' => 'AI test completed successfully',
                'data' => [
                    'ai_response' => $response['response'] ?? 'No response generated',
                    'confidence' => $response['confidence'] ?? 0,
                    'intent' => $response['intent'] ?? 'unknown',
                    'response_type' => $response['response_type'] ?? 'unknown',
                    'processing_time_ms' => $response['processing_time_ms'] ?? $responseTime,
                    'should_escalate' => $response['should_escalate'] ?? false,
                    'ai_available' => $this->aiService->isAIAvailable(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'AI test failed: ' . $e->getMessage(),
                'data' => [
                    'ai_available' => $this->aiService->isAIAvailable(),
                    'error_details' => $e->getMessage(),
                ],
            ], 500);
        }
    }

    /**
     * Get AI performance analytics.
     */
    public function getAnalytics(Request $request): JsonResponse
    {
        $days = $request->get('days', 7);
        
        try {
            $analytics = Cache::remember("ai_analytics_{$days}d", 1800, function () use ($days) {
                // In a real implementation, you'd query actual analytics data
                // For now, return mock data
                return [
                    'response_times' => [
                        'avg' => 1.2,
                        'min' => 0.3,
                        'max' => 4.1,
                        'p95' => 2.8,
                    ],
                    'success_rates' => [
                        'total_requests' => 1250,
                        'successful_responses' => 1194,
                        'failed_responses' => 56,
                        'success_rate' => 95.5,
                    ],
                    'escalation_data' => [
                        'total_escalations' => 154,
                        'escalation_rate' => 12.3,
                        'avg_escalation_time' => 45, // seconds
                    ],
                    'intent_distribution' => [
                        'general_inquiry' => 45,
                        'support_request' => 30,
                        'sales_inquiry' => 15,
                        'greeting' => 10,
                    ],
                    'daily_usage' => $this->generateDailyUsageData($days),
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $analytics,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve analytics: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate mock daily usage data.
     */
    protected function generateDailyUsageData(int $days): array
    {
        $data = [];
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $data[] = [
                'date' => $date,
                'requests' => rand(80, 200),
                'successful' => rand(75, 190),
                'escalations' => rand(5, 25),
                'avg_response_time' => round(rand(800, 2500) / 1000, 2),
            ];
        }
        return $data;
    }

    /**
     * Test AI provider connection.
     */
    public function testProvider(Request $request): JsonResponse
    {
        $request->validate([
            'provider' => 'required|string|in:openai,anthropic,google,xai',
            'model' => 'nullable|string',
            'message' => 'nullable|string|max:500',
        ]);

        try {
            $provider = $request->input('provider');

            // First, validate the provider configuration
            try {
                $aiProvider = $this->aiProviderManager->provider($provider);
                $aiProvider->validateConfiguration();
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => 'Configuration Error: ' . $e->getMessage(),
                    'type' => 'configuration_error'
                ], 400);
            }

            $message = $request->input('message', 'Hello, this is a test message from ChiSolution admin.');

            $response = $this->aiService->generateResponseWithProvider(
                $message,
                new \App\Models\ChatRoom(['language' => 'en']), // Temporary room for testing
                ['temperature' => 0.7, 'max_tokens' => 100],
                $provider,
                $request->input('model')
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'response' => $response['response'],
                    'provider' => $response['provider_used'],
                    'model' => $response['model_used'],
                    'processing_time_ms' => $response['processing_time_ms'],
                    'tokens_used' => $response['tokens_used'],
                    'confidence' => $response['confidence'],
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Provider test failed: ' . $e->getMessage(),
                'type' => 'test_error'
            ], 500);
        }
    }

    /**
     * Get available providers and models.
     */
    public function getProviders(): JsonResponse
    {
        try {
            $providers = $this->aiService->getAvailableProvidersAndModels();

            return response()->json([
                'success' => true,
                'data' => $providers,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch providers: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get provider configuration status.
     */
    public function getProviderStatus(): JsonResponse
    {
        $providers = ['openai', 'anthropic', 'google', 'xai'];
        $status = [];

        foreach ($providers as $providerName) {
            try {
                $provider = $this->aiProviderManager->provider($providerName);
                $provider->validateConfiguration();
                $status[$providerName] = [
                    'configured' => true,
                    'status' => 'ready',
                    'message' => 'Provider is properly configured'
                ];
            } catch (\Exception $e) {
                $status[$providerName] = [
                    'configured' => false,
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }

        return response()->json([
            'success' => true,
            'data' => $status,
        ]);
    }

    /**
     * Get AI usage statistics.
     */
    public function getUsageStats(): JsonResponse
    {
        try {
            $stats = $this->aiService->getAIUsageStats();

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch usage stats: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update AI provider settings.
     */
    public function updateProviderSettings(Request $request): JsonResponse
    {
        $request->validate([
            'default_provider' => 'required|string|in:openai,anthropic,google,xai',
            'fallback_enabled' => 'boolean',
            'rate_limiting_enabled' => 'boolean',
            'caching_enabled' => 'boolean',
            'openai_model' => 'nullable|string',
            'anthropic_model' => 'nullable|string',
            'google_model' => 'nullable|string',
            'xai_model' => 'nullable|string',
        ]);

        try {
            // Update AI provider settings
            $settings = [
                'ai_default_provider' => $request->input('default_provider'),
                'ai_fallback_enabled' => $request->boolean('fallback_enabled') ? 'true' : 'false',
                'ai_rate_limiting_enabled' => $request->boolean('rate_limiting_enabled') ? 'true' : 'false',
                'ai_caching_enabled' => $request->boolean('caching_enabled') ? 'true' : 'false',
            ];

            // Add model settings if provided
            if ($request->filled('openai_model')) {
                $settings['ai_openai_model'] = $request->input('openai_model');
            }
            if ($request->filled('anthropic_model')) {
                $settings['ai_anthropic_model'] = $request->input('anthropic_model');
            }
            if ($request->filled('google_model')) {
                $settings['ai_google_model'] = $request->input('google_model');
            }
            if ($request->filled('xai_model')) {
                $settings['ai_xai_model'] = $request->input('xai_model');
            }

            foreach ($settings as $key => $value) {
                ChatSystemSetting::updateOrCreate(
                    ['setting_key' => $key],
                    [
                        'setting_value' => $value,
                        'setting_type' => 'string',
                        'description' => $this->getSettingDescription($key),
                        'is_public' => false,
                    ]
                );
            }

            // Clear cache
            Cache::forget('chat_ai_settings');

            return response()->json([
                'success' => true,
                'message' => 'AI provider settings updated successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update settings: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get setting description for AI provider settings.
     */
    protected function getSettingDescription(string $key): string
    {
        $descriptions = [
            'ai_default_provider' => 'Default AI provider for chat responses',
            'ai_fallback_enabled' => 'Enable fallback to other providers when primary fails',
            'ai_rate_limiting_enabled' => 'Enable rate limiting for AI API calls',
            'ai_caching_enabled' => 'Enable caching for AI responses',
            'ai_openai_model' => 'Default OpenAI model for chat responses',
            'ai_anthropic_model' => 'Default Anthropic model for chat responses',
            'ai_google_model' => 'Default Google model for chat responses',
            'ai_xai_model' => 'Default xAI model for chat responses',
        ];

        return $descriptions[$key] ?? 'AI provider setting';
    }

    /**
     * Display AI analytics dashboard.
     */
    public function analyticsDashboard(): View
    {
        return view('admin.chat.ai.analytics');
    }

    /**
     * Get enhanced AI analytics data for dashboard.
     */
    public function getEnhancedAnalytics(Request $request): JsonResponse
    {
        $days = $request->input('days', 7);
        $export = $request->boolean('export', false);

        try {
            // Get basic analytics data
            $analytics = $this->getPerformanceMetrics();

            // Enhanced analytics data
            $enhancedAnalytics = [
                'metrics' => [
                    'total_responses' => $analytics['total_responses'] ?? 0,
                    'success_rate' => $analytics['success_rate'] ?? 0,
                    'avg_response_time' => $analytics['avg_response_time'] ?? 0,
                    'escalation_rate' => $analytics['escalation_rate'] ?? 0,
                    'responses_change' => rand(-10, 15), // Mock data - replace with real calculation
                    'success_change' => rand(-5, 10),
                    'time_change' => rand(-20, 5),
                    'escalation_change' => rand(-15, 5),
                ],
                'daily_responses' => $this->getDailyResponseData($days),
                'daily_success_rate' => $this->getDailySuccessRateData($days),
                'top_intents' => $this->getTopIntents($days),
                'response_time_distribution' => $this->getResponseTimeDistribution($days),
                'provider_performance' => $this->getProviderPerformanceData($days),
                'error_analysis' => $this->getErrorAnalysisData($days),
                'detailed_analytics' => $this->getDetailedAnalyticsData($days),
            ];

            if ($export) {
                return $this->exportAnalyticsReport($enhancedAnalytics);
            }

            return response()->json([
                'success' => true,
                'data' => $enhancedAnalytics,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch analytics: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get daily response data.
     */
    protected function getDailyResponseData(int $days): array
    {
        // Mock data - replace with real database queries
        $data = [];
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $data[] = [
                'date' => $date,
                'count' => rand(50, 200),
            ];
        }
        return $data;
    }

    /**
     * Get daily success rate data.
     */
    protected function getDailySuccessRateData(int $days): array
    {
        // Mock data - replace with real database queries
        $data = [];
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $data[] = [
                'date' => $date,
                'success_rate' => rand(85, 98) / 100,
            ];
        }
        return $data;
    }

    /**
     * Get top intents data.
     */
    protected function getTopIntents(int $days): array
    {
        return [
            ['intent' => 'Order Support', 'count' => rand(100, 300)],
            ['intent' => 'Product Info', 'count' => rand(80, 250)],
            ['intent' => 'Technical Help', 'count' => rand(60, 200)],
            ['intent' => 'Billing', 'count' => rand(40, 150)],
            ['intent' => 'General Inquiry', 'count' => rand(30, 120)],
        ];
    }

    /**
     * Get response time distribution.
     */
    protected function getResponseTimeDistribution(int $days): array
    {
        return [
            ['range' => '0-500ms', 'count' => rand(200, 400)],
            ['range' => '500ms-1s', 'count' => rand(150, 300)],
            ['range' => '1-2s', 'count' => rand(100, 200)],
            ['range' => '2-5s', 'count' => rand(50, 100)],
            ['range' => '5s+', 'count' => rand(10, 50)],
        ];
    }

    /**
     * Get provider performance data.
     */
    protected function getProviderPerformanceData(int $days): array
    {
        return [
            [
                'name' => 'OpenAI',
                'responses' => rand(500, 1000),
                'success_rate' => rand(90, 98) / 100,
                'avg_time' => rand(800, 1500),
            ],
            [
                'name' => 'Anthropic',
                'responses' => rand(300, 800),
                'success_rate' => rand(88, 96) / 100,
                'avg_time' => rand(900, 1800),
            ],
            [
                'name' => 'Google',
                'responses' => rand(200, 600),
                'success_rate' => rand(85, 95) / 100,
                'avg_time' => rand(600, 1200),
            ],
            [
                'name' => 'xAI',
                'responses' => rand(100, 400),
                'success_rate' => rand(82, 92) / 100,
                'avg_time' => rand(1000, 2000),
            ],
        ];
    }

    /**
     * Get error analysis data.
     */
    protected function getErrorAnalysisData(int $days): array
    {
        $errors = [
            ['type' => 'Rate Limit', 'message' => 'API rate limit exceeded', 'count' => rand(5, 20)],
            ['type' => 'Timeout', 'message' => 'Request timeout', 'count' => rand(3, 15)],
            ['type' => 'Invalid Response', 'message' => 'Malformed API response', 'count' => rand(1, 8)],
        ];

        // Sometimes return no errors
        return rand(0, 1) ? [] : array_slice($errors, 0, rand(1, 3));
    }

    /**
     * Get detailed analytics data.
     */
    protected function getDetailedAnalyticsData(int $days): array
    {
        $data = [];
        $providers = ['OpenAI', 'Anthropic', 'Google', 'xAI'];

        for ($i = min($days, 7) - 1; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            foreach ($providers as $provider) {
                $data[] = [
                    'date' => $date,
                    'provider' => $provider,
                    'responses' => rand(50, 200),
                    'success_rate' => rand(85, 98) / 100,
                    'avg_time' => rand(800, 2000),
                    'tokens_used' => rand(10000, 50000),
                    'cost' => rand(5, 25) / 10,
                ];
            }
        }

        return $data;
    }

    /**
     * Export analytics report.
     */
    protected function exportAnalyticsReport(array $data): JsonResponse
    {
        // In a real implementation, you would generate a PDF or Excel file
        return response()->json([
            'success' => true,
            'message' => 'Analytics report exported successfully',
            'download_url' => '#', // Would be actual download URL
        ]);
    }
}
