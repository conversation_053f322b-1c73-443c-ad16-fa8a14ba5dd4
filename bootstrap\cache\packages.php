<?php return array (
  'artesaos/seotools' => 
  array (
    'aliases' => 
    array (
      'SEO' => 'Artesaos\\SEOTools\\Facades\\SEOTools',
      'JsonLd' => 'Artesaos\\SEOTools\\Facades\\JsonLd',
      'SEOMeta' => 'Artesaos\\SEOTools\\Facades\\SEOMeta',
      'Twitter' => 'Artesaos\\SEOTools\\Facades\\TwitterCard',
      'OpenGraph' => 'Artesaos\\SEOTools\\Facades\\OpenGraph',
    ),
    'providers' => 
    array (
      0 => 'Artesaos\\SEOTools\\Providers\\SEOToolsServiceProvider',
    ),
  ),
  'intervention/image-laravel' => 
  array (
    'aliases' => 
    array (
      'Image' => 'Intervention\\Image\\Laravel\\Facades\\Image',
    ),
    'providers' => 
    array (
      0 => 'Intervention\\Image\\Laravel\\ServiceProvider',
    ),
  ),
  'jenssegers/agent' => 
  array (
    'aliases' => 
    array (
      'Agent' => 'Jenssegers\\Agent\\Facades\\Agent',
    ),
    'providers' => 
    array (
      0 => 'Jenssegers\\Agent\\AgentServiceProvider',
    ),
  ),
  'laravel/pail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Pail\\PailServiceProvider',
    ),
  ),
  'laravel/sail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sail\\SailServiceProvider',
    ),
  ),
  'laravel/sanctum' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sanctum\\SanctumServiceProvider',
    ),
  ),
  'laravel/tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Tinker\\TinkerServiceProvider',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'nunomaduro/collision' => 
  array (
    'providers' => 
    array (
      0 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    ),
  ),
  'nunomaduro/termwind' => 
  array (
    'providers' => 
    array (
      0 => 'Termwind\\Laravel\\TermwindServiceProvider',
    ),
  ),
  'openai-php/laravel' => 
  array (
    'providers' => 
    array (
      0 => 'OpenAI\\Laravel\\ServiceProvider',
    ),
  ),
  'spatie/laravel-sitemap' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Sitemap\\SitemapServiceProvider',
    ),
  ),
);