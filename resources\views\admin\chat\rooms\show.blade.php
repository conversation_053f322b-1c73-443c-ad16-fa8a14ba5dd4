@extends('layouts.dashboard')

@section('title', 'Chat Room - ' . ($chatRoom->title ?? 'Chat #' . $chatRoom->id))

@section('content')
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div class="flex items-center space-x-4">
            <a href="{{ route('admin.chat.rooms.index') }}" class="text-gray-600 hover:text-gray-900">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ $chatRoom->title ?? 'Chat #' . $chatRoom->id }}</h1>
                <p class="text-gray-600">
                    Status: 
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                        {{ $chatRoom->status === 'active' ? 'bg-green-100 text-green-800' : '' }}
                        {{ $chatRoom->status === 'waiting' ? 'bg-yellow-100 text-yellow-800' : '' }}
                        {{ $chatRoom->status === 'closed' ? 'bg-gray-100 text-gray-800' : '' }}">
                        {{ ucfirst($chatRoom->status) }}
                    </span>
                    | Priority: {{ $chatRoom->priority }}/5
                    | Created {{ $chatRoom->created_at->diffForHumans() }}
                </p>
            </div>
        </div>
        <div class="flex space-x-3">
            @if($chatRoom->status !== 'closed')
                <button onclick="updateRoomStatus('closed')" class="btn btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Close Chat
                </button>
            @endif
            <button onclick="showAssignmentModal()" class="btn btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                {{ $chatRoom->currentAssignment ? 'Reassign' : 'Assign' }}
            </button>
        </div>
    </div>

    <!-- Chat Interface -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <!-- Chat Messages -->
        <div class="lg:col-span-3">
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 flex flex-col h-[600px]">
                <!-- Chat Header -->
                <div class="px-6 py-4 border-b border-neutral-200 flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Conversation</h3>
                        <p class="text-sm text-gray-600">{{ $chatRoom->participants->count() }} participants</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 rounded-full {{ $chatRoom->status === 'active' ? 'bg-green-500' : 'bg-gray-400' }}"></div>
                        <span class="text-sm text-gray-600">{{ ucfirst($chatRoom->status) }}</span>
                    </div>
                </div>

                <!-- Messages Container -->
                <div id="messages-container" class="flex-1 overflow-y-auto p-6 space-y-4">
                    @forelse($messages as $message)
                        <div class="flex {{ $message->sender_type === 'staff' ? 'justify-end' : 'justify-start' }}">
                            <div class="max-w-xs lg:max-w-md">
                                <div class="flex items-center space-x-2 mb-1">
                                    @if($message->sender_type !== 'staff')
                                        <div class="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center">
                                            <svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                    @endif
                                    <span class="text-xs font-medium text-gray-600">
                                        @if($message->sender_type === 'ai')
                                            🤖 AI Assistant
                                        @elseif($message->sender_type === 'staff')
                                            👨‍💼 {{ $message->user->name ?? 'Staff' }}
                                        @else
                                            👤 {{ $message->user->name ?? 'Visitor' }}
                                        @endif
                                    </span>
                                    <span class="text-xs text-gray-500">{{ $message->created_at->format('H:i') }}</span>
                                </div>
                                <div class="rounded-lg px-4 py-2 {{ $message->sender_type === 'staff' ? 'bg-blue-600 text-white' : ($message->sender_type === 'ai' ? 'bg-gray-100 text-gray-900' : 'bg-gray-200 text-gray-900') }}">
                                    <p class="text-sm">{{ $message->content }}</p>
                                    @if($message->files->isNotEmpty())
                                        <div class="mt-2 space-y-1">
                                            @foreach($message->files as $file)
                                                <a href="{{ $file->url }}" target="_blank" class="block text-xs underline">
                                                    📎 {{ $file->original_name }}
                                                </a>
                                            @endforeach
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-8">
                            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                            <p class="text-gray-500">No messages yet</p>
                        </div>
                    @endforelse
                </div>

                <!-- Message Input -->
                @if($canRespond && $chatRoom->status !== 'closed')
                    <div class="px-6 py-4 border-t border-neutral-200">
                        <form id="message-form" class="flex space-x-3">
                            <div class="flex-1">
                                <textarea 
                                    id="message-input" 
                                    name="message" 
                                    rows="2" 
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none" 
                                    placeholder="Type your message..."
                                    required></textarea>
                            </div>
                            <div class="flex flex-col space-y-2">
                                <button type="submit" class="btn btn-primary px-4 py-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                    </svg>
                                </button>
                                <label for="file-input" class="btn btn-secondary px-4 py-2 cursor-pointer">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                    </svg>
                                </label>
                                <input type="file" id="file-input" name="files[]" multiple class="hidden" accept="image/*,.pdf,.doc,.docx,.txt">
                            </div>
                        </form>
                        <div id="file-preview" class="mt-2 hidden">
                            <div class="text-sm text-gray-600 mb-2">Selected files:</div>
                            <div id="file-list" class="space-y-1"></div>
                        </div>
                    </div>
                @else
                    <div class="px-6 py-4 border-t border-neutral-200 bg-gray-50">
                        <p class="text-center text-gray-500">
                            @if(!$canRespond)
                                You don't have permission to respond to this chat.
                            @else
                                This chat room is closed.
                            @endif
                        </p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Room Info -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Room Information</h4>
                <div class="space-y-3">
                    <div>
                        <span class="text-sm font-medium text-gray-600">Room ID:</span>
                        <span class="text-sm text-gray-900 ml-2">{{ $chatRoom->uuid }}</span>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-600">Type:</span>
                        <span class="text-sm text-gray-900 ml-2">{{ ucfirst($chatRoom->type) }}</span>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-600">Language:</span>
                        <span class="text-sm text-gray-900 ml-2">{{ strtoupper($chatRoom->language) }}</span>
                    </div>
                    @if($chatRoom->currentAssignment)
                        <div>
                            <span class="text-sm font-medium text-gray-600">Assigned to:</span>
                            <span class="text-sm text-gray-900 ml-2">{{ $chatRoom->currentAssignment->assignedStaff->name }}</span>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Participants -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Participants</h4>
                <div class="space-y-3">
                    @foreach($chatRoom->participants as $participant)
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center">
                                <svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-900">
                                    {{ $participant->user->name ?? $participant->visitor_name ?? 'Anonymous' }}
                                </div>
                                <div class="text-xs text-gray-500">
                                    {{ $participant->user ? 'Registered User' : 'Visitor' }}
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h4>
                <div class="space-y-2">
                    <button onclick="updatePriority()" class="w-full btn btn-secondary text-left">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        Update Priority
                    </button>
                    <button onclick="transferChat()" class="w-full btn btn-secondary text-left">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                        </svg>
                        Transfer Chat
                    </button>
                    @if($chatRoom->status !== 'closed')
                        <button onclick="updateRoomStatus('resolved')" class="w-full btn btn-success text-left">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Mark Resolved
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Assignment Modal -->
<div id="assignmentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Assign Chat Room</h3>
            </div>
            <form id="assignmentForm">
                <div class="px-6 py-4">
                    <label for="staff_id" class="block text-sm font-medium text-gray-700 mb-2">Assign to Staff Member</label>
                    <select name="staff_id" id="staff_id" class="form-select w-full" required>
                        <option value="">Select staff member...</option>
                        @foreach($availableStaff as $staff)
                            <option value="{{ $staff->id }}" {{ $chatRoom->currentAssignment && $chatRoom->currentAssignment->assigned_to === $staff->id ? 'selected' : '' }}>
                                {{ $staff->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                    <button type="button" onclick="closeAssignmentModal()" class="btn btn-secondary">Cancel</button>
                    <button type="submit" class="btn btn-primary">Assign</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
let lastMessageId = {{ $messages->last()?->id ?? 0 }};

// Auto-scroll to bottom
function scrollToBottom() {
    const container = document.getElementById('messages-container');
    container.scrollTop = container.scrollHeight;
}

// Initial scroll to bottom
scrollToBottom();

// Message form submission
document.getElementById('message-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const messageInput = document.getElementById('message-input');
    const fileInput = document.getElementById('file-input');
    
    // Add files to form data
    for (let i = 0; i < fileInput.files.length; i++) {
        formData.append('files[]', fileInput.files[i]);
    }
    
    fetch(`{{ route('admin.chat.rooms.messages.store', $chatRoom) }}`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            messageInput.value = '';
            fileInput.value = '';
            hideFilePreview();
            loadNewMessages();
        } else {
            alert('Failed to send message: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while sending the message.');
    });
});

// File input handling
document.getElementById('file-input').addEventListener('change', function(e) {
    const files = e.target.files;
    if (files.length > 0) {
        showFilePreview(files);
    } else {
        hideFilePreview();
    }
});

function showFilePreview(files) {
    const preview = document.getElementById('file-preview');
    const fileList = document.getElementById('file-list');
    
    fileList.innerHTML = '';
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const fileItem = document.createElement('div');
        fileItem.className = 'text-sm text-gray-600';
        fileItem.textContent = `📎 ${file.name} (${(file.size / 1024).toFixed(1)} KB)`;
        fileList.appendChild(fileItem);
    }
    
    preview.classList.remove('hidden');
}

function hideFilePreview() {
    document.getElementById('file-preview').classList.add('hidden');
}

// Load new messages
function loadNewMessages() {
    fetch(`{{ route('admin.chat.rooms.updates', $chatRoom) }}?last_message_id=${lastMessageId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data.messages.length > 0) {
                appendNewMessages(data.data.messages);
                lastMessageId = data.data.last_message_id;
            }
        })
        .catch(error => console.error('Error loading new messages:', error));
}

function appendNewMessages(messages) {
    const container = document.getElementById('messages-container');
    
    messages.forEach(message => {
        const messageDiv = createMessageElement(message);
        container.appendChild(messageDiv);
    });
    
    scrollToBottom();
}

function createMessageElement(message) {
    // This would create the message HTML element
    // Implementation depends on your message structure
    const div = document.createElement('div');
    div.innerHTML = `
        <div class="flex ${message.sender_type === 'staff' ? 'justify-end' : 'justify-start'}">
            <div class="max-w-xs lg:max-w-md">
                <div class="flex items-center space-x-2 mb-1">
                    <span class="text-xs font-medium text-gray-600">
                        ${message.sender_type === 'staff' ? '👨‍💼 ' + (message.user?.name || 'Staff') : '👤 ' + (message.user?.name || 'Visitor')}
                    </span>
                    <span class="text-xs text-gray-500">${new Date(message.created_at).toLocaleTimeString('en-US', {hour: '2-digit', minute:'2-digit'})}</span>
                </div>
                <div class="rounded-lg px-4 py-2 ${message.sender_type === 'staff' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-900'}">
                    <p class="text-sm">${message.content}</p>
                </div>
            </div>
        </div>
    `;
    return div.firstElementChild;
}

// Poll for new messages every 3 seconds
setInterval(loadNewMessages, 3000);

// Assignment modal functions
function showAssignmentModal() {
    document.getElementById('assignmentModal').classList.remove('hidden');
}

function closeAssignmentModal() {
    document.getElementById('assignmentModal').classList.add('hidden');
}

// Assignment form submission
document.getElementById('assignmentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch(`{{ route('admin.chat.rooms.assign', $chatRoom) }}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            staff_id: formData.get('staff_id')
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeAssignmentModal();
            location.reload();
        } else {
            alert('Failed to assign chat room: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while assigning the chat room.');
    });
});

// Room status update
function updateRoomStatus(status) {
    if (confirm(`Are you sure you want to mark this chat as ${status}?`)) {
        fetch(`{{ route('admin.chat.rooms.update', $chatRoom) }}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ status: status })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to update room status: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the room status.');
        });
    }
}

// Placeholder functions for other actions
function updatePriority() {
    const priority = prompt('Enter new priority (1-5):', '{{ $chatRoom->priority }}');
    if (priority && priority >= 1 && priority <= 5) {
        fetch(`{{ route('admin.chat.rooms.update', $chatRoom) }}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ priority: parseInt(priority) })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to update priority: ' + data.message);
            }
        });
    }
}

function transferChat() {
    showAssignmentModal();
}
</script>
@endsection
