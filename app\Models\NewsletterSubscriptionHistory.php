<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class NewsletterSubscriptionHistory extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'newsletter_subscription_id',
        'action',
        'status_from',
        'status_to',
        'description',
        'metadata',
        'triggered_by',
        'admin_user_id',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'metadata' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($history) {
            if (empty($history->uuid)) {
                $history->uuid = Str::uuid();
            }
        });
    }

    /**
     * Get the newsletter subscription that owns this history entry.
     */
    public function newsletterSubscription(): BelongsTo
    {
        return $this->belongsTo(NewsletterSubscription::class);
    }

    /**
     * Get the admin user who triggered this action.
     */
    public function adminUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'admin_user_id');
    }

    /**
     * Get the action icon based on action type.
     */
    public function getActionIconAttribute(): string
    {
        return match($this->action) {
            'subscribed' => 'M12 6v6m0 0v6m0-6h6m-6 0H6',
            'unsubscribed' => 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z',
            'resubscribed' => 'M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15',
            'verified' => 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z',
            'activated' => 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
            'deactivated' => 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z',
            default => 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
        };
    }

    /**
     * Get the action color based on action type.
     */
    public function getActionColorAttribute(): string
    {
        return match($this->action) {
            'subscribed' => 'blue',
            'unsubscribed' => 'red',
            'resubscribed' => 'green',
            'verified' => 'purple',
            'activated' => 'green',
            'deactivated' => 'red',
            default => 'gray',
        };
    }

    /**
     * Get formatted action description.
     */
    public function getFormattedDescriptionAttribute(): string
    {
        if ($this->description) {
            return $this->description;
        }

        return match($this->action) {
            'subscribed' => 'Subscribed to newsletter',
            'unsubscribed' => 'Unsubscribed from newsletter',
            'resubscribed' => 'Resubscribed to newsletter',
            'verified' => 'Email address verified',
            'activated' => 'Subscription activated by admin',
            'deactivated' => 'Subscription deactivated by admin',
            default => ucfirst($this->action),
        };
    }
}
