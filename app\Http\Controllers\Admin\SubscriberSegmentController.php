<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\NewsletterSubscription;
use App\Models\SubscriberTag;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SubscriberSegmentController extends Controller
{
    /**
     * Display subscriber segmentation dashboard.
     */
    public function index(): View
    {
        // Get overall subscriber statistics
        $overallStats = $this->getOverallStatistics();

        // Get lifecycle distribution
        $lifecycleDistribution = NewsletterSubscription::select('lifecycle_stage', DB::raw('count(*) as count'))
            ->where('is_active', true)
            ->groupBy('lifecycle_stage')
            ->pluck('count', 'lifecycle_stage')
            ->toArray();

        // Get engagement distribution
        $engagementDistribution = NewsletterSubscription::selectRaw('
                CASE
                    WHEN engagement_score >= 80 THEN "High (80-100)"
                    WHEN engagement_score >= 60 THEN "Medium (60-79)"
                    WHEN engagement_score >= 40 THEN "Low (40-59)"
                    ELSE "Very Low (0-39)"
                END as engagement_level,
                COUNT(*) as count
            ')
            ->where('is_active', true)
            ->groupBy('engagement_level')
            ->pluck('count', 'engagement_level')
            ->toArray();

        // Get top tags
        $topTags = SubscriberTag::withCount(['subscribers' => function ($query) {
                $query->where('is_active', true);
            }])
            ->where('is_active', true)
            ->orderBy('subscribers_count', 'desc')
            ->limit(10)
            ->get();

        return view('admin.subscriber-segments.index', compact(
            'overallStats',
            'lifecycleDistribution',
            'engagementDistribution',
            'topTags'
        ));
    }

    /**
     * Get subscribers based on segmentation criteria.
     */
    public function getSegmentedSubscribers(Request $request): JsonResponse
    {
        $query = NewsletterSubscription::with(['tags']);

        // Apply filters
        $this->applySegmentationFilters($query, $request->all());

        // Get results
        $subscribers = $query->paginate(20);

        return response()->json([
            'success' => true,
            'subscribers' => $subscribers,
            'total_count' => $subscribers->total(),
        ]);
    }

    /**
     * Preview segment count.
     */
    public function previewSegment(Request $request): JsonResponse
    {
        $query = NewsletterSubscription::query();

        // Apply filters
        $this->applySegmentationFilters($query, $request->all());

        $count = $query->count();

        return response()->json([
            'success' => true,
            'count' => $count,
        ]);
    }

    /**
     * Export segmented subscribers.
     */
    public function exportSegment(Request $request): \Symfony\Component\HttpFoundation\StreamedResponse
    {
        $query = NewsletterSubscription::with(['tags']);

        // Apply filters
        $this->applySegmentationFilters($query, $request->all());

        $filename = 'subscriber_segment_' . date('Y-m-d_H-i-s') . '.csv';

        return response()->streamDownload(function () use ($query) {
            $handle = fopen('php://output', 'w');

            // CSV headers
            fputcsv($handle, [
                'Email', 'Name', 'Status', 'Lifecycle Stage', 'Engagement Score',
                'Total Emails Opened', 'Total Emails Clicked', 'Last Activity',
                'Subscription Date', 'Source', 'Tags'
            ]);

            // Export data in chunks
            $query->chunk(100, function ($subscribers) use ($handle) {
                foreach ($subscribers as $subscriber) {
                    fputcsv($handle, [
                        $subscriber->email,
                        $subscriber->name ?? '',
                        $subscriber->is_active ? 'Active' : 'Inactive',
                        $subscriber->lifecycle_stage,
                        $subscriber->engagement_score,
                        $subscriber->total_emails_opened,
                        $subscriber->total_emails_clicked,
                        $subscriber->last_activity_at?->format('Y-m-d H:i:s') ?? '',
                        $subscriber->created_at->format('Y-m-d H:i:s'),
                        $subscriber->subscription_source ?? '',
                        $subscriber->tags->pluck('name')->implode(', '),
                    ]);
                }
            });

            fclose($handle);
        }, $filename, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ]);
    }

    /**
     * Bulk tag assignment to segmented subscribers.
     */
    public function bulkTagAssignment(Request $request): JsonResponse
    {
        $request->validate([
            'tag_ids' => 'required|array|min:1',
            'tag_ids.*' => 'exists:subscriber_tags,id',
            'action' => 'required|string|in:add,remove',
        ]);

        $query = NewsletterSubscription::query();
        $this->applySegmentationFilters($query, $request->all());

        $subscriberIds = $query->pluck('id')->toArray();
        $tagIds = $request->get('tag_ids');
        $action = $request->get('action');

        if (empty($subscriberIds)) {
            return response()->json([
                'success' => false,
                'message' => 'No subscribers match the current segmentation criteria.',
            ], 422);
        }

        $affectedCount = 0;

        foreach ($tagIds as $tagId) {
            $tag = SubscriberTag::find($tagId);
            if (!$tag) continue;

            if ($action === 'add') {
                // Get subscribers that don't already have this tag
                $existingSubscriberIds = $tag->subscribers()->pluck('newsletter_subscriptions.id')->toArray();
                $newSubscriberIds = array_diff($subscriberIds, $existingSubscriberIds);

                if (!empty($newSubscriberIds)) {
                    $tag->subscribers()->attach($newSubscriberIds, [
                        'tagged_at' => now(),
                        'tagged_by' => auth()->id(),
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                    $affectedCount += count($newSubscriberIds);
                }
            } else {
                $tag->subscribers()->detach($subscriberIds);
                $affectedCount += count($subscriberIds);
            }
        }

        $actionText = $action === 'add' ? 'assigned to' : 'removed from';
        $tagText = count($tagIds) === 1 ? 'tag' : 'tags';

        return response()->json([
            'success' => true,
            'message' => "Tags {$actionText} {$affectedCount} subscribers successfully.",
            'affected_count' => $affectedCount,
        ]);
    }

    /**
     * Get lifecycle stage analytics.
     */
    public function getLifecycleAnalytics(): JsonResponse
    {
        $analytics = NewsletterSubscription::selectRaw('
                lifecycle_stage,
                COUNT(*) as total_count,
                AVG(engagement_score) as avg_engagement,
                AVG(total_emails_opened) as avg_opens,
                AVG(total_emails_clicked) as avg_clicks,
                SUM(CASE WHEN last_activity_at >= ? THEN 1 ELSE 0 END) as active_last_30_days
            ', [Carbon::now()->subDays(30)])
            ->where('is_active', true)
            ->groupBy('lifecycle_stage')
            ->get();

        return response()->json([
            'success' => true,
            'analytics' => $analytics,
        ]);
    }

    /**
     * Get engagement analytics.
     */
    public function getEngagementAnalytics(): JsonResponse
    {
        $analytics = NewsletterSubscription::selectRaw('
                CASE
                    WHEN engagement_score >= 80 THEN "High"
                    WHEN engagement_score >= 60 THEN "Medium"
                    WHEN engagement_score >= 40 THEN "Low"
                    ELSE "Very Low"
                END as engagement_level,
                COUNT(*) as total_count,
                AVG(total_emails_opened) as avg_opens,
                AVG(total_emails_clicked) as avg_clicks,
                SUM(CASE WHEN last_activity_at >= ? THEN 1 ELSE 0 END) as active_last_30_days
            ', [Carbon::now()->subDays(30)])
            ->where('is_active', true)
            ->groupBy('engagement_level')
            ->get();

        return response()->json([
            'success' => true,
            'analytics' => $analytics,
        ]);
    }

    /**
     * Apply segmentation filters to query.
     */
    private function applySegmentationFilters($query, array $filters): void
    {
        // Status filter
        if (isset($filters['status'])) {
            if ($filters['status'] === 'active') {
                $query->where('is_active', true);
            } elseif ($filters['status'] === 'inactive') {
                $query->where('is_active', false);
            }
        } else {
            // Default to active subscribers only
            $query->where('is_active', true);
        }

        // Lifecycle stage filter
        if (!empty($filters['lifecycle_stages'])) {
            $query->whereIn('lifecycle_stage', $filters['lifecycle_stages']);
        }

        // Engagement score filter
        if (isset($filters['engagement_min'])) {
            $query->where('engagement_score', '>=', $filters['engagement_min']);
        }
        if (isset($filters['engagement_max'])) {
            $query->where('engagement_score', '<=', $filters['engagement_max']);
        }

        // Activity filter
        if (isset($filters['last_activity_days'])) {
            $date = Carbon::now()->subDays($filters['last_activity_days']);
            $query->where('last_activity_at', '>=', $date);
        }

        // Subscription date filter
        if (isset($filters['subscribed_after'])) {
            $query->where('created_at', '>=', $filters['subscribed_after']);
        }
        if (isset($filters['subscribed_before'])) {
            $query->where('created_at', '<=', $filters['subscribed_before']);
        }

        // Email activity filters
        if (isset($filters['min_opens'])) {
            $query->where('total_emails_opened', '>=', $filters['min_opens']);
        }
        if (isset($filters['min_clicks'])) {
            $query->where('total_emails_clicked', '>=', $filters['min_clicks']);
        }

        // Tag filters
        if (!empty($filters['include_tags'])) {
            $query->whereHas('tags', function ($q) use ($filters) {
                $q->whereIn('subscriber_tags.id', $filters['include_tags']);
            });
        }
        if (!empty($filters['exclude_tags'])) {
            $query->whereDoesntHave('tags', function ($q) use ($filters) {
                $q->whereIn('subscriber_tags.id', $filters['exclude_tags']);
            });
        }

        // Source filter
        if (!empty($filters['sources'])) {
            $query->whereIn('subscription_source', $filters['sources']);
        }

        // Search filter
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('email', 'like', "%{$search}%")
                  ->orWhere('name', 'like', "%{$search}%");
            });
        }
    }

    /**
     * Get overall statistics.
     */
    private function getOverallStatistics(): array
    {
        $totalSubscribers = NewsletterSubscription::count();
        $activeSubscribers = NewsletterSubscription::where('is_active', true)->count();
        $newThisMonth = NewsletterSubscription::where('created_at', '>=', Carbon::now()->startOfMonth())->count();
        $activeThisMonth = NewsletterSubscription::where('last_activity_at', '>=', Carbon::now()->startOfMonth())->count();

        return [
            'total_subscribers' => $totalSubscribers,
            'active_subscribers' => $activeSubscribers,
            'inactive_subscribers' => $totalSubscribers - $activeSubscribers,
            'new_this_month' => $newThisMonth,
            'active_this_month' => $activeThisMonth,
            'avg_engagement_score' => NewsletterSubscription::where('is_active', true)->avg('engagement_score') ?? 0,
        ];
    }
}
