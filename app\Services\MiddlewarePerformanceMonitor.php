<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class MiddlewarePerformanceMonitor
{
    private static $timers = [];
    private static $counters = [];
    
    /**
     * Start timing a middleware operation
     */
    public static function startTimer(string $middleware, string $operation = 'total'): void
    {
        $key = "{$middleware}.{$operation}";
        self::$timers[$key] = microtime(true);
    }
    
    /**
     * End timing and log if slow
     */
    public static function endTimer(string $middleware, string $operation = 'total', float $warningThreshold = 0.1): float
    {
        $key = "{$middleware}.{$operation}";
        
        if (!isset(self::$timers[$key])) {
            return 0;
        }
        
        $duration = microtime(true) - self::$timers[$key];
        unset(self::$timers[$key]);
        
        // Log slow operations
        if ($duration > $warningThreshold) {
            Log::warning("Slow middleware operation detected", [
                'middleware' => $middleware,
                'operation' => $operation,
                'duration_ms' => round($duration * 1000, 2),
                'threshold_ms' => round($warningThreshold * 1000, 2)
            ]);
        }
        
        // Update performance metrics
        self::updateMetrics($middleware, $operation, $duration);
        
        return $duration;
    }
    
    /**
     * Increment a counter for middleware operations
     */
    public static function incrementCounter(string $middleware, string $counter): void
    {
        $key = "{$middleware}.{$counter}";
        
        if (!isset(self::$counters[$key])) {
            self::$counters[$key] = 0;
        }
        
        self::$counters[$key]++;
        
        // Log high frequency operations
        if (self::$counters[$key] % 100 === 0) {
            Log::info("High frequency middleware operation", [
                'middleware' => $middleware,
                'counter' => $counter,
                'count' => self::$counters[$key]
            ]);
        }
    }
    
    /**
     * Update performance metrics in cache
     */
    private static function updateMetrics(string $middleware, string $operation, float $duration): void
    {
        $cacheKey = "middleware_perf_{$middleware}_{$operation}";
        
        $metrics = Cache::get($cacheKey, [
            'count' => 0,
            'total_time' => 0,
            'max_time' => 0,
            'min_time' => PHP_FLOAT_MAX,
            'avg_time' => 0
        ]);
        
        $metrics['count']++;
        $metrics['total_time'] += $duration;
        $metrics['max_time'] = max($metrics['max_time'], $duration);
        $metrics['min_time'] = min($metrics['min_time'], $duration);
        $metrics['avg_time'] = $metrics['total_time'] / $metrics['count'];
        
        Cache::put($cacheKey, $metrics, 3600); // 1 hour
    }
    
    /**
     * Get performance metrics for a middleware
     */
    public static function getMetrics(string $middleware, string $operation = 'total'): array
    {
        $cacheKey = "middleware_perf_{$middleware}_{$operation}";
        return Cache::get($cacheKey, []);
    }
    
    /**
     * Get all performance metrics
     */
    public static function getAllMetrics(): array
    {
        $pattern = 'middleware_perf_*';
        $keys = Cache::getRedis()->keys($pattern);
        $metrics = [];
        
        foreach ($keys as $key) {
            $metrics[$key] = Cache::get($key, []);
        }
        
        return $metrics;
    }
    
    /**
     * Clear all performance metrics
     */
    public static function clearMetrics(): void
    {
        $pattern = 'middleware_perf_*';
        $keys = Cache::getRedis()->keys($pattern);
        
        foreach ($keys as $key) {
            Cache::forget($key);
        }
        
        self::$timers = [];
        self::$counters = [];
    }
    
    /**
     * Check if middleware is performing poorly
     */
    public static function isPerformingPoorly(string $middleware, string $operation = 'total'): bool
    {
        $metrics = self::getMetrics($middleware, $operation);
        
        if (empty($metrics) || $metrics['count'] < 10) {
            return false; // Not enough data
        }
        
        // Consider poor performance if:
        // - Average time > 100ms
        // - Max time > 500ms
        // - More than 50% of requests are slow
        return $metrics['avg_time'] > 0.1 || 
               $metrics['max_time'] > 0.5;
    }
    
    /**
     * Get performance summary
     */
    public static function getPerformanceSummary(): array
    {
        $allMetrics = self::getAllMetrics();
        $summary = [];
        
        foreach ($allMetrics as $key => $metrics) {
            if (empty($metrics) || $metrics['count'] === 0) {
                continue;
            }
            
            $parts = explode('_', str_replace('middleware_perf_', '', $key));
            $middleware = $parts[0] ?? 'unknown';
            $operation = $parts[1] ?? 'total';
            
            $summary[] = [
                'middleware' => $middleware,
                'operation' => $operation,
                'requests' => $metrics['count'],
                'avg_time_ms' => round($metrics['avg_time'] * 1000, 2),
                'max_time_ms' => round($metrics['max_time'] * 1000, 2),
                'min_time_ms' => round($metrics['min_time'] * 1000, 2),
                'total_time_ms' => round($metrics['total_time'] * 1000, 2),
                'is_slow' => self::isPerformingPoorly($middleware, $operation)
            ];
        }
        
        // Sort by average time descending
        usort($summary, function($a, $b) {
            return $b['avg_time_ms'] <=> $a['avg_time_ms'];
        });
        
        return $summary;
    }
    
    /**
     * Log performance summary
     */
    public static function logPerformanceSummary(): void
    {
        $summary = self::getPerformanceSummary();
        
        if (empty($summary)) {
            return;
        }
        
        Log::info('Middleware Performance Summary', [
            'summary' => $summary,
            'slow_middleware' => array_filter($summary, fn($item) => $item['is_slow'])
        ]);
    }
}
