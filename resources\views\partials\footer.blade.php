<!-- Footer -->
<footer class="bg-gray-900 !text-white">
    <!-- Main <PERSON>er -->
    <div class="container mx-auto px-4 py-12">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
            <!-- Company Info - Equal width column -->
            <div>
                <div class="flex items-center mb-3">
                    <img src="{{ asset('images/logo-white.svg') }}" alt="{{ __('common.company_name') }}" class="h-6 w-auto">
                    <span class="ml-2 text-lg font-bold text-white">{{ __('common.company_name') }}</span>
                </div>
                <p class="text-gray-300 mb-4 text-sm">{{ __('common.company_description') }}</p>
                <div class="flex space-x-3">
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                        </svg>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                        </svg>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Services -->
            <div>
                <h3 class="text-lg font-semibold mb-4 nav-link !text-white">{{ __('common.services') }}</h3>
                <ul class="space-y-2">
                    <li><a href="{{ route('services.web-development', ['locale' => app()->getLocale()]) }}" class="text-gray-300 hover:text-white transition-colors">{{ __('common.web_development') }}</a></li>
                    <li><a href="{{ route('services.mobile-app-development', ['locale' => app()->getLocale()]) }}" class="text-gray-300 hover:text-white transition-colors">{{ __('common.mobile_app_development') }}</a></li>
                    <li><a href="{{ route('services.ecommerce-development', ['locale' => app()->getLocale()]) }}" class="text-gray-300 hover:text-white transition-colors">{{ __('common.ecommerce_development') }}</a></li>
                    <li><a href="{{ route('services.digital-marketing', ['locale' => app()->getLocale()]) }}" class="text-gray-300 hover:text-white transition-colors">{{ __('common.digital_marketing') }}</a></li>
                    <li><a href="{{ route('services.data-analytics', ['locale' => app()->getLocale()]) }}" class="text-gray-300 hover:text-white transition-colors">Data Analytics</a></li>
                    <li><a href="{{ route('services.seo-services', ['locale' => app()->getLocale()]) }}" class="text-gray-300 hover:text-white transition-colors">{{ __('common.seo_services') }}</a></li>
                    <li><a href="{{ route('services.accounting-services', ['locale' => app()->getLocale()]) }}" class="text-gray-300 hover:text-white transition-colors">{{ __('common.accounting_services') }}</a></li>
                    <li><a href="{{ route('services.maintenance-support', ['locale' => app()->getLocale()]) }}" class="text-gray-300 hover:text-white transition-colors">{{ __('common.maintenance_support') }}</a></li>
                    <li><a href="{{ route('services.system-design', ['locale' => app()->getLocale()]) }}" class="text-gray-300 hover:text-white transition-colors">System Design</a></li>
                </ul>
            </div>

            <!-- Quick Links -->
            <div>
                <h3 class="text-lg font-semibold mb-4 nav-link !text-white">Quick Links</h3>
                <ul class="space-y-2">
                    <li><a href="{{ route('about', ['locale' => app()->getLocale()]) }}" class="text-gray-300 hover:text-white transition-colors">{{ __('common.about') }}</a></li>
                    <li><a href="{{ route('projects.index', ['locale' => app()->getLocale()]) }}" class="text-gray-300 hover:text-white transition-colors">{{ __('common.projects') }}</a></li>
                    <li><a href="{{ route('blog.index', ['locale' => app()->getLocale()]) }}" class="text-gray-300 hover:text-white transition-colors">{{ __('common.blog') }}</a></li>
                    <li><a href="{{ route('contact', ['locale' => app()->getLocale()]) }}" class="text-gray-300 hover:text-white transition-colors">{{ __('common.contact') }}</a></li>
                    <li><a href="{{ route('shop.index', ['locale' => app()->getLocale()]) }}" class="text-gray-300 hover:text-white transition-colors">{{ __('common.shop') }}</a></li>
                    <li><a href="{{ route('apply.project', ['locale' => app()->getLocale()]) }}" class="text-gray-300 hover:text-white transition-colors">Start Project</a></li>
                    <li><a href="{{ route('careers.index', ['locale' => app()->getLocale()]) }}" class="text-gray-300 hover:text-white transition-colors">Careers</a></li>
                </ul>
            </div>

            <!-- Shop Categories -->
            <div>
                <h3 class="text-lg font-semibold mb-4 nav-link !text-white">Shop Categories</h3>
                <ul class="space-y-2">
                    @php
                        $featuredCategories = \App\Models\ProductCategory::active()
                            ->where('is_featured', true)
                            ->root()
                            ->ordered()
                            ->limit(5)
                            ->get();
                    @endphp
                    @forelse($featuredCategories as $category)
                        <li><a href="{{ route('shop.category', ['category' => $category->slug, 'locale' => app()->getLocale()]) }}" class="text-gray-300 hover:text-white transition-colors">{{ $category->name }}</a></li>
                    @empty
                        <li><a href="{{ route('shop.index', ['locale' => app()->getLocale()]) }}" class="text-gray-300 hover:text-white transition-colors">All Products</a></li>
                        <li><a href="{{ route('shop.index', ['locale' => app()->getLocale()]) }}?featured=1" class="text-gray-300 hover:text-white transition-colors">Featured Items</a></li>
                        <li><a href="{{ route('shop.index', ['locale' => app()->getLocale()]) }}?sort=latest" class="text-gray-300 hover:text-white transition-colors">New Arrivals</a></li>
                        <li><a href="{{ route('shop.index', ['locale' => app()->getLocale()]) }}?sort=popular" class="text-gray-300 hover:text-white transition-colors">Best Sellers</a></li>
                        <li><a href="{{ route('shop.index', ['locale' => app()->getLocale()]) }}?on_sale=1" class="text-gray-300 hover:text-white transition-colors">Sale Items</a></li>
                    @endforelse
                </ul>
            </div>

            <!-- Contact Info -->
            <div>
                <h3 class="text-lg font-semibold mb-4 nav-link !text-white">{{ __('common.contact') }}</h3>
                <div class="space-y-3">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-gray-400 mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                        </svg>
                        <div>
                            <p class="text-gray-300 text-sm">{{ config('company.address.street') }}</p>
                            <p class="text-gray-300 text-sm">{{ config('company.address.city') }}, {{ config('company.address.postal_code') }}</p>
                            <p class="text-gray-300 text-sm">{{ config('company.address.country') }}</p>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-gray-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                        </svg>
                        <a href="tel:{{ str_replace(' ', '', config('company.contact.phone')) }}" class="text-gray-300 hover:text-white transition-colors text-sm">{{ config('company.contact.phone') }}</a>
                    </div>
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-gray-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                        </svg>
                        <a href="mailto:{{ config('company.contact.info_email') }}" class="text-gray-300 hover:text-white transition-colors text-sm">{{ config('company.contact.info_email') }}</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Newsletter Signup -->
    <div class="border-t border-gray-800">
        <div class="container mx-auto px-4 py-8">
            <div class="max-w-md mx-auto text-center">
                <h3 class="text-lg font-semibold mb-2 nav-link !text-white">{{ __('common.stay_updated') }}</h3>
                <p class="text-gray-300 mb-4">{{ __('common.newsletter_description') }}</p>

                <!-- Success/Error Messages -->
                <div id="footer-newsletter-message" class="mb-4 hidden">
                    <div id="footer-newsletter-message-content" class="p-3 rounded-lg text-sm"></div>
                </div>

                <form id="footer-newsletter-form" action="{{ route('newsletter.subscribe', ['locale' => app()->getLocale()]) }}" method="POST" class="space-y-3">
                    @csrf
                    <div class="flex flex-col sm:flex-row w-full max-w-md mx-auto overflow-hidden rounded-lg border border-gray-700 bg-gray-800">
                      <input
                        type="email"
                        name="email"
                        placeholder="Enter your email"
                        required
                        class="flex-1 px-4 py-2 bg-gray-800 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                      />
                      <button
                        type="submit"
                        id="footer-newsletter-submit"
                        class="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 transition-colors sm:rounded-r-lg sm:rounded-none"
                      >
                        {{ __('common.subscribe') }}
                      </button>
                    </div>

                    <div class="flex items-center justify-center">
                        <input type="checkbox" id="footer_newsletter_consent" name="consent" required
                               class="h-3 w-3 text-blue-600 focus:ring-blue-500 border-gray-600 rounded bg-gray-800">
                        <label for="footer_newsletter_consent" class="ml-2 text-xs text-gray-400">
                            {{ __('common.newsletter_consent') }}
                        </label>
                    </div>
                </form>

                <div class="mt-2">
                    <p class="text-xs text-gray-500">
                        <a href="{{ route('newsletter.unsubscribe.form', ['locale' => app()->getLocale()]) }}" class="text-blue-400 hover:text-blue-300 underline">
                            {{ __('common.unsubscribe') }}
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Footer -->
    <div class="border-t border-gray-800">
        <div class="container mx-auto px-4 py-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-4">
                    <p class="text-gray-400 text-sm">
                        &copy {{ date('Y') }} {{ __('common.company_name') }} All rights reserved.
                    </p>
                    <div class="flex items-center space-x-4 text-gray-400 text-sm">
                        <a href="#" class="hover:text-white transition-colors">Privacy Policy</a>
                        <span>•</span>
                        <a href="#" class="hover:text-white transition-colors">Terms of Service</a>
                    </div>
                </div>
                <div class="flex items-center space-x-2 mt-2 md:mt-0">
                    <img src="{{ asset('images/payment-visa.png') }}" alt="Visa" class="h-6">
                    <img src="{{ asset('images/payment-mastercard.png') }}" alt="Mastercard" class="h-6">
                    <img src="{{ asset('images/payment-paypal.png') }}" alt="PayPal" class="h-6">
                </div>
            </div>
        </div>
    </div>
</footer>