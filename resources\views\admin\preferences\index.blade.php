@extends('layouts.dashboard')

@section('title', 'User Preferences')

@section('content')
<div class="max-w-6xl mx-auto">
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">User Preferences</h1>
        <p class="mt-2 text-gray-600">Customize your dashboard experience and system settings</p>
    </div>

    <!-- Success/Error Messages -->
    <div id="message-container" class="mb-6 hidden">
        <div id="success-message" class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg hidden">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <span id="success-text"></span>
            </div>
        </div>
        <div id="error-message" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg hidden">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                <span id="error-text"></span>
            </div>
        </div>
    </div>

    <form id="preferences-form" class="space-y-8">
        @csrf
        
        <!-- General Preferences -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    General Preferences
                </h2>
                <p class="text-sm text-gray-600">Basic settings for language, timezone, and display</p>
            </div>

            <div class="p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Language -->
                <div>
                    <label for="language" class="block text-sm font-medium text-gray-700 mb-2">Language</label>
                    <select name="language" id="language" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        @foreach($availableLanguages as $code => $name)
                            <option value="{{ $code }}" {{ $preferences->language === $code ? 'selected' : '' }}>
                                {{ $name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Timezone -->
                <div>
                    <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
                    <select name="timezone" id="timezone" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        @foreach($availableTimezones as $code => $name)
                            <option value="{{ $code }}" {{ $preferences->timezone === $code ? 'selected' : '' }}>
                                {{ $name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Currency -->
                <div>
                    <label for="currency" class="block text-sm font-medium text-gray-700 mb-2">Currency</label>
                    <select name="currency" id="currency" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        @foreach($availableCurrencies as $code => $name)
                            <option value="{{ $code }}" {{ $preferences->currency === $code ? 'selected' : '' }}>
                                {{ $name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Date Format -->
                <div>
                    <label for="date_format" class="block text-sm font-medium text-gray-700 mb-2">Date Format</label>
                    <select name="date_format" id="date_format" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="Y-m-d" {{ $preferences->date_format === 'Y-m-d' ? 'selected' : '' }}>YYYY-MM-DD</option>
                        <option value="d/m/Y" {{ $preferences->date_format === 'd/m/Y' ? 'selected' : '' }}>DD/MM/YYYY</option>
                        <option value="m/d/Y" {{ $preferences->date_format === 'm/d/Y' ? 'selected' : '' }}>MM/DD/YYYY</option>
                        <option value="d-m-Y" {{ $preferences->date_format === 'd-m-Y' ? 'selected' : '' }}>DD-MM-YYYY</option>
                    </select>
                </div>

                <!-- Time Format -->
                <div>
                    <label for="time_format" class="block text-sm font-medium text-gray-700 mb-2">Time Format</label>
                    <select name="time_format" id="time_format" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="H:i" {{ $preferences->time_format === 'H:i' ? 'selected' : '' }}>24 Hour (14:30)</option>
                        <option value="h:i A" {{ $preferences->time_format === 'h:i A' ? 'selected' : '' }}>12 Hour (2:30 PM)</option>
                    </select>
                </div>

                <!-- Theme -->
                <div>
                    <label for="theme" class="block text-sm font-medium text-gray-700 mb-2">Theme</label>
                    <select name="theme" id="theme" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="light" {{ $preferences->theme === 'light' ? 'selected' : '' }}>Light</option>
                        <option value="dark" {{ $preferences->theme === 'dark' ? 'selected' : '' }}>Dark</option>
                        <option value="auto" {{ $preferences->theme === 'auto' ? 'selected' : '' }}>Auto (System)</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Dashboard Preferences -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Dashboard Preferences
                </h2>
                <p class="text-sm text-gray-600">Customize your dashboard layout and behavior</p>
            </div>

            <div class="p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Dashboard Layout -->
                <div>
                    <label for="dashboard_layout" class="block text-sm font-medium text-gray-700 mb-2">Dashboard Layout</label>
                    <select name="dashboard_layout" id="dashboard_layout" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="grid" {{ $preferences->dashboard_layout === 'grid' ? 'selected' : '' }}>Grid View</option>
                        <option value="list" {{ $preferences->dashboard_layout === 'list' ? 'selected' : '' }}>List View</option>
                        <option value="compact" {{ $preferences->dashboard_layout === 'compact' ? 'selected' : '' }}>Compact View</option>
                    </select>
                </div>

                <!-- Auto Refresh Interval -->
                <div>
                    <label for="auto_refresh_interval" class="block text-sm font-medium text-gray-700 mb-2">Auto Refresh (seconds)</label>
                    <select name="auto_refresh_interval" id="auto_refresh_interval" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="10" {{ $preferences->auto_refresh_interval === 10 ? 'selected' : '' }}>10 seconds</option>
                        <option value="30" {{ $preferences->auto_refresh_interval === 30 ? 'selected' : '' }}>30 seconds</option>
                        <option value="60" {{ $preferences->auto_refresh_interval === 60 ? 'selected' : '' }}>1 minute</option>
                        <option value="300" {{ $preferences->auto_refresh_interval === 300 ? 'selected' : '' }}>5 minutes</option>
                    </select>
                </div>

                <!-- Items Per Page -->
                <div>
                    <label for="items_per_page" class="block text-sm font-medium text-gray-700 mb-2">Items Per Page</label>
                    <select name="items_per_page" id="items_per_page" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="10" {{ $preferences->items_per_page === 10 ? 'selected' : '' }}>10</option>
                        <option value="25" {{ $preferences->items_per_page === 25 ? 'selected' : '' }}>25</option>
                        <option value="50" {{ $preferences->items_per_page === 50 ? 'selected' : '' }}>50</option>
                        <option value="100" {{ $preferences->items_per_page === 100 ? 'selected' : '' }}>100</option>
                    </select>
                </div>

                <!-- Boolean Options -->
                <div class="md:col-span-2 lg:col-span-3">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <label class="flex items-center">
                            <input type="checkbox" name="show_welcome_message" value="1" {{ $preferences->show_welcome_message ? 'checked' : '' }} class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">Show welcome message</span>
                        </label>

                        <label class="flex items-center">
                            <input type="checkbox" name="auto_refresh_dashboard" value="1" {{ $preferences->auto_refresh_dashboard ? 'checked' : '' }} class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">Auto refresh dashboard</span>
                        </label>

                        <label class="flex items-center">
                            <input type="checkbox" name="show_tooltips" value="1" {{ $preferences->show_tooltips ? 'checked' : '' }} class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">Show tooltips</span>
                        </label>

                        <label class="flex items-center">
                            <input type="checkbox" name="compact_mode" value="1" {{ $preferences->compact_mode ? 'checked' : '' }} class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">Compact mode</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notification Preferences -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2zM4 7h12V5H4v2z"></path>
                    </svg>
                    Notification Preferences
                </h2>
                <p class="text-sm text-gray-600">Control how and when you receive notifications</p>
            </div>

            <div class="p-6 space-y-6">
                <!-- Notification Types -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <label class="flex items-center">
                        <input type="checkbox" name="email_notifications" value="1" {{ $preferences->email_notifications ? 'checked' : '' }} class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">Email notifications</span>
                    </label>

                    <label class="flex items-center">
                        <input type="checkbox" name="browser_notifications" value="1" {{ $preferences->browser_notifications ? 'checked' : '' }} class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">Browser notifications</span>
                    </label>

                    <label class="flex items-center">
                        <input type="checkbox" name="sms_notifications" value="1" {{ $preferences->sms_notifications ? 'checked' : '' }} class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">SMS notifications</span>
                    </label>
                </div>

                <!-- Quiet Hours -->
                <div>
                    <h3 class="text-sm font-medium text-gray-700 mb-3">Quiet Hours</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="quiet_hours_start" class="block text-sm text-gray-600 mb-1">Start Time</label>
                            <input type="time" name="quiet_hours_start" id="quiet_hours_start" value="{{ $preferences->quiet_hours_start ? $preferences->quiet_hours_start->format('H:i') : '' }}" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                        <div>
                            <label for="quiet_hours_end" class="block text-sm text-gray-600 mb-1">End Time</label>
                            <input type="time" name="quiet_hours_end" id="quiet_hours_end" value="{{ $preferences->quiet_hours_end ? $preferences->quiet_hours_end->format('H:i') : '' }}" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">No notifications will be sent during these hours</p>
                </div>
            </div>
        </div>

        @if(auth()->user()->isAdminOrStaff())
        <!-- Chat Preferences (Staff/Admin Only) -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    Chat Preferences
                </h2>
                <p class="text-sm text-gray-600">Configure your chat system settings</p>
            </div>

            <div class="p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Chat Status -->
                <div>
                    <label for="chat_status" class="block text-sm font-medium text-gray-700 mb-2">Chat Status</label>
                    <select name="chat_status" id="chat_status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="available" {{ $preferences->chat_status === 'available' ? 'selected' : '' }}>Available</option>
                        <option value="busy" {{ $preferences->chat_status === 'busy' ? 'selected' : '' }}>Busy</option>
                        <option value="away" {{ $preferences->chat_status === 'away' ? 'selected' : '' }}>Away</option>
                        <option value="offline" {{ $preferences->chat_status === 'offline' ? 'selected' : '' }}>Offline</option>
                    </select>
                </div>

                <!-- Max Concurrent Chats -->
                <div>
                    <label for="max_concurrent_chats" class="block text-sm font-medium text-gray-700 mb-2">Max Concurrent Chats</label>
                    <select name="max_concurrent_chats" id="max_concurrent_chats" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        @for($i = 1; $i <= 20; $i++)
                            <option value="{{ $i }}" {{ $preferences->max_concurrent_chats === $i ? 'selected' : '' }}>{{ $i }}</option>
                        @endfor
                    </select>
                </div>

                <!-- Chat Options -->
                <div class="md:col-span-2 lg:col-span-1">
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="checkbox" name="chat_sound_enabled" value="1" {{ $preferences->chat_sound_enabled ? 'checked' : '' }} class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">Chat sound notifications</span>
                        </label>

                        <label class="flex items-center">
                            <input type="checkbox" name="chat_desktop_notifications" value="1" {{ $preferences->chat_desktop_notifications ? 'checked' : '' }} class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">Desktop notifications</span>
                        </label>

                        <label class="flex items-center">
                            <input type="checkbox" name="auto_assign_chats" value="1" {{ $preferences->auto_assign_chats ? 'checked' : '' }} class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">Auto-assign new chats</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- Privacy Preferences -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                    Privacy Preferences
                </h2>
                <p class="text-sm text-gray-600">Control your privacy and data tracking settings</p>
            </div>

            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <label class="flex items-center">
                        <input type="checkbox" name="profile_visible" value="1" {{ $preferences->profile_visible ? 'checked' : '' }} class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">Profile visible to others</span>
                    </label>

                    <label class="flex items-center">
                        <input type="checkbox" name="activity_tracking" value="1" {{ $preferences->activity_tracking ? 'checked' : '' }} class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">Activity tracking</span>
                    </label>

                    <label class="flex items-center">
                        <input type="checkbox" name="analytics_tracking" value="1" {{ $preferences->analytics_tracking ? 'checked' : '' }} class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">Analytics tracking</span>
                    </label>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-between items-center">
            <div class="flex space-x-4">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium transition-colors duration-200">
                    Save Preferences
                </button>
                <button type="button" id="reset-btn" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-md font-medium transition-colors duration-200">
                    Reset to Defaults
                </button>
            </div>
            <button type="button" id="export-btn" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md font-medium transition-colors duration-200">
                Export Settings
            </button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('preferences-form');
    const messageContainer = document.getElementById('message-container');
    const successMessage = document.getElementById('success-message');
    const errorMessage = document.getElementById('error-message');
    const successText = document.getElementById('success-text');
    const errorText = document.getElementById('error-text');
    const resetBtn = document.getElementById('reset-btn');
    const exportBtn = document.getElementById('export-btn');

    function showMessage(type, text) {
        messageContainer.classList.remove('hidden');
        if (type === 'success') {
            successMessage.classList.remove('hidden');
            errorMessage.classList.add('hidden');
            successText.textContent = text;
        } else {
            errorMessage.classList.remove('hidden');
            successMessage.classList.add('hidden');
            errorText.textContent = text;
        }
        
        // Auto hide after 5 seconds
        setTimeout(() => {
            messageContainer.classList.add('hidden');
        }, 5000);
    }

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(form);
        const data = {};
        
        // Convert FormData to object, handling checkboxes
        for (let [key, value] of formData.entries()) {
            if (form.querySelector(`[name="${key}"][type="checkbox"]`)) {
                data[key] = true;
            } else {
                data[key] = value;
            }
        }
        
        // Add unchecked checkboxes as false
        const checkboxes = form.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            if (!checkbox.checked) {
                data[checkbox.name] = false;
            }
        });

        fetch('{{ route("admin.preferences.update") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('success', data.message);
            } else {
                showMessage('error', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('error', 'An error occurred while saving preferences');
        });
    });

    // Reset button
    resetBtn.addEventListener('click', function() {
        if (confirm('Are you sure you want to reset all preferences to defaults? This cannot be undone.')) {
            fetch('{{ route("admin.preferences.reset") }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('success', data.message);
                    // Reload page to show reset values
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showMessage('error', data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showMessage('error', 'An error occurred while resetting preferences');
            });
        }
    });

    // Export button
    exportBtn.addEventListener('click', function() {
        fetch('{{ route("admin.preferences.export") }}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `user-preferences-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                showMessage('success', 'Preferences exported successfully');
            } else {
                showMessage('error', 'Failed to export preferences');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('error', 'An error occurred while exporting preferences');
        });
    });
});
</script>
@endsection
