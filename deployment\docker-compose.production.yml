version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: deployment/Dockerfile
    container_name: chisolution_app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./deployment/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - chisolution_network
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - DB_CONNECTION=mysql
      - DB_HOST=db
      - DB_PORT=3306
      - DB_DATABASE=chisolution
      - DB_USERNAME=chisolution
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - QUEUE_CONNECTION=redis
      - CACHE_DRIVER=redis
      - SESSION_DRIVER=redis
      - BROADCAST_DRIVER=pusher
      - PUSHER_APP_ID=${PUSHER_APP_ID}
      - PUSHER_APP_KEY=${PUSHER_APP_KEY}
      - PUSHER_APP_SECRET=${PUSHER_APP_SECRET}
      - PUSHER_APP_CLUSTER=${PUSHER_APP_CLUSTER}
      - MAIL_MAILER=smtp
      - MAIL_HOST=${MAIL_HOST}
      - MAIL_PORT=${MAIL_PORT}
      - MAIL_USERNAME=${MAIL_USERNAME}
      - MAIL_PASSWORD=${MAIL_PASSWORD}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - db
      - redis

  nginx:
    image: nginx:alpine
    container_name: chisolution_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./:/var/www
      - ./deployment/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./deployment/nginx/sites/:/etc/nginx/sites-available
      - ./deployment/nginx/ssl/:/etc/nginx/ssl
    depends_on:
      - app
    networks:
      - chisolution_network

  db:
    image: mysql:8.0
    container_name: chisolution_db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: chisolution
      MYSQL_USER: chisolution
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
    volumes:
      - db_data:/var/lib/mysql
      - ./deployment/mysql/my.cnf:/etc/mysql/my.cnf
    ports:
      - "3306:3306"
    networks:
      - chisolution_network

  redis:
    image: redis:7-alpine
    container_name: chisolution_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - chisolution_network

  queue:
    build:
      context: .
      dockerfile: deployment/Dockerfile
    container_name: chisolution_queue
    restart: unless-stopped
    command: php artisan queue:work --sleep=3 --tries=3 --max-time=3600
    working_dir: /var/www
    volumes:
      - ./:/var/www
    networks:
      - chisolution_network
    environment:
      - APP_ENV=production
      - DB_CONNECTION=mysql
      - DB_HOST=db
      - DB_PORT=3306
      - DB_DATABASE=chisolution
      - DB_USERNAME=chisolution
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - QUEUE_CONNECTION=redis
    depends_on:
      - db
      - redis

  scheduler:
    build:
      context: .
      dockerfile: deployment/Dockerfile
    container_name: chisolution_scheduler
    restart: unless-stopped
    command: /bin/sh -c "while true; do php artisan schedule:run; sleep 60; done"
    working_dir: /var/www
    volumes:
      - ./:/var/www
    networks:
      - chisolution_network
    environment:
      - APP_ENV=production
      - DB_CONNECTION=mysql
      - DB_HOST=db
      - DB_PORT=3306
      - DB_DATABASE=chisolution
      - DB_USERNAME=chisolution
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    depends_on:
      - db
      - redis

  websocket:
    build:
      context: .
      dockerfile: deployment/Dockerfile
    container_name: chisolution_websocket
    restart: unless-stopped
    command: php artisan websockets:serve --host=0.0.0.0 --port=6001
    working_dir: /var/www
    volumes:
      - ./:/var/www
    ports:
      - "6001:6001"
    networks:
      - chisolution_network
    environment:
      - APP_ENV=production
      - DB_CONNECTION=mysql
      - DB_HOST=db
      - DB_PORT=3306
      - DB_DATABASE=chisolution
      - DB_USERNAME=chisolution
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    depends_on:
      - db
      - redis

  monitoring:
    image: prom/prometheus
    container_name: chisolution_prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./deployment/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - chisolution_network

  grafana:
    image: grafana/grafana
    container_name: chisolution_grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./deployment/monitoring/grafana/:/etc/grafana/provisioning/
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    networks:
      - chisolution_network

volumes:
  db_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  chisolution_network:
    driver: bridge
