<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\CustomerSatisfactionService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Carbon\Carbon;

class CustomerSatisfactionController extends Controller
{
    protected CustomerSatisfactionService $satisfactionService;

    public function __construct(CustomerSatisfactionService $satisfactionService)
    {
        $this->satisfactionService = $satisfactionService;
        $this->middleware('auth');
    }

    /**
     * Display the customer satisfaction dashboard.
     */
    public function index(): View
    {
        $defaultFilters = [
            'start_date' => now()->subDays(30),
            'end_date' => now(),
        ];

        $metrics = $this->satisfactionService->getSatisfactionMetrics($defaultFilters);

        return view('admin.chat.satisfaction.index', compact('metrics', 'defaultFilters'));
    }

    /**
     * Get satisfaction metrics with filters.
     */
    public function getMetrics(Request $request): JsonResponse
    {
        $filters = $this->buildFilters($request);
        
        try {
            $metrics = $this->satisfactionService->getSatisfactionMetrics($filters);
            
            return response()->json([
                'success' => true,
                'data' => $metrics,
                'filters' => $filters,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get satisfaction metrics',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get overview metrics.
     */
    public function getOverview(Request $request): JsonResponse
    {
        $filters = $this->buildFilters($request);
        
        try {
            $metrics = $this->satisfactionService->getSatisfactionMetrics($filters);
            
            return response()->json([
                'success' => true,
                'data' => $metrics['overview'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get overview metrics',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get ratings breakdown.
     */
    public function getRatingsBreakdown(Request $request): JsonResponse
    {
        $filters = $this->buildFilters($request);
        
        try {
            $metrics = $this->satisfactionService->getSatisfactionMetrics($filters);
            
            return response()->json([
                'success' => true,
                'data' => $metrics['ratings_breakdown'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get ratings breakdown',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get satisfaction trends.
     */
    public function getTrends(Request $request): JsonResponse
    {
        $filters = $this->buildFilters($request);
        
        try {
            $metrics = $this->satisfactionService->getSatisfactionMetrics($filters);
            
            return response()->json([
                'success' => true,
                'data' => $metrics['satisfaction_trends'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get satisfaction trends',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get feedback analysis.
     */
    public function getFeedbackAnalysis(Request $request): JsonResponse
    {
        $filters = $this->buildFilters($request);
        
        try {
            $metrics = $this->satisfactionService->getSatisfactionMetrics($filters);
            
            return response()->json([
                'success' => true,
                'data' => $metrics['feedback_analysis'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get feedback analysis',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get staff performance.
     */
    public function getStaffPerformance(Request $request): JsonResponse
    {
        $filters = $this->buildFilters($request);
        
        try {
            $metrics = $this->satisfactionService->getSatisfactionMetrics($filters);
            
            return response()->json([
                'success' => true,
                'data' => $metrics['staff_performance'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get staff performance',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get NPS metrics.
     */
    public function getNPSMetrics(Request $request): JsonResponse
    {
        $filters = $this->buildFilters($request);
        
        try {
            $metrics = $this->satisfactionService->getSatisfactionMetrics($filters);
            
            return response()->json([
                'success' => true,
                'data' => $metrics['nps_metrics'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get NPS metrics',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get survey questions.
     */
    public function getSurveyQuestions(): JsonResponse
    {
        try {
            $questions = $this->satisfactionService->getSurveyQuestions();
            
            return response()->json([
                'success' => true,
                'data' => $questions,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get survey questions',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Submit customer rating.
     */
    public function submitRating(Request $request): JsonResponse
    {
        $request->validate([
            'chat_room_id' => 'required|integer|exists:chat_rooms,id',
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000',
        ]);

        try {
            $rating = $this->satisfactionService->submitRating(
                $request->chat_room_id,
                $request->rating,
                $request->comment,
                auth()->id()
            );
            
            return response()->json([
                'success' => true,
                'data' => $rating,
                'message' => 'Thank you for your feedback!',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to submit rating',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export satisfaction report.
     */
    public function exportReport(Request $request): JsonResponse
    {
        $filters = $this->buildFilters($request);
        $format = $request->get('format', 'json');
        
        try {
            $metrics = $this->satisfactionService->getSatisfactionMetrics($filters);
            
            $exportData = [
                'report_generated_at' => now()->toISOString(),
                'filters_applied' => $filters,
                'format' => $format,
                'data' => $metrics,
            ];
            
            return response()->json([
                'success' => true,
                'data' => $exportData,
                'download_url' => $this->generateDownloadUrl($exportData, $format),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to export report',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Build filters from request.
     */
    protected function buildFilters(Request $request): array
    {
        $filters = [];

        if ($request->has('start_date')) {
            $filters['start_date'] = Carbon::parse($request->get('start_date'));
        }

        if ($request->has('end_date')) {
            $filters['end_date'] = Carbon::parse($request->get('end_date'));
        }

        if ($request->has('staff_id') && $request->get('staff_id') !== 'all') {
            $filters['staff_id'] = (int) $request->get('staff_id');
        }

        if ($request->has('department') && $request->get('department') !== 'all') {
            $filters['department'] = $request->get('department');
        }

        return $filters;
    }

    /**
     * Generate download URL for exported report.
     */
    protected function generateDownloadUrl(array $exportData, string $format): ?string
    {
        // In a real implementation, this would save the file and return a download URL
        // For now, return null to indicate inline download
        return null;
    }
}
