/**
 * AI Services 3D Animations and Interactive Elements
 * Uses Three.js for 3D graphics and GSAP for smooth animations
 */

import * as THREE from 'three';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

class AIServices3D {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.particles = null;
        this.serviceIcons = [];
        this.isInitialized = false;
        this.animationId = null;
        
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }

    init() {
        // Only initialize on AI services page
        if (!document.querySelector('#ai-services-hero')) {
            return;
        }

        this.setupScene();
        this.createParticleSystem();
        this.createServiceIcons();
        this.setupEventListeners();
        this.animate();
        this.initGSAPAnimations();
        
        this.isInitialized = true;
    }

    setupScene() {
        // Create scene
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x0a0a0a);

        // Create camera
        this.camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            1000
        );
        this.camera.position.z = 50;

        // Create renderer
        const canvas = document.getElementById('ai-services-canvas');
        if (!canvas) return;

        this.renderer = new THREE.WebGLRenderer({
            canvas: canvas,
            alpha: true,
            antialias: true
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    }

    createParticleSystem() {
        const particleCount = 2000;
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);

        // AI-themed colors: blue, purple, cyan
        const colorPalette = [
            new THREE.Color(0x3b82f6), // Blue
            new THREE.Color(0x8b5cf6), // Purple
            new THREE.Color(0x06b6d4), // Cyan
            new THREE.Color(0x10b981), // Emerald
        ];

        for (let i = 0; i < particleCount; i++) {
            // Position particles in a sphere
            const radius = Math.random() * 100 + 50;
            const theta = Math.random() * Math.PI * 2;
            const phi = Math.random() * Math.PI;

            positions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
            positions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
            positions[i * 3 + 2] = radius * Math.cos(phi);

            // Random color from palette
            const color = colorPalette[Math.floor(Math.random() * colorPalette.length)];
            colors[i * 3] = color.r;
            colors[i * 3 + 1] = color.g;
            colors[i * 3 + 2] = color.b;
        }

        const geometry = new THREE.BufferGeometry();
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

        const material = new THREE.PointsMaterial({
            size: 0.8,
            vertexColors: true,
            transparent: true,
            opacity: 0.8,
            blending: THREE.AdditiveBlending
        });

        this.particles = new THREE.Points(geometry, material);
        this.scene.add(this.particles);
    }

    createServiceIcons() {
        // Create 3D geometric shapes representing AI services
        const services = [
            { name: 'agentic-coding', position: [-30, 20, 0], color: 0x3b82f6 },
            { name: 'automation', position: [30, 20, 0], color: 0x8b5cf6 },
            { name: 'chatbots', position: [-30, -20, 0], color: 0x06b6d4 },
            { name: 'voice-control', position: [30, -20, 0], color: 0x10b981 },
            { name: 'generative-ai', position: [0, 0, 0], color: 0xf59e0b }
        ];

        services.forEach((service, index) => {
            const geometry = this.getServiceGeometry(service.name);
            const material = new THREE.MeshBasicMaterial({
                color: service.color,
                transparent: true,
                opacity: 0.7,
                wireframe: true
            });

            const mesh = new THREE.Mesh(geometry, material);
            mesh.position.set(...service.position);
            mesh.userData = { service: service.name, originalPosition: service.position };
            
            this.serviceIcons.push(mesh);
            this.scene.add(mesh);
        });
    }

    getServiceGeometry(serviceName) {
        switch (serviceName) {
            case 'agentic-coding':
                return new THREE.BoxGeometry(8, 8, 8);
            case 'automation':
                return new THREE.CylinderGeometry(4, 4, 8, 8);
            case 'chatbots':
                return new THREE.SphereGeometry(5, 16, 16);
            case 'voice-control':
                return new THREE.ConeGeometry(4, 8, 8);
            case 'generative-ai':
                return new THREE.OctahedronGeometry(6);
            default:
                return new THREE.BoxGeometry(5, 5, 5);
        }
    }

    animate() {
        if (!this.isInitialized) return;

        this.animationId = requestAnimationFrame(() => this.animate());

        // Rotate particle system slowly
        if (this.particles) {
            this.particles.rotation.y += 0.001;
            this.particles.rotation.x += 0.0005;
        }

        // Animate service icons
        this.serviceIcons.forEach((icon, index) => {
            icon.rotation.x += 0.01;
            icon.rotation.y += 0.01;
            
            // Floating animation
            const time = Date.now() * 0.001;
            icon.position.y = icon.userData.originalPosition[1] + Math.sin(time + index) * 2;
        });

        if (this.renderer && this.scene && this.camera) {
            this.renderer.render(this.scene, this.camera);
        }
    }

    initGSAPAnimations() {
        // Hero section animations - check if elements exist
        if (document.querySelector('.ai-hero-title')) {
            gsap.from('.ai-hero-title', {
                opacity: 0,
                y: 50,
                duration: 1.5,
                ease: 'power3.out',
                delay: 0.5
            });
        }

        if (document.querySelector('.ai-hero-subtitle')) {
            gsap.from('.ai-hero-subtitle', {
                opacity: 0,
                y: 30,
                duration: 1.2,
                ease: 'power3.out',
                delay: 0.8
            });
        }

        if (document.querySelector('.ai-hero-description')) {
            gsap.from('.ai-hero-description', {
                opacity: 0,
                y: 20,
                duration: 1,
                ease: 'power3.out',
                delay: 1.1
            });
        }

        if (document.querySelector('.ai-hero-buttons')) {
            gsap.from('.ai-hero-buttons', {
                opacity: 0,
                y: 20,
                duration: 1,
                ease: 'power3.out',
                delay: 1.4
            });
        }

        // Service cards animation
        if (document.querySelector('.ai-service-card') && document.querySelector('.ai-services-grid')) {
            gsap.from('.ai-service-card', {
                opacity: 0,
                y: 50,
                duration: 0.8,
                ease: 'power3.out',
                stagger: 0.2,
                delay: 0.5,
                scrollTrigger: {
                    trigger: '.ai-services-grid',
                    start: 'top 80%'
                }
            });
        }

        // 3D icons animation on scroll
        if (document.querySelector('.ai-3d-icon') && document.querySelector('.ai-technologies-section')) {
            gsap.from('.ai-3d-icon', {
                scale: 0,
                rotation: 180,
                duration: 1,
                ease: 'back.out(1.7)',
                stagger: 0.1,
                scrollTrigger: {
                    trigger: '.ai-technologies-section',
                    start: 'top 70%'
                }
            });
        }
    }

    setupEventListeners() {
        // Handle window resize
        window.addEventListener('resize', () => this.onWindowResize());
        
        // Handle mouse movement for interactive effects
        window.addEventListener('mousemove', (event) => this.onMouseMove(event));
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', () => this.cleanup());
    }

    onWindowResize() {
        if (!this.camera || !this.renderer) return;

        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }

    onMouseMove(event) {
        if (!this.camera) return;

        const mouseX = (event.clientX / window.innerWidth) * 2 - 1;
        const mouseY = -(event.clientY / window.innerHeight) * 2 + 1;

        // Subtle camera movement based on mouse position
        gsap.to(this.camera.position, {
            x: mouseX * 5,
            y: mouseY * 5,
            duration: 2,
            ease: 'power2.out'
        });
    }

    cleanup() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        
        if (this.renderer) {
            this.renderer.dispose();
        }
        
        // Clean up geometries and materials
        this.serviceIcons.forEach(icon => {
            icon.geometry.dispose();
            icon.material.dispose();
        });
        
        if (this.particles) {
            this.particles.geometry.dispose();
            this.particles.material.dispose();
        }
    }
}

// Initialize the 3D system
new AIServices3D();
