@extends('layouts.app')

@section('title', 'Popular Articles - Blog')
@section('meta_description', 'Discover our most popular articles based on reader engagement. Find the content that resonates most with our community.')
@section('meta_keywords', 'popular articles, trending, most read, blog, web development, mobile apps, digital marketing')

@push('structured_data')
@verbatim
<script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "CollectionPage",
        "name": @json('Popular Articles - Blog'),
        "description": @json('Discover our most popular articles based on reader engagement. Find the content that resonates most with our community.'),
        "url": @json(url()->current()),
        "mainEntity": {
            "@type": "ItemList",
            "numberOfItems": @json($posts->total()),
            "itemListElement": [
                @foreach($posts as $index => $post)
                {
                    "@type": "ListItem",
                    "position": {{ $index + 1 }},
                    "item": {
                        "@type": "BlogPosting",
                        "name": @json($post->title),
                        "description": @json($post->meta_description ?: Str::limit(strip_tags($post->content), 160)),
                        "url": @json(route('blog.show', ['post' => $post->slug])),
                        "datePublished": @json($post->published_at->toISOString()),
                        "dateModified": @json($post->updated_at->toISOString())
                    }
                }
                @if(!$loop->last),@endif
                @endforeach
            ]
        }
    }
</script>
@endverbatim
@endpush

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-blue text-white overflow-hidden">
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
    </div>
    
    <div class="container mx-auto px-4 py-20 lg:py-32 relative z-10">
        <div class="text-center max-w-4xl mx-auto">
            <!-- Breadcrumb -->
            <nav class="mb-8">
                <ol class="flex items-center justify-center space-x-2 text-sm text-blue-200">
                    <li><a href="{{ route('home') }}" class="hover:text-white transition-colors">Home</a></li>
                    <li><span class="mx-2">/</span></li>
                    <li><a href="{{ route('blog.index') }}" class="hover:text-white transition-colors">Blog</a></li>
                    <li><span class="mx-2">/</span></li>
                    <li class="text-white">Popular</li>
                </ol>
            </nav>

            <h1 class="heading-1 text-white mb-6">
                Popular <span class="text-blue-300">Articles</span>
            </h1>
            <p class="text-lead text-blue-100 mb-8">
                Discover the articles that have captured our readers' attention. These are the most-read and engaging pieces from our blog.
            </p>
            <div class="text-blue-200">
                {{ $posts->total() }} popular {{ Str::plural('article', $posts->total()) }} ranked by views
            </div>
        </div>
    </div>
    
    <div class="absolute bottom-0 left-0 w-full">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block w-full h-16 fill-white">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
        </svg>
    </div>
</section>

<!-- Popular Posts Grid -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        @if($posts->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($posts as $index => $post)
                    <article class="blog-card card-hover bg-white rounded-lg shadow-lg overflow-hidden relative">
                        <!-- Popularity Rank -->
                        <div class="absolute top-4 left-4 z-10">
                            <div class="w-8 h-8 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center shadow-lg">
                                <span class="text-white font-bold text-sm">#{{ $posts->firstItem() + $index }}</span>
                            </div>
                        </div>
                        
                        <div class="relative overflow-hidden">
                            @if($post->featured_image)
                                <picture>
                                    @if($post->getWebPImageUrl($post->featured_image))
                                        <source srcset="{{ $post->getWebPImageUrl($post->featured_image) }}" type="image/webp">
                                    @endif
                                    <img src="{{ $post->getOptimizedImageUrl($post->featured_image, 'medium') }}" 
                                         alt="{{ $post->title }}" 
                                         class="w-full h-48 object-cover hover-lift">
                                </picture>
                            @elseif($post->first_gallery_image_url)
                                <picture>
                                    @if($post->getWebPImageUrl($post->gallery_images[0]))
                                        <source srcset="{{ $post->getWebPImageUrl($post->gallery_images[0]) }}" type="image/webp">
                                    @endif
                                    <img src="{{ $post->getOptimizedImageUrl($post->gallery_images[0], 'medium') }}" 
                                         alt="{{ $post->title }}" 
                                         class="w-full h-48 object-cover hover-lift">
                                </picture>
                            @else
                                <div class="w-full h-48 bg-gradient-to-br from-red-500 to-red-600 flex items-center justify-center">
                                    <svg class="w-12 h-12 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                    </svg>
                                </div>
                            @endif
                            
                            @if($post->total_image_count > 1)
                                <div class="absolute top-2 right-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded-full flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                                    </svg>
                                    {{ $post->total_image_count }}
                                </div>
                            @endif
                        </div>
                        
                        <div class="p-6 space-y-3">
                            <div class="flex items-center space-x-2">
                                @if($post->category)
                                    <span class="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded">{{ $post->category->name }}</span>
                                @endif
                                @if($post->is_featured)
                                    <span class="px-2 py-1 bg-yellow-100 text-yellow-600 text-xs rounded">Featured</span>
                                @endif
                                <span class="text-gray-500 text-xs">{{ $post->formatted_published_date }}</span>
                            </div>
                            
                            <h3 class="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors">
                                <a href="{{ route('blog.show', $post->slug) }}">{{ $post->title }}</a>
                            </h3>
                            
                            <p class="text-gray-600 text-sm leading-relaxed">
                                {{ $post->excerpt }}
                            </p>
                            
                            @if($post->services()->count() > 0)
                                <div class="flex flex-wrap gap-1">
                                    @foreach($post->services()->take(2) as $service)
                                        <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">{{ $service->name }}</span>
                                    @endforeach
                                    @if($post->services()->count() > 2)
                                        <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">+{{ $post->services()->count() - 2 }} more</span>
                                    @endif
                                </div>
                            @endif
                            
                            <!-- Popularity Metrics -->
                            <div class="bg-red-50 rounded-lg p-3">
                                <div class="flex items-center justify-between text-sm">
                                    <div class="flex items-center space-x-4 text-red-600">
                                        <div class="flex items-center space-x-1">
                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                                            </svg>
                                            <span class="font-semibold">{{ number_format($post->view_count) }}</span>
                                        </div>
                                        <div class="flex items-center space-x-1">
                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                            </svg>
                                            <span>{{ $post->reading_time }} min</span>
                                        </div>
                                    </div>
                                    <div class="text-red-500 font-semibold">
                                        🔥 Trending
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between pt-3">
                                <div class="flex items-center space-x-2">
                                    <div class="w-8 h-8 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center">
                                        <span class="text-white font-semibold text-xs">{{ substr($post->author->first_name, 0, 1) }}{{ substr($post->author->last_name, 0, 1) }}</span>
                                    </div>
                                    <span class="text-sm text-gray-600">{{ $post->author->first_name }} {{ $post->author->last_name }}</span>
                                </div>
                            </div>
                        </div>
                    </article>
                @endforeach
            </div>
            
            <!-- Pagination -->
            @if($posts->hasPages())
                <div class="flex justify-center mt-12">
                    {{ $posts->links('pagination::tailwind') }}
                </div>
            @endif
        @else
            <div class="text-center py-12">
                <div class="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-12 h-12 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">No Popular Articles Yet</h3>
                <p class="text-gray-600 mb-6">Articles need some time to gain popularity. Check back soon to see trending content!</p>
                <a href="{{ route('blog.index') }}" class="btn-primary">
                    Browse All Articles
                </a>
            </div>
        @endif
    </div>
</section>

<!-- Trending Topics -->
@if($posts->count() > 0)
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="heading-2 mb-6">
                    What Makes Content <span class="text-blue-600">Popular</span>
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Reader Engagement</h3>
                        <p class="text-gray-600 text-sm">High view counts and time spent reading indicate valuable content.</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Social Sharing</h3>
                        <p class="text-gray-600 text-sm">Articles that get shared frequently across social platforms.</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Practical Value</h3>
                        <p class="text-gray-600 text-sm">Content that provides actionable insights and solves real problems.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endif

@push('styles')
<style>
.blog-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.blog-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
</style>
@endpush
@endsection
