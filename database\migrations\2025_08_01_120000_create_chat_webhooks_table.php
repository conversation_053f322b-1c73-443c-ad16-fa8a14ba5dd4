<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_webhooks', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->string('name');
            $table->string('url');
            $table->json('events'); // Array of event types to listen for
            $table->string('secret')->nullable(); // For HMAC signature verification
            $table->json('headers')->nullable(); // Custom headers to send
            $table->boolean('is_active')->default(true);
            $table->integer('max_retries')->default(3);
            $table->integer('timeout_seconds')->default(30);
            $table->json('ip_whitelist')->nullable(); // IP addresses allowed to receive webhooks
            $table->string('content_type')->default('application/json');
            $table->text('description')->nullable();
            $table->timestamps();

            $table->index(['is_active']);
            $table->index(['events']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_webhooks');
    }
};
