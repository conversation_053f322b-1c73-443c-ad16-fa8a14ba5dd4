<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class EmailCampaignSend extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'email_campaign_id',
        'newsletter_subscription_id',
        'email',
        'status',
        'sent_at',
        'delivered_at',
        'opened_at',
        'first_clicked_at',
        'unsubscribed_at',
        'bounced_at',
        'open_count',
        'click_count',
        'clicked_links',
        'bounce_reason',
        'error_message',
        'tracking_token',
        'unsubscribe_token',
        'user_agent',
        'ip_address',
    ];

    protected $casts = [
        'sent_at' => 'datetime',
        'delivered_at' => 'datetime',
        'opened_at' => 'datetime',
        'first_clicked_at' => 'datetime',
        'unsubscribed_at' => 'datetime',
        'bounced_at' => 'datetime',
        'clicked_links' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($send) {
            if (empty($send->uuid)) {
                $send->uuid = Str::uuid();
            }
            if (empty($send->tracking_token)) {
                $send->tracking_token = Str::random(32);
            }
            if (empty($send->unsubscribe_token)) {
                $send->unsubscribe_token = Str::random(32);
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Scope a query by status.
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope sent emails.
     */
    public function scopeSent($query)
    {
        return $query->whereNotNull('sent_at');
    }

    /**
     * Scope delivered emails.
     */
    public function scopeDelivered($query)
    {
        return $query->whereNotNull('delivered_at');
    }

    /**
     * Scope opened emails.
     */
    public function scopeOpened($query)
    {
        return $query->whereNotNull('opened_at');
    }

    /**
     * Scope clicked emails.
     */
    public function scopeClicked($query)
    {
        return $query->whereNotNull('first_clicked_at');
    }

    /**
     * Get the email campaign.
     */
    public function emailCampaign(): BelongsTo
    {
        return $this->belongsTo(EmailCampaign::class);
    }

    /**
     * Get the newsletter subscription.
     */
    public function newsletterSubscription(): BelongsTo
    {
        return $this->belongsTo(NewsletterSubscription::class);
    }

    /**
     * Mark as sent.
     */
    public function markAsSent(): void
    {
        $this->update([
            'status' => 'sent',
            'sent_at' => now(),
        ]);
    }

    /**
     * Mark as delivered.
     */
    public function markAsDelivered(): void
    {
        $this->update([
            'status' => 'delivered',
            'delivered_at' => now(),
        ]);
    }

    /**
     * Mark as bounced.
     */
    public function markAsBounced(string $reason = null): void
    {
        $this->update([
            'status' => 'bounced',
            'bounced_at' => now(),
            'bounce_reason' => $reason,
        ]);
    }

    /**
     * Track email open.
     */
    public function trackOpen(string $userAgent = null, string $ipAddress = null): void
    {
        $updates = [
            'open_count' => $this->open_count + 1,
            'user_agent' => $userAgent,
            'ip_address' => $ipAddress,
        ];

        if (!$this->opened_at) {
            $updates['opened_at'] = now();
        }

        $this->update($updates);
    }

    /**
     * Track email click.
     */
    public function trackClick(string $url, string $userAgent = null, string $ipAddress = null): void
    {
        $clickedLinks = $this->clicked_links ?? [];
        $clickedLinks[] = [
            'url' => $url,
            'clicked_at' => now()->toISOString(),
        ];

        $updates = [
            'click_count' => $this->click_count + 1,
            'clicked_links' => $clickedLinks,
            'user_agent' => $userAgent,
            'ip_address' => $ipAddress,
        ];

        if (!$this->first_clicked_at) {
            $updates['first_clicked_at'] = now();
        }

        $this->update($updates);
    }

    /**
     * Track unsubscribe.
     */
    public function trackUnsubscribe(): void
    {
        $this->update(['unsubscribed_at' => now()]);
    }

    /**
     * Get tracking URL.
     */
    public function getTrackingUrl(): string
    {
        return route('email.track.open', ['token' => $this->tracking_token]);
    }

    /**
     * Get unsubscribe URL.
     */
    public function getUnsubscribeUrl(): string
    {
        return route('email.unsubscribe', ['token' => $this->unsubscribe_token]);
    }

    /**
     * Check if email was opened.
     */
    public function wasOpened(): bool
    {
        return !is_null($this->opened_at);
    }

    /**
     * Check if email was clicked.
     */
    public function wasClicked(): bool
    {
        return !is_null($this->first_clicked_at);
    }

    /**
     * Get status badge color.
     */
    public function getStatusBadgeColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'bg-gray-100 text-gray-800',
            'sent' => 'bg-blue-100 text-blue-800',
            'delivered' => 'bg-green-100 text-green-800',
            'bounced' => 'bg-red-100 text-red-800',
            'failed' => 'bg-red-100 text-red-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }




}
