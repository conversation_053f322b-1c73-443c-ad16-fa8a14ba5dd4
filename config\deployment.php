<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Deployment Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains deployment-specific configuration for different
    | environments. It helps optimize the application for production,
    | staging, and development environments.
    |
    */

    'environments' => [
        'production' => [
            'optimizations' => [
                'config_cache' => true,
                'route_cache' => true,
                'view_cache' => true,
                'event_cache' => true,
                'opcache' => true,
                'asset_versioning' => true,
                'gzip_compression' => true,
                'cdn_enabled' => true,
            ],
            'security' => [
                'force_https' => true,
                'hsts_enabled' => true,
                'csrf_protection' => true,
                'xss_protection' => true,
                'content_security_policy' => true,
            ],
            'monitoring' => [
                'error_tracking' => true,
                'performance_monitoring' => true,
                'uptime_monitoring' => true,
                'log_level' => 'error',
            ],
        ],
        
        'staging' => [
            'optimizations' => [
                'config_cache' => true,
                'route_cache' => true,
                'view_cache' => false,
                'event_cache' => false,
                'opcache' => false,
                'asset_versioning' => true,
                'gzip_compression' => true,
                'cdn_enabled' => false,
            ],
            'security' => [
                'force_https' => true,
                'hsts_enabled' => false,
                'csrf_protection' => true,
                'xss_protection' => true,
                'content_security_policy' => false,
            ],
            'monitoring' => [
                'error_tracking' => true,
                'performance_monitoring' => false,
                'uptime_monitoring' => false,
                'log_level' => 'debug',
            ],
        ],
        
        'local' => [
            'optimizations' => [
                'config_cache' => false,
                'route_cache' => false,
                'view_cache' => false,
                'event_cache' => false,
                'opcache' => false,
                'asset_versioning' => false,
                'gzip_compression' => false,
                'cdn_enabled' => false,
            ],
            'security' => [
                'force_https' => false,
                'hsts_enabled' => false,
                'csrf_protection' => true,
                'xss_protection' => true,
                'content_security_policy' => false,
            ],
            'monitoring' => [
                'error_tracking' => false,
                'performance_monitoring' => false,
                'uptime_monitoring' => false,
                'log_level' => 'debug',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Optimizations
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'database' => [
            'query_cache' => env('DB_QUERY_CACHE', true),
            'connection_pooling' => env('DB_CONNECTION_POOLING', false),
            'read_write_split' => env('DB_READ_WRITE_SPLIT', false),
        ],
        'cache' => [
            'default_ttl' => env('CACHE_DEFAULT_TTL', 3600),
            'tag_support' => env('CACHE_TAG_SUPPORT', true),
            'compression' => env('CACHE_COMPRESSION', true),
        ],
        'session' => [
            'gc_probability' => env('SESSION_GC_PROBABILITY', 1),
            'gc_divisor' => env('SESSION_GC_DIVISOR', 100),
            'gc_maxlifetime' => env('SESSION_GC_MAXLIFETIME', 1440),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    */
    'security' => [
        'headers' => [
            'x_frame_options' => 'DENY',
            'x_content_type_options' => 'nosniff',
            'x_xss_protection' => '1; mode=block',
            'referrer_policy' => 'strict-origin-when-cross-origin',
            'permissions_policy' => 'geolocation=(), microphone=(), camera=()',
        ],
        'rate_limiting' => [
            'api_requests_per_minute' => 60,
            'login_attempts_per_minute' => 5,
            'password_reset_per_hour' => 3,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring & Analytics
    |--------------------------------------------------------------------------
    */
    'monitoring' => [
        'services' => [
            'sentry' => [
                'enabled' => env('SENTRY_ENABLED', false),
                'dsn' => env('SENTRY_LARAVEL_DSN'),
                'traces_sample_rate' => env('SENTRY_TRACES_SAMPLE_RATE', 0.1),
            ],
            'new_relic' => [
                'enabled' => env('NEW_RELIC_ENABLED', false),
                'app_name' => env('NEW_RELIC_APP_NAME'),
                'license_key' => env('NEW_RELIC_LICENSE_KEY'),
            ],
        ],
        'health_checks' => [
            'database' => true,
            'cache' => true,
            'queue' => true,
            'storage' => true,
            'mail' => false,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | CDN Configuration
    |--------------------------------------------------------------------------
    */
    'cdn' => [
        'enabled' => env('CDN_ENABLED', false),
        'url' => env('CDN_URL'),
        'assets' => [
            'css' => true,
            'js' => true,
            'images' => true,
            'fonts' => true,
        ],
        'cache_control' => [
            'max_age' => 31536000, // 1 year
            'public' => true,
        ],
    ],
];
