<?php

namespace App\Http\Controllers;

use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use App\Services\VisitorAnalytics;

class OrderController extends Controller
{
    protected VisitorAnalytics $visitorAnalytics;

    /**
     * Create a new controller instance.
     */
    public function __construct(VisitorAnalytics $visitorAnalytics)
    {
        $this->middleware('auth');
        $this->visitorAnalytics = $visitorAnalytics;
    }

    /**
     * Display a listing of the user's orders.
     */
    public function index(Request $request): View
    {
        $user = auth()->user();

        $query = Order::where('user_id', $user->id)
                     ->where('is_deleted', false)
                     ->with(['items.product', 'items.variant', 'currency']);

        // Filter by status if provided
        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('notes', 'like', "%{$search}%");
            });
        }

        $orders = $query->orderBy('created_at', 'desc')->paginate(10);

        // Get status counts for filter tabs
        $statusCounts = [
            'all' => Order::where('user_id', $user->id)->where('is_deleted', false)->count(),
            'pending' => Order::where('user_id', $user->id)->where('is_deleted', false)->where('status', 'pending')->count(),
            'confirmed' => Order::where('user_id', $user->id)->where('is_deleted', false)->where('status', 'confirmed')->count(),
            'processing' => Order::where('user_id', $user->id)->where('is_deleted', false)->where('status', 'processing')->count(),
            'shipped' => Order::where('user_id', $user->id)->where('is_deleted', false)->where('status', 'shipped')->count(),
            'delivered' => Order::where('user_id', $user->id)->where('is_deleted', false)->where('status', 'delivered')->count(),
            'cancelled' => Order::where('user_id', $user->id)->where('is_deleted', false)->where('status', 'cancelled')->count(),
        ];

        // Track orders page visit
        $this->visitorAnalytics->trackPageVisit(
            'Orders List',
            [
                'user_id' => $user->id,
                'total_orders' => $orders->total(),
                'filter_status' => $request->get('status', 'all'),
                'search_query' => $request->get('search'),
            ]
        );

        return view('orders.index', compact('orders', 'statusCounts'));
    }

    /**
     * Display the specified order.
     */
    public function show(Order $order): View
    {
        // Check if order is deleted
        if ($order->is_deleted) {
            abort(404);
        }

        // Ensure user can only view their own orders
        if ($order->user_id !== auth()->id()) {
            abort(403, 'Unauthorized access to order.');
        }

        // Load relationships
        $order->load(['items.product', 'items.variant', 'currency', 'coupon']);

        // Track order view
        $this->visitorAnalytics->trackPageVisit(
            "Order #{$order->order_number}",
            [
                'user_id' => auth()->id(),
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'order_status' => $order->status,
                'order_total' => $order->total_amount,
            ]
        );

        return view('orders.show', compact('order'));
    }

    /**
     * Soft delete the specified order.
     */
    public function destroy(Order $order): RedirectResponse
    {
        // Check if order is already deleted
        if ($order->is_deleted) {
            abort(404);
        }

        // Ensure user can only delete their own orders
        if ($order->user_id !== auth()->id()) {
            abort(403, 'Unauthorized access to order.');
        }

        // Only allow deletion of certain statuses
        if (!in_array($order->status, ['pending', 'cancelled'])) {
            return back()->withErrors(['error' => 'Orders that are confirmed, processing, shipped, or delivered cannot be deleted.']);
        }

        // Soft delete the order
        $order->update(['is_deleted' => true]);

        return redirect()->route('orders.index')->with('success', 'Order deleted successfully.');
    }
}
