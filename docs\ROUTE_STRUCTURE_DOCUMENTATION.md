# 🛣️ ChiSolution Route Structure Documentation

## Overview

This document provides a comprehensive overview of the ChiSolution application's routing structure, clearly differentiating between public and protected routes, and explaining the authentication and authorization mechanisms.

## 🏗️ Route Architecture

### Route Categories

1. **Public Routes** - No authentication required
2. **Protected Routes** - Authentication required
3. **Admin Routes** - Admin/Staff role required
4. **API Routes** - Various authentication levels
5. **Health Check Routes** - Monitoring endpoints

---

## 🌐 Public Routes (No Authentication Required)

### Root & Localization
```
GET /                           → Redirect to /en/
GET /{locale}/                  → Home page
```

### Static Pages
```
GET /{locale}/about             → About page
GET /{locale}/contact           → Contact page
POST /{locale}/contact          → Contact form submission
```

### Public Project Application System
```
GET /{locale}/apply             → Project application form (PUBLIC)
POST /{locale}/apply            → Submit project application (PUBLIC)
GET /{locale}/apply/success/{ref} → Application success page (PUBLIC)
POST /{locale}/apply/status     → Check application status (PUBLIC)
```

### Public Portfolio & Projects
```
GET /{locale}/projects          → Public project showcase
GET /{locale}/projects/{slug}   → Individual project details
```

### Public Blog System
```
GET /{locale}/blog              → Blog index
GET /{locale}/blog/{slug}       → Individual blog post
POST /{locale}/blog/{slug}/comment → Submit comment (requires auth)
```

### Public E-commerce
```
GET /{locale}/shop              → Shop index
GET /{locale}/shop/search       → Product search
GET /{locale}/shop/category/{category} → Category products
GET /{locale}/shop/product/{product}   → Product details
```

### Public Services
```
GET /{locale}/services          → Services overview
GET /{locale}/services/{slug}   → Individual service details
```

### Public Career System
```
GET /{locale}/careers           → Job listings
GET /{locale}/careers/{job}     → Job details
GET /{locale}/careers/{job}/apply → Job application form (PUBLIC)
POST /{locale}/careers/{job}/apply → Submit job application (PUBLIC)
```

### Authentication Routes (Guest Only)
```
GET /login                      → Login form
POST /login                     → Process login
GET /register                   → Registration form
POST /register                  → Process registration
GET /password/reset             → Password reset form
POST /password/email            → Send reset email
GET /password/reset/{token}     → Reset password form
POST /password/reset            → Process password reset
```

---

## 🔐 Protected Routes (Authentication Required)

### Customer Dashboard
```
GET /dashboard                  → Customer dashboard
GET /dashboard/search           → Dashboard search
```

### Order Management
```
GET /orders                     → User's orders
GET /orders/{order}             → Order details
DELETE /orders/{order}          → Cancel order
```

### Project Management (Authenticated Users)
```
GET /my-projects                → User's projects
GET /my-projects/{project}      → Project details
DELETE /my-projects/{project}   → Delete project
```

### Project Applications (Authenticated Users)
```
GET /project-applications       → User's applications
GET /project-applications/create → Application form (AUTHENTICATED)
POST /project-applications      → Submit application (AUTHENTICATED)
GET /project-applications/{app} → Application details
PUT /project-applications/{app} → Update application
DELETE /project-applications/{app} → Delete application
```

### Career Applications (Authenticated Users)
```
GET /my-job-applications        → User's job applications
```

### Shopping Cart & Checkout
```
GET /{locale}/cart              → Shopping cart
POST /{locale}/cart/add         → Add to cart
PUT /{locale}/cart/update       → Update cart
DELETE /{locale}/cart/remove    → Remove from cart
POST /{locale}/cart/clear       → Clear cart
POST /{locale}/cart/coupon      → Apply coupon
DELETE /{locale}/cart/coupon    → Remove coupon
GET /{locale}/checkout          → Checkout page
POST /{locale}/checkout         → Process checkout
```

---

## 👑 Admin Routes (Admin/Staff Role Required)

### Admin Dashboard
```
GET /admin                      → Admin dashboard
GET /admin/dashboard            → Admin dashboard (alternative)
GET /admin/dashboard/visitor-chart-data → Visitor analytics
GET /admin/dashboard/top-pages  → Top pages analytics
```

### User Management
```
GET /admin/users                → User list
GET /admin/users/create         → Create user form
POST /admin/users               → Store user
GET /admin/users/{user}         → User details
PUT /admin/users/{user}         → Update user
DELETE /admin/users/{user}      → Delete user
```

### Project Management (Admin)
```
GET /admin/projects             → All projects
GET /admin/projects/create      → Create project form
POST /admin/projects            → Store project
GET /admin/projects/{project}   → Project details
PUT /admin/projects/{project}   → Update project
DELETE /admin/projects/{project} → Delete project
```

### Project Application Management (Admin)
```
GET /admin/project-applications → All applications
GET /admin/project-applications/{app} → Application details
PUT /admin/project-applications/{app} → Update application
PUT /admin/project-applications/{app}/status → Update status
```

### Content Management
```
GET /admin/blog                 → Blog management
GET /admin/services             → Service management
GET /admin/categories           → Category management
GET /admin/products             → Product management
```

### System Management (Admin Only)
```
GET /admin/permissions          → Permission management
GET /admin/activity-logs        → Activity logs
GET /admin/ai-providers         → AI provider management
```

---

## 🔌 API Routes

### Public API
```
POST /api/v1/register           → User registration
POST /api/v1/login              → User login
```

### Protected API (Sanctum Token Required)
```
POST /api/v1/logout             → User logout
GET /api/v1/user                → Get user data
```

### Chat API (Mixed Authentication)
```
POST /api/v1/chat/rooms         → Create chat room (PUBLIC)
GET /api/v1/chat/rooms/{uuid}   → Get chat room (PUBLIC)
POST /api/v1/chat/rooms/{uuid}/messages → Send message (PUBLIC)
GET /api/v1/chat/rooms/{uuid}/messages  → Get messages (PUBLIC)
```

### Cart API
```
POST /api/cart/sync             → Sync cart data
GET /api/cart/count             → Get cart count
```

---

## 🏥 Health Check Routes (Monitoring)

```
GET /health                     → Basic health check
GET /health/detailed            → Detailed health check
GET /health/ready               → Readiness probe
GET /health/live                → Liveness probe
GET /health/metrics             → Application metrics
GET /health/prometheus          → Prometheus metrics
```

---

## 🛡️ Middleware Configuration

### Global Web Middleware (Applied to All Web Routes)
1. `StaticAssetBypassMiddleware` (prepend)
2. `SafeOutputMiddleware` (append)
3. `LocalizationMiddleware` (append)
4. `VisitorAnalyticsMiddleware` (append)

### Route-Specific Middleware
- `auth` - Requires authentication
- `guest` - Redirects authenticated users
- `role:admin,staff` - Requires admin or staff role
- `permission:resource,action` - Requires specific permission
- `activity.log` - Logs user activities
- `chat.auth` - Chat-specific authentication
- `chat.usage` - Chat usage tracking

---

## 🎯 Key Distinctions

### Public vs Protected Project Applications

**Public Route (No Auth Required):**
- Route: `/{locale}/apply`
- Controller: `ProjectApplicationController@create`
- Layout: `layouts.app`
- Purpose: Allow visitors to submit project applications

**Protected Route (Auth Required):**
- Route: `/project-applications/create`
- Controller: `ProjectApplicationController@create`
- Layout: `layouts.dashboard`
- Purpose: Allow authenticated users to manage applications

### Layout Usage
- **Public routes**: Use `layouts.app` (includes public header/footer)
- **Protected routes**: Use `layouts.dashboard` (includes sidebar, auth checks)
- **Admin routes**: Use `layouts.dashboard` with admin-specific navigation
- **Auth routes**: Use `layouts.auth` (minimal layout for login/register)

---

## 🔄 Route Redirects

### Localization Redirects
```
/apply → /{locale}/apply
/blog → /{locale}/blog
/shop → /{locale}/shop
/cart → /{locale}/cart
```

### Authentication Redirects
- Unauthenticated users → `/login`
- Authenticated users accessing guest routes → appropriate dashboard
- Admin users → `/admin/dashboard`
- Regular users → `/dashboard`

---

## 📝 Notes

1. **Locale Parameter**: Most public routes include `{locale}` parameter for internationalization
2. **Middleware Stacking**: Routes can have multiple middleware applied
3. **Resource Routes**: Many admin routes use Laravel's resource routing
4. **API Versioning**: API routes are versioned (v1) for future compatibility
5. **Security**: All routes implement appropriate security measures based on their access level

---

## 🎯 Quick Reference Summary

### Route Patterns by Authentication Level

| Authentication Level | Route Pattern | Layout | Example |
|---------------------|---------------|--------|---------|
| **Public** | `/{locale}/...` | `layouts.app` | `/{locale}/apply` |
| **Protected** | `/...` (no locale) | `layouts.dashboard` | `/project-applications` |
| **Admin** | `/admin/...` | `layouts.dashboard` | `/admin/projects` |
| **API** | `/api/v1/...` | N/A (JSON) | `/api/v1/chat` |
| **Auth** | `/login`, `/register` | `layouts.auth` | `/login` |

### Key Route Conflicts Resolved

1. **Project Applications**:
   - Public: `/{locale}/apply` (no auth, uses app layout)
   - Protected: `/project-applications` (auth required, uses dashboard layout)

2. **Project Viewing**:
   - Public: `/{locale}/projects` (portfolio showcase)
   - Protected: `/my-projects` (user's own projects)
   - Admin: `/admin/projects` (all projects management)

3. **Career Applications**:
   - Public: `/{locale}/careers/{job}/apply` (no auth)
   - Protected: `/my-job-applications` (view own applications)

### Middleware Application Strategy

- **Public routes**: Minimal middleware (localization, analytics)
- **Protected routes**: Authentication + activity logging
- **Admin routes**: Authentication + role checking + activity logging
- **API routes**: Token authentication + usage tracking
