<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class EmailTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'name',
        'slug',
        'description',
        'subject',
        'html_content',
        'text_content',
        'variables',
        'category',
        'type',
        'is_active',
        'is_default',
        'design_settings',
        'preview_image',
        'created_by',
        'updated_by',
        'last_used_at',
        'usage_count',
    ];

    protected $casts = [
        'variables' => 'array',
        'design_settings' => 'array',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'last_used_at' => 'datetime',
        'usage_count' => 'integer',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($template) {
            if (empty($template->uuid)) {
                $template->uuid = Str::uuid();
            }
            if (empty($template->slug)) {
                $template->slug = Str::slug($template->name);
            }
        });

        static::updating(function ($template) {
            if ($template->isDirty('name') && empty($template->slug)) {
                $template->slug = Str::slug($template->name);
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Scope a query to only include active templates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query by category.
     */
    public function scopeCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope a query by type.
     */
    public function scopeType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get the user who created this template.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this template.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the campaigns using this template.
     */
    public function campaigns(): HasMany
    {
        return $this->hasMany(EmailCampaign::class);
    }

    /**
     * Render the template with variables.
     */
    public function render(array $variables = []): array
    {
        $htmlContent = $this->html_content;
        $textContent = $this->text_content;
        $subject = $this->subject;

        // Replace variables in content
        foreach ($variables as $key => $value) {
            $placeholder = '{{' . $key . '}}';
            $htmlContent = str_replace($placeholder, $value, $htmlContent);
            $textContent = str_replace($placeholder, $value, $textContent);
            $subject = str_replace($placeholder, $value, $subject);
        }

        return [
            'subject' => $subject,
            'html_content' => $htmlContent,
            'text_content' => $textContent,
        ];
    }

    /**
     * Get available template variables.
     */
    public function getAvailableVariables(): array
    {
        return $this->variables ?? [];
    }

    /**
     * Mark template as used.
     */
    public function markAsUsed(): void
    {
        $this->increment('usage_count');
        $this->update(['last_used_at' => now()]);
    }

    /**
     * Get template categories.
     */
    public static function getCategories(): array
    {
        return [
            'general' => 'General',
            'newsletter' => 'Newsletter',
            'promotional' => 'Promotional',
            'transactional' => 'Transactional',
            'welcome' => 'Welcome Series',
            'abandoned_cart' => 'Abandoned Cart',
            'follow_up' => 'Follow Up',
        ];
    }

    /**
     * Get template types.
     */
    public static function getTypes(): array
    {
        return [
            'custom' => 'Custom',
            'system' => 'System',
            'automated' => 'Automated',
        ];
    }

    /**
     * Get formatted category name.
     */
    public function getCategoryNameAttribute(): string
    {
        $categories = self::getCategories();
        return $categories[$this->category] ?? ucfirst($this->category);
    }

    /**
     * Get formatted type name.
     */
    public function getTypeNameAttribute(): string
    {
        $types = self::getTypes();
        return $types[$this->type] ?? ucfirst($this->type);
    }

    /**
     * Get preview URL.
     */
    public function getPreviewUrlAttribute(): ?string
    {
        return $this->preview_image ? asset('storage/' . $this->preview_image) : null;
    }

    /**
     * Check if template can be deleted.
     */
    public function canBeDeleted(): bool
    {
        // System templates cannot be deleted
        if ($this->type === 'system') {
            return false;
        }

        // Templates with active campaigns cannot be deleted
        if ($this->campaigns()->whereIn('status', ['scheduled', 'sending'])->exists()) {
            return false;
        }

        return true;
    }

    /**
     * Duplicate template.
     */
    public function duplicate(string $newName = null): self
    {
        $newTemplate = $this->replicate();
        $newTemplate->uuid = Str::uuid();
        $newTemplate->name = $newName ?? $this->name . ' (Copy)';
        $newTemplate->slug = Str::slug($newTemplate->name);
        $newTemplate->is_default = false;
        $newTemplate->usage_count = 0;
        $newTemplate->last_used_at = null;
        $newTemplate->save();

        return $newTemplate;
    }
}
