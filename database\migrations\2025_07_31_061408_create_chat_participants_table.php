<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_participants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('chat_room_id')->constrained('chat_rooms')->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('cascade')
                  ->comment('NULL for anonymous visitors');
            $table->enum('participant_type', ['customer', 'staff', 'admin', 'visitor', 'bot']);
            $table->enum('role', ['participant', 'moderator', 'observer', 'owner'])->default('participant');
            $table->string('display_name', 100)->nullable()->comment('Display name for anonymous users');
            $table->json('permissions')->nullable()->comment('Participant-specific permissions');
            $table->timestamp('joined_at')->useCurrent();
            $table->timestamp('left_at')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_seen_at')->nullable();
            $table->timestamps();
            
            // Unique constraint to prevent duplicate participants
            $table->unique(['chat_room_id', 'user_id'], 'unique_room_user');
            
            // Performance indexes
            $table->index(['chat_room_id', 'is_active'], 'idx_room_active');
            $table->index(['user_id', 'is_active'], 'idx_user_active');
            $table->index('participant_type', 'idx_participant_type');
            $table->index('last_seen_at', 'idx_last_seen');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_participants');
    }
};
