<?php

namespace App\Observers;

use App\Models\Product;
use App\Services\ShopCacheService;

class ProductObserver
{
    protected $shopCache;

    public function __construct(ShopCacheService $shopCache)
    {
        $this->shopCache = $shopCache;
    }

    /**
     * Handle the Product "created" event.
     */
    public function created(Product $product): void
    {
        $this->invalidateProductCaches();
    }

    /**
     * Handle the Product "updated" event.
     */
    public function updated(Product $product): void
    {
        $this->invalidateProductCaches();
        
        // If price changed, invalidate price range caches
        if ($product->wasChanged('price')) {
            $this->shopCache->invalidateProductCaches();
        }
        
        // If featured status changed, invalidate featured products cache
        if ($product->wasChanged('is_featured')) {
            $this->shopCache->invalidateProductCaches();
        }
        
        // If active status changed, invalidate all shop caches
        if ($product->wasChanged('is_active')) {
            $this->shopCache->invalidateAll();
        }
    }

    /**
     * Handle the Product "deleted" event.
     */
    public function deleted(Product $product): void
    {
        $this->invalidateProductCaches();
    }

    /**
     * Handle the Product "restored" event.
     */
    public function restored(Product $product): void
    {
        $this->invalidateProductCaches();
    }

    /**
     * Handle the Product "force deleted" event.
     */
    public function forceDeleted(Product $product): void
    {
        $this->invalidateProductCaches();
    }

    /**
     * Invalidate product-related caches.
     */
    private function invalidateProductCaches(): void
    {
        $this->shopCache->invalidateProductCaches();
    }
}
