<?php

namespace App\Listeners;

use App\Events\PermissionChanged;
use App\Events\RolePermissionsChanged;
use App\Services\ActivityLogger;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class LogPermissionChange implements ShouldQueue
{
    use InteractsWithQueue;

    protected ActivityLogger $activityLogger;

    /**
     * Create the event listener.
     */
    public function __construct(ActivityLogger $activityLogger)
    {
        $this->activityLogger = $activityLogger;
    }

    /**
     * Handle the PermissionChanged event.
     */
    public function handlePermissionChanged(PermissionChanged $event): void
    {
        $oldRoleName = $event->oldRole ? $event->oldRole->name : 'None';
        $newRoleName = $event->newRole->name;

        $message = "User role changed from '{$oldRoleName}' to '{$newRoleName}' for {$event->user->email}";

        $this->activityLogger->logActivity(
            'permission_change',
            $message,
            'success',
            null,
            [
                'user_id' => $event->user->id,
                'user_email' => $event->user->email,
                'old_role_id' => $event->oldRole?->id,
                'old_role_name' => $oldRoleName,
                'new_role_id' => $event->newRole->id,
                'new_role_name' => $newRoleName,
                'changed_by_id' => $event->changedBy->id,
                'changed_by_email' => $event->changedBy->email,
                'change_type' => $event->changeType,
                'metadata' => $event->metadata,
            ],
            [],
            50, // Medium-high risk score for permission changes
            $event->changedBy
        );

        // Log to Laravel log for additional tracking
        Log::info('Permission change logged', [
            'event' => 'PermissionChanged',
            'user_id' => $event->user->id,
            'old_role' => $oldRoleName,
            'new_role' => $newRoleName,
            'changed_by' => $event->changedBy->id,
        ]);
    }

    /**
     * Handle the RolePermissionsChanged event.
     */
    public function handleRolePermissionsChanged(RolePermissionsChanged $event): void
    {
        $changesSummary = $event->getChangesSummary();
        $affectedUsersCount = $event->role->users()->count();

        $message = "Permissions updated for role '{$event->role->name}' affecting {$affectedUsersCount} users";

        $this->activityLogger->logActivity(
            'role_permission_change',
            $message,
            'success',
            null,
            [
                'role_id' => $event->role->id,
                'role_name' => $event->role->name,
                'old_permissions' => $event->oldPermissions,
                'new_permissions' => $event->newPermissions,
                'changes_summary' => $changesSummary,
                'affected_users_count' => $affectedUsersCount,
                'changed_by_id' => $event->changedBy->id,
                'changed_by_email' => $event->changedBy->email,
                'metadata' => $event->metadata,
            ],
            [],
            75, // High risk score for role permission changes as they affect multiple users
            $event->changedBy
        );

        // Log to Laravel log for additional tracking
        Log::info('Role permissions change logged', [
            'event' => 'RolePermissionsChanged',
            'role_id' => $event->role->id,
            'role_name' => $event->role->name,
            'affected_users' => $affectedUsersCount,
            'changed_by' => $event->changedBy->id,
            'changes_summary' => $changesSummary,
        ]);
    }

    /**
     * Handle a job failure.
     */
    public function failed(PermissionChanged|RolePermissionsChanged $event, \Throwable $exception): void
    {
        Log::error('Failed to log permission change', [
            'event' => get_class($event),
            'exception' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }
}
