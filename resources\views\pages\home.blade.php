@extends('layouts.app')

@section('title', __('home.hero_title_plain') . ' - ' . __('common.company_name'))
@section('meta_description', __('home.hero_description'))
@section('meta_keywords', 'digital agency south africa, web development cape town, mobile app development johannesburg, e-commerce development, digital marketing services, seo services south africa, AI-powered web development, progressive web apps, cross-platform mobile apps, digital transformation, business intelligence, 2025 web technologies')
@section('og_type', 'website')
@section('og_image', asset('images/home-hero-og.jpg'))
@section('og_image_alt', 'ChiSolution Digital Agency - Leading Web Development and Digital Services in South Africa')
@section('twitter_image', asset('images/home-hero-twitter.jpg'))
@section('twitter_image_alt', 'ChiSolution Digital Agency - Expert Digital Solutions')

@push('structured_data')
<x-structured-data type="organization" />
@endpush

@push('structured_data')
<x-structured-data type="webpage" :data="[
    'name' => __('home.hero_title_plain'),
    'description' => __('home.meta_description'),
    'url' => url('/'),
    'language' => app()->getLocale(),
    'breadcrumb' => [
        ['name' => __('common.home'), 'url' => url('/')]
    ]
]" />
@endpush

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 text-white overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
    </div>
    
    <div class="container mx-auto px-4 py-20 lg:py-32 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- Hero Content -->
            <div class="text-center lg:text-left">
                <h1 class="text-4xl lg:text-6xl font-bold mb-6 leading-tight">
                    {!! __('home.hero_title') !!}
                </h1>
                <p class="text-xl lg:text-2xl text-blue-100 mb-8 leading-relaxed">
                    {{ __('home.hero_description') }}
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <a href="{{ route('services.index', ['locale' => app()->getLocale()]) }}" class="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-50 transition-colors inline-flex items-center justify-center">
                        {{ __('home.our_services') }}
                        <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </a>
                    <a href="{{ route('contact', ['locale' => app()->getLocale()]) }}" class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors inline-flex items-center justify-center">
                        {{ __('home.get_started') }}
                    </a>
                </div>
            </div>
            
            <!-- Hero Image -->
            <div class="relative">
                <div class="relative z-10">
                    <img src="{{ asset('images/hero-illustration.svg') }}"
                         alt="ChiSolution Digital Agency - AI-Powered Web Development, Mobile Apps, and Digital Marketing Services in South Africa"
                         class="w-full h-auto"
                         loading="eager"
                         width="600"
                         height="400">
                </div>
                <!-- Floating Elements -->
                <div class="absolute top-10 right-10 w-16 h-16 bg-blue-400 rounded-full opacity-20 animate-pulse"></div>
                <div class="absolute bottom-10 left-10 w-12 h-12 bg-blue-300 rounded-full opacity-30 animate-bounce"></div>
                <div class="absolute top-1/2 left-0 w-8 h-8 bg-white rounded-full opacity-20 animate-ping"></div>
            </div>
        </div>
    </div>
    
    <!-- Wave Separator -->
    <div class="absolute bottom-0 left-0 w-full">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block w-full h-16 fill-white">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
        </svg>
    </div>
</section>

<!-- About Section -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- About Image -->
            <div class="relative">
                <img src="{{ asset('images/about-illustration.svg') }}"
                     alt="About ChiSolution - Leading Digital Agency Team in South Africa specializing in Web Development and Digital Solutions"
                     class="w-full h-auto rounded-lg"
                     loading="lazy"
                     width="500"
                     height="400">
                <div class="absolute -bottom-6 -right-6 bg-blue-600 text-white p-6 rounded-lg shadow-lg">
                    <div class="text-center">
                        <div class="text-3xl font-bold">5+</div>
                        <div class="text-sm">{{ __('home.years_experience') }}</div>
                    </div>
                </div>
            </div>
            
            <!-- About Content -->
            <div>
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                    {!! __('home.about_title') !!}
                </h2>
                <p class="text-lg text-gray-600 mb-6 leading-relaxed">
                    {{ __('home.about_description_1') }}
                </p>
                <p class="text-gray-600 mb-8 leading-relaxed">
                    {{ __('home.about_description_2') }}
                </p>
                <a href="{{ route('about', ['locale' => app()->getLocale()]) }}" class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors inline-flex items-center">
                    {{ __('home.learn_more') }}
                    <svg class="ml-2 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <!-- Section Header -->
        <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                {!! __('home.services_title') !!}
            </h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                {{ __('home.services_description') }}
            </p>
        </div>
        
        <!-- Services Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- AI Services -->
            <div class="bg-gradient-to-br from-purple-50 to-indigo-50 p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow group border border-purple-100">
                <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="flex items-center mb-4">
                    <h3 class="text-xl font-semibold text-gray-900">{{ __('services.ai_services') }}</h3>
                    <span class="ml-2 px-2 py-1 text-xs bg-purple-100 text-purple-700 rounded-full font-medium">New</span>
                </div>
                <p class="text-gray-600 mb-6">{{ __('home.ai_services_description') }}</p>
                <a href="{{ route('services.ai-services', ['locale' => app()->getLocale()]) }}" class="text-purple-600 font-semibold hover:text-purple-700 transition-colors">{{ __('home.learn_more') }} →</a>
            </div>

            <!-- Web Development -->
            <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow group">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-blue-600 transition-colors">
                    <svg class="w-8 h-8 text-blue-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ __('common.web_development') }}</h3>
                <p class="text-gray-600 mb-6">{{ __('home.web_development_description') }}</p>
                <a href="{{ route('services.web-development', ['locale' => app()->getLocale()]) }}" class="text-blue-600 font-semibold hover:text-blue-700 transition-colors">{{ __('home.learn_more') }} →</a>
            </div>
            
            <!-- Mobile App Development -->
            <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow group">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-blue-600 transition-colors">
                    <svg class="w-8 h-8 text-blue-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7 2a2 2 0 00-2 2v12a2 2 0 002 2h6a2 2 0 002-2V4a2 2 0 00-2-2H7zM6 4a1 1 0 011-1h6a1 1 0 011 1v10a1 1 0 01-1 1H7a1 1 0 01-1-1V4zm2.5 9a1.5 1.5 0 103 0 1.5 1.5 0 00-3 0z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ __('common.mobile_app_development') }}</h3>
                <p class="text-gray-600 mb-6">{{ __('home.mobile_app_description') }}</p>
                <a href="{{ route('services.mobile-app-development', ['locale' => app()->getLocale()]) }}" class="text-blue-600 font-semibold hover:text-blue-700 transition-colors">{{ __('home.learn_more') }} →</a>
            </div>
            
            <!-- E-commerce Development -->
            <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow group">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-blue-600 transition-colors">
                    <svg class="w-8 h-8 text-blue-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 2L3 7v11a1 1 0 001 1h12a1 1 0 001-1V7l-7-5zM6 9.5a.5.5 0 01.5-.5h7a.5.5 0 01.5.5v1a.5.5 0 01-.5.5h-7a.5.5 0 01-.5-.5v-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ __('common.ecommerce_development') }}</h3>
                <p class="text-gray-600 mb-6">{{ __('home.ecommerce_description') }}</p>
                <a href="{{ route('services.ecommerce-development', ['locale' => app()->getLocale()]) }}" class="text-blue-600 font-semibold hover:text-blue-700 transition-colors">{{ __('home.learn_more') }} →</a>
            </div>
            
            <!-- Digital Marketing -->
            <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow group">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-blue-600 transition-colors">
                    <svg class="w-8 h-8 text-blue-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ __('common.digital_marketing') }}</h3>
                <p class="text-gray-600 mb-6">{{ __('home.digital_marketing_description') }}</p>
                <a href="{{ route('services.digital-marketing', ['locale' => app()->getLocale()]) }}" class="text-blue-600 font-semibold hover:text-blue-700 transition-colors">{{ __('home.learn_more') }} →</a>
            </div>
            
            <!-- SEO Services -->
            <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow group">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-blue-600 transition-colors">
                    <svg class="w-8 h-8 text-blue-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ __('common.seo_services') }}</h3>
                <p class="text-gray-600 mb-6">{{ __('home.seo_description') }}</p>
                <a href="{{ route('services.seo-services', ['locale' => app()->getLocale()]) }}" class="text-blue-600 font-semibold hover:text-blue-700 transition-colors">{{ __('home.learn_more') }} →</a>
            </div>
            
            <!-- Maintenance & Support -->
            <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow group">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-blue-600 transition-colors">
                    <svg class="w-8 h-8 text-blue-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ __('common.maintenance_support') }}</h3>
                <p class="text-gray-600 mb-6">{{ __('home.maintenance_description') }}</p>
                <a href="{{ route('services.maintenance-support', ['locale' => app()->getLocale()]) }}" class="text-blue-600 font-semibold hover:text-blue-700 transition-colors">{{ __('home.learn_more') }} →</a>
            </div>

            <!-- Data Analytics & Consultancy -->
            <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow group">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-blue-600 transition-colors">
                    <svg class="w-8 h-8 text-blue-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ __('common.data_analytics') }}</h3>
                <p class="text-gray-600 mb-6">{{ __('home.data_analytics_description') }}</p>
                <a href="{{ route('services.data-analytics', ['locale' => app()->getLocale()]) }}" class="text-blue-600 font-semibold hover:text-blue-700 transition-colors">{{ __('home.learn_more') }} →</a>
            </div>

            <!-- Accounting Services -->
            <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow group">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-blue-600 transition-colors">
                    <svg class="w-8 h-8 text-blue-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ __('common.accounting_services') }}</h3>
                <p class="text-gray-600 mb-6">{{ __('home.accounting_description') }}</p>
                <a href="{{ route('services.accounting-services', ['locale' => app()->getLocale()]) }}" class="text-blue-600 font-semibold hover:text-blue-700 transition-colors">{{ __('home.learn_more') }} →</a>
            </div>

            <!-- UI/UX Design -->
            <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow group">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-blue-600 transition-colors">
                    <svg class="w-8 h-8 text-blue-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">UI/UX Design</h3>
                <p class="text-gray-600 mb-6">Create intuitive, engaging user experiences with our expert UI/UX design services. From mobile apps to websites, we design interfaces that users love.</p>
                <a href="{{ route('services.ui-ux-design', ['locale' => app()->getLocale()]) }}" class="text-blue-600 font-semibold hover:text-blue-700 transition-colors">{{ __('home.learn_more') }} →</a>
            </div>
        </div>
    </div>
</section>

<!-- 3D Client Carousel Section -->
@if($featuredClients->count() > 0)
<x-carousel-3d
    :clients="$featuredClients"
    :autoplay="true"
    :interval="4000"
    title="{{ __('home.trusted_clients_title') }}"
    subtitle="{{ __('home.trusted_clients_description') }}"
/>
@endif

<!-- Featured Projects Section -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <!-- Section Header -->
        <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                {!! __('home.featured_projects_title') !!}
            </h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                {{ __('home.featured_projects_description') }}
            </p>
        </div>

        @if($featuredProjects->count() > 0)
            <!-- Projects Carousel -->
            <div id="featured-projects-carousel" class="relative">
                <!-- Carousel Container -->
                <div class="overflow-hidden">
                    <div class="carousel-track flex transition-transform duration-500 ease-in-out">
                        @foreach($featuredProjects as $project)
                            <div class="carousel-slide w-full md:w-1/2 lg:w-1/3 flex-shrink-0 px-4">
                                <div class="group relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-shadow">
                                    <div class="aspect-w-16 aspect-h-9">
                                        @if($project->featured_image)
                                            <img src="{{ $project->featured_image_url }}" alt="{{ $project->title }}" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                                        @else
                                            <div class="w-full h-64 bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                                                <svg class="w-16 h-16 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                                </svg>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                    <div class="absolute bottom-0 left-0 right-0 p-6 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                                        <h3 class="text-xl font-semibold mb-2">{{ $project->title }}</h3>
                                        <p class="text-sm text-gray-200 mb-4">{{ Str::limit($project->description, 60) }}</p>
                                        @if($project->service)
                                            <span class="inline-block bg-blue-600 text-white text-xs px-2 py-1 rounded mb-3">{{ $project->service->name }}</span>
                                        @endif
                                        <a href="{{ route('projects.show', ['locale' => app()->getLocale(), 'project' => $project->slug]) }}" class="inline-flex items-center text-blue-300 hover:text-blue-200">
                                            {{ __('home.view_project') }}
                                            <svg class="ml-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                @if($featuredProjects->count() > 3)
                    <!-- Navigation Arrows -->
                    <button class="carousel-prev absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 bg-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-200 z-10">
                        <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>
                    <button class="carousel-next absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 bg-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-200 z-10">
                        <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>

                    <!-- Indicators -->
                    <div class="flex justify-center mt-8 space-x-2">
                        @for($i = 0; $i < ceil($featuredProjects->count() / 3); $i++)
                            <button class="carousel-indicator w-3 h-3 rounded-full bg-gray-300 hover:bg-gray-400 transition-colors duration-200"></button>
                        @endfor
                    </div>
                @endif
            </div>
        @else
            <!-- No Projects Message -->
            <div class="text-center py-12">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ __('home.no_featured_projects') }}</h3>
                <p class="text-gray-600">{{ __('home.no_projects_message') }}</p>
            </div>
        @endif

        <!-- View All Projects Button -->
        <div class="text-center mt-12">
            <a href="{{ route('projects.index', ['locale' => app()->getLocale()]) }}" class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors inline-flex items-center">
                {{ __('home.view_all_projects') }}
                <svg class="ml-2 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </a>
        </div>
    </div>
</section>

<!-- Latest Blog Posts Section -->
@if($latestBlogPosts->count() > 0)
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <!-- Section Header -->
        <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                {!! __('home.latest_insights_title') !!}
            </h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                {{ __('home.latest_insights_description') }}
            </p>
        </div>

        <!-- Blog Posts Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($latestBlogPosts as $post)
                <article class="group bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                    <div class="relative overflow-hidden">
                        @if($post->featured_image)
                            <picture>
                                @if($post->getWebPImageUrl($post->featured_image))
                                    <source srcset="{{ $post->getWebPImageUrl($post->featured_image) }}" type="image/webp">
                                @endif
                                <img src="{{ $post->getOptimizedImageUrl($post->featured_image, 'medium') }}"
                                     alt="{{ $post->title }}"
                                     class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                            </picture>
                        @elseif($post->first_gallery_image_url)
                            <picture>
                                @if($post->getWebPImageUrl($post->gallery_images[0]))
                                    <source srcset="{{ $post->getWebPImageUrl($post->gallery_images[0]) }}" type="image/webp">
                                @endif
                                <img src="{{ $post->getOptimizedImageUrl($post->gallery_images[0], 'medium') }}"
                                     alt="{{ $post->title }}"
                                     class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                            </picture>
                        @else
                            <div class="w-full h-48 bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                                <svg class="w-12 h-12 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                </svg>
                            </div>
                        @endif

                        @if($post->total_image_count > 1)
                            <div class="absolute top-2 right-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded-full flex items-center">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                                </svg>
                                {{ $post->total_image_count }}
                            </div>
                        @endif
                    </div>

                    <div class="p-6">
                        <div class="flex items-center space-x-2 mb-3">
                            @if($post->category)
                                <span class="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded">{{ $post->category->name }}</span>
                            @endif
                            @if($post->is_featured)
                                <span class="px-2 py-1 bg-yellow-100 text-yellow-600 text-xs rounded">{{ __('home.featured') }}</span>
                            @endif
                            <span class="text-gray-500 text-xs">{{ $post->formatted_published_date }}</span>
                        </div>

                        <h3 class="text-lg font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                            <a href="{{ route('blog.show', ['locale' => app()->getLocale(), 'post' => $post->slug]) }}">{{ $post->title }}</a>
                        </h3>

                        <p class="text-gray-600 text-sm leading-relaxed mb-4">
                            {{ $post->excerpt }}
                        </p>

                        @if($post->services()->count() > 0)
                            <div class="flex flex-wrap gap-1 mb-4">
                                @foreach($post->services()->take(2) as $service)
                                    <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">{{ $service->name }}</span>
                                @endforeach
                                @if($post->services()->count() > 2)
                                    <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">+{{ $post->services()->count() - 2 }} {{ __('home.more') }}</span>
                                @endif
                            </div>
                        @endif

                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                                    <span class="text-white font-semibold text-xs">{{ substr($post->author->first_name, 0, 1) }}{{ substr($post->author->last_name, 0, 1) }}</span>
                                </div>
                                <span class="text-sm text-gray-600">{{ $post->author->first_name }} {{ $post->author->last_name }}</span>
                            </div>
                            <div class="flex items-center space-x-2 text-xs text-gray-500">
                                <span>{{ $post->reading_time }} {{ __('home.min_read') }}</span>
                                @if($post->view_count > 0)
                                    <span>•</span>
                                    <span>{{ number_format($post->view_count) }} {{ __('home.views') }}</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </article>
            @endforeach
        </div>

        <!-- View All Blog Button -->
        <div class="text-center mt-12">
            <a href="{{ route('blog.index', ['locale' => app()->getLocale()]) }}" class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors inline-flex items-center">
                {{ __('home.view_all_articles') }}
                <svg class="ml-2 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </a>
        </div>
    </div>
</section>
@endif

<!-- Testimonials Section -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <!-- Section Header -->
        <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                {!! __('home.testimonials_title') !!}
            </h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                {{ __('home.testimonials_description') }}
            </p>
        </div>

        <!-- Testimonials Carousel -->
        <div class="relative max-w-4xl mx-auto">
            <div id="testimonials-carousel" class="overflow-hidden">
                <div class="flex transition-transform duration-500 ease-in-out" id="testimonials-track">
                    <!-- Testimonial 1 -->
                    <div class="w-full flex-shrink-0 px-4">
                        <div class="bg-white p-8 rounded-lg shadow-lg text-center">
                            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                                <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <blockquote class="text-lg text-gray-600 mb-6 italic">
                                "{{ __('home.testimonial_1') }}"
                            </blockquote>
                            <div class="flex items-center justify-center">
                                <img src="{{ asset('images/testimonials/client-1.jpg') }}" alt="{{ __('home.testimonial_1_author') }}" class="w-12 h-12 rounded-full mr-4">
                                <div class="text-left">
                                    <div class="font-semibold text-gray-900">{{ __('home.testimonial_1_author') }}</div>
                                    <div class="text-sm text-gray-600">{{ __('home.testimonial_1_company') }}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial 2 -->
                    <div class="w-full flex-shrink-0 px-4">
                        <div class="bg-white p-8 rounded-lg shadow-lg text-center">
                            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                                <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <blockquote class="text-lg text-gray-600 mb-6 italic">
                                "{{ __('home.testimonial_2') }}"
                            </blockquote>
                            <div class="flex items-center justify-center">
                                <img src="{{ asset('images/testimonials/client-2.jpg') }}" alt="{{ __('home.testimonial_2_author') }}" class="w-12 h-12 rounded-full mr-4">
                                <div class="text-left">
                                    <div class="font-semibold text-gray-900">{{ __('home.testimonial_2_author') }}</div>
                                    <div class="text-sm text-gray-600">{{ __('home.testimonial_2_company') }}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial 3 -->
                    <div class="w-full flex-shrink-0 px-4">
                        <div class="bg-white p-8 rounded-lg shadow-lg text-center">
                            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                                <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <blockquote class="text-lg text-gray-600 mb-6 italic">
                                "{{ __('home.testimonial_3') }}"
                            </blockquote>
                            <div class="flex items-center justify-center">
                                <img src="{{ asset('images/testimonials/client-3.jpg') }}" alt="{{ __('home.testimonial_3_author') }}" class="w-12 h-12 rounded-full mr-4">
                                <div class="text-left">
                                    <div class="font-semibold text-gray-900">{{ __('home.testimonial_3_author') }}</div>
                                    <div class="text-sm text-gray-600">{{ __('home.testimonial_3_company') }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Carousel Controls -->
            <button id="prev-testimonial" class="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-shadow">
                <svg class="w-6 h-6 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
            </button>
            <button id="next-testimonial" class="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-shadow">
                <svg class="w-6 h-6 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
            </button>

            <!-- Carousel Indicators -->
            <div class="flex justify-center mt-8 space-x-2">
                <button class="w-3 h-3 bg-blue-600 rounded-full testimonial-indicator active" data-slide="0"></button>
                <button class="w-3 h-3 bg-gray-300 rounded-full testimonial-indicator" data-slide="1"></button>
                <button class="w-3 h-3 bg-gray-300 rounded-full testimonial-indicator" data-slide="2"></button>
            </div>
        </div>
    </div>
</section>

<!-- Newsletter Section -->
<section class="py-20 bg-blue-600 text-white">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- Newsletter Content -->
            <div>
                <h2 class="text-3xl lg:text-4xl font-bold mb-4">
                    {{ __('home.newsletter_title') }}
                </h2>
                <p class="text-xl text-blue-100 mb-6">
                    {{ __('home.newsletter_description') }}
                </p>
                <ul class="space-y-2 text-blue-100">
                    <li class="flex items-center">
                        <svg class="w-5 h-5 mr-3 text-blue-300" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('home.newsletter_feature_1') }}
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 mr-3 text-blue-300" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('home.newsletter_feature_2') }}
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 mr-3 text-blue-300" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('home.newsletter_feature_3') }}
                    </li>
                </ul>
            </div>

            <!-- Newsletter Form -->
            <div class="bg-white p-8 rounded-lg">
                <!-- Success/Error Messages -->
                <div id="newsletter-message" class="mb-4 hidden">
                    <div id="newsletter-message-content" class="p-4 rounded-lg"></div>
                </div>

                <form id="newsletter-form" action="{{ route('newsletter.subscribe', ['locale' => app()->getLocale()]) }}" method="POST" class="space-y-4">
                    @csrf
                    <div>
                        <label for="newsletter_email" class="block text-sm font-medium text-gray-700 mb-2">{{ __('home.email_address') }}</label>
                        <input type="email" id="newsletter_email" name="email" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="{{ __('home.email_placeholder') }}">
                    </div>
                    <div>
                        <label for="newsletter_name" class="block text-sm font-medium text-gray-700 mb-2">{{ __('home.name_optional') }}</label>
                        <input type="text" id="newsletter_name" name="name"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="{{ __('home.name_placeholder') }}">
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="newsletter_consent" name="consent" required
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="newsletter_consent" class="ml-2 block text-sm text-gray-700">
                            {{ __('home.newsletter_consent') }}
                        </label>
                    </div>
                    <button type="submit" id="newsletter-submit" class="w-full bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                        {{ __('home.subscribe_now') }}
                    </button>
                </form>

                <div class="mt-4 text-center">
                    <p class="text-xs text-gray-500">
                        {{ __('home.already_subscribed') }}
                        <a href="{{ route('newsletter.unsubscribe.form', ['locale' => app()->getLocale()]) }}" class="text-blue-600 hover:text-blue-800 underline">
                            {{ __('home.unsubscribe_here') }}
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-blue-600 text-white">
    <div class="container mx-auto px-4 text-center">
        <h2 class="text-3xl lg:text-4xl font-bold mb-4">
            {{ __('home.cta_title') }}
        </h2>
        <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            {{ __('home.cta_description') }}
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('project-applications.create', ['locale' => app()->getLocale()]) }}" class="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-50 transition-colors inline-flex items-center justify-center">
                {{ __('home.start_application') }}
                <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </a>
            <a href="{{ route('contact', ['locale' => app()->getLocale()]) }}" class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors inline-flex items-center justify-center">
                {{ __('home.get_free_consultation') }}
            </a>
            <a href="{{ route('projects.index', ['locale' => app()->getLocale()]) }}" class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors inline-flex items-center justify-center">
                {{ __('home.view_our_work') }}
            </a>
        </div>
    </div>
</section>

<!-- Tech Supply & Fit Services Section -->
<section class="py-20 relative overflow-hidden" style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);">
    <!-- Subtle Pattern Overlay -->
    <div class="absolute inset-0 opacity-5" style="background-image: radial-gradient(circle at 1px 1px, rgba(59,130,246,0.3) 1px, transparent 0); background-size: 20px 20px;"></div>
    <div class="container mx-auto px-4 relative z-10">
        <!-- Section Header -->
        <div class="text-center max-w-3xl mx-auto mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                {{ __('home.tech_supply_title') }}
            </h2>
            <p class="text-xl text-gray-600 mb-6">
                {{ __('home.tech_supply_description') }}
            </p>
            <div class="w-20 h-1 bg-blue-600 mx-auto"></div>
        </div>

        <!-- Services Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            <!-- Computer & Laptop Supply -->
            <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow group">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-blue-600 transition-colors">
                    <svg class="w-8 h-8 text-blue-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ __('home.computers_laptops_title') }}</h3>
                <p class="text-gray-600 mb-6">
                    {{ __('home.computers_laptops_description') }}
                </p>
                <ul class="space-y-2 text-sm text-gray-600 mb-6">
                    <li class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('home.business_grade_hardware') }}
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('home.bulk_pricing_available') }}
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('home.warranty_included') }}
                    </li>
                </ul>
            </div>

            <!-- Professional Installation -->
            <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow group">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-blue-600 transition-colors">
                    <svg class="w-8 h-8 text-blue-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ __('home.professional_installation_title') }}</h3>
                <p class="text-gray-600 mb-6">
                    {{ __('home.professional_installation_description') }}
                </p>
                <ul class="space-y-2 text-sm text-gray-600 mb-6">
                    <li class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('home.onsite_installation') }}
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('home.network_configuration') }}
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('home.software_setup') }}
                    </li>
                </ul>
            </div>

            <!-- Upgrades & Accessories -->
            <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow group">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-blue-600 transition-colors">
                    <svg class="w-8 h-8 text-blue-600 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ __('home.upgrades_accessories_title') }}</h3>
                <p class="text-gray-600 mb-6">
                    {{ __('home.upgrades_accessories_description') }}
                </p>
                <ul class="space-y-2 text-sm text-gray-600 mb-6">
                    <li class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('home.memory_storage_upgrades') }}
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('home.peripherals_accessories') }}
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('home.performance_optimization') }}
                    </li>
                </ul>
            </div>
        </div>

        <!-- Features Section -->
        <div class="bg-white rounded-lg shadow-lg p-8 mb-12">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-2">{{ __('home.quality_assured') }}</h4>
                    <p class="text-sm text-gray-600">{{ __('home.quality_assured_description') }}</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-2">{{ __('home.fast_delivery') }}</h4>
                    <p class="text-sm text-gray-600">{{ __('home.fast_delivery_description') }}</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-2">{{ __('home.expert_support') }}</h4>
                    <p class="text-sm text-gray-600">{{ __('home.expert_support_description') }}</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-2">{{ __('home.competitive_pricing') }}</h4>
                    <p class="text-sm text-gray-600">{{ __('home.competitive_pricing_description') }}</p>
                </div>
            </div>
        </div>

        <!-- CTA Section -->
        <div class="text-center">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">
                {{ __('home.tech_cta_title') }}
            </h3>
            <p class="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
                {{ __('home.tech_cta_description') }}
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('contact', ['locale' => app()->getLocale()]) }}" class="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors inline-flex items-center justify-center">
                    {{ __('home.get_free_quote') }}
                    <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </a>
                <a href="{{ route('shop.index', ['locale' => app()->getLocale()]) }}" class="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors inline-flex items-center justify-center">
                    {{ __('home.browse_products') }}
                </a>
            </div>
        </div>
    </div>
</section>

@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Testimonials Carousel
    const track = document.getElementById('testimonials-track');
    const prevBtn = document.getElementById('prev-testimonial');
    const nextBtn = document.getElementById('next-testimonial');
    const indicators = document.querySelectorAll('.testimonial-indicator');

    let currentSlide = 0;
    const totalSlides = 3;

    function updateCarousel() {
        const translateX = -currentSlide * 100;
        track.style.transform = `translateX(${translateX}%)`;

        // Update indicators
        indicators.forEach((indicator, index) => {
            if (index === currentSlide) {
                indicator.classList.add('bg-blue-600');
                indicator.classList.remove('bg-gray-300');
                indicator.classList.add('active');
            } else {
                indicator.classList.remove('bg-blue-600');
                indicator.classList.add('bg-gray-300');
                indicator.classList.remove('active');
            }
        });
    }

    function nextSlide() {
        currentSlide = (currentSlide + 1) % totalSlides;
        updateCarousel();
    }

    function prevSlide() {
        currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
        updateCarousel();
    }

    // Event listeners
    nextBtn.addEventListener('click', nextSlide);
    prevBtn.addEventListener('click', prevSlide);

    // Indicator clicks
    indicators.forEach((indicator, index) => {
        indicator.addEventListener('click', () => {
            currentSlide = index;
            updateCarousel();
        });
    });

    // Auto-play carousel
    setInterval(nextSlide, 5000);

    // Newsletter form AJAX enhancement
    const newsletterForm = document.getElementById('newsletter-form');
    const newsletterMessage = document.getElementById('newsletter-message');
    const newsletterMessageContent = document.getElementById('newsletter-message-content');

    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const email = this.querySelector('input[name="email"]').value;
            const consent = this.querySelector('input[name="consent"]').checked;
            const submitBtn = document.getElementById('newsletter-submit');
            const originalText = submitBtn.textContent;

            // Basic validation
            if (!email || !consent) {
                showNewsletterMessage('Please fill in your email and agree to receive marketing emails.', 'error');
                return;
            }

            // Show loading state
            submitBtn.innerHTML = `
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Subscribing...
            `;
            submitBtn.disabled = true;

            // Hide previous messages
            newsletterMessage.classList.add('hidden');

            // Submit form via AJAX
            fetch(this.action, {
                method: 'POST',
                body: new FormData(this),
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNewsletterMessage(data.message, 'success');
                    // Clear form on success
                    this.reset();
                } else {
                    let errorMessage = data.message || 'An error occurred. Please try again.';

                    // Show field errors if any
                    if (data.errors) {
                        const errorList = Object.values(data.errors).flat();
                        if (errorList.length > 0) {
                            errorMessage += ' ' + errorList.join(' ');
                        }
                    }

                    showNewsletterMessage(errorMessage, data.type === 'info' ? 'info' : 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNewsletterMessage('An unexpected error occurred. Please try again later.', 'error');
            })
            .finally(() => {
                // Restore button
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        });
    }

    function showNewsletterMessage(message, type) {
        newsletterMessage.classList.remove('hidden');

        // Remove existing classes
        newsletterMessageContent.className = 'p-4 rounded-lg';

        // Add appropriate classes based on type
        if (type === 'success') {
            newsletterMessageContent.classList.add('bg-green-100', 'border', 'border-green-400', 'text-green-700');
        } else if (type === 'info') {
            newsletterMessageContent.classList.add('bg-blue-100', 'border', 'border-blue-400', 'text-blue-700');
        } else {
            newsletterMessageContent.classList.add('bg-red-100', 'border', 'border-red-400', 'text-red-700');
        }

        newsletterMessageContent.textContent = message;

        // Auto-hide success messages after 5 seconds
        if (type === 'success') {
            setTimeout(() => {
                newsletterMessage.classList.add('hidden');
            }, 5000);
        }
    }
            submitBtn.textContent = 'Subscribing...';
            submitBtn.disabled = true;

            // Re-enable after 3 seconds if form doesn't submit
            setTimeout(() => {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }, 3000);
        });
    }
});
</script>
@endpush

