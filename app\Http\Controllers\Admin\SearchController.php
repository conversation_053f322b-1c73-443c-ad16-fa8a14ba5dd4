<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\GlobalSearchService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class SearchController extends Controller
{
    protected GlobalSearchService $searchService;

    public function __construct(GlobalSearchService $searchService)
    {
        $this->searchService = $searchService;
    }

    /**
     * Show the advanced search page.
     */
    public function advanced()
    {
        return view('admin.search.index');
    }

    /**
     * Handle global search requests.
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'q' => 'required|string|min:2|max:100',
                'limit' => 'sometimes|integer|min:1|max:100',
                'type' => 'sometimes|nullable|string|in:user,order,product,project,job,job_application,coupon,payment,activity_log,visitor_analytic,contact_submission,newsletter_subscription',
                'status' => 'sometimes|nullable|string|max:50',
                'date_range' => 'sometimes|nullable|string|in:today,week,month,quarter,year'
            ]);

            $query = $request->get('q');
            $limit = $request->get('limit', 50);
            $filters = array_filter($request->only(['type', 'status', 'date_range']), function($value) {
                return !empty($value);
            });

            $results = $this->searchService->search($query, $limit, $filters);

            return response()->json([
                'success' => true,
                'results' => $results->values(),
                'total' => $results->count(),
                'query' => $query,
                'filters' => $filters
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid search parameters.',
                'errors' => $e->errors(),
                'results' => [],
                'total' => 0,
                'query' => $request->get('q', '')
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Search failed', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'query' => $request->get('q', ''),
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Search failed. Please try again.',
                'results' => [],
                'total' => 0,
                'query' => $request->get('q', '')
            ], 500);
        }
    }

    /**
     * Get search suggestions based on query.
     */
    public function suggestions(Request $request): JsonResponse
    {
        $request->validate([
            'q' => 'required|string|min:1|max:50',
        ]);

        $query = $request->get('q');

        try {
            // Get quick suggestions with limited results
            $results = $this->searchService->search($query, 10);

            // Group by type for better organization
            $grouped = $results->groupBy('type')->map(function ($items, $type) {
                return [
                    'type' => $type,
                    'label' => $this->getTypeLabel($type),
                    'items' => $items->take(3)->values()
                ];
            });

            return response()->json([
                'success' => true,
                'suggestions' => $grouped->values(),
                'query' => $query
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get suggestions.',
                'suggestions' => [],
                'query' => $query
            ], 500);
        }
    }

    /**
     * Get advanced search filters and options.
     */
    public function filters(): JsonResponse
    {
        try {
            $filters = [
                'types' => [
                    ['value' => 'user', 'label' => 'Users'],
                    ['value' => 'order', 'label' => 'Orders'],
                    ['value' => 'product', 'label' => 'Products'],
                    ['value' => 'project', 'label' => 'Projects'],
                    ['value' => 'job', 'label' => 'Jobs'],
                    ['value' => 'job_application', 'label' => 'Job Applications'],
                    ['value' => 'coupon', 'label' => 'Coupons'],
                    ['value' => 'payment', 'label' => 'Payments'],
                    ['value' => 'activity_log', 'label' => 'Activity Logs'],
                    ['value' => 'visitor_analytic', 'label' => 'Visitor Analytics'],
                ],
                'order_statuses' => [
                    ['value' => 'pending', 'label' => 'Pending'],
                    ['value' => 'processing', 'label' => 'Processing'],
                    ['value' => 'shipped', 'label' => 'Shipped'],
                    ['value' => 'delivered', 'label' => 'Delivered'],
                    ['value' => 'cancelled', 'label' => 'Cancelled'],
                ],
                'payment_statuses' => [
                    ['value' => 'pending', 'label' => 'Pending'],
                    ['value' => 'paid', 'label' => 'Paid'],
                    ['value' => 'failed', 'label' => 'Failed'],
                    ['value' => 'refunded', 'label' => 'Refunded'],
                ],
                'user_roles' => [
                    ['value' => 'admin', 'label' => 'Admin'],
                    ['value' => 'staff', 'label' => 'Staff'],
                    ['value' => 'client', 'label' => 'Client'],
                    ['value' => 'customer', 'label' => 'Customer'],
                ],
                'project_statuses' => [
                    ['value' => 'planning', 'label' => 'Planning'],
                    ['value' => 'in_progress', 'label' => 'In Progress'],
                    ['value' => 'review', 'label' => 'Under Review'],
                    ['value' => 'completed', 'label' => 'Completed'],
                    ['value' => 'on_hold', 'label' => 'On Hold'],
                    ['value' => 'cancelled', 'label' => 'Cancelled'],
                ]
            ];

            return response()->json([
                'success' => true,
                'filters' => $filters
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get filters.',
                'filters' => []
            ], 500);
        }
    }

    /**
     * Get type label for display.
     */
    private function getTypeLabel(string $type): string
    {
        $labels = [
            'user' => 'Users',
            'order' => 'Orders',
            'product' => 'Products',
            'project' => 'Projects',
            'job' => 'Jobs',
            'job_application' => 'Job Applications',
            'coupon' => 'Coupons',
            'payment' => 'Payments',
            'activity_log' => 'Activity Logs',
            'visitor_analytic' => 'Visitor Analytics',
        ];

        return $labels[$type] ?? ucfirst($type);
    }
}
