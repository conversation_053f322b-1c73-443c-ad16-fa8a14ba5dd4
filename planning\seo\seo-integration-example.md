# Laravel SEO Integration — SeoService, Blade Snippets, Sitemap & Tests

This document contains a starter implementation for SEO in a Laravel app, based on the sprint plan approved. It includes:
- `SeoService` class
- Blade snippet for rendering meta and structured data
- Sitemap generation command
- Example controller usage
- Unit tests for meta output

---

## 1. SeoService Implementation
```php
<?php

namespace App\Services;

use Artesaos\SEOTools\Facades\SEOMeta;
use Artesaos\SEOTools\Facades\OpenGraph;
use Artesaos\SEOTools\Facades\TwitterCard;
use Spatie\SchemaOrg\Schema;

class SeoService
{
    protected array $jsonLd = [];

    public function setTitle(string $title): self
    {
        SEOMeta::setTitle($title);
        OpenGraph::setTitle($title);
        TwitterCard::setTitle($title);
        return $this;
    }

    public function setDescription(string $description): self
    {
        SEOMeta::setDescription($description);
        OpenGraph::setDescription($description);
        TwitterCard::setDescription($description);
        return $this;
    }

    public function addMeta(string $name, string $content): self
    {
        SEOMeta::addMeta($name, $content);
        return $this;
    }

    public function addJsonLd($schema): self
    {
        $this->jsonLd[] = $schema->toScript();
        return $this;
    }

    public function render(): string
    {
        return implode("\n", [
            SEOMeta::generate(),
            OpenGraph::generate(),
            TwitterCard::generate(),
            ...$this->jsonLd
        ]);
    }
}
```

---

## 2. Blade Snippet
In `resources/views/components/seo.blade.php`:
```blade
{!! app(App\Services\SeoService::class)->render() !!}
```
Usage in layout:
```blade
<head>
    ...
    <x-seo />
</head>
```

---

## 3. Sitemap Generation

**Command**: `php artisan make:command GenerateSitemap`
```php
<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;

class GenerateSitemap extends Command
{
    protected $signature = 'seo:generate-sitemap';
    protected $description = 'Generate the sitemap.xml file';

    public function handle(): void
    {
        Sitemap::create()
            ->add(Url::create('/'))
            ->add(Url::create('/about'))
            ->add(Url::create('/services'))
            ->writeToFile(public_path('sitemap.xml'));

        $this->info('Sitemap generated at ' . public_path('sitemap.xml'));
    }
}
```

**Scheduler (app/Console/Kernel.php)**:
```php
$schedule->command('seo:generate-sitemap')->weekly();
```

---

## 4. Example Controller Usage
```php
public function show(Post $post, SeoService $seo)
{
    $seo->setTitle($post->title)
        ->setDescription($post->excerpt)
        ->addJsonLd(
            Schema::article()
                ->headline($post->title)
                ->datePublished($post->created_at)
        );

    return view('posts.show', compact('post'));
}
```

---

## 5. Unit Tests
`tests/Unit/SeoServiceTest.php`
```php
<?php

namespace Tests\Unit;

use App\Services\SeoService;
use PHPUnit\Framework\TestCase;
use Spatie\SchemaOrg\Schema;

class SeoServiceTest extends TestCase
{
    public function test_it_sets_title_and_description()
    {
        $seo = new SeoService();
        $seo->setTitle('My Title')->setDescription('My Description');

        $output = $seo->render();

        $this->assertStringContainsString('My Title', $output);
        $this->assertStringContainsString('My Description', $output);
    }

    public function test_it_adds_jsonld()
    {
        $seo = new SeoService();
        $seo->addJsonLd(
            Schema::article()->headline('Test Article')
        );

        $output = $seo->render();
        $this->assertStringContainsString('Test Article', $output);
        $this->assertStringContainsString('application/ld+json', $output);
    }
}
```

---

## 6. Feature Test Example
`tests/Feature/SeoRenderTest.php`
```php
<?php

namespace Tests\Feature;

use Tests\TestCase;

class SeoRenderTest extends TestCase
{
    public function test_seo_snippet_is_rendered_in_layout()
    {
        $response = $this->get('/');

        $response->assertStatus(200);
        $response->assertSee('<meta', false);
        $response->assertSee('application/ld+json', false);
    }
}
```

---

## 7. Blade Helper Directive (Optional)
Instead of component:
```php
Blade::directive('seo', function () {
    return "<?php echo app(\\App\\Services\\SeoService::class)->render(); ?>";
});
```
Usage:
```blade
@seo
```

---

## 8. Developer Documentation
- **Adding page SEO**: Inject `SeoService` into controller, call setters.
- **Adding structured data**: Use `Spatie\SchemaOrg` builders, pass to `addJsonLd()`.
- **Generating sitemap**: Run `php artisan seo:generate-sitemap`, or rely on scheduler.

---

## 9. Caveats & Next Steps
- **Caveat — Avoid duplication**: Don’t redefine titles/descriptions in multiple services. Centralize in `SeoService`.
- **Caveat — Performance**: JSON-LD generation with complex schemas can add overhead. Cache where possible.
- **Caveat — Testing**: Meta tags often change with design/marketing input. Keep tests flexible (assert presence, not full HTML match).
- **Next Step — Multi-language SEO**: Integrate Laravel localization into `SeoService`.
- **Next Step — OpenGraph Images**: Extend service to handle dynamic OG/Twitter preview images.
- **Next Step — Canonical URLs**: Add method `setCanonical()` for duplicate content management.
- **Next Step — Automated Audits**: Add GitHub Action with Lighthouse CI or Ahrefs API to catch regressions.
- **Next Step — Analytics Integration**: Provide hook for GA4/Matomo so SEO impact can be measured.

---
