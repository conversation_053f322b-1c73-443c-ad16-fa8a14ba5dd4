<?php

namespace App\Http\Middleware;

use App\Services\VisitorAnalytics;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class VisitorAnalyticsMiddleware
{
    protected VisitorAnalytics $visitorAnalytics;

    public function __construct(VisitorAnalytics $visitorAnalytics)
    {
        $this->visitorAnalytics = $visitorAnalytics;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip tracking for non-GET requests and unwanted routes
        if ($request->method() !== 'GET' || $this->shouldSkipTracking($request)) {
            return $next($request);
        }

        $startTime = microtime(true);
        $response = $next($request);
        $endTime = microtime(true);

        // Queue analytics processing asynchronously to avoid blocking
        $this->queueAnalyticsProcessing($request, $response, $startTime, $endTime);

        return $response;
    }

    /**
     * Queue analytics processing for background execution.
     */
    private function queueAnalyticsProcessing(Request $request, Response $response, float $startTime, float $endTime): void
    {
        // Use Laravel's queue system or simple async processing
        try {
            // For immediate deployment without queue setup, use a simple async approach
            if (function_exists('fastcgi_finish_request')) {
                // This allows the response to be sent to user while processing continues
                register_shutdown_function(function() use ($request, $response, $startTime, $endTime) {
                    $this->processAnalytics($request, $response, $startTime, $endTime);
                });
            } else {
                // Fallback: process with timeout protection
                $this->processAnalyticsWithTimeout($request, $response, $startTime, $endTime);
            }
        } catch (\Exception $e) {
            // Never let analytics break the request
            \Log::error('Analytics queue failed: ' . $e->getMessage());
        }
    }

    /**
     * Process analytics with timeout protection.
     */
    private function processAnalyticsWithTimeout(Request $request, Response $response, float $startTime, float $endTime): void
    {
        $processingStart = microtime(true);
        $maxProcessingTime = 0.5; // 500ms max for analytics

        try {
            if ($response->getStatusCode() >= 200 && $response->getStatusCode() < 400) {
                // Check timeout before processing
                if ((microtime(true) - $processingStart) > $maxProcessingTime) {
                    return;
                }

                $responseTime = round(($endTime - $startTime) * 1000);

                $this->visitorAnalytics->trackPageVisit(
                    $this->extractPageTitle($response),
                    [
                        'response_status' => $response->getStatusCode(),
                        'response_time_ms' => $responseTime,
                    ]
                );
            }
        } catch (\Exception $e) {
            \Log::error('Visitor analytics processing failed: ' . $e->getMessage());
        }
    }

    /**
     * Process analytics (for background execution).
     */
    private function processAnalytics(Request $request, Response $response, float $startTime, float $endTime): void
    {
        try {
            if ($response->getStatusCode() >= 200 && $response->getStatusCode() < 400) {
                $responseTime = round(($endTime - $startTime) * 1000);

                $this->visitorAnalytics->trackPageVisit(
                    $this->extractPageTitle($response),
                    [
                        'response_status' => $response->getStatusCode(),
                        'response_time_ms' => $responseTime,
                    ]
                );
            } else {
                $this->visitorAnalytics->trackError(
                    'http_error',
                    "HTTP {$response->getStatusCode()} error",
                    [
                        'status_code' => $response->getStatusCode(),
                        'url' => $request->fullUrl(),
                        'method' => $request->method(),
                    ]
                );
            }
        } catch (\Exception $e) {
            \Log::error('Background analytics processing failed: ' . $e->getMessage());
        }
    }

    /**
     * Determine if tracking should be skipped for this request.
     */
    protected function shouldSkipTracking(Request $request): bool
    {
        $path = $request->path();

        // Ultra-fast static asset detection
        if ($this->isStaticAsset($path)) {
            return true;
        }

        // Skip admin routes
        if (str_starts_with($path, 'admin/') || str_starts_with($path, 'api/')) {
            return true;
        }

        // Skip debug routes
        $debugRoutes = ['livewire', '_debugbar', 'telescope', 'horizon'];
        foreach ($debugRoutes as $route) {
            if (str_starts_with($path, $route)) {
                return true;
            }
        }

        // Skip if user is authenticated as admin/staff AND accessing admin routes
        if (auth()->check() && auth()->user()->isAdminOrStaff() && str_starts_with($path, 'admin/')) {
            return true;
        }

        return false;
    }

    /**
     * Ultra-fast static asset detection.
     */
    private function isStaticAsset(string $path): bool
    {
        // Check file extension directly (fastest method)
        $extension = pathinfo($path, PATHINFO_EXTENSION);

        $staticExtensions = [
            'css', 'js', 'png', 'jpg', 'jpeg', 'gif', 'svg', 'ico',
            'woff', 'woff2', 'ttf', 'eot', 'otf', 'webp', 'avif',
            'mp4', 'webm', 'pdf', 'zip', 'txt', 'xml', 'json'
        ];

        return in_array(strtolower($extension), $staticExtensions);
    }

    /**
     * Extract page title from response content.
     */
    protected function extractPageTitle(Response $response): ?string
    {
        if (!$response instanceof \Illuminate\Http\Response) {
            return null;
        }

        $content = $response->getContent();
        
        if (preg_match('/<title[^>]*>(.*?)<\/title>/i', $content, $matches)) {
            return trim(strip_tags($matches[1]));
        }

        return null;
    }
}
