/**
 * Dynamic Script Loader for Performance Optimization
 */

class DynamicLoader {
    static async loadAI3D() {
        // Only load if on AI services page and user interacts
        if (!document.querySelector('#ai-services-hero')) return;

        // Load heavy 3D libraries only when needed
        const { default: AIServices3D } = await import('./ai-services-3d.js');
        return new AIServices3D();
    }

    static initIntersectionLoader() {
        // Load 3D when user scrolls to technologies section
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadAI3D();
                    observer.disconnect();
                }
            });
        }, { threshold: 0.1 });

        const target = document.querySelector('.ai-technologies-section');
        if (target) observer.observe(target);
    }

    static initUserInteractionLoader() {
        // Load 3D on first user interaction
        const events = ['click', 'scroll', 'keydown', 'touchstart'];
        
        const loadOnce = () => {
            this.loadAI3D();
            events.forEach(event => {
                document.removeEventListener(event, loadOnce);
            });
        };

        events.forEach(event => {
            document.addEventListener(event, loadOnce, { once: true, passive: true });
        });
    }
}

// Initialize loaders
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        DynamicLoader.initIntersectionLoader();
        DynamicLoader.initUserInteractionLoader();
    });
} else {
    DynamicLoader.initIntersectionLoader();
    DynamicLoader.initUserInteractionLoader();
}

export default DynamicLoader;