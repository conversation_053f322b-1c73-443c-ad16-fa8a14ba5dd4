<?php

namespace App\Http\Controllers;

use App\Models\BlogPost;
use App\Models\BlogComment;
use App\Services\ImageService;
use App\Services\VisitorAnalytics;
use App\Services\ActivityLogger;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class CommentController extends Controller
{
    protected ImageService $imageService;
    protected VisitorAnalytics $visitorAnalytics;
    protected ActivityLogger $activityLogger;

    public function __construct(
        ImageService $imageService,
        VisitorAnalytics $visitorAnalytics,
        ActivityLogger $activityLogger
    ) {
        $this->imageService = $imageService;
        $this->visitorAnalytics = $visitorAnalytics;
        $this->activityLogger = $activityLogger;

        // Require authentication for all comment actions
        $this->middleware('auth');

        // Rate limiting for comment submission
        $this->middleware('throttle:10,1')->only(['store', 'rate']);
    }

    /**
     * Store a new comment.
     */
    public function store(Request $request, BlogPost $post): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'content' => 'required|string|min:10|max:2000',
                'rating' => 'nullable|integer|min:1|max:5',
                'parent_comment_id' => 'nullable|exists:blog_comments,id',
                'attachments' => 'nullable|array|max:3',
                'attachments.*' => 'file|max:10240|mimes:jpg,jpeg,png,gif,pdf,doc,docx,txt', // 10MB max
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Check if user already rated this post (if rating provided)
            if ($request->rating && $post->hasUserRated(Auth::id())) {
                return response()->json([
                    'success' => false,
                    'message' => 'You have already rated this post. You can only rate once.'
                ], 422);
            }

            // Handle file attachments
            $attachmentPaths = [];
            if ($request->hasFile('attachments')) {
                foreach ($request->file('attachments') as $file) {
                    $attachmentPath = $this->handleFileUpload($file);
                    if ($attachmentPath) {
                        $attachmentPaths[] = $attachmentPath;
                    }
                }
            }

            // Create the comment
            $comment = BlogComment::create([
                'blog_post_id' => $post->id,
                'user_id' => Auth::id(),
                'parent_comment_id' => $request->parent_comment_id,
                'content' => strip_tags($request->content, '<p><br><strong><em><u>'), // Allow basic HTML
                'rating' => $request->rating,
                'attachments' => !empty($attachmentPaths) ? $attachmentPaths : null,
                'is_approved' => false, // Requires admin approval
            ]);

            // Track comment submission
            $this->visitorAnalytics->trackPageVisit('Comment Submitted', [
                'blog_post_id' => $post->id,
                'comment_id' => $comment->id,
                'has_rating' => !is_null($request->rating),
                'has_attachments' => !empty($attachmentPaths),
                'is_reply' => !is_null($request->parent_comment_id),
            ]);

            // Log activity
            $this->activityLogger->log(
                'comment_submitted',
                'BlogComment',
                $comment->id,
                [
                    'blog_post_title' => $post->title,
                    'comment_content' => Str::limit($comment->content, 100),
                    'rating' => $comment->rating,
                    'attachment_count' => count($attachmentPaths),
                ]
            );

            return response()->json([
                'success' => true,
                'message' => 'Comment submitted successfully! It will appear after admin approval.',
                'comment' => [
                    'id' => $comment->uuid,
                    'content' => $comment->content,
                    'rating' => $comment->rating,
                    'created_at' => $comment->formatted_created_date,
                    'user_name' => $comment->user->first_name . ' ' . $comment->user->last_name,
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Comment submission failed', [
                'user_id' => Auth::id(),
                'blog_post_id' => $post->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while submitting your comment. Please try again.'
            ], 500);
        }
    }

    /**
     * Handle file upload with security scanning and optimization.
     */
    private function handleFileUpload($file): ?string
    {
        try {
            // Generate unique filename
            $filename = Str::uuid() . '.' . $file->getClientOriginalExtension();
            $path = 'blog-comments/' . date('Y/m');

            // Store the file
            $filePath = $file->storeAs($path, $filename, 'public');

            // If it's an image, scan and optimize using ImageService
            if ($this->isImage($file)) {
                try {
                    // Scan for malicious content
                    $this->imageService->scanImage(storage_path('app/public/' . $filePath));

                    // Optimize the image
                    $this->imageService->optimizeImage(storage_path('app/public/' . $filePath));

                } catch (\Exception $e) {
                    // If scanning/optimization fails, delete the file and reject upload
                    Storage::disk('public')->delete($filePath);
                    \Log::warning('File upload rejected due to security scan failure', [
                        'filename' => $filename,
                        'error' => $e->getMessage()
                    ]);
                    return null;
                }
            }

            return $filePath;

        } catch (\Exception $e) {
            \Log::error('File upload failed', [
                'filename' => $file->getClientOriginalName(),
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Check if file is an image.
     */
    private function isImage($file): bool
    {
        return in_array(strtolower($file->getClientOriginalExtension()), ['jpg', 'jpeg', 'png', 'gif', 'webp']);
    }

    /**
     * Mark comment as helpful.
     */
    public function markHelpful(Request $request, BlogComment $comment): JsonResponse
    {
        try {
            if ($comment->markAsHelpful(Auth::id())) {
                return response()->json([
                    'success' => true,
                    'helpful_count' => $comment->helpful_count
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'You have already marked this comment as helpful.'
            ], 422);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred.'
            ], 500);
        }
    }

    /**
     * Remove helpful mark.
     */
    public function removeHelpful(Request $request, BlogComment $comment): JsonResponse
    {
        try {
            if ($comment->removeHelpful(Auth::id())) {
                return response()->json([
                    'success' => true,
                    'helpful_count' => $comment->helpful_count
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'You have not marked this comment as helpful.'
            ], 422);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred.'
            ], 500);
        }
    }

    /**
     * Flag comment as inappropriate.
     */
    public function flag(Request $request, BlogComment $comment): JsonResponse
    {
        try {
            if ($comment->flagAsInappropriate(Auth::id())) {
                // Log the flag action
                $this->activityLogger->log(
                    'comment_flagged',
                    'BlogComment',
                    $comment->id,
                    [
                        'flagged_by' => Auth::user()->first_name . ' ' . Auth::user()->last_name,
                        'flag_count' => $comment->flag_count,
                        'auto_flagged' => $comment->is_flagged,
                    ]
                );

                return response()->json([
                    'success' => true,
                    'message' => 'Comment has been flagged for review.'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'You have already flagged this comment.'
            ], 422);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred.'
            ], 500);
        }
    }

    /**
     * Download attachment.
     */
    public function downloadAttachment(BlogComment $comment, string $filename): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        // Verify the file belongs to this comment
        if (!$comment->has_attachments || !in_array($filename, $comment->attachments)) {
            abort(404);
        }

        $filePath = storage_path('app/public/' . $filename);

        if (!file_exists($filePath)) {
            abort(404);
        }

        // Track download
        $this->visitorAnalytics->trackPageVisit('Comment Attachment Download', [
            'comment_id' => $comment->id,
            'filename' => basename($filename),
            'blog_post_id' => $comment->blog_post_id,
        ]);

        return response()->download($filePath);
    }
}
