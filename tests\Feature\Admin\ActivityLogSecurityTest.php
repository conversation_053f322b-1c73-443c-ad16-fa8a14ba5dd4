<?php

namespace Tests\Feature\Admin;

use App\Models\ActivityLog;
use App\Models\Role;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;

class ActivityLogSecurityTest extends TestCase
{
    use RefreshDatabase;

    protected $seed = true; // Use seeders to populate required data

    #[Test]
    public function unauthenticated_users_cannot_access_activity_logs()
    {
        $response = $this->get('/admin/activity-logs');

        $response->assertRedirect('/login');
    }

    #[Test]
    public function customers_cannot_access_activity_logs()
    {
        $customerRole = Role::where('name', 'customer')->first();
        $customer = User::factory()->create(['role_id' => $customerRole->id]);

        $response = $this->actingAs($customer)->get('/admin/activity-logs');
        
        $response->assertStatus(403);
    }

    #[Test]
    public function clients_cannot_access_activity_logs()
    {
        $clientRole = Role::where('name', 'client')->first();
        $client = User::factory()->create(['role_id' => $clientRole->id]);

        $response = $this->actingAs($client)->get('/admin/activity-logs');
        
        $response->assertStatus(403);
    }

    #[Test]
    public function admin_can_access_activity_logs()
    {
        $adminRole = Role::where('name', 'admin')->first();
        $admin = User::factory()->create(['role_id' => $adminRole->id]);

        $response = $this->actingAs($admin)->get('/admin/activity-logs');
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.activity-logs.index');
    }

    #[Test]
    public function staff_can_access_activity_logs()
    {
        $staffRole = Role::where('name', 'staff')->first();
        $staff = User::factory()->create(['role_id' => $staffRole->id]);

        $response = $this->actingAs($staff)->get('/admin/activity-logs');
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.activity-logs.index');
    }

    #[Test]
    public function unauthenticated_users_cannot_access_activity_log_details()
    {
        $log = ActivityLog::factory()->create();

        $response = $this->get("/admin/activity-logs/{$log->uuid}");
        
        $response->assertRedirect('/login');
    }

    #[Test]
    public function customers_cannot_access_activity_log_details()
    {
        $customerRole = Role::where('name', 'customer')->first();
        $customer = User::factory()->create(['role_id' => $customerRole->id]);
        $log = ActivityLog::factory()->create();

        $response = $this->actingAs($customer)->get("/admin/activity-logs/{$log->uuid}");
        
        $response->assertStatus(403);
    }

    #[Test]
    public function admin_can_access_activity_log_details()
    {
        $adminRole = Role::where('name', 'admin')->first();
        $admin = User::factory()->create(['role_id' => $adminRole->id]);
        $log = ActivityLog::factory()->create();

        $response = $this->actingAs($admin)->get("/admin/activity-logs/{$log->uuid}");
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.activity-logs.show');
    }

    #[Test]
    public function unauthenticated_users_cannot_access_activity_log_api_endpoints()
    {
        $response = $this->getJson('/admin/activity-logs-data');

        $response->assertStatus(401);
        $response->assertJson([
            'message' => 'Unauthenticated.',
        ]);
    }

    #[Test]
    public function customers_cannot_access_activity_log_api_endpoints()
    {
        $customerRole = Role::where('name', 'customer')->first();
        $customer = User::factory()->create(['role_id' => $customerRole->id]);

        $response = $this->actingAs($customer)->getJson('/admin/activity-logs-data');

        $response->assertStatus(403);
        $response->assertJson([
            'success' => false,
            'error' => [
                'code' => 'INSUFFICIENT_PERMISSIONS',
                'message' => 'You do not have permission to access this resource.',
            ]
        ]);
    }

    #[Test]
    public function admin_can_access_activity_log_api_endpoints()
    {
        $adminRole = Role::where('name', 'admin')->first();
        $admin = User::factory()->create(['role_id' => $adminRole->id]);

        $response = $this->actingAs($admin)->getJson('/admin/activity-logs-data');
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data',
            'pagination' => [
                'current_page',
                'last_page',
                'per_page',
                'total'
            ]
        ]);
    }

    #[Test]
    public function admin_can_access_activity_log_stats()
    {
        $adminRole = Role::where('name', 'admin')->first();
        $admin = User::factory()->create(['role_id' => $adminRole->id]);

        $response = $this->actingAs($admin)->getJson('/admin/activity-logs-stats');
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'stats' => [
                'total_logs',
                'password_reset_requests',
                'successful_resets',
                'failed_resets',
                'suspicious_activities',
                'today_logs',
                'this_week_logs',
                'high_risk_activities'
            ],
            'top_ips',
            'recent_suspicious',
            'activity_by_type'
        ]);
    }

    #[Test]
    public function inactive_admin_cannot_access_activity_logs()
    {
        $adminRole = Role::where('name', 'admin')->first();
        $admin = User::factory()->create([
            'role_id' => $adminRole->id,
            'is_active' => false
        ]);

        $response = $this->actingAs($admin)->getJson('/admin/activity-logs-data');

        $response->assertStatus(403);
        $response->assertJson([
            'success' => false,
            'error' => [
                'code' => 'ACCOUNT_INACTIVE',
                'message' => 'Your account is inactive.',
            ]
        ]);
    }

    #[Test]
    public function deleted_admin_cannot_access_activity_logs()
    {
        $adminRole = Role::where('name', 'admin')->first();
        $admin = User::factory()->create([
            'role_id' => $adminRole->id,
            'is_deleted' => true
        ]);

        $response = $this->actingAs($admin)->getJson('/admin/activity-logs-data');

        $response->assertStatus(403);
        $response->assertJson([
            'success' => false,
            'error' => [
                'code' => 'ACCOUNT_INACTIVE',
                'message' => 'Your account is inactive.',
            ]
        ]);
    }
}
