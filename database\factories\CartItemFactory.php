<?php

namespace Database\Factories;

use App\Models\CartItem;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\ShoppingCart;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CartItem>
 */
class CartItemFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CartItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $quantity = $this->faker->numberBetween(1, 5);
        $price = $this->faker->randomFloat(2, 10, 500);
        $total = $price * $quantity;

        return [
            'cart_id' => ShoppingCart::factory(),
            'product_id' => Product::factory(),
            'product_variant_id' => null,
            'quantity' => $quantity,
            'price' => $price,
            'total' => $total,
        ];
    }

    /**
     * Create a cart item for a specific cart.
     */
    public function forCart(ShoppingCart $cart): static
    {
        return $this->state(fn (array $attributes) => [
            'cart_id' => $cart->id,
        ]);
    }

    /**
     * Create a cart item for a specific product.
     */
    public function forProduct(Product $product): static
    {
        return $this->state(fn (array $attributes) => [
            'product_id' => $product->id,
            'price' => $product->price,
            'total' => $product->price * $attributes['quantity'],
        ]);
    }

    /**
     * Create a cart item for a specific product variant.
     */
    public function forVariant(ProductVariant $variant): static
    {
        return $this->state(fn (array $attributes) => [
            'product_id' => $variant->product_id,
            'product_variant_id' => $variant->id,
            'price' => $variant->price,
            'total' => $variant->price * $attributes['quantity'],
        ]);
    }

    /**
     * Create a cart item with specific quantity.
     */
    public function withQuantity(int $quantity): static
    {
        return $this->state(function (array $attributes) use ($quantity) {
            $total = $attributes['price'] * $quantity;
            
            return [
                'quantity' => $quantity,
                'total' => $total,
            ];
        });
    }

    /**
     * Create a cart item with specific price.
     */
    public function withPrice(float $price): static
    {
        return $this->state(function (array $attributes) use ($price) {
            $total = $price * $attributes['quantity'];
            
            return [
                'price' => $price,
                'total' => $total,
            ];
        });
    }
}
