<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\EmailCampaign;
use App\Models\EmailCampaignSend;
use App\Models\NewsletterSubscription;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class EmailAnalyticsController extends Controller
{
    /**
     * Display email analytics dashboard.
     */
    public function index(Request $request): View
    {
        $dateRange = $this->getDateRange($request);

        // Overall statistics
        $overallStats = $this->getOverallStatistics($dateRange);

        // Recent campaigns
        $recentCampaigns = EmailCampaign::with(['emailTemplate'])
            ->whereBetween('created_at', $dateRange)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Top performing campaigns
        $topCampaigns = EmailCampaign::whereBetween('created_at', $dateRange)
            ->where('emails_sent', '>', 0)
            ->orderByRaw('(emails_opened / emails_sent) DESC')
            ->limit(5)
            ->get();

        // Subscriber growth
        $subscriberGrowth = $this->getSubscriberGrowth($dateRange);

        return view('admin.email-analytics.index', compact(
            'overallStats',
            'recentCampaigns',
            'topCampaigns',
            'subscriberGrowth',
            'dateRange'
        ));
    }

    /**
     * Get campaign performance data for charts.
     */
    public function getCampaignPerformance(Request $request): JsonResponse
    {
        $dateRange = $this->getDateRange($request);
        $campaignId = $request->get('campaign_id');

        $query = EmailCampaignSend::selectRaw('
                DATE(sent_at) as date,
                COUNT(*) as sent,
                SUM(CASE WHEN delivered_at IS NOT NULL THEN 1 ELSE 0 END) as delivered,
                SUM(CASE WHEN bounced_at IS NOT NULL THEN 1 ELSE 0 END) as bounced,
                SUM(CASE WHEN opened_at IS NOT NULL THEN 1 ELSE 0 END) as opened,
                SUM(CASE WHEN first_clicked_at IS NOT NULL THEN 1 ELSE 0 END) as clicked,
                SUM(CASE WHEN unsubscribed_at IS NOT NULL THEN 1 ELSE 0 END) as unsubscribed
            ')
            ->whereNotNull('sent_at')
            ->whereBetween('sent_at', $dateRange);

        if ($campaignId) {
            $query->where('email_campaign_id', $campaignId);
        }

        $data = $query->groupBy('date')
                     ->orderBy('date')
                     ->get();

        return response()->json([
            'success' => true,
            'data' => $data,
        ]);
    }

    /**
     * Get engagement metrics over time.
     */
    public function getEngagementMetrics(Request $request): JsonResponse
    {
        $dateRange = $this->getDateRange($request);

        $data = EmailCampaignSend::selectRaw('
                DATE(sent_at) as date,
                COUNT(*) as total_sent,
                SUM(CASE WHEN opened_at IS NOT NULL THEN 1 ELSE 0 END) as total_opened,
                SUM(CASE WHEN first_clicked_at IS NOT NULL THEN 1 ELSE 0 END) as total_clicked,
                ROUND(AVG(CASE WHEN opened_at IS NOT NULL THEN 1 ELSE 0 END) * 100, 2) as open_rate,
                ROUND(AVG(CASE WHEN first_clicked_at IS NOT NULL THEN 1 ELSE 0 END) * 100, 2) as click_rate
            ')
            ->whereNotNull('sent_at')
            ->whereBetween('sent_at', $dateRange)
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $data,
        ]);
    }

    /**
     * Get subscriber engagement analysis.
     */
    public function getSubscriberEngagement(Request $request): JsonResponse
    {
        $dateRange = $this->getDateRange($request);

        // Engagement by lifecycle stage
        $lifecycleEngagement = NewsletterSubscription::selectRaw('
                lifecycle_stage,
                COUNT(*) as total_subscribers,
                AVG(engagement_score) as avg_engagement_score,
                SUM(CASE WHEN last_activity_at >= ? THEN 1 ELSE 0 END) as active_last_30_days
            ', [Carbon::now()->subDays(30)])
            ->where('is_active', true)
            ->groupBy('lifecycle_stage')
            ->get();

        // Top engaged subscribers
        $topSubscribers = NewsletterSubscription::select([
                'id', 'email', 'engagement_score', 'lifecycle_stage',
                'last_activity_at', 'total_emails_opened', 'total_emails_clicked'
            ])
            ->where('is_active', true)
            ->orderBy('engagement_score', 'desc')
            ->limit(10)
            ->get();

        // Engagement distribution
        $engagementDistribution = NewsletterSubscription::selectRaw('
                CASE
                    WHEN engagement_score >= 80 THEN "High (80-100)"
                    WHEN engagement_score >= 60 THEN "Medium (60-79)"
                    WHEN engagement_score >= 40 THEN "Low (40-59)"
                    ELSE "Very Low (0-39)"
                END as engagement_level,
                COUNT(*) as count
            ')
            ->where('is_active', true)
            ->groupBy('engagement_level')
            ->get();

        return response()->json([
            'success' => true,
            'lifecycle_engagement' => $lifecycleEngagement,
            'top_subscribers' => $topSubscribers,
            'engagement_distribution' => $engagementDistribution,
        ]);
    }

    /**
     * Get deliverability metrics.
     */
    public function getDeliverabilityMetrics(Request $request): JsonResponse
    {
        $dateRange = $this->getDateRange($request);

        $data = EmailCampaignSend::selectRaw('
                DATE(sent_at) as date,
                COUNT(*) as total_sent,
                SUM(CASE WHEN delivered_at IS NOT NULL THEN 1 ELSE 0 END) as delivered,
                SUM(CASE WHEN bounced_at IS NOT NULL THEN 1 ELSE 0 END) as bounced,
                SUM(CASE WHEN failed_at IS NOT NULL THEN 1 ELSE 0 END) as failed,
                ROUND((SUM(CASE WHEN delivered_at IS NOT NULL THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as delivery_rate,
                ROUND((SUM(CASE WHEN bounced_at IS NOT NULL THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as bounce_rate
            ')
            ->whereNotNull('sent_at')
            ->whereBetween('sent_at', $dateRange)
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Bounce reasons
        $bounceReasons = EmailCampaignSend::selectRaw('
                bounce_reason,
                COUNT(*) as count
            ')
            ->whereNotNull('bounced_at')
            ->whereBetween('sent_at', $dateRange)
            ->groupBy('bounce_reason')
            ->orderBy('count', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'deliverability_data' => $data,
            'bounce_reasons' => $bounceReasons,
        ]);
    }

    /**
     * Get campaign comparison data.
     */
    public function getCampaignComparison(Request $request): JsonResponse
    {
        $campaignIds = $request->get('campaign_ids', []);

        if (empty($campaignIds)) {
            return response()->json([
                'success' => false,
                'message' => 'No campaigns selected for comparison.',
            ], 400);
        }

        $campaigns = EmailCampaign::whereIn('id', $campaignIds)
            ->with(['emailTemplate'])
            ->get()
            ->map(function ($campaign) {
                $stats = $campaign->getStatistics();
                return [
                    'id' => $campaign->id,
                    'name' => $campaign->name,
                    'type' => $campaign->type_name,
                    'template' => $campaign->emailTemplate?->name,
                    'sent_at' => $campaign->started_at?->format('M j, Y'),
                    'recipients' => $campaign->emails_sent,
                    'open_rate' => $stats['open_rate'],
                    'click_rate' => $stats['click_rate'],
                    'bounce_rate' => $stats['bounce_rate'],
                    'unsubscribe_rate' => $stats['unsubscribe_rate'],
                ];
            });

        return response()->json([
            'success' => true,
            'campaigns' => $campaigns,
        ]);
    }

    /**
     * Export analytics data.
     */
    public function export(Request $request): \Symfony\Component\HttpFoundation\StreamedResponse
    {
        $dateRange = $this->getDateRange($request);
        $type = $request->get('type', 'campaigns');

        $filename = "email_analytics_{$type}_" . date('Y-m-d') . '.csv';

        return response()->streamDownload(function () use ($type, $dateRange) {
            $handle = fopen('php://output', 'w');

            switch ($type) {
                case 'campaigns':
                    $this->exportCampaigns($handle, $dateRange);
                    break;
                case 'subscribers':
                    $this->exportSubscribers($handle, $dateRange);
                    break;
                case 'engagement':
                    $this->exportEngagement($handle, $dateRange);
                    break;
                default:
                    $this->exportCampaigns($handle, $dateRange);
            }

            fclose($handle);
        }, $filename, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ]);
    }

    /**
     * Get overall statistics for the dashboard.
     */
    private function getOverallStatistics(array $dateRange): array
    {
        $campaignStats = EmailCampaign::selectRaw('
                COUNT(*) as total_campaigns,
                SUM(emails_sent) as total_sent,
                SUM(emails_delivered) as total_delivered,
                SUM(emails_opened) as total_opened,
                SUM(emails_clicked) as total_clicked,
                SUM(emails_bounced) as total_bounced,
                SUM(unsubscribes) as total_unsubscribes
            ')
            ->whereBetween('created_at', $dateRange)
            ->first();

        $subscriberStats = NewsletterSubscription::selectRaw('
                COUNT(*) as total_subscribers,
                SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_subscribers,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as new_subscribers
            ', $dateRange)
            ->first();

        return [
            'campaigns' => [
                'total' => $campaignStats->total_campaigns ?? 0,
                'sent' => $campaignStats->total_sent ?? 0,
                'delivered' => $campaignStats->total_delivered ?? 0,
                'opened' => $campaignStats->total_opened ?? 0,
                'clicked' => $campaignStats->total_clicked ?? 0,
                'bounced' => $campaignStats->total_bounced ?? 0,
                'unsubscribes' => $campaignStats->total_unsubscribes ?? 0,
            ],
            'subscribers' => [
                'total' => $subscriberStats->total_subscribers ?? 0,
                'active' => $subscriberStats->active_subscribers ?? 0,
                'new' => $subscriberStats->new_subscribers ?? 0,
            ],
            'rates' => [
                'delivery' => $campaignStats->total_sent > 0
                    ? round(($campaignStats->total_delivered / $campaignStats->total_sent) * 100, 2)
                    : 0,
                'open' => $campaignStats->total_sent > 0
                    ? round(($campaignStats->total_opened / $campaignStats->total_sent) * 100, 2)
                    : 0,
                'click' => $campaignStats->total_sent > 0
                    ? round(($campaignStats->total_clicked / $campaignStats->total_sent) * 100, 2)
                    : 0,
                'bounce' => $campaignStats->total_sent > 0
                    ? round(($campaignStats->total_bounced / $campaignStats->total_sent) * 100, 2)
                    : 0,
                'unsubscribe' => $campaignStats->total_sent > 0
                    ? round(($campaignStats->total_unsubscribes / $campaignStats->total_sent) * 100, 2)
                    : 0,
            ],
        ];
    }

    /**
     * Get subscriber growth data.
     */
    private function getSubscriberGrowth(array $dateRange): array
    {
        $growth = NewsletterSubscription::selectRaw('
                DATE(created_at) as date,
                COUNT(*) as new_subscribers,
                SUM(COUNT(*)) OVER (ORDER BY DATE(created_at)) as cumulative_subscribers
            ')
            ->whereBetween('created_at', $dateRange)
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $unsubscribes = NewsletterSubscription::selectRaw('
                DATE(unsubscribed_at) as date,
                COUNT(*) as unsubscribes
            ')
            ->whereNotNull('unsubscribed_at')
            ->whereBetween('unsubscribed_at', $dateRange)
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        return $growth->map(function ($item) use ($unsubscribes) {
            $unsubscribeCount = $unsubscribes->get($item->date)?->unsubscribes ?? 0;
            return [
                'date' => $item->date,
                'new_subscribers' => $item->new_subscribers,
                'unsubscribes' => $unsubscribeCount,
                'net_growth' => $item->new_subscribers - $unsubscribeCount,
                'cumulative_subscribers' => $item->cumulative_subscribers,
            ];
        })->toArray();
    }

    /**
     * Get date range from request.
     */
    private function getDateRange(Request $request): array
    {
        $from = $request->get('date_from', Carbon::now()->subDays(30)->format('Y-m-d'));
        $to = $request->get('date_to', Carbon::now()->format('Y-m-d'));

        return [
            Carbon::parse($from)->startOfDay(),
            Carbon::parse($to)->endOfDay(),
        ];
    }

    /**
     * Export campaign data to CSV.
     */
    private function exportCampaigns($handle, array $dateRange): void
    {
        // CSV headers
        fputcsv($handle, [
            'Campaign Name', 'Type', 'Status', 'Template', 'Created Date', 'Sent Date',
            'Recipients', 'Delivered', 'Opened', 'Clicked', 'Bounced', 'Unsubscribed',
            'Delivery Rate %', 'Open Rate %', 'Click Rate %', 'Bounce Rate %', 'Unsubscribe Rate %'
        ]);

        // Get campaigns data
        EmailCampaign::with(['emailTemplate'])
            ->whereBetween('created_at', $dateRange)
            ->chunk(100, function ($campaigns) use ($handle) {
                foreach ($campaigns as $campaign) {
                    $stats = $campaign->getStatistics();

                    fputcsv($handle, [
                        $campaign->name,
                        $campaign->type_name,
                        $campaign->status_name,
                        $campaign->emailTemplate?->name ?? 'N/A',
                        $campaign->created_at->format('Y-m-d H:i:s'),
                        $campaign->started_at?->format('Y-m-d H:i:s') ?? 'N/A',
                        $campaign->emails_sent,
                        $campaign->emails_delivered,
                        $campaign->emails_opened,
                        $campaign->emails_clicked,
                        $campaign->emails_bounced,
                        $campaign->unsubscribes,
                        $stats['delivery_rate'],
                        $stats['open_rate'],
                        $stats['click_rate'],
                        $stats['bounce_rate'],
                        $stats['unsubscribe_rate'],
                    ]);
                }
            });
    }

    /**
     * Export subscriber data to CSV.
     */
    private function exportSubscribers($handle, array $dateRange): void
    {
        // CSV headers
        fputcsv($handle, [
            'Email', 'Status', 'Lifecycle Stage', 'Engagement Score', 'Total Opens', 'Total Clicks',
            'Last Activity', 'Subscribed Date', 'Unsubscribed Date', 'Source'
        ]);

        // Get subscribers data
        NewsletterSubscription::whereBetween('created_at', $dateRange)
            ->chunk(100, function ($subscribers) use ($handle) {
                foreach ($subscribers as $subscriber) {
                    fputcsv($handle, [
                        $subscriber->email,
                        $subscriber->is_active ? 'Active' : 'Inactive',
                        $subscriber->lifecycle_stage,
                        $subscriber->engagement_score,
                        $subscriber->total_emails_opened,
                        $subscriber->total_emails_clicked,
                        $subscriber->last_activity_at?->format('Y-m-d H:i:s') ?? 'N/A',
                        $subscriber->created_at->format('Y-m-d H:i:s'),
                        $subscriber->unsubscribed_at?->format('Y-m-d H:i:s') ?? 'N/A',
                        $subscriber->subscription_source ?? 'N/A',
                    ]);
                }
            });
    }

    /**
     * Export engagement data to CSV.
     */
    private function exportEngagement($handle, array $dateRange): void
    {
        // CSV headers
        fputcsv($handle, [
            'Date', 'Campaign', 'Subscriber Email', 'Action', 'Details', 'IP Address', 'User Agent'
        ]);

        // Get engagement data
        EmailCampaignSend::with(['emailCampaign', 'newsletterSubscription'])
            ->whereBetween('sent_at', $dateRange)
            ->where(function ($query) {
                $query->whereNotNull('opened_at')
                      ->orWhereNotNull('first_clicked_at')
                      ->orWhereNotNull('unsubscribed_at');
            })
            ->chunk(100, function ($sends) use ($handle) {
                foreach ($sends as $send) {
                    if ($send->opened_at) {
                        fputcsv($handle, [
                            $send->opened_at->format('Y-m-d H:i:s'),
                            $send->emailCampaign->name,
                            $send->newsletterSubscription->email,
                            'Opened',
                            'Email opened',
                            $send->open_ip ?? 'N/A',
                            $send->open_user_agent ?? 'N/A',
                        ]);
                    }

                    if ($send->first_clicked_at) {
                        fputcsv($handle, [
                            $send->first_clicked_at->format('Y-m-d H:i:s'),
                            $send->emailCampaign->name,
                            $send->newsletterSubscription->email,
                            'Clicked',
                            $send->clicked_urls ?? 'N/A',
                            $send->click_ip ?? 'N/A',
                            $send->click_user_agent ?? 'N/A',
                        ]);
                    }

                    if ($send->unsubscribed_at) {
                        fputcsv($handle, [
                            $send->unsubscribed_at->format('Y-m-d H:i:s'),
                            $send->emailCampaign->name,
                            $send->newsletterSubscription->email,
                            'Unsubscribed',
                            'Unsubscribed from emails',
                            'N/A',
                            'N/A',
                        ]);
                    }
                }
            });
    }
}
