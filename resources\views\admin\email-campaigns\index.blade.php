@extends('layouts.dashboard')

@section('title', 'Email Campaigns')

@section('content')
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Email Campaigns</h1>
            <p class="mt-1 text-sm text-gray-600">Manage your email marketing campaigns and automation</p>
        </div>
        <div class="mt-4 sm:mt-0 flex items-center space-x-3">
            <a href="{{ route('admin.email-templates.index') }}" 
               class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                </svg>
                Templates
            </a>
            <a href="{{ route('admin.email-campaigns.create') }}" 
               class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
                Create Campaign
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <form method="GET" action="{{ route('admin.email-campaigns.index') }}" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    <input type="text" 
                           id="search" 
                           name="search" 
                           value="{{ request('search') }}"
                           placeholder="Search campaigns..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                </div>

                <!-- Type Filter -->
                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Type</label>
                    <select id="type" 
                            name="type" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">All Types</option>
                        @foreach(\App\Models\EmailCampaign::getTypes() as $key => $label)
                            <option value="{{ $key }}" {{ request('type') === $key ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select id="status" 
                            name="status" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">All Statuses</option>
                        @foreach(\App\Models\EmailCampaign::getStatuses() as $key => $label)
                            <option value="{{ $key }}" {{ request('status') === $key ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Template Filter -->
                <div>
                    <label for="template" class="block text-sm font-medium text-gray-700 mb-1">Template</label>
                    <select id="template" 
                            name="template" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">All Templates</option>
                        @foreach($templates as $template)
                            <option value="{{ $template->id }}" {{ request('template') == $template->id ? 'selected' : '' }}>
                                {{ $template->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Date Range -->
                <div>
                    <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
                    <div class="flex space-x-2">
                        <input type="date" 
                               id="date_from" 
                               name="date_from" 
                               value="{{ request('date_from') }}"
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <input type="date" 
                               id="date_to" 
                               name="date_to" 
                               value="{{ request('date_to') }}"
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    </div>
                </div>
            </div>

            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div class="flex items-center space-x-2">
                    <button type="submit" 
                            class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                        Filter
                    </button>
                    <a href="{{ route('admin.email-campaigns.index') }}" 
                       class="inline-flex items-center px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors duration-200">
                        Clear
                    </a>
                </div>

                <!-- Bulk Actions -->
                <div class="mt-4 sm:mt-0 flex items-center space-x-2">
                    <select id="bulk-action" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">Bulk Actions</option>
                        <option value="pause">Pause</option>
                        <option value="resume">Resume</option>
                        <option value="cancel">Cancel</option>
                        <option value="delete">Delete</option>
                    </select>
                    <button type="button" 
                            id="apply-bulk-action"
                            class="inline-flex items-center px-4 py-2 bg-secondary-600 text-white rounded-lg hover:bg-secondary-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                            disabled>
                        Apply
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Campaigns List -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        @if($campaigns->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left">
                                <input type="checkbox" id="select-all" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Campaign</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recipients</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Schedule</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($campaigns as $campaign)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4">
                                    <input type="checkbox" 
                                           class="campaign-checkbox rounded border-gray-300 text-primary-600 focus:ring-primary-500" 
                                           value="{{ $campaign->id }}">
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">
                                                <a href="{{ route('admin.email-campaigns.show', $campaign) }}" class="hover:text-primary-600">
                                                    {{ $campaign->name }}
                                                </a>
                                            </div>
                                            @if($campaign->description)
                                                <div class="text-sm text-gray-500 truncate max-w-xs">{{ $campaign->description }}</div>
                                            @endif
                                            @if($campaign->emailTemplate)
                                                <div class="text-xs text-blue-600">{{ $campaign->emailTemplate->name }}</div>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ $campaign->type_name }}
                                    </span>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $campaign->status_badge_color }}">
                                        {{ $campaign->status_name }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-900">
                                    <div>{{ number_format($campaign->total_recipients) }} targeted</div>
                                    @if($campaign->emails_sent > 0)
                                        <div class="text-xs text-gray-500">{{ number_format($campaign->emails_sent) }} sent</div>
                                    @endif
                                </td>
                                <td class="px-6 py-4">
                                    @if($campaign->emails_sent > 0)
                                        <div class="text-sm text-gray-900">
                                            <div>{{ $campaign->getOpenRate() }}% opens</div>
                                            <div class="text-xs text-gray-500">{{ $campaign->getClickRate() }}% clicks</div>
                                        </div>
                                    @else
                                        <span class="text-sm text-gray-400">No data</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-900">
                                    @if($campaign->scheduled_at)
                                        <div>{{ $campaign->scheduled_at->format('M j, Y') }}</div>
                                        <div class="text-xs text-gray-500">{{ $campaign->scheduled_at->format('g:i A') }}</div>
                                    @elseif($campaign->started_at)
                                        <div>Started</div>
                                        <div class="text-xs text-gray-500">{{ $campaign->started_at->format('M j, Y g:i A') }}</div>
                                    @else
                                        <span class="text-gray-400">Not scheduled</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center space-x-2">
                                        <!-- Quick Actions based on status -->
                                        @if($campaign->status === 'draft')
                                            <button type="button" 
                                                    class="schedule-btn text-sm text-blue-600 hover:text-blue-700"
                                                    data-campaign-id="{{ $campaign->id }}">
                                                Schedule
                                            </button>
                                            <button type="button" 
                                                    class="send-now-btn text-sm text-green-600 hover:text-green-700"
                                                    data-campaign-id="{{ $campaign->id }}">
                                                Send Now
                                            </button>
                                        @elseif($campaign->status === 'scheduled')
                                            <button type="button" 
                                                    class="send-now-btn text-sm text-green-600 hover:text-green-700"
                                                    data-campaign-id="{{ $campaign->id }}">
                                                Send Now
                                            </button>
                                            <button type="button" 
                                                    class="cancel-btn text-sm text-red-600 hover:text-red-700"
                                                    data-campaign-id="{{ $campaign->id }}">
                                                Cancel
                                            </button>
                                        @elseif($campaign->status === 'sending')
                                            <button type="button" 
                                                    class="pause-btn text-sm text-yellow-600 hover:text-yellow-700"
                                                    data-campaign-id="{{ $campaign->id }}">
                                                Pause
                                            </button>
                                        @elseif($campaign->status === 'paused')
                                            <button type="button" 
                                                    class="resume-btn text-sm text-green-600 hover:text-green-700"
                                                    data-campaign-id="{{ $campaign->id }}">
                                                Resume
                                            </button>
                                            <button type="button" 
                                                    class="cancel-btn text-sm text-red-600 hover:text-red-700"
                                                    data-campaign-id="{{ $campaign->id }}">
                                                Cancel
                                            </button>
                                        @endif

                                        <!-- Dropdown Menu -->
                                        <div class="relative inline-block text-left">
                                            <button type="button" 
                                                    class="dropdown-toggle p-1 text-gray-400 hover:text-gray-600"
                                                    data-campaign-id="{{ $campaign->id }}">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"/>
                                                </svg>
                                            </button>
                                            <div class="dropdown-menu hidden absolute right-0 z-10 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200">
                                                <div class="py-1">
                                                    <a href="{{ route('admin.email-campaigns.show', $campaign) }}" 
                                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">View Details</a>
                                                    @if($campaign->canBeEdited())
                                                        <a href="{{ route('admin.email-campaigns.edit', $campaign) }}" 
                                                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Edit</a>
                                                    @endif
                                                    <a href="{{ route('admin.email-campaigns.preview', $campaign) }}" 
                                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" target="_blank">Preview</a>
                                                    <button type="button" 
                                                            class="duplicate-btn w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                            data-campaign-id="{{ $campaign->id }}">
                                                        Duplicate
                                                    </button>
                                                    @if($campaign->type === 'drip')
                                                        <a href="{{ route('admin.email-campaigns.create-drip-email', $campaign) }}" 
                                                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Add Sequence Email</a>
                                                    @endif
                                                    @if($campaign->canBeDeleted())
                                                        <button type="button" 
                                                                class="delete-btn w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                                                                data-campaign-id="{{ $campaign->id }}">
                                                            Delete
                                                        </button>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No email campaigns</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by creating your first email campaign.</p>
                <div class="mt-6">
                    <a href="{{ route('admin.email-campaigns.create') }}" 
                       class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        Create Campaign
                    </a>
                </div>
            </div>
        @endif
    </div>

    <!-- Pagination -->
    @if($campaigns->hasPages())
        <div class="flex justify-center">
            {{ $campaigns->links() }}
        </div>
    @endif
</div>

<!-- Schedule Modal -->
<div id="schedule-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-lg bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Schedule Campaign</h3>
            <form id="schedule-form">
                <div class="mb-4">
                    <label for="scheduled_at" class="block text-sm font-medium text-gray-700 mb-2">Schedule Date & Time</label>
                    <input type="datetime-local" 
                           id="scheduled_at" 
                           name="scheduled_at" 
                           required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" 
                            id="cancel-schedule"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors duration-200">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200">
                        Schedule
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Bulk action functionality
    const checkboxes = document.querySelectorAll('.campaign-checkbox');
    const selectAllCheckbox = document.getElementById('select-all');
    const bulkActionSelect = document.getElementById('bulk-action');
    const applyBulkActionBtn = document.getElementById('apply-bulk-action');

    function updateBulkActionButton() {
        const checkedBoxes = document.querySelectorAll('.campaign-checkbox:checked');
        if (applyBulkActionBtn) {
            applyBulkActionBtn.disabled = checkedBoxes.length === 0;
        }
    }

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActionButton();
        });
    }

    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActionButton);
    });

    if (applyBulkActionBtn) {
        applyBulkActionBtn.addEventListener('click', function() {
        const action = bulkActionSelect.value;
        const checkedBoxes = document.querySelectorAll('.campaign-checkbox:checked');
        const campaignIds = Array.from(checkedBoxes).map(cb => cb.value);

        if (!action || campaignIds.length === 0) {
            return;
        }

        if ((action === 'delete' || action === 'cancel') && !confirm(`Are you sure you want to ${action} the selected campaigns?`)) {
            return;
        }

        // Perform bulk action via AJAX
        fetch('{{ route("admin.email-campaigns.bulk-action") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                action: action,
                campaign_ids: campaignIds
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'An error occurred');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
        });
    }

    // Schedule modal functionality
    const scheduleModal = document.getElementById('schedule-modal');
    const scheduleForm = document.getElementById('schedule-form');
    const cancelScheduleBtn = document.getElementById('cancel-schedule');
    let currentCampaignId = null;

    document.querySelectorAll('.schedule-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            currentCampaignId = this.dataset.campaignId;
            scheduleModal.classList.remove('hidden');
        });
    });

    cancelScheduleBtn.addEventListener('click', function() {
        scheduleModal.classList.add('hidden');
        currentCampaignId = null;
    });

    scheduleForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!currentCampaignId) return;

        const formData = new FormData(this);
        
        fetch(`/admin/email-campaigns/${currentCampaignId}/schedule`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                scheduleModal.classList.add('hidden');
                location.reload();
            } else {
                alert(data.message || 'An error occurred');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    });

    // Individual action handlers
    document.querySelectorAll('.send-now-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            if (!confirm('Are you sure you want to send this campaign now?')) {
                return;
            }

            const campaignId = this.dataset.campaignId;
            
            fetch(`/admin/email-campaigns/${campaignId}/send-now`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || 'An error occurred');
                }
            });
        });
    });

    document.querySelectorAll('.pause-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const campaignId = this.dataset.campaignId;
            
            fetch(`/admin/email-campaigns/${campaignId}/pause`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || 'An error occurred');
                }
            });
        });
    });

    document.querySelectorAll('.resume-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const campaignId = this.dataset.campaignId;
            
            fetch(`/admin/email-campaigns/${campaignId}/resume`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || 'An error occurred');
                }
            });
        });
    });

    document.querySelectorAll('.cancel-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            if (!confirm('Are you sure you want to cancel this campaign?')) {
                return;
            }

            const campaignId = this.dataset.campaignId;
            
            fetch(`/admin/email-campaigns/${campaignId}/cancel`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || 'An error occurred');
                }
            });
        });
    });

    document.querySelectorAll('.duplicate-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const campaignId = this.dataset.campaignId;
            
            fetch(`/admin/email-campaigns/${campaignId}/duplicate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = data.redirect;
                } else {
                    alert(data.message || 'An error occurred');
                }
            });
        });
    });

    document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            if (!confirm('Are you sure you want to delete this campaign?')) {
                return;
            }

            const campaignId = this.dataset.campaignId;
            
            fetch(`/admin/email-campaigns/${campaignId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || 'An error occurred');
                }
            });
        });
    });

    // Dropdown functionality
    document.querySelectorAll('.dropdown-toggle').forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.stopPropagation();
            const dropdown = this.nextElementSibling;
            
            // Close all other dropdowns
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (menu !== dropdown) {
                    menu.classList.add('hidden');
                }
            });
            
            dropdown.classList.toggle('hidden');
        });
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function() {
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            menu.classList.add('hidden');
        });
    });
});
</script>
@endpush
@endsection
