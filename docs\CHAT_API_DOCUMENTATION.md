# ChiSolution Chat API Documentation

## Overview

The ChiSolution Chat API provides a comprehensive RESTful interface for integrating live chat functionality into any application. This API supports real-time messaging, AI-powered responses, file uploads, staff management, and advanced analytics.

## Base URL

```
https://your-domain.com/api/v1/chat
```

## Authentication

The API supports multiple authentication methods:

### 1. API Key Authentication
```http
X-API-Key: your-api-key-here
```

### 2. Bearer Token Authentication
```http
Authorization: Bearer your-token-here
```

### 3. Session Authentication
For web applications using Laravel sessions.

## Rate Limiting

The API implements rate limiting to ensure fair usage:

- **Room Creation**: 10 requests per hour
- **Message Sending**: 60 requests per minute
- **File Uploads**: 10 requests per minute
- **Real-time Actions**: 30 requests per minute

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 45
X-RateLimit-Reset: 1642248000
```

## Response Format

All API responses follow a consistent format:

### Success Response
```json
{
    "success": true,
    "data": {
        // Response data
    },
    "message": "Operation completed successfully",
    "meta": {
        "timestamp": "2024-01-15T10:30:00Z",
        "request_id": "req_123456789"
    }
}
```

### Error Response
```json
{
    "success": false,
    "data": null,
    "message": "Error description",
    "errors": [
        {
            "field": "field_name",
            "code": "validation_error",
            "message": "Detailed error message"
        }
    ],
    "meta": {
        "timestamp": "2024-01-15T10:30:00Z",
        "request_id": "req_123456789"
    }
}
```

## Endpoints

### Chat Rooms

#### Create Chat Room
```http
POST /rooms
Content-Type: application/json

{
    "type": "visitor|support|sales",
    "title": "Optional room title",
    "visitor_info": {
        "name": "John Doe",
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "page": "https://example.com/product/123"
    },
    "metadata": {
        "product_id": 123,
        "order_id": 456
    },
    "priority": 1,
    "language": "en"
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "room": {
            "id": 123,
            "uuid": "550e8400-e29b-41d4-a716-************",
            "type": "visitor",
            "status": "active",
            "priority": 1,
            "language": "en",
            "created_at": "2024-01-15T10:30:00Z",
            "participants": [],
            "current_assignment": null
        }
    },
    "message": "Chat room created successfully"
}
```

#### Get Chat Room
```http
GET /rooms/{uuid}
```

#### Update Chat Room
```http
PUT /rooms/{uuid}
Content-Type: application/json

{
    "title": "Updated title",
    "priority": 2,
    "metadata": {
        "updated_field": "value"
    }
}
```

#### Delete Chat Room
```http
DELETE /rooms/{uuid}
```

### Messages

#### Send Message
```http
POST /rooms/{uuid}/messages
Content-Type: application/json

{
    "content": "Hello, I need help with my order",
    "message_type": "text|file|image|system",
    "metadata": {
        "order_id": 123
    },
    "reply_to_message_id": null
}
```

#### Get Messages
```http
GET /rooms/{uuid}/messages?page=1&per_page=50&since=2024-01-15T10:00:00Z
```

#### Mark Message as Read
```http
POST /rooms/{roomUuid}/messages/{messageUuid}/read
```

### Real-time Features

#### Join Room
```http
POST /rooms/{uuid}/join
```

#### Leave Room
```http
POST /rooms/{uuid}/leave
```

#### Send Typing Indicator
```http
POST /rooms/{uuid}/typing
Content-Type: application/json

{
    "is_typing": true
}
```

#### Get Room Status
```http
GET /rooms/{uuid}/status
```

### File Management

#### Upload File
```http
POST /rooms/{uuid}/files
Content-Type: multipart/form-data

file: [binary file data]
message_content: "Here's the document you requested"
```

### Staff Management

#### Get Available Staff
```http
GET /staff/availability
```

#### Assign Staff to Room
```http
POST /rooms/{uuid}/assign
Content-Type: application/json

{
    "staff_id": 456
}
```

#### Transfer Room
```http
POST /rooms/{uuid}/transfer
Content-Type: application/json

{
    "new_staff_id": 789,
    "reason": "Specialist required"
}
```

### AI Integration

#### Generate AI Response
```http
POST /ai/generate-response
Content-Type: application/json

{
    "chat_room_id": "550e8400-e29b-41d4-a716-************",
    "user_message": "What are your business hours?",
    "context": {
        "conversation_history": [],
        "user_language": "en",
        "intent": "business_hours"
    }
}
```

### Analytics

#### Get Statistics
```http
GET /statistics?start_date=2024-01-01&end_date=2024-01-31
```

#### Get Analytics
```http
GET /analytics?metric=response_time&period=7d&staff_id=123
```

### Chat Ratings

#### Submit Rating
```http
POST /rooms/{uuid}/rating
Content-Type: application/json

{
    "rating": 5,
    "feedback": "Excellent support!",
    "rating_categories": {
        "response_time": 5,
        "helpfulness": 5,
        "professionalism": 5
    }
}
```

## WebSocket Integration

For real-time updates, connect to the WebSocket endpoint:

```
wss://your-domain.com/ws/chat/{room_uuid}
```

### WebSocket Events

#### Message Sent
```json
{
    "type": "message",
    "data": {
        "message": {
            "id": 123,
            "content": "Hello!",
            "user": {
                "name": "John Doe"
            },
            "created_at": "2024-01-15T10:30:00Z"
        }
    }
}
```

#### User Typing
```json
{
    "type": "typing",
    "data": {
        "user_id": 456,
        "user_name": "Support Agent",
        "is_typing": true
    }
}
```

#### Room Status Update
```json
{
    "type": "room_status",
    "data": {
        "status": "assigned",
        "assigned_staff": {
            "id": 789,
            "name": "Jane Smith"
        }
    }
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid request data |
| 401 | Unauthorized - Invalid or missing authentication |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 422 | Unprocessable Entity - Validation errors |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server error |
| 503 | Service Unavailable - Chat system disabled |

## SDK Usage Examples

### JavaScript SDK
```javascript
import ChiChatSDK from './chat-sdk.js';

const chat = new ChiChatSDK({
    baseUrl: 'https://api.example.com/v1/chat',
    apiKey: 'your-api-key'
});

// Create room and send message
const room = await chat.createRoom({
    type: 'visitor',
    visitor_info: { name: 'John Doe' }
});

await chat.sendMessage(room.data.room.uuid, {
    content: 'Hello!',
    message_type: 'text'
});

// Connect WebSocket for real-time updates
chat.connectWebSocket(room.data.room.uuid);
chat.on('websocket:message', (data) => {
    console.log('New message:', data.message);
});
```

### PHP SDK
```php
use App\SDK\ChiChatSDK;

$chat = new ChiChatSDK([
    'base_url' => 'https://api.example.com/v1/chat',
    'api_key' => 'your-api-key'
]);

// Create room and send message
$room = $chat->createRoom([
    'type' => 'visitor',
    'visitor_info' => ['name' => 'John Doe']
]);

$chat->sendMessage($room['data']['room']['uuid'], [
    'content' => 'Hello!',
    'message_type' => 'text'
]);

// Upload file
$chat->uploadFile(
    $room['data']['room']['uuid'],
    '/path/to/file.pdf',
    'Here is the document'
);
```

## Webhook Integration

Configure webhooks to receive real-time notifications about chat events:

### Webhook Events
- `message.sent` - New message sent
- `room.created` - Chat room created
- `room.closed` - Chat room closed
- `room.assigned` - Staff assigned to room
- `file.uploaded` - File uploaded
- `rating.submitted` - Rating submitted

### Webhook Payload
```json
{
    "event": "message.sent",
    "timestamp": "2024-01-15T10:30:00Z",
    "data": {
        "room_id": "550e8400-e29b-41d4-a716-************",
        "message": {
            "id": 123,
            "content": "Hello!",
            "sender": {
                "type": "customer",
                "name": "John Doe"
            }
        }
    },
    "signature": "sha256=abc123..."
}
```

## Best Practices

1. **Always handle errors gracefully** - Check response status and handle error cases
2. **Implement retry logic** - For network failures and temporary errors
3. **Use WebSockets for real-time features** - Don't poll for new messages
4. **Respect rate limits** - Implement exponential backoff for rate-limited requests
5. **Validate webhook signatures** - Verify webhook authenticity using HMAC
6. **Cache frequently accessed data** - Reduce API calls where possible
7. **Use appropriate authentication** - API keys for server-to-server, tokens for user sessions

## Support

For API support and questions:
- Documentation: https://docs.chisolution.com/chat-api
- Support Email: <EMAIL>
- GitHub Issues: https://github.com/chisolution/chat-api/issues
