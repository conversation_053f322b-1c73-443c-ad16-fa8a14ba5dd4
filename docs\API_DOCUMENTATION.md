# Live Chat AI System - API Documentation

## Overview

The Live Chat AI System provides a comprehensive RESTful API for managing real-time chat conversations with AI chatbot integration. This documentation covers all available endpoints, authentication methods, and usage examples.

## Base URL

```
Production: https://chisolution.io/api
Development: http://localhost:8000/api
```

## Authentication

The API uses Laravel Sanctum for authentication. Include the Bearer token in the Authorization header:

```
Authorization: Bearer {your-token}
```

## API Versioning

All API endpoints are versioned. Current version: `v1`

```
/api/v1/{endpoint}
```

## Rate Limiting

- **General API**: 60 requests per minute per user
- **Message Sending**: 60 messages per minute per user
- **AI Requests**: 30 requests per minute per user
- **File Uploads**: 10 uploads per 5 minutes per user

## Response Format

All API responses follow this structure:

```json
{
    "success": true|false,
    "data": {},
    "message": "Success message",
    "errors": {},
    "meta": {
        "pagination": {},
        "filters": {}
    }
}
```

## Chat Rooms API

### List Chat Rooms

```http
GET /api/v1/chat/rooms
```

**Parameters:**
- `status` (optional): Filter by status (active, closed, pending)
- `user_id` (optional): Filter by user ID
- `page` (optional): Page number for pagination
- `per_page` (optional): Items per page (default: 15)

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "user_id": 123,
            "status": "active",
            "created_at": "2024-01-01T10:00:00Z",
            "updated_at": "2024-01-01T10:30:00Z",
            "user": {
                "id": 123,
                "first_name": "John",
                "last_name": "Doe"
            },
            "messages_count": 15,
            "last_message": {
                "id": 456,
                "message": "Thank you for your help!",
                "created_at": "2024-01-01T10:30:00Z"
            }
        }
    ],
    "meta": {
        "pagination": {
            "current_page": 1,
            "total_pages": 5,
            "total_items": 75
        }
    }
}
```

### Create Chat Room

```http
POST /api/v1/chat/rooms
```

**Request Body:**
```json
{
    "user_id": 123,
    "initial_message": "Hello, I need help with my order"
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "id": 1,
        "user_id": 123,
        "status": "pending",
        "created_at": "2024-01-01T10:00:00Z"
    },
    "message": "Chat room created successfully"
}
```

### Get Chat Room Details

```http
GET /api/v1/chat/rooms/{id}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "id": 1,
        "user_id": 123,
        "status": "active",
        "created_at": "2024-01-01T10:00:00Z",
        "user": {
            "id": 123,
            "first_name": "John",
            "last_name": "Doe"
        },
        "assignments": [
            {
                "id": 1,
                "assigned_to": 456,
                "assigned_at": "2024-01-01T10:05:00Z",
                "staff": {
                    "id": 456,
                    "first_name": "Jane",
                    "last_name": "Smith"
                }
            }
        ],
        "messages": [
            {
                "id": 1,
                "message": "Hello, I need help",
                "sender_type": "user",
                "sender_id": 123,
                "created_at": "2024-01-01T10:00:00Z"
            }
        ]
    }
}
```

## Messages API

### Send Message

```http
POST /api/v1/chat/rooms/{room_id}/messages
```

**Request Body:**
```json
{
    "message": "Hello, how can I help you?",
    "sender_type": "user|staff|ai",
    "attachments": [
        {
            "type": "image",
            "url": "https://example.com/image.jpg",
            "filename": "screenshot.jpg"
        }
    ]
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "id": 123,
        "chat_room_id": 1,
        "message": "Hello, how can I help you?",
        "sender_type": "staff",
        "sender_id": 456,
        "created_at": "2024-01-01T10:15:00Z",
        "attachments": []
    },
    "message": "Message sent successfully"
}
```

### Get Messages

```http
GET /api/v1/chat/rooms/{room_id}/messages
```

**Parameters:**
- `page` (optional): Page number
- `per_page` (optional): Messages per page (default: 50)
- `since` (optional): Get messages since timestamp

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "id": 123,
            "message": "Hello!",
            "sender_type": "user",
            "sender_id": 123,
            "created_at": "2024-01-01T10:00:00Z",
            "read_at": null,
            "attachments": []
        }
    ]
}
```

### Mark Message as Read

```http
POST /api/v1/chat/messages/{id}/read
```

**Response:**
```json
{
    "success": true,
    "message": "Message marked as read"
}
```

## AI Chatbot API

### Get AI Response

```http
POST /api/v1/chat/ai/response
```

**Request Body:**
```json
{
    "chat_room_id": 1,
    "message": "What are your business hours?",
    "context": {
        "user_language": "en",
        "conversation_history": []
    }
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "response": "Our business hours are Monday to Friday, 9 AM to 6 PM EST.",
        "confidence": 0.95,
        "source": "faq",
        "escalate_to_human": false,
        "suggested_actions": [
            "show_contact_info",
            "schedule_callback"
        ]
    }
}
```

### Update AI Configuration

```http
PUT /api/v1/chat/ai/config
```

**Request Body:**
```json
{
    "enabled": true,
    "auto_response": true,
    "escalation_threshold": 0.7,
    "response_templates": {
        "greeting": "Hello! How can I help you today?",
        "goodbye": "Thank you for contacting us!"
    }
}
```

## Staff Assignment API

### Assign Staff to Chat

```http
POST /api/v1/chat/rooms/{room_id}/assign
```

**Request Body:**
```json
{
    "staff_id": 456,
    "notes": "Customer needs help with billing"
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "id": 1,
        "chat_room_id": 1,
        "assigned_to": 456,
        "assigned_by": 789,
        "assigned_at": "2024-01-01T10:05:00Z",
        "notes": "Customer needs help with billing"
    }
}
```

### Transfer Chat

```http
POST /api/v1/chat/rooms/{room_id}/transfer
```

**Request Body:**
```json
{
    "from_staff_id": 456,
    "to_staff_id": 789,
    "reason": "Specialist required for technical issue"
}
```

## Analytics API

### Get Chat Analytics

```http
GET /api/v1/chat/analytics
```

**Parameters:**
- `start_date` (optional): Start date (YYYY-MM-DD)
- `end_date` (optional): End date (YYYY-MM-DD)
- `staff_id` (optional): Filter by staff member
- `department` (optional): Filter by department

**Response:**
```json
{
    "success": true,
    "data": {
        "overview": {
            "total_conversations": 150,
            "active_conversations": 12,
            "average_response_time": 2.5,
            "customer_satisfaction": 4.2
        },
        "trends": {
            "daily_conversations": [
                {"date": "2024-01-01", "count": 25},
                {"date": "2024-01-02", "count": 30}
            ]
        }
    }
}
```

### Get AI Performance Metrics

```http
GET /api/v1/chat/analytics/ai-performance
```

**Response:**
```json
{
    "success": true,
    "data": {
        "total_ai_interactions": 500,
        "successful_resolutions": 350,
        "escalations_to_human": 150,
        "average_response_time": 1.2,
        "accuracy_score": 0.85,
        "user_satisfaction": 4.1
    }
}
```

## Customer Satisfaction API

### Submit Rating

```http
POST /api/v1/chat/satisfaction/ratings
```

**Request Body:**
```json
{
    "chat_room_id": 1,
    "rating": 5,
    "comment": "Excellent service, very helpful!"
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "id": 123,
        "chat_room_id": 1,
        "rating": 5,
        "comment": "Excellent service, very helpful!",
        "created_at": "2024-01-01T11:00:00Z"
    },
    "message": "Rating submitted successfully"
}
```

### Get Satisfaction Metrics

```http
GET /api/v1/chat/satisfaction/metrics
```

**Response:**
```json
{
    "success": true,
    "data": {
        "overview": {
            "average_rating": 4.3,
            "total_ratings": 200,
            "satisfaction_rate": 85.5,
            "nps_score": 42
        },
        "ratings_breakdown": {
            "5": 120,
            "4": 50,
            "3": 20,
            "2": 7,
            "1": 3
        }
    }
}
```

## File Upload API

### Upload File

```http
POST /api/v1/chat/upload
```

**Request Body (multipart/form-data):**
- `file`: File to upload
- `chat_room_id`: Chat room ID
- `type`: File type (image, document, etc.)

**Response:**
```json
{
    "success": true,
    "data": {
        "id": 123,
        "filename": "document.pdf",
        "original_name": "invoice.pdf",
        "mime_type": "application/pdf",
        "size": 1024000,
        "url": "https://example.com/uploads/document.pdf",
        "created_at": "2024-01-01T10:30:00Z"
    }
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input data |
| 401 | Unauthorized - Invalid or missing authentication |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 422 | Unprocessable Entity - Validation errors |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server error |

## WebSocket Events

### Real-time Events

The system broadcasts real-time events via WebSocket:

**Message Sent:**
```json
{
    "event": "message.sent",
    "data": {
        "chat_room_id": 1,
        "message": {...}
    }
}
```

**User Typing:**
```json
{
    "event": "user.typing",
    "data": {
        "chat_room_id": 1,
        "user_id": 123,
        "typing": true
    }
}
```

**Staff Assignment:**
```json
{
    "event": "staff.assigned",
    "data": {
        "chat_room_id": 1,
        "staff_id": 456
    }
}
```

## SDKs and Libraries

### JavaScript SDK

```javascript
import ChatAPI from '@chisolution/chat-api-sdk';

const chat = new ChatAPI({
    baseURL: 'https://chisolution.io/api/v1',
    token: 'your-auth-token'
});

// Send message
await chat.sendMessage(roomId, 'Hello!');

// Listen for real-time events
chat.on('message.sent', (data) => {
    console.log('New message:', data);
});
```

### PHP SDK

```php
use ChiSolution\ChatAPI\Client;

$client = new Client([
    'base_uri' => 'https://chisolution.io/api/v1',
    'token' => 'your-auth-token'
]);

// Send message
$response = $client->sendMessage($roomId, 'Hello!');
```

## Testing

### Postman Collection

Import our Postman collection for easy API testing:
[Download Postman Collection](./postman/chat-api.json)

### Example Requests

See the `examples/` directory for complete request/response examples in various programming languages.

## Support

For API support, please contact:
- Email: <EMAIL>
- Documentation: https://chisolution.com/
- Status Page: https://chisolution.io
