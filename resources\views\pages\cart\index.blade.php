@extends('layouts.app')

@section('title', 'Shopping Cart - ' . __('common.company_name'))
@section('meta_description', 'Review your cart items and proceed to checkout for your digital products and services.')

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-blue text-white overflow-hidden">
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
    </div>
    
    <div class="container mx-auto px-4 py-16 relative z-10">
        <div class="text-center max-w-4xl mx-auto">
            <h1 class="heading-1 text-white mb-4">
                Shopping <span class="text-blue-300">Cart</span>
            </h1>
            <p class="text-lead text-blue-100">
                Review your selected items and proceed to checkout when you're ready.
            </p>
        </div>
    </div>
    
    <div class="absolute bottom-0 left-0 w-full">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block w-full h-16 fill-white">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
        </svg>
    </div>
</section>

<!-- Cart Content -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        @if($cart && !$cart->isEmpty())
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Cart Items -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-xl font-semibold text-gray-900">Cart Items ({{ $cart->item_count }})</h2>
                    </div>
                    
                    <div class="divide-y divide-gray-200">
                        @foreach($cart->items as $item)
                        <div class="p-6 cart-item" data-item-id="{{ $item->id }}">
                            <div class="flex items-start space-x-4">
                                <!-- Product Image -->
                                <div class="flex-shrink-0">
                                    <img src="{{ $item->image }}" alt="{{ $item->name }}" 
                                         class="w-20 h-20 object-cover rounded-lg">
                                </div>
                                
                                <!-- Product Details -->
                                <div class="flex-1 min-w-0">
                                    <h3 class="text-lg font-medium text-gray-900 mb-1">
                                        <a href="{{ route('shop.product', ['product' => $item->product->slug, 'locale' => app()->getLocale()]) }}"
                                           class="hover:text-blue-600 transition-colors">
                                            {{ $item->name }}
                                        </a>
                                    </h3>
                                    
                                    @if($item->productVariant)
                                    <div class="text-sm text-gray-600 mb-2">
                                        @foreach($item->productVariant->formatted_attributes as $attribute)
                                        <span class="inline-block mr-3">
                                            {{ $attribute['name'] }}: {{ $attribute['value'] }}
                                        </span>
                                        @endforeach
                                    </div>
                                    @endif
                                    
                                    <div class="text-sm text-gray-500 mb-3">
                                        SKU: {{ $item->sku }}
                                    </div>
                                    
                                    <!-- Quantity and Price -->
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <label class="text-sm text-gray-600">Qty:</label>
                                            <div class="flex items-center border border-gray-300 rounded">
                                                <button type="button" class="quantity-btn decrease-qty px-3 py-1 text-gray-600 hover:bg-gray-100" 
                                                        data-item-id="{{ $item->id }}">-</button>
                                                <input type="number" value="{{ $item->quantity }}" min="1" max="99" 
                                                       class="quantity-input w-16 px-2 py-1 text-center border-0 focus:ring-0" 
                                                       data-item-id="{{ $item->id }}">
                                                <button type="button" class="quantity-btn increase-qty px-3 py-1 text-gray-600 hover:bg-gray-100" 
                                                        data-item-id="{{ $item->id }}">+</button>
                                            </div>
                                        </div>
                                        
                                        <div class="text-right">
                                            <div class="text-lg font-semibold text-gray-900 item-total">
                                                {{ $item->formatted_total }}
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                {{ $item->formatted_price }} each
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Stock Status -->
                                    @if(!$item->isAvailable())
                                    <div class="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                                        <div class="flex items-center">
                                            <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                            </svg>
                                            <span class="text-sm text-red-700">
                                                This item is no longer available or out of stock.
                                            </span>
                                        </div>
                                    </div>
                                    @elseif($item->getAvailableQuantity() < $item->quantity)
                                    <div class="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                                        <div class="flex items-center">
                                            <svg class="w-5 h-5 text-yellow-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                            </svg>
                                            <span class="text-sm text-yellow-700">
                                                Only {{ $item->getAvailableQuantity() }} items available in stock.
                                            </span>
                                        </div>
                                    </div>
                                    @endif
                                </div>
                                
                                <!-- Remove Button -->
                                <div class="flex-shrink-0">
                                    <button type="button" class="remove-item text-gray-400 hover:text-red-500 transition-colors" 
                                            data-item-id="{{ $item->id }}">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                    
                    <!-- Cart Actions -->
                    <div class="p-6 border-t border-gray-200 bg-gray-50">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                            <button type="button" id="clear-cart" class="btn-outline text-red-600 border-red-300 hover:bg-red-50">
                                Clear Cart
                            </button>
                            <a href="{{ route('shop.index', ['locale' => app()->getLocale()]) }}" class="btn-outline">
                                Continue Shopping
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Cart Summary -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-sm p-6 sticky top-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Order Summary</h3>
                    
                    <div class="space-y-4 mb-6">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Subtotal</span>
                            <span class="font-medium cart-subtotal">{{ $cart->formatted_subtotal }}</span>
                        </div>
                        
                        @if($cart->tax_amount > 0)
                        <div class="flex justify-between">
                            <span class="text-gray-600">VAT (15%)</span>
                            <span class="font-medium cart-tax">{{ $cart->formatted_tax_amount }}</span>
                        </div>
                        @endif
                        
                        @if($cart->shipping_amount > 0)
                        <div class="flex justify-between">
                            <span class="text-gray-600">Shipping</span>
                            <span class="font-medium cart-shipping">{{ $cart->formatted_shipping_amount }}</span>
                        </div>
                        @endif
                        
                        @if($cart->discount_amount > 0)
                        <div class="flex justify-between text-green-600">
                            <span>Discount</span>
                            <span class="font-medium cart-discount">-{{ $cart->formatted_discount_amount }}</span>
                        </div>
                        @endif
                        
                        <div class="border-t border-gray-200 pt-4">
                            <div class="flex justify-between text-lg font-semibold">
                                <span>Total</span>
                                <span class="cart-total">{{ $cart->formatted_total }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Coupon Code -->
                    <div class="mb-6">
                        @if($cart->coupon_code)
                        <!-- Applied Coupon -->
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    <div>
                                        <div class="text-sm font-medium text-green-800">
                                            Coupon Applied: {{ $cart->coupon_code }}
                                        </div>
                                        <div class="text-xs text-green-600">
                                            You saved {{ $cart->formatted_discount_amount }}
                                        </div>
                                    </div>
                                </div>
                                <button type="button" id="remove-coupon" class="text-green-600 hover:text-green-800 text-sm underline">
                                    Remove
                                </button>
                            </div>
                        </div>
                        @else
                        <!-- Coupon Form -->
                        <form id="coupon-form" class="space-y-3">
                            <div>
                                <label for="coupon_code" class="block text-sm font-medium text-gray-700 mb-1">
                                    Coupon Code
                                </label>
                                <div class="flex">
                                    <input type="text" id="coupon_code" name="coupon_code"
                                           class="flex-1 form-input rounded-r-none"
                                           placeholder="Enter coupon code">
                                    <button type="submit" class="btn-outline rounded-l-none border-l-0">
                                        Apply
                                    </button>
                                </div>
                            </div>
                        </form>
                        @endif
                    </div>
                    
                    <!-- Checkout Button -->
                    <div class="space-y-3">
                        @if($cart->allItemsAvailable())
                        <a href="{{ route('checkout.index', ['locale' => app()->getLocale()]) }}" class="w-full btn-primary text-center block">
                            Proceed to Checkout
                            <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </a>
                        @else
                        <div class="w-full bg-gray-300 text-gray-500 px-6 py-3 rounded-lg text-center">
                            Some items are unavailable
                        </div>
                        @endif
                        
                        <div class="text-xs text-gray-500 text-center">
                            Secure checkout with SSL encryption
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @else
        <!-- Empty Cart -->
        <div class="text-center py-12">
            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg class="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path>
                </svg>
            </div>
            <h3 class="text-xl font-medium text-gray-900 mb-2">Your cart is empty</h3>
            <p class="text-gray-600 mb-6">
                Looks like you haven't added any items to your cart yet.
            </p>
            <a href="{{ route('shop.index', ['locale' => app()->getLocale()]) }}" class="btn-primary">
                Start Shopping
                <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </a>
        </div>
        @endif
    </div>
</section>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Quantity update handlers
    document.querySelectorAll('.quantity-btn').forEach(button => {
        button.addEventListener('click', function() {
            const itemId = this.dataset.itemId;
            const input = document.querySelector(`.quantity-input[data-item-id="${itemId}"]`);
            const isIncrease = this.classList.contains('increase-qty');
            
            let newQuantity = parseInt(input.value);
            if (isIncrease) {
                newQuantity++;
            } else {
                newQuantity = Math.max(0, newQuantity - 1);
            }
            
            updateCartItem(itemId, newQuantity);
        });
    });
    
    // Quantity input change handler
    document.querySelectorAll('.quantity-input').forEach(input => {
        input.addEventListener('change', function() {
            const itemId = this.dataset.itemId;
            const quantity = Math.max(0, parseInt(this.value) || 0);
            updateCartItem(itemId, quantity);
        });
    });
    
    // Remove item handlers
    document.querySelectorAll('.remove-item').forEach(button => {
        button.addEventListener('click', function() {
            const itemId = this.dataset.itemId;
            removeCartItem(itemId);
        });
    });
    
    // Clear cart handler
    document.getElementById('clear-cart')?.addEventListener('click', function() {
        if (confirm('Are you sure you want to clear your cart?')) {
            clearCart();
        }
    });
    
    // Coupon form handler
    document.getElementById('coupon-form')?.addEventListener('submit', function(e) {
        e.preventDefault();
        applyCoupon();
    });

    // Remove coupon handler
    document.getElementById('remove-coupon')?.addEventListener('click', function() {
        removeCoupon();
    });
});

function updateCartItem(itemId, quantity) {
    // Update local state first for instant feedback
    if (window.cartStateManager) {
        window.cartStateManager.updateItemQuantity(itemId, quantity);

        // Update UI immediately
        if (quantity === 0) {
            document.querySelector(`[data-item-id="${itemId}"]`).closest('.cart-item').remove();
            showNotification('Item removed from cart', 'success');
        } else {
            // Update quantity display
            const quantityInput = document.querySelector(`[data-item-id="${itemId}"]`);
            if (quantityInput) {
                quantityInput.value = quantity;
            }
            showNotification('Cart updated', 'success');
        }

        // Update totals from local state
        const state = window.cartStateManager.getState();
        updateCartTotalsFromState(state);

        return; // Local update complete, server sync happens automatically
    }

    // Fallback to server request
    fetch(`/cart/update/${itemId}`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({ quantity: quantity })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (quantity === 0) {
                document.querySelector(`[data-item-id="${itemId}"]`).closest('.cart-item').remove();
            } else {
                updateCartDisplay(data);
            }
            updateCartTotals(data.cart);
            showNotification(data.message, 'success');
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('An error occurred. Please try again.', 'error');
    });
}

function removeCartItem(itemId) {
    // Update local state first for instant feedback
    if (window.cartStateManager) {
        window.cartStateManager.removeItem(itemId);

        // Update UI immediately
        document.querySelector(`[data-item-id="${itemId}"]`).closest('.cart-item').remove();
        showNotification('Item removed from cart', 'success');

        // Update totals from local state
        const state = window.cartStateManager.getState();
        updateCartTotalsFromState(state);

        // Reload page if cart is empty
        if (state.count === 0) {
            location.reload();
        }

        return; // Local update complete, server sync happens automatically
    }

    // Fallback to server request
    fetch(`/cart/remove/${itemId}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.querySelector(`[data-item-id="${itemId}"]`).closest('.cart-item').remove();
            updateCartTotals(data.cart);
            showNotification(data.message, 'success');

            // Reload page if cart is empty
            if (data.cart.item_count === 0) {
                location.reload();
            }
        } else {
            showNotification(data.message, 'error');
        }
    });
}

function clearCart() {
    fetch('/cart/clear', {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            showNotification('Failed to clear cart', 'error');
        }
    });
}

function applyCoupon() {
    const couponCode = document.getElementById('coupon_code').value.trim();

    if (!couponCode) {
        showNotification('Please enter a coupon code.', 'error');
        return;
    }

    const submitBtn = document.querySelector('#coupon-form button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Applying...';
    submitBtn.disabled = true;

    fetch(window.appConfig?.routes?.cartCoupon || '/cart/coupon', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({ coupon_code: couponCode })
    })
    .then(response => response.json())
    .then(data => {
        showNotification(data.message, data.success ? 'success' : 'error');

        if (data.success) {
            // Reload page to show applied coupon
            location.reload();
        }
    })
    .catch(error => {
        showNotification('An error occurred. Please try again.', 'error');
    })
    .finally(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });
}

function removeCoupon() {
    if (!confirm('Are you sure you want to remove this coupon?')) {
        return;
    }

    fetch(window.appConfig?.routes?.cartCouponRemove || '/cart/coupon', {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        showNotification(data.message, data.success ? 'success' : 'error');

        if (data.success) {
            // Reload page to show updated cart
            location.reload();
        }
    })
    .catch(error => {
        showNotification('An error occurred. Please try again.', 'error');
    });
}

function updateCartTotals(cart) {
    document.querySelector('.cart-total').textContent = cart.formatted_total;
    // Use global cart count update function
    updateCartCount(cart.item_count);
}

function updateCartTotalsFromState(state) {
    // Format currency values
    const formatCurrency = (amount) => `R ${amount.toFixed(2)}`;

    // Update totals if elements exist
    const subtotalEl = document.getElementById('cart-subtotal');
    const taxEl = document.getElementById('cart-tax');
    const totalEl = document.querySelector('.cart-total');

    if (subtotalEl) subtotalEl.textContent = formatCurrency(state.subtotal);
    if (taxEl) taxEl.textContent = formatCurrency(state.tax_amount);
    if (totalEl) totalEl.textContent = formatCurrency(state.total);

    // Update item count in header
    updateCartCount(state.count);
}

function updateCartDisplay(data) {
    const itemElement = document.querySelector(`[data-item-id="${data.item.id}"]`);
    if (itemElement) {
        itemElement.querySelector('.quantity-input').value = data.item.quantity;
        itemElement.querySelector('.item-total').textContent = data.item.formatted_total;
    }
}

// Note: showNotification function is now loaded globally from cart-utils.js
</script>
@endpush
@endsection
