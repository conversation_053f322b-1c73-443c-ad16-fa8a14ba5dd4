<?php

namespace App\Http\Requests;

use App\Services\ActivityLogger;
use App\Services\VisitorAnalytics;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Auth;

class CheckoutRequest extends FormRequest
{
    protected ActivityLogger $activityLogger;
    protected VisitorAnalytics $visitorAnalytics;

    public function __construct()
    {
        parent::__construct();
        $this->activityLogger = app(ActivityLogger::class);
        $this->visitorAnalytics = app(VisitorAnalytics::class);
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Log authorization attempt
        $this->activityLogger->logCheckoutActivity(
            'form_validation',
            [
                'authorization_check' => true,
                'user_authenticated' => Auth::check(),
                'user_id' => Auth::id(),
                'request_method' => $this->method(),
                'request_url' => $this->url()
            ],
            true,
            null,
            null,
            []
        );

        return true; // Allow all users (guests and authenticated) to checkout
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            // Customer Information
            'email' => 'required|email|max:255',
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',
            
            // Billing Address
            'billing_address_line_1' => 'required|string|max:255',
            'billing_address_line_2' => 'nullable|string|max:255',
            'billing_city' => 'required|string|max:255',
            'billing_state' => 'required|string|max:255',
            'billing_postal_code' => 'required|string|max:10',
            'billing_country' => 'required|string|max:2',
            
            // Shipping Address
            'same_as_billing' => 'boolean',
            'shipping_address_line_1' => 'required_unless:same_as_billing,1,true|nullable|string|max:255',
            'shipping_address_line_2' => 'nullable|string|max:255',
            'shipping_city' => 'required_unless:same_as_billing,1,true|nullable|string|max:255',
            'shipping_state' => 'required_unless:same_as_billing,1,true|nullable|string|max:255',
            'shipping_postal_code' => 'required_unless:same_as_billing,1,true|nullable|string|max:10',
            'shipping_country' => 'required_unless:same_as_billing,1,true|nullable|string|max:2',
            
            // Payment and Account
            'payment_method' => 'required|in:stripe,paypal,cash_on_delivery,pay_offline',
            'terms_accepted' => 'required|accepted',
            'create_account' => 'boolean',
            'password' => 'required_if:create_account,true|nullable|string|min:8|confirmed',
            'password_confirmation' => 'required_if:create_account,true|nullable|string|min:8',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'email.required' => 'Email address is required.',
            'email.email' => 'Please enter a valid email address.',
            'first_name.required' => 'First name is required.',
            'last_name.required' => 'Last name is required.',
            'billing_address_line_1.required' => 'Billing address is required.',
            'billing_city.required' => 'Billing city is required.',
            'billing_state.required' => 'Billing state/province is required.',
            'billing_postal_code.required' => 'Billing postal code is required.',
            'billing_country.required' => 'Billing country is required.',
            'shipping_address_line_1.required_unless' => 'Shipping address is required when different from billing.',
            'shipping_city.required_unless' => 'Shipping city is required when different from billing.',
            'shipping_state.required_unless' => 'Shipping state/province is required when different from billing.',
            'shipping_postal_code.required_unless' => 'Shipping postal code is required when different from billing.',
            'shipping_country.required_unless' => 'Shipping country is required when different from billing.',
            'payment_method.required' => 'Please select a payment method.',
            'payment_method.in' => 'Please select a valid payment method.',
            'terms_accepted.required' => 'You must accept the terms and conditions.',
            'terms_accepted.accepted' => 'You must accept the terms and conditions.',
            'password.required_if' => 'Password is required when creating an account.',
            'password.min' => 'Password must be at least 8 characters long.',
            'password.confirmed' => 'Password confirmation does not match.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     */
    public function attributes(): array
    {
        return [
            'billing_address_line_1' => 'billing address',
            'billing_address_line_2' => 'billing address line 2',
            'billing_city' => 'billing city',
            'billing_state' => 'billing state/province',
            'billing_postal_code' => 'billing postal code',
            'billing_country' => 'billing country',
            'shipping_address_line_1' => 'shipping address',
            'shipping_address_line_2' => 'shipping address line 2',
            'shipping_city' => 'shipping city',
            'shipping_state' => 'shipping state/province',
            'shipping_postal_code' => 'shipping postal code',
            'shipping_country' => 'shipping country',
            'same_as_billing' => 'same as billing address',
            'payment_method' => 'payment method',
            'terms_accepted' => 'terms and conditions',
            'create_account' => 'create account',
            'password_confirmation' => 'password confirmation',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Log validation preparation
        $this->activityLogger->logCheckoutActivity(
            'form_validation',
            [
                'validation_preparation' => true,
                'fields_submitted' => array_keys($this->all()),
                'payment_method' => $this->input('payment_method'),
                'create_account' => $this->boolean('create_account'),
                'same_as_billing' => $this->boolean('same_as_billing'),
                'has_shipping_info' => !$this->boolean('same_as_billing'),
                'form_size' => count($this->all()),
                'customer_type' => $this->boolean('create_account') ? 'new_account' : (Auth::check() ? 'existing_user' : 'guest'),
                'cart_type' => Auth::check() ? 'user' : 'session',
                'user_authenticated' => Auth::check()
            ],
            true,
            null,
            null,
            []
        );

        // Normalize boolean fields
        $this->merge([
            'same_as_billing' => $this->boolean('same_as_billing'),
            'create_account' => $this->boolean('create_account'),
            'terms_accepted' => $this->boolean('terms_accepted'),
        ]);

        // Clean up phone number
        if ($this->has('phone') && $this->input('phone')) {
            $this->merge([
                'phone' => preg_replace('/[^+\d\s\-\(\)]/', '', $this->input('phone'))
            ]);
        }

        // Normalize postal codes
        if ($this->has('billing_postal_code')) {
            $this->merge([
                'billing_postal_code' => strtoupper(trim($this->input('billing_postal_code')))
            ]);
        }

        if ($this->has('shipping_postal_code')) {
            $this->merge([
                'shipping_postal_code' => strtoupper(trim($this->input('shipping_postal_code')))
            ]);
        }
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(Validator $validator): void
    {
        $errors = $validator->errors()->toArray();
        
        // Log detailed validation failure
        $this->activityLogger->logCheckoutActivity(
            'form_validation',
            [
                'validation_failed' => true,
                'fields_submitted' => array_keys($this->all()),
                'failed_fields' => array_keys($errors),
                'error_count' => count($errors),
                'critical_fields_failed' => $this->getCriticalFieldFailures($errors),
                'payment_method' => $this->input('payment_method'),
                'create_account' => $this->boolean('create_account'),
                'same_as_billing' => $this->boolean('same_as_billing'),
                'validation_summary' => $this->getValidationSummary($errors)
            ],
            false,
            'Checkout form validation failed',
            null,
            $errors
        );

        // Track form validation failure in visitor analytics
        $this->visitorAnalytics->trackFormInteraction(
            'checkout_form',
            'validation_failed',
            false,
            [
                'failed_fields' => array_keys($errors),
                'error_count' => count($errors),
                'payment_method' => $this->input('payment_method')
            ]
        );

        parent::failedValidation($validator);
    }

    /**
     * Handle a passed validation attempt.
     */
    protected function passedValidation(): void
    {
        // Log successful validation
        $this->activityLogger->logCheckoutActivity(
            'form_validation',
            [
                'validation_passed' => true,
                'validated_fields' => array_keys($this->validated()),
                'customer_email' => $this->input('email'),
                'payment_method' => $this->input('payment_method'),
                'billing_country' => $this->input('billing_country'),
                'shipping_country' => $this->input('shipping_country', $this->input('billing_country')),
                'create_account' => $this->boolean('create_account'),
                'same_as_billing' => $this->boolean('same_as_billing')
            ],
            true,
            null,
            null,
            []
        );

        // Track successful form validation
        $this->visitorAnalytics->trackFormInteraction(
            'checkout_form',
            'validation_passed',
            true,
            [
                'payment_method' => $this->input('payment_method'),
                'customer_type' => $this->boolean('create_account') ? 'new_account' : (Auth::check() ? 'existing_user' : 'guest')
            ]
        );
    }

    /**
     * Get critical field failures.
     */
    private function getCriticalFieldFailures(array $errors): array
    {
        $criticalFields = [
            'email',
            'payment_method',
            'billing_address_line_1',
            'billing_city',
            'billing_country',
            'terms_accepted'
        ];

        return array_intersect(array_keys($errors), $criticalFields);
    }

    /**
     * Get validation summary for logging.
     */
    private function getValidationSummary(array $errors): array
    {
        $summary = [
            'total_errors' => count($errors),
            'field_categories' => []
        ];

        $categories = [
            'customer_info' => ['email', 'first_name', 'last_name', 'phone', 'company'],
            'billing_address' => ['billing_address_line_1', 'billing_address_line_2', 'billing_city', 'billing_state', 'billing_postal_code', 'billing_country'],
            'shipping_address' => ['shipping_address_line_1', 'shipping_address_line_2', 'shipping_city', 'shipping_state', 'shipping_postal_code', 'shipping_country'],
            'payment' => ['payment_method'],
            'account' => ['create_account', 'password', 'password_confirmation'],
            'terms' => ['terms_accepted']
        ];

        foreach ($categories as $category => $fields) {
            $categoryErrors = array_intersect(array_keys($errors), $fields);
            if (!empty($categoryErrors)) {
                $summary['field_categories'][$category] = count($categoryErrors);
            }
        }

        return $summary;
    }
}
