@extends('layouts.dashboard')

@section('title', 'Subscriber Segmentation')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Subscriber Segmentation</h1>
            <p class="mt-1 text-sm text-gray-600">Advanced subscriber targeting and segmentation tools</p>
        </div>
        <div class="mt-4 sm:mt-0 flex items-center space-x-3">
            <a href="{{ route('admin.subscriber-tags.index') }}" 
               class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                </svg>
                Manage Tags
            </a>
            <button id="export-segment-btn" 
                    class="inline-flex items-center px-4 py-2 bg-secondary-600 text-white rounded-lg hover:bg-secondary-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                Export Segment
            </button>
        </div>
    </div>

    <!-- Overall Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Subscribers</p>
                    <p class="text-2xl font-bold text-gray-900">{{ number_format($overallStats['total_subscribers']) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active Subscribers</p>
                    <p class="text-2xl font-bold text-gray-900">{{ number_format($overallStats['active_subscribers']) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Avg. Engagement</p>
                    <p class="text-2xl font-bold text-gray-900">{{ number_format($overallStats['avg_engagement_score'], 1) }}%</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">New This Month</p>
                    <p class="text-2xl font-bold text-gray-900">{{ number_format($overallStats['new_this_month']) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Segmentation Tools -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Segmentation Filters -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Segmentation Filters</h3>
                
                <form id="segmentation-form" class="space-y-6">
                    <!-- Basic Filters -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Lifecycle Stage -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Lifecycle Stage</label>
                            <div class="space-y-2">
                                @foreach(['new', 'active', 'engaged', 'at_risk', 'inactive'] as $stage)
                                    <div class="flex items-center">
                                        <input type="checkbox" 
                                               id="lifecycle_{{ $stage }}" 
                                               name="lifecycle_stages[]" 
                                               value="{{ $stage }}"
                                               class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                        <label for="lifecycle_{{ $stage }}" class="ml-2 text-sm text-gray-700 capitalize">{{ $stage }}</label>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- Engagement Score -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Engagement Score</label>
                            <div class="space-y-2">
                                <div class="flex space-x-2">
                                    <input type="number" 
                                           name="engagement_min" 
                                           placeholder="Min"
                                           min="0" 
                                           max="100"
                                           class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                                    <input type="number" 
                                           name="engagement_max" 
                                           placeholder="Max"
                                           min="0" 
                                           max="100"
                                           class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Activity Filters -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Last Activity -->
                        <div>
                            <label for="last_activity_days" class="block text-sm font-medium text-gray-700 mb-2">Active Within (Days)</label>
                            <select name="last_activity_days" 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                                <option value="">Any time</option>
                                <option value="7">Last 7 days</option>
                                <option value="30">Last 30 days</option>
                                <option value="90">Last 90 days</option>
                                <option value="365">Last year</option>
                            </select>
                        </div>

                        <!-- Email Activity -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email Activity</label>
                            <div class="space-y-2">
                                <input type="number" 
                                       name="min_opens" 
                                       placeholder="Min opens"
                                       min="0"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                                <input type="number" 
                                       name="min_clicks" 
                                       placeholder="Min clicks"
                                       min="0"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            </div>
                        </div>
                    </div>

                    <!-- Tag Filters -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Tags</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Include Tags -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">Include Tags</label>
                                <div class="max-h-32 overflow-y-auto border border-gray-200 rounded-lg p-2 space-y-1">
                                    @foreach($topTags as $tag)
                                        <div class="flex items-center">
                                            <input type="checkbox" 
                                                   id="include_tag_{{ $tag->id }}" 
                                                   name="include_tags[]" 
                                                   value="{{ $tag->id }}"
                                                   class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                            <label for="include_tag_{{ $tag->id }}" class="ml-2 text-sm text-gray-700 flex items-center">
                                                <span class="inline-block w-3 h-3 rounded-full mr-2" style="background-color: {{ $tag->color }}"></span>
                                                {{ $tag->name }}
                                            </label>
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                            <!-- Exclude Tags -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">Exclude Tags</label>
                                <div class="max-h-32 overflow-y-auto border border-gray-200 rounded-lg p-2 space-y-1">
                                    @foreach($topTags as $tag)
                                        <div class="flex items-center">
                                            <input type="checkbox" 
                                                   id="exclude_tag_{{ $tag->id }}" 
                                                   name="exclude_tags[]" 
                                                   value="{{ $tag->id }}"
                                                   class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                            <label for="exclude_tag_{{ $tag->id }}" class="ml-2 text-sm text-gray-700 flex items-center">
                                                <span class="inline-block w-3 h-3 rounded-full mr-2" style="background-color: {{ $tag->color }}"></span>
                                                {{ $tag->name }}
                                            </label>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Date Filters -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="subscribed_after" class="block text-sm font-medium text-gray-700 mb-2">Subscribed After</label>
                            <input type="date" 
                                   name="subscribed_after" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        </div>
                        <div>
                            <label for="subscribed_before" class="block text-sm font-medium text-gray-700 mb-2">Subscribed Before</label>
                            <input type="date" 
                                   name="subscribed_before" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        </div>
                    </div>

                    <!-- Search -->
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                        <input type="text" 
                               name="search" 
                               placeholder="Search by email or name..."
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    </div>

                    <!-- Actions -->
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                        <div class="flex items-center space-x-3">
                            <button type="button" 
                                    id="apply-filters-btn"
                                    class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200">
                                Apply Filters
                            </button>
                            <button type="button" 
                                    id="clear-filters-btn"
                                    class="inline-flex items-center px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors duration-200">
                                Clear All
                            </button>
                        </div>
                        <div class="text-sm text-gray-600">
                            <span id="segment-count">0</span> subscribers match criteria
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Quick Segments & Analytics -->
        <div class="space-y-6">
            <!-- Lifecycle Distribution -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Lifecycle Distribution</h3>
                <div class="space-y-3">
                    @foreach($lifecycleDistribution as $stage => $count)
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 capitalize">{{ $stage }}</span>
                            <span class="text-sm font-medium text-gray-900">{{ number_format($count) }}</span>
                        </div>
                    @endforeach
                </div>
            </div>

            <!-- Engagement Distribution -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Engagement Levels</h3>
                <div class="space-y-3">
                    @foreach($engagementDistribution as $level => $count)
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">{{ $level }}</span>
                            <span class="text-sm font-medium text-gray-900">{{ number_format($count) }}</span>
                        </div>
                    @endforeach
                </div>
            </div>

            <!-- Top Tags -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Popular Tags</h3>
                <div class="space-y-3">
                    @foreach($topTags->take(5) as $tag)
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <span class="inline-block w-3 h-3 rounded-full" style="background-color: {{ $tag->color }}"></span>
                                <span class="text-sm text-gray-600">{{ $tag->name }}</span>
                            </div>
                            <span class="text-sm font-medium text-gray-900">{{ number_format($tag->subscribers_count) }}</span>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- Segmented Subscribers Results -->
    <div id="segmented-results" class="hidden bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">Segmented Subscribers</h3>
            <div class="flex items-center space-x-2">
                <button id="bulk-tag-btn" 
                        class="inline-flex items-center px-3 py-2 bg-secondary-600 text-white rounded-lg hover:bg-secondary-700 transition-colors duration-200">
                    Bulk Tag Assignment
                </button>
            </div>
        </div>
        <div id="subscribers-table-container">
            <!-- Results will be loaded here -->
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const segmentationForm = document.getElementById('segmentation-form');
    const applyFiltersBtn = document.getElementById('apply-filters-btn');
    const clearFiltersBtn = document.getElementById('clear-filters-btn');
    const segmentCountSpan = document.getElementById('segment-count');
    const segmentedResults = document.getElementById('segmented-results');
    const subscribersTableContainer = document.getElementById('subscribers-table-container');

    // Apply filters
    applyFiltersBtn.addEventListener('click', function() {
        const formData = new FormData(segmentationForm);
        
        // First get the count
        fetch('{{ route("admin.subscriber-segments.preview") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                segmentCountSpan.textContent = data.count.toLocaleString();
                
                if (data.count > 0) {
                    loadSegmentedSubscribers();
                } else {
                    segmentedResults.classList.add('hidden');
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    });

    // Clear filters
    clearFiltersBtn.addEventListener('click', function() {
        segmentationForm.reset();
        segmentCountSpan.textContent = '0';
        segmentedResults.classList.add('hidden');
    });

    // Load segmented subscribers
    function loadSegmentedSubscribers() {
        const formData = new FormData(segmentationForm);
        const params = new URLSearchParams(formData);
        
        fetch(`{{ route("admin.subscriber-segments.subscribers") }}?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displaySubscribers(data.subscribers);
                segmentedResults.classList.remove('hidden');
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }

    // Display subscribers in table
    function displaySubscribers(subscribers) {
        let html = `
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" id="select-all-subscribers" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subscriber</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lifecycle</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Engagement</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activity</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tags</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
        `;

        subscribers.data.forEach(subscriber => {
            html += `
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4">
                        <input type="checkbox" class="subscriber-checkbox rounded border-gray-300 text-primary-600 focus:ring-primary-500" value="${subscriber.id}">
                    </td>
                    <td class="px-6 py-4">
                        <div class="text-sm font-medium text-gray-900">${subscriber.email}</div>
                        ${subscriber.name ? `<div class="text-sm text-gray-500">${subscriber.name}</div>` : ''}
                    </td>
                    <td class="px-6 py-4">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 capitalize">
                            ${subscriber.lifecycle_stage}
                        </span>
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-900">
                        ${subscriber.engagement_score}%
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-500">
                        ${subscriber.last_activity_at ? new Date(subscriber.last_activity_at).toLocaleDateString() : 'Never'}
                    </td>
                    <td class="px-6 py-4">
                        <div class="flex flex-wrap gap-1">
                            ${subscriber.tags.map(tag => `
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium" style="background-color: ${tag.color}20; color: ${tag.color}">
                                    ${tag.name}
                                </span>
                            `).join('')}
                        </div>
                    </td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        subscribersTableContainer.innerHTML = html;

        // Add select all functionality
        const selectAllCheckbox = document.getElementById('select-all-subscribers');
        const subscriberCheckboxes = document.querySelectorAll('.subscriber-checkbox');

        selectAllCheckbox.addEventListener('change', function() {
            subscriberCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }

    // Export segment
    document.getElementById('export-segment-btn').addEventListener('click', function() {
        const formData = new FormData(segmentationForm);
        const params = new URLSearchParams(formData);
        
        window.open(`{{ route("admin.subscriber-segments.export") }}?${params}`, '_blank');
    });

    // Auto-update count when filters change
    segmentationForm.addEventListener('change', function() {
        const formData = new FormData(segmentationForm);
        
        fetch('{{ route("admin.subscriber-segments.preview") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                segmentCountSpan.textContent = data.count.toLocaleString();
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    });
});
</script>
@endpush
@endsection
