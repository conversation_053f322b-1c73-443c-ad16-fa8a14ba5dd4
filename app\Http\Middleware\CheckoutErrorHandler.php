<?php

namespace App\Http\Middleware;

use App\Services\ActivityLogger;
use App\Services\VisitorAnalytics;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;

class CheckoutErrorHandler
{
    protected ActivityLogger $activityLogger;
    protected VisitorAnalytics $visitorAnalytics;

    public function __construct(ActivityLogger $activityLogger, VisitorAnalytics $visitorAnalytics)
    {
        $this->activityLogger = $activityLogger;
        $this->visitorAnalytics = $visitorAnalytics;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): SymfonyResponse
    {
        try {
            $response = $next($request);

            // Log successful checkout requests
            if ($this->isCheckoutRoute($request) && $response->getStatusCode() < 400) {
                $this->logSuccessfulCheckoutRequest($request, $response);
            }

            return $response;

        } catch (\Throwable $exception) {
            // Log the exception with comprehensive checkout context
            $this->logCheckoutException($request, $exception);

            // Track error in visitor analytics
            $this->trackCheckoutError($request, $exception);

            // Re-throw the exception to maintain normal error handling flow
            throw $exception;
        }
    }

    /**
     * Check if the current route is a checkout route.
     */
    protected function isCheckoutRoute(Request $request): bool
    {
        $routeName = $request->route()?->getName();
        $uri = $request->getRequestUri();

        return str_starts_with($routeName ?? '', 'checkout.') ||
               str_contains($uri, '/checkout') ||
               str_contains($uri, '/payment');
    }

    /**
     * Log successful checkout request.
     */
    protected function logSuccessfulCheckoutRequest(Request $request, SymfonyResponse $response): void
    {
        $routeName = $request->route()?->getName();
        $method = $request->getMethod();

        // Only log important successful requests to avoid spam
        $importantRoutes = [
            'checkout.index',
            'checkout.process',
            'checkout.payment',
            'checkout.process-payment',
            'checkout.success'
        ];

        if (in_array($routeName, $importantRoutes)) {
            $this->activityLogger->logCheckoutActivity(
                'request_successful',
                [
                    'route_name' => $routeName,
                    'method' => $method,
                    'status_code' => $response->getStatusCode(),
                    'request_uri' => $request->getRequestUri(),
                    'user_authenticated' => Auth::check(),
                    'user_id' => Auth::id(),
                    'customer_type' => Auth::check() ? 'registered' : 'guest',
                    'cart_type' => Auth::check() ? 'user' : 'session',
                    'session_id' => $request->session()->getId(),
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'referer' => $request->header('referer'),
                    'request_size' => strlen($request->getContent()),
                    'response_size' => $response instanceof Response ? strlen($response->getContent()) : null
                ],
                true,
                null,
                null,
                []
            );
        }
    }

    /**
     * Log checkout exception with comprehensive context.
     */
    protected function logCheckoutException(Request $request, \Throwable $exception): void
    {
        $routeName = $request->route()?->getName();
        $method = $request->getMethod();

        // Get checkout context
        $checkoutContext = $this->getCheckoutContext($request);

        // Determine error severity
        $severity = $this->determineErrorSeverity($exception);

        $errorData = [
            'exception_class' => get_class($exception),
            'exception_message' => $exception->getMessage(),
            'exception_file' => $exception->getFile(),
            'exception_line' => $exception->getLine(),
            'exception_code' => $exception->getCode(),
            'route_name' => $routeName,
            'method' => $method,
            'request_uri' => $request->getRequestUri(),
            'user_authenticated' => Auth::check(),
            'user_id' => Auth::id(),
            'customer_type' => Auth::check() ? 'registered' : 'guest',
            'cart_type' => Auth::check() ? 'user' : 'session',
            'session_id' => $request->session()->getId(),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'referer' => $request->header('referer'),
            'request_data' => $this->sanitizeRequestData($request->all()),
            'checkout_context' => $checkoutContext,
            'severity' => $severity,
            'stack_trace' => $exception->getTraceAsString(),
            'previous_exception' => $exception->getPrevious() ? [
                'class' => get_class($exception->getPrevious()),
                'message' => $exception->getPrevious()->getMessage(),
                'file' => $exception->getPrevious()->getFile(),
                'line' => $exception->getPrevious()->getLine(),
            ] : null
        ];

        // Log the checkout exception
        $this->activityLogger->logCheckoutActivity(
            'exception_occurred',
            $errorData,
            false,
            "Checkout exception: {$exception->getMessage()}",
            null,
            []
        );

        // Log as security event if it's a potential security issue
        if ($this->isPotentialSecurityIssue($exception)) {
            $this->activityLogger->logSecurityEvent(
                'checkout_security_exception',
                [
                    'exception_type' => get_class($exception),
                    'route' => $routeName,
                    'ip_address' => $request->ip(),
                    'user_id' => Auth::id(),
                    'checkout_context' => $checkoutContext
                ],
                $severity
            );
        }

        // Standard Laravel logging
        Log::error('Checkout Exception', $errorData);
    }

    /**
     * Track checkout error in visitor analytics.
     */
    protected function trackCheckoutError(Request $request, \Throwable $exception): void
    {
        $routeName = $request->route()?->getName();
        
        // Track the error
        $this->visitorAnalytics->trackError('checkout_exception', [
            'exception_class' => get_class($exception),
            'route_name' => $routeName,
            'method' => $request->getMethod(),
            'checkout_step' => $this->determineCheckoutStep($request),
        ]);

        // Track checkout abandonment if this was during an active checkout
        if ($this->isActiveCheckoutStep($request)) {
            $this->visitorAnalytics->trackCheckoutAbandonment(
                $this->determineCheckoutStep($request),
                [
                    'abandonment_reason' => 'exception',
                    'exception_type' => get_class($exception),
                    'route_name' => $routeName
                ]
            );
        }
    }

    /**
     * Get checkout context from the request.
     */
    protected function getCheckoutContext(Request $request): array
    {
        $context = [
            'step' => $this->determineCheckoutStep($request),
            'has_cart' => $request->session()->has('cart'),
            'form_data_present' => !empty($request->all()),
        ];

        // Add order context if available
        if ($request->route('order')) {
            $order = $request->route('order');
            $context['order_id'] = $order->id ?? null;
            $context['order_uuid'] = $order->uuid ?? null;
            $context['order_status'] = $order->status ?? null;
            $context['payment_status'] = $order->payment_status ?? null;
        }

        return $context;
    }

    /**
     * Determine checkout step from request.
     */
    protected function determineCheckoutStep(Request $request): string
    {
        $routeName = $request->route()?->getName();
        
        return match($routeName) {
            'checkout.index' => 'checkout_form',
            'checkout.process' => 'form_processing',
            'checkout.payment' => 'payment_page',
            'checkout.process-payment' => 'payment_processing',
            'checkout.success' => 'completion',
            'checkout.offline-payment' => 'offline_payment',
            default => 'unknown'
        };
    }

    /**
     * Check if this is an active checkout step.
     */
    protected function isActiveCheckoutStep(Request $request): bool
    {
        $step = $this->determineCheckoutStep($request);
        return !in_array($step, ['completion', 'unknown']);
    }

    /**
     * Determine error severity.
     */
    protected function determineErrorSeverity(\Throwable $exception): string
    {
        // Critical errors that affect payment processing
        if ($exception instanceof \Stripe\Exception\ApiErrorException ||
            str_contains($exception->getMessage(), 'payment') ||
            str_contains($exception->getMessage(), 'charge')) {
            return 'critical';
        }

        // High severity for validation and database errors
        if ($exception instanceof \Illuminate\Validation\ValidationException ||
            $exception instanceof \Illuminate\Database\QueryException) {
            return 'high';
        }

        // Medium severity for general application errors
        if ($exception instanceof \Symfony\Component\HttpKernel\Exception\HttpException) {
            return $exception->getStatusCode() >= 500 ? 'high' : 'medium';
        }

        return 'medium';
    }

    /**
     * Check if exception indicates potential security issue.
     */
    protected function isPotentialSecurityIssue(\Throwable $exception): bool
    {
        $securityIndicators = [
            'sql injection',
            'xss',
            'csrf',
            'unauthorized',
            'forbidden',
            'authentication',
            'permission denied',
            'access denied'
        ];

        $message = strtolower($exception->getMessage());
        
        foreach ($securityIndicators as $indicator) {
            if (str_contains($message, $indicator)) {
                return true;
            }
        }

        return $exception instanceof \Illuminate\Auth\AuthenticationException ||
               $exception instanceof \Illuminate\Auth\Access\AuthorizationException ||
               $exception instanceof \Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException ||
               $exception instanceof \Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
    }

    /**
     * Sanitize request data for logging.
     */
    protected function sanitizeRequestData(array $data): array
    {
        $sensitiveFields = [
            'password',
            'password_confirmation',
            'stripe_token',
            'payment_token',
            'card_number',
            'cvv',
            'card_cvc',
            'card_exp_month',
            'card_exp_year'
        ];

        $sanitized = $data;
        foreach ($sensitiveFields as $field) {
            if (isset($sanitized[$field])) {
                $sanitized[$field] = '[REDACTED]';
            }
        }

        return $sanitized;
    }
}
