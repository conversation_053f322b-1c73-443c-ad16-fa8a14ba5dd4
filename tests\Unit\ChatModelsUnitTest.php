<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;
use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\ChatSystemSetting;
use PHPUnit\Framework\Attributes\Test;

class ChatModelsUnitTest extends TestCase
{
    /**
     * Test ChatRoom model basic functionality.
     */
    #[Test]
    public function test_chat_room_model_exists(): void
    {
        $this->assertTrue(class_exists(ChatRoom::class));
    }

    /**
     * Test ChatMessage model basic functionality.
     */
    #[Test]
    public function test_chat_message_model_exists(): void
    {
        $this->assertTrue(class_exists(ChatMessage::class));
    }

    /**
     * Test ChatSystemSetting model basic functionality.
     */
    #[Test]
    public function test_chat_system_setting_model_exists(): void
    {
        $this->assertTrue(class_exists(ChatSystemSetting::class));
    }

    /**
     * Test ChatRoom priority labels.
     */
    #[Test]
    public function test_chat_room_priority_labels(): void
    {
        $room = new ChatRoom(['priority' => 1]);
        $this->assertEquals('Low', $room->priority_label);

        $room = new ChatRoom(['priority' => 2]);
        $this->assertEquals('Medium', $room->priority_label);

        $room = new ChatRoom(['priority' => 3]);
        $this->assertEquals('High', $room->priority_label);

        $room = new ChatRoom(['priority' => 4]);
        $this->assertEquals('Urgent', $room->priority_label);
    }

    /**
     * Test ChatMessage sender types.
     */
    #[Test]
    public function test_chat_message_sender_types(): void
    {
        // Test that the getSenderTypeAttribute method exists
        $message = new ChatMessage();
        $this->assertTrue(method_exists($message, 'getSenderTypeAttribute'));

        // Test AI generated logic by checking the method directly
        $message->is_ai_generated = true;
        // For unit test, we'll just verify the method exists and can handle the logic
        // The actual database-dependent logic is tested in integration tests
        $this->assertTrue($message->is_ai_generated);
    }

    /**
     * Test ChatRoom fillable attributes.
     */
    #[Test]
    public function test_chat_room_fillable_attributes(): void
    {
        $expectedFillable = [
            'uuid',
            'type',
            'status',
            'title',
            'visitor_info',
            'metadata',
            'priority',
            'language',
            'closed_at',
        ];

        $room = new ChatRoom();
        $this->assertEquals($expectedFillable, $room->getFillable());
    }

    /**
     * Test ChatMessage fillable attributes.
     */
    #[Test]
    public function test_chat_message_fillable_attributes(): void
    {
        $expectedFillable = [
            'uuid',
            'chat_room_id',
            'user_id',
            'message_type',
            'content',
            'metadata',
            'is_ai_generated',
            'ai_confidence',
            'ai_model',
            'reply_to_message_id',
            'is_edited',
            'edit_count',
        ];

        $message = new ChatMessage();
        $this->assertEquals($expectedFillable, $message->getFillable());
    }

    /**
     * Test ChatSystemSetting typed values.
     */
    #[Test]
    public function test_chat_system_setting_typed_values(): void
    {
        // Test boolean type
        $setting = new ChatSystemSetting([
            'setting_type' => 'boolean',
            'setting_value' => '1'
        ]);
        $this->assertTrue($setting->typed_value);

        $setting = new ChatSystemSetting([
            'setting_type' => 'boolean',
            'setting_value' => '0'
        ]);
        $this->assertFalse($setting->typed_value);

        // Test integer type
        $setting = new ChatSystemSetting([
            'setting_type' => 'integer',
            'setting_value' => '42'
        ]);
        $this->assertEquals(42, $setting->typed_value);

        // Test string type
        $setting = new ChatSystemSetting([
            'setting_type' => 'string',
            'setting_value' => 'test value'
        ]);
        $this->assertEquals('test value', $setting->typed_value);
    }

    /**
     * Test ChatRoom status constants.
     */
    #[Test]
    public function test_chat_room_status_constants(): void
    {
        $room = new ChatRoom();
        
        // Test that status constants exist (if defined in the model)
        $this->assertIsString($room->getTable());
        $this->assertEquals('chat_rooms', $room->getTable());
    }

    /**
     * Test ChatMessage type constants.
     */
    #[Test]
    public function test_chat_message_type_constants(): void
    {
        $message = new ChatMessage();
        
        // Test that the model has the correct table name
        $this->assertEquals('chat_messages', $message->getTable());
    }

    /**
     * Test model casts.
     */
    #[Test]
    public function test_model_casts(): void
    {
        $room = new ChatRoom();
        $expectedCasts = [
            'visitor_info' => 'array',
            'metadata' => 'array',
            'closed_at' => 'datetime',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];

        foreach ($expectedCasts as $attribute => $cast) {
            $this->assertEquals($cast, $room->getCasts()[$attribute] ?? null);
        }

        $message = new ChatMessage();
        $expectedMessageCasts = [
            'metadata' => 'array',
            'is_ai_generated' => 'boolean',
            'ai_confidence' => 'decimal:2',
            'is_edited' => 'boolean',
            'edit_count' => 'integer',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];

        foreach ($expectedMessageCasts as $attribute => $cast) {
            $this->assertEquals($cast, $message->getCasts()[$attribute] ?? null);
        }
    }
}
