<?php

namespace Database\Factories;

use App\Models\ChatWebhook;
use App\Models\ChatWebhookDelivery;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ChatWebhookDelivery>
 */
class ChatWebhookDeliveryFactory extends Factory
{
    protected $model = ChatWebhookDelivery::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'chat_webhook_id' => ChatWebhook::factory(),
            'event_type' => $this->faker->randomElement([
                'message.sent',
                'message.received',
                'room.created',
                'room.closed',
                'room.assigned',
                'user.joined',
                'user.left',
                'file.uploaded',
                'rating.submitted',
            ]),
            'payload' => [
                'event' => $this->faker->randomElement(['message.sent', 'room.created']),
                'timestamp' => now()->toISOString(),
                'data' => [
                    'id' => $this->faker->numberBetween(1, 1000),
                    'content' => $this->faker->sentence(),
                ],
                'version' => '1.0',
            ],
            'status' => $this->faker->randomElement(['pending', 'delivered', 'failed', 'retrying']),
            'attempts' => $this->faker->numberBetween(0, 3),
            'response_status' => $this->faker->optional()->randomElement([200, 201, 400, 404, 500]),
            'response_body' => $this->faker->optional()->randomElement([
                '{"success": true}',
                '{"error": "Bad request"}',
                '{"message": "Not found"}',
            ]),
            'error_message' => $this->faker->optional()->sentence(),
            'delivered_at' => $this->faker->optional()->dateTimeBetween('-1 week', 'now'),
            'next_retry_at' => $this->faker->optional()->dateTimeBetween('now', '+1 hour'),
        ];
    }

    /**
     * Indicate that the delivery was successful.
     */
    public function delivered(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'delivered',
            'response_status' => 200,
            'response_body' => '{"success": true}',
            'delivered_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'error_message' => null,
            'next_retry_at' => null,
        ]);
    }

    /**
     * Indicate that the delivery failed.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'response_status' => $this->faker->randomElement([400, 404, 500]),
            'response_body' => '{"error": "Delivery failed"}',
            'error_message' => $this->faker->sentence(),
            'delivered_at' => null,
            'next_retry_at' => null,
        ]);
    }

    /**
     * Indicate that the delivery is pending retry.
     */
    public function retrying(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'retrying',
            'attempts' => $this->faker->numberBetween(1, 2),
            'error_message' => $this->faker->sentence(),
            'next_retry_at' => $this->faker->dateTimeBetween('now', '+1 hour'),
        ]);
    }

    /**
     * Indicate that the delivery is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'attempts' => 0,
            'response_status' => null,
            'response_body' => null,
            'error_message' => null,
            'delivered_at' => null,
            'next_retry_at' => null,
        ]);
    }
}
