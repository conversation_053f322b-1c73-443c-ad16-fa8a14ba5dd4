# AI Provider System Documentation

## Overview

The ChiSolution Live Chat system now includes a comprehensive multi-provider AI system that supports multiple AI providers with automatic fallback, rate limiting, caching, and advanced features.

## Supported AI Providers

### 1. OpenAI
- **Models**: GPT-4.1, GPT-4.1 Mini, GPT-4o, GPT-4o Mini
- **Features**: Vision, Function Calling, Streaming
- **Pricing**: Competitive token-based pricing
- **Use Cases**: General chat, complex reasoning, code assistance

### 2. Anthropic Claude
- **Models**: <PERSON> 4, <PERSON> 4, <PERSON> 4
- **Features**: Large context windows, safety-focused
- **Pricing**: Token-based with different tiers
- **Use Cases**: Long conversations, content analysis, safety-critical applications

### 3. Google Gemini
- **Models**: Gemini 2.5 Flash, Gemini 2.5 Pro, Gemini 2.5 Pro Thinking
- **Features**: Multimodal, thinking mode, fast responses
- **Pricing**: Competitive with free tier
- **Use Cases**: Fast responses, multimodal content, reasoning tasks

### 4. xA<PERSON>rok
- **Models**: Grok 3, Grok 2, Grok 2 Mini
- **Features**: Real-time information, conversational AI
- **Pricing**: Subscription-based
- **Use Cases**: Current events, conversational AI, real-time data

## Key Features

### Multi-Provider Support
- **Provider Selection**: Choose default provider with automatic fallback
- **Model Selection**: Specific model selection per provider
- **Load Balancing**: Distribute requests across providers
- **Cost Optimization**: Route to most cost-effective provider

### Advanced Capabilities
- **Sentiment Analysis**: AI-powered sentiment detection
- **Text Translation**: Multi-language translation support
- **Conversation Summarization**: Automatic conversation summaries
- **Topic Extraction**: Identify key conversation topics
- **Intent Recognition**: Understand user intentions

### Management Features
- **Usage Statistics**: Track tokens, costs, and performance
- **Provider Testing**: Test individual providers and models
- **Performance Comparison**: Compare providers side-by-side
- **Rate Limiting**: Prevent API quota exhaustion
- **Response Caching**: Cache responses for efficiency

## Configuration

### Environment Variables
```env
# AI Provider Configuration
AI_DEFAULT_PROVIDER=openai
AI_FALLBACK_ENABLED=true
AI_RATE_LIMITING_ENABLED=true
AI_CACHING_ENABLED=true

# OpenAI Configuration
OPENAI_API_KEY=your_openai_key
OPENAI_BASE_URL=https://api.openai.com/v1

# Anthropic Configuration
ANTHROPIC_API_KEY=your_anthropic_key
ANTHROPIC_BASE_URL=https://api.anthropic.com

# Google AI Configuration
GOOGLE_AI_API_KEY=your_google_key
GOOGLE_AI_BASE_URL=https://generativelanguage.googleapis.com/v1beta

# xAI Configuration
XAI_API_KEY=your_xai_key
XAI_BASE_URL=https://api.x.ai/v1
```

### Database Settings
The system stores AI provider settings in the `chat_system_settings` table:
- `ai_default_provider`: Default AI provider
- `ai_fallback_enabled`: Enable provider fallback
- `ai_rate_limiting_enabled`: Enable rate limiting
- `ai_caching_enabled`: Enable response caching
- `ai_openai_model`: Default OpenAI model
- `ai_anthropic_model`: Default Anthropic model
- `ai_google_model`: Default Google model
- `ai_xai_model`: Default xAI model

## API Endpoints

### Admin Endpoints
- `GET /admin/chat/ai` - AI configuration dashboard
- `POST /admin/chat/ai/test-provider` - Test AI provider
- `GET /admin/chat/ai/providers` - Get available providers
- `GET /admin/chat/ai/usage-stats` - Get usage statistics
- `POST /admin/chat/ai/provider-settings` - Update provider settings

### Public API Endpoints
- `POST /api/v1/chat/ai/generate` - Generate AI response
- `POST /api/v1/chat/ai/sentiment` - Analyze sentiment
- `POST /api/v1/chat/ai/translate` - Translate text
- `POST /api/v1/chat/ai/summarize` - Summarize conversation
- `POST /api/v1/chat/ai/topics` - Extract topics
- `GET /api/v1/chat/ai/providers` - Get available providers

## Usage Examples

### Generate AI Response
```php
$chatAI = app(ChatAIService::class);
$response = $chatAI->generateResponseWithProvider(
    'Hello, I need help with my order',
    $chatRoom,
    ['temperature' => 0.7, 'max_tokens' => 1000],
    'openai',
    'gpt-4.1-mini'
);
```

### Analyze Sentiment
```php
$result = $chatAI->analyzeSentimentWithAI(
    'I love this product!',
    'anthropic',
    'claude-sonnet-4-20250514'
);
```

### Translate Text
```php
$translation = $chatAI->translateWithAI(
    'Hello, how are you?',
    'Spanish',
    'English',
    'google',
    'gemini-2.5-flash'
);
```

## Admin Dashboard Features

### Provider Management
- View all available providers and their status
- Test individual providers with custom messages
- Compare provider performance side-by-side
- Monitor usage statistics and costs
- Configure default providers and models

### Settings Configuration
- Enable/disable provider fallback
- Configure rate limiting settings
- Enable/disable response caching
- Set default models for each provider
- Manage API keys and endpoints

### Analytics & Monitoring
- Real-time usage statistics
- Cost tracking per provider
- Performance metrics
- Error rate monitoring
- Response time analysis

## Security & Best Practices

### API Key Management
- Store API keys in environment variables
- Use different keys for different environments
- Rotate keys regularly
- Monitor API key usage

### Rate Limiting
- Implement per-provider rate limits
- Use exponential backoff for retries
- Monitor quota usage
- Set up alerts for quota exhaustion

### Error Handling
- Graceful fallback to alternative providers
- Comprehensive error logging
- User-friendly error messages
- Automatic retry mechanisms

### Data Privacy
- No sensitive data in logs
- Secure API communication (HTTPS)
- Data encryption at rest
- GDPR compliance features

## Monitoring & Maintenance

### Health Checks
- Provider availability monitoring
- Response time tracking
- Error rate monitoring
- Cost threshold alerts

### Performance Optimization
- Response caching strategies
- Request batching
- Connection pooling
- Load balancing

### Troubleshooting
- Comprehensive logging
- Error tracking
- Performance profiling
- Debug mode for development

## Future Enhancements

### Planned Features
- Advanced routing algorithms
- Custom model fine-tuning
- Voice integration
- Image analysis capabilities
- Real-time streaming responses

### Integration Opportunities
- CRM system integration
- Knowledge base integration
- Analytics platform integration
- Third-party AI services

---

This AI provider system provides a robust, scalable, and feature-rich foundation for AI-powered chat capabilities in the ChiSolution platform.
