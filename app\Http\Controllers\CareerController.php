<?php

namespace App\Http\Controllers;

use App\Models\Job;
use App\Models\JobApplication;
use App\Services\ActivityLogger;
use App\Services\VisitorAnalytics;
use App\Services\EmailNotificationService;
use App\Services\FileService;
use App\Services\ImageService;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;

class CareerController extends Controller
{
    protected ActivityLogger $activityLogger;
    protected VisitorAnalytics $visitorAnalytics;

    /**
     * Create a new controller instance.
     */
    public function __construct(ActivityLogger $activityLogger, VisitorAnalytics $visitorAnalytics)
    {
        $this->activityLogger = $activityLogger;
        $this->visitorAnalytics = $visitorAnalytics;
    }
    /**
     * Display the careers page with job listings.
     */
    public function index(Request $request): View
    {
        $query = Job::active()->with(['jobApplications']);

        // Filter by employment type
        if ($request->filled('employment_type') && $request->employment_type !== 'all') {
            $query->where('employment_type', $request->employment_type);
        }

        // Filter by experience level
        if ($request->filled('experience_level') && $request->experience_level !== 'all') {
            $query->where('experience_level', $request->experience_level);
        }

        // Filter by department
        if ($request->filled('department') && $request->department !== 'all') {
            $query->where('department', $request->department);
        }

        // Filter by location
        if ($request->filled('location') && $request->location !== 'all') {
            $query->where('location', $request->location);
        }

        // Search functionality
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%")
                  ->orWhere('department', 'like', "%{$searchTerm}%")
                  ->orWhere('location', 'like', "%{$searchTerm}%");
            });
        }

        $jobs = $query->ordered()->paginate(12);

        // Get filter options
        $employmentTypes = Job::getEmploymentTypeOptions();
        $experienceLevels = Job::getExperienceLevelOptions();
        $departments = Job::active()->distinct()->pluck('department')->filter()->sort();
        $locations = Job::active()->distinct()->pluck('location')->filter()->sort();

        return view('careers.index', compact(
            'jobs',
            'employmentTypes',
            'experienceLevels',
            'departments',
            'locations'
        ));
    }

    /**
     * Display a specific job.
     */
    public function show(string $locale, Job $job): View
    {
        // Check if job is active
        if (!$job->is_active || $job->is_deleted) {
            abort(404);
        }

        // Log job viewing activity
        $this->activityLogger->logJobView($job->id, $job->title);

        // Get related jobs
        $relatedJobs = Job::active()
            ->where('id', '!=', $job->id)
            ->where(function ($query) use ($job) {
                $query->where('department', $job->department)
                      ->orWhere('employment_type', $job->employment_type);
            })
            ->limit(3)
            ->get();

        return view('careers.show', compact('job', 'relatedJobs'));
    }

    /**
     * Show the job application form.
     */
    public function apply(string $locale, Job $job): View
    {
        // Check if job is active and accepting applications
        if (!$job->is_active || $job->is_deleted) {
            abort(404);
        }

        if ($job->application_deadline && $job->application_deadline->isPast()) {
            abort(410, 'Application deadline has passed.');
        }

        $user = Auth::user();

        // Pre-fill contact information for authenticated users
        $contactInfo = [];
        if ($user) {
            $contactInfo = [
                'first_name' => explode(' ', $user->name)[0] ?? '',
                'last_name' => explode(' ', $user->name, 2)[1] ?? '',
                'email' => $user->email,
                'phone' => $user->phone ?? '',
                'country' => $user->country ?? 'South Africa',
            ];
        }

        // Track job application form visit
        $this->visitorAnalytics->trackPageVisit(
            "Apply for {$job->title}",
            [
                'job_id' => $job->id,
                'job_title' => $job->title,
                'job_department' => $job->department,
                'job_location' => $job->location,
                'user_id' => $user?->id,
                'is_authenticated' => $user !== null,
            ]
        );

        // Track journey step
        $this->visitorAnalytics->trackJourneyStep(
            "Job Application Form: {$job->title}",
            'form_view',
            [
                'form_type' => 'job_application',
                'job_id' => $job->id,
                'job_title' => $job->title,
                'job_department' => $job->department,
                'is_authenticated' => $user !== null,
            ]
        );

        // Update lead score for viewing job application
        $this->visitorAnalytics->updateLeadScore(
            'form_view',
            ['form_type' => 'job_application', 'job_department' => $job->department]
        );

        return view('careers.apply', compact('job', 'user', 'contactInfo'));
    }

    /**
     * Store a job application.
     */
    public function storeApplication(Request $request, string $locale, Job $job): RedirectResponse
    {
        // Check if job is active and accepting applications
        if (!$job->is_active || $job->is_deleted) {
            abort(404);
        }

        if ($job->application_deadline && $job->application_deadline->isPast()) {
            abort(410, 'Application deadline has passed.');
        }

        $isAuthenticated = Auth::check();

        // Validation rules
        $rules = [
            // Personal Information
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'date_of_birth' => 'nullable|date|before:today',
            'nationality' => 'nullable|string|max:255',
            'id_number' => 'nullable|string|max:20',

            // Contact Information
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:255',
            'province' => 'nullable|string|max:255',
            'postal_code' => 'nullable|string|max:10',
            'country' => 'required|string|max:255',

            // Professional Information
            'cover_letter' => 'required|string|max:5000',
            'experience_summary' => 'required|string|max:3000',
            'current_position' => 'nullable|string|max:255',
            'current_company' => 'nullable|string|max:255',
            'current_salary' => 'nullable|numeric|min:0',
            'expected_salary' => 'nullable|numeric|min:0',
            'notice_period' => 'nullable|string|max:255',
            'available_start_date' => 'nullable|date|after:today',

            // Education
            'highest_qualification' => 'required|string|max:255',
            'institution' => 'nullable|string|max:255',
            'field_of_study' => 'nullable|string|max:255',
            'graduation_year' => 'nullable|integer|min:1950|max:' . (date('Y') + 10),

            // Skills and Preferences
            'skills' => 'nullable|array',
            'skills.*' => 'string|max:255',
            'languages' => 'nullable|array',
            'languages.*' => 'string|max:255',
            'willing_to_relocate' => 'nullable|boolean',
            'willing_to_travel' => 'nullable|boolean',
            'has_drivers_license' => 'nullable|boolean',
            'has_own_transport' => 'nullable|boolean',

            // Additional Information
            'additional_notes' => 'nullable|string|max:2000',
            'newsletter_signup' => 'nullable|boolean',
            'terms_accepted' => 'required|boolean|accepted',

            // File attachments - Enhanced security validation
            'attachments' => 'nullable|array|max:10',
            'attachments.*' => 'file|max:25600|mimes:pdf,doc,docx,jpg,jpeg,png,txt', // 25MB max per file, specific types only
        ];

        $validated = $request->validate($rules);

        // Handle file uploads with comprehensive security
        $attachmentData = [];
        $fileUploadResults = [];
        if ($request->hasFile('attachments')) {
            $userId = $isAuthenticated ? Auth::id() : 'public';

            foreach ($request->file('attachments') as $file) {
                try {
                    // Use FileService for documents and ImageService for images
                    $mimeType = $file->getMimeType();

                    if (str_starts_with($mimeType, 'image/')) {
                        // Process image files with ImageService
                        $result = app(ImageService::class)->processUploadedImage($file, [
                            'folder' => 'job-applications/' . $userId . '/' . date('Y/m'),
                            'create_variants' => false,
                            'convert_to_webp' => false,
                            'scan_for_viruses' => true,
                        ]);
                    } else {
                        // Process document files with FileService
                        $result = app(FileService::class)->processUploadedFile($file, [
                            'folder' => 'job-applications/' . $userId . '/' . date('Y/m'),
                            'scan_for_viruses' => true,
                            'scan_content' => true,
                        ]);
                    }

                    if ($result['success']) {
                        $attachmentData[] = [
                            'original_name' => $file->getClientOriginalName(),
                            'stored_name' => $result['filename'] ?? $result['stored_name'] ?? 'unknown',
                            'path' => $result['path'],
                            'size' => $file->getSize(),
                            'mime_type' => $mimeType,
                            'scan_results' => $result['scan_results'] ?? null,
                        ];

                        $fileUploadResults[] = [
                            'original_name' => $file->getClientOriginalName(),
                            'size' => $file->getSize(),
                            'mime_type' => $mimeType,
                            'virus_scan_passed' => $result['virus_scan_passed'] ?? true,
                            'scan_engine' => $result['scan_engine'] ?? 'unknown',
                            'success' => true,
                        ];
                    } else {
                        $fileUploadResults[] = [
                            'original_name' => $file->getClientOriginalName(),
                            'size' => $file->getSize(),
                            'mime_type' => $mimeType,
                            'virus_scan_passed' => false,
                            'scan_engine' => 'unknown',
                            'success' => false,
                            'error' => $result['message'] ?? 'Unknown error',
                        ];

                        // Log failed file upload and return error
                        $this->activityLogger->logJobApplication(
                            $job->id,
                            $job->title,
                            false,
                            'File upload failed: ' . ($result['message'] ?? 'Unknown error'),
                            $validated,
                            $fileUploadResults
                        );

                        return redirect()->back()
                            ->withInput()
                            ->withErrors(['attachments' => 'Error processing file "' . $file->getClientOriginalName() . '": ' . ($result['message'] ?? 'Unknown error')]);
                    }
                } catch (\Exception $e) {
                    $fileUploadResults[] = [
                        'original_name' => $file->getClientOriginalName(),
                        'size' => $file->getSize(),
                        'mime_type' => $file->getMimeType(),
                        'virus_scan_passed' => false,
                        'scan_engine' => 'unknown',
                        'success' => false,
                        'error' => $e->getMessage(),
                    ];

                    // Log exception and return error
                    $this->activityLogger->logJobApplication(
                        $job->id,
                        $job->title,
                        false,
                        'File upload exception: ' . $e->getMessage(),
                        $validated,
                        $fileUploadResults
                    );

                    return redirect()->back()
                        ->withInput()
                        ->withErrors(['attachments' => 'Error processing file "' . $file->getClientOriginalName() . '": ' . $e->getMessage()]);
                }
            }
        }

        // Prepare data for storage
        $applicationData = $validated;
        $applicationData['job_id'] = $job->id;
        $applicationData['user_id'] = $isAuthenticated ? Auth::id() : null;
        $applicationData['attachments'] = $attachmentData;

        // Add tracking data
        $applicationData['ip_address'] = $request->ip();
        $applicationData['user_agent'] = $request->userAgent();
        $applicationData['referrer'] = $request->header('referer');

        // Create job application
        $application = JobApplication::create($applicationData);

        // Send email notifications
        $emailService = new EmailNotificationService();
        $emailService->sendJobApplicationReceived($application);

        // Log successful job application
        $this->activityLogger->logJobApplication(
            $job->id,
            $job->title,
            true,
            null,
            $validated,
            $fileUploadResults
        );

        $message = 'Your job application has been submitted successfully! We will review it and get back to you soon.';
        if (!empty($attachmentData)) {
            $fileCount = count($attachmentData);
            $message .= " Your {$fileCount} file(s) have been securely uploaded and scanned.";
        }

        // Return appropriate response based on authentication status
        if ($isAuthenticated) {
            return redirect()->route('careers.my-applications')->with('success', $message);
        } else {
            // For public applications, redirect to success page with reference number
            return redirect()->route('careers.application-success', $application->reference_number)
                ->with('success', $message);
        }
    }

    /**
     * Show success page for public applications.
     */
    public function applicationSuccess(string $referenceNumber): View
    {
        $application = JobApplication::where('reference_number', $referenceNumber)
            ->with(['job'])
            ->firstOrFail();

        return view('careers.application-success', compact('application'));
    }

    /**
     * Show user's job applications (authenticated users only).
     */
    public function myApplications(): View
    {
        $this->middleware('auth');

        $applications = Auth::user()->jobApplications()
            ->with(['job'])
            ->latest()
            ->paginate(10);

        return view('careers.my-applications', compact('applications'));
    }

    /**
     * Check application status (public).
     */
    public function checkStatus(Request $request)
    {
        $request->validate([
            'reference_number' => 'required|string',
            'email' => 'required|email',
        ]);

        $application = JobApplication::where('reference_number', $request->reference_number)
            ->where('email', $request->email)
            ->with(['job'])
            ->first();

        $found = $application !== null;
        $referenceNumber = $request->reference_number;
        $email = $request->email;

        // Get client information for analytics
        $clientInfo = [
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'is_ajax' => $request->ajax(),
            'reference_number' => $referenceNumber,
            'email_domain' => substr($email, strpos($email, '@') + 1),
            'application_found' => $found,
        ];

        // Log application status check in activity logger
        $this->activityLogger->logApplicationStatusCheck(
            $referenceNumber,
            $found,
            'job'
        );

        // Track form interaction in visitor analytics
        $this->visitorAnalytics->trackFormInteraction(
            'career_status_check_form',
            'submit',
            $found,
            array_merge($clientInfo, [
                'application_id' => $found ? $application->id : null,
                'job_id' => $found ? $application->job_id : null,
                'job_title' => $found ? $application->job->title : null,
                'is_authenticated' => auth()->check(),
            ])
        );

        if (!$application) {
            // Update visitor analytics for failed lookup
            $this->visitorAnalytics->updateLeadScore('career_status_check_failed', [
                'application_found' => false,
                'reference_number_format' => strlen($referenceNumber),
            ]);

            return redirect()->back()->withErrors(['reference_number' => 'Application not found with the provided details.']);
        }

        // Update visitor analytics lead score for successful status check
        $this->visitorAnalytics->updateLeadScore('career_status_check', [
            'application_found' => true,
            'job_title' => $application->job->title,
            'application_status' => $application->status,
            'is_returning_check' => true, // Indicates user is following up
        ]);

        return view('careers.status', compact('application'));
    }
}
