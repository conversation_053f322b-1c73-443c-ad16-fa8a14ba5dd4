<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('newsletter_subscription_histories')) {
            Schema::create('newsletter_subscription_histories', function (Blueprint $table) {
                $table->id();
                $table->uuid('uuid')->unique();
                $table->unsignedBigInteger('newsletter_subscription_id');
                $table->string('action'); // subscribed, unsubscribed, resubscribed, verified, deactivated, activated
                $table->string('status_from')->nullable(); // previous status
                $table->string('status_to')->nullable(); // new status
                $table->text('description')->nullable();
                $table->json('metadata')->nullable(); // additional data like IP, user agent, admin user, etc.
                $table->string('triggered_by')->nullable(); // user, admin, system
                $table->unsignedBigInteger('admin_user_id')->nullable();
                $table->ipAddress('ip_address')->nullable();
                $table->text('user_agent')->nullable();
                $table->timestamps();

                $table->foreign('newsletter_subscription_id', 'ns_hist_sub_fk')->references('id')->on('newsletter_subscriptions')->onDelete('cascade');
                $table->foreign('admin_user_id', 'ns_hist_admin_fk')->references('id')->on('users')->onDelete('set null');

                $table->index(['newsletter_subscription_id', 'created_at'], 'ns_hist_sub_created_idx');
                $table->index('action', 'ns_hist_action_idx');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('newsletter_subscription_histories');
    }
};
