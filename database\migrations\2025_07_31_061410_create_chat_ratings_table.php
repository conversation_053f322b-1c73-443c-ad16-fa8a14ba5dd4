<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_ratings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('chat_room_id')->constrained('chat_rooms')->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null')
                  ->comment('NULL for anonymous visitors');
            $table->tinyInteger('rating')->unsigned()->comment('Rating 1-5');
            $table->text('feedback')->nullable();
            $table->json('rating_categories')->nullable()->comment('Detailed rating breakdown');
            $table->foreignId('staff_user_id')->nullable()->constrained('users')->onDelete('set null')
                  ->comment('Staff member being rated');
            $table->boolean('is_anonymous')->default(false);
            $table->timestamps();
            
            // Unique constraint to prevent multiple ratings per room per user
            $table->unique(['chat_room_id', 'user_id'], 'unique_room_rating');
            
            // Performance indexes
            $table->index(['rating', 'created_at'], 'idx_rating_created');
            $table->index(['staff_user_id', 'rating'], 'idx_staff_rating');
            $table->index('created_at', 'idx_created_at');
            
            // Note: Check constraint for rating range (1-5) will be enforced at application level
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_ratings');
    }
};
