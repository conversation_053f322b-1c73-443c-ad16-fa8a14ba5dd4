<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Monitoring Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration for monitoring the Live Chat AI System
    | including health checks, performance monitoring, and alerting.
    |
    */

    'enabled' => env('MONITORING_ENABLED', true),

    'health_checks' => [
        /*
        |--------------------------------------------------------------------------
        | Health Check Configuration
        |--------------------------------------------------------------------------
        |
        | Configure various health checks for the system components.
        |
        */
        'enabled' => env('HEALTH_CHECKS_ENABLED', true),
        'endpoint' => '/health',
        'cache_ttl' => env('HEALTH_CHECK_CACHE_TTL', 60), // seconds
        
        'checks' => [
            'database' => [
                'enabled' => true,
                'timeout' => 5, // seconds
                'critical' => true,
            ],
            'redis' => [
                'enabled' => true,
                'timeout' => 3,
                'critical' => true,
            ],
            'storage' => [
                'enabled' => true,
                'timeout' => 5,
                'critical' => false,
            ],
            'queue' => [
                'enabled' => true,
                'timeout' => 10,
                'critical' => false,
            ],
            'ai_service' => [
                'enabled' => true,
                'timeout' => 15,
                'critical' => false,
            ],
            'broadcasting' => [
                'enabled' => true,
                'timeout' => 5,
                'critical' => false,
            ],
        ],
    ],

    'performance' => [
        /*
        |--------------------------------------------------------------------------
        | Performance Monitoring
        |--------------------------------------------------------------------------
        |
        | Configure performance monitoring thresholds and metrics.
        |
        */
        'enabled' => env('PERFORMANCE_MONITORING_ENABLED', true),
        
        'thresholds' => [
            'response_time' => [
                'warning' => env('RESPONSE_TIME_WARNING', 1.0), // seconds
                'critical' => env('RESPONSE_TIME_CRITICAL', 3.0),
            ],
            'memory_usage' => [
                'warning' => env('MEMORY_WARNING', 80), // percentage
                'critical' => env('MEMORY_CRITICAL', 95),
            ],
            'cpu_usage' => [
                'warning' => env('CPU_WARNING', 70), // percentage
                'critical' => env('CPU_CRITICAL', 90),
            ],
            'disk_usage' => [
                'warning' => env('DISK_WARNING', 80), // percentage
                'critical' => env('DISK_CRITICAL', 95),
            ],
            'queue_size' => [
                'warning' => env('QUEUE_SIZE_WARNING', 100),
                'critical' => env('QUEUE_SIZE_CRITICAL', 500),
            ],
            'active_connections' => [
                'warning' => env('CONNECTIONS_WARNING', 800),
                'critical' => env('CONNECTIONS_CRITICAL', 950),
            ],
        ],
        
        'metrics_retention' => [
            'real_time' => 3600, // 1 hour
            'hourly' => 86400 * 7, // 1 week
            'daily' => 86400 * 30, // 1 month
            'monthly' => 86400 * 365, // 1 year
        ],
    ],

    'alerting' => [
        /*
        |--------------------------------------------------------------------------
        | Alerting Configuration
        |--------------------------------------------------------------------------
        |
        | Configure alerting channels and rules.
        |
        */
        'enabled' => env('ALERTING_ENABLED', true),
        
        'channels' => [
            'email' => [
                'enabled' => env('EMAIL_ALERTS_ENABLED', true),
                'recipients' => explode(',', env('ALERT_EMAIL_RECIPIENTS', '')),
                'rate_limit' => 300, // seconds between same alerts
            ],
            'slack' => [
                'enabled' => env('SLACK_ALERTS_ENABLED', false),
                'webhook_url' => env('SLACK_WEBHOOK_URL'),
                'channel' => env('SLACK_ALERT_CHANNEL', '#alerts'),
                'rate_limit' => 300,
            ],
            'discord' => [
                'enabled' => env('DISCORD_ALERTS_ENABLED', false),
                'webhook_url' => env('DISCORD_WEBHOOK_URL'),
                'rate_limit' => 300,
            ],
            'sms' => [
                'enabled' => env('SMS_ALERTS_ENABLED', false),
                'provider' => env('SMS_PROVIDER', 'twilio'),
                'recipients' => explode(',', env('ALERT_SMS_RECIPIENTS', '')),
                'rate_limit' => 900, // 15 minutes
            ],
        ],
        
        'rules' => [
            'critical_system_down' => [
                'conditions' => ['database_down', 'redis_down'],
                'channels' => ['email', 'slack', 'sms'],
                'escalation_time' => 300, // 5 minutes
            ],
            'performance_degradation' => [
                'conditions' => ['high_response_time', 'high_memory_usage'],
                'channels' => ['email', 'slack'],
                'escalation_time' => 600, // 10 minutes
            ],
            'ai_service_issues' => [
                'conditions' => ['ai_service_down', 'high_ai_error_rate'],
                'channels' => ['email'],
                'escalation_time' => 900, // 15 minutes
            ],
        ],
    ],

    'logging' => [
        /*
        |--------------------------------------------------------------------------
        | Monitoring Logging
        |--------------------------------------------------------------------------
        |
        | Configure logging for monitoring events.
        |
        */
        'enabled' => env('MONITORING_LOGGING_ENABLED', true),
        'channel' => env('MONITORING_LOG_CHANNEL', 'monitoring'),
        'level' => env('MONITORING_LOG_LEVEL', 'info'),
        
        'events' => [
            'health_check_failed' => 'warning',
            'performance_threshold_exceeded' => 'warning',
            'alert_sent' => 'info',
            'system_recovery' => 'info',
            'monitoring_error' => 'error',
        ],
    ],

    'dashboard' => [
        /*
        |--------------------------------------------------------------------------
        | Monitoring Dashboard
        |--------------------------------------------------------------------------
        |
        | Configure the monitoring dashboard settings.
        |
        */
        'enabled' => env('MONITORING_DASHBOARD_ENABLED', true),
        'route' => '/admin/monitoring',
        'middleware' => ['auth', 'admin'],
        'refresh_interval' => env('DASHBOARD_REFRESH_INTERVAL', 30), // seconds
        
        'widgets' => [
            'system_status' => true,
            'performance_metrics' => true,
            'active_chats' => true,
            'queue_status' => true,
            'ai_performance' => true,
            'error_rates' => true,
            'response_times' => true,
            'user_activity' => true,
        ],
    ],

    'external_services' => [
        /*
        |--------------------------------------------------------------------------
        | External Monitoring Services
        |--------------------------------------------------------------------------
        |
        | Configure integration with external monitoring services.
        |
        */
        'new_relic' => [
            'enabled' => env('NEW_RELIC_ENABLED', false),
            'license_key' => env('NEW_RELIC_LICENSE_KEY'),
            'app_name' => env('NEW_RELIC_APP_NAME', 'Live Chat AI System'),
        ],
        
        'datadog' => [
            'enabled' => env('DATADOG_ENABLED', false),
            'api_key' => env('DATADOG_API_KEY'),
            'app_key' => env('DATADOG_APP_KEY'),
            'host' => env('DATADOG_HOST', 'api.datadoghq.com'),
        ],
        
        'sentry' => [
            'enabled' => env('SENTRY_ENABLED', false),
            'dsn' => env('SENTRY_LARAVEL_DSN'),
            'traces_sample_rate' => env('SENTRY_TRACES_SAMPLE_RATE', 0.1),
        ],
        
        'pingdom' => [
            'enabled' => env('PINGDOM_ENABLED', false),
            'check_url' => env('PINGDOM_CHECK_URL'),
            'check_interval' => env('PINGDOM_CHECK_INTERVAL', 60), // seconds
        ],
    ],

    'maintenance' => [
        /*
        |--------------------------------------------------------------------------
        | Maintenance Mode Monitoring
        |--------------------------------------------------------------------------
        |
        | Configure monitoring during maintenance mode.
        |
        */
        'monitor_during_maintenance' => env('MONITOR_DURING_MAINTENANCE', true),
        'reduced_check_frequency' => env('MAINTENANCE_CHECK_FREQUENCY', 300), // seconds
        'maintenance_alerts' => env('MAINTENANCE_ALERTS_ENABLED', false),
    ],

    'security' => [
        /*
        |--------------------------------------------------------------------------
        | Security Monitoring
        |--------------------------------------------------------------------------
        |
        | Configure security-related monitoring.
        |
        */
        'enabled' => env('SECURITY_MONITORING_ENABLED', true),
        
        'events' => [
            'failed_login_attempts' => [
                'threshold' => 5,
                'window' => 300, // 5 minutes
                'action' => 'alert',
            ],
            'suspicious_activity' => [
                'threshold' => 10,
                'window' => 600, // 10 minutes
                'action' => 'alert_and_block',
            ],
            'rate_limit_exceeded' => [
                'threshold' => 3,
                'window' => 300,
                'action' => 'alert',
            ],
        ],
    ],

    'backup_monitoring' => [
        /*
        |--------------------------------------------------------------------------
        | Backup Monitoring
        |--------------------------------------------------------------------------
        |
        | Monitor backup processes and alert on failures.
        |
        */
        'enabled' => env('BACKUP_MONITORING_ENABLED', true),
        'check_interval' => env('BACKUP_CHECK_INTERVAL', 3600), // 1 hour
        'max_backup_age' => env('MAX_BACKUP_AGE', 86400), // 24 hours
        'alert_on_failure' => env('BACKUP_FAILURE_ALERTS', true),
    ],

    'custom_checks' => [
        /*
        |--------------------------------------------------------------------------
        | Custom Health Checks
        |--------------------------------------------------------------------------
        |
        | Define custom health checks specific to your application.
        |
        */
        'chat_system_specific' => [
            'ai_model_availability' => [
                'enabled' => true,
                'timeout' => 10,
                'critical' => false,
            ],
            'websocket_connectivity' => [
                'enabled' => true,
                'timeout' => 5,
                'critical' => false,
            ],
            'file_upload_service' => [
                'enabled' => true,
                'timeout' => 5,
                'critical' => false,
            ],
            'email_service' => [
                'enabled' => true,
                'timeout' => 10,
                'critical' => false,
            ],
        ],
    ],

    'reporting' => [
        /*
        |--------------------------------------------------------------------------
        | Monitoring Reports
        |--------------------------------------------------------------------------
        |
        | Configure automated monitoring reports.
        |
        */
        'enabled' => env('MONITORING_REPORTS_ENABLED', true),
        
        'schedules' => [
            'daily' => [
                'enabled' => true,
                'time' => '08:00',
                'recipients' => explode(',', env('DAILY_REPORT_RECIPIENTS', '')),
                'include' => ['uptime', 'performance', 'errors', 'usage'],
            ],
            'weekly' => [
                'enabled' => true,
                'day' => 'monday',
                'time' => '09:00',
                'recipients' => explode(',', env('WEEKLY_REPORT_RECIPIENTS', '')),
                'include' => ['trends', 'capacity', 'security', 'recommendations'],
            ],
            'monthly' => [
                'enabled' => true,
                'day' => 1,
                'time' => '10:00',
                'recipients' => explode(',', env('MONTHLY_REPORT_RECIPIENTS', '')),
                'include' => ['summary', 'growth', 'optimization', 'planning'],
            ],
        ],
    ],
];
