<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ChatWebhook;
use App\Models\ChatWebhookDelivery;
use App\Services\ChatWebhookService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

class ChatWebhookController extends Controller
{
    public function __construct(
        protected ChatWebhookService $webhookService
    ) {
        $this->middleware(['auth', 'admin']);
    }

    /**
     * Display webhook management dashboard.
     */
    public function index(): View
    {
        $webhooks = ChatWebhook::with(['recentDeliveries'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        $stats = $this->webhookService->getWebhookStats();

        return view('admin.chat.webhooks.index', compact('webhooks', 'stats'));
    }

    /**
     * Show the form for creating a new webhook.
     */
    public function create(): View
    {
        $availableEvents = ChatWebhook::getAvailableEvents();
        
        return view('admin.chat.webhooks.create', compact('availableEvents'));
    }

    /**
     * Store a newly created webhook.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'url' => 'required|url|max:500',
            'events' => 'required|array|min:1',
            'events.*' => 'string|in:' . implode(',', array_keys(ChatWebhook::getAvailableEvents())),
            'secret' => 'nullable|string|max:255',
            'headers' => 'nullable|array',
            'headers.*' => 'string|max:500',
            'max_retries' => 'integer|min:0|max:10',
            'timeout_seconds' => 'integer|min:5|max:120',
            'ip_whitelist' => 'nullable|array',
            'ip_whitelist.*' => 'ip',
            'description' => 'nullable|string|max:1000',
        ]);

        $webhook = ChatWebhook::create($validated);

        return response()->json([
            'success' => true,
            'message' => 'Webhook created successfully',
            'data' => $webhook,
        ], 201);
    }

    /**
     * Display the specified webhook.
     */
    public function show(ChatWebhook $webhook): View
    {
        $webhook->load(['recentDeliveries']);
        $stats = $webhook->getDeliveryStats();
        
        return view('admin.chat.webhooks.show', compact('webhook', 'stats'));
    }

    /**
     * Show the form for editing the specified webhook.
     */
    public function edit(ChatWebhook $webhook): View
    {
        $availableEvents = ChatWebhook::getAvailableEvents();
        
        return view('admin.chat.webhooks.edit', compact('webhook', 'availableEvents'));
    }

    /**
     * Update the specified webhook.
     */
    public function update(Request $request, ChatWebhook $webhook): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'url' => 'required|url|max:500',
            'events' => 'required|array|min:1',
            'events.*' => 'string|in:' . implode(',', array_keys(ChatWebhook::getAvailableEvents())),
            'secret' => 'nullable|string|max:255',
            'headers' => 'nullable|array',
            'headers.*' => 'string|max:500',
            'max_retries' => 'integer|min:0|max:10',
            'timeout_seconds' => 'integer|min:5|max:120',
            'ip_whitelist' => 'nullable|array',
            'ip_whitelist.*' => 'ip',
            'description' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
        ]);

        $webhook->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Webhook updated successfully',
            'data' => $webhook,
        ]);
    }

    /**
     * Remove the specified webhook.
     */
    public function destroy(ChatWebhook $webhook): JsonResponse
    {
        $webhook->delete();

        return response()->json([
            'success' => true,
            'message' => 'Webhook deleted successfully',
        ]);
    }

    /**
     * Test webhook endpoint.
     */
    public function test(ChatWebhook $webhook): JsonResponse
    {
        $result = $this->webhookService->testWebhook($webhook);

        return response()->json([
            'success' => $result['success'],
            'message' => $result['success'] ? 'Webhook test successful' : 'Webhook test failed',
            'data' => $result,
        ]);
    }

    /**
     * Toggle webhook active status.
     */
    public function toggle(ChatWebhook $webhook): JsonResponse
    {
        $webhook->update(['is_active' => !$webhook->is_active]);

        return response()->json([
            'success' => true,
            'message' => $webhook->is_active ? 'Webhook enabled' : 'Webhook disabled',
            'data' => ['is_active' => $webhook->is_active],
        ]);
    }

    /**
     * Get webhook deliveries.
     */
    public function deliveries(ChatWebhook $webhook): JsonResponse
    {
        $deliveries = $webhook->deliveries()
            ->orderBy('created_at', 'desc')
            ->paginate(50);

        return response()->json([
            'success' => true,
            'data' => $deliveries,
        ]);
    }

    /**
     * Retry failed deliveries for a webhook.
     */
    public function retryDeliveries(ChatWebhook $webhook): JsonResponse
    {
        $failedDeliveries = $webhook->deliveries()
            ->where('status', 'failed')
            ->get();

        $retried = 0;
        foreach ($failedDeliveries as $delivery) {
            $delivery->update([
                'status' => 'pending',
                'attempts' => 0,
                'error_message' => null,
                'next_retry_at' => null,
            ]);
            
            \App\Jobs\DeliverChatWebhook::dispatch($delivery);
            $retried++;
        }

        return response()->json([
            'success' => true,
            'message' => "Retrying {$retried} failed deliveries",
            'data' => ['retried_count' => $retried],
        ]);
    }
}
