<?php

return [
    // Page Meta
    'page_title' => 'Développement d\'Applications Mobiles - ChiSolution',
    'meta_description' => 'Entreprise leader en développement d\'applications mobiles en Afrique du Sud spécialisée dans le développement d\'applications multiplateformes, React Native, Flutter, applications iOS et Android. Services experts de développement d\'applications mobiles avec prix compétitifs.',
    'meta_keywords' => 'développement applications mobiles Afrique du Sud, développement applications multiplateformes, développement React Native, développement applications Flutter, développement applications iOS, développement applications Android, coût développement applications mobiles, entreprise développement applications, applications mobiles natives, développement applications hybrides, design UI/UX applications mobiles, services développement applications',

    // Hero Section
    'hero_title' => 'Services de Développement d\'Applications Mobiles Multiplateformes',
    'hero_description' => 'Entreprise leader en développement d\'applications mobiles spécialisée dans React Native, Flutter et applications natives iOS/Android. Transformez vos idées en applications mobiles puissantes avec des prix compétitifs et des services de développement experts.',
    'get_quote' => 'Obtenir un Devis',
    'view_apps' => 'Voir les Applications',

    // Cross-Platform Section
    'cross_platform_title' => 'Développement d\'Applications Mobiles <span class="text-blue-600">Multiplateformes</span>',
    'cross_platform_description' => 'Développez une fois, déployez partout. Nos services de développement d\'applications mobiles multiplateformes vous aident à atteindre les utilisateurs iOS et Android avec une seule base de code, réduisant le temps de développement et les coûts.',

    // Features
    'react_native_title' => 'Développement React Native',
    'react_native_description' => 'Performance native avec JavaScript. Construisez des applications mobiles de haute qualité en utilisant React Native pour les plateformes iOS et Android.',
    'flutter_title' => 'Développement d\'Applications Flutter',
    'flutter_description' => 'Boîte à outils UI de Google pour de belles applications compilées nativement à partir d\'une seule base de code.',
    'native_ios_title' => 'Développement iOS Natif',
    'native_ios_description' => 'Développement Swift et Objective-C pour des performances optimales et des fonctionnalités spécifiques à la plateforme.',
    'native_android_title' => 'Développement Android Natif',
    'native_android_description' => 'Développement Kotlin et Java exploitant toute la puissance de la plateforme Android.',
    'ui_ux_design_title' => 'Design UI/UX Mobile',
    'ui_ux_design_description' => 'Approche de design centrée sur l\'utilisateur créant des expériences mobiles intuitives et engageantes.',
    'app_testing_title' => 'Tests d\'Applications et QA',
    'app_testing_description' => 'Tests complets sur appareils, plateformes et scénarios pour assurer la qualité.',
    'educational_apps_title' => 'Applications Éducatives',
    'educational_apps_description' => 'Plateformes d\'apprentissage avec contenu interactif, suivi des progrès et fonctionnalités de gamification.',
    'healthcare_apps_title' => 'Applications de Santé',
    'healthcare_apps_description' => 'Applications de santé et bien-être avec prise de rendez-vous, suivi de santé et fonctionnalités de télémédecine.',
    'entertainment_apps_title' => 'Applications de Divertissement',
    'entertainment_apps_description' => 'Applications de jeux, streaming média et divertissement avec expériences utilisateur engageantes.',

    // Technologies Section
    'technologies_title' => 'Technologies d\'Applications Mobiles <span class="text-blue-600">2025</span>',
    'technologies_description' => 'Technologies de développement d\'applications mobiles de pointe pour des applications rentables et haute performance avec prix compétitifs.',

    // Technology Items
    'react_native' => 'React Native',
    'flutter' => 'Flutter',
    'swift' => 'Swift',
    'kotlin' => 'Kotlin',
    'xamarin' => 'Xamarin',
    'ionic' => 'Ionic',
    'firebase' => 'Firebase',
    'aws_amplify' => 'AWS Amplify',
    'graphql' => 'GraphQL',
    'rest_api' => 'API REST',
    'push_notifications' => 'Notifications Push',
    'offline_storage' => 'Stockage Hors Ligne',

    // Process Steps
    'discovery_title' => 'Découverte et Planification',
    'discovery_description' => 'Nous analysons vos exigences, public cible et objectifs commerciaux pour créer une stratégie de développement complète.',
    'design_title' => 'Design UI/UX',
    'design_description' => 'Nos designers créent des interfaces utilisateur intuitives et engageantes optimisées pour les appareils mobiles.',
    'development_title' => 'Développement',
    'development_description' => 'Nos développeurs experts construisent votre application en utilisant les dernières technologies et meilleures pratiques.',
    'testing_title' => 'Tests et Lancement',
    'testing_description' => 'Tests rigoureux sur appareils et plateformes avant le lancement sur les app stores.',

    // Service Descriptions
    'react_native_description' => 'Applications mobiles multiplateformes avec React Native. Base de code unique pour iOS et Android avec 60% d\'économies et mise sur le marché plus rapide.',
    'cross_platform_description' => 'Base de code unique pour iOS et Android utilisant React Native ou Flutter. Développement plus rapide et solution rentable.',

    // Benefits
    'cost_effective_title' => 'Rentable',
    'cost_effective_description' => 'Base de code unique pour plusieurs plateformes',
    'faster_development_title' => 'Développement Plus Rapide',
    'faster_development_description' => 'Temps de mise sur le marché réduit',
    'single_codebase_title' => 'Base de Code Unique',
    'native_performance_title' => 'Performance Native',
    'native_performance_description' => 'Optimisé pour chaque plateforme',
    'easy_maintenance_title' => 'Maintenance Facile',
    'easy_maintenance_description' => 'Mises à jour et corrections de bugs simplifiées',

    // CTA Section
    'cta_title' => 'Prêt à Construire Votre Application Mobile ?',
    'cta_description' => 'Discutons de votre idée d\'application mobile et créons une solution qui engage vos utilisateurs et fait croître votre entreprise.',
    'start_project' => 'Commencer Votre Projet',
    'get_consultation' => 'Consultation Gratuite',
];
