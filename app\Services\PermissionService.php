<?php

namespace App\Services;

use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class PermissionService
{
    private const CACHE_TTL = 3600; // 1 hour
    private const CACHE_PREFIX = 'permissions:';

    /**
     * Available system permissions with their allowed actions.
     */
    private const AVAILABLE_PERMISSIONS = [
        'users' => ['create', 'read', 'update', 'delete'],
        'products' => ['create', 'read', 'update', 'delete'],
        'blog' => ['create', 'read', 'update', 'delete'],
        'blog_comments' => ['read', 'moderate', 'delete'],
        'orders' => ['create', 'read', 'update', 'delete'],
        'projects' => ['create', 'read', 'update', 'delete'],
        'content' => ['create', 'read', 'update', 'delete'],
        'analytics' => ['read'],
        'settings' => ['create', 'read', 'update', 'delete'],
        'team' => ['create', 'read', 'update', 'delete'],
        'newsletter' => ['create', 'read', 'update', 'delete', 'manage'],
        'contact_submissions' => ['create', 'read', 'update', 'delete', 'manage'],
        'chat' => ['participate', 'moderate', 'assign', 'view_all', 'export', 'admin'],
        'categories' => ['create', 'read', 'update', 'delete'],
        'coupons' => ['create', 'read', 'update', 'delete'],
        'jobs' => ['create', 'read', 'update', 'delete'],
        'job_applications' => ['create', 'read', 'update', 'delete'],
        'project_applications' => ['create', 'read', 'update', 'delete'],
        'visitor_analytics' => ['read', 'export'],
        'activity_logs' => ['read', 'delete'],
        'permissions' => ['read', 'manage'], // Permission to manage permissions
        'profile' => ['read', 'update'],
        'email_templates' => ['create', 'read', 'update', 'delete'],
        'email_campaigns' => ['create', 'read', 'update', 'delete', 'manage'],
        'email_analytics' => ['read', 'export']
    ];

    /**
     * Permission inheritance rules.
     */
    private const PERMISSION_INHERITANCE = [
        'delete' => ['update', 'read'],
        'update' => ['read'],
        'manage' => ['create', 'read', 'update', 'delete'],
        'admin' => ['participate', 'moderate', 'assign', 'view_all', 'export']
    ];

    /**
     * Check if user has specific permission.
     */
    public function userHasPermission(User $user, string $resource, string $action): bool
    {
        if (!$user->role) {
            return false;
        }

        // Cache key for user permissions
        $cacheKey = self::CACHE_PREFIX . "user:{$user->id}:{$resource}:{$action}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($user, $resource, $action) {
            return $this->checkPermissionWithInheritance($user->role, $resource, $action);
        });
    }

    /**
     * Check if role has specific permission with inheritance.
     */
    public function roleHasPermission(Role $role, string $resource, string $action): bool
    {
        $cacheKey = self::CACHE_PREFIX . "role:{$role->id}:{$resource}:{$action}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($role, $resource, $action) {
            return $this->checkPermissionWithInheritance($role, $resource, $action);
        });
    }

    /**
     * Get all permissions for a user.
     */
    public function getUserPermissions(User $user): array
    {
        if (!$user->role) {
            return [];
        }

        $cacheKey = self::CACHE_PREFIX . "user_all:{$user->id}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($user) {
            return $this->expandPermissions($user->role->permissions ?? []);
        });
    }

    /**
     * Get all permissions for a role.
     */
    public function getRolePermissions(Role $role): array
    {
        $cacheKey = self::CACHE_PREFIX . "role_all:{$role->id}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($role) {
            return $this->expandPermissions($role->permissions ?? []);
        });
    }

    /**
     * Validate permission structure.
     */
    public function validatePermissions(array $permissions): array
    {
        $errors = [];

        foreach ($permissions as $resource => $actions) {
            if (!isset(self::AVAILABLE_PERMISSIONS[$resource])) {
                $errors[] = "Invalid permission resource: {$resource}";
                continue;
            }

            if (!is_array($actions)) {
                $errors[] = "Actions for resource '{$resource}' must be an array";
                continue;
            }

            foreach ($actions as $action) {
                if (!in_array($action, self::AVAILABLE_PERMISSIONS[$resource])) {
                    $errors[] = "Invalid action '{$action}' for resource '{$resource}'";
                }
            }
        }

        return $errors;
    }

    /**
     * Get available permissions structure.
     */
    public function getAvailablePermissions(): array
    {
        return self::AVAILABLE_PERMISSIONS;
    }

    /**
     * Get permission inheritance rules.
     */
    public function getPermissionInheritance(): array
    {
        return self::PERMISSION_INHERITANCE;
    }

    /**
     * Clear permission cache for user.
     */
    public function clearUserPermissionCache(User $user): void
    {
        $patterns = [
            self::CACHE_PREFIX . "user:{$user->id}:*",
            self::CACHE_PREFIX . "user_all:{$user->id}"
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }

        // If user role changed, also clear role cache
        if ($user->role) {
            $this->clearRolePermissionCache($user->role);
        }
    }

    /**
     * Clear permission cache for role.
     */
    public function clearRolePermissionCache(Role $role): void
    {
        $patterns = [
            self::CACHE_PREFIX . "role:{$role->id}:*",
            self::CACHE_PREFIX . "role_all:{$role->id}"
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }

        // Clear cache for all users with this role
        $users = $role->users;
        foreach ($users as $user) {
            $this->clearUserPermissionCache($user);
        }
    }

    /**
     * Clear all permission caches.
     */
    public function clearAllPermissionCaches(): void
    {
        $keys = Cache::getRedis()->keys(self::CACHE_PREFIX . '*');
        if (!empty($keys)) {
            Cache::getRedis()->del($keys);
        }
    }

    /**
     * Check permission with inheritance logic.
     */
    private function checkPermissionWithInheritance(Role $role, string $resource, string $action): bool
    {
        $permissions = $role->permissions ?? [];

        if (!isset($permissions[$resource])) {
            return false;
        }

        $resourcePermissions = $permissions[$resource];

        // Direct permission check
        if (in_array($action, $resourcePermissions)) {
            return true;
        }

        // Check inherited permissions
        foreach ($resourcePermissions as $grantedAction) {
            if (isset(self::PERMISSION_INHERITANCE[$grantedAction])) {
                if (in_array($action, self::PERMISSION_INHERITANCE[$grantedAction])) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Expand permissions with inheritance.
     */
    private function expandPermissions(array $permissions): array
    {
        $expanded = [];

        foreach ($permissions as $resource => $actions) {
            $expanded[$resource] = [];

            foreach ($actions as $action) {
                // Add the direct action
                $expanded[$resource][] = $action;

                // Add inherited actions
                if (isset(self::PERMISSION_INHERITANCE[$action])) {
                    foreach (self::PERMISSION_INHERITANCE[$action] as $inheritedAction) {
                        if (!in_array($inheritedAction, $expanded[$resource])) {
                            $expanded[$resource][] = $inheritedAction;
                        }
                    }
                }
            }

            // Remove duplicates and sort
            $expanded[$resource] = array_unique($expanded[$resource]);
            sort($expanded[$resource]);
        }

        return $expanded;
    }

    /**
     * Get permission matrix for all roles.
     */
    public function getPermissionMatrix(): array
    {
        $roles = Role::all();
        $matrix = [];

        foreach ($roles as $role) {
            $matrix[$role->name] = [];
            foreach (self::AVAILABLE_PERMISSIONS as $resource => $actions) {
                $matrix[$role->name][$resource] = [];
                foreach ($actions as $action) {
                    $matrix[$role->name][$resource][$action] = $this->roleHasPermission($role, $resource, $action);
                }
            }
        }

        return $matrix;
    }

    /**
     * Get users with specific permission.
     */
    public function getUsersWithPermission(string $resource, string $action): array
    {
        $users = User::with('role')->where('is_deleted', false)->get();
        $usersWithPermission = [];

        foreach ($users as $user) {
            if ($this->userHasPermission($user, $resource, $action)) {
                $usersWithPermission[] = $user;
            }
        }

        return $usersWithPermission;
    }

    /**
     * Get roles with specific permission.
     */
    public function getRolesWithPermission(string $resource, string $action): array
    {
        $roles = Role::active()->get();
        $rolesWithPermission = [];

        foreach ($roles as $role) {
            if ($this->roleHasPermission($role, $resource, $action)) {
                $rolesWithPermission[] = $role;
            }
        }

        return $rolesWithPermission;
    }
}
