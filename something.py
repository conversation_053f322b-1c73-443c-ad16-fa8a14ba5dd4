import re

def analyze_php_code_for_slow_parts(php_code: str):
    issues = []

    # Detect long-running or risky while loops
    while_loops = re.findall(r'while\s*\((.*?)\)\s*\{', php_code, re.DOTALL)
    for loop in while_loops:
        if 'true' in loop or '<' in loop or '!=' in loop or '!==':
            issues.append(f"Suspicious while loop condition: {loop.strip()}")

    # Detect delay functions
    if 'usleep(' in php_code:
        issues.append("Found 'usleep' — can delay processing if used in a retry loop")

    # Detect Redis lock patterns
    if 'Redis::set' in php_code and 'NX' in php_code and 'EX' in php_code:
        issues.append("Redis lock with retries detected — may hang if lock isn’t released")

    return issues

# Paste your CartService code here
with open('CartService.php', 'r') as f:
    php_code = f.read()

issues_found = analyze_php_code_for_slow_parts(php_code)
for issue in issues_found:
    print("⚠️", issue)
