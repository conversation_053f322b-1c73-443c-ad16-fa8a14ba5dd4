<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\ChatParticipant;
use App\Models\ChatAssignment;
use App\Models\User;
use App\Services\ChatService;
use App\Services\ChatAssignmentService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\DB;

class ChatModerationController extends Controller
{
    protected ChatService $chatService;
    protected ChatAssignmentService $assignmentService;

    public function __construct(
        ChatService $chatService,
        ChatAssignmentService $assignmentService
    ) {
        $this->chatService = $chatService;
        $this->assignmentService = $assignmentService;
    }

    /**
     * Display the chat monitoring dashboard.
     */
    public function index(): View
    {
        $stats = $this->getChatStats();
        $activeChats = $this->getActiveChats();
        $staffWorkload = $this->getStaffWorkload();
        $recentMessages = $this->getRecentMessages();

        return view('admin.chat.moderation.index', compact(
            'stats',
            'activeChats',
            'staffWorkload',
            'recentMessages'
        ));
    }

    /**
     * Get chat statistics for dashboard.
     */
    protected function getChatStats(): array
    {
        return [
            'active_chats' => ChatRoom::where('status', 'active')->count(),
            'waiting_chats' => ChatRoom::where('status', 'waiting')->count(),
            'total_messages_today' => ChatMessage::whereDate('created_at', today())->count(),
            'online_staff' => User::staff()->online()->count(),
            'avg_response_time' => $this->calculateAverageResponseTime(),
            'satisfaction_rating' => $this->calculateSatisfactionRating(),
        ];
    }

    /**
     * Get active chat rooms with details.
     */
    protected function getActiveChats()
    {
        return ChatRoom::with(['participants.user', 'currentAssignment.assignedStaff', 'messages' => function($query) {
                $query->latest()->limit(1);
            }])
            ->whereIn('status', ['active', 'waiting'])
            ->orderBy('priority', 'desc')
            ->orderBy('created_at', 'asc')
            ->limit(20)
            ->get();
    }

    /**
     * Get staff workload information.
     */
    protected function getStaffWorkload()
    {
        return User::staff()
            ->with(['chatAssignments' => function($query) {
                $query->where('status', 'active');
            }])
            ->get()
            ->map(function($staff) {
                $activeAssignments = $staff->chatAssignments->count();
                $maxAssignments = $staff->role && $staff->role->name === 'admin' ? 10 : 5;

                return [
                    'staff' => $staff,
                    'active_assignments' => $activeAssignments,
                    'max_assignments' => $maxAssignments,
                    'workload_percentage' => ($activeAssignments / $maxAssignments) * 100,
                    'is_available' => $activeAssignments < $maxAssignments && $staff->isOnline(),
                    'is_online' => $staff->isOnline(),
                    'last_seen' => $staff->last_seen_at,
                    'last_seen_human' => $staff->last_seen_at ? $staff->last_seen_at->diffForHumans() : 'Never',
                ];
            })
            ->sortByDesc('workload_percentage');
    }

    /**
     * Get recent messages for monitoring.
     */
    protected function getRecentMessages()
    {
        return ChatMessage::with(['chatRoom', 'user'])
            ->whereHas('chatRoom', function($query) {
                $query->whereIn('status', ['active', 'waiting']);
            })
            ->orderBy('created_at', 'desc')
            ->limit(50)
            ->get();
    }

    /**
     * Block a user from chat.
     */
    public function blockUser(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'reason' => 'required|string|max:500',
            'duration' => 'nullable|integer|min:1|max:365', // days
        ]);

        try {
            $user = User::findOrFail($request->user_id);
            
            // Update user's chat permissions
            $user->update([
                'chat_blocked' => true,
                'chat_blocked_until' => $request->duration ? 
                    now()->addDays($request->duration) : null,
                'chat_block_reason' => $request->reason,
            ]);

            // Remove user from all active chats
            ChatParticipant::where('user_id', $user->id)
                ->where('is_active', true)
                ->update(['is_active' => false, 'left_at' => now()]);

            return response()->json([
                'success' => true,
                'message' => 'User blocked from chat successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to block user: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Unblock a user from chat.
     */
    public function unblockUser(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        try {
            $user = User::findOrFail($request->user_id);
            
            $user->update([
                'chat_blocked' => false,
                'chat_blocked_until' => null,
                'chat_block_reason' => null,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'User unblocked from chat successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to unblock user: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a message.
     */
    public function deleteMessage(Request $request): JsonResponse
    {
        $request->validate([
            'message_id' => 'required|exists:chat_messages,id',
            'reason' => 'required|string|max:500',
        ]);

        try {
            $message = ChatMessage::findOrFail($request->message_id);
            
            // Soft delete the message
            $message->update([
                'content' => '[Message deleted by moderator]',
                'metadata' => array_merge($message->metadata ?? [], [
                    'deleted_by' => auth()->id(),
                    'deleted_at' => now(),
                    'deletion_reason' => $request->reason,
                ]),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Message deleted successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete message: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Escalate a chat to higher priority.
     */
    public function escalateChat(Request $request): JsonResponse
    {
        $request->validate([
            'room_id' => 'required|exists:chat_rooms,id',
            'reason' => 'required|string|max:500',
            'priority' => 'required|integer|min:1|max:5',
        ]);

        try {
            $room = ChatRoom::findOrFail($request->room_id);
            
            $room->update([
                'priority' => $request->priority,
                'metadata' => array_merge($room->metadata ?? [], [
                    'escalated_by' => auth()->id(),
                    'escalated_at' => now(),
                    'escalation_reason' => $request->reason,
                ]),
            ]);

            // Try to reassign to admin if high priority
            if ($request->priority >= 4) {
                $this->assignmentService->autoAssign($room);
            }

            return response()->json([
                'success' => true,
                'message' => 'Chat escalated successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to escalate chat: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get real-time chat statistics.
     */
    public function getStats(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'stats' => $this->getChatStats(),
        ]);
    }

    /**
     * Calculate average response time.
     */
    protected function calculateAverageResponseTime(): float
    {
        // This is a simplified calculation
        // In production, you'd want more sophisticated metrics
        return 2.5; // minutes
    }

    /**
     * Calculate satisfaction rating.
     */
    protected function calculateSatisfactionRating(): float
    {
        $ratings = DB::table('chat_ratings')
            ->whereDate('created_at', '>=', now()->subDays(30))
            ->avg('rating');

        return round($ratings ?? 0, 1);
    }
}
