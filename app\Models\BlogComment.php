<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;
use Carbon\Carbon;

class BlogComment extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'blog_post_id',
        'user_id',
        'parent_comment_id',
        'content',
        'rating',
        'attachments',
        'is_approved',
        'approved_by',
        'approved_at',
        'admin_notes',
        'is_deleted',
        'deleted_by',
        'deleted_at',
        'helpful_count',
        'helpful_users',
        'is_flagged',
        'flag_count',
        'flagged_by',
    ];

    protected $casts = [
        'attachments' => 'array',
        'helpful_users' => 'array',
        'flagged_by' => 'array',
        'is_approved' => 'boolean',
        'is_deleted' => 'boolean',
        'is_flagged' => 'boolean',
        'approved_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $appends = [
        'formatted_created_date',
        'time_ago',
        'has_attachments',
        'attachment_count',
        'is_reply',
        'reply_count',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($comment) {
            if (empty($comment->uuid)) {
                $comment->uuid = Str::uuid();
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    // ==================== RELATIONSHIPS ====================

    /**
     * Get the blog post that this comment belongs to.
     */
    public function blogPost(): BelongsTo
    {
        return $this->belongsTo(BlogPost::class);
    }

    /**
     * Get the user who wrote this comment.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the parent comment (for replies).
     */
    public function parentComment(): BelongsTo
    {
        return $this->belongsTo(BlogComment::class, 'parent_comment_id');
    }

    /**
     * Get the replies to this comment.
     */
    public function replies(): HasMany
    {
        return $this->hasMany(BlogComment::class, 'parent_comment_id')
            ->where('is_approved', true)
            ->where('is_deleted', false)
            ->orderBy('created_at', 'asc');
    }

    /**
     * Get all replies including unapproved ones (for admin).
     */
    public function allReplies(): HasMany
    {
        return $this->hasMany(BlogComment::class, 'parent_comment_id')
            ->where('is_deleted', false)
            ->orderBy('created_at', 'asc');
    }

    /**
     * Get the admin who approved this comment.
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the admin who deleted this comment.
     */
    public function deletedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'deleted_by');
    }

    // ==================== SCOPES ====================

    /**
     * Scope a query to only include approved comments.
     */
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    /**
     * Scope a query to only include non-deleted comments.
     */
    public function scopeActive($query)
    {
        return $query->where('is_deleted', false);
    }

    /**
     * Scope a query to only include top-level comments (not replies).
     */
    public function scopeTopLevel($query)
    {
        return $query->whereNull('parent_comment_id');
    }

    /**
     * Scope a query to only include comments with ratings.
     */
    public function scopeWithRating($query)
    {
        return $query->whereNotNull('rating');
    }

    /**
     * Scope a query to only include flagged comments.
     */
    public function scopeFlagged($query)
    {
        return $query->where('is_flagged', true);
    }

    /**
     * Scope a query to only include pending approval comments.
     */
    public function scopePending($query)
    {
        return $query->where('is_approved', false)->where('is_deleted', false);
    }

    // ==================== ACCESSORS ====================

    /**
     * Get the formatted creation date.
     */
    public function getFormattedCreatedDateAttribute(): string
    {
        return $this->created_at->format('M j, Y');
    }

    /**
     * Get the time ago string.
     */
    public function getTimeAgoAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Check if comment has attachments.
     */
    public function getHasAttachmentsAttribute(): bool
    {
        return !empty($this->attachments) && is_array($this->attachments);
    }

    /**
     * Get the number of attachments.
     */
    public function getAttachmentCountAttribute(): int
    {
        return $this->has_attachments ? count($this->attachments) : 0;
    }

    /**
     * Check if this is a reply to another comment.
     */
    public function getIsReplyAttribute(): bool
    {
        return !is_null($this->parent_comment_id);
    }

    /**
     * Get the number of replies to this comment.
     */
    public function getReplyCountAttribute(): int
    {
        return $this->replies()->count();
    }

    /**
     * Get the star rating display.
     */
    public function getStarRatingAttribute(): string
    {
        if (!$this->rating) {
            return '';
        }

        $stars = '';
        for ($i = 1; $i <= 5; $i++) {
            $stars .= $i <= $this->rating ? '★' : '☆';
        }
        return $stars;
    }

    // ==================== METHODS ====================

    /**
     * Mark comment as helpful by a user.
     */
    public function markAsHelpful(int $userId): bool
    {
        $helpfulUsers = $this->helpful_users ?? [];

        if (!in_array($userId, $helpfulUsers)) {
            $helpfulUsers[] = $userId;
            $this->helpful_users = $helpfulUsers;
            $this->helpful_count = count($helpfulUsers);
            return $this->save();
        }

        return false;
    }

    /**
     * Remove helpful mark by a user.
     */
    public function removeHelpful(int $userId): bool
    {
        $helpfulUsers = $this->helpful_users ?? [];
        $key = array_search($userId, $helpfulUsers);

        if ($key !== false) {
            unset($helpfulUsers[$key]);
            $this->helpful_users = array_values($helpfulUsers);
            $this->helpful_count = count($helpfulUsers);
            return $this->save();
        }

        return false;
    }

    /**
     * Check if user marked this comment as helpful.
     */
    public function isMarkedHelpfulBy(int $userId): bool
    {
        return in_array($userId, $this->helpful_users ?? []);
    }

    /**
     * Flag comment as inappropriate.
     */
    public function flagAsInappropriate(int $userId): bool
    {
        $flaggedBy = $this->flagged_by ?? [];

        if (!in_array($userId, $flaggedBy)) {
            $flaggedBy[] = $userId;
            $this->flagged_by = $flaggedBy;
            $this->flag_count = count($flaggedBy);

            // Auto-flag if multiple users flag it
            if ($this->flag_count >= 3) {
                $this->is_flagged = true;
            }

            return $this->save();
        }

        return false;
    }

    /**
     * Approve the comment.
     */
    public function approve(int $adminId, ?string $notes = null): bool
    {
        $this->is_approved = true;
        $this->approved_by = $adminId;
        $this->approved_at = now();
        if ($notes) {
            $this->admin_notes = $notes;
        }

        return $this->save();
    }

    /**
     * Reject/delete the comment.
     */
    public function reject(int $adminId, ?string $reason = null): bool
    {
        $this->is_deleted = true;
        $this->deleted_by = $adminId;
        $this->deleted_at = now();
        if ($reason) {
            $this->admin_notes = $reason;
        }

        return $this->save();
    }

    /**
     * Get attachment URLs with optimization.
     */
    public function getOptimizedAttachments(): array
    {
        if (!$this->has_attachments) {
            return [];
        }

        $optimized = [];
        foreach ($this->attachments as $attachment) {
            $optimized[] = [
                'original' => $attachment,
                'optimized' => $this->getOptimizedAttachmentUrl($attachment),
                'type' => $this->getAttachmentType($attachment),
                'size' => $this->getAttachmentSize($attachment),
            ];
        }

        return $optimized;
    }

    /**
     * Get optimized attachment URL using ImageService if applicable.
     */
    private function getOptimizedAttachmentUrl(string $path): string
    {
        // Check if it's an image and use ImageService
        if ($this->isImage($path)) {
            try {
                $imageService = app(\App\Services\ImageService::class);
                return $imageService->getOptimizedUrl($path, 'medium');
            } catch (\Exception $e) {
                \Log::warning('Failed to optimize comment attachment', [
                    'path' => $path,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return asset('storage/' . $path);
    }

    /**
     * Check if attachment is an image.
     */
    private function isImage(string $path): bool
    {
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
        return in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp']);
    }

    /**
     * Get attachment type.
     */
    private function getAttachmentType(string $path): string
    {
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));

        if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
            return 'image';
        } elseif (in_array($extension, ['pdf'])) {
            return 'document';
        } elseif (in_array($extension, ['doc', 'docx'])) {
            return 'word';
        } elseif (in_array($extension, ['xls', 'xlsx'])) {
            return 'excel';
        } else {
            return 'file';
        }
    }

    /**
     * Get attachment file size.
     */
    private function getAttachmentSize(string $path): ?int
    {
        $fullPath = storage_path('app/public/' . $path);
        return file_exists($fullPath) ? filesize($fullPath) : null;
    }
}
