@extends('layouts.dashboard')

@section('title', 'AI Performance Dashboard')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">AI Performance Dashboard</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.chat.analytics.index') }}">Chat Analytics</a></li>
                        <li class="breadcrumb-item active">AI Performance</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Row -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form id="performance-filters" class="row g-3">
                        <div class="col-md-3">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                   value="{{ $defaultFilters['start_date']->format('Y-m-d') }}">
                        </div>
                        <div class="col-md-3">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" 
                                   value="{{ $defaultFilters['end_date']->format('Y-m-d') }}">
                        </div>
                        <div class="col-md-2">
                            <label for="model" class="form-label">AI Model</label>
                            <select class="form-select" id="model" name="model">
                                <option value="all">All Models</option>
                                <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                                <option value="gpt-4">GPT-4</option>
                                <option value="template">Template</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="language" class="form-label">Language</label>
                            <select class="form-select" id="language" name="language">
                                <option value="all">All Languages</option>
                                <option value="en">English</option>
                                <option value="af">Afrikaans</option>
                                <option value="es">Spanish</option>
                                <option value="fr">French</option>
                                <option value="zu">Zulu</option>
                                <option value="xh">Xhosa</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block">Apply Filters</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Metrics Row -->
    <div class="row">
        <div class="col-xl-2 col-md-4">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate">Total Interactions</h5>
                            <h3 class="my-2 py-1" id="total-interactions">{{ $report['summary']['total_interactions'] ?? 0 }}</h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-robot text-primary" style="font-size: 24px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate">Success Rate</h5>
                            <h3 class="my-2 py-1" id="success-rate">{{ number_format($report['summary']['success_rate'] ?? 0, 1) }}%</h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-check-circle text-success" style="font-size: 24px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate">Escalation Rate</h5>
                            <h3 class="my-2 py-1" id="escalation-rate">{{ number_format($report['summary']['escalation_rate'] ?? 0, 1) }}%</h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-arrow-up-circle text-warning" style="font-size: 24px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate">Avg Confidence</h5>
                            <h3 class="my-2 py-1" id="avg-confidence">{{ number_format($report['summary']['avg_confidence_score'] ?? 0, 2) }}</h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-gauge text-info" style="font-size: 24px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate">Avg Response Time</h5>
                            <h3 class="my-2 py-1" id="avg-response-time">{{ number_format($report['summary']['avg_response_time_ms'] ?? 0, 0) }}ms</h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-timer text-secondary" style="font-size: 24px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row">
        <div class="col-xl-6">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Success Rate by Response Type</h4>
                    <div id="success-by-type-chart" style="height: 300px;"></div>
                </div>
            </div>
        </div>

        <div class="col-xl-6">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Confidence Score Distribution</h4>
                    <div id="confidence-distribution-chart" style="height: 300px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analysis Row -->
    <div class="row">
        <div class="col-xl-8">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Language Performance</h4>
                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap mb-0" id="language-performance-table">
                            <thead class="table-light">
                                <tr>
                                    <th>Language</th>
                                    <th>Interactions</th>
                                    <th>Success Rate</th>
                                    <th>Avg Confidence</th>
                                    <th>Avg Response Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($report['language_performance'] ?? [] as $lang)
                                <tr>
                                    <td>{{ strtoupper($lang['language']) }}</td>
                                    <td>{{ $lang['total_interactions'] }}</td>
                                    <td>{{ number_format($lang['success_rate'], 1) }}%</td>
                                    <td>{{ number_format($lang['avg_confidence'], 2) }}</td>
                                    <td>{{ number_format($lang['avg_response_time'], 0) }}ms</td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="text-center text-muted">No language performance data available</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Escalation Analysis</h4>
                    <div id="escalation-reasons-chart" style="height: 250px;"></div>
                    
                    <div class="mt-4">
                        <h6 class="mb-3">Common Escalation Triggers</h6>
                        @if(isset($report['escalation_analysis']['common_triggers']))
                            @foreach($report['escalation_analysis']['common_triggers'] as $trigger => $count)
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>{{ ucfirst(str_replace('_', ' ', $trigger)) }}</span>
                                <span class="badge bg-warning">{{ $count }}</span>
                            </div>
                            @endforeach
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Model Comparison Row -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Model Performance Comparison</h4>
                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap mb-0" id="model-comparison-table">
                            <thead class="table-light">
                                <tr>
                                    <th>Model</th>
                                    <th>Total Interactions</th>
                                    <th>Success Rate</th>
                                    <th>Avg Confidence</th>
                                    <th>Avg Response Time</th>
                                    <th>Performance Score</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($report['model_comparison'] ?? [] as $model)
                                <tr>
                                    <td><strong>{{ $model['model'] }}</strong></td>
                                    <td>{{ $model['total_interactions'] }}</td>
                                    <td>
                                        <span class="badge bg-{{ $model['success_rate'] >= 80 ? 'success' : ($model['success_rate'] >= 60 ? 'warning' : 'danger') }}">
                                            {{ number_format($model['success_rate'], 1) }}%
                                        </span>
                                    </td>
                                    <td>{{ number_format($model['avg_confidence'], 2) }}</td>
                                    <td>{{ number_format($model['avg_response_time'], 0) }}ms</td>
                                    <td>
                                        @php
                                            $score = ($model['success_rate'] * 0.4) + ($model['avg_confidence'] * 100 * 0.3) + (max(0, 100 - ($model['avg_response_time'] / 100)) * 0.3);
                                        @endphp
                                        <span class="badge bg-{{ $score >= 80 ? 'success' : ($score >= 60 ? 'warning' : 'danger') }}">
                                            {{ number_format($score, 1) }}
                                        </span>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="6" class="text-center text-muted">No model comparison data available</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Row -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Export & Actions</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-primary me-2" onclick="exportReport('json')">
                                <i class="mdi mdi-download"></i> Export JSON
                            </button>
                            <button type="button" class="btn btn-success me-2" onclick="exportReport('csv')">
                                <i class="mdi mdi-file-excel"></i> Export CSV
                            </button>
                            <button type="button" class="btn btn-danger" onclick="exportReport('pdf')">
                                <i class="mdi mdi-file-pdf"></i> Export PDF
                            </button>
                        </div>
                        <div class="col-md-6 text-end">
                            <button type="button" class="btn btn-info" onclick="refreshReport()">
                                <i class="mdi mdi-refresh"></i> Refresh Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh report every 5 minutes
setInterval(function() {
    refreshReport();
}, 300000);

// Handle filter form submission
document.getElementById('performance-filters').addEventListener('submit', function(e) {
    e.preventDefault();
    refreshReport();
});

function refreshReport() {
    const formData = new FormData(document.getElementById('performance-filters'));
    const params = new URLSearchParams(formData);
    
    fetch(`{{ route('admin.chat.ai.performance.report') }}?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateDashboard(data.data);
            }
        })
        .catch(error => console.error('Error refreshing report:', error));
}

function updateDashboard(report) {
    // Update summary metrics
    document.getElementById('total-interactions').textContent = report.summary.total_interactions;
    document.getElementById('success-rate').textContent = report.summary.success_rate.toFixed(1) + '%';
    document.getElementById('escalation-rate').textContent = report.summary.escalation_rate.toFixed(1) + '%';
    document.getElementById('avg-confidence').textContent = report.summary.avg_confidence_score.toFixed(2);
    document.getElementById('avg-response-time').textContent = Math.round(report.summary.avg_response_time_ms) + 'ms';
    
    // Update tables and charts would go here
    console.log('Dashboard updated with new data:', report);
}

function exportReport(format) {
    const formData = new FormData(document.getElementById('performance-filters'));
    formData.append('format', format);
    
    fetch('{{ route("admin.chat.ai.performance.export") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.download_url) {
                window.open(data.download_url, '_blank');
            } else {
                // Handle JSON export
                const blob = new Blob([JSON.stringify(data.data, null, 2)], {type: 'application/json'});
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `ai-performance-report-${format}-${new Date().toISOString().split('T')[0]}.${format}`;
                a.click();
                window.URL.revokeObjectURL(url);
            }
        }
    })
    .catch(error => console.error('Error exporting report:', error));
}
</script>
@endsection
