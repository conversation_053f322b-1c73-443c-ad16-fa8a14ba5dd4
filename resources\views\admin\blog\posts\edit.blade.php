@extends('layouts.dashboard')

@section('title', 'Edit Blog Post - Admin Dashboard')
@section('page_title', 'Edit Blog Post')

@section('content')
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Edit Blog Post</h1>
            <p class="text-gray-600">{{ $post->title }}</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('admin.blog.posts.show', $post) }}" 
               class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                </svg>
                View Post
            </a>
            <a href="{{ route('admin.blog.posts.index') }}" 
               class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                Back to Posts
            </a>
        </div>
    </div>

    <!-- Form -->
    <form action="{{ route('admin.blog.posts.update', $post) }}" 
          method="POST" 
          enctype="multipart/form-data"
          class="space-y-6">
        @csrf
        @method('PUT')

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content Column -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information Card -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h2>
                    
                    <!-- Title -->
                    <div class="mb-4">
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-1">
                            Title <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               name="title" 
                               id="title" 
                               value="{{ old('title', $post->title) }}"
                               required
                               maxlength="300"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('title') border-red-500 @enderror">
                        @error('title')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Slug -->
                    <div class="mb-4">
                        <label for="slug" class="block text-sm font-medium text-gray-700 mb-1">
                            URL Slug
                        </label>
                        <input type="text" 
                               name="slug" 
                               id="slug" 
                               value="{{ old('slug', $post->slug) }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('slug') border-red-500 @enderror">
                        @error('slug')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Excerpt -->
                    <div class="mb-4">
                        <label for="excerpt" class="block text-sm font-medium text-gray-700 mb-1">
                            Excerpt
                        </label>
                        <textarea name="excerpt" 
                                  id="excerpt" 
                                  rows="3"
                                  maxlength="500"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('excerpt') border-red-500 @enderror">{{ old('excerpt', $post->excerpt) }}</textarea>
                        @error('excerpt')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Content -->
                    <div>
                        <label for="content" class="block text-sm font-medium text-gray-700 mb-1">
                            Content <span class="text-red-500">*</span>
                        </label>
                        <textarea name="content" 
                                  id="content" 
                                  rows="20"
                                  required
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('content') border-red-500 @enderror">{{ old('content', $post->content) }}</textarea>
                        @error('content')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Media Card -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Media</h2>
                    
                    <!-- Current Featured Image -->
                    @if($post->featured_image)
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Current Featured Image</label>
                        <div class="relative inline-block">
                            <img src="{{ asset('storage/' . $post->featured_image) }}" 
                                 alt="{{ $post->title }}"
                                 class="max-w-xs rounded-lg shadow">
                        </div>
                    </div>
                    @endif

                    <!-- Featured Image -->
                    <div class="mb-6">
                        <label for="featured_image" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ $post->featured_image ? 'Replace Featured Image' : 'Featured Image' }}
                        </label>
                        <input type="file" 
                               name="featured_image" 
                               id="featured_image" 
                               accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('featured_image') border-red-500 @enderror">
                        <p class="mt-1 text-xs text-gray-500">Max 5MB. Formats: JPEG, PNG, GIF, WebP</p>
                        @error('featured_image')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Current Gallery Images -->
                    @if($post->gallery_images && count($post->gallery_images) > 0)
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Current Gallery Images</label>
                        <div class="grid grid-cols-3 gap-2" id="current_gallery">
                            @foreach($post->gallery_images as $index => $image)
                            <div class="relative group" data-image="{{ $image }}">
                                <img src="{{ asset('storage/' . $image) }}" 
                                     alt="Gallery image {{ $index + 1 }}"
                                     class="w-full h-24 object-cover rounded-lg shadow">
                                <button type="button" 
                                        onclick="removeGalleryImage('{{ $image }}')"
                                        class="absolute top-1 right-1 bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                    </svg>
                                </button>
                            </div>
                            @endforeach
                        </div>
                        <input type="hidden" name="remove_gallery_images[]" id="remove_gallery_images" value="">
                    </div>
                    @endif

                    <!-- Gallery Images -->
                    <div>
                        <label for="gallery_images" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ $post->gallery_images ? 'Add More Gallery Images' : 'Gallery Images' }}
                        </label>
                        <input type="file" 
                               name="gallery_images[]" 
                               id="gallery_images" 
                               accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
                               multiple
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('gallery_images') border-red-500 @enderror">
                        <p class="mt-1 text-xs text-gray-500">Max 10 images total, 5MB each</p>
                        @error('gallery_images')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <div id="gallery_preview" class="mt-2 grid grid-cols-3 gap-2"></div>
                    </div>
                </div>

                <!-- SEO Card -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">SEO Settings</h2>
                    
                    <!-- Meta Title -->
                    <div class="mb-4">
                        <label for="meta_title" class="block text-sm font-medium text-gray-700 mb-1">
                            Meta Title
                        </label>
                        <input type="text" 
                               name="meta_title" 
                               id="meta_title" 
                               value="{{ old('meta_title', $post->meta_title) }}"
                               maxlength="255"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <!-- Meta Description -->
                    <div class="mb-4">
                        <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-1">
                            Meta Description
                        </label>
                        <textarea name="meta_description" 
                                  id="meta_description" 
                                  rows="3"
                                  maxlength="500"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">{{ old('meta_description', $post->meta_description) }}</textarea>
                        <p class="mt-1 text-xs text-gray-500">Recommended: 150-160 characters</p>
                    </div>

                    <!-- Meta Keywords -->
                    <div class="mb-4">
                        <label for="meta_keywords" class="block text-sm font-medium text-gray-700 mb-1">
                            Meta Keywords
                        </label>
                        <input type="text" 
                               name="meta_keywords" 
                               id="meta_keywords" 
                               value="{{ old('meta_keywords', $post->meta_keywords) }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <!-- Focus Keyword -->
                    <div class="mb-4">
                        <label for="focus_keyword" class="block text-sm font-medium text-gray-700 mb-1">
                            Focus Keyword
                        </label>
                        <input type="text" 
                               name="focus_keyword" 
                               id="focus_keyword" 
                               value="{{ old('focus_keyword', $post->focus_keyword) }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <!-- Canonical URL -->
                    <div>
                        <label for="canonical_url" class="block text-sm font-medium text-gray-700 mb-1">
                            Canonical URL
                        </label>
                        <input type="url" 
                               name="canonical_url" 
                               id="canonical_url" 
                               value="{{ old('canonical_url', $post->canonical_url) }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
            </div>

            <!-- Sidebar Column -->
            <div class="lg:col-span-1 space-y-6">
                <!-- Publish Card -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Publish</h2>
                    
                    <!-- Status -->
                    <div class="mb-4">
                        <label class="flex items-center">
                            <input type="checkbox" 
                                   name="is_published" 
                                   id="is_published" 
                                   value="1"
                                   {{ old('is_published', $post->is_published) ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">Published</span>
                        </label>
                    </div>

                    <!-- Featured -->
                    <div class="mb-4">
                        <label class="flex items-center">
                            <input type="checkbox" 
                                   name="is_featured" 
                                   id="is_featured" 
                                   value="1"
                                   {{ old('is_featured', $post->is_featured) ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">Featured</span>
                        </label>
                    </div>

                    <!-- Scheduled Publishing -->
                    <div class="mb-4">
                        <label for="scheduled_at" class="block text-sm font-medium text-gray-700 mb-1">
                            Schedule for
                        </label>
                        <input type="datetime-local" 
                               name="scheduled_at" 
                               id="scheduled_at" 
                               value="{{ old('scheduled_at', $post->scheduled_at ? $post->scheduled_at->format('Y-m-d\TH:i') : '') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <!-- Submit Button -->
                    <button type="submit" 
                            class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 shadow-md">
                        Update Post
                    </button>
                </div>

                <!-- Category & Author Card -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Organization</h2>
                    
                    <!-- Category -->
                    <div class="mb-4">
                        <label for="category_id" class="block text-sm font-medium text-gray-700 mb-1">
                            Category
                        </label>
                        <select name="category_id" 
                                id="category_id"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Uncategorized</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}" {{ old('category_id', $post->category_id) == $category->id ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Author -->
                    <div class="mb-4">
                        <label for="author_id" class="block text-sm font-medium text-gray-700 mb-1">
                            Author
                        </label>
                        <select name="author_id" 
                                id="author_id"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            @foreach($authors as $author)
                                <option value="{{ $author->id }}" {{ old('author_id', $post->author_id) == $author->id ? 'selected' : '' }}>
                                    {{ $author->first_name }} {{ $author->last_name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Related Services -->
                    <div>
                        <label for="service_ids" class="block text-sm font-medium text-gray-700 mb-1">
                            Related Services
                        </label>
                        <select name="service_ids[]" 
                                id="service_ids"
                                multiple
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            @foreach($services as $service)
                                <option value="{{ $service->id }}" {{ in_array($service->id, old('service_ids', $post->service_ids ?? [])) ? 'selected' : '' }}>
                                    {{ $service->name }}
                                </option>
                            @endforeach
                        </select>
                        <p class="mt-1 text-xs text-gray-500">Hold Ctrl/Cmd to select multiple</p>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

@push('scripts')
<script>
let imagesToRemove = [];

function removeGalleryImage(imagePath) {
    if (confirm('Are you sure you want to remove this image?')) {
        imagesToRemove.push(imagePath);
        document.getElementById('remove_gallery_images').value = JSON.stringify(imagesToRemove);
        
        // Hide the image
        const imageDiv = document.querySelector(`[data-image="${imagePath}"]`);
        if (imageDiv) {
            imageDiv.style.display = 'none';
        }
    }
}

// Gallery images preview
document.getElementById('gallery_images').addEventListener('change', function(e) {
    const preview = document.getElementById('gallery_preview');
    preview.innerHTML = '';
    
    Array.from(e.target.files).forEach(file => {
        const reader = new FileReader();
        reader.onload = function(e) {
            const div = document.createElement('div');
            div.className = 'relative';
            div.innerHTML = `<img src="${e.target.result}" class="w-full h-24 object-cover rounded-lg shadow">`;
            preview.appendChild(div);
        };
        reader.readAsDataURL(file);
    });
});
</script>
@endpush
@endsection

