<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\User;
use App\Services\ChatService;
use App\Services\ChatAssignmentService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class ChatRoomController extends Controller
{
    protected ChatService $chatService;
    protected ChatAssignmentService $assignmentService;

    public function __construct(
        ChatService $chatService,
        ChatAssignmentService $assignmentService
    ) {
        $this->chatService = $chatService;
        $this->assignmentService = $assignmentService;
    }

    /**
     * Display a listing of chat rooms.
     */
    public function index(Request $request): View
    {
        $query = ChatRoom::with(['participants.user', 'currentAssignment.assignedStaff', 'messages' => function($q) {
            $q->latest()->limit(1);
        }]);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('assigned_to')) {
            if ($request->assigned_to === 'me') {
                $query->whereHas('currentAssignment', function($q) {
                    $q->where('assigned_to', auth()->id())->where('status', 'active');
                });
            } elseif ($request->assigned_to === 'unassigned') {
                $query->whereDoesntHave('currentAssignment', function($q) {
                    $q->where('status', 'active');
                });
            }
        }

        if ($request->filled('priority')) {
            $query->where('priority', '>=', $request->priority);
        }

        $chatRooms = $query->orderBy('priority', 'desc')
                          ->orderBy('created_at', 'desc')
                          ->paginate(20);

        $stats = [
            'total' => ChatRoom::count(),
            'active' => ChatRoom::where('status', 'active')->count(),
            'waiting' => ChatRoom::where('status', 'waiting')->count(),
            'closed' => ChatRoom::where('status', 'closed')->count(),
        ];

        return view('admin.chat.rooms.index', compact('chatRooms', 'stats'));
    }

    /**
     * Display the specified chat room.
     */
    public function show(Request $request, ChatRoom $chatRoom): View
    {
        $chatRoom->load([
            'participants.user',
            'currentAssignment.assignedStaff',
            'messages.user',
            'messages.files'
        ]);

        // Get messages with pagination
        $messages = $chatRoom->messages()
            ->with(['user', 'files'])
            ->orderBy('created_at', 'asc')
            ->paginate(50);

        // Get available staff for assignment
        $availableStaff = User::whereHas('role', function($query) {
            $query->whereIn('name', ['staff', 'admin']);
        })->get();

        // Check if current user can respond
        $canRespond = auth()->user()->hasRole(['admin', 'staff']);

        return view('admin.chat.rooms.show', compact(
            'chatRoom',
            'messages',
            'availableStaff',
            'canRespond'
        ));
    }

    /**
     * Send a message to the chat room.
     */
    public function sendMessage(Request $request, ChatRoom $chatRoom): JsonResponse
    {
        $request->validate([
            'message' => 'required|string|max:2000',
            'files.*' => 'file|max:10240', // 10MB max per file
        ]);

        try {
            // Ensure user is assigned to this chat or is admin
            if (!auth()->user()->hasRole('admin')) {
                $assignment = $chatRoom->currentAssignment;
                if (!$assignment || $assignment->assigned_to !== auth()->id()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'You are not assigned to this chat room.',
                    ], 403);
                }
            }

            $messageData = [
                'content' => $request->message,
                'sender_type' => 'staff',
                'user_id' => auth()->id(),
            ];

            $message = $this->chatService->sendMessage($chatRoom, $messageData);

            // Handle file uploads if any
            if ($request->hasFile('files')) {
                foreach ($request->file('files') as $file) {
                    $this->chatService->uploadFile($message, $file);
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Message sent successfully',
                'data' => [
                    'message' => $message->load(['user', 'files']),
                ],
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to send message in chat room', [
                'room_id' => $chatRoom->id,
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send message. Please try again.',
            ], 500);
        }
    }

    /**
     * Assign chat room to a staff member.
     */
    public function assign(Request $request, ChatRoom $chatRoom): JsonResponse
    {
        $request->validate([
            'staff_id' => 'required|exists:users,id',
        ]);

        try {
            $staff = User::findOrFail($request->staff_id);
            
            // Verify staff has appropriate role
            if (!$staff->hasRole(['staff', 'admin'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Selected user is not a staff member.',
                ], 400);
            }

            $this->assignmentService->assignToStaff($chatRoom, $staff, auth()->user());

            return response()->json([
                'success' => true,
                'message' => 'Chat room assigned successfully',
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to assign chat room', [
                'room_id' => $chatRoom->id,
                'staff_id' => $request->staff_id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to assign chat room. Please try again.',
            ], 500);
        }
    }

    /**
     * Update chat room status or priority.
     */
    public function update(Request $request, ChatRoom $chatRoom): JsonResponse
    {
        $request->validate([
            'status' => 'sometimes|in:active,waiting,closed,resolved',
            'priority' => 'sometimes|integer|min:1|max:5',
            'title' => 'sometimes|string|max:255',
        ]);

        try {
            $chatRoom->update($request->only(['status', 'priority', 'title']));

            return response()->json([
                'success' => true,
                'message' => 'Chat room updated successfully',
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to update chat room', [
                'room_id' => $chatRoom->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update chat room. Please try again.',
            ], 500);
        }
    }

    /**
     * Close a chat room.
     */
    public function close(Request $request, ChatRoom $chatRoom): JsonResponse
    {
        $request->validate([
            'reason' => 'sometimes|string|max:500',
        ]);

        try {
            $this->chatService->closeRoom($chatRoom, auth()->user(), $request->reason);

            return response()->json([
                'success' => true,
                'message' => 'Chat room closed successfully',
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to close chat room', [
                'room_id' => $chatRoom->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to close chat room. Please try again.',
            ], 500);
        }
    }

    /**
     * Get real-time updates for a chat room.
     */
    public function getUpdates(Request $request, ChatRoom $chatRoom): JsonResponse
    {
        $lastMessageId = $request->get('last_message_id', 0);

        $newMessages = $chatRoom->messages()
            ->with(['user', 'files'])
            ->where('id', '>', $lastMessageId)
            ->orderBy('created_at', 'asc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'messages' => $newMessages,
                'room_status' => $chatRoom->status,
                'last_message_id' => $newMessages->last()?->id ?? $lastMessageId,
            ],
        ]);
    }
}
