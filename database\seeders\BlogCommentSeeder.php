<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\BlogComment;
use App\Models\BlogPost;
use App\Models\User;
use Illuminate\Support\Str;

class BlogCommentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get existing blog posts and users
        $blogPosts = BlogPost::where('is_published', true)->get();
        $users = User::all();

        if ($blogPosts->isEmpty() || $users->isEmpty()) {
            $this->command->warn('No blog posts or users found. Please seed them first.');
            return;
        }

        $comments = [
            [
                'content' => 'This is an excellent article! The insights about modern web development trends are really valuable. I especially appreciate the section about performance optimization.',
                'rating' => 5,
                'is_approved' => true,
            ],
            [
                'content' => 'Great read! I\'ve been working with these technologies for a while and this article perfectly summarizes the current state of the industry.',
                'rating' => 4,
                'is_approved' => true,
            ],
            [
                'content' => 'Very informative post. Could you elaborate more on the security aspects mentioned in the article?',
                'rating' => 4,
                'is_approved' => false, // Pending approval
            ],
            [
                'content' => 'I disagree with some points here. The approach mentioned might not work for all use cases.',
                'rating' => 2,
                'is_approved' => false, // Pending approval
            ],
            [
                'content' => 'Thanks for sharing this! It helped me solve a problem I was facing in my current project.',
                'rating' => 5,
                'is_approved' => true,
            ],
            [
                'content' => 'This article is outdated and contains incorrect information.',
                'rating' => 1,
                'is_approved' => false,
                'is_flagged' => true,
                'flag_count' => 2,
            ],
            [
                'content' => 'Awesome tutorial! Step-by-step instructions are very clear and easy to follow.',
                'rating' => 5,
                'is_approved' => true,
            ],
            [
                'content' => 'I have a question about the implementation details. How would this work with legacy systems?',
                'rating' => null, // No rating, just a question
                'is_approved' => true,
            ],
        ];

        foreach ($blogPosts as $post) {
            // Add 2-4 comments per post
            $commentCount = rand(2, 4);
            $selectedComments = collect($comments)->random($commentCount);

            foreach ($selectedComments as $commentData) {
                $user = $users->random();

                $comment = BlogComment::create([
                    'uuid' => Str::uuid(),
                    'blog_post_id' => $post->id,
                    'user_id' => $user->id,
                    'content' => $commentData['content'],
                    'rating' => $commentData['rating'],
                    'is_approved' => $commentData['is_approved'],
                    'is_flagged' => $commentData['is_flagged'] ?? false,
                    'flag_count' => $commentData['flag_count'] ?? 0,
                    'helpful_count' => rand(0, 5),
                    'created_at' => now()->subDays(rand(1, 30)),
                ]);

                // Add some replies to approved comments
                if ($comment->is_approved && rand(1, 3) === 1) {
                    $replyUser = $users->where('id', '!=', $user->id)->random();

                    BlogComment::create([
                        'uuid' => Str::uuid(),
                        'blog_post_id' => $post->id,
                        'user_id' => $replyUser->id,
                        'parent_comment_id' => $comment->id,
                        'content' => 'Thanks for your comment! I agree with your points.',
                        'rating' => null,
                        'is_approved' => true,
                        'helpful_count' => rand(0, 3),
                        'created_at' => $comment->created_at->addHours(rand(1, 24)),
                    ]);
                }
            }
        }

        $this->command->info('Blog comments seeded successfully!');
    }
}
