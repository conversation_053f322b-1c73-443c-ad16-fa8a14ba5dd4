#!/bin/bash

# ChiSolution Chat System Deployment Script
# This script handles the complete deployment process for production

set -e  # Exit on any error

# Configuration
APP_NAME="chisolution"
DOCKER_COMPOSE_FILE="deployment/docker-compose.production.yml"
BACKUP_DIR="/var/backups/chisolution"
LOG_FILE="/var/log/chisolution-deploy.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root for security reasons"
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check if .env file exists
    if [[ ! -f .env ]]; then
        error ".env file not found. Please create it from .env.example"
    fi
    
    # Check if required environment variables are set
    source .env
    required_vars=("DB_PASSWORD" "DB_ROOT_PASSWORD" "REDIS_PASSWORD" "OPENAI_API_KEY")
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            error "Required environment variable $var is not set in .env file"
        fi
    done
    
    success "Prerequisites check passed"
}

# Create backup
create_backup() {
    log "Creating backup..."
    
    # Create backup directory if it doesn't exist
    sudo mkdir -p "$BACKUP_DIR"
    
    # Backup database
    if docker-compose -f "$DOCKER_COMPOSE_FILE" ps db | grep -q "Up"; then
        log "Backing up database..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T db mysqldump \
            -u root -p"$DB_ROOT_PASSWORD" chisolution > "$BACKUP_DIR/db_backup_$(date +%Y%m%d_%H%M%S).sql"
        success "Database backup created"
    else
        warning "Database container not running, skipping database backup"
    fi
    
    # Backup application files
    log "Backing up application files..."
    tar -czf "$BACKUP_DIR/app_backup_$(date +%Y%m%d_%H%M%S).tar.gz" \
        --exclude=node_modules \
        --exclude=vendor \
        --exclude=.git \
        --exclude=storage/logs \
        .
    
    success "Application backup created"
}

# Build and deploy
deploy() {
    log "Starting deployment..."
    
    # Pull latest changes (if this is a git deployment)
    if [[ -d .git ]]; then
        log "Pulling latest changes from git..."
        git pull origin main
    fi
    
    # Build Docker images
    log "Building Docker images..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" build --no-cache
    
    # Stop existing containers
    log "Stopping existing containers..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    
    # Start new containers
    log "Starting new containers..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    # Wait for services to be ready
    log "Waiting for services to be ready..."
    sleep 30
    
    # Run database migrations
    log "Running database migrations..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T app php artisan migrate --force
    
    # Clear and cache config
    log "Optimizing application..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T app php artisan config:cache
    docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T app php artisan route:cache
    docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T app php artisan view:cache
    
    # Restart queue workers
    log "Restarting queue workers..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" restart queue
    
    success "Deployment completed successfully"
}

# Health check
health_check() {
    log "Performing health check..."
    
    # Wait a bit for services to fully start
    sleep 10
    
    # Check if containers are running
    if ! docker-compose -f "$DOCKER_COMPOSE_FILE" ps | grep -q "Up"; then
        error "Some containers are not running properly"
    fi
    
    # Check application health endpoint
    max_attempts=30
    attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        log "Health check attempt $attempt/$max_attempts..."
        
        if curl -f -s http://localhost/health > /dev/null; then
            success "Application is healthy and responding"
            break
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            error "Application health check failed after $max_attempts attempts"
        fi
        
        sleep 10
        ((attempt++))
    done
    
    # Check database connectivity
    if docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T app php artisan tinker --execute="DB::connection()->getPdo(); echo 'Database OK';" | grep -q "Database OK"; then
        success "Database connectivity check passed"
    else
        error "Database connectivity check failed"
    fi
    
    # Check Redis connectivity
    if docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T app php artisan tinker --execute="Redis::ping(); echo 'Redis OK';" | grep -q "Redis OK"; then
        success "Redis connectivity check passed"
    else
        error "Redis connectivity check failed"
    fi
    
    success "All health checks passed"
}

# Rollback function
rollback() {
    log "Starting rollback process..."
    
    # Stop current containers
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    
    # Find latest backup
    latest_backup=$(ls -t "$BACKUP_DIR"/app_backup_*.tar.gz 2>/dev/null | head -n1)
    latest_db_backup=$(ls -t "$BACKUP_DIR"/db_backup_*.sql 2>/dev/null | head -n1)
    
    if [[ -z "$latest_backup" ]]; then
        error "No application backup found for rollback"
    fi
    
    # Restore application files
    log "Restoring application files from $latest_backup..."
    tar -xzf "$latest_backup" -C .
    
    # Restore database if backup exists
    if [[ -n "$latest_db_backup" ]]; then
        log "Restoring database from $latest_db_backup..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" up -d db
        sleep 10
        docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T db mysql \
            -u root -p"$DB_ROOT_PASSWORD" chisolution < "$latest_db_backup"
    fi
    
    # Start containers
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    success "Rollback completed"
}

# Cleanup old backups
cleanup_backups() {
    log "Cleaning up old backups..."
    
    # Keep only last 7 days of backups
    find "$BACKUP_DIR" -name "*.tar.gz" -mtime +7 -delete
    find "$BACKUP_DIR" -name "*.sql" -mtime +7 -delete
    
    success "Backup cleanup completed"
}

# Main deployment process
main() {
    log "Starting ChiSolution deployment process..."
    
    case "${1:-deploy}" in
        "deploy")
            check_root
            check_prerequisites
            create_backup
            deploy
            health_check
            cleanup_backups
            success "Deployment process completed successfully!"
            ;;
        "rollback")
            check_root
            rollback
            health_check
            success "Rollback process completed successfully!"
            ;;
        "health")
            health_check
            ;;
        "backup")
            create_backup
            ;;
        *)
            echo "Usage: $0 {deploy|rollback|health|backup}"
            echo "  deploy   - Full deployment process (default)"
            echo "  rollback - Rollback to previous version"
            echo "  health   - Run health checks only"
            echo "  backup   - Create backup only"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
