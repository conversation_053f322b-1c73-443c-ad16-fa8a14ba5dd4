<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\ConversationInsightsService;
use App\Services\ActivityLogger;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Carbon\Carbon;

class ConversationInsightsController extends Controller
{
    protected ConversationInsightsService $insightsService;
    protected ActivityLogger $activityLogger;

    public function __construct(ConversationInsightsService $insightsService, ActivityLogger $activityLogger)
    {
        $this->insightsService = $insightsService;
        $this->activityLogger = $activityLogger;
    }

    /**
     * Display the conversation insights dashboard.
     */
    public function index(Request $request): View
    {
        $defaultFilters = [
            'start_date' => Carbon::now()->subDays(30),
            'end_date' => Carbon::now(),
            'staff_id' => $request->get('staff_id', 'all'),
            'department' => $request->get('department', 'all'),
        ];

        $insights = $this->insightsService->getConversationInsights($defaultFilters);

        $this->activityLogger->log(
            'conversation_insights_viewed',
            $request->user(),
            ['filters' => $defaultFilters]
        );

        return view('admin.chat.insights.index', compact('insights', 'defaultFilters'));
    }

    /**
     * Get conversation insights data via API.
     */
    public function getInsights(Request $request): JsonResponse
    {
        $filters = $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'staff_id' => 'nullable|integer|exists:users,id',
            'department' => 'nullable|string|in:support,sales,technical',
        ]);

        try {
            $insights = $this->insightsService->getConversationInsights($filters);

            $this->activityLogger->log(
                'conversation_insights_api_accessed',
                $request->user(),
                ['filters' => $filters]
            );

            return response()->json([
                'success' => true,
                'data' => $insights,
                'filters' => $filters,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve conversation insights',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get overview insights.
     */
    public function getOverview(Request $request): JsonResponse
    {
        $filters = $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'staff_id' => 'nullable|integer|exists:users,id',
            'department' => 'nullable|string|in:support,sales,technical',
        ]);

        try {
            $insights = $this->insightsService->getConversationInsights($filters);

            return response()->json([
                'success' => true,
                'data' => $insights['overview'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve overview insights',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get conversation patterns.
     */
    public function getPatterns(Request $request): JsonResponse
    {
        $filters = $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'staff_id' => 'nullable|integer|exists:users,id',
            'department' => 'nullable|string|in:support,sales,technical',
        ]);

        try {
            $insights = $this->insightsService->getConversationInsights($filters);

            return response()->json([
                'success' => true,
                'data' => $insights['conversation_patterns'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve conversation patterns',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get trending topics.
     */
    public function getTrendingTopics(Request $request): JsonResponse
    {
        $filters = $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'staff_id' => 'nullable|integer|exists:users,id',
            'department' => 'nullable|string|in:support,sales,technical',
        ]);

        try {
            $insights = $this->insightsService->getConversationInsights($filters);

            return response()->json([
                'success' => true,
                'data' => $insights['trending_topics'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve trending topics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get usage patterns.
     */
    public function getUsagePatterns(Request $request): JsonResponse
    {
        $filters = $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'staff_id' => 'nullable|integer|exists:users,id',
            'department' => 'nullable|string|in:support,sales,technical',
        ]);

        try {
            $insights = $this->insightsService->getConversationInsights($filters);

            return response()->json([
                'success' => true,
                'data' => $insights['usage_patterns'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve usage patterns',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get conversation flow analysis.
     */
    public function getConversationFlow(Request $request): JsonResponse
    {
        $filters = $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'staff_id' => 'nullable|integer|exists:users,id',
            'department' => 'nullable|string|in:support,sales,technical',
        ]);

        try {
            $insights = $this->insightsService->getConversationInsights($filters);

            return response()->json([
                'success' => true,
                'data' => $insights['conversation_flow'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve conversation flow',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get peak hours analysis.
     */
    public function getPeakHours(Request $request): JsonResponse
    {
        $filters = $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'staff_id' => 'nullable|integer|exists:users,id',
            'department' => 'nullable|string|in:support,sales,technical',
        ]);

        try {
            $insights = $this->insightsService->getConversationInsights($filters);

            return response()->json([
                'success' => true,
                'data' => $insights['peak_hours'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve peak hours',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get user behavior analysis.
     */
    public function getUserBehavior(Request $request): JsonResponse
    {
        $filters = $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'staff_id' => 'nullable|integer|exists:users,id',
            'department' => 'nullable|string|in:support,sales,technical',
        ]);

        try {
            $insights = $this->insightsService->getConversationInsights($filters);

            return response()->json([
                'success' => true,
                'data' => $insights['user_behavior'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve user behavior',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get staff insights.
     */
    public function getStaffInsights(Request $request): JsonResponse
    {
        $filters = $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'department' => 'nullable|string|in:support,sales,technical',
        ]);

        try {
            $insights = $this->insightsService->getConversationInsights($filters);

            return response()->json([
                'success' => true,
                'data' => $insights['staff_insights'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve staff insights',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export insights report.
     */
    public function exportReport(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'format' => 'required|string|in:json,csv,pdf',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'staff_id' => 'nullable|integer|exists:users,id',
            'department' => 'nullable|string|in:support,sales,technical',
        ]);

        try {
            $filters = array_filter($validated, function ($key) {
                return $key !== 'format';
            }, ARRAY_FILTER_USE_KEY);

            $insights = $this->insightsService->getConversationInsights($filters);

            $reportData = [
                'report_generated_at' => now()->toISOString(),
                'filters_applied' => $filters,
                'format' => $validated['format'],
                'data' => $insights,
            ];

            $this->activityLogger->log(
                'conversation_insights_exported',
                $request->user(),
                ['format' => $validated['format'], 'filters' => $filters]
            );

            if ($validated['format'] === 'json') {
                return response()->json([
                    'success' => true,
                    'data' => $reportData,
                ]);
            }

            // For CSV and PDF, you would implement file generation and return download URL
            return response()->json([
                'success' => true,
                'message' => 'Report export initiated',
                'download_url' => null, // Would be actual download URL in production
                'data' => $reportData,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export insights report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
