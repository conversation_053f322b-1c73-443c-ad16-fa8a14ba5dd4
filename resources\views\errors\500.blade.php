@extends('layouts.app')

@section('title', __('errors.500_title', ['default' => 'Server Error']) . ' - ' . __('common.company_name'))
@section('meta_description', __('errors.500_description', ['default' => 'An internal server error occurred.']))

@section('content')
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
        <!-- Error Icon -->
        <div class="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-red-100">
            <svg class="h-16 w-16 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
        </div>

        <!-- Error Code -->
        <div>
            <h1 class="text-6xl font-bold text-gray-900">500</h1>
            <h2 class="mt-2 text-3xl font-extrabold text-gray-900">
                {{ __('errors.500_title', ['default' => 'Server Error']) }}
            </h2>
            <p class="mt-2 text-base text-gray-600">
                {{ __('errors.500_description', ['default' => 'Oops! Something went wrong on our end. We\'re working to fix it.']) }}
            </p>
        </div>

        <!-- Actions -->
        <div class="mt-8 space-y-4">
            <button onclick="location.reload()" class="btn-primary inline-flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                </svg>
                {{ __('errors.try_again', ['default' => 'Try Again']) }}
            </button>
            <div>
                <a href="{{ route('home', ['locale' => app()->getLocale()]) }}" class="text-primary-600 hover:text-primary-800 font-medium">
                    {{ __('errors.go_home', ['default' => 'Go to Homepage']) }}
                </a>
            </div>
        </div>

        <!-- Contact Support -->
        <div class="mt-8 pt-8 border-t border-gray-200">
            <p class="text-sm text-gray-600 mb-2">
                {{ __('errors.persistent_issue', ['default' => 'If this problem persists, please contact our support team.']) }}
            </p>
            <a href="{{ route('contact', ['locale' => app()->getLocale()]) }}" class="inline-flex items-center text-primary-600 hover:text-primary-800 font-medium">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                </svg>
                {{ __('errors.contact_support', ['default' => 'Contact Support']) }}
            </a>
        </div>
    </div>
</div>
@endsection

