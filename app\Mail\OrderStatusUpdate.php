<?php

namespace App\Mail;

use App\Models\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class OrderStatusUpdate extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $order;
    public $previousStatus;

    /**
     * Create a new message instance.
     */
    public function __construct(Order $order, string $previousStatus = null)
    {
        $this->order = $order;
        $this->previousStatus = $previousStatus;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $statusText = ucfirst(str_replace('_', ' ', $this->order->status));
        
        return new Envelope(
            subject: "Order {$statusText} - {$this->order->order_number}",
            from: config('mail.from.address'),
            replyTo: config('mail.from.address'),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        $billingAddress = $this->order->billing_address ?? [];
        $customerName = trim(($billingAddress['first_name'] ?? '') . ' ' . ($billingAddress['last_name'] ?? ''));

        // Fallback to user name if billing address doesn't have names
        if (empty($customerName) && $this->order->user) {
            $customerName = $this->order->user->name;
        }

        // Final fallback
        if (empty($customerName)) {
            $customerName = 'Valued Customer';
        }

        return new Content(
            view: 'emails.orders.status-update',
            with: [
                'order' => $this->order,
                'previousStatus' => $this->previousStatus,
                'customer_name' => $customerName,
                'company_name' => config('app.name'),
                'status_message' => $this->getStatusMessage(),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }

    /**
     * Get status-specific message.
     */
    private function getStatusMessage(): string
    {
        return match($this->order->status) {
            'processing' => 'Your order is now being processed and will be prepared for shipment.',
            'shipped' => 'Great news! Your order has been shipped and is on its way to you.',
            'delivered' => 'Your order has been successfully delivered. We hope you enjoy your purchase!',
            'cancelled' => 'Your order has been cancelled. If you have any questions, please contact our support team.',
            'refunded' => 'Your order has been refunded. The refund will appear in your account within 3-5 business days.',
            default => 'Your order status has been updated.',
        };
    }
}
