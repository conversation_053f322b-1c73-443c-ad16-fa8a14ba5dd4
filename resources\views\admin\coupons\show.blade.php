@extends('layouts.dashboard')

@section('title', 'View Coupon - Admin Dashboard')
@section('page_title', 'View Coupon')

@section('content')
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ $coupon->name }}</h1>
            <p class="text-gray-600">Coupon details and usage statistics</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('admin.coupons.edit', $coupon) }}" 
               class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                </svg>
                Edit Coupon
            </a>
            <a href="{{ route('admin.coupons.index') }}" 
               class="inline-flex items-center px-4 py-2 bg-neutral-600 text-white rounded-lg hover:bg-neutral-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                Back to Coupons
            </a>
        </div>
    </div>

    <!-- Status Alerts -->
    @if(!$coupon->is_active)
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">Coupon is currently inactive</h3>
                    <p class="mt-1 text-sm text-yellow-700">This coupon is not available for customers to use.</p>
                </div>
            </div>
        </div>
    @endif

    @if($coupon->expires_at && $coupon->expires_at->isPast())
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Coupon has expired</h3>
                    <p class="mt-1 text-sm text-red-700">This coupon expired on {{ $coupon->expires_at->format('M j, Y \a\t g:i A') }}.</p>
                </div>
            </div>
        </div>
    @endif

    @if($coupon->usage_limit && $coupon->used_count >= $coupon->usage_limit)
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Usage limit reached</h3>
                    <p class="mt-1 text-sm text-red-700">This coupon has reached its maximum usage limit.</p>
                </div>
            </div>
        </div>
    @endif

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Left Column - Coupon Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100">
                <div class="px-6 py-4 border-b border-neutral-200">
                    <h3 class="text-lg font-semibold text-gray-900">Basic Information</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Coupon Name</label>
                            <p class="text-gray-900">{{ $coupon->name }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Coupon Code</label>
                            <div class="flex items-center space-x-2">
                                <code class="bg-gray-100 px-3 py-1 rounded text-sm font-mono">{{ $coupon->code }}</code>
                                <button onclick="copyToClipboard('{{ $coupon->code }}')" 
                                        class="text-primary-600 hover:text-primary-800 transition-colors">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    @if($coupon->description)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <p class="text-gray-900">{{ $coupon->description }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Discount Settings -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100">
                <div class="px-6 py-4 border-b border-neutral-200">
                    <h3 class="text-lg font-semibold text-gray-900">Discount Settings</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Discount Type</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $coupon->type === 'percentage' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800' }}">
                                {{ $coupon->type === 'percentage' ? 'Percentage' : 'Fixed Amount' }}
                            </span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Discount Value</label>
                            <p class="text-lg font-semibold text-gray-900">
                                {{ $coupon->type === 'percentage' ? $coupon->value . '%' : 'R' . number_format($coupon->value, 2) }}
                            </p>
                        </div>
                        @if($coupon->type === 'percentage' && $coupon->maximum_discount)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Maximum Discount</label>
                            <p class="text-gray-900">R{{ number_format($coupon->maximum_discount, 2) }}</p>
                        </div>
                        @endif
                    </div>
                    
                    @if($coupon->minimum_amount)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Minimum Order Amount</label>
                        <p class="text-gray-900">R{{ number_format($coupon->minimum_amount, 2) }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Validity Period -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100">
                <div class="px-6 py-4 border-b border-neutral-200">
                    <h3 class="text-lg font-semibold text-gray-900">Validity Period</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                            <p class="text-gray-900">
                                {{ $coupon->starts_at ? $coupon->starts_at->format('M j, Y \a\t g:i A') : 'Immediate' }}
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Expiry Date</label>
                            <p class="text-gray-900">
                                {{ $coupon->expires_at ? $coupon->expires_at->format('M j, Y \a\t g:i A') : 'No expiry' }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column - Statistics & Status -->
        <div class="space-y-6">
            <!-- Status Card -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100">
                <div class="px-6 py-4 border-b border-neutral-200">
                    <h3 class="text-lg font-semibold text-gray-900">Status</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Active Status</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $coupon->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ $coupon->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Validity</span>
                        @php
                            $isValid = $coupon->isValid();
                            $validationIssues = [];

                            if (!$coupon->is_active) {
                                $validationIssues[] = 'Coupon is not active';
                            }
                            if ($coupon->is_deleted) {
                                $validationIssues[] = 'Coupon is deleted';
                            }
                            if ($coupon->starts_at && $coupon->starts_at->isFuture()) {
                                $validationIssues[] = 'Start date is in the future';
                            }
                            if ($coupon->expires_at && $coupon->expires_at->isPast()) {
                                $validationIssues[] = 'Coupon has expired';
                            }
                            if ($coupon->usage_limit && $coupon->used_count >= $coupon->usage_limit) {
                                $validationIssues[] = 'Usage limit reached';
                            }
                        @endphp
                        <div class="text-right">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $isValid ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ $isValid ? 'Valid' : 'Invalid' }}
                            </span>
                            @if(!$isValid && !empty($validationIssues))
                                <div class="mt-1 text-xs text-red-600">
                                    {{ implode(', ', $validationIssues) }}
                                </div>
                            @endif
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Usage Limit</span>
                        @php
                            $hasUsageLeft = !$coupon->usage_limit || $coupon->used_count < $coupon->usage_limit;
                        @endphp
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $hasUsageLeft ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ $hasUsageLeft ? 'Available' : 'Exhausted' }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Usage Statistics -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100">
                <div class="px-6 py-4 border-b border-neutral-200">
                    <h3 class="text-lg font-semibold text-gray-900">Usage Statistics</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-primary-600">{{ $coupon->used_count }}</div>
                        <div class="text-sm text-gray-600">Times Used</div>
                    </div>
                    
                    @if($coupon->usage_limit)
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Usage Progress</span>
                            <span class="font-medium">{{ $coupon->used_count }}/{{ $coupon->usage_limit }}</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-primary-600 h-2 rounded-full" style="width: {{ min(100, ($coupon->used_count / $coupon->usage_limit) * 100) }}%"></div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="text-center">
                            <div class="font-semibold text-gray-900">{{ max(0, $coupon->usage_limit - $coupon->used_count) }}</div>
                            <div class="text-gray-600">Remaining</div>
                        </div>
                        <div class="text-center">
                            <div class="font-semibold text-gray-900">{{ round(($coupon->used_count / $coupon->usage_limit) * 100, 1) }}%</div>
                            <div class="text-gray-600">Used</div>
                        </div>
                    </div>
                    @else
                    <div class="text-center text-sm text-gray-600">
                        Unlimited usage allowed
                    </div>
                    @endif
                </div>
            </div>

            <!-- Recent Orders Using This Coupon -->
            @php
                $recentOrders = $coupon->orders()->with(['user', 'currency'])
                    ->orderBy('created_at', 'desc')
                    ->limit(10)
                    ->get();
            @endphp

            @if($recentOrders->count() > 0)
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100">
                <div class="px-6 py-4 border-b border-neutral-200">
                    <h3 class="text-lg font-semibold text-gray-900">Recent Orders Using This Coupon</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-3">
                        @foreach($recentOrders as $order)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3">
                                        <div>
                                            <a href="{{ route('admin.orders.show', $order) }}"
                                               class="font-medium text-blue-600 hover:text-blue-800">
                                                {{ $order->order_number }}
                                            </a>
                                            <div class="text-sm text-gray-600">
                                                @if($order->user)
                                                    {{ $order->user->first_name }} {{ $order->user->last_name }}
                                                @else
                                                    Guest ({{ $order->email }})
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="font-medium text-gray-900">
                                        {{ $order->currency->symbol ?? 'R' }}{{ number_format($order->total_amount, 2) }}
                                    </div>
                                    <div class="text-sm text-gray-600">
                                        Saved: {{ $order->currency->symbol ?? 'R' }}{{ number_format($order->discount_amount, 2) }}
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        {{ $order->created_at->format('M j, Y') }}
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    @if($coupon->orders()->count() > 10)
                        <div class="mt-4 text-center">
                            <p class="text-sm text-gray-600">
                                Showing 10 of {{ $coupon->orders()->count() }} total orders
                            </p>
                        </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Quick Actions -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100">
                <div class="px-6 py-4 border-b border-neutral-200">
                    <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
                </div>
                <div class="p-6 space-y-3">
                    @if($coupon->is_active)
                        <form method="POST" action="{{ route('admin.coupons.toggle', $coupon) }}" class="w-full">
                            @csrf
                            <button type="submit"
                                    class="w-full inline-flex items-center justify-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors duration-200">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9V6a4 4 0 118 0v3M5 9h14l1 12H4L5 9z"/>
                                </svg>
                                Deactivate Coupon
                            </button>
                        </form>
                    @else
                        <form method="POST" action="{{ route('admin.coupons.toggle', $coupon) }}" class="w-full">
                            @csrf
                            <button type="submit"
                                    class="w-full inline-flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"/>
                                </svg>
                                Activate Coupon
                            </button>
                        </form>
                    @endif
                    
                    <a href="{{ route('admin.coupons.edit', $coupon) }}" 
                       class="w-full inline-flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                        </svg>
                        Edit Coupon
                    </a>
                    
                    <form method="POST" action="{{ route('admin.coupons.destroy', $coupon) }}" 
                          onsubmit="return confirm('Are you sure you want to delete this coupon? This action cannot be undone.')" 
                          class="w-full">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                                class="w-full inline-flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                            Delete Coupon
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
        toast.textContent = 'Coupon code copied to clipboard!';
        document.body.appendChild(toast);
        
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 3000);
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
    });
}
</script>
@endpush
@endsection
