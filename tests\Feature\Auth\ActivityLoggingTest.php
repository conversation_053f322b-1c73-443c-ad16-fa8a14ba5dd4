<?php

namespace Tests\Feature\Auth;

use App\Models\ActivityLog;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Password;
use Tests\TestCase;

use PHPUnit\Framework\Attributes\Test;
class ActivityLoggingTest extends TestCase
{
    use RefreshDatabase;

    protected $seed = true; // Use seeders to populate required data
    #[Test]
    public function password_reset_request_is_logged_for_existing_email()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $this->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $this->assertDatabaseHas('activity_logs', [
            'user_id' => $user->id,
            'user_email' => '<EMAIL>',
            'activity_type' => 'password_reset_request',
            'status' => 'success',
        ]);

        $log = ActivityLog::where('activity_type', 'password_reset_request')->first();
        $this->assertNotNull($log);
        $this->assertEquals('<EMAIL>', $log->user_email);

        // Check if request_data exists and has expected structure
        if (isset($log->request_data['email_exists'])) {
            $this->assertTrue($log->request_data['email_exists']);
        }
        if (isset($log->request_data['was_rate_limited'])) {
            $this->assertFalse($log->request_data['was_rate_limited']);
        }

        $this->assertNotNull($log->ip_address);
        $this->assertNotNull($log->user_agent);
    }
    #[Test]
    public function password_reset_request_is_logged_for_non_existing_email()
    {
        $this->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $this->assertDatabaseHas('activity_logs', [
            'user_email' => '<EMAIL>',
            'activity_type' => 'password_reset_request',
            'status' => 'success', // Always success for security
        ]);

        $log = ActivityLog::where('activity_type', 'password_reset_request')->first();
        $this->assertNotNull($log);
        $this->assertNull($log->user_id); // No user for non-existent email

        // Check if request_data exists and has expected structure
        if (isset($log->request_data['email_exists'])) {
            $this->assertFalse($log->request_data['email_exists']);
        }
        if (isset($log->request_data['was_rate_limited'])) {
            $this->assertFalse($log->request_data['was_rate_limited']);
        }
    }
    #[Test]
    public function successful_password_reset_is_logged()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => 'old-password',
        ]);

        $token = Password::createToken($user);

        $this->postJson('/reset-password', [
            'token' => $token,
            'email' => '<EMAIL>',
            'password' => 'SecureP@ssw0rd2024!',
            'password_confirmation' => 'SecureP@ssw0rd2024!',
        ]);

        $this->assertDatabaseHas('activity_logs', [
            'user_id' => $user->id,
            'user_email' => '<EMAIL>',
            'activity_type' => 'password_reset_success',
            'status' => 'success',
        ]);

        $log = ActivityLog::where('activity_type', 'password_reset_success')->first();
        $this->assertNotNull($log);
        $this->assertTrue($log->response_data['password_changed']);
        $this->assertTrue($log->response_data['sessions_cleared']);
        $this->assertTrue($log->response_data['remember_tokens_cleared']);
    }
    #[Test]
    public function failed_password_reset_is_logged()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $this->postJson('/reset-password', [
            'token' => 'invalid-token',
            'email' => '<EMAIL>',
            'password' => 'SecureP@ssw0rd2024!',
            'password_confirmation' => 'SecureP@ssw0rd2024!',
        ]);

        $this->assertDatabaseHas('activity_logs', [
            'user_email' => '<EMAIL>',
            'activity_type' => 'password_reset_failed',
            'status' => 'failed',
        ]);

        $log = ActivityLog::where('activity_type', 'password_reset_failed')->first();
        $this->assertNotNull($log);
        $this->assertStringContainsString('Invalid or expired token', $log->failure_reason);
        $this->assertStringContainsString('invalid-', $log->request_data['token_provided']);
    }
    #[Test]
    public function activity_log_captures_device_and_location_info()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $this->withHeaders([
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ])->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $log = ActivityLog::where('activity_type', 'password_reset_request')->first();
        $this->assertNotNull($log);
        $this->assertNotNull($log->user_agent);
        $this->assertNotNull($log->ip_address);
        $this->assertEquals('127.0.0.1', $log->ip_address);
        $this->assertEquals('Local', $log->country); // Local IP
    }
    #[Test]
    public function activity_log_includes_request_context()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $this->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $log = ActivityLog::where('activity_type', 'password_reset_request')->first();
        $this->assertNotNull($log);
        $this->assertEquals('POST', $log->method);
        $this->assertStringContainsString('/forgot-password', $log->url);
        $this->assertNotNull($log->session_id);
        $this->assertNotNull($log->request_id);
        $this->assertIsArray($log->request_data);
        $this->assertIsArray($log->response_data);
    }
    #[Test]
    public function activity_log_sanitizes_sensitive_data()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $token = Password::createToken($user);

        $this->postJson('/reset-password', [
            'token' => $token,
            'email' => '<EMAIL>',
            'password' => 'SecureP@ssw0rd2024!',
            'password_confirmation' => 'SecureP@ssw0rd2024!',
        ]);

        $log = ActivityLog::where('activity_type', 'password_reset_success')->first();
        $this->assertNotNull($log);
        
        // Check that sensitive data is redacted
        $this->assertEquals('***REDACTED***', $log->request_data['form_data']['password']);
        $this->assertEquals('***REDACTED***', $log->request_data['form_data']['password_confirmation']);
        $this->assertEquals('***REDACTED***', $log->request_data['form_data']['token']);
        
        // But token reference should be partially visible
        $this->assertStringContainsString('...', $log->request_data['token_used']);
        $this->assertNotEquals($token, $log->request_data['token_used']);
    }
    #[Test]
    public function activity_log_calculates_risk_scores()
    {
        // Test with existing email (lower risk)
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $this->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $log = ActivityLog::where('user_email', '<EMAIL>')->first();
        $this->assertLessThan(50, $log->risk_score); // Should be low risk

        // Test with non-existent email (higher risk)
        $this->postJson('/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $log = ActivityLog::where('user_email', '<EMAIL>')->first();
        $this->assertGreaterThan(20, $log->risk_score); // Should be higher risk
    }
}
